// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "UWA/overdraw_opaque" {
	SubShader{
		Tags {"Queue" = "Geometry" "RenderType" = "Opaque" "IgnoreProjector" = "True" }

		Fog{ Mode Off }
		Blend One One // additive blending

		Pass{

			CGPROGRAM

			#pragma vertex vert
			#pragma fragment frag

			struct vertInput {
				float4 pos : POSITION;
			};

			struct vertOutput {
				float4 pos : SV_POSITION;
			};

			vertOutput vert(vertInput input) {
				vertOutput o;
				o.pos = UnityObjectToClipPos(input.pos);
				return o;
			}

			half4 frag(vertOutput output) : COLOR{
				return half4(0.02, 0.008, 0.004, 0);
			}
			ENDCG
		}
	}
}

