#pragma warning disable
#if !DISABLE_UWA_SDK
using System.Collections.Generic;
using UnityEngine;
#if UNITY_5_5_OR_NEWER
using UnityEngine.Profiling;
#endif

#if !UNITY_WEBGL
namespace UWA.UWASDK
{
    public class TypeHolder
    {
        public static readonly TypeHolder Instance = new TypeHolder();
        private TypeHolder() { }

        // This code is used for keepping types and methods from being stripped, and will never be called. 
        public void Hold()
        {
            Resources.Load("");
            Resources.UnloadAsset(new Object());
            Resources.UnloadUnusedAssets();
            Resources.LoadAll("");

            Canvas c = null;
            GameObject go = null;
            WWW w = null;
            AssetBundle ab = null;
            Animator at = null;
            Animation an = null;
            ParticleSystem ps = null;
            TextAsset ta = null;
            Mesh m = null;
            Material mt = null;
            MeshFilter mf = null;
            MeshCollider mc = null;
            Shader s = null;
            Texture t = null;
            Texture2D t2 = null;
            RenderTexture rt = null;
            AnimationClip ac = null;
            AudioClip ac2 = null;
            SkinnedMeshRenderer r = null;
            Projector p = null;
            Font f = null;
            Color cc = Color.red;
            List<int> il = new List<int>();

#if UNITY_2019_1_OR_NEWER
            Check(QualitySettings.skinWeights);
#else
            Check(QualitySettings.blendWeights);
#endif

#if UNITY_2018_3_OR_NEWER
            Profiler.SetAreaEnabled(ProfilerArea.CPU, false);
            Recorder recorder = Recorder.Get("");
            recorder.CollectFromAllThreads();
            recorder.FilterToCurrentThread();
#endif

#if UNITY_2018_1_OR_NEWER
            Profiler.GetAllocatedMemoryForGraphicsDriver();
#endif
#if UNITY_2018_2_OR_NEWER
            Check(m.GetUVDistributionMetric(0));
            mt.GetTexturePropertyNameIDs(il);
#endif

#if !UNITY_4_7 && !UNITY_4_6
            Cursor.lockState = CursorLockMode.None;
            Check(Cursor.lockState);
#endif

#if UNITY_5_3_OR_NEWER
            Check(SystemInfo.processorFrequency);
            Check(SystemInfo.graphicsMultiThreaded);
#endif

#if UNITY_4_6 || UNITY_4_7 || UNITY_5
            t2.LoadImage(null);
#else
            ImageConversion.LoadImage(t2, null);
#endif

#if UNITY_2017_1_OR_NEWER
            ScreenCapture.CaptureScreenshot("", 1);
#else
            Application.CaptureScreenshot("");
#endif

#if UNITY_5_6_OR_NEWER
            List<string> temp = new List<string>();
            Sampler.GetNames(temp);
            Sampler sampler = Sampler.Get("");
            Check(sampler.isValid);
            Check(sampler.name);
            Recorder recorder2 = Recorder.Get("");
            recorder2.enabled = true;
            Check(recorder2.isValid);
            Check(recorder2.enabled);
            Check(recorder2.sampleBlockCount);
            Check(recorder2.elapsedNanoseconds);
#endif
            Profiler.BeginSample("", new Object());
            Profiler.BeginSample("");
            Profiler.EndSample();
            Profiler.GetRuntimeMemorySize(null);
            Profiler.GetTotalAllocatedMemory();
            Profiler.GetTotalReservedMemory();
            Profiler.GetTotalUnusedReservedMemory();
            Profiler.GetMonoUsedSize();
            Profiler.GetMonoHeapSize();
            Profiler.enableBinaryLog = true;
            Profiler.enabled = true;
            Profiler.logFile = "";
            Check(Profiler.usedHeapSize);
            Check(Profiler.enableBinaryLog);
            Check(Profiler.enabled);
            Check(Profiler.logFile);
            Check(Profiler.supported);
            Check(f.fontSize);
            Check(t2.mipmapCount);
            Check(mc.sharedMesh);
            Check(mf.mesh);
            Check(mf.sharedMesh);
            Check(w.assetBundle);
            Check(w.error);
            Check(r.sharedMesh);
            Check(r.rootBone);
            Check(r.bones);
            Check(r.updateWhenOffscreen);
            Check(r.quality);
            Check(c.renderMode);
            Check(RenderTexture.active);
            GL.Clear(true, true, Color.red);
            object[] os = Camera.allCameras;
        }

        void Check(object o) { }

        void OnGUI()
        {
            GUILayout.Button("");
            GUILayout.Label("");
        }
    }
}
#endif
#endif
#pragma warning restore