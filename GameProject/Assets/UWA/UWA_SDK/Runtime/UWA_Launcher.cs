#if !DISABLE_UWA_SDK && (!UNITY_IPHONE && !UNITY_ANDROID && !UNITY_STANDALONE_WIN && !UNITY_WEBGL && !UNITY_OPENHARMONY)
#define DISABLE_UWA_SDK
#endif

#if DISABLE_UWA_SDK
using UnityEngine;
using System.Collections;
using System;
using System.Diagnostics;

public class UWAEngine
{
    public enum DumpType { 
        ManagedHeap = 1,
        Lua = 1 << 1,
        Resources = 1 << 2,
        Overdraw = 1 << 3,
    }
    public enum Mode {
        Overview = 0,
        Mono = 1,
        [Obsolete("'Assets' mode is now renamed as 'Resources' mode.")]
        Assets = 2,
        Resources = 2,
        Lua = 3,
        Gpu = 4,
        Unset = 5,
    }

    public static int FrameId;
    [Conditional("ENABLE_UWA_SDK")]
    public static void StaticInit() { }
    public static bool AutoLaunch { get { return false; } }
    [Conditional("ENABLE_UWA_SDK")]
    public static void Start(Mode mode) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void Stop() { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void PushSample(string sampleName) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void PopSample() { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void LogValue(string valueName, float value) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void LogValue(string valueName, Vector3 value) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void LogValue(string valueName, int value) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void LogValue(string valueName, bool value) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void AddMarker(string valueName) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void SetOverrideLuaLib(string luaLib) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void SetOverrideLuaState(object luaState) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void Upload(Action<bool> callback, string user, string password, string projectName, int timeLimitS) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void Upload(Action<bool> callback, string user, string password, int projectId, int timeLimitS) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void Tag(string tag) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void Note(string note) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void SetUIActive(bool active) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void Dump(DumpType t) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void SetOverrideAndroidActivity(AndroidJavaObject activity) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void SetConfig(string startConfig) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void SetUWAToolsIP(string ip) { }
    [Conditional("ENABLE_UWA_SDK")]
    public static void SetBundleVersion(string version) { }
}
#else

using UnityEngine;
using System.Collections;
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using UWA.UWASDK;
using UWA;

#if UNITY_2018_3_OR_NEWER
using UnityEngine.Scripting;
#endif

#if UNITY_EDITOR
using UWAPlatform = UWA.UWASDK.Proxy;
#elif UNITY_IPHONE
using UWAPlatform = UWA.IOS;       
#elif UNITY_WEBGL
using UWAPlatform = UWA.WebGL;     
#elif UNITY_ANDROID
using UWAPlatform = UWA.Android;   
#elif UNITY_STANDALONE_WIN
using UWAPlatform = UWA.Windows;
#elif UNITY_OPENHARMONY
using UWAPlatform = UWA.Harmony;
#endif

#if UNITY_2018_3_OR_NEWER
[assembly: AlwaysLinkAssembly]
#endif

public class UWAEngine
{
#if (UNITY_IPHONE || UNITY_WEBGL) && !UNITY_EDITOR
    [DllImport("__Internal")]
    private static extern void UWARegisterPluginLoad();
#endif

    [RuntimeInitializeOnLoadMethod]
    public static void AutoStart()
    {
#if (UNITY_IPHONE || UNITY_WEBGL) && !UNITY_EDITOR
        UWARegisterPluginLoad();
#endif
        if (UWA.UWASDK.Proxy.UWAEngine.AutoLaunch)
        { 
            StaticInit();
        }
    }

    /// <summary>
    /// [UWA GOT | UWA GPM] This api can be used to initialize the UWA SDK, instead of draging the UWA_Launcher.prefab into your scene.
    /// </summary>
    public static void StaticInit(bool poco = false)
    {
        UWAPlatform.UWAEngine.StaticInit();
        UWAPlatform.GUIWrapper.ControlByPoco = poco || UWAPlatform.GUIWrapper.ControlByPoco;
    }

    /// <summary>
    /// [UWA GOT | UWA GPM] The recorded frame count
    /// </summary>
    public static int FrameId { get { return UWAPlatform.UWAEngine.FrameId; } }

    /// <summary>
    /// [UWA GOT] The profiling mode 
    /// </summary>
    public enum Mode
    {
        Overview = 0,
        Mono = 1,
        [Obsolete("'Assets' mode is now renamed as 'Resources' mode.")]
        Assets = 2,
        Resources = 2,
        Lua = 3,
        Gpu = 4,
        Unset = 5,
    }

    /// <summary>
    /// [UWA GOT] This api can be used to start the test with the given mode, instead of pressing the button in GUI panel.
    /// Test can be started only once.
    /// </summary>
    /// <param name="mode"> the profiling mode to be started</param>
    public static void Start(Mode mode)
    {
        UWAPlatform.UWAEngine.Start((UWAPlatform.UWAEngine.Mode)mode);
    }

    /// <summary>
    /// [UWA GOT] This api can be used to stop the test, instead of pressing the button in GUI panel.
    /// Test can be stopped only once.
    /// </summary>
    public static void Stop()
    {
        UWAPlatform.UWAEngine.Stop();
    }

    /// <summary>
    /// [UWA GOT] Give a tag to the following test case. This tag will override the scene name got from unity
    /// in the performance reports
    /// </summary>
    /// <param name="tag"></param>
    public static void Tag(string tag)
    {
        UWAPlatform.UWAEngine.Tag(tag);
    }

    /// <summary>
    /// [UWA GOT] Give a note to the following test case. This note will show as "��ע"
    /// in the performance reports
    /// </summary>
    /// <param name="note"></param>
    public static void Note(string note)
    {
        UWAPlatform.UWAEngine.Note(note);
    }

    /// <summary>
    /// Set active state of the UI panel
    /// </summary>
    /// <param name="active"></param>
    public static void SetUIActive(bool active)
    {
        UWAPlatform.UWAEngine.SetUIActive(active);
    }

    public enum DumpType
    {
        ManagedHeap = 1,
        Lua = 1 << 1,
        Resources = 1 << 2,
        Overdraw = 1 << 3,
    }

    /// <summary>
    /// Do simple asset dump in overview mode
    /// </summary>
    [Conditional("ENABLE_PROFILER")]
    public static void Dump(DumpType t)
    {
        UWAPlatform.UWAEngine.Dump((eDumpType)t);
    }

    /// <summary>
    /// [UWA GOT] Add a sample into the function lists in the UWAEngine, so the performance 
    /// between a Push and a Pop will be recorded with the given name.
    /// It is supported to call the PushSample and PopSample recursively, and they must be called in pairs.
    /// </summary>
    /// <param name="sampleName"></param>
    [Conditional("ENABLE_PROFILER")]
    public static void PushSample(string sampleName)
    {
        UWAPlatform.UWAEngine.PushSample(sampleName);
    }
    /// <summary>
    /// [UWA GOT] Add a sample into the function lists in the UWAEngine, so the performance
    /// between a Push and a Pop will be recorded with the given name.
    /// It is supported to call the PushSample and PopSample recursively, and they must be called in pairs.
    /// </summary>
    [Conditional("ENABLE_PROFILER")]
    public static void PopSample()
    {
        UWAPlatform.UWAEngine.PopSample();
    }

    /// <summary>
    /// [UWA GOT] Upload the profiling data to GOT Online with project name
    /// </summary>
    /// <param name="callback">callback with true if the uploading succeed</param>
    /// <param name="user">user name for uwa4d.com</param>
    /// <param name="password">password for uwa4d.com</param>
    /// <param name="projectName">name of the target project</param>
    /// <param name="timeLimitS">the duration of the profiling data to upload by api has to be smaller than the time limit,
    /// or you have to upload it manually.</param>
#if !UNITY_WEBGL
    public static void Upload(Action<bool> callback, string user, string password, string projectName, int timeLimitS)
    {
        UWAPlatform.UWAEngine.Upload(callback, user, password, projectName, timeLimitS);
    }
#endif

/// <summary>
/// [UWA GOT] Upload the profiling data to GOT Online with project id
/// </summary>
/// <param name="callback">callback with true if the uploading succeed</param>
/// <param name="user">user name for uwa4d.com</param>
/// <param name="password">password for uwa4d.com</param>
/// <param name="projectName">id of the target project</param>
/// <param name="timeLimitS">the duration of the profiling data to upload by api has to be smaller than the time limit,
/// or you have to upload it manually.</param>
#if !UNITY_WEBGL
    public static void Upload(Action<bool> callback, string user, string password, int projectId, int timeLimitS)
    {
        UWAPlatform.UWAEngine.Upload(callback, user, password, projectId, timeLimitS);
    }
#endif

    [Conditional("ENABLE_PROFILER")]
    public static void LogValue(string valueName, float value)
    {
        UWAPlatform.UWAEngine.LogValue(valueName, value);
    }
    [Conditional("ENABLE_PROFILER")]
    public static void LogValue(string valueName, int value)
    {
        UWAPlatform.UWAEngine.LogValue(valueName, value);
    }
    [Conditional("ENABLE_PROFILER")]
    public static void LogValue(string valueName, Vector3 value)
    {
        UWAPlatform.UWAEngine.LogValue(valueName, value);
    }
    [Conditional("ENABLE_PROFILER")]
    public static void LogValue(string valueName, bool value)
    {
        UWAPlatform.UWAEngine.LogValue(valueName, value);
    }
    [Conditional("ENABLE_PROFILER")]
    public static void AddMarker(string valueName)
    {
        UWAPlatform.UWAEngine.AddMarker(valueName);
    }

    /// <summary>
    /// [UWA GOT] Change the Lua state to a custom object.
    /// There is no need to call it when you use the default lua state.
    /// </summary>
    public static void SetOverrideLuaState(object luaState)
    {
#if !UNITY_IPHONE
        UWAPlatform.UWAEngine.SetOverrideLuaState(luaState);
#endif
    }
    /// <summary>
    /// [UWA GOT] Change the lua lib to a custom name, e.g. 'libgamex.so'.
    /// There is no need to call it when you use the default ulua/tolua/slua/xlua lib.
    /// </summary>
    public static void SetOverrideLuaLib(string luaLib)
    {
#if !UNITY_IPHONE
        UWAPlatform.UWAEngine.SetOverrideLuaLib(luaLib);
#endif
    }
    /// <summary>
    /// [UWA GOT] Change the android activity to a custom activity.
    /// There is no need to call it when you use the default UnityActivity.
    /// </summary>
#if !UNITY_WEBGL
    public static void SetOverrideAndroidActivity(AndroidJavaObject activity)
    {
#if UNITY_ANDROID
        UWAPlatform.UWAEngine.SetOverrideAndroidActivity(activity);
#endif
    }
#endif
    /// <summary>
    /// [UWA GOT] This api can be used to override start settings.
    /// </summary>
    /// <param name="startConfig"> start settings inn json format</param>
    /*
        开启 GOT 测试，可指定 SDK 配置设置
        mode 包含 : "overview", "mono", "resources", "lua", "gpu"
        config 为 dict 类型，包含各个模块的配置，如果为 None，则使用默认配置
        
        config = {
            "overview.mode": 0,
            "overview.engine_cpu_stack": True,
            "overview.lua_cpu_stack": True,
            "overview.lua_mem_stack": True,
            "overview.time_line": True,
            "overview.stack_detail": 1,
            "overview.unity_api": True,
            "overview.lua": True,
            "overview.lua_dump_step": 0,
            "overview.resources": True,
            "overview.unity_loading": True,
            "mono.mono_dump_step": 0,
            "resources.unity_loading": True,
            "lua.lua_dump_step": 0,
            "lua.lua_cpu_stack": True,
            "lua.lua_mem_stack": True,
            "gpu.texture_analysis": True,
            "gpu.mesh_analysis": True
        }

        其中各个字段的含义如下：
        overview.mode : Overview 模式具体测试模式，0 表示自定义模式，1 表示极简模式，2 表示CPU模式，3 表示内存模式 (Overview 模式的其他字段，只在自定义模式下有效)
        overview.engine_cpu_stack : Overview 模式下，是否开启引擎、C#逻辑代码 CPU 调用堆栈统计
        overview.lua_cpu_stack : Overview 模式下，是否开启 Lua 逻辑代码 CPU 调用耗时堆栈统计。前置条件：engine_cpu_stack 为 True
        overview.lua_mem_stack : Overview 模式下，是否开启 Lua 逻辑代码内存占用堆栈统计。前置条件：lua 为 True
        overview.time_line : Overview 模式下，是否开启 Timeline 统计。前置条件：engine_cpu_stack 为 True
        overview.stack_detail : Overview 模式下，堆栈统计的细节控制，0 表示默认细节，1 表示详细细节。前置条件：engine_cpu_stack 为 True
        overview.unity_api : Overview 模式下，是否开启 Unity API 调用统计。前置条件：engine_cpu_stack 为 True
        overview.lua : Overview 模式下，是否开启 Lua 内存测试
        overview.lua_dump_step : Overview 模式下，Lua 内存测试采样间隔，-1 表示不支持Dump，0 表示手动Dump，N自动以N为周期进行Lua Dump。前置条件：lua 为 True
        overview.resources : Overview 模式下，是否开启资源统计，0 表示完全关闭Resources获取（不可Dump），1 表示只获取资源总数、总内存（可Dump），2 表示获取资源详细信息（可Dump）
        overview.unity_loading : Overview 模式下，是否开启资源管理功能
        mono.mono_dump_step : Mono 模式下，Mono 测试采样间隔，0 表示手动Dump，N自动以N为周期进行Mono Dump
        resources.unity_loading : Resources 模式下，是否开启资源管理功能
        lua.lua_dump_step : Lua 模式下，Lua 测试采样间隔，0 表示手动Dump，N自动以N为周期进行Lua Dump
        lua.lua_cpu_stack : Lua 模式下，是否开启 Lua 逻辑代码 CPU 调用耗时堆栈统计，固定为 True
        lua.lua_mem_stack : Lua 模式下，是否开启 Lua 逻辑代码内存占用堆栈统计，固定为 True
        gpu.texture_analysis : GPU 模式下，是否开启纹理分析
        gpu.mesh_analysis : GPU 模式下，是否开启网格分析
    */
    public static void SetConfig(string startConfig)
    {
        UWAPlatform.UWAEngine.SetConfig(startConfig);
    }

    public static void SetUWAToolsIP(string ip)
    {
        UWAPlatform.UWAEngine.SetUWAToolsIP(ip);
    }

    public static void SetBundleVersion(string version)
    {
        UWAPlatform.UWAEngine.SetBundleVersion(version);
    }
}

#endif