using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

public class FindUnusedResourcePath : EditorWindow
{
    private KeyValuePair<string, string>[] foldersToSearch =
    {
        new KeyValuePair<string, string>("Assets/Res", ""),
        new KeyValuePair<string, string>("Assets/ArtTmp", ""),
        new KeyValuePair<string, string>("Assets/SeasonZero", "SeasonZero/"),
        new KeyValuePair<string, string>("Assets/SeasonOne", "SeasonOne/"),
    };
    private string jsonFilePath = "Assets/Res/Config/resourcePath1.json";
    private string codeFilePath = "prefabInCode.txt";
    // private List<string> missingResources = new List<string>();
    private Vector2 scrollPosition;
    
    private List<KeyValuePair<string, Object>> unusedObjects = new ();
    private List<string> unusedPath =  new List<string>();
    private string ignoreFolders;
    private string ignoreFiles;
    
    private List<string> listIgnoreFolders = new List<string>();
    private List<string> listIgnoreFiles = new List<string>();
    private int ignoreCount;
    private int totalCount;

    [MenuItem("AOW/FindUnusedResourcePath")]
    public static void ShowWindow()
    {
        GetWindow<FindUnusedResourcePath>("FindUnusedResourcePath");
    }

    private void OnGUI()
    {
        EditorGUILayout.LabelField("Folders to search (comma separated):");
        // EditorGUILayout.EndVertical();
        //用label展示搜索路径
        EditorGUILayout.LabelField("搜索路径:");
        foreach (var folder in foldersToSearch)
        {
            EditorGUILayout.LabelField(folder.Key);
        }
        

        EditorGUILayout.LabelField("JSON:" +jsonFilePath);
        
        //忽略路径
        ignoreFolders = EditorGUILayout.TextField("忽略路径(,分隔)", ignoreFolders);
        ignoreFiles = EditorGUILayout.TextField("忽略文件(,分隔)", ignoreFiles);
        
        if (GUILayout.Button("Check Resources"))
        {
            EditorPrefs.SetString("FindUnusedResourcePath_ignoreFolders", ignoreFolders);
            EditorPrefs.SetString("FindUnusedResourcePath_ignoreFiles", ignoreFiles);
            CheckResources();
        }

        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("未使用个数：" + unusedObjects.Count + "/" + totalCount + ", 忽略个数：" + ignoreCount);
        if (GUILayout.Button("导出"))
        {
            //路径换行符间隔，保存到文件
            var str = string.Join("\n", unusedPath);
            var filePath = "unusedPath.txt";
            File.WriteAllText(filePath, str);
            //保存后打开到文件夹
            EditorUtility.RevealInFinder(filePath);
        }
        EditorGUILayout.EndHorizontal();

        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        foreach (var item in unusedObjects)
        {
            var obj = item.Value;
            if (GUILayout.Button(item.Key))
            {
                EditorGUIUtility.PingObject(obj);
                Selection.activeObject = obj;
            }
        }
        EditorGUILayout.EndScrollView();
    }
    
    public void OnEnable()
    {
        ignoreFolders = EditorPrefs.GetString("FindUnusedResourcePath_ignoreFolders", "");
        ignoreFiles = EditorPrefs.GetString("FindUnusedResourcePath_ignoreFiles", "");
        CheckResources();
    }

    private void CheckResources()
    {
        unusedObjects.Clear();
        unusedPath.Clear();
        ignoreCount = 0;
        totalCount = 0;
        listIgnoreFolders = ignoreFolders.Split(',').ToList();
        listIgnoreFiles = ignoreFiles.Split(',').ToList();
        for (int i = listIgnoreFolders.Count - 1; i >= 0; i--)
            if (listIgnoreFolders[i].Trim() == "")
                listIgnoreFolders.RemoveAt(i);
        
        for (int i = listIgnoreFiles.Count - 1; i >= 0; i--)
            if (listIgnoreFiles[i].Trim() == "")
                listIgnoreFiles.RemoveAt(i);

        // 读取JSON文件
        string jsonText = File.ReadAllText(jsonFilePath);
        var jsonResources1 = Newtonsoft.Json.JsonConvert.DeserializeObject<List<ResourceListItem>>(jsonText);
        
        var jsonResources = new HashSet<string>();
        foreach (var resource in jsonResources1)
        {
            jsonResources.Add(resource.artResourcePath);
        }
        
        // 读取代码文件
        string codeText = File.ReadAllText(codeFilePath);
        var lines = codeText.Split('\n');
        foreach (var line in lines)
        {
            jsonResources.Add(line);
        }

        // 遍历文件夹中的Prefab
        foreach (var item in foldersToSearch)
        {
            var folder = item.Key;
            string[] prefabGuids = AssetDatabase.FindAssets("t:Prefab OR t:Asset", new[] { folder });
            totalCount += prefabGuids.Length;
            foreach (string guid in prefabGuids)
            {
                string prefabPath = AssetDatabase.GUIDToAssetPath(guid);
                //相对路寄给你
                var relativePath = item.Value + prefabPath.Substring(folder.Length + 1);
                if (!jsonResources.Contains(relativePath))
                {
                    var ignore = false;
                    foreach (var ignoreFile in listIgnoreFiles)
                    {
                        if (ignoreFile == prefabPath)
                        {
                            ignore = true;
                            break;
                        }
                    }

                    if (!ignore)
                    {
                        foreach (var ignoreFolder in listIgnoreFolders)
                        {
                            if (prefabPath.StartsWith(ignoreFolder))
                            {
                                ignore = true;
                                break;
                            }
                        }
                    }
                    if (!ignore)
                    {
                        Debug.Log(relativePath);
                        unusedPath.Add(prefabPath);
                        var obj = AssetDatabase.LoadAssetAtPath<Object>(prefabPath);
                        unusedObjects.Add(new KeyValuePair<string, Object>(prefabPath, obj));
                    }
                    else
                    {
                        ignoreCount++;
                    }
                }
            }
        }
    }

    [Serializable]
    private class ResourceListItem
    {
        public string artResourcePath;
    }
}    