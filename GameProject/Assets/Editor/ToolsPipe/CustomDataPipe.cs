using System;
using Sultan.BuildTools;
using Sultan.Tools;
using UnityEditor;
using UnityEngine;

namespace Sultan.ToolsPipe
{
    [ProcessingPipeline("Addressable", "打包数据资源(Lua,Flat)", (int)ProcessingStepSort.BuildLuaAndFlatData),
     ProcessingPipeline("BuildProject", "打包数据资源(Lua,Flat)", (int)ProcessingStepSort.BuildLuaAndFlatData)]
    public class CustomDataPipe : IProcessingPipeline
    {
        private IProcessingPipelineContent m_ContentPipe;
        
        public void OnEnable(IProcessingPipelineContent content)
        {
            m_ContentPipe = content;
        }

        public void Start()
        {
            PackageBuildData content = m_ContentPipe as PackageBuildData;
            if (content == null)
                return;

            try
            {
                if (content.IsBuildFirstPackage)
                {
                    AssetDatabase.DeleteAsset("Assets/Res/Data/luaData_update.bytes");
                    AssetDatabase.DeleteAsset("Assets/Res/Data/flatData_update.bytes");
                    AssetDatabase.DeleteAsset("Assets/Res/Data/fguiData_update.bytes");
                    AssetDatabase.DeleteAsset("Assets/Res/Data/luaData_hash.txt");
                    AssetDatabase.DeleteAsset("Assets/Res/Data/flatData_hash.txt");
                    AssetDatabase.DeleteAsset("Assets/Res/Data/fguiData_hash.txt");

                    DataCombineTools.CombineLuaFilesToBin(content.BuildTarget);
                    DataCombineTools.CombineFlatFilesToBin(content.BuildTarget);
                    DataCombineTools.CombineFGUIFilesToBin(content.BuildTarget);
                }
                else
                {
                    DataCombineTools.CombineLuaFilesToDiffBin(content.BuildTarget);
                    DataCombineTools.CombineFlatFilesToDiffBin(content.BuildTarget);
                    DataCombineTools.CombineFGUIFilesToDiffBin(content.BuildTarget);
                }
                AssetDatabase.Refresh();
            }
            catch (Exception e)
            {
                Debug.LogError(e.Message);
            }
        }

        public void Clear()
        {
        }

        public void OnGUI()
        {
        }
    }
}