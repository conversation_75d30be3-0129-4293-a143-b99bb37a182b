using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using Sirenix.Utilities;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Sultan.Tools 
{

    [Serializable]
    public class DependenceRule
    {
        [Header("规则名")]
        public string RuleName;
        public string RegexRule;
        public List<string> SearchList;
    }
    
    public class DependenceConfigData : ScriptableObject
    {
        public List<string> IgnoreFolderList;
        public List<string> IgnoreFileList;
        public List<DependenceRule> RuleList;
        public List<Object> ErrorFileList;

        private HashSet<string> ErrorFilePathSet = new HashSet<string>();
        
        public Dictionary<string, DependenceInfo> DependenceDict = new Dictionary<string, DependenceInfo>();
        
        private static readonly string CONFIG_DATA_PATH = "Assets/CustomSettings/DependenceConfigData.asset";

        #region API
        public static DependenceConfigData GetOrCreateConfigData()
        {
            DependenceConfigData data = AssetDatabase.LoadAssetAtPath<DependenceConfigData>(CONFIG_DATA_PATH);
            if (data == null)
            {
                data = ScriptableObject.CreateInstance<DependenceConfigData>();
                AssetDatabase.CreateAsset(data, CONFIG_DATA_PATH);
                Resources.UnloadAsset(data);
                data = AssetDatabase.LoadAssetAtPath<DependenceConfigData>(CONFIG_DATA_PATH);
            }
            return data;
        }

        [MenuItem("Assets/资源/检测依赖", false, 50)]
        public static void CheckSelectionDependence()
        {
            var data = GetOrCreateConfigData();
            data.ClearCache();
            var folderPath = data.GetSelectionFolderPath();
            Debug.Log($"开始检测目录:{folderPath}");
            if (string.IsNullOrEmpty(folderPath))
                return;
            
            data.CollectDirDependence(folderPath);
            data.FillIndeirectDependence();
            data.CheckDirDependence(folderPath, folderPath);
            if(MainEditorWindow.WINDOW == null)
                MainEditorWindow.ShowTools();
            MainEditorWindow.WINDOW.Select("依赖检测");
        }
        
        public void CollectDirDependence(string rootDir)
        {
            if (!AssetDatabase.IsValidFolder(rootDir))
                return;
            FileUtility.WalkDir(rootDir, rootDir, (path, shortPath, info) =>
            {
                if (path == rootDir)
                    return;
                
                if(path.EndsWith(".meta"))
                    return;
 
                if (AssetDatabase.IsValidFolder(path))
                {
                    CollectDirDependence(path);
                    return;
                }
                
                
                GetFileDirectDependence(path);
            });
        }

        public void FillIndeirectDependence()
        {
            foreach (var kv in DependenceDict)
            {
                kv.Value.FillIndirectDependence(DependenceDict);
            }
            
        }
        
        public DependenceInfo GetFileDirectDependence(string rootAsset)
        {
            // var guid = AssetDatabase.AssetPathToGUID(rootAsset);
            if (string.IsNullOrEmpty(rootAsset))
                return null;
            if (DependenceDict.TryGetValue(rootAsset, out var info))
                return info;
            
            info = new DependenceInfo();
            info.fileName = Path.GetFileName(rootAsset);
            var dependencies = AssetDatabase.GetDependencies(rootAsset);
            if (dependencies.Length > 0)
            {
                foreach (var dependency in dependencies)
                {
                    info.directDependence.Add(dependency);
                }
                DependenceDict.Add(rootAsset, info);     
            }

            return info;
        }
        
        public static void CheckSubDependencies(string rootAsset, string assetPath, Action<string, string> callback, ref HashSet<string> checkSets)
        {
            if (checkSets.Contains(assetPath))
                return;
            
            checkSets.Add(assetPath);
            var dependencies = AssetDatabase.GetDependencies(assetPath);
            Debug.Log(assetPath);
            if (dependencies.Length > 0)
            {
                foreach (var dependency in dependencies)
                {
                    CheckSubDependencies(assetPath,dependency, callback, ref checkSets);
                }
            }

            callback(rootAsset, assetPath);
        }

        public void CheckDirDependence(string rootDir, string checkFolder)
        {
            FileUtility.WalkDir(rootDir, rootDir, (path, shortPath, fileInfo) =>
            {
                if (AssetDatabase.IsValidFolder(path))
                {
                    return;
                }

                if (DependenceDict.TryGetValue(path, out var dInfo))
                {
                    CheckAllDependenceInDir(dInfo, checkFolder, IgnoreFolderList, IgnoreFileList);
                }
            });

            foreach (var assetPath in ErrorFilePathSet)
            {
                var obj = AssetDatabase.LoadAssetAtPath<Object>(assetPath);
                if(obj)
                    ErrorFileList.Add(obj);
            }
        }

        public void ClearCache()
        {
            DependenceDict.Clear();
            ErrorFileList.Clear();
            ErrorFilePathSet.Clear();
        }

        public string GetSelectionFolderPath()
        {
            string CheckFolder = "";
            if (Selection.objects.Length > 0)
            {
                var obj = Selection.objects[0];
                var foldPath = AssetDatabase.GetAssetPath(obj);
                if (!AssetDatabase.IsValidFolder(foldPath))
                {
                    foldPath = foldPath.Replace("\\", "/");
                    foldPath = foldPath.Substring(0, foldPath.LastIndexOf("/"));
                    CheckFolder = foldPath;
                }
                else
                    CheckFolder = foldPath;
            }

            return CheckFolder;
        }
        
        public bool CheckAllDependenceInDir(DependenceInfo info, string dirPath, List<string> ignoreFolder = null, List<string> ignoreFile = null)
        {
            Debug.Log($"开始检测 {info.fileName}");
            bool result = true;
            foreach (var assetPath in info.indirectDependence)
            {
                // var path = AssetDatabase.GUIDToAssetPath(guid);

                bool isIgnore = assetPath.EndsWith(".cs");
                if (ignoreFolder != null)
                {
                    foreach (var ignore in ignoreFolder)
                    {
                        if(isIgnore)
                            continue;
                        
                        if (assetPath.StartsWith(ignore))
                            isIgnore = true;
                    }     
                }
                    
                if(isIgnore)
                    continue;
                    
                if(ignoreFile != null && ignoreFile.Contains(assetPath))
                    continue;
                    
                if (!assetPath.StartsWith(dirPath))
                {
                    result = false;
                    ErrorFilePathSet.Add(assetPath);
                    Debug.LogError($"dependence Error: {assetPath}");
                }
            }
            Debug.Log($"检测结束");
            return result;
        }

        public void ExecuteRule()
        {
            HashSet<string> folderList = new HashSet<string>();
            foreach (var dependenceCfg in RuleList)
            {
                foreach (var searchPath in dependenceCfg.SearchList)
                {
                    FileUtility.WalkDirFolder(searchPath, searchPath, (path, shortPath, info) =>
                    {
                        if (Regex.Match(path, dependenceCfg.RegexRule).Success)
                        {
                            folderList.Add(path);
                        }
                    });
                }
            }
            
            foreach (var folderPath in folderList)
            {
                CollectDirDependence(folderPath);
            }
            FillIndeirectDependence();
            foreach (var folderPath in folderList)
            {
                CheckDirDependence(folderPath, folderPath);
            }
        }
        #endregion

    }
}