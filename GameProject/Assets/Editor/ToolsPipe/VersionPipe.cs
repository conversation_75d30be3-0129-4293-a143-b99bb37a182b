using Codice.CM.WorkspaceServer;
using GameApp.Update;
using Sultan.BuildTools;
using UnityEditor;
using UnityEngine;
using UnityEngine.AddressableAssets;

namespace Sultan.ToolsPipe
{
    [ProcessingPipeline("Addressable", "生成版本配置", (int)ProcessingStepSort.BuildVersionConfig),
     ProcessingPipeline("BuildProject", "生成版本配置", (int)ProcessingStepSort.BuildVersionConfig)]
    public class VersionPipe : IProcessingPipeline
    {
        private IProcessingPipelineContent m_ContentPipe = null;
        public void OnEnable(IProcessingPipelineContent content)
        {
            m_ContentPipe = content;
        }

        public void Start()
        {
            Versions v = AssetDatabase.LoadAssetAtPath<Versions>(Versions.DEFAULT_VERSIONS_ASSET);
            PackageBuildData content = m_ContentPipe as PackageBuildData ;
            // if (v == null)
            // {
            //     v = ScriptableObject.CreateInstance<Versions>();
            //     v.Version = content.ResVersion;
            //     AssetDatabase.CreateAsset(v, Versions.DEFAULT_VERSIONS_ASSET);
            // }

            // v.Version = content.ResVersion;
            v.ExportVersions(content.ResVersion);
            // v.isInner = content.PackageType == PackageType.Develop;
            EditorUtility.SetDirty(v);
            AssetDatabase.SaveAssetIfDirty(v);
            AssetDatabase.Refresh();
        }

        public void Clear()
        {
        }

        public void OnGUI()
        {
        }
    }
}