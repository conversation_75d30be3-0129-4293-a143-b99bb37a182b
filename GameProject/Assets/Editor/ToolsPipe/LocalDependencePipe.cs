using System.Collections.Generic;
using Sirenix.Utilities.Editor;
using Sultan.BuildTools;
using Sultan.Core;
using Terrains;
using UnityEditor;
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Settings;
using UnityEngine;

namespace Sultan.ToolsPipe
{
    [ProcessingPipeline("LocalResourceTools", "调整本地资源依赖分组", (int)ProcessingStepSort.LocalDependence)]
    [ProcessingPipeline("Addressable", "调整本地资源依赖分组", (int)ProcessingStepSort.LocalDependence)]
    public class LocalDependencePipe : SimpleProcessingPipeline
    {
        public override void Start()
        {
            LocalResourceSetting.GetLocalResourceSetting().ModifyDependenceEntry();
        }
    }

    [ProcessingPipeline("LocalResourceTools", "导出记录转为对象", (int)ProcessingStepSort.ResourceRecordTrans)]
    public class LocalResourceRecordPipe : SimpleProcessingPipeline
    {
        private static string LOCAL_RESOURCE_GROUP = "LocalResource";

        public void CheckTerrainPreload()
        {
            HashSet<string> checkSets = new HashSet<string>();
            
            var preloadAsset =
                AssetDatabase.LoadAssetAtPath<TerrainPreloadData>(
                    "Assets/Res/Scenes/Terrain/chunkAssets/chunk/world/TerrainPreload.asset");
            LocalResourceSetting localSetting = LocalResourceSetting.GetLocalResourceSetting();
            var settings = AddressableAssetSettingsDefaultObject.Settings;
            AddressableAssetGroup group = settings.FindGroup(LOCAL_RESOURCE_GROUP);

            // HashSet<string> finalGuids = new HashSet<string>();
            // finalGuids.AddRange(localSetting.FinalResourceGUIDs);
            foreach (var path in preloadAsset.PreloadDataPath)
            {
                EditorUtils.CheckDependencies($"Assets/Res/{path}", group, (entry, assetGroup) =>
                {
                    // finalGuids.Add(entry.guid);
                    localSetting.FinalResourceGUIDs.Add(entry.guid);
                    Debug.LogError($"preload download res: {entry.AssetPath}");
                }, ref checkSets);
            }
            
        }
        public override void Start()
        {
            LocalResourceSetting.GetLocalResourceSetting().RecordAssetsToObject();
            LocalResourceSetting.GetLocalResourceSetting().CollectDependence();
        }

        public override void OnGUI()
        {
            base.OnGUI();
            if (GUILayout.Button("检测地形预加载资源"))
            {
                CheckTerrainPreload();
            }
        }

        private void CollectPreloadDependence()
        {
            
        }
    }
    
    [ProcessingPipeline("LocalResourceTools", "导出本地资源信息", (int)ProcessingStepSort.ExportResourceRecord)]
    public class LocalResourceExportPipe : SimpleProcessingPipeline
    {
        public override void Start()
        {
            ExportFactoryRecord(CacheOpFactory.RecordAssetSet);
        }
        
        public static void ExportFactoryRecord(HashSet<string> records)
        {
            HashSet<AddressableAssetEntry> guids = new HashSet<AddressableAssetEntry>();
            var localSetting = LocalResourceSetting.GetLocalResourceSetting();
            localSetting.RecordAssetPaths.Clear();

            foreach (var record in records)
            {
                if(record.StartsWith("Config/") || record.StartsWith("FGUI/") || record.StartsWith("Packages"))
                    continue;
                localSetting.RecordAssetPaths.Add(record);
            }
            AssetDatabase.SaveAssetIfDirty(localSetting);
        }
    }
    
    [ProcessingPipeline("LocalResourceTools", "预加载资源依赖处理", (int)ProcessingStepSort.ResourceRecordTrans+1)]
    public class TerrainPreloadDataPipe : SimpleProcessingPipeline
    {
        
    }

}