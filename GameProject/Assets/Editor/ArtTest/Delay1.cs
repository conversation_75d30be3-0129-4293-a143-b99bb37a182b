using UnityEngine;
using System.Collections;

public class Delay1 : MonoBehaviour {
	
	public float DelayTime = 0.0f;
    public float RunningTime = 0.0f;
    public float RePlayTime = 0.0f;
    public bool isEnabling = false;
	// Use this for initialization
	void Awake () {		
	}

	void OnEnable()
	{
		if(!isEnabling)
		{
			gameObject.SetActive (false);
			Invoke("DelayFunc", DelayTime);
			isEnabling = true;

        }
	}

	void DelayFunc() 
	{
		gameObject.SetActive(true);
		if(isEnabling)
		{
			CancelInvoke ("DelayFunc");
			isEnabling = false;
          
        }
	}

    void RunFunc()
    {
        //gameObject.SetActive (false);
        if (!isEnabling)
        {
            gameObject.SetActive(false);
            Invoke ("DelayFunc", RunningTime);
            isEnabling = true;
            



        }
    }

}
