using System;
using System.Text.RegularExpressions;
using CI;
using Platform;
using RuntimeInspectorNamespace;
using UnityEditor;
using UnityEditor.Android;
using UnityEngine;

namespace Sultan.Tools
{
    [EditorTools("控制台")]
    public class ConsoleDebuggerTool : IEditorTools
    {
        enum ConnectState : byte
        {
            Idle,
            Connecting,
            Connected,
        }

        private string ipStr = "127.0.0.1:6666";
        private string textMsg = "print(1)";
        private ConnectState _ConnectState = ConnectState.Idle;
        private Vector2 scrollViewPosition;

        public void OnEnable()
        {
        }

        public void DrawMainGUI()
        {
            try
            {
                EditorGUIUtility.labelWidth = 50;
                EditorGUILayout.BeginHorizontal();
                ipStr = EditorGUILayout.TextField("IP地址:", ipStr);
                if (_ConnectState == ConnectState.Idle && GUILayout.Button("连接", GUILayout.Width(60)))
                    StartConnect();
                else if (_ConnectState == ConnectState.Connected && GUILayout.Button("断开", GUILayout.Width(60)))
                    CloseConnect();
                if (GUILayout.Button("文件", GUILayout.Width(50)))
                {
                    string filePath = EditorUtility.OpenFilePanel("Open File", "", "");
                    if (!string.IsNullOrEmpty(filePath))
                        textMsg = System.IO.File.ReadAllText(filePath);
                }

                EditorGUILayout.EndHorizontal();

                this.scrollViewPosition = GUILayout.BeginScrollView(this.scrollViewPosition);
                GUILayout.BeginVertical();
                textMsg = GUILayout.TextArea(textMsg, GUILayout.MaxHeight(99999));
                GUILayout.EndVertical();
                GUILayout.EndScrollView();
                if (GUILayout.Button("发送消息"))
                {
                    StartConnect();
                    SendMsg();
                }

            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        #region func

        private bool CheckPattern(string ipAddr)
        {
            string pattern = @"^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+$";
            Regex regex = new Regex(pattern);
            return regex.IsMatch(ipAddr);
        }

        private void StartConnect()
        {
            if (!Application.isPlaying)
                throw new Exception("游戏运行中才能使用");
            
            if (_ConnectState == ConnectState.Connected || _ConnectState == ConnectState.Connecting)
                return;
            
            if (!CheckPattern(ipStr))
                throw new Exception("IP地址格式错误");
            
#if UNITY_EDITOR
            _ConnectState = ConnectState.Connecting;

            var strs = ipStr.Split(":");
            var isConnect = ConsoleDebuggerClient.GetInstance().Connect(strs[0], int.Parse(strs[1]));
            if (isConnect)
                _ConnectState = ConnectState.Connected;
            else
            {
                _ConnectState = ConnectState.Idle;
                throw new Exception("连接失败");
            }
#endif
        }

        private void SendMsg()
        {
            if (!Application.isPlaying)
                throw new Exception("游戏运行中才能使用");
            
            if (_ConnectState != ConnectState.Connected)
                return;
            ConsoleDebuggerClient.GetInstance().Send(textMsg);
        }

        private void CloseConnect()
        {
            if (!Application.isPlaying)
                throw new Exception("游戏运行中才能使用");
            
            if (_ConnectState != ConnectState.Connected)
                return;
            _ConnectState = ConnectState.Idle;
            ConsoleDebuggerClient.GetInstance().Close();
        }

        #endregion func

    }
}