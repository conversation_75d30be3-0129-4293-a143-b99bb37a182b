using UnityEngine;
using UnityEditor;
using System.IO;

public class ResourcePathCopy
{
    private string sourcePath = "";

    [MenuItem("Assets/复制资源路径")]
    public static void DoResourcePathCopy()
    {
        // 右键选中的资源，拷贝资源路径
        var objs = Selection.objects;
        var path = "";
        foreach (var obj in objs)
        {
            var p = AssetDatabase.GetAssetPath(obj);
            if(path.Length > 0)
                path += "\n";
            path += p.Replace("\\", "/").Replace("Assets/Res/", "").Replace("Assets/ArtTmp/", "").Replace("Assets/", "");
        }
        GUIUtility.systemCopyBuffer = path;
    }
}