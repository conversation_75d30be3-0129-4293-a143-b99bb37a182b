using UnityEngine;
using UnityEditor;

namespace Sultan.Test.Editor
{
    public class TextureSizeSyncer : EditorWindow
    {
        [MenuItem("Assets/SyncTexturePlatformSetting", false, 2)]
        static void SyncMaxSize()
        {
            // 获取选中的对象
            Object selectedObject = Selection.activeObject;

            if (selectedObject == null)
            {
                Debug.LogWarning("Please select a folder.");
                return;
            }

            string folderPath = AssetDatabase.GetAssetPath(selectedObject);

            // 如果不是文件夹则返回
            if (!AssetDatabase.IsValidFolder(folderPath))
            {
                Debug.LogWarning("Selected item is not a folder.");
                return;
            }

            // 获取文件夹内所有的纹理资源
            string[] guids = AssetDatabase.FindAssets("t:Texture2D", new[] { folderPath });

            foreach (string guid in guids)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                TextureImporter textureImporter = AssetImporter.GetAtPath(assetPath) as TextureImporter;

                if (textureImporter != null)
                {
                    // 获取iOS和Android平台的设置
                    TextureImporterPlatformSettings iosSettings = textureImporter.GetPlatformTextureSettings("iPhone");
                    TextureImporterPlatformSettings androidSettings = textureImporter.GetPlatformTextureSettings("Android");

                    // 检查是否需要同步最大尺寸
                    if (iosSettings.maxTextureSize != androidSettings.maxTextureSize)
                    {
                        int maxSize = androidSettings.maxTextureSize;
                        TextureImporterFormat format = androidSettings.format;

                        // 设置iOS平台的最大尺寸
                        iosSettings.maxTextureSize = maxSize;
                        iosSettings.format = format;
                        textureImporter.SetPlatformTextureSettings(iosSettings);
                        

                        // 应用更改
                        AssetDatabase.ImportAsset(assetPath);
                        Debug.Log($"Synced max size for texture at path: {assetPath}");
                    }
                }
            }

            AssetDatabase.Refresh();
            Debug.Log("All textures have been processed.");
        }
    }
}