using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

public class ImageExporterEditorWindow : EditorWindow
{
    #region [数据成员]

    private bool isSupportAlpha;
    private Vector2Int resolution = new Vector2Int(1280,720); //默认值

    // private int superSize;
    private string fileName;
    private string filePath;

    #endregion

    #region [初始化窗口]

    [MenuItem("Tools/TA Tools/截屏...")]
    static void ShowWindow()
    {
        var window = GetWindow<ImageExporterEditorWindow>();
        window.titleContent = new GUIContent("截屏...");
        window.Show();
    }

    #endregion


    #region [绘制窗口]

    void OnGUI()
    {
        isSupportAlpha = EditorGUILayout.Toggle("是否支持透明(URP下关闭后处理)", isSupportAlpha);
        resolution = EditorGUILayout.Vector2IntField("分辨率", resolution);
        //superSize = EditorGUILayout.IntSlider("尺寸倍数", superSize, 1, 5);//负数和0无效，1以上成倍数增加
        fileName = EditorGUILayout.TextField("图片名称", fileName);

        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.PrefixLabel("保存路径"); //这种文本可以自适应行宽
        if (GUILayout.Button("..."))
            filePath = EditorUtility.OpenFolderPanel("选择文件夹", "Assets/", ""); //打开文件夹，设置默认路径

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.LabelField(filePath); //把文件夹的路径打印到label中

        if (GUILayout.Button("截屏并保存图片"))
        {
            Camera cam = Camera.main; //获取主相机
            int width = resolution.x;
            int height = resolution.y;
            
            
            string path = string.Format("{0}/{1}.png", filePath, fileName); //得到完整的截屏路径
            // ScreenCapture.CaptureScreenshot(path,superSize);//使用它另一个重载，可以加上尺寸的缩放，有限制，不用了

            RenderTexture
                rt = RenderTexture.GetTemporary(width, height, 0, RenderTextureFormat.ARGB32); //新建RT,优化写法，从池子里找没有的话再新建
            cam.targetTexture = rt;

            CameraClearFlags clearFlags = cam.clearFlags;
            Color backgroundColor = cam.backgroundColor; //记录一下当前的清除标签和背景色

            if (isSupportAlpha)
            {
                cam.clearFlags = CameraClearFlags.SolidColor; //设置当前的清除标签和背景色
                cam.backgroundColor = new Color(0,0,0,0);
            }


            cam.Render();

            RenderTexture.active = rt; //设置当前激活的是rt


            //1.把屏幕像素写入到一个纹理（Texture2D）中
            Texture2D tex = new Texture2D(width, height, TextureFormat.ARGB32, false);
            tex.ReadPixels(new Rect(0, 0, width, height), 0, 0); //默认从屏幕的xy位置读取宽高的像素
            tex.Apply(); //应用给图片纹理
            //2.把纹理编译成二进制字节
            byte[] bytes;
            bytes = tex.EncodeToPNG();
            //3.把字节数据保存成纹理
            File.WriteAllBytes(path, bytes); //把字节数组写入路径


            cam.clearFlags = clearFlags;
            cam.backgroundColor = backgroundColor; //恢复回来

            cam.targetTexture = null; //最终对rt进行置空
            RenderTexture.ReleaseTemporary(rt); //释放rt图
            AssetDatabase.Refresh(); //刷新资源
        }
        
        if (GUILayout.Button("打开保存图片的位置"))
        {
            OpenSaveLocation();
        }
        EditorGUILayout.LabelField("v1.0  By JUSYOU");
    }
    
    private void OpenSaveLocation()
    {
        if (!string.IsNullOrEmpty(filePath) && Directory.Exists(filePath))
        {
            // 使用Process.Start打开文件夹
            System.Diagnostics.Process process = new System.Diagnostics.Process();
            process.StartInfo = new System.Diagnostics.ProcessStartInfo
            {
                FileName = filePath,
                UseShellExecute = true, // 必须为true才能正确处理文件夹路径
                Verb = "open"
            };
            process.Start();
        }
        else
        {
            EditorUtility.DisplayDialog("错误", "保存路径无效或未设置。", "确定");
        }
    }

    #endregion
}