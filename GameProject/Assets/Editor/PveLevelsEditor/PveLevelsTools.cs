using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using MeshCombineStudio;
using SRF;
using Sultan.Core;
using TMPro;
using Unity.Mathematics;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.ProBuilder;
using UnityEngine.ProBuilder.Shapes;
using UnityEngine.SocialPlatforms;
namespace Sultan.Tools
{
    [EditorTools("pve导出工具")]
    public class PveLevelsTools : IEditorTools
    {
        private string loadPath = "Assets/UnUsed/PveLevels/";
        private string outPutpath = "Assets/Res/Prefabs/PveLevels/";

        private string textPath = "Assets/UnUsed/PveLevels/TextLevel.prefab";
        private string SpherePath = "Assets/ArtTmp/Scenes/Prefabs/Com/com_pve01_beacon_01.prefab";

        private float padding = 2.0f;
        public GameObject rootObj;
        public void OnEnable()
        {
        }

        public void DrawMainGUI()
        {
            // rootObj = (GameObject)EditorGUILayout.ObjectField("关卡父节点", rootObj, typeof(GameObject), false);

            if (GUILayout.Button("重命名和显示文本"))
            {
                RenameLevels();
            }

            if (GUILayout.Button("导出预制体"))
            {
                OutputConfig();
            }
        }

        public void Update()
        {

        }

        private void OutputConfig()
        {
            if (Directory.Exists(loadPath))
            {
                DirectoryInfo directoryInfo = new DirectoryInfo(loadPath);
                FileInfo[] files = directoryInfo.GetFiles("*");
                Physics.autoSimulation = false;
                Physics.Simulate(Time.fixedDeltaTime);
                for (int i = 0; i < files.Length; i++)
                {
                    if (files[i].Name.EndsWith(".prefab") && !files[i].Name.StartsWith("Text"))
                    {
                        var prefabRoot = new GameObject();
                        Dictionary<int, List<Vector3>> spherePosList = new Dictionary<int, List<Vector3>>();
                        Debug.Log(loadPath + files[i].Name);
                        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(loadPath + files[i].Name);
                        var levelObj = GameObject.Instantiate(prefab);
                        var root = prefab.transform.Find("Root");
                        if (root == null)
                        {
                            Debug.LogError("没有找到Root节点");
                            continue;
                        }
                        bool needAddLine = isPveLevels(files[i].Name);

                        if (needAddLine)
                        {
                            var lineRoot = new GameObject();
                            lineRoot.name = "lineRoot";
                            lineRoot.transform.parent = prefabRoot.transform;
                            lineRoot.transform.position = new Vector3(0, 0, 0);
                            BoxCollider[] boxColliders = levelObj.GetComponentsInChildren<BoxCollider>();
                            foreach (var item in boxColliders)
                            {
                                item.enabled = false;
                            }

                            SphereCollider[] sphereColliders = levelObj.GetComponentsInChildren<SphereCollider>();
                            foreach (var item in sphereColliders)
                            {
                                item.enabled = false;
                            }
                            //给所有地板加meshcollider transform.GetChild(0).Find("MeshCombineStudio(Clone)")
                            MeshRenderer[] mfs = levelObj.GetComponentsInChildren<MeshRenderer>();
                            addMeshCollider(mfs);
                        }
                        for (int j = 0; j < root.transform.childCount; j++)
                        {
                            var child = root.transform.GetChild(j);
                            var itemCopy = new GameObject();
                            itemCopy.transform.parent = prefabRoot.transform;
                            itemCopy.transform.position = child.transform.position;
                            itemCopy.transform.rotation = child.transform.rotation;
                            itemCopy.transform.localScale = child.transform.localScale;
                            itemCopy.name = child.name;

                            if (needAddLine && j + 1 < root.transform.childCount)
                            {
                                int levelId = getLevelIdByName(child.name);
                                var nextChild = root.transform.GetChild(j + 1);
                                int nextLevelId = getLevelIdByName(nextChild.name);
                                if (levelId > 0 && nextLevelId > 0)
                                {
                                    Vector3 beginPos = child.transform.position;
                                    Vector3 endPos = nextChild.transform.position;
                                    float distance = Vector3.Distance(beginPos, endPos);
                                    int num = (int)MathF.Ceiling(distance / padding);
                                    List<Vector3> lst = new List<Vector3>();
                                    for (int k = 1; k < num; k++)
                                    {
                                        Vector3 newPos = Vector3.Lerp(beginPos, endPos, (float)k / (float)num);
                                        Ray ray = new Ray(new Vector3(newPos.x, 15, newPos.z), Vector3.down);
                                        RaycastHit[] hits = new RaycastHit[10];
                                        int hitCount = Physics.RaycastNonAlloc(ray, hits, 50);
                                        float posY = float.MinValue;
                                        for (int index = 0; index < hitCount; index++)
                                        {
                                            Debug.Log("hits[index].point.y" + hits[index].point.y + "name:" + hits[index].transform.name);
                                            if (hits[index].point.y > posY)
                                            {
                                                posY = hits[index].point.y;
                                            }
                                        }
                                        // if (posY == float.MinValue)
                                        // {
                                        //     if (lst.Count > 0)
                                        //         posY = lst[lst.Count - 1].y;
                                        //     else
                                        //         posY = 0;
                                        // }
                                        Debug.Log("levelId" + levelId + "newPos:" + newPos + " newPosY:" + posY + "hitCount: " + hitCount);
                                        lst.Add(new Vector3(newPos.x, posY, newPos.z));
                                    }
                                    spherePosList[levelId] = lst;
                                }
                            }
                        }
                        PrefabUtility.SaveAsPrefabAsset(prefabRoot, outPutpath + files[i].Name);

                        GameObject newprefab = AssetDatabase.LoadAssetAtPath<GameObject>(outPutpath + files[i].Name);
                        GameObject newinstance = PrefabUtility.InstantiatePrefab(newprefab) as GameObject;

                        //修正一下中间点突然变高或者变低情况
                        foreach (var item in spherePosList)
                        {
                            for (int j = 0; j < item.Value.Count; j++)
                            {
                                if (j > 0 && j + 1 < item.Value.Count)
                                {
                                    Vector3 lastPos = item.Value[j - 1];
                                    Vector3 nowPos = item.Value[j];
                                    Vector3 nextPos = item.Value[j + 1];
                                    if ((math.abs(nowPos.y - lastPos.y) > 2 && math.abs(nextPos.y - nowPos.y) > 2 && math.abs(nextPos.y - lastPos.y) < 0.1) || nowPos.y == float.MinValue)
                                    {
                                        nowPos.y = (float)math.lerp(lastPos.y, nextPos.y, 0.5);
                                        item.Value[j] = nowPos;
                                    }
                                }
                            }

                        }


                        var fixedRoot = root.Find("fixed");
                        if (fixedRoot != null)
                        {
                            foreach (Transform item in fixedRoot.transform)
                            {
                                if (PrefabUtility.IsPartOfAnyPrefab(item.gameObject) && !item.name.StartsWith("TextLevel"))
                                {
                                    var source = PrefabUtility.GetCorrespondingObjectFromSource(item.gameObject);
                                    GameObject newItem = PrefabUtility.InstantiatePrefab(source) as GameObject;
                                    newItem.transform.parent = newinstance.transform.Find("fixed");
                                    newItem.transform.position = item.position;
                                    newItem.transform.rotation = item.rotation;
                                    newItem.transform.localScale = item.localScale;
                                }
                            }
                        }
                        if (needAddLine)
                        {
                            GameObject spherePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(SpherePath);
                            foreach (var item in spherePosList)
                            {
                                for (int j = 0; j < item.Value.Count; j++)
                                {
                                    GameObject newItem = PrefabUtility.InstantiatePrefab(spherePrefab) as GameObject;
                                    newItem.transform.parent = newinstance.transform.Find("lineRoot");
                                    newItem.transform.position = item.Value[j];
                                    newItem.name = item.Key.ToString() + j;
                                }

                            }
                            // clearMeshCollider(mfs);
                        }
                        PrefabUtility.SaveAsPrefabAsset(newinstance, outPutpath + files[i].Name);
                        GameObject.DestroyImmediate(prefabRoot);
                        GameObject.DestroyImmediate(newinstance);
                        GameObject.DestroyImmediate(levelObj);
                    }

                }

            }
        }

        private void RenameLevels()
        {

            rootObj = Selection.activeGameObject;
            Debug.Log(AssetDatabase.GetAssetPath(rootObj.transform.parent));
            if (rootObj == null)
            {
                Debug.LogError("没有找到Root节点");
                return;
            }
            if (rootObj.name != "Root")
            {
                Debug.LogError("选中的不是Root节点");
                return;
            }

            IEnumerable<Transform> list = rootObj.transform.GetChildren();
            List<Transform> listItem = list.ToList();
            // listItem.Sort((a, b) => a.name.CompareTo(b.name));
            int firstId = Convert.ToInt32(listItem[0].name);
            for (int i = 0; i < listItem.Count; i++)
            {

                int levelId = getLevelIdByName(listItem[i].name);
                if (levelId > 0)
                {
                    firstId = levelId;
                    break;
                }
            }
            GameObject textPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(textPath);
            foreach (Transform item in listItem)
            {
                int levelId = getLevelIdByName(item.name);
                if (levelId > 0)
                {
                    Debug.Log(firstId.ToString());
                    item.name = firstId.ToString();
                    firstId = firstId + 1;
                }

                TextMeshPro obj = item.GetComponentInChildren<TextMeshPro>();
                if (obj != null)
                {
                    obj.text = item.name;
                    obj.transform.localPosition = new Vector3(0, 1, 0);
                    continue;
                }
                var textObj = GameObject.Instantiate(textPrefab);
                textObj.transform.parent = item.transform;
                textObj.transform.localPosition = new Vector3(0, 1, 0);
                obj = textObj.transform.GetComponent<TextMeshPro>();
                obj.text = item.name;
            }
            PrefabUtility.SaveAsPrefabAsset(rootObj.transform.parent.gameObject, loadPath + rootObj.transform.parent.name + ".prefab");
        }

        private int getLevelIdByName(string name)
        {
            if (int.TryParse(name, out int levelId))
            {
                if (levelId > 1000000 && levelId < 100000000)
                    return levelId;

            }
            return 0;
        }

        private bool isPveLevels(string name)
        {
            string[] sArray = name.Split(new string[] { "pveLevels_", ".prefab" }, StringSplitOptions.RemoveEmptyEntries);
            int.TryParse(sArray[0], out int chapterId);
            return chapterId < 10000;
        }

        private float getMinLandPosY(MeshFilter[] filters, float x, float z)
        {

            return 0;
        }

        private void addMeshCollider(MeshRenderer[] meshRenderers)
        {
            foreach (var item in meshRenderers)
            {
                var gameObject = item.gameObject;
                if (gameObject.layer == LayerMask.NameToLayer("Default") && !gameObject.name.Contains("water") && !gameObject.GetComponent<SphereCollider>())
                {
                    Undo.AddComponent<MeshCollider>(gameObject);
                    var collider = gameObject.transform.GetComponent<MeshCollider>();
                    // collider.convex = true; // 必须设置为凸包
                }
            }
        }

        private void clearMeshCollider(MeshRenderer[] meshRenderers)
        {
            foreach (var item in meshRenderers)
            {
                var gameObject = item.gameObject;
                var mc = gameObject.GetComponent<MeshCollider>();
                GameObject.DestroyImmediate(mc);
            }
        }
    }
}