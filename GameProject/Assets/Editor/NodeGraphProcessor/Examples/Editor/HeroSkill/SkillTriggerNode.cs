using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GraphProcessor;
using System.Linq;
using Sirenix.OdinInspector;

[System.Serializable, NodeMenuItem("Skill/SkillTrigger")]
public class SkillTriggerNode : BaseNode
{
    [Input(name = "id"), ShowAsDrawer] public long input_id;
    [Input(name = "triggerEvent"), ShowAsDrawer] public int input_triggerEvent;
    [Input(name = "triggerCondition"), ShowAsDrawer] public string input_triggerCondition;
    [Input(name = "actions")] public List<int> input_actions = new List<int>();
    [Input(name = "cd"), ShowAsDrawer]  public int input_cd;
    [Input(name = "maxTime"), ShowAsDrawer] public int input_maxTime;
    [Input(name = "isForAll"), ShowAsDrawer] public int input_isForAll;
    [Input(name = "group"), ShowAsDrawer] public int input_group;
    [Input(name = "isSkillAdd"), ShowAsDrawer] public bool input_isSkillAdd;
    
    public string nodeDesc;
    
    [Output(name = "actions", allowMultiple = true),ShowPortIcon(IconNameMatchedInUSSFile = "Array")] public List<long> output_actions = new List<long>();
    public override string name => "SkillTrigger";

    protected override void Process()
    {
        //output = input * 42;
    }
    
    [CustomPortBehavior(nameof(output_actions))]
    IEnumerable< PortData > ListPortBehavior(List< SerializableEdge > edges)
    {
        for (int i = 0; i < output_actions.Count; i++)
        {
            yield return new PortData {
                displayName = "Action:" + output_actions[i].ToString(),
                displayType = typeof(long),
                identifier = output_actions[i].ToString(), // Must be unique
            };
        }
    }
}