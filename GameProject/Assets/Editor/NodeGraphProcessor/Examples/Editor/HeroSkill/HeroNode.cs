using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GraphProcessor;
using System.Linq;
using Sirenix.OdinInspector;

[System.Serializable, NodeMenuItem("Skill/Hero")]
public class HeroNode : BaseNode
{
    [Input(name = "id"), ShowAsDrawer] public long input_id;
    [Input(name = "skills"), ShowPortIcon(IconNameMatchedInUSSFile = "Array")] public List<long> input_skills = new List<long>();
    [Output(name = "skills"),ShowPortIcon(IconNameMatchedInUSSFile = "Array")] public List<long> output_skills = new List<long>(){};
    
    public override string name => "Hero";
    public string desc = "Hero";
    
    public string nodeDesc;

    protected override void Process()
    {
        //output = input * 42;
    }
    
    [CustomPortBehavior(nameof(output_skills))]
    IEnumerable< PortData > ListPortBehavior(List< SerializableEdge > edges)
    {
        for (int i = 0; i < output_skills.Count; i++)
        {
            yield return new PortData {
                displayName = "skill:" + output_skills[i].ToString(),
                displayType = typeof(long),
                identifier = output_skills[i].ToString(), // Must be unique
            };
        }
    }
}