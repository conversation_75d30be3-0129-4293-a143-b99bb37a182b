using System;
using System.Linq;
using Examples.Editor._05_All;
using GraphProcessor;
using NodeGraphProcessor.Examples;
using UnityEditor;
using UnityEditor.Experimental.GraphView;
using UnityEngine;
using XLua;

namespace Examples.Editor._05_All
{
    public class HeroSkillTest
    {
        [MenuItem("Skill/HeroSkillTest")]
        public static void TestHeroSkill()
        {
           TestSkill_Edward(); 
        }

        public static void TestBasic()
        {
            //1.create graph
            SkillGraph skillGraph = ScriptableObject.CreateInstance<SkillGraph>();
            
            //1.2 add node 1
            TextNode txtNode = BaseNode.CreateFromType<TextNode>(Vector2.zero);
            txtNode.output = "test1";
            //add node to graph
            skillGraph.AddNode(txtNode);
            
            //1.3 add node 2
            ConsoleNode logNode = BaseNode.CreateFromType<ConsoleNode>(new Vector2(10, 100));
            skillGraph.AddNode(logNode);

            //1.4 connect 
            // skillGraph.Connect(logNode.inputPorts.First(), txtNode.outputPorts.First(), true);
            // skillGraph.Connect(logNode.GetInputPort("logText"), txtNode.GetOutputPort("output"), true);
            
            skillGraph.Connect(logNode.GetInputPort("obj"), txtNode.GetOutputPort("output"), true);
            
            //4.open it
            EditorWindow.GetWindow<SkillGraphWindow>().InitializeGraph(skillGraph); 
        }
        
        public static void TestSkill_Edward()
        {
            //1.create graph
            SkillGraph skillGraph = ScriptableObject.CreateInstance<SkillGraph>();
            
            //1.2 add node 1
            TextNode txtNode = BaseNode.CreateFromType<TextNode>(Vector2.zero);
            txtNode.output = "test1";
            //add node to graph
            skillGraph.AddNode(txtNode);
            
            //1.3 add node 2
            ConsoleNode logNode = BaseNode.CreateFromType<ConsoleNode>(new Vector2(10, 100));
            skillGraph.AddNode(logNode);

            //1.4 connect 
            // skillGraph.Connect(logNode.inputPorts.First(), txtNode.outputPorts.First(), true);
            // skillGraph.Connect(logNode.GetInputPort("logText"), txtNode.GetOutputPort("output"), true);
            
            skillGraph.Connect(logNode.GetInputPort("obj"), txtNode.GetOutputPort("output"), true);



            //skill test
            SkillNode skillNode = BaseNode.CreateFromType<SkillNode>(new Vector2(200, 200));
            skillNode.output_actions.Add(1);
            skillNode.output_actions.Add(2);
            skillNode.output_actions.Add(3);
            skillGraph.AddNode(skillNode);

            SkillActionNode actionNode1 = BaseNode.CreateFromType<SkillActionNode>(new Vector2(300, 200));
            SkillActionNode actionNode2 = BaseNode.CreateFromType<SkillActionNode>(new Vector2(300, 300));
            SkillActionNode actionNode3 = BaseNode.CreateFromType<SkillActionNode>(new Vector2(300, 400));
            skillGraph.AddNode(actionNode1);
            skillGraph.AddNode(actionNode2);
            skillGraph.AddNode(actionNode3);

            skillGraph.Connect(actionNode1.GetInputPort("input_id"), skillNode.GetPort("output_actions", 1.ToString()), true);
            skillGraph.Connect(actionNode2.GetInputPort("input_id"), skillNode.GetPort("output_actions", 2.ToString()), true);
            skillGraph.Connect(actionNode3.GetInputPort("input_id"), skillNode.GetPort("output_actions", 3.ToString()), true);
            
            //4.open it
            EditorWindow.GetWindow<HeroSkillGraphWindow>().InitializeGraph(skillGraph); 
        }


        public static HeroSkillGraphWindow ShowLuaSkillGraph(LuaTable LuaSkill)
        {
            SkillGraph graph = LuaSkill.Get<SkillGraph>("graph");
            Action initCallback = LuaSkill.Get<Action>("onInitialized");
            var window = EditorWindow.GetWindow<HeroSkillGraphWindow>();
            window.InitializeGraph(graph, initCallback);
            Action<int> selectCallback = LuaSkill.Get<Action<int> >("onHeroSelected");
            window.setHeroSelectedCalllback(selectCallback);
            return window;
        }
    }
}