using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GraphProcessor;
using System.Linq;
using Sirenix.OdinInspector;

[System.Serializable, NodeMenuItem("Skill/SkillNode")]
public class SkillNode : BaseNode
{
    [Input(name = "id"), ShowAsDrawer] public long input_id;
    [Input(name = "name"), ShowAsDrawer] public string input_name;
    [Input(name = "desc"), ShowAsDrawer] public string input_desc;
    [Input(name = "descParam"), ShowAsDrawer] public string input_descParam;
    [Input(name = "lv"), ShowAsDrawer]  public int input_lv = 5;
    [Input(name = "icon"), ShowAsDrawer] public string input_icon;
    [Input(name = "ani"), ShowAsDrawer] public string input_ani;
    [Input(name = "skillType"), ShowAsDrawer] public int input_skillType;
    [Input(name = "actions"), ShowPortIcon(IconNameMatchedInUSSFile = "Array")] public List<long> input_actions = new List<long>();
    [Input(name = "castEffectShow"),ShowPortIcon(IconNameMatchedInUSSFile = "Array")] public List<int> input_castEffectShow = new List<int>();
    [Input(name = "angry"), ShowAsDrawer] public int input_angry;
    [Input(name = "blackScreen"), ShowAsDrawer] public int input_blackScreen;
    [Input(name = "initCd"), ShowAsDrawer] public int input_initCd;
    [Input(name = "cd"), ShowAsDrawer] public int input_cd;
    [Input(name = "castDis"), ShowAsDrawer] public float input_castDis;
    [Input(name = "targetCenter"), ShowAsDrawer, ShowPortIcon(ShowIcon = true, IconNameMatchedInUSSFile = "Object")] public int input_targetCenter;
    [Input(name = "targetCenterCondParm"), ShowAsDrawer] public int input_targetCenterCondParm;
    [Input(name = "targetRange"), ShowAsDrawer] public int input_targetRange;
    [Input(name = "targetRangeParam"), ShowAsDrawer] public int input_targetRangeParam;
    [Input(name = "targetType"), ShowAsDrawer] public int input_targetType;
    [Input(name = "targetCond"), ShowAsDrawer] public int input_targetCond;
    [Input(name = "targetCondParm"), ShowAsDrawer] public int input_targetCondParm;
    [Input(name = "targetNum"), ShowAsDrawer] public int input_targetNum;
    [Input(name = "recoverAngry"), ShowAsDrawer] public int input_recoverAngry;
    [Input(name = "isChannel"), ShowAsDrawer] public bool input_isChannel;
    [Input(name = "channelTime"), ShowAsDrawer] public int input_channelTime;
    [Input(name = "preAtkTime"), ShowAsDrawer] public int input_preAtkTime;
    [Input(name = "afterAtkTIme"), ShowAsDrawer] public int input_afterAtkTime;
    [Input(name = "sound"), ShowAsDrawer] public string input_sound;
    
    // public string input_name;
    // public string input_desc;
    // public string input_descParam;
    // public int input_lv = 5;
    // public string input_icon;
    // public string input_ani;
    // public int input_skillType;
    // public List<int> input_actions = new List<int>(){1,2,3};
    // public List<int> input_castEffectShow = new List<int>();
    // public int input_angry;
    // public int input_blackScreen;
    // public int input_initCd;
    // public int input_cd;
    // public float input_castDis;
    // public int input_targetCenter;
    // public int input_targetCenterCondParm;
    // public int input_targetRange;
    // public int input_targetRangeParam;
    // public int input_targetType;
    // public int input_targetCond;
    // public int input_targetCondParm;
    // public int input_targetNum;
    // public int input_recoverAngry;
    // public bool input_isChannel;
    // public int input_channelTime;
    // public int input_preAtkTime;
    // public int input_afterAtkTime;
    // public string input_sound = "a.sound";

    public string nodeDesc;
    
    
    [Output(name = "actions"),ShowPortIcon(IconNameMatchedInUSSFile = "Array")] public List<long> output_actions = new List<long>(){};
    [Input(name = "castEffectShow"),ShowPortIcon(IconNameMatchedInUSSFile = "Array")] public List<long> output_castEffectShow = new List<long>(){};
    public override string name => "Skill";

    protected override void Process()
    {
        //output = input * 42;
    }
    
    [CustomPortBehavior(nameof(output_actions))]
    IEnumerable< PortData > ListPortBehavior(List< SerializableEdge > edges)
    {
        for (int i = 0; i < output_actions.Count; i++)
        {
            yield return new PortData {
                displayName = "Action:" + output_actions[i].ToString(),
                displayType = typeof(long),
                identifier = output_actions[i].ToString(), // Must be unique
            };
        }
    }
    
    [CustomPortBehavior(nameof(output_castEffectShow))]
    IEnumerable< PortData > ListPortBehavior_castEffectShow(List< SerializableEdge > edges)
    {
        for (int i = 0; i < output_castEffectShow.Count; i++)
        {
            yield return new PortData {
                displayName = "Action:" + output_castEffectShow[i].ToString(),
                displayType = typeof(long),
                identifier = output_castEffectShow[i].ToString(), // Must be unique
            };
        }
    }
}