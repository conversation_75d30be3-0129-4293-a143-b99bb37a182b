using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GraphProcessor;
using System.Linq;
using Sirenix.OdinInspector;

[System.Serializable, NodeMenuItem("Skill/SkillDefault")]
public class SkillDefaultNode : BaseNode
{
    [Input(name = "id"), ShowAsDrawer] public long input_id;
    
    public override string name => "SkillDefault";
    public string nodeDesc;
    
    
    [Output(name = "actions", allowMultiple = true),ShowPortIcon(IconNameMatchedInUSSFile = "Array")] public List<long> output_actions = new List<long>();
    // public override string name => "SkillAction";
    

    protected override void Process()
    {
        //output = input * 42;
    }
    
    [CustomPortBehavior(nameof(output_actions))]
    IEnumerable< PortData > ListPortBehavior(List< SerializableEdge > edges)
    {
        for (int i = 0; i < output_actions.Count; i++)
        {
            string actionIdentifier = output_actions[i].ToString() ;
            yield return new PortData {
                displayName = actionIdentifier,
                displayType = typeof(long),
                identifier = actionIdentifier, // Must be unique
            };
        }
    }

}