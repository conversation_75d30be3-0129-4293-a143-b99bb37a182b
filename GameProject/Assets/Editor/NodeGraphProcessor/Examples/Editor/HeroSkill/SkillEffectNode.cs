using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GraphProcessor;
using System.Linq;
using Sirenix.OdinInspector;

[System.Serializable, NodeMenuItem("Skill/SkillEffect")]
public class SkillEffectNode : BaseNode
{
    [Input(name = "id"), ShowAsDrawer] public long input_id;
    [Input(name = "effectType"), ShowAsDrawer] public int input_effectType;
    [Input(name = "effectData")] public List<int> input_effectData = new List<int>();
    [Input(name = "formula"), ShowAsDrawer] public int input_formula;
    [Input(name = "canCrit"), ShowAsDrawer]  public bool input_canCrit;
    [Input(name = "canDodge"), ShowAsDrawer] public bool input_canDodge;
    
    public override string name => "SkillEffect";
    
    public string nodeDesc;

    protected override void Process()
    {
        //output = input * 42;
    }
}