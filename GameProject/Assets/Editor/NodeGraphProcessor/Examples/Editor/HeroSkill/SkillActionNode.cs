using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GraphProcessor;
using System.Linq;
using Sirenix.OdinInspector;

[System.Serializable, NodeMenuItem("Skill/SkillAction")]
public class SkillActionNode : BaseNode
{
    [Input(name = "id"), ShowAsDrawer] public long input_id;
    [Input(name = "type"), ShowAsDrawer] public int input_type;
    [Input(name = "actionParam"), ShowAsDrawer] public string input_actionParam;
    [Input(name = "isOverrideCenter"), ShowAsDrawer] public bool input_isOverrideCenter;
    [Input(name = "targetCenter"), ShowAsDrawer]  public int input_targetCenter;
    [Input(name = "targetCenterCond"), ShowAsDrawer] public int input_targetCenterCond;
    [Input(name = "targetCenterCondParm"), ShowAsDrawer] public int input_targetCenterCondParm;
    [Input(name = "targetRange"), ShowAsDrawer] public int input_targetRange;
    [Input(name = "targetRangeParam"), ShowAsDrawer] public int input_targetRangeParam;
    [Input(name = "targetType"), ShowAsDrawer] public int input_targetType;
    [Input(name = "targetCond"), ShowAsDrawer] public int input_targetCond;
    [Input(name = "targetCondParm"), ShowAsDrawer] public int input_targetCondParm;
    [Input(name = "targetNum"), ShowAsDrawer] public int input_targetNum;
    [Input(name = "bulletId"), ShowAsDrawer] public int input_bulletId;
    [Input(name = "bulletSpeed"), ShowAsDrawer] public float input_bulletSpeed;
    [Input(name = "actionDelay"), ShowAsDrawer] public int input_actionDelay;
    [Input(name = "moveFollow"), ShowAsDrawer] public bool input_moveFollow;

    public string typeName;
    
    
    [Output(name = "actions", allowMultiple = true),ShowPortIcon(IconNameMatchedInUSSFile = "Array")] public List<long> output_actions = new List<long>();
    public override string name => "SkillAction";
    
    public string nodeDesc;

    protected override void Process()
    {
        //output = input * 42;
    }
    
    [CustomPortBehavior(nameof(output_actions))]
    IEnumerable< PortData > ListPortBehavior(List< SerializableEdge > edges)
    {
        for (int i = 0; i < output_actions.Count; i++)
        {
            string actionIdentifier = input_type.ToString() + "_" + output_actions[i].ToString() ;
            yield return new PortData {
                displayName = typeName +":"+ actionIdentifier,
                displayType = typeof(long),
                identifier = actionIdentifier, // Must be unique
            };
        }
    }
}