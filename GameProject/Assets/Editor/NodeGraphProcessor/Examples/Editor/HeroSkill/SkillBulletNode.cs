using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GraphProcessor;
using System.Linq;
using Sirenix.OdinInspector;

[System.Serializable, NodeMenuItem("Skill/SkillBullet")]
public class SkillBulletNode : BaseNode
{
    [Input(name = "id"), ShowAsDrawer] public long input_id;
    [Input(name = "line"), ShowAsDrawer] public int input_line;
    [Input(name = "startPoint"), ShowAsDrawer] public string input_startPoint;
    [Input(name = "endPoint"), ShowAsDrawer] public string input_endPoint;
    [Input(name = "bulletEffect"), ShowAsDrawer]  public int input_bulletEffect;
    [Input(name = "hitEffect"), ShowAsDrawer] public int input_HitEffect;
    
    public override string name => "SkillBullet";
    
    public string nodeDesc;

    protected override void Process()
    {
        //output = input * 42;
    }
}