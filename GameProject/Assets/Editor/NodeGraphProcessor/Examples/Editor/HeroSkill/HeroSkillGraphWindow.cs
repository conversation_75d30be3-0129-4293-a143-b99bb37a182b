using System;
using System.Collections;
using System.Collections.Generic;
using Examples.Editor._05_All;
using UnityEngine;
using UnityEditor;
using GraphProcessor;
using UnityEditor.Experimental.GraphView;
using UnityEngine.Profiling;
using UnityEngine.UIElements;

public class HeroSkillGraphWindow : SkillGraphWindow
{
    // 英雄ID列表
    private List<int> m_HeroIdxList = new List<int>() {};
    
    // 英雄名称列表
    private List<string> m_HeroNameList = new List<string>();
    
    // 英雄ID列表的Foldout组件
    private Foldout m_HeroIdFoldout;

    public Action<int> m_heroSelectedCalllback;
    
    protected override void InitializeWindow(BaseGraph graph)
    {
        graphView = new UniversalGraphView(this);

        m_MiniMap = new MiniMap() {anchored = true};
        graphView.Add(m_MiniMap);

        m_ToolbarView = new SkillToolbarView(this, graphView, m_MiniMap, graph);
        graphView.Add(m_ToolbarView);
        
        
        var grid = new GridBackground();
        graphView.Insert(0, grid);
        grid.StretchToParentSize();
        
        var styleSheet = EditorGUIUtility.Load("SkillNodeEditorStyle.uss") as StyleSheet;
        graphView.styleSheets.Add(styleSheet);
        
        // 创建英雄ID列表的Foldout
        CreateHeroIdListFoldout();
    }
    
    /// <summary>
    /// 创建英雄ID列表的Foldout
    /// </summary>
    private void CreateHeroIdListFoldout()
    {
        // 创建Foldout组件
        m_HeroIdFoldout = new Foldout();
        m_HeroIdFoldout.text = "英雄ID列表";
        m_HeroIdFoldout.value = true; // 默认展开
        
        // 设置Foldout的样式
        m_HeroIdFoldout.style.position = Position.Absolute;
        m_HeroIdFoldout.style.top = 50;
        m_HeroIdFoldout.style.left = 10;
        m_HeroIdFoldout.style.width = 150;
        m_HeroIdFoldout.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        m_HeroIdFoldout.style.paddingTop = 5;
        m_HeroIdFoldout.style.paddingBottom = 5;
        m_HeroIdFoldout.style.paddingLeft = 5;
        m_HeroIdFoldout.style.paddingRight = 5;
        m_HeroIdFoldout.style.borderTopWidth = 1;
        m_HeroIdFoldout.style.borderBottomWidth = 1;
        m_HeroIdFoldout.style.borderLeftWidth = 1;
        m_HeroIdFoldout.style.borderRightWidth = 1;
        m_HeroIdFoldout.style.borderTopLeftRadius = 3;
        m_HeroIdFoldout.style.borderTopRightRadius = 3;
        m_HeroIdFoldout.style.borderBottomLeftRadius = 3;
        m_HeroIdFoldout.style.borderBottomRightRadius = 3;
        
        // 添加英雄ID列表项
        RefreshHeroListItems();
        
        // 将Foldout添加到graphView
        graphView.Add(m_HeroIdFoldout);
    }
    
    /// <summary>
    /// 英雄ID点击回调
    /// </summary>
    /// <param name="heroId">被点击的英雄ID</param>
    private void OnHeroIdClicked(int heroIdx)
    {
        Debug.Log($"英雄ID {heroIdx} 被点击了");
        
        // 这里可以添加更多的处理逻辑，比如：
        // 1. 加载英雄相关的技能数据
        // 2. 更新图表显示
        // 3. 触发其他UI更新等

       m_heroSelectedCalllback?.Invoke(heroIdx); 
    }
    
    /// <summary>
    /// 刷新英雄列表
    /// </summary>
    /// <param name="heroNames">英雄名称列表</param>
    /// <param name="heroIds">英雄Index列表</param>
    public void RefreshHeroIdList(List<string> heroNames, List<int> heroIdxs)
    {
        // 更新数据
        m_HeroNameList = heroNames;
        m_HeroIdxList = heroIdxs;
        
        // 如果Foldout已经创建，则刷新列表项
        if (m_HeroIdFoldout != null)
        {
            RefreshHeroListItems();
        }
    }
    
    /// <summary>
    /// 刷新英雄列表项
    /// </summary>
    private void RefreshHeroListItems()
    {
        // 清空当前列表项
        m_HeroIdFoldout.Clear();
        
        // 添加新的列表项
        for (int i = 0; i < m_HeroIdxList.Count; i++)
        {
            int heroIdx = m_HeroIdxList[i];
            string heroName = i < m_HeroNameList.Count ? m_HeroNameList[i] : string.Empty;
            
            var heroItem = new Button(() => OnHeroIdClicked(heroIdx));
            
            // 如果有英雄名称，则显示名称和ID，否则只显示ID
            if (!string.IsNullOrEmpty(heroName))
            {
                heroItem.text = $"{heroName} ({heroIdx})";
            }
            else
            {
                heroItem.text = $"英雄IDX: {heroIdx}";
            }
            
            heroItem.style.marginTop = 2;
            heroItem.style.marginBottom = 2;
            
            m_HeroIdFoldout.Add(heroItem);
        }
    }

    public void setHeroSelectedCalllback(Action<int> heroSelectedCallback)
    {
        m_heroSelectedCalllback = heroSelectedCallback;
    }
    
}