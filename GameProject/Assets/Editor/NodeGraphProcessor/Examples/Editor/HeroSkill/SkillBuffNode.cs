using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GraphProcessor;
using System.Linq;
using Sirenix.OdinInspector;

[System.Serializable, NodeMenuItem("Skill/SkillBuff")]
public class SkillBuffNode: BaseNode
{
    [Input(name = "id"), ShowAsDrawer] public long input_id;
    [Input(name = "lv"), ShowAsDrawer] public int input_lv;
    [Input(name = "buffTag")] public List<int> input_buffTag = new List<int>();
    [Input(name = "buffGroup"), ShowAsDrawer] public int input_buffGroup;
    [Input(name = "priority"), ShowAsDrawer]  public int input_priority;
    [Input(name = "isBuff"), ShowAsDrawer] public int input_isBuff;
    [Input(name = "buffEffectShow"), ShowAsDrawer] public int input_buffEffectShow;
    [Input(name = "buffName"), ShowAsDrawer] public string input_buffName;
    [Input(name = "showTip"), ShowAsDrawer] public int input_showTip;
    [Input(name = "isDispersible"), ShowAsDrawer] public bool input_isDispersible;

    public int stackCount;
    
    public override string name => "SkillBuff";
    
    public string nodeDesc;

    protected override void Process()
    {
        //output = input * 42;
    }
}