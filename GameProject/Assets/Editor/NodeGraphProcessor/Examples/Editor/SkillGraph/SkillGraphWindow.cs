using System;
using System.Collections;
using System.Collections.Generic;
using Examples.Editor._05_All;
using UnityEngine;
using UnityEditor;
using GraphProcessor;
using UnityEditor.Experimental.GraphView;
using UnityEngine.Profiling;
using UnityEngine.UIElements;

public class SkillGraphWindow : UniversalGraphWindow
{
    // 英雄ID列表
    private List<int> m_HeroIdList = new List<int>() { 1001, 1002, 1003, 1004, 1005 };
    
    // 英雄名称列表
    private List<string> m_HeroNameList = new List<string>();
    
    // 英雄ID列表的Foldout组件
    private Foldout m_HeroIdFoldout;
    
    protected override void InitializeWindow(BaseGraph graph)
    {
        graphView = new UniversalGraphView(this);

        m_MiniMap = new MiniMap() {anchored = true};
        graphView.Add(m_MiniMap);

        m_ToolbarView = new SkillToolbarView(this, graphView, m_MiniMap, graph);
        graphView.Add(m_ToolbarView);
        
        
        var grid = new GridBackground();
        graphView.Insert(0, grid);
        grid.StretchToParentSize();
        
        var styleSheet = EditorGUIUtility.Load("SkillNodeEditorStyle.uss") as StyleSheet;
        graphView.styleSheets.Add(styleSheet);
        
        // 创建英雄ID列表的Foldout
        CreateHeroIdListFoldout();
    }

    public bool ShowFlowPoint;

    private float m_FlowPointGap = 60f;

    private float m_FlowPointMoveSpeed = 0.003f;

    public void ShowOrHideEdgeFlowPoint()
    {
        ShowFlowPoint = !ShowFlowPoint;
    }

    protected override void Update()
    {
        base.Update();
        if (graphView == null)
        {
           return; 
        }
        if (ShowFlowPoint)
        {
            ShowEdgeFlowPoint();
        }
        else
        {
            HideEdgeFlowPoint();
        }
    }

    /// <summary>
    /// 展示FlowPoint，自己计算位置信息
    ///
    /// ---------------------------------------------------------------------------------------
    /// 
    /// 也可以继承Manipulator实现一个控制器，对EdgeView添加AddManipulator，Manipulator即可自动同步位置
    /// 只需要设置Manipulator的left和top间距即可，可参见https://github.com/HalfLobsterMan/3.0_GraphProcessor/blob/56c2928a1790994df4a1f8a9c2a30477bbe6e21d/Editor/Views/BaseEdgeView.cs
    /// </summary>
    private void ShowEdgeFlowPoint()
    {
        foreach (var edgeView in graphView.edgeViews)
        {
            float edgeLength = 0;
            for (int i = 0; i < edgeView.GetPointsAndTangents.Length - 1; i++)
            {
                edgeLength += Vector2.Distance(edgeView.GetPointsAndTangents[i],
                    edgeView.GetPointsAndTangents[i + 1]);
            }

            float eachChunkContainsPercentage = m_FlowPointGap / edgeLength;
            int flowPointCount = (int) (1 / eachChunkContainsPercentage);

            if (flowPointCount % 2 == 0)
            {
                flowPointCount++;
            }

            if (edgeView.EdgeFlowPointVisualElements != null && edgeView.EdgeFlowPointVisualElements.Count > 0 &&
                //如果长度发生变化就需要重新计算
                edgeView.EdgeFlowPointVisualElements.Count == flowPointCount)
            {
                for (int i = 0; i < flowPointCount; i++)
                {
                    edgeView.FlowPointProgress[i] += Time.deltaTime * m_FlowPointMoveSpeed;

                    edgeView.EdgeFlowPointVisualElements[i].transform.position =
                        EdgeFlowPointCaculator.GetFlowPointPosByPercentage(
                            Mathf.Repeat(edgeView.FlowPointProgress[i], 1),
                            edgeView.GetPointsAndTangents, edgeLength) -
                        new Vector2(8 * i, 0);
                }
            }
            else
            {
                if (edgeView.EdgeFlowPointVisualElements != null)
                {
                    foreach (var oldFlowPoint in edgeView.EdgeFlowPointVisualElements)
                    {
                        edgeView.Remove(oldFlowPoint);
                    }
                }

                edgeView.EdgeFlowPointVisualElements = new List<VisualElement>();
                edgeView.FlowPointProgress.Clear();

                for (int i = 0; i < flowPointCount; i++)
                {
                    float initalPercentage = eachChunkContainsPercentage * i;

                    VisualElement visualElement = new VisualElement()
                    {
                        name = "EdgeFlowPoint", transform =
                        {
                            position = EdgeFlowPointCaculator.GetFlowPointPosByPercentage(
                                           initalPercentage, edgeView.GetPointsAndTangents, edgeLength) -
                                       new Vector2(8 * i, 0),
                        }
                    };
                    
                    //可以自定义流点颜色，但注意将其alpha通道设置为1
                    //visualElement.style.unityBackgroundImageTintColor = edgeView.serializedEdge.outputNode.color;
                    edgeView.FlowPointProgress.Add(initalPercentage);
                    edgeView.EdgeFlowPointVisualElements.Add(visualElement);
                    edgeView.Add(visualElement);
                }
            }
        }
    }

    private void HideEdgeFlowPoint()
    {
        foreach (var edgeView in graphView.edgeViews)
        {
            if (edgeView.EdgeFlowPointVisualElements == null) return;
            foreach (var edgeFlowPoint in edgeView.EdgeFlowPointVisualElements)
            {
                edgeView.Remove(edgeFlowPoint);
            }

            edgeView.EdgeFlowPointVisualElements.Clear();
        }
    }
    
    /// <summary>
    /// 创建英雄ID列表的Foldout
    /// </summary>
    private void CreateHeroIdListFoldout()
    {
        // 创建Foldout组件
        m_HeroIdFoldout = new Foldout();
        m_HeroIdFoldout.text = "英雄ID列表";
        m_HeroIdFoldout.value = true; // 默认展开
        
        // 设置Foldout的样式
        m_HeroIdFoldout.style.position = Position.Absolute;
        m_HeroIdFoldout.style.top = 50;
        m_HeroIdFoldout.style.right = 10;
        m_HeroIdFoldout.style.width = 150;
        m_HeroIdFoldout.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        m_HeroIdFoldout.style.paddingTop = 5;
        m_HeroIdFoldout.style.paddingBottom = 5;
        m_HeroIdFoldout.style.paddingLeft = 5;
        m_HeroIdFoldout.style.paddingRight = 5;
        m_HeroIdFoldout.style.borderTopWidth = 1;
        m_HeroIdFoldout.style.borderBottomWidth = 1;
        m_HeroIdFoldout.style.borderLeftWidth = 1;
        m_HeroIdFoldout.style.borderRightWidth = 1;
        m_HeroIdFoldout.style.borderTopLeftRadius = 3;
        m_HeroIdFoldout.style.borderTopRightRadius = 3;
        m_HeroIdFoldout.style.borderBottomLeftRadius = 3;
        m_HeroIdFoldout.style.borderBottomRightRadius = 3;
        
        // 添加英雄ID列表项
        RefreshHeroListItems();
        
        // 将Foldout添加到graphView
        graphView.Add(m_HeroIdFoldout);
    }
    
    /// <summary>
    /// 英雄ID点击回调
    /// </summary>
    /// <param name="heroId">被点击的英雄ID</param>
    private void OnHeroIdClicked(int heroId)
    {
        Debug.Log($"英雄ID {heroId} 被点击了");
        
        // 这里可以添加更多的处理逻辑，比如：
        // 1. 加载英雄相关的技能数据
        // 2. 更新图表显示
        // 3. 触发其他UI更新等
    }
    
    /// <summary>
    /// 刷新英雄列表
    /// </summary>
    /// <param name="heroNames">英雄名称列表</param>
    /// <param name="heroIds">英雄ID列表</param>
    public void RefreshHeroIdList(List<string> heroNames, List<int> heroIds)
    {
        // 更新数据
        m_HeroNameList = heroNames;
        m_HeroIdList = heroIds;
        
        // 如果Foldout已经创建，则刷新列表项
        if (m_HeroIdFoldout != null)
        {
            RefreshHeroListItems();
        }
    }
    
    /// <summary>
    /// 刷新英雄列表项
    /// </summary>
    private void RefreshHeroListItems()
    {
        // 清空当前列表项
        m_HeroIdFoldout.Clear();
        
        // 添加新的列表项
        for (int i = 0; i < m_HeroIdList.Count; i++)
        {
            int heroId = m_HeroIdList[i];
            string heroName = i < m_HeroNameList.Count ? m_HeroNameList[i] : string.Empty;
            
            var heroItem = new Button(() => OnHeroIdClicked(heroId));
            
            // 如果有英雄名称，则显示名称和ID，否则只显示ID
            if (!string.IsNullOrEmpty(heroName))
            {
                heroItem.text = $"{heroName} ({heroId})";
            }
            else
            {
                heroItem.text = $"英雄ID: {heroId}";
            }
            
            heroItem.style.marginTop = 2;
            heroItem.style.marginBottom = 2;
            
            m_HeroIdFoldout.Add(heroItem);
        }
    }
}