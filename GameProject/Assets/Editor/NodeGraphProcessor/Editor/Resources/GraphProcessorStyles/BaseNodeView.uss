#node {
    background-color: rgba(0, 63, 63, 0.8);
}

#controls {
    background-color: rgba(63, 63, 63, 0.8);
}

#settings-button {
    border-width: 0px;
    width: 28px;
    height: 28px;
    background-color: rgba(63, 63, 63, 0);
    border-radius: 0px;
}

#settings-button > #icon {
    -unity-background-scale-mode: scale-to-fit;
    background-image: resource("Icons/SettingsIcons");
    flex-grow: 1;
}

#settings-button:hover {
    background-color: rgba(127, 127, 127 0.2);
}

#contents #top .executed #connector, #contents #top .executed #connector #cap,
#contents #top .executes #connector, #contents #top .executes #connector #cap,
#contents #top .executeAfter #connector, #contents #top .executeAfter #connector #cap,
#contents #top .true #connector, #contents #top .true #connector #cap,
#contents #top .false #connector, #contents #top .false #connector #cap,
#contents #top .loopBody #connector, #contents #top .loopBody #connector #cap,
#contents #top .loopCompleted #connector, #contents #top .loopCompleted #connector #cap {
    border-radius: 0px;
}

.Highlight {
    background-color: rgba(0, 63, 63, 0.8);
}

ParameterNodeView #title {
    height: 16px;
}

ParameterNodeView #title.input {
    flex-direction: row-reverse;
}

ParameterNodeView #node-border {
    border-radius: 10px;
}

ParameterNodeView #selection-border {
    border-radius: 12px;
}

ParameterNodeView #title > Label {
    margin-left: 2px;
    font-size: 11px;
}

ParameterNodeView #title #top PortView {
    padding-right: 0;
    padding-left: 0;
    margin-top: 2px;
}

ParameterNodeView PortView > Label {
    visibility: hidden;
    width: 0;
}

ParameterNodeView #controls EnumField {
    margin: 0;
}

ParameterNodeView #controls EnumField > VisualElement {
    border-width: 0;
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 4px;
    padding-right: 4px;
    height: 14px;
}

ParameterNodeView #controls EnumField > VisualElement > TextElement {
    font-size: 10px;
    margin-top: 0;
}

ParameterNodeView #controls EnumField > VisualElement > VisualElement {
    margin-right: -2px;
}

#RightTitleContainer {
    justify-content: flex-end;
    flex-grow: 1;
    flex-direction: row;
}

#input-container {
    position: absolute;
    right: 100%;
    top: 45px;
    align-items: flex-end;
    --layer: -50;
}

#input-container > .port-input-element > IntegerField,
#input-container > .port-input-element > FloatField {
    min-width: 30px;
    max-width: 100px;
}

#input-container > .port-input-element > ColorField,
#input-container > .port-input-element > ObjectField,
#input-container > .port-input-element > CurveField {
    margin-top: 0;
    margin-bottom: 0;
}

#input-container > .port-input-element > TextField {
    min-width: 50px;
    max-width: 150px;
}

#input-container > .port-input-element > CurveField {
    width: 100px;
}

#input-container > .port-input-element > Vector4Field Label,
#input-container > .port-input-element > Vector3Field Label,
#input-container > .port-input-element > Vector2Field Label,
#input-container > .port-input-element > Vector3IntField Label,
#input-container > .port-input-element > Vector2IntField Label {
    font-size: 8px;
    min-width: 8px;
    flex-basis: 8px;
    padding-top: 2px;
    margin-right: 1px;
}

#input-container > .port-input-element > Vector4Field FloatInput,
#input-container > .port-input-element > Vector3Field FloatInput,
#input-container > .port-input-element > Vector2Field FloatInput,
#input-container > .port-input-element > Vector3IntField IntegerInput,
#input-container > .port-input-element > Vector2IntField IntegerInput {
    min-width: 28px;
}

#input-container > .port-input-element > * .unity-composite-field__field-spacer {
    flex-grow: 0.01;
}

#input-container ObjectFieldSelector {
    width: 17px;
    height: 15px;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
}

#input-container ObjectFieldDisplay > Label {
    margin-top: 2px;
}

#input-container ObjectFieldDisplay > Image {
    margin: 0;
}

#input-container > .port-input-element > ColorField {
    width: 60px;
}

#input-container > .port-input-element > * {
    margin-left: 2px;
}

#input-container > .port-input-element * {
    font-size: 8px;
}

#input-container > .port-input-element {
    max-width: 180px;
    background-color: rgba(72, 72, 72, 0.6);
    margin-top: 3px;
    margin-bottom: 2px;
    padding-top: 1px;
    padding-bottom: 1px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    height: 19px;
    overflow: hidden;
    padding-right: 1px;
}

#input-container > .port-input-element.empty {
    width: 0;
    padding: 0;
}

.collapsed #input-container {
    visibility: hidden;
}

#input-container {
    opacity: 0.6;
}

.node:checked > #input-container {
    opacity: 1;
}

#input-container > .port-input-element IntegerInput,
#input-container > .port-input-element FloatInput,
#input-container > .port-input-element TextInput {
    border-top-left-radius: 1px;
    border-bottom-left-radius: 1px;
    border-top-right-radius: 1px;
    border-bottom-right-radius: 1px;
    padding-bottom: 1px;
    height: 15px;
}

#TopPortContainer, #BottomPortContainer
{
   align-self: center; 
   flex-direction: row;
}

#TopPortContainer
{
    margin-bottom: -1px;
}

#BottomPortContainer
{
    margin-top: -1px;
}

#title-button-container
{
    align-items: center;
    align-content: center;
}

#collapse-button:
{
    width: 16px;
    height: 16px;
    margin: 4px;
}

#NodeIcon_Action {
    align-self: center;
    margin-left: 8px;
    
    width: 12px;
    height: 12px;
    background-image: resource("Icons/Function@x3");
}

.NodeTexturePreview{
    width: 200px;
    height: 200px;
}