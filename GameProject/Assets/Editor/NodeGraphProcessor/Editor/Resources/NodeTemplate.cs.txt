using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GraphProcessor;
using System.Linq;

[System.Serializable, NodeMenuItem("Custom/#NAME#")]
public class #SCRIPTNAME# : BaseNode
{
	[Input(name = "In")]
    public float                input;

	[Output(name = "Out")]
	public float				output;

	public override string		name => "#NAME#";

	protected override void Process()
	{
	    output = input * 42;
	}
}
