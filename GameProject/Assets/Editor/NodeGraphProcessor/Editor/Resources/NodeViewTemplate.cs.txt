using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEditor.UIElements;
using UnityEditor.Experimental.GraphView;
using UnityEngine.UIElements;
using GraphProcessor;

[NodeCustomEditor(typeof(NODE_TYPE))]
public class #SCRIPTNAME# : BaseNodeView
{
	public override void Enable()
	{
		var node = nodeTarget as NODE_TYPE;

        // Create your fields using node's variables and add them to the controlsContainer

		controlsContainer.Add(new Label("Hello World !"));
	}
}