//------------------------------------------------------------
// Author: 烟雨迷离半世殇
// Mail: <EMAIL>
// Data: 2021年6月9日 14:08:27
//------------------------------------------------------------

using System;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace GraphProcessor
{
    public static class GraphCreateAndSaveHelper
    {
        /// <summary>
        /// NodeGraphProcessor路径前缀
        /// </summary>
        public const string NodeGraphProcessorPathPrefix = "Assets/Editor/NodeGraphProcessor";

        public static BaseGraph CreateGraph(Type graphType)
        {
            BaseGraph baseGraph = ScriptableObject.CreateInstance(graphType) as BaseGraph;
            string panelPath = $"{NodeGraphProcessorPathPrefix}/Examples/Saves/";
            Directory.CreateDirectory(panelPath);
            string panelFileName = "Graph";
            string path = EditorUtility.SaveFilePanelInProject("Save Graph Asset", panelFileName, "asset", "", panelPath);
            if (string.IsNullOrEmpty(path))
            {
                Debug.Log("创建graph已取消");
                return null;
            }
            AssetDatabase.CreateAsset(baseGraph, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            return baseGraph;
        }
        
        public static void SaveGraphToDisk(BaseGraph baseGraphToSave)
        {
            if (baseGraphToSave.doNotSaveOnEditor)
            {
                Debug.Log("graph not saved, doNotSaveOnEditor = " + baseGraphToSave.doNotSaveOnEditor);
                return;
            }
            EditorUtility.SetDirty(baseGraphToSave);
            AssetDatabase.SaveAssets();
        }
    }
}