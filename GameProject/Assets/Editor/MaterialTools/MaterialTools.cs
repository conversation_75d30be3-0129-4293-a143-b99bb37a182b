using System;
using System.Collections.Generic;
using Sultan.Tools;
using UnityEditor;
using UnityEngine;

namespace Sultan.Tools 
{
    public class MaterialTools : EditorWindow
    {
        public List<Material> collected = new List<Material>();
        
        private SerializedObject serializedObject;
        private SerializedProperty serializedProperty;
        private bool FindFoldout = false;
        private Shader findShader;
        private string findKeyword;
        private bool keywordState;
        Vector2 pos;
        
        
        
        [MenuItem("Tools/TA Tools/MaterialWindow")]
        private static void OpenWindow()
        {
            MaterialTools window = GetWindow<MaterialTools>();
            window.wantsMouseMove = false;
            window.titleContent = new GUIContent("MaterialWindow");
            window.Show();
            window.Focus();
        }
        
        
        
        public void CollectMaterialsPaleteData()
        {
            collected.Clear();
            var materials = AssetDatabase.FindAssets("t:Material");
            foreach (var matGUID in materials)
            {
                var matPath = AssetDatabase.GUIDToAssetPath(matGUID);
                var mat = AssetDatabase.LoadAssetAtPath<Material>(matPath);
                if(mat.shader.name == "Sultan/Scene/BuildUnLit" || mat.shader.name == "Sultan/Scene/BuildPBR")
                {
                    var saturation = mat.GetFloat("_Saturation");
                    var contrast = mat.GetFloat("_Contrast");
                    var brightness = mat.GetFloat("_Brightness");
                    if (!mat.IsKeywordEnabled("PALETTE_COLOR_ON"))
                    {
                        if (!saturation.Equals(1f) || !contrast.Equals(1f) || !brightness.Equals(1f))
                        {
                            collected.Add(mat);
                        }     
                    }
                }
            }
            serializedObject.Update();
        }



        public void FindMaterialVariant(Shader s, string keywords, bool isEnable)
        {
            collected.Clear();
            var materials = AssetDatabase.FindAssets("t:Material");
            var subKeywords = keywords.Split(',');
            foreach (var matGUID in materials)
            {
                var matPath = AssetDatabase.GUIDToAssetPath(matGUID);
                var mat = AssetDatabase.LoadAssetAtPath<Material>(matPath);
                if (mat.shader.name == s.name)
                {
                    // if (mat.IsKeywordEnabled("PALETTE_COLOR_ON") != isEnable)
                    bool check = true;
                    foreach (var k in subKeywords)
                    {
                        collected.Add(mat);
                        if (mat.IsKeywordEnabled(k) != isEnable)
                        {
                            check = false;
                        }
                    }

                    if (check)
                        collected.Add(mat);

                }
            }
        }

        public void OnEnable()
        {
            serializedObject = new SerializedObject(this);
            serializedProperty = serializedObject.FindProperty("collected");
        }

        public void OnGUI()
        {
            DrawMainGUI();
        }

        public void DrawMainGUI()
        {
            GUILayout.BeginVertical();
            if (GUILayout.Button("检测PALETTE_COLOR_ON"))
            {
                CollectMaterialsPaleteData();
            }
            
            if(GUILayout.Button("Fix PALETTE_COLOR_ON"))
            {
                foreach (var mat in collected)
                {
                    mat.SetFloat("_Saturation", 1f);
                    mat.SetFloat("_Contrast", 1f);
                    mat.SetFloat("_Brightness", 1f);
                }
            }
            
            EditorGUILayout.Space(2);
            FindFoldout = EditorGUILayout.Foldout(FindFoldout, "查找材质变体是否开启");
            if (FindFoldout)
            {
                findShader = (Shader)EditorGUILayout.ObjectField( findShader, typeof(Shader), false);
                findKeyword = EditorGUILayout.TextField("关键字", findKeyword);
                keywordState = EditorGUILayout.Toggle("是否开启", keywordState);
                var btnName = keywordState ? "查找开启关键字的材质" : "查找未开启关键字的材质";
                if (GUILayout.Button(btnName))
                {
                    FindMaterialVariant(findShader, findKeyword, keywordState);
                }
            }
            
            
                
            pos = EditorGUILayout.BeginScrollView(pos);
            EditorGUILayout.PropertyField(serializedProperty, true);
            // 如果需要更新序列化, 调用
            // serializedObject.Update();
            EditorGUILayout.EndScrollView();
            GUILayout.EndVertical();
        }
    }
}