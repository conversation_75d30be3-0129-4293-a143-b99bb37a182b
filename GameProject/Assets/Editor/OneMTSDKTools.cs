using System.Collections;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using UnityEditor;
using UnityEngine;

/*
 * 关于OneMTSDK的一些工具脚本, 一期使用Editor实现, 二期使用EditorWindow或者UGUI实现
 *  功能清单:
 *    1. 更新SDK版本
 */

namespace OneMT.SDK.Editor.Tools
{
    public class OneMTSDKTools : EditorWindow
    {
        #region [菜单栏]

        [MenuItem("Tools/SDK/更新版本")]
        public static void UpdateSDKVersion()
        {
            p_updateVersion();
        }

        #endregion

        #region [Private Method]

        static void p_updateVersion()
        {
            string manifestPath = Path.Combine(Application.dataPath, "../Packages/manifest.json");
            if (!File.Exists(manifestPath))
            {
                UnityEngine.Debug.LogError("Manifest file not found!");
                return;
            }

            // 将JSON字符串反序列化为Hashtable对象
            string manifestJson = File.ReadAllText(manifestPath);
            Hashtable hashtable = JsonConvert.DeserializeObject<Hashtable>(manifestJson);
            var dependencies =
                JsonConvert.DeserializeObject<Hashtable>(JsonConvert.SerializeObject(hashtable["dependencies"]));

            foreach (DictionaryEntry entry in dependencies)
            {
                string key = entry.Key.ToString();
                string value = entry.Value.ToString();
                if (key.Contains("com.onemt.unity") && value.Contains("#") && value.Contains("git"))
                {
                    //读取版本号
                    string pattern = @"#(\d+\.\d+\.\d+)";
                    Match match = Regex.Match(value, pattern);
                    if (!match.Success)
                    {
                        UnityEngine.Debug.LogError("无法匹配版本号");
                        continue;
                    }

                    string version = match.Groups[1].Value; //接入版本号
                    var parts = value.Split('#');
                    string configVersion = parts[1]; //配置的版本号
                    var url = parts[0];
                    var tags = GetGitTags(parts[0]);

                    if (tags.Length == 0)
                    {
                        UnityEngine.Debug.LogFormat("无法获取版本号, 请检查git地址是否正确: {0}", url);
                        continue;
                    }

                    var filtered = tags.Where(str =>
                            System.Text.RegularExpressions.Regex.IsMatch(str, @"^[0-9.]+$") && str.StartsWith(version))
                        .ToList();
                    var sorted = filtered.OrderByDescending(str => int.Parse(str.Split('.')[3])).ToList();


                    // 返回排序后的第一个元素, 即最大版本号
                    string maxVersion = sorted[0];

                    //判断版本号是否一致
                    if (configVersion == maxVersion)
                    {
                        UnityEngine.Debug.LogFormat("组件: {0} 已经是最新版本", key);
                        continue;
                    }

                    UnityEngine.Debug.LogFormat("更新组件名称:{0}, 接入版本:{1}\n 配置版本号：{2}, 最新版本号: {3}", key, version,
                        configVersion, maxVersion);
                    var neValue = url + "#" + maxVersion;
                    // 使用新的版本号替换旧的版本号
                    manifestJson = manifestJson.Replace(value, neValue);
                }
            }

            File.WriteAllText(manifestPath, manifestJson);
        }

        private static string[] GetGitTags(string url)
        {
            string command = "git ls-remote --tags " + url;
            Process process = new Process();
            process.StartInfo.FileName = "/bin/bash";
            process.StartInfo.Arguments = "-c \"" + command + "\"";
            process.StartInfo.RedirectStandardOutput = true;
            process.StartInfo.UseShellExecute = false;
            process.Start();
            string output = process.StandardOutput.ReadToEnd();
            process.WaitForExit();
            string[] tags = output.Split(new char[] { '\n', '\r' }, System.StringSplitOptions.RemoveEmptyEntries);
            for (int i = 0; i < tags.Length; i++)
            {
                tags[i] = tags[i].Substring(tags[i].LastIndexOf('/') + 1);
            }

            return tags;
        }

        #endregion
    }
}