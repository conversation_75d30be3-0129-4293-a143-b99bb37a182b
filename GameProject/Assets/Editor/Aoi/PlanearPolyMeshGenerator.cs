//Author:Aoicocoon
//Date:20250418
//纹理转平面网格

using System;
using System.Collections;
using System.Collections.Generic;
using Spine.Unity;
using Unity.Mathematics;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

public class PlanearPolyMeshGenerator : Editor
{
    [UnityEditor.MenuItem("Tools/模型导出/图片导出平面网格")]
    public static void BeginGenerate()
    {
        PlanearPolyMeshGeneratorEditorWindow window = (PlanearPolyMeshGeneratorEditorWindow)EditorWindow.GetWindow<PlanearPolyMeshGeneratorEditorWindow>();
        window.minSize = new Vector2(400, 700);
        window.maxSize = window.minSize;
        window.ShowUtility();
    }

    public class PlanearPolyMeshGeneratorEditorWindow : EditorWindow
    {
        private const string SAVE_FOLDER = "ArtTmp/Scenes/PlanearPolyMeshGenerator";
        
        private Texture2D ColorTexture;
        private Texture2D ShadowTexture;
        
        private MeshPreview ColorPreview;
        private MeshPreview ShadowPreview;

        private PreviewRenderUtility ColorEditor;
        private PreviewRenderUtility ShadowEditor;
        private Material PreviewMaterial;

        private Mesh ColorGeneratedMesh;
        private Mesh ShadowGeneratedMesh;
        private GameObject tmpGameObject;
        private GameObject ShadowLightObject;
        private Vector3 ProfileColorScale = new Vector3(4, 4, 4);
        private Vector3 ProfileColorRotation = new Vector3(45,-135,0);
        
        private GameObject TraceColorObject;
        private GameObject TraceShadowObject;

        private bool HasSaved;
        private string OverrideSaveFolder;

        private void OnEnable()
        {
            ColorEditor = new PreviewRenderUtility();
            ColorEditor.camera.fieldOfView = 60f; // 设置相机视野
            ColorEditor.camera.transform.position = new Vector3(0, 0.5f, -1); // 设置相机位置
            ColorEditor.camera.transform.rotation = Quaternion.identity; // 设置相机旋转
            ColorEditor.camera.nearClipPlane = 0.1f;
            ColorEditor.camera.farClipPlane = 100f;
            
            ShadowEditor = new PreviewRenderUtility();
            ShadowEditor.camera.fieldOfView = 60f; // 设置相机视野
            ShadowEditor.camera.transform.position = new Vector3(0, 0.5f, -1); // 设置相机位置
            ShadowEditor.camera.transform.rotation = Quaternion.identity; // 设置相机旋转
            ShadowEditor.camera.nearClipPlane = 0.1f;
            ShadowEditor.camera.farClipPlane = 100f;

            // 默认材质
            PreviewMaterial = new Material(Shader.Find("PlanearPolyMeshGenerator/Wireframe"));
            PreviewMaterial.color = Color.white;

            HasSaved = false;
        }

        private void OnDisable()
        {
            if(null != ColorPreview)
            {
                ColorPreview.Dispose();
                ColorPreview = null;
            }
            
            if(null != ShadowPreview)
            {
                ShadowPreview.Dispose();
                ShadowPreview = null;
            }

            if (!HasSaved)
            {
                if (ColorGeneratedMesh)
                {
                    DestroyImmediate(ColorGeneratedMesh,true);
                }
            
                if (ShadowGeneratedMesh)
                {
                    DestroyImmediate(ShadowGeneratedMesh,true);
                }
            }

            if (null != ColorEditor)
            {
                ColorEditor.Cleanup();
                ColorEditor = null;
            }
        }

        public void OnGUI()
        {
            GUILayout.Label("Color Texture:", EditorStyles.boldLabel);
            GUILayout.Box(ColorTexture, GUILayout.Width(128), GUILayout.Height(128));
            Rect dropArea = new Rect(0,25,128,128);
            GUI.Box(dropArea, "Drop Color Texture Here");
            HandleDragAndDrop(dropArea, ref ColorTexture);
            HandleClick(dropArea, ColorTexture);
            
            ShowMeshPreview(ColorEditor, ref PreviewMaterial,  ColorGeneratedMesh, new Rect(150,25,128,128));
            
            GUILayout.Space(25);
            
            GUILayout.Label("Shadow Texture:", EditorStyles.boldLabel);
            GUILayout.Box(ShadowTexture, GUILayout.Width(128), GUILayout.Height(128));
            dropArea = new Rect(0,25 + 128 + 25 + 25,128,128);
            GUI.Box(dropArea, "Drop Shadow Texture Here");
            HandleDragAndDrop(dropArea, ref ShadowTexture);
            HandleClick(dropArea, ShadowTexture);
            
            ShowMeshPreview(ShadowEditor, ref PreviewMaterial,  ShadowGeneratedMesh, new Rect(150,25 + 128 + 25 + 25,128,128));

            GUILayout.Label(string.Format("Color Vertex:{0} Tri:{1}, Shadow Vertex:{2} Tri:{3}",
                ColorGeneratedMesh? ColorGeneratedMesh.vertexCount:0, ColorGeneratedMesh?ColorGeneratedMesh.triangles.Length:0,
                ShadowGeneratedMesh? ShadowGeneratedMesh.vertexCount:0, ShadowGeneratedMesh?ShadowGeneratedMesh.triangles.Length:0));
            
            GUILayout.Label("拖入\u2193 场景的灯光组件，可自动让阴影网格面朝灯光");
            ShadowLightObject = EditorGUILayout.ObjectField(ShadowLightObject, typeof(GameObject), true) as GameObject;
            ProfileColorScale = EditorGUILayout.Vector3Field("预设Color 缩放", ProfileColorScale);
            ProfileColorRotation = EditorGUILayout.Vector3Field("预设Color 旋转", ProfileColorRotation);
            
            if (GUILayout.Button("生成!"))
            {
                ColorGeneratedMesh = DoGenerate(ColorTexture);
                ShadowGeneratedMesh = DoGenerate(ShadowTexture);

                if (tmpGameObject)
                {
                    DestroyImmediate(tmpGameObject);
                }
                tmpGameObject = new GameObject();
                tmpGameObject.name = "tmp";
                TraceColorObject = BuildGameObject("Color", ColorGeneratedMesh, ColorTexture, tmpGameObject.transform);
                TraceShadowObject = BuildGameObject("Shadow", ShadowGeneratedMesh, ShadowTexture, tmpGameObject.transform);

                if (TraceColorObject)
                {
                    TraceColorObject.transform.localScale = ProfileColorScale;
                    TraceColorObject.transform.rotation = Quaternion.Euler(ProfileColorRotation);
                }
                
                if (TraceShadowObject)
                {
                   
                    TraceShadowObject.transform.localScale = ProfileColorScale;

                    if (ShadowLightObject)
                    {
                        TraceShadowObject.transform.localPosition = new Vector3(-ProfileColorScale.x/2, 0, -ProfileColorScale.z/2);
                        TraceShadowObject.transform.rotation = new Quaternion(
                            -ShadowLightObject.transform.rotation.x,
                            -ShadowLightObject.transform.rotation.y,
                            -ShadowLightObject.transform.rotation.z,
                            -ShadowLightObject.transform.rotation.w
                        );
                    }
                    else
                    {
                        TraceShadowObject.transform.localPosition = Vector3.zero;
                        if (TraceColorObject)
                        {
                            TraceShadowObject.transform.localRotation = TraceColorObject.transform.rotation;
                        }
                    }
                }
            }
            
            GUILayout.Label("确保tmp位置位于0,0,0点，旋转也为0.只修改Color/Shadow节点");
            GUILayout.Label("只会保存编辑好的网格旋转，预制体和材质球要自己建");
            GUILayout.Label("拖入\u2193下面 编辑好的Tmp  GameObject，再点保存");
            tmpGameObject = EditorGUILayout.ObjectField(tmpGameObject, typeof(GameObject), true) as GameObject;
            
            if (GUILayout.Button("保存网格!"))
            {
                HasSaved = true;
                
                SkeletonAnimationBake spineObj = tmpGameObject.GetComponentInChildren<SkeletonAnimationBake>();

                if (spineObj)
                {
                    TraceColorObject = tmpGameObject;
                    Mesh tmpShadowMesh = TraceShadowObject ? TraceShadowObject.GetComponentInChildren<MeshFilter>().sharedMesh : null;
                    Mesh ColorMesh = TraceColorObject ? TraceColorObject.GetComponentInChildren<MeshFilter>().sharedMesh : null;
                    var comebined = Spine_CombineAndSaveMesh(ColorMesh, tmpShadowMesh, TraceColorObject, TraceShadowObject);
                    if(TraceShadowObject)GameObject.DestroyImmediate(TraceShadowObject);
                    SaveToSpine(comebined, tmpGameObject);
                    return;
                }
                else
                {
                    PopResult ret = PopFileSavePanel(SAVE_FOLDER);
                    OverrideSaveFolder = ret.OverrideSaveFolder;
                    string projectPath = ret.projectPath;
                    string lastFolder = ret.lastFolder;
                
                    Debug.Log(projectPath);

                    //允许美术魔改完mesh保存
                    if (tmpGameObject)
                    {
                        var tmpObj = tmpGameObject.transform.Find("Color");
                        MeshFilter tmpRenderer = tmpObj.GetComponent<MeshFilter>();
                        if (tmpRenderer && tmpRenderer.sharedMesh)
                        {
                            ColorGeneratedMesh = tmpRenderer.sharedMesh;
                        }
                    }

                    if (tmpGameObject)
                    {
                        var tmpObj = tmpGameObject.transform.Find("Shadow");
                        MeshFilter tmpRenderer = tmpObj.GetComponent<MeshFilter>();
                        if (tmpRenderer && tmpRenderer.sharedMesh)
                        {
                            ShadowGeneratedMesh = tmpRenderer.sharedMesh;
                        }
                    }
                    CombineAndSaveMesh(ColorGeneratedMesh, ShadowGeneratedMesh, tmpGameObject, lastFolder);
                    
                    // 使用 GUID 获取文件夹对象
                    UnityEngine.Object folderObject = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>("Assets/" + lastFolder);
                    if (folderObject != null)
                    {
                        // 在项目窗口中选中文件夹
                        Selection.activeObject = folderObject;
                
                        // 自动展开文件夹视图
                        EditorGUIUtility.PingObject(folderObject);
                    }
                }
            }

            if (GUILayout.Button("单独保存Color网格"))
            {
                if (ColorGeneratedMesh)
                {
                    var ret = PopFileSavePanel(SAVE_FOLDER);
                    CombineAndSaveMesh(ColorGeneratedMesh, null, tmpGameObject, ret.lastFolder);
                }
            }

            if (GUILayout.Button("单独保存Shadow网格"))
            {
                if (ShadowGeneratedMesh)
                {
                    var ret = PopFileSavePanel(SAVE_FOLDER);
                    CombineAndSaveMesh(ShadowGeneratedMesh, null, tmpGameObject, ret.lastFolder);
                }
            }
            
            GUILayout.Label("SaveFolder:" + OverrideSaveFolder);
            GUILayout.Toggle(HasSaved, "Saved<-不要点，调试用");
        }
    }

    struct PopResult
    {
        public string OverrideSaveFolder;
        public string lastFolder;
        public string projectPath;
    }
    private static PopResult PopFileSavePanel(string SAVE_FOLDER)
    {
        PopResult popResult = new PopResult();
        
        string OverrideSaveFolder = EditorUtility.SaveFilePanel("SaveMesh", SAVE_FOLDER, "CombinedMesh", "asset");

        popResult.OverrideSaveFolder = OverrideSaveFolder;
        
        if (string.IsNullOrEmpty(OverrideSaveFolder))
        {
            return popResult; 
        }

        string lastFolder = string.Empty;
        string projectPath = string.Empty;
        if (OverrideSaveFolder.Contains("ResArtTmp"))
        {
            lastFolder = OverrideSaveFolder.Substring(OverrideSaveFolder.LastIndexOf("ResArtTmp"));
            lastFolder = lastFolder.Replace("ResArtTmp", "ArtTmp");
            projectPath = Application.dataPath + "/" + lastFolder;
        }else if(OverrideSaveFolder.Contains("ResTmp"))
        {
            lastFolder = OverrideSaveFolder.Substring(OverrideSaveFolder.LastIndexOf("ResTmp"));
            lastFolder = lastFolder.Replace("ResTmp", "/Res");
            projectPath = Application.dataPath + "/" + lastFolder;
        }
        else
        {
            projectPath = OverrideSaveFolder;
            lastFolder = OverrideSaveFolder.Replace(Application.dataPath, "");
        }
        
        popResult.lastFolder = lastFolder;
        popResult.projectPath = projectPath;

        return popResult;
    }

    struct OpmizedVertex
    {
        public half posX, posY, posZ, posW;
        public half uvX, uvY;

        public void SetPos(Vector3 pos)
        {
            posX = (half)pos.x;
            posY = (half)pos.y;
            posZ = (half)pos.z;
        }

        public void SetUV(Vector2 uv)
        {
            uvX = (half)uv.x;
            uvY = (half)uv.y;
        }
    }
    
    struct SpineOpmizedVertexUV0
    {
        public half posX, posY, posZ, posW;
        public half uv0X, uv0Y, uv0Z, uv0W;
        
        public void SetPos(Vector3 pos)
        {
            posX = (half)pos.x;
            posY = (half)pos.y;
            posZ = (half)pos.z;
        }
        
        public void SetUV(Vector4 uv0, Vector4 uv1, Vector4 uv2, Vector4 uv3)
        {
            uv0X = (half)uv0.x;
            uv0Y = (half)uv0.y;
            uv0Z = (half)uv0.z;
            uv0W = (half)uv0.w;
        }
    }
    
    struct SpineOpmizedVertexUV1
    {
        public half posX, posY, posZ, posW;
        public half uv0X, uv0Y, uv0Z, uv0W;
        public half uv1X, uv1Y, uv1Z, uv1W;
        
        public void SetPos(Vector3 pos)
        {
            posX = (half)pos.x;
            posY = (half)pos.y;
            posZ = (half)pos.z;
        }
        
        public void SetUV(Vector4 uv0, Vector4 uv1, Vector4 uv2, Vector4 uv3)
        {
            uv0X = (half)uv0.x;
            uv0Y = (half)uv0.y;
            uv0Z = (half)uv0.z;
            uv0W = (half)uv0.w;
            
            uv1X = (half)uv1.x;
            uv1Y = (half)uv1.y;
            uv1Z = (half)uv1.z;
            uv1W = (half)uv1.w;
        }
    }
    
    struct SpineOpmizedVertexUV2
    {
        public half posX, posY, posZ, posW;
        public half uv0X, uv0Y, uv0Z, uv0W;
        public half uv1X, uv1Y, uv1Z, uv1W;
        public half uv2X, uv2Y, uv2Z, uv2W;
        
        public void SetPos(Vector3 pos)
        {
            posX = (half)pos.x;
            posY = (half)pos.y;
            posZ = (half)pos.z;
        }
        
        public void SetUV(Vector4 uv0, Vector4 uv1, Vector4 uv2, Vector4 uv3)
        {
            uv0X = (half)uv0.x;
            uv0Y = (half)uv0.y;
            uv0Z = (half)uv0.z;
            uv0W = (half)uv0.w;
            
            uv1X = (half)uv1.x;
            uv1Y = (half)uv1.y;
            uv1Z = (half)uv1.z;
            uv1W = (half)uv1.w;
            
            uv2X = (half)uv2.x;
            uv2Y = (half)uv2.y;
            uv2Z = (half)uv2.z;
            uv2W = (half)uv2.w;
        }
    }

    class SpineOpmizedVertexProxy
    {
        private int uv_size = -1;
        
        private List<SpineOpmizedVertexUV3> vertexList3 = new List<SpineOpmizedVertexUV3>();
        private List<SpineOpmizedVertexUV2> vertexList2 = new List<SpineOpmizedVertexUV2>();
        private List<SpineOpmizedVertexUV1> vertexList1 = new List<SpineOpmizedVertexUV1>();
        private List<SpineOpmizedVertexUV0> vertexList0 = new List<SpineOpmizedVertexUV0>();
        

        public void Init(int uvSize)
        {
            uv_size = uvSize;
        }

        public void SetPosAndUV(Vector3 pos, Vector4 uv0, Vector4 uv1, Vector4 uv2, Vector4 uv3)
        {
            if (uv_size == 3)
            {
                SpineOpmizedVertexUV3 v = new SpineOpmizedVertexUV3();
                v.SetPos(pos);
                v.SetUV(uv0, uv1, uv2, uv3);
                vertexList3.Add(v);
            }else if (uv_size == 2)
            {
                SpineOpmizedVertexUV2 v = new SpineOpmizedVertexUV2();
                v.SetPos(pos);
                v.SetUV(uv0, uv1, uv2, uv3);
                vertexList2.Add(v);
            }else if (uv_size == 1)
            {
                SpineOpmizedVertexUV1 v = new SpineOpmizedVertexUV1();
                v.SetPos(pos);
                v.SetUV(uv0, uv1, uv2, uv3);
                vertexList1.Add(v);
            }else if (uv_size == 0)
            {
                SpineOpmizedVertexUV0 v = new SpineOpmizedVertexUV0();
                v.SetPos(pos);
                v.SetUV(uv0, uv1, uv2, uv3);
                vertexList0.Add(v);
            }
        }

        public void FillMesh(Mesh mesh, VertexAttributeDescriptor[] layout)
        {
            if (uv_size == 3)
            {
                mesh.SetVertexBufferParams(vertexList3.Count, layout);
                mesh.subMeshCount = 2;
                mesh.SetVertexBufferData(vertexList3, 0 , 0, vertexList3.Count);
            }else if (uv_size == 2)
            {
                mesh.SetVertexBufferParams(vertexList2.Count, layout);
                mesh.subMeshCount = 2;
                mesh.SetVertexBufferData(vertexList2, 0 , 0, vertexList2.Count);
            }else if (uv_size == 1)
            {
                mesh.SetVertexBufferParams(vertexList1.Count, layout);
                mesh.subMeshCount = 2;
                mesh.SetVertexBufferData(vertexList1, 0 , 0, vertexList1.Count);
            }else if (uv_size == 0)
            {
                mesh.SetVertexBufferParams(vertexList0.Count, layout);
                mesh.subMeshCount = 2;
                mesh.SetVertexBufferData(vertexList0, 0 , 0, vertexList0.Count);
            }
        }
    }

    struct SpineOpmizedVertexUV3
    {
        public half posX, posY, posZ, posW;
        public half uv0X, uv0Y, uv0Z, uv0W;
        public half uv1X, uv1Y, uv1Z, uv1W;
        public half uv2X, uv2Y, uv2Z, uv2W;
        public half uv3X, uv3Y, uv3Z, uv3W;
        
        public void SetPos(Vector3 pos)
        {
            posX = (half)pos.x;
            posY = (half)pos.y;
            posZ = (half)pos.z;
        }
        
        public void SetUV(Vector4 uv0, Vector4 uv1, Vector4 uv2, Vector4 uv3)
        {
            uv0X = (half)uv0.x;
            uv0Y = (half)uv0.y;
            uv0Z = (half)uv0.z;
            uv0W = (half)uv0.w;
            
            uv1X = (half)uv1.x;
            uv1Y = (half)uv1.y;
            uv1Z = (half)uv1.z;
            uv1W = (half)uv1.w;
            
            uv2X = (half)uv2.x;
            uv2Y = (half)uv2.y;
            uv2Z = (half)uv2.z;
            uv2W = (half)uv2.w;
            
            uv3X = (half)uv3.x;
            uv3Y = (half)uv3.y;
            uv3Z = (half)uv3.z;
            uv3W = (half)uv3.w;
        }
    }

    private static void SaveToSpine(Mesh mesh, GameObject tmpObj)
    {
        var originPrefab = PrefabUtility.GetOutermostPrefabInstanceRoot(tmpObj);
        var prefabPath = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(originPrefab);
        var meshFilter = tmpObj.GetComponentInChildren<MeshFilter>();
        var tmpMesh = meshFilter.sharedMesh;

        var tmpMeshPath = AssetDatabase.GetAssetPath(tmpMesh);
        var mainAsset = AssetDatabase.LoadMainAssetAtPath(tmpMeshPath);

        if (!mainAsset)
        {
            EditorUtility.DisplayDialog("这不对", "找不到MainAsset", "OK");
            return;
        }
        
        AssetDatabase.RemoveObjectFromAsset(tmpMesh);
        AssetDatabase.AddObjectToAsset(mesh, mainAsset);
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        mesh = AssetDatabase.LoadAssetAtPath<Mesh>(tmpMeshPath);
        meshFilter.sharedMesh = mesh;

        if (PrefabUtility.IsAnyPrefabInstanceRoot(tmpObj) && !string.IsNullOrEmpty(prefabPath))
        {
            PrefabUtility.SaveAsPrefabAssetAndConnect(tmpObj, prefabPath, InteractionMode.AutomatedAction);
        }
        
        UnityEngine.Object folderObject = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(tmpMeshPath);
        PingObject(folderObject);
    }

    private static Mesh Spine_CombineAndSaveMesh(Mesh ColorMesh, Mesh ShadowMesh,  GameObject tmpColorGO, GameObject tmpShadowGO)
    {
        if (!ColorMesh || !ShadowMesh) return null;
        
        Matrix4x4 colorMatrix = Matrix4x4.identity;
        Matrix4x4 shadowMatrix = Matrix4x4.identity;
        
        //spine cant not modify vertex orgin pos
        // if (tmpColorGO)
        // {
        //     colorMatrix = tmpColorGO.transform.localToWorldMatrix;
        // }

        if (tmpShadowGO)
        {
            shadowMatrix = tmpShadowGO.transform.localToWorldMatrix;
        }

        var tmpMesh = ColorMesh;
        
        List<Vector4> tmpUV0 = null;
        List<Vector4> tmpUV1 = null;
        List<Vector4> tmpUV2 = null;
        List<Vector4> tmpUV3 = null;
        
        List<VertexAttributeDescriptor> newLayout = new List<VertexAttributeDescriptor>();
        List<VertexAttributeDescriptor> oldLayout = new List<VertexAttributeDescriptor>();
        tmpMesh.GetVertexAttributes(oldLayout);
        
        int uvSize = -1;
        
        foreach (var attr in oldLayout)
        {
            switch (attr.attribute)
            {
                case VertexAttribute.Position:
                    newLayout.Add(new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float16, 4));
                    break;
                case VertexAttribute.TexCoord0:
                    tmpUV0 = new List<Vector4>();
                    tmpMesh.GetUVs(0, tmpUV0);
                    newLayout.Add(new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float16, 4));
                    uvSize = Mathf.Max(0, uvSize);
                    break;
                case VertexAttribute.TexCoord1:
                    tmpUV1 = new List<Vector4>();
                    tmpMesh.GetUVs(1, tmpUV1);
                    newLayout.Add(new VertexAttributeDescriptor(VertexAttribute.TexCoord1, VertexAttributeFormat.Float16, 4));
                    uvSize = Mathf.Max(1, uvSize);
                    break;
                case VertexAttribute.TexCoord2:
                    tmpUV2 = new List<Vector4>();
                    tmpMesh.GetUVs(2, tmpUV2);
                    newLayout.Add(new VertexAttributeDescriptor(VertexAttribute.TexCoord2, VertexAttributeFormat.Float16, 4));
                    uvSize = Mathf.Max(2, uvSize);
                    break;
                case VertexAttribute.TexCoord3:
                    tmpUV3 = new List<Vector4>();
                    tmpMesh.GetUVs(3, tmpUV3);
                    newLayout.Add(new VertexAttributeDescriptor(VertexAttribute.TexCoord3, VertexAttributeFormat.Float16, 4));
                    uvSize = Mathf.Max(3, uvSize);
                    break;
            }
        }
        
        Vector3 max = new Vector3(float.MinValue, float.MinValue, float.MinValue);
        Vector3 min = new Vector3(float.MaxValue, float.MaxValue, float.MaxValue);
        
        SpineOpmizedVertexProxy proxy = new SpineOpmizedVertexProxy();
        proxy.Init(uvSize);
        
        List<Vector3> combinedVertices = new List<Vector3>();
        Vector3[] tmpVertices = tmpMesh.vertices;
        for (int i = 0; i < tmpMesh.vertices.Length; i++)
        {
            Vector3 v = tmpVertices[i];
            
            proxy.SetPosAndUV(v, 
                null != tmpUV0 ? tmpUV0[i] : Vector4.zero, 
                null != tmpUV1 ? tmpUV1[i] : Vector4.zero, 
                null != tmpUV2 ? tmpUV2[i] : Vector4.zero,
                null != tmpUV3 ? tmpUV3[i] : Vector4.zero
                );
            combinedVertices.Add(v);
            
            max.x = Mathf.Max(max.x, v.x);
            max.y = Mathf.Max(max.y, v.y);
            max.z = Mathf.Max(max.z, v.z);
                
            min.x = Mathf.Min(min.x, v.x);
            min.y = Mathf.Min(min.y, v.y);
            min.z = Mathf.Min(min.z, v.z);
        }
        
        tmpVertices = new Vector3[ShadowMesh.vertexCount];
        for (int i = 0; i < ShadowMesh.vertices.Length; i++)
        {
            Vector3 v = shadowMatrix.MultiplyPoint3x4(ShadowMesh.vertices[i]);
            tmpVertices[i] = v;
                
            max.x = Mathf.Max(max.x, v.x);
            max.y = Mathf.Max(max.y, v.y);
            max.z = Mathf.Max(max.z, v.z);
                
            min.x = Mathf.Min(min.x, v.x);
            min.y = Mathf.Min(min.y, v.y);
            min.z = Mathf.Min(min.z, v.z);
            
            combinedVertices.Add(v);
            
            proxy.SetPosAndUV(v, Vector4.zero, Vector4.zero, Vector4.zero, Vector4.zero);
        }
        
        List<ushort> opmizedColorIndices = new List<ushort>();
        for (int i = 0; i < tmpMesh.triangles.Length; i++)
        {
            opmizedColorIndices.Add((ushort)tmpMesh.triangles[i]);
        }
        
        int verticesOffset = ColorMesh.vertexCount;
        for (int i = 0; i < ShadowMesh.triangles.Length; i++)
        {
            opmizedColorIndices.Add((ushort)(ShadowMesh.triangles[i] + verticesOffset));
        }
        
        var combineMesh = new Mesh();
        combineMesh.name = "Combined Mesh";
        //combineMesh.SetVertices(combinedVertices);
        //combineMesh.subMeshCount = 2;
        //combineMesh.SetTriangles(tmpMesh.triangles, 0);
        //combineMesh.SetTriangles(opmizedColorIndices, 1);
        
        // List<Vector3> vertices = new List<Vector3>();
        // vertices.AddRange(ColorMesh.vertices);
        
        proxy.FillMesh(combineMesh, newLayout.ToArray());
        
        combineMesh.SetIndexBufferParams(opmizedColorIndices.Count, IndexFormat.UInt16);
        combineMesh.SetIndexBufferData(opmizedColorIndices, 0, 0, opmizedColorIndices.Count);
        combineMesh.SetSubMesh(0, new SubMeshDescriptor(0, tmpMesh.triangles.Length) );
        
        if (ShadowMesh)
        {
            combineMesh.SetSubMesh(1, new SubMeshDescriptor(tmpMesh.triangles.Length, ShadowMesh.triangles.Length) );
        }
        
        combineMesh.bounds = new Bounds((max + min) / 2, max - min);
        combineMesh.UploadMeshData(true);
        
        // string fullPath = "Assets/" + saveFolder;
        // AssetDatabase.CreateAsset(combineMesh, fullPath);
        // combineMesh = AssetDatabase.LoadAssetAtPath<Mesh>(fullPath);
        
        return combineMesh;
    }

    private static Mesh CombineAndSaveMesh(Mesh ColorMesh, Mesh ShadowMesh, GameObject tmpObj, string saveFolder)
    {
        Matrix4x4 colorMatrix = Matrix4x4.identity;
        Matrix4x4 shadowMatrix = Matrix4x4.identity;
        
        if (tmpObj)
        {
            tmpObj.transform.position = Vector3.zero;

            Transform colorTrans = tmpObj.transform.Find("Color");
            if (colorTrans)
            {
                colorMatrix = colorTrans.localToWorldMatrix;
            }
            
            Transform shadowTrans = tmpObj.transform.Find("Shadow");
            if (shadowTrans)
            {
                shadowMatrix = shadowTrans.localToWorldMatrix;
            }
        }

        VertexAttributeDescriptor[] layout = new[]
        {
            new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float16, 4),
            new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float16, 2)
        };

        Vector3 max = new Vector3(float.MinValue, float.MinValue, float.MinValue);
        Vector3 min = new Vector3(float.MaxValue, float.MaxValue, float.MaxValue);
        
        var combineMesh = new Mesh();
        combineMesh.name = "Combined Mesh";
        int subMeshCount = 0;
        
        List<Vector3> vertices = new List<Vector3>();
        List<Vector2> uvs = new List<Vector2>();
        if (ColorMesh)
        {
            Vector3[] tmpVertices = new Vector3[ColorMesh.vertexCount];
            for (int i = 0; i < ColorMesh.vertices.Length; i++)
            {
                var v = ColorMesh.vertices[i];
                Vector3 newV = colorMatrix.MultiplyPoint3x4(v);
                tmpVertices[i] = newV;

                max.x = Mathf.Max(max.x, v.x);
                max.y = Mathf.Max(max.y, v.y);
                max.z = Mathf.Max(max.z, v.z);
                
                min.x = Mathf.Min(min.x, v.x);
                min.y = Mathf.Min(min.y, v.y);
                min.z = Mathf.Min(min.z, v.z);
            }
            
            vertices.AddRange(tmpVertices);
            uvs.AddRange(ColorMesh.uv);
            ++subMeshCount;

            tmpVertices = null;
        }

        if (ShadowMesh)
        {
            Vector3[] tmpVertices = new Vector3[ShadowMesh.vertexCount];
            for (int i = 0; i < ShadowMesh.vertices.Length; i++)
            {
                var v = ShadowMesh.vertices[i];
                Vector3 newV = shadowMatrix.MultiplyPoint3x4(v);
                tmpVertices[i] = newV;
                
                max.x = Mathf.Max(max.x, v.x);
                max.y = Mathf.Max(max.y, v.y);
                max.z = Mathf.Max(max.z, v.z);
                
                min.x = Mathf.Min(min.x, v.x);
                min.y = Mathf.Min(min.y, v.y);
                min.z = Mathf.Min(min.z, v.z);
            }
            
            vertices.AddRange(tmpVertices);
            uvs.AddRange(ShadowMesh.uv);
            ++subMeshCount;

            tmpVertices = null;
        }

        combineMesh.SetVertexBufferParams(vertices.Count, layout);
        combineMesh.subMeshCount = subMeshCount;
        
        OpmizedVertex[] opmizedVertices = new OpmizedVertex[vertices.Count];
        for (int i = 0; i < vertices.Count; i++)
        {
            opmizedVertices[i].SetPos(vertices[i]);
            opmizedVertices[i].SetUV(uvs[i]);
        }
        
        combineMesh.SetVertexBufferData(opmizedVertices, 0,  0, opmizedVertices.Length);
        
        
        List<ushort> opmizedColorIndices = new List<ushort>();
        for (int i = 0; i < ColorMesh.triangles.Length; i++)
        {
            opmizedColorIndices.Add((ushort)ColorMesh.triangles[i]);
        }

        if (ShadowMesh)
        {
            int verticesOffset = ColorMesh.vertexCount;
            int[] tmpIndices = new int[ShadowMesh.triangles.Length];
            ShadowMesh.triangles.CopyTo(tmpIndices, 0);
            for (int i = 0; i < tmpIndices.Length; i ++)
            {
                tmpIndices[i] += verticesOffset;
            }
            
            for (int i = 0; i < tmpIndices.Length; i++)
            {
                opmizedColorIndices.Add((ushort)tmpIndices[i]);
            }
        }
        
        combineMesh.SetIndexBufferParams(opmizedColorIndices.Count, IndexFormat.UInt16);
        combineMesh.SetIndexBufferData(opmizedColorIndices, 0, 0, opmizedColorIndices.Count);
        combineMesh.SetSubMesh(0, new SubMeshDescriptor(0, ColorMesh.triangles.Length) );

        if (ShadowMesh)
        {
            combineMesh.SetSubMesh(1, new SubMeshDescriptor(ColorMesh.triangles.Length, ShadowMesh.triangles.Length) );
        }
        
        combineMesh.bounds = new Bounds((max + min) / 2, max - min);
        combineMesh.UploadMeshData(true);

        string fullPath = "Assets/" + saveFolder;
        AssetDatabase.CreateAsset(combineMesh, fullPath);
        combineMesh = AssetDatabase.LoadAssetAtPath<Mesh>(fullPath);

        return combineMesh;
    }

    private static void ShowMeshPreview(PreviewRenderUtility preview, ref Material PreviewMaterial,  Mesh previewMesh, Rect dropArea)
    {
        if (previewMesh == null) return;
        
        // 开始预览渲染
        preview.BeginPreview(dropArea, GUIStyle.none);

        // 设置网格和材质
        preview.DrawMesh(
            previewMesh,
            Matrix4x4.identity, 
            PreviewMaterial,
            0
        );

        preview.Render();
        // 提交预览
        Texture previewTexture = preview.EndPreview();

        // 显示预览
        GUI.DrawTexture(dropArea, previewTexture);
    }

    private static GameObject BuildGameObject(string strName, Mesh mesh, Texture2D tex, Transform parent)
    {
        if (!mesh || !tex) return null;
        
        GameObject go = new GameObject(strName);
        var mr = go.AddComponent<MeshRenderer>();
        var mf = go.AddComponent<MeshFilter>();

        Material defaultMat =
            AssetDatabase.LoadAssetAtPath<Material>(
                "Assets/ArtTmp/Scenes/PlanearPolyMeshGenerator/Test/planebuildingtest_commonshadow.mat");
        
        if (!defaultMat || strName == "Color")
        {
            //defaultMat = new Material(Shader.Find("Unlit/Texture"));
            defaultMat = new Material(Shader.Find("Sultan/Scene/BuildUnLit"));
            if (null != defaultMat)
            {
                defaultMat.SetShaderPassEnabled("CSMShadowCaster", false);
                defaultMat.SetInt("_RECEIVE_SHADOWS", 0);
                
                defaultMat.EnableKeyword("_ALPHATEST_ON");
            }
        }
        var mat = defaultMat;
        mat.mainTexture = tex;
        
        mf.sharedMesh = mesh;
        mr.sharedMaterial = mat;

        if (parent)
        {
            go.transform.SetParent(parent);
        }

        return go;
    }

    private static Mesh DoGenerate(Texture2D input)
    {
        if (!input) return null;
        
        TextureImporter importer = AssetImporter.GetAtPath(AssetDatabase.GetAssetPath(input)) as TextureImporter;

        TextureImporterFormat originFormat = TextureImporterFormat.Automatic;
        if (importer != null)
        {
            var platformSetting = importer.GetPlatformTextureSettings("Android");
            if (null != platformSetting)
            {
                originFormat = platformSetting.format;

                platformSetting.format = TextureImporterFormat.ETC2_RGBA8;
                
                importer.SetPlatformTextureSettings(platformSetting);
            }
            
            importer.isReadable = true;
            importer.SaveAndReimport();
        }
        
        Debug.Log(input.isReadable);

        if (!input.isReadable)
        {
            Debug.LogError("Input texture is not readable!");
            return null;
        }
        
        var pixels = input.GetPixels();
        var newTexture = new Texture2D(input.width,input.height,TextureFormat.ARGB32, false);

        var dummyGO = new GameObject("Dummy");
        dummyGO.hideFlags = HideFlags.HideAndDontSave;
        var spriteRenderer = dummyGO.AddComponent<SpriteRenderer>();
        
        for (int i = 0 ; i < pixels.Length ; i++ ) 
        { 
            if (pixels[i].a == 0)
            {
                pixels[i] = Color.clear;
            }
        }
        
        newTexture.SetPixels(pixels);
        newTexture.Apply();
        
        var mySprite = Sprite.Create(newTexture, new Rect(0, 0, newTexture.width, newTexture.height), new Vector2(0,0f), 1.0f, 0, SpriteMeshType.Tight,new Vector4(0,0,0,0),false);

        spriteRenderer.sprite = mySprite;
        
        var collider = dummyGO.AddComponent<PolygonCollider2D>();
        
        int w = newTexture.width;
        int h = newTexture.height;
        // var mesh = collider.CreateMesh(false, false);
        // mesh.hideFlags = HideFlags.None;
        // mesh.RecalculateBounds();
        // var bound = mesh.bounds;
        // var tmpvertices = mesh.vertices;
        // List<Vector2> uvs = new List<Vector2>();
        // for (int i = 0; i < tmpvertices.Length; i++)
        // {
        //     Vector2 uv = new Vector2((tmpvertices[i].x)/(w), (tmpvertices[i].y)/(h));
        //     uvs.Add(new Vector2(Mathf.Abs(uv.x), Mathf.Abs(uv.y)));
        // }
        // mesh.uv = uvs.ToArray();
        //
       

        Vector2[] vertices = spriteRenderer.sprite.vertices;
        Vector2[] uv = spriteRenderer.sprite.uv;
        ushort[] triangles = spriteRenderer.sprite.triangles;
        
        for (int i = 0; i < vertices.Length; i++)
        {
            var v = vertices[i];
            v.x -= w / 2.0f;
        
            v.x /= w;
            v.y /= h;
            vertices[i] = v;
        }

        // 创建一个新的 Mesh
        Mesh mesh = new Mesh();
        mesh.vertices = System.Array.ConvertAll(vertices, v => (Vector3)v); // 转换为 Vector3
        mesh.uv = uv;
        mesh.triangles =  System.Array.ConvertAll(triangles, t => (int)t);

        // 计算法线
        mesh.RecalculateNormals();
        
        mesh.RecalculateBounds();
        
        DestroyImmediate(dummyGO);
        
        importer = AssetImporter.GetAtPath(AssetDatabase.GetAssetPath(input)) as TextureImporter;

        if (importer != null)
        {
            var platformSetting = importer.GetPlatformTextureSettings("Android");
            if (null != platformSetting)
            {
                platformSetting.format = originFormat;
                importer.SetPlatformTextureSettings(platformSetting);
            }
            
            importer.isReadable = false;
            importer.SaveAndReimport();
        }
        
        return mesh;
    }

    private static void HandleMeshPreview(MeshPreview preview, Mesh GeneratedMesh, string tip)
    {
        if (null != GeneratedMesh)
        {
            if (null == preview)
            {
                preview = new UnityEditor.MeshPreview(GeneratedMesh);
            }

            if (preview.mesh != GeneratedMesh)
            {
                preview.mesh = GeneratedMesh;
            }
                
            var previewRect = GUILayoutUtility.GetRect(250, 250, GUILayout.ExpandWidth(true));
            preview.OnPreviewGUI(previewRect, tip);
        }
    }

    private static void PingObject(UnityEngine.Object obj)
    {
        if (obj != null)
        {
            // 在项目窗口中选中文件夹
            Selection.activeObject = obj;
                
            // 自动展开文件夹视图
            EditorGUIUtility.PingObject(obj);
        }
    }

    private static void HandleClick(Rect dropArea, Texture2D texture)
    {
        bool isClick = false;
        Event currentEvent = Event.current;
        if (currentEvent.type == EventType.MouseDown)
        {
            if (dropArea.Contains(currentEvent.mousePosition))
            {
                isClick = true;
            }
        }

        if (isClick && texture)
        {
            PingObject(texture);
        }
    }
    
    private static void HandleDragAndDrop(Rect dropArea, ref Texture2D outputTexture)
    {
        Event currentEvent = Event.current;

        // 检查鼠标是否在拖放区域内
        if (currentEvent.type == EventType.DragUpdated || currentEvent.type == EventType.DragPerform)
        {
            if (dropArea.Contains(currentEvent.mousePosition))
            {
                // 检查拖入的对象是否是纹理
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;

                if (currentEvent.type == EventType.DragPerform)
                {
                    DragAndDrop.AcceptDrag();

                    foreach (UnityEngine.Object draggedObject in DragAndDrop.objectReferences)
                    {
                        if (draggedObject is Texture2D texture)
                        {
                            outputTexture = texture; // 设置拖入的纹理
                        }
                    }

                    currentEvent.Use(); // 标记事件已处理
                }
            }
        }
    }
}
