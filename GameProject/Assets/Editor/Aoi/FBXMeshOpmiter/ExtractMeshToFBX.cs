using System;
using System.Collections.Generic;
using System.Reflection;
using Autodesk.Fbx;
using Sultan.GPUSkin.Editor;
using Sultan.GPUSkin.Runtime;
using Unity.Collections;
using Unity.Mathematics;
using UnityEngine;
//using Unity.FbxSdk;
//using FbxExporters.Editor;
using UnityEngine.Rendering;
using Object = UnityEngine.Object;

namespace UnityEditor.Formats.Fbx.Exporter
{
    // Place in 'Editor' folder
    public class ExtractMeshToFBX : MonoBehaviour
    {
        // true: fbx file is easy-to-debug ascii, false: fbx file is binary.
        static bool saveFbxAsAscii = false;

        // The preferred axis system for the exported fbx file
        static FbxAxisSystem fbxAxisSystem = FbxAxisSystem.Max;

        // The preferred units of the exported fbx file
        static FbxSystemUnit fbxUnit = FbxSystemUnit.m;

        static string fbxFileTitle = "TITLE HERE";
        static string fbxFileSubject = "SUBJECT HERE";
        static string fbxFileComment = "COMMENT HERE";
        static string fbxFileKeywords = "KEYWORDS HERE";
        static string fbxFileAuthor = "AUTHOR HERE";
        static string fbxFileRevision = "1.0";
        static string fbxFileApplication = "Unity FBX SDK";

        private const string TMP_FBX_PATH = "Assets/Editor/Aoi/FBXMeshOpmiter/Tmp.fbx";
        private const string TMP_MAT_PATH = "Assets/Editor/Aoi/FBXMeshOpmiter/SubMeshMat.mat";
        
        static void LoopChild(Transform t, List<Object> output)
        {
            output.Add(t.gameObject);

            for (int i = 0; i < t.childCount; i++)
            {
                var child = t.GetChild(i);
                LoopChild(child, output);
            }
        }

        static Material FixMissingMaterial(string goName)
        {
            string findAssetFilter = string.Format("{0} t:Material", goName);
            var buildcityCollect = AssetDatabase.FindAssets(findAssetFilter);
            if (null != buildcityCollect && buildcityCollect.Length > 0)
            {
                var guidPath = buildcityCollect[0];

                var assetPath = AssetDatabase.GUIDToAssetPath(guidPath);
                Material mat = AssetDatabase.LoadAssetAtPath<Material>(assetPath);

                return mat;
            }

            return null;
        }
        

        static Mesh FixMissingMesh(string goName)
        {
            string modelPath = "Assets/ArtTmp/Scenes/Models/Build/models";
            string findAssetFilter = string.Format("{0} t:Model", goName);
            string findAssetAnimFilter = string.Format("{0} @skin t:Model", goName);
            bool meshhasAnim = false;

            var buildcityCollect = AssetDatabase.FindAssets(findAssetAnimFilter);
            string guidPath = string.Empty;
            Mesh finedMesh = null;
            if (null != buildcityCollect && buildcityCollect.Length > 0)
            {
                guidPath = buildcityCollect[0];

                var assetPath = AssetDatabase.GUIDToAssetPath(guidPath);
                GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                SkinnedMeshRenderer[] smrs = go.GetComponentsInChildren<SkinnedMeshRenderer>();
                foreach (var smr in smrs)
                {
                    double similarity = findSimilarity(smr.name, go.name);
                    if (similarity > 0.9f)
                    {
                        finedMesh = smr.sharedMesh;
                        break;
                    }
                }

                MeshFilter[] mfs = go.GetComponentsInChildren<MeshFilter>();
                foreach (var mf in mfs)
                {
                    double similarity = findSimilarity(mf.name, go.name);
                    if (similarity > 0.9f)
                    {
                        finedMesh = mf.sharedMesh;
                        break;
                    }
                }

                if (null != finedMesh)
                {
                    meshhasAnim = true;
                }
            }

            if (!meshhasAnim)
            {
                buildcityCollect = AssetDatabase.FindAssets(findAssetFilter, new string[] { modelPath });
                if (null != buildcityCollect && buildcityCollect.Length > 0)
                {
                    guidPath = buildcityCollect[0];
                    
                    var assetPath = AssetDatabase.GUIDToAssetPath(guidPath);
                    GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                    MeshFilter[] mfs = go.GetComponentsInChildren<MeshFilter>();

                    foreach (var mf in mfs)
                    {
                        double similarity = findSimilarity(mf.name, go.name);
                        if (similarity > 0.9f)
                        {
                            finedMesh = mf.sharedMesh;
                            break;
                        }
                    }
                }
            }

            return finedMesh;
        }

        [MenuItem("Assets/BuildCityFBXOpmize/ClearSMRMAT")]
        public static void ClearSMRMATS()
        {
            List<string> handleFBXPath = new List<string>();
            var buildcityCollect = AssetDatabase.FindAssets("build_city t:GameObject", 
                new string[]
                {
                    "Assets/Res/Scenes/Prefabs/Buildcombined",
                    "Assets/ArtTmp/Scenes/Prefabs",
                });
            if (null != buildcityCollect)
            {
                foreach (var guid in buildcityCollect)
                {
                    var assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    //Debug.Log(assetPath);
                    handleFBXPath.Add(assetPath);
                }
            }
            
            foreach(var path in handleFBXPath)
            {
                GameObject go = PrefabUtility.LoadPrefabContents(path);
                Renderer[] renderers = go.GetComponentsInChildren<Renderer>();
                MeshFilter[] mfs = go.GetComponentsInChildren<MeshFilter>();

                bool dataDirty = false;
                if (null != renderers)
                {
                    foreach (var r in renderers)
                    {
                        if (r.sharedMaterials.Length > 1)
                        {
                            if (r.sharedMaterials[1].shader.name == "Sultan/Common/Default" || r.sharedMaterials[1].name == "Lit")
                            {
                                r.sharedMaterials = new Material[1] { r.sharedMaterials[0] };
                                dataDirty = true;
                            }
                        }

                        if (r.sharedMaterials.Length > 0)
                        {
                            if (null == r.sharedMaterials[0] || r.sharedMaterials[0].shader.name == "Sultan/Common/Default" || r.sharedMaterials[0].name == "Lit")
                            {
                                r.sharedMaterials = new Material[1] { FixMissingMaterial(r.gameObject.name) };
                                dataDirty = true;
                            }  
                        }
                        
                        if (r is SkinnedMeshRenderer)
                        {
                            SkinnedMeshRenderer smr = r as SkinnedMeshRenderer;
                            if (null == smr.sharedMesh)
                            {
                                Debug.Log("need Fix smr mesh:" + go.name);
                                smr.sharedMesh = FixMissingMesh(smr.gameObject.name);
                                if (null != smr.sharedMesh)
                                {
                                    Debug.Log("Fiexd:" + go.name);
                                }
                                dataDirty = true;
                            }
                        }
                    }
                }

                if (null != mfs)
                {
                    foreach (var mf in mfs)
                    {
                        if (null == mf.sharedMesh)
                        {
                            Debug.Log("need Fix mf mesh:" + go.name);
                            mf.sharedMesh = FixMissingMesh(mf.gameObject.name);
                            if (null != mf.sharedMesh)
                            {
                                Debug.Log("Fixed:" + go.name);
                            }
                            dataDirty = true;
                        }
                    }
                }

                if (dataDirty)
                {
                    PrefabUtility.SaveAsPrefabAssetAndConnect(go, path, InteractionMode.AutomatedAction);
                }
                else
                {
                    PrefabUtility.UnloadPrefabContents(go);
                }
            }

            EditorUtility.DisplayDialog("好了", "好了", "好了");
        }

        [MenuItem("Assets/BuildCityFBXOpmize/Export")]
        public static void MenuExtractToFBX()
        {
            string saveFolder = "Assets/ArtTmp/Scenes/Models/CompressedIndexBufferMesh";

            //clear folder
            if (System.IO.Directory.Exists(saveFolder))
            {
                System.IO.DirectoryInfo di = new System.IO.DirectoryInfo(saveFolder);
                foreach (System.IO.FileInfo file in di.GetFiles())
                {
                    file.Delete(); 
                }
                AssetDatabase.Refresh();
            }

            //create folder
            if (!System.IO.Directory.Exists(saveFolder))
            {
                System.IO.Directory.CreateDirectory(saveFolder);
                AssetDatabase.Refresh();
            }
            
            Dictionary<string,string> handleFBXPath = new Dictionary<string, string>();
            //正向查找，从游戏直接使用的资源查找，然后修改对应fbx,并构建关联
            //旧Version:直接改FBX 可能会破坏预制体引用
            var buildcityCollect = AssetDatabase.FindAssets("t:GameObject", new string[]{"Assets/Res/Scenes/Prefabs/Buildcombined"});
            
            if (null != buildcityCollect)
            {
                foreach (var guid in buildcityCollect)
                {
                    var assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    var fileName = System.IO.Path.GetFileNameWithoutExtension(assetPath);

                    //美术这个资源有相同名字，不同网格的情况，忽略不管
                    if (fileName.Contains("build_city_kuai")) continue;

                    //if (!fileName.Contains("build_city_academy_lv2")) continue;
                    
                    handleFBXPath.Add(fileName,assetPath);
                }
            }
            
            foreach (var path in handleFBXPath)
            {
                GameObject go = PrefabUtility.LoadPrefabContents(path.Value);

                Debug.Log("Handle--------------:" + go.name);
                List<GameObject> toDelete = new List<GameObject>();

                bool dataDirty = false;
                
                for (int i = 0; i < go.transform.childCount; i++)
                {
                    var child = go.transform.GetChild(i);
                    
                    //如果是fbx预制体，实例化后替代
                    if (PrefabUtility.IsPartOfAnyPrefab(child))
                    {
                        string pathToPrefagb = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(child);
                        var source = PrefabUtility.GetCorrespondingObjectFromSourceAtPath(child, pathToPrefagb);
                        string prefabPath = AssetDatabase.GetAssetPath(source);
                        Debug.Log("UnPack:" + prefabPath);
                        if (prefabPath.EndsWith(".fbx"))
                        {
                            GameObject newGo = GameObject.Instantiate(child.gameObject);
                            newGo.transform.parent = go.transform;
                            
                            toDelete.Add(child.gameObject);
                        }
                        else
                        {
                            GameObject rootPrefab = PrefabUtility.GetNearestPrefabInstanceRoot(child);
                            PrefabUtility.UnpackPrefabInstance(rootPrefab, PrefabUnpackMode.Completely, InteractionMode.AutomatedAction);
                        }
                    }
                }

                //移除fbx prefab关联
                foreach (var child in toDelete)
                {
                    GameObject.DestroyImmediate(child);
                }

                MeshFilter[] mfs = go.GetComponentsInChildren<MeshFilter>();
                foreach (var mf in mfs)
                {
                    Mesh m = mf.sharedMesh;

                    if (!m)
                    {
                        continue;
                    }
                    
                    MeshRenderer mr = mf.gameObject.GetComponent<MeshRenderer>();
                    VATAnimator vat = mf.gameObject.GetComponent<VATAnimator>();
                    if (vat)
                    {
                        continue;
                    }

                    if (m.subMeshCount > 1)
                    {
                        continue;
                    }
                    
                    if (mr.sharedMaterial && !mr.sharedMaterial.GetShaderPassEnabled("CSMShadowCaster"))
                    {
                        continue;
                    }
                    
                    string currentMeshPath = AssetDatabase.GetAssetPath(m);

                    if (currentMeshPath.Contains("CompressedIndexBufferMesh"))
                    {
                        continue;
                    }
                    
                    string compressedPath = string.Format("{0}/{1}.asset",saveFolder, m.name);
                    Mesh hasCompressedMesh = AssetDatabase.LoadAssetAtPath<Mesh>(compressedPath);
                    if (hasCompressedMesh && hasCompressedMesh.vertexCount == m.vertexCount)
                    {
                        mf.sharedMesh = hasCompressedMesh;
                        dataDirty = true;
                        continue;
                    }
                    
                    Mesh newM = GameObject.Instantiate(m);
                    newM = CompressMeshIfNeeded(newM);
                    
                    //remove all uv data > 2
                    newM.uv3 = newM.uv4 = newM.uv5 = newM.uv6 = newM.uv7 = newM.uv8 = null;
                    
                    newM.name = m.name;
                    bool done = ModelExportHelper.AppendShadowIndexBuffer(newM);

                    if (done)
                    {
                        newM.bounds = m.bounds;
                        newM.RecalculateUVDistributionMetrics();
                        newM.Optimize();
                        newM.UploadMeshData(true);
                        AssetDatabase.CreateAsset(newM, compressedPath);
                        mf.sharedMesh = AssetDatabase.LoadAssetAtPath<Mesh>(compressedPath);
                        dataDirty = true;
                    }
                }

                SkinnedMeshRenderer[] smrs = go.GetComponentsInChildren<SkinnedMeshRenderer>();
                foreach (var smr in smrs)
                {
                    Mesh m = smr.sharedMesh;

                    if (!m)
                    {
                        continue;
                    }
                    
                    if (m.subMeshCount > 1)
                    {
                        continue;
                    }
                    
                    string currentMeshPath = AssetDatabase.GetAssetPath(m);

                    if (currentMeshPath.Contains("CompressedIndexBufferMesh"))
                    {
                        continue;
                    }
                    
                    string compressedPath = string.Format("{0}/{1}.asset",saveFolder, m.name);
                    Mesh hasCompressedMesh = AssetDatabase.LoadAssetAtPath<Mesh>(compressedPath);
                    if (hasCompressedMesh && hasCompressedMesh.vertexCount == m.vertexCount)
                    {
                        smr.sharedMesh = hasCompressedMesh;
                        continue;
                    } 
                    
                    Mesh newM = GameObject.Instantiate(m);
                    newM = CompressMeshIfNeeded(newM);
                    newM.name = m.name;
                    bool done = ModelExportHelper.AppendShadowIndexBuffer(newM);

                    if (done)
                    {
                        newM.Optimize();
                        newM.UploadMeshData(true);
                        AssetDatabase.CreateAsset(newM, compressedPath);
                        smr.sharedMesh = AssetDatabase.LoadAssetAtPath<Mesh>(compressedPath);
                        dataDirty = true;
                    }
                }

                if (dataDirty)
                {
                    PrefabUtility.SaveAsPrefabAssetAndConnect(go, path.Value, InteractionMode.AutomatedAction);
                    dataDirty = false;
                }
            }
        }

        static Mesh CompressMeshIfNeeded(Mesh mesh)
        {
            List<VertexAttributeDescriptor> vertexDescList = new List<VertexAttributeDescriptor>();
            mesh.GetVertexAttributes(vertexDescList);
        
            foreach (var vertexDesc in vertexDescList)
            {
                if (vertexDesc.attribute == VertexAttribute.Position)
                {
                    if (vertexDesc.format == VertexAttributeFormat.Float16 && vertexDesc.dimension == 4)
                    {
                        return mesh;
                    }
                }
            }
        
            bool canCompress = vertexDescList.Count == 2 && vertexDescList[0].attribute == VertexAttribute.Position &&
                               vertexDescList[1].attribute == VertexAttribute.TexCoord0;
            canCompress |= vertexDescList.Count == 4 && vertexDescList[2].attribute == VertexAttribute.BlendWeight &&
                           vertexDescList[3].attribute == VertexAttribute.BlendIndices;
        
            if (!canCompress) return mesh;
        
            List<VertexAttributeDescriptor> output = new List<VertexAttributeDescriptor>();
            output.Add(new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float16, 4));
            output.Add(new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float16, 2));

            if (vertexDescList.Count == 4)
            {
                output.Add(new VertexAttributeDescriptor(VertexAttribute.BlendIndices, VertexAttributeFormat.UInt16, 4, vertexDescList[2].stream));
                output.Add(new VertexAttributeDescriptor(VertexAttribute.BlendWeight, VertexAttributeFormat.Float16, 4, vertexDescList[3].stream));
            }
            
            NativeArray<tmpCompressVertex> opmizedVertexList =
                new NativeArray<tmpCompressVertex>(mesh.vertexCount, Allocator.Persistent);
        
            List<Vector3> vertex = new List<Vector3>();
            mesh.GetVertices(vertex);
            
            Vector2[] uv = mesh.uv;
            
            for (int i = 0; i < mesh.vertexCount; i++)
            {
                tmpCompressVertex singleVertex = new tmpCompressVertex();
                singleVertex.posX = (half)vertex[i].x;
                singleVertex.posY = (half)vertex[i].y;
                singleVertex.posZ = (half)vertex[i].z;
                singleVertex.posW = (half)1;
        
                singleVertex.uvX = (half)uv[i].x;
                singleVertex.uvY = (half)uv[i].y;
        
                opmizedVertexList[i] = singleVertex;
            }
        
            int[] triangles = mesh.GetTriangles(0);
            var newMesh = new Mesh();
            newMesh.SetVertexBufferParams(mesh.vertexCount, output.ToArray());
            newMesh.SetVertexBufferData(opmizedVertexList, 0, 0, opmizedVertexList.Length, 0);
        
            if (vertexDescList.Count == 4)
            {
                var boneIndicesData = mesh.GetVertexBuffer(vertexDescList[2].stream);
                BoneWeight[] indicesData = new BoneWeight[mesh.vertexCount];
                boneIndicesData.GetData(indicesData);
                tmpCompressSkin[] compressIndicesData = CovertSkin(indicesData);

                newMesh.SetVertexBufferData(compressIndicesData, 0, 0, mesh.vertexCount,vertexDescList[2].stream);
                newMesh.bindposes = mesh.bindposes;
            }
            
            newMesh.SetTriangles(triangles, 0);
            opmizedVertexList.Dispose();
        
            return newMesh;
        }

        static tmpCompressSkin[] CovertSkin(BoneWeight[] input)
        {
            tmpCompressSkin[] output = new tmpCompressSkin[input.Length];
            for (int i = 0; i < input.Length; i++)
            {
                tmpCompressSkin s;
                s.wx = (half)input[i].weight0;
                s.wy = (half)input[i].weight1;
                s.wz = (half)input[i].weight2;
                s.ww = (half)input[i].weight3;

                s.ix = (ushort)input[i].boneIndex0;
                s.iy = (ushort)input[i].boneIndex1;
                s.iz = (ushort)input[i].boneIndex2;
                s.iw = (ushort)input[i].boneIndex3;

                output[i] = s;
            }

            return output;
        }

        [Serializable]
        struct tmpCompressSkin
        {
            [SerializeField]
            public half wx, wy, wz, ww;
            [SerializeField]
            public ushort ix, iy, iz, iw;
        }
        
        struct tmpCompressVertex
        {
            public half posX, posY, posZ, posW;
            public half uvX, uvY;
        }

        static string RecreateFBX(GameObject go, string meshFilePath)
        {
                List<Object> outputList = new List<Object>();
                outputList.Add(go);
            
                var newFBX = ModelExporter.ExportObjects(TMP_FBX_PATH, outputList.ToArray());
                
                UnityEditor.ModelImporter oldImporter =
                    AssetImporter.GetAtPath(meshFilePath) as UnityEditor.ModelImporter;
            
                UnityEditor.ModelImporter newImporter = AssetImporter.GetAtPath(newFBX) as UnityEditor.ModelImporter;
                newImporter.importBlendShapes = oldImporter.importBlendShapes;
                newImporter.importVisibility = oldImporter.importVisibility;
                newImporter.importCameras = oldImporter.importCameras;
                newImporter.importLights = oldImporter.importLights;
                newImporter.preserveHierarchy = oldImporter.preserveHierarchy;
                newImporter.sortHierarchyByName = oldImporter.sortHierarchyByName;
                newImporter.meshCompression = oldImporter.meshCompression;
                newImporter.isReadable = oldImporter.isReadable;
                newImporter.materialImportMode = oldImporter.materialImportMode;
                newImporter.animationCompression = oldImporter.animationCompression;
                newImporter.importNormals = oldImporter.importNormals;
                newImporter.importTangents = oldImporter.importTangents;
                newImporter.importBlendShapeNormals = oldImporter.importBlendShapeNormals;
                newImporter.humanDescription = oldImporter.humanDescription;
                newImporter.importAnimation = oldImporter.importAnimation;
                newImporter.SaveAndReimport();
                
                
                //backup guid
                //string metaPath = meshFilePath + ".meta";
                //var content = System.IO.File.ReadAllLines(metaPath);
                //Debug.Log(content);
            
                //copy back and replce
                //AssetDatabase.DeleteAsset(meshFilePath);
                //EditorUtility.CopySerialized(AssetDatabase.LoadAssetAtPath<Object>(newFBX), AssetDatabase.LoadAssetAtPath<Object>(meshFilePath));
                AssetDatabase.CopyAsset(TMP_FBX_PATH, meshFilePath);
                AssetDatabase.DeleteAsset(TMP_FBX_PATH);
                AssetDatabase.Refresh();
                
                //recover guid
                //System.IO.File.WriteAllLines(metaPath, content);
                
                AssetDatabase.Refresh();

                return newFBX;
        }
        

        [MenuItem("Assets/BuildCityFBXOpmize/Export")]
        public static void MenuExtractToFBX0()
        {
            Dictionary<string,string> handleFBXPath = new Dictionary<string, string>();
            //先找出所有模型
            var buildcityCollect = AssetDatabase.FindAssets("build_city_shengjipai_01 t:Model", new string[]{"Assets/ArtTmp/Scenes/Models/Build/models"});
            
            if (null != buildcityCollect)
            {
                foreach (var guid in buildcityCollect)
                {
                    var assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    var fileName = System.IO.Path.GetFileNameWithoutExtension(assetPath);
                    handleFBXPath.Add(fileName,assetPath);
                }
            }
            
            //找出所有带Skin的模型，有Skin的移除后缀，替换Model模型， 无Skin的，直接用Model模型
            buildcityCollect = AssetDatabase.FindAssets("build_city_shengjipai_01 @skin t:Model");
            if (null != buildcityCollect)
            {
                foreach (var guid in buildcityCollect)
                {
                    var assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    var fileName = System.IO.Path.GetFileName(assetPath);
                    fileName = fileName.Substring(0, fileName.IndexOf('@'));
                    //Debug.Log(assetPath);
                    if (handleFBXPath.ContainsKey(fileName))
                    {
                        handleFBXPath[fileName] = assetPath;
                    }
                    else
                    {
                        handleFBXPath.Add(fileName,assetPath);
                    }
                }
            }

            foreach (var path in handleFBXPath)
            {
                string meshFilePath = path.Value;
                UnityEditor.ModelImporter oldImporter =
                    AssetImporter.GetAtPath(meshFilePath) as UnityEditor.ModelImporter;
                
                Mesh m = null;
                GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(meshFilePath);
            
                var renderer = go.GetComponentInChildren<Renderer>();
                MeshFilter mf = go.GetComponentInChildren<MeshFilter>();
            
                if (renderer)
                {
                    Material[] mats = new Material[2];
                    mats[0] = renderer.sharedMaterial;
                    mats[1] = AssetDatabase.LoadAssetAtPath<Material>(TMP_MAT_PATH);
            
                    if (renderer is SkinnedMeshRenderer)
                    {
                        m = ((SkinnedMeshRenderer)renderer).sharedMesh;
                        ModelExportHelper.AppendShadowIndexBuffer(m);
                        ((SkinnedMeshRenderer)renderer).sharedMesh = m;
                    }
                    else
                    {
                        m = mf.sharedMesh;
                        ModelExportHelper.AppendShadowIndexBuffer(m);
                        mf.sharedMesh = m;
                    }
            
                    renderer.sharedMaterials = mats;
                }
            
                if (mf)
                {
                    m = mf.sharedMesh;
                    mf.sharedMesh = m;
                }
                
                List<Object> outputList = new List<Object>();
                outputList.Add(go);
            
                var newFBX = ModelExporter.ExportObjects(TMP_FBX_PATH, outputList.ToArray());
            
                UnityEditor.ModelImporter newImporter = AssetImporter.GetAtPath(newFBX) as UnityEditor.ModelImporter;
                newImporter.importBlendShapes = oldImporter.importBlendShapes;
                newImporter.importVisibility = oldImporter.importVisibility;
                newImporter.importCameras = oldImporter.importCameras;
                newImporter.importLights = oldImporter.importLights;
                newImporter.preserveHierarchy = oldImporter.preserveHierarchy;
                newImporter.sortHierarchyByName = oldImporter.sortHierarchyByName;
                newImporter.meshCompression = oldImporter.meshCompression;
                newImporter.isReadable = oldImporter.isReadable;
                newImporter.materialImportMode = oldImporter.materialImportMode;
                newImporter.animationCompression = oldImporter.animationCompression;
                newImporter.importNormals = oldImporter.importNormals;
                newImporter.importTangents = oldImporter.importTangents;
                newImporter.importBlendShapeNormals = oldImporter.importBlendShapeNormals;
                newImporter.humanDescription = oldImporter.humanDescription;
                newImporter.importAnimation = oldImporter.importAnimation;
                newImporter.SaveAndReimport();
                
                
                //backup guid
                string metaPath = meshFilePath + ".meta";
                var content = System.IO.File.ReadAllLines(metaPath);
                Debug.Log(content);
            
                //copy back and replce
                //AssetDatabase.DeleteAsset(meshFilePath);
                //EditorUtility.CopySerialized(AssetDatabase.LoadAssetAtPath<Object>(newFBX), AssetDatabase.LoadAssetAtPath<Object>(meshFilePath));
                AssetDatabase.CopyAsset(TMP_FBX_PATH, meshFilePath);
                AssetDatabase.DeleteAsset(TMP_FBX_PATH);
                AssetDatabase.Refresh();
                
                //recover guid
                System.IO.File.WriteAllLines(metaPath, content);
                
                AssetDatabase.Refresh();
            }

            //if (null != Selection.activeObject)   
            //{

            // Debug.Log(meshFilePath);
            // var children = AssetDatabase.LoadAllAssetRepresentationsAtPath(meshFilePath);
            // foreach (var child in children)
            // {
            //     Debug.Log(child.name);
            //     Debug.Log(AssetDatabase.GetAssetPath(child));
            //     if (child is Mesh)
            //     {
            //         Mesh newMesh = GameObject.Instantiate(child as Mesh);
            //         newMesh.name = "fuck";
            //         AssetDatabase.AddObjectToAsset(newMesh, go);
            //     }
            // }
            // AssetDatabase.Refresh();
            //}
            // // We assume validation worked and this is always defined.
            // Mesh mesh = Selection.activeObject as Mesh;
            //
            // // Set up paths
            // string meshFilePath = AssetDatabase.GetAssetPath(mesh);
            // string meshDirectory = Path.GetDirectoryName(meshFilePath);
            //
            // string filename = Path.GetFileNameWithoutExtension(meshFilePath) + ".fbx";
            // string filePath = Path.Combine(meshDirectory, filename);
            //
            // ExtractToFBX(mesh, filePath);
        }

        public static void ExtractToFBX(Mesh mesh, string filePath)
        {
            // Make a temporary copy of the mesh to modify it
            Mesh tempMesh = Object.Instantiate(mesh);
            tempMesh.name = mesh.name + "tmp";

            // If meters, divide by 100 since default is cm. Assume centered at origin.
            if (fbxUnit == FbxSystemUnit.m)
            {
                Vector3[] vertices = tempMesh.vertices;
                for (int i = 0; i < vertices.Length; ++i)
                    vertices[i] /= 100.0f;
                tempMesh.vertices = vertices;
            }
            // You could handle other SystemUnits here


            // FBX Manager
            FbxManager manager = FbxManager.Create();
            manager.SetIOSettings(FbxIOSettings.Create(manager, Globals.IOSROOT));

            FbxImporter importer = FbxImporter.Create(manager, "myimporter");
            bool status = importer.Initialize(filePath, -1, manager.GetIOSettings());
            FbxScene fbxScene = FbxScene.Create(manager, "myScene");

            FbxDocumentInfo sceneInfo = FbxDocumentInfo.Create(manager, "SceneInfo");

            // Set up scene info
            sceneInfo.mTitle = fbxFileTitle;
            sceneInfo.mSubject = fbxFileSubject;
            sceneInfo.mComment = fbxFileComment;
            sceneInfo.mAuthor = fbxFileAuthor;
            sceneInfo.mRevision = fbxFileRevision;
            sceneInfo.mKeywords = fbxFileKeywords;
            sceneInfo.Original_ApplicationName.Set(fbxFileApplication);
            sceneInfo.LastSaved_ApplicationName.Set(fbxFileApplication);
            fbxScene.SetSceneInfo(sceneInfo);

            // Set up Global settings
            FbxGlobalSettings globalSettings = fbxScene.GetGlobalSettings();
            globalSettings.SetSystemUnit(fbxUnit);
            globalSettings.SetAxisSystem(fbxAxisSystem);

            FbxNode modelNode = FbxNode.Create(fbxScene, tempMesh.name);
            // Add mesh to a node in the scene
            // using (ModelExporter modelExporter = new ModelExporter())
            // {
            //     var t = typeof(ModelExporter);
            //     var mis = t.GetMethods(BindingFlags.NonPublic | BindingFlags.Instance);
            //     MethodInfo mi = null;
            //     foreach (var m in mis)
            //     {
            //         if (m.Name == "ExportMesh" && m.GetParameters().Length == 3)
            //         {
            //             mi = m;
            //             break;
            //         }
            //     }
            //
            //     if (null != mi)
            //     {
            //         bool ret = (bool)mi.Invoke(modelExporter, new object[] { tempMesh, modelNode, null });
            //         if (!ret)
            //         {
            //             Debug.LogError("Problem Exporting Mesh");
            //         }
            //     }
            //
            //     // if (!modelExporter.ExportMesh(tempMesh, modelNode))
            //     //     Debug.LogError("Problem Exporting Mesh");
            // }

            // add the model to the scene
            fbxScene.GetRootNode().AddChild(modelNode);

            bool good = importer.Import(fbxScene);
            AssetDatabase.Refresh();
            Debug.Log(good);


            // // FBX Exporter
            // FbxExporter fbxExporter = FbxExporter.Create(manager, "Exporter");
            //
            // // Binary
            // int fileFormat = -1;
            // // Ascii
            // if (saveFbxAsAscii)
            //     fileFormat = manager.GetIOPluginRegistry().FindWriterIDByDescription("FBX ascii (*.fbx)");
            //
            // fbxExporter.Initialize(filePath, fileFormat, manager.GetIOSettings());
            // fbxExporter.SetFileExportVersion("FBX201400");
            //
            // // FBX Scene
            // FbxScene fbxScene = FbxScene.Create(manager, "Scene");
            // FbxDocumentInfo sceneInfo = FbxDocumentInfo.Create(manager, "SceneInfo");
            //
            // // Set up scene info
            // sceneInfo.mTitle    = fbxFileTitle;
            // sceneInfo.mSubject  = fbxFileSubject;
            // sceneInfo.mComment  = fbxFileComment;
            // sceneInfo.mAuthor   = fbxFileAuthor;
            // sceneInfo.mRevision = fbxFileRevision;
            // sceneInfo.mKeywords = fbxFileKeywords;
            // sceneInfo.Original_ApplicationName.Set(fbxFileApplication);
            // sceneInfo.LastSaved_ApplicationName.Set(fbxFileApplication);
            // fbxScene.SetSceneInfo(sceneInfo);
            //
            // // Set up Global settings
            // FbxGlobalSettings globalSettings = fbxScene.GetGlobalSettings();
            // globalSettings.SetSystemUnit(fbxUnit);
            // globalSettings.SetAxisSystem(fbxAxisSystem);
            //
            // FbxNode modelNode = FbxNode.Create(fbxScene, tempMesh.name);
            // // Add mesh to a node in the scene
            // using (ModelExporter modelExporter = new ModelExporter())
            // {
            //     var t = typeof(ModelExporter);
            //     var mis = t.GetMethods(BindingFlags.NonPublic | BindingFlags.Instance);
            //     MethodInfo mi = null;
            //     foreach(var m in mis)
            //     {
            //         if (m.Name == "ExportMesh" && m.GetParameters().Length == 3)
            //         {
            //             mi = m;
            //             break;
            //         }
            //     }
            //
            //     if (null != mi)
            //     {
            //         bool ret = (bool)mi.Invoke(modelExporter, new object[] { tempMesh, modelNode, null });
            //         if (!ret)
            //         {
            //             Debug.LogError("Problem Exporting Mesh");
            //         }
            //         
            //     }
            //     
            //     // if (!modelExporter.ExportMesh(tempMesh, modelNode))
            //     //     Debug.LogError("Problem Exporting Mesh");
            // }
            // // add the model to the scene
            // fbxScene.GetRootNode().AddChild(modelNode);
            //
            // // Finally actually save the scene
            // bool sceneSuccess = fbxExporter.Export(fbxScene);
            // AssetDatabase.Refresh();
            //
            // // clean up temporary model
            // if (Application.isPlaying)
            //     Object.Destroy(tempMesh);
            // else
            //     Object.DestroyImmediate(tempMesh);
        }
        
        public static int getEditDistance(string X, string Y)
        {
            int m = X.Length;
            int n = Y.Length;
 
            int[][] T = new int[m + 1][];
            for (int i = 0; i < m + 1; ++i) {
                T[i] = new int[n + 1];
            }
 
            for (int i = 1; i <= m; i++) {
                T[i][0] = i;
            }
            for (int j = 1; j <= n; j++) {
                T[0][j] = j;
            }
 
            int cost;
            for (int i = 1; i <= m; i++) {
                for (int j = 1; j <= n; j++) {
                    cost = X[i - 1] == Y[j - 1] ? 0: 1;
                    T[i][j] = Math.Min(Math.Min(T[i - 1][j] + 1, T[i][j - 1] + 1),
                        T[i - 1][j - 1] + cost);
                }
            }
 
            return T[m][n];
        }
 
        public static double findSimilarity(string x, string y) {
            if (x == null || y == null) {
                throw new ArgumentException("Strings must not be null");
            }
 
            double maxLength = Math.Max(x.Length, y.Length);
            if (maxLength > 0) {
                // 如果需要，可以选择忽略大小写
                return (maxLength - getEditDistance(x, y)) / maxLength;
            }
            return 1.0;
        }

    }
}