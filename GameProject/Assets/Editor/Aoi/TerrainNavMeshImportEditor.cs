using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using UnityEditor.SceneManagement;
using System;

public sealed class TerrainNavMeshImportEditor : Editor
{
    // private const string _existNavMeshDataPath = "Assets/StreamingAssets/Setting/AStar/";
    private const string _existNavMeshDataPath = "E:/AStar/";
    private static float[] _mapBoundMin = new float[3];
    private static float[] _mapBoundMax = new float[3];
    private static float[] _navMeshPolyVertices = new float[20480000 * 3];
    private static float _navMeshPolyVerticesCount = 0;
    private static int _rise = 0;

    private static Mesh _debugMesh = null;
    private static Material _material = null;
    private static Color _drawColor = new Color(0.3137f, 0.9196f, 1.0f, 1.0f);
    static Material _polyMaterial;

    [UnityEditor.MenuItem("Tools/地形/加载寻路网格数据")]
    public static void LoadNavMesh()
    {    
        string sceneName = EditorSceneManager.GetActiveScene().name;
        string currentSceneName = sceneName.ToLower();
        string currentSceneNavMeshDataFile = _existNavMeshDataPath + currentSceneName + ".bytes";
        if (!File.Exists(currentSceneNavMeshDataFile))
        {
            return;
        }
        using (FileStream fileStream = new FileStream(currentSceneNavMeshDataFile, FileMode.Open, FileAccess.Read))
        {
            byte[] datas = new byte[fileStream.Length];
            fileStream.Read(datas, 0, System.Convert.ToInt32(fileStream.Length));
            bool ret = RecastDLL.BuildNAVFromMemoryData(0, datas);
            if (!ret) return;

            Array.Clear(_mapBoundMin,0,_mapBoundMin.Length);
            Array.Clear(_mapBoundMax, 0, _mapBoundMax.Length);
            Array.Clear(_navMeshPolyVertices, 0, _navMeshPolyVertices.Length);

            RecastDLL.GetNavMeshDatas(datas, (uint)datas.Length, _mapBoundMin, _mapBoundMax, _navMeshPolyVertices, ref _navMeshPolyVerticesCount);         
            SetupDebugMeshAndMaterial();
            SetupDebugMesh();
           // DrawMesh();
            SceneView.RepaintAll();
        }
    }

    [UnityEditor.MenuItem("Tools/地形/寻路测试")]

    public static void NavMeshFindPath()
    {
         string sceneName = EditorSceneManager.GetActiveScene().name;
         string currentSceneName = sceneName.ToLower();
        //  string currentSceneNavMeshDataFile = _existNavMeshDataPath + currentSceneName + ".bytes";
        string currentSceneNavMeshDataFile = _existNavMeshDataPath  + "TerrainNavMesh.bytes";
        if (!File.Exists(currentSceneNavMeshDataFile))
         {
             return;
         }
        using (FileStream fileStream = new FileStream(currentSceneNavMeshDataFile, FileMode.Open, FileAccess.Read))
         {
             byte[] datas = new byte[fileStream.Length];
             fileStream.Read(datas, 0, System.Convert.ToInt32(fileStream.Length));
             bool ret = RecastDLL.BuildNAVFromMemoryData(0, datas);
            if (!ret) return;

             fileStream.Read(datas, 0, System.Convert.ToInt32(fileStream.Length));

             GameObject startObj = GameObject.FindGameObjectWithTag("startPos");
             GameObject endObj = GameObject.FindGameObjectWithTag("endPos");

             AgentDataVector3 startPos = new AgentDataVector3();
             if (startObj)
             {
                 startPos.x = (int)(startObj.transform.position.x );
                 startPos.y = (int)(startObj.transform.position.y );
                 startPos.z = (int)(startObj.transform.position.z );
             }
             else
             {
                 startPos.x = (int)(7713.439f );
                 startPos.y = (int)(77.76529f );
                 startPos.z = (int)(10858.5f );
             }

             AgentDataVector3 endPos = new AgentDataVector3();
             if (endObj)
             {
                 endPos.x = (int)(endObj.transform.position.x );
                 endPos.y = (int)(endObj.transform.position.y );
                 endPos.z = (int)(endObj.transform.position.z );
             }
             else
             {
                 endPos.x = (int)(7674.3f );
                 endPos.y = (int)(77.76529f );
                 endPos.z = (int)(11062f );
             }

             float[] pPathInfo = new float[102400];
             uint resultCount = RecastDLL.GetNAVMeshPath(0, pPathInfo, (uint)pPathInfo.Length, ref startPos, ref endPos, 0);
             GameObject testContainer = new GameObject("Cotainer");

             resultCount = resultCount / 3;
             Vector3 pos = new Vector3();
             for (int i = 0; i < resultCount; i++)
             {              
                 pos.x = pPathInfo[i * 3];
                 pos.y = pPathInfo[i * 3 + 1];
                 pos.z = pPathInfo[i * 3 + 2];

                 GameObject obj = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                 obj.transform.localScale = Vector3.one;
                 obj.transform.localPosition = pos;
                 obj.transform.parent = testContainer.transform;
             }
        }
    }

    private static void SetupDebugMeshAndMaterial()
    {
        if (!_debugMesh)
            _debugMesh = new Mesh();
        if (!_material)
        {
            _material = new Material(Shader.Find("Unlit/Color"));        
            _material.SetColor("_Color", _drawColor);
        }
    }

    static void DrawMesh()
    {
        GameObject debugMesh = GameObject.Find("DebugMesh");
        MeshRenderer mr = null;
        MeshFilter mf = null;
        if (null == debugMesh)
        {
            debugMesh = new GameObject("DebugMesh");
            mr = debugMesh.AddComponent<MeshRenderer>();
            mf = debugMesh.AddComponent<MeshFilter>();
        }
        else
        {
            mr = debugMesh.GetComponent<MeshRenderer>();
            mf = debugMesh.GetComponent<MeshFilter>();
        }

        mf.sharedMesh = _debugMesh;
        mr.sharedMaterial = _material;     
    }
   

    private static void SetupDebugMesh()
    {
        _debugMesh = new Mesh();
        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();

        for (int i = 0; i < _navMeshPolyVerticesCount; i += 11)
        {
            float x = (float)_navMeshPolyVertices[i];
            float y = (float)_navMeshPolyVertices[i + 1] + _rise;
            float z = (float)_navMeshPolyVertices[i + 2];

            float x1 = (float)_navMeshPolyVertices[i + 3];
            float y1 = (float)_navMeshPolyVertices[i + 4] + _rise;
            float z1 = (float)_navMeshPolyVertices[i + 5];

            float x2 = (float)_navMeshPolyVertices[i + 6];
            float y2 = (float)_navMeshPolyVertices[i + 7] + _rise;
            float z2 = (float)_navMeshPolyVertices[i + 8];

            vertices.Add(new Vector3(x,y,z));
            vertices.Add(new Vector3(x1, y1, z1));
            vertices.Add(new Vector3(x2, y2, z2));  
        }

        if (vertices.Count > 65535)
       {
            float count = (int)Mathf.Ceil(vertices.Count / 65535) + 1;         
            Dictionary<int, List<Vector3>> dict = new Dictionary<int, List<Vector3>>();
            for (int i = 0; i < count; i++)
            {
                List<Vector3> lst = new List<Vector3>();
                int startIndex = i * 65535;
                int endIndex = Mathf.Min((i + 1) * 65535, vertices.Count);
                for (int j = startIndex; j < endIndex; j++)
                {
                    lst.Add(vertices[j]);
                }
                dict[i] = lst;
            }

            foreach (var item in dict)
            {
                DrawMesh22(item.Key, item.Value);
            }
        }
        else
        {
            for (int i = 0; i < vertices.Count - 3; i++)
            {
                triangles.Add(i);
            }

            _debugMesh.vertices = vertices.ToArray();
            _debugMesh.triangles = triangles.ToArray();
            DrawMesh();
        }

          
    }

    static void DrawMesh22(int index,List<Vector3> lst)
    {
        GameObject debugMesh = GameObject.Find("DebugMesh" + index);
        MeshRenderer mr = null;
        MeshFilter mf = null;
        if (null == debugMesh)
        {
            debugMesh = new GameObject("DebugMesh");
            mr = debugMesh.AddComponent<MeshRenderer>();
            mf = debugMesh.AddComponent<MeshFilter>();
        }
        else
        {
            mr = debugMesh.GetComponent<MeshRenderer>();
            mf = debugMesh.GetComponent<MeshFilter>();
        }

        Mesh _debugMesh = new Mesh();
        _debugMesh.vertices = lst.ToArray();

        List<int> triangles = new List<int>();

        for (int i = 0; i < lst.Count - 3; i++)
        {
            triangles.Add(i);
        }
        _debugMesh.triangles = triangles.ToArray();

        mf.sharedMesh = _debugMesh;
        mr.sharedMaterial = _material;
    }

    [UnityEditor.MenuItem("Tools/地形/动态障碍物测试")]

    public static void AddCylinderObstacle()
    {
        Shader shader = Shader.Find("Hidden/Internal-Colored");
        _polyMaterial = new Material(shader);
        _polyMaterial.hideFlags = HideFlags.HideAndDontSave;
        _polyMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        _polyMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        _polyMaterial.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        _polyMaterial.SetInt("_ZWrite", 0);

        string sceneName = EditorSceneManager.GetActiveScene().name;
        string currentSceneName = sceneName.ToLower();
        string currentSceneNavMeshDataFile = _existNavMeshDataPath + currentSceneName + ".bytes";
        if (!File.Exists(currentSceneNavMeshDataFile))
        {
            return;
        }     

        //float[] pos1 = new float[3];
       // pos1[0] = 5000;
       // pos1[1] = 0;
        //pos1[2] = 5000;

        Array.Clear(_navMeshPolyVertices, 0, _navMeshPolyVertices.Length);
        _navMeshPolyVerticesCount = 0;      

        using (FileStream fileStream = new FileStream(currentSceneNavMeshDataFile, FileMode.Open, FileAccess.Read))
        {
            byte[] datas = new byte[fileStream.Length];
            fileStream.Read(datas, 0, System.Convert.ToInt32(fileStream.Length));
            bool ret = RecastDLL.BuildNAVFromMemoryData(0, datas);
            if (!ret) return;

            int result = -1;
            GameObject[] objs = GameObject.FindGameObjectsWithTag("NavigationTest");
            float[] pos = new float[3*objs.Length];
            int size = objs.Length;
            for (int i=0;i<size;i++)
            {
                int index = i * 3;
                pos[index] = objs[i].transform.position.x;
                pos[index+1] = 0;
                pos[index + 2] = objs[i].transform.position.z;
            }
           
            result = RecastDLL.addCylinderObstacleUpdate(0, pos, 5, 100, _navMeshPolyVertices, ref _navMeshPolyVerticesCount,size);          
            //Debug.LogError("@@result=====" + result);
            if (result == 1)
            {
               // DrawPolyMeshDetails(false);
              //  SceneView.RepaintAll();
                //  Debug.LogError("@@=====" + _navMeshPolyVerticesCount);
                  SetupDebugMeshAndMaterial();
                  SetupDebugMesh();
                  SceneView.RepaintAll();
            }
        }
    }


    private static void DrawPolyMeshDetails(bool isColourful)
    {
        _polyMaterial.SetPass(0);

        GL.PushMatrix();

        GL.Begin(GL.TRIANGLES);

        if (!isColourful)
        {
            Color blurColor = new Color(28 / 255f, 137f / 255f, 211 / 255f, 192 / 255f);
            GL.Color(blurColor);
        }

        for (int i = 0; i < _navMeshPolyVerticesCount; i += 11)
        {
            float x = (float)_navMeshPolyVertices[i];
            float y = (float)_navMeshPolyVertices[i + 1] + _rise;
            float z = (float)_navMeshPolyVertices[i + 2];

            float x1 = (float)_navMeshPolyVertices[i + 3];
            float y1 = (float)_navMeshPolyVertices[i + 4] + _rise;
            float z1 = (float)_navMeshPolyVertices[i + 5];

            float x2 = (float)_navMeshPolyVertices[i + 6];
            float y2 = (float)_navMeshPolyVertices[i + 7] + _rise;
            float z2 = (float)_navMeshPolyVertices[i + 8];

            if (isColourful)
            {
                int value = (int)_navMeshPolyVertices[i + 9];
                Color polyColor = ToColor(value, 192);
                GL.Color(polyColor);
            }

            GL.Vertex3(x, y, z);
            GL.Vertex3(x1, y1, z1);
            GL.Vertex3(x2, y2, z2);
        }
        GL.End();

        // 用线用三角形
        GL.Begin(GL.LINES);
        GL.Color(Color.black);
        for (int i = 0; i < _navMeshPolyVerticesCount; i += 11)
        {
            float x = (float)_navMeshPolyVertices[i];
            float y = (float)_navMeshPolyVertices[i + 1] + _rise;
            float z = (float)_navMeshPolyVertices[i + 2];

            float x1 = (float)_navMeshPolyVertices[i + 3];
            float y1 = (float)_navMeshPolyVertices[i + 4] + _rise;
            float z1 = (float)_navMeshPolyVertices[i + 5];

            float x2 = (float)_navMeshPolyVertices[i + 6];
            float y2 = (float)_navMeshPolyVertices[i + 7] + _rise;
            float z2 = (float)_navMeshPolyVertices[i + 8];

            GL.Vertex3(x, y, z);
            GL.Vertex3(x1, y1, z1);

            GL.Vertex3(x1, y1, z1);
            GL.Vertex3(x2, y2, z2);

            GL.Vertex3(x2, y2, z2);
            GL.Vertex3(x, y, z);
        }
        GL.End();

        GL.PopMatrix();
    }

    private static int bit(int a, int b)
    {
        return (a & (1 << b)) >> b;
    }

    private static Color ToColor(int i, int a)
    {
        int r = bit(i, 1) + bit(i, 3) * 2 + 1;
        int g = bit(i, 2) + bit(i, 4) * 2 + 1;
        int b = bit(i, 0) + bit(i, 5) * 2 + 1;
        return new Color(r * 63 / 255f, g * 63 / 255f, b * 63 / 255f, a / 255f);
    }

}