using System;
using CI;
using Platform;
using UnityEditor;
using UnityEditor.Android;
using UnityEngine;

namespace Sultan.Tools
{
    [EditorTools("SDKTools")]
    public class SDKDebuggerTool : IEditorTools
    {
        enum ConnectState : byte 
        {
            Idle,
            Connecting,
            Connected,
        }
        private int _Port = 6667;
        private ConnectState _ConnectState= ConnectState.Idle;
        private static readonly string ADBPath;

        private void mappingPort()
        {
            #if UNITY_EDITOR_WIN
            ADB.GetInstance().Run(new []{$"forward tcp:{_Port} tcp:{_Port}"}, "端口映射失败");
            #endif
            // CLIUtil.Execute(CLI_Linker,
            //     string.Format("-srcPath {0}", varSrcPath),
            //     string.Format("-dstPath {0}", varDstPath));
        }
        private void StartDebugger() {
            // 注意加上UNITY_EDITOR宏判断，确保只在编辑器环境下使用
#if UNITY_EDITOR
            // 启动调试器，注意，这里的ip目前只能填写127.0.0.1，端口号默认6667
            _ConnectState = ConnectState.Connecting;
            SdkDebuggerClient.GetInstance().Connect("127.0.0.1", _Port, StatusCallback);
#endif
        }
        
        private void StopDebugger(){
#if UNITY_EDITOR
            SdkDebuggerClient.GetInstance().close();
            var com = GetOrCreateDebuggerControl();
            _ConnectState = ConnectState.Idle;
            com.Connected = false;
#endif
        }
        
        // 连接回调，status为true表示连接127.0.0.1成功
        private void StatusCallback(bool status) {
            Debug.Log($"SDK Debugger Connect {status}");
            
            _ConnectState = status ? ConnectState.Connected : ConnectState.Idle;
        }

        public void OnEnable()
        {
            _ConnectState = ConnectState.Idle;
        }
        
        public SDKDebuggerControl GetOrCreateDebuggerControl()
        {
            if (SdkDebuggerClient.Instance != null)
            {
                var obj = SdkDebuggerClient.GetInstance();
                var com = obj.GetComponent<SDKDebuggerControl>();
                if (com == null)
                {
                    com = obj.gameObject.AddComponent<SDKDebuggerControl>();
                }

                return com;
            }

            return null;
        }
        

        public void DrawMainGUI()
        {
            if (GUILayout.Button("端口映射"))
            {
                mappingPort();
            }
            
            if (!Application.isPlaying)
            {
                GUILayout.Label("SDK 调试工具只能在运行时使用");
            }
            else
            {
                var com = GetOrCreateDebuggerControl();
                if (ConnectState.Connected == _ConnectState)
                {
                    com.Connected = true;
                    _ConnectState = ConnectState.Idle;
                    OnemtImManager.Instance.DebuggerInit();
                }
                
                if (com.Connected)
                {
                    GUILayout.Label("SDK 调试工具已启动 ");
                    if (GUILayout.Button("停止调试"))
                    {
                        StopDebugger();
                    }
                }
                else if (_ConnectState == ConnectState.Connecting)
                {
                    GUILayout.Label("SDK 调试工具正在连接中 ");
                }
                else
                {
                    GUILayout.Label("SDK 调试工具未启动");
                    if (GUILayout.Button("启动调试"))
                    {
                        StartDebugger();
                    }
                }
            }
        }
    }
}