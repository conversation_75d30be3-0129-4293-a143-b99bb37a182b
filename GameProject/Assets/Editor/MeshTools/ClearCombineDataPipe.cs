using System;
using System.Globalization;
using System.Text.RegularExpressions;
using Sultan.BuildTools;
using Sultan.Tools;
using UnityEditor;
using UnityEngine;

namespace Sultan.ToolsPipe
{
    [ProcessingPipeline("CombineTools", "清楚旧资源", 0)]
    public class ClearCombineDataPipe : IProcessingPipeline
    {
        private MeshCombineSetting m_ContentPipe = null;
        private bool cleanAll = false;
        public void OnEnable(IProcessingPipelineContent content)
        {
            m_ContentPipe = content as MeshCombineSetting;
            cleanAll = false;
        }

        public void Start()
        {
            var matSavePath = $"{m_ContentPipe.CombineSavePath}/materials";
            var configSavePath =$"{m_ContentPipe.CombineSavePath}/config";
            var meshSavePath =$"{m_ContentPipe.CombineSavePath}/mesh";
            var textureSavePath =$"{m_ContentPipe.CombineSavePath}/textures";
            try
            {
                AssetDatabase.StartAssetEditing();
                clearDir(matSavePath, "t:material", !cleanAll);
                clearDir(textureSavePath, "t:texture", !cleanAll);
                clearDir(configSavePath, "combine", !cleanAll);

                if (cleanAll)
                {
                    clearDir(meshSavePath, "t:mesh", false);
                    clearPrefab();
                }

            }
            finally
            {
                AssetDatabase.StopAssetEditing();
            }
        }

        public void Clear()
        {
        }

        public void OnGUI()
        {
            cleanAll = EditorGUILayout.Toggle("清除全部数据（含预制）", cleanAll);
        }

        public void clearDir(string dirPath, string filter, bool checkTime)
        {
            string rule = "_(?<time>\\d+).(asset|mat|)";
            var s = System.DateTime.Now;

            var guids = AssetDatabase.FindAssets(filter, new []{dirPath});
            foreach (var gid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(gid);
                if (checkTime)
                {
                    var result = Regex.Matches(path, rule);
                    if (result.Count > 0)
                    {
                        var time = result[0].Groups["time"].Value;
                        var data = DateTime.ParseExact(time, "yyyyMMddHHmmss",CultureInfo.InvariantCulture, DateTimeStyles.None);
                        if (data.Year == s.Year && data.DayOfYear == s.DayOfYear)
                        {
                            continue;
                        }
                    }    
                }
                AssetDatabase.DeleteAsset(path);
            }
        }

        public void clearPrefab()
        {
            foreach (var buildConfig in m_ContentPipe.BuildPathList)
            {
                bool regex = !buildConfig.RegexRule.Equals("");
                var guids = AssetDatabase.FindAssets("t:Prefab", new []{buildConfig.SrcBuildPath});
                foreach (var guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    if (regex && !Regex.IsMatch(path, buildConfig.RegexRule))
                        continue;
                    GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                    if (!prefab)
                        continue;
                    // var meshOutputPath =
                    //     $"{m_ContentPipe.CombineSavePath}/mesh/{prefab.name}_{buildConfig.ExportNodeName}.asset";     
                    var savePath = $"{buildConfig.ExportPath}/{prefab.name}.prefab"; 
                    AssetDatabase.DeleteAsset(savePath); 
                }
            }
        }
    }
}