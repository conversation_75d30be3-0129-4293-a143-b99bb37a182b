using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using NUnit.Framework;
using Spine;
using Sultan.BuildTools;
using Sultan.Core;
using Sultan.Render;
using Unity.Collections;
using Unity.Mathematics;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

#if true 
namespace Sultan.Tools
{
    public static class CombineHelper
    {
        public static readonly string BUILD_UNLIT_SHAER_NAME = "Sultan/Scene/BuildUnLit";

        public struct BackUpTexInfo
        {
            public int Size;
            public TextureFormat Format;

            public BackUpTexInfo(Texture2D tex)
            {
                Size = tex.width;
                Format = tex.format;
            }
        }

        
        public struct TextureData
        {
            public Texture2DArray Texture2DArray;
            public Dictionary<Texture2D, BackUpTexInfo> InfoDict;
            public List<string> SortList;
            
            public static TextureData Create()
            {
                return new TextureData()
                {
                    InfoDict = new Dictionary<Texture2D, BackUpTexInfo>(),
                    SortList = new List<string>()
                };
            }
        }

        // public struct CombineMaterialData
        public class MaterialData<T> where T: IShaderBackUp
        {
            public Type BackUpType;
            public Material Material;
            public List<T> BlockBackUpList;
            public Dictionary<Material, IShaderBackUp> InfoDict;
            public List<string> MaterialSortList; // src material path
            public static MaterialData<T> Create()
            {
                return new MaterialData<T>()
                {
                    InfoDict = new Dictionary<Material, IShaderBackUp>(),
                    MaterialSortList = new List<string>(),
                    BlockBackUpList = new List<T>()
                };
            }
        }

        public struct MeshData
        {
            public Mesh Mesh;
            public Material Material;
            public Matrix4x4 LocalToWorld;
            public Vector3 scale;
        }
        
        struct CustomVertex
        {
            public half posX, posY, posZ, posW;
            public half uvX, uvY;
            public half transX, transY, transZ, scaleXZ;
            public half shadowCast, unused;
        }
        
        struct ColorCustomVertex
        {
            public half posX, posY, posZ, posW;
            public half R, G, B,A;
            public half uvX, uvY;
            public half transX, transY, transZ, transW;
        }
        
        #region Material and Texture2DArray

        public static void CollectTextureAndMat(List<Material> matList, ref Dictionary<string, Texture2D> packedTextureDict, ref Dictionary<string, Material> matDict)
        {
            for (int i = 0; i < matList.Count; i++)
            {
                var mat = matList[i];
                var mainTexture = mat.GetTexture("_BaseMap");
                if (mainTexture)
                {
                    string texName = mainTexture.name;
                    packedTextureDict.TryAdd(texName, mainTexture as Texture2D);
                }
                var path = AssetDatabase.GetAssetPath(mat);
                matDict?.TryAdd(path, mat);
            } 
        }
        
        /*
        //合并主题类材质数据
        public static ThemeRenderConfig CombineThemeMaterial(List<Material> matList, string outputDir, int arraySize = 256)
        {
            matList.Sort(SortMat);
            Dictionary<string, Texture2D> packedTextureDict = new Dictionary<string, Texture2D>();
            Dictionary<string, Material> matDict = new Dictionary<string, Material>();

            CollectTextureAndMat(matList, ref packedTextureDict, ref matDict);
            var textureData = CombineTextureArray(packedTextureDict, arraySize);

            ThemeRenderConfig cfg = new ThemeRenderConfig();
            foreach (var matKV in matDict)
            {
                ThemeInstanceProp prop = new ThemeInstanceProp();
                prop.BackUpMaterialProp(matKV.Value);
                var tex = matKV.Value.GetTexture("_BaseMap");
                // prop.TextureName = Path.GetFileName(AssetDatabase.GetAssetPath(tex)); 
                prop.TextureArrayIdx = GetMainTextureInArrayIndexByMaterial(matKV.Value, textureData.SortList);
                cfg.AddMatPropCfg(matKV.Key,  prop);
            }
            
            // AssetDatabase.CreateAsset(textureData.);
            var outDirName = Path.GetDirectoryName(outputDir);
            Debug.Log(outDirName);
            Debug.Log(Path.GetFileName(outputDir));
            CreateDir( outDirName, Path.GetFileName(outputDir));
            var s = System.DateTime.Now.ToString("yyyyMMddHHmmss");
            var texture2DArrayPath = $"{outputDir}/textures/combineTextureArray_{s}.asset";
            CreateDir(outputDir, "textures");
            AssetDatabase.CreateAsset(textureData.Texture2DArray, texture2DArrayPath);
            AssetDatabase.Refresh();
            
            var texture2DArray = AssetDatabase.LoadAssetAtPath<Texture>(texture2DArrayPath);
            textureData.Texture2DArray = (Texture2DArray)texture2DArray;

            Material combineMat = new Material(Shader.Find("Sultan/Scene/BuildCombineInstance"));           
            combineMat.SetTexture("_BaseMapArray", texture2DArray);
            combineMat.enableInstancing = true;
            combineMat.renderQueue = (int)RenderQueue.AlphaTest;
            CreateDir(outputDir, "materials");
            var matPath = $"{outputDir}/materials/combineMaterial_{s}.asset"; 
            AssetDatabase.CreateAsset(combineMat, matPath);
            AssetDatabase.Refresh();
            combineMat = AssetDatabase.LoadAssetAtPath<Material>(matPath);

            cfg.CombineMaterial = combineMat;
            cfg.Texture2DArray = texture2DArray;
            

            return cfg;
        }
        */
        
        //合并建筑材质
        public static CombineMaterialData CombineMaterial(List<Material> matList, string outputDir, int arraySize = 256)
        {
            matList.Sort(SortMat);
            List<Texture2D> textureList = new List<Texture2D>();
            Dictionary<string, Texture2D> packedTextureDict = new Dictionary<string, Texture2D>();
            Dictionary<string, Material> matDict = new Dictionary<string, Material>();
            CollectTextureAndMat(matList, ref packedTextureDict, ref matDict);
            // for (int i = 0; i < matList.Count; i++)
            // {
            //     var mat = matList[i];
            //     var mainTexture = mat.GetTexture("_BaseMap");
            //     if (mainTexture)
            //     {
            //         textureList.Add(mainTexture as Texture2D);
            //         
            //         string texName = mainTexture.name;
            //         packedTextureDict.TryAdd(texName, mainTexture as Texture2D);
            //     }
            //
            //     var path = AssetDatabase.GetAssetPath(mat);
            //     matDict.TryAdd(path, mat);
            // }
            
            // textureList.Sort(SortTexture);

            var textureData = CombineTextureArray(packedTextureDict, arraySize);
            
            var s = System.DateTime.Now.ToString("yyyyMMddHHmmss");
            var texture2DArrayPath = $"{outputDir}/textures/combineTextureArray_{s}.asset";
            CreateDir(outputDir, "textures");
            AssetDatabase.CreateAsset(textureData.Texture2DArray, texture2DArrayPath);
            AssetDatabase.Refresh();

            var texture2DArray = AssetDatabase.LoadAssetAtPath<Texture>(texture2DArrayPath);
            textureData.Texture2DArray = (Texture2DArray)texture2DArray;
            
            var materialData = CombineMaterialData<BuildUnLitBackUp>(matDict, textureData);

            var dataTexture = CombineTextureData(materialData.BlockBackUpList);
            var textureDataPath = $"{outputDir}/textures/combineTextureData_{s}.asset";
            AssetDatabase.CreateAsset(dataTexture, textureDataPath);
            AssetDatabase.Refresh();
            
            dataTexture = (Texture2D)AssetDatabase.LoadAssetAtPath<Texture>(textureDataPath);
            materialData.Material.SetTexture("_DataMap", dataTexture);
 
            
            CreateDir(outputDir, "materials");
            var matPath = $"{outputDir}/materials/combineMaterial_{s}.asset"; 
            Material oldMat = AssetDatabase.LoadAssetAtPath<Material>(matPath);
            if (oldMat)
            {
                AssetDatabase.DeleteAsset(matPath);
            }
            int h = GetNearSize(materialData.BlockBackUpList.Count);
            materialData.Material.SetFloat("_DataHeight", 1.0f / h);
            AssetDatabase.CreateAsset(materialData.Material, matPath);
            AssetDatabase.Refresh();

            var combineData = ScriptableObject.CreateInstance<CombineMaterialData>();
            combineData.ArrayData = (Texture2DArray)texture2DArray;
            combineData.CombineMaterial =AssetDatabase.LoadAssetAtPath<Material>(matPath);
            combineData.DataTexture = dataTexture;
            combineData.TextureList = textureData.SortList;
            combineData.BackUpData = materialData.BlockBackUpList;
            combineData.BackUpMaterialList = materialData.MaterialSortList;

            
            CreateDir(outputDir, "config");
            var configPath = $"{outputDir}/config/combineData_{s}.asset";
            AssetDatabase.CreateAsset(combineData, configPath);
            AssetDatabase.Refresh();

            return AssetDatabase.LoadAssetAtPath<CombineMaterialData>(configPath);
        }

        public static int GetNearSize(int n)
        {
            float r = math.log(n) / math.log(2);
            int t = (int)math.pow(2, math.floor(r)); 
            return t >= n ? t : (int)math.pow(2, math.floor(r)+1); 
        }

        public static Texture2D CombineTextureData<T>(List<T> matData) where T : IShaderBackUp
        {
            if (matData.Count == 0)
                return null;

            var tmp = matData[0].TransToTexData();
            var dataSize = (int)math.ceil(tmp.Length * 0.25);
            
            int w = GetNearSize(dataSize);
            int h = GetNearSize(matData.Count);
            Texture2D tex = new Texture2D(w, h, TextureFormat.RGBAHalf, false, true);
            Color[] pixels = tex.GetPixels();

            for (int i = 0; i < matData.Count; i++)
            {
                var data = matData[i];
                var pixelData = data.TransToTexData();
                var colors = TransArrToColorArr(pixelData);
                for (int j=0; j< colors.Length; j++)
                {
                    pixels[i * w + j] = colors[j];
                } 
            }

            tex.filterMode = FilterMode.Point;
            tex.SetPixels(pixels);
            tex.Apply();

            return tex;
        }

        private static Color[] TransArrToColorArr(float[] arr)
        {
            if (arr != null && arr.Length > 0)
            {
                int colorLength = (int)math.ceil(arr.Length * 0.25f);
                Color[] colorArr = new Color[colorLength];
                for (int i = 0; i < colorLength; i++)
                {
                    Color c = new Color();
                    int idx = i * 4;
                    c.r = idx < arr.Length ? arr[idx] : 0;
                    idx++;
                    c.g = idx < arr.Length ? arr[idx] : 0;
                    idx++;
                    c.b = idx < arr.Length ? arr[idx] : 0;
                    idx++;
                    c.a = idx < arr.Length ? arr[idx] : 0;
                    colorArr[i] = c;
                }

                return colorArr;
            }

            return null;
        }
        

        public static TextureData CombineTextureArray(Dictionary<string, Texture2D> textureDict, int textureArraySize)
        {
            int idx = 0;
            TextureFormat format = TextureFormat.ARGB32;
            FilterMode filterMode = FilterMode.Bilinear;
            TextureWrapMode wrapMode = TextureWrapMode.Clamp;
            var combineData = TextureData.Create();
            try
            {
                AssetDatabase.StartAssetEditing();
                foreach (var t in textureDict)
                {
                    if (idx == 0)
                    {
                        format = t.Value.format;
                        filterMode = t.Value.filterMode;
                        wrapMode = t.Value.wrapMode;
                    }
                    Debug.LogFormat("idx {0} t {1}", idx++, t.Key);
                    
                    combineData.InfoDict.Add(t.Value, new BackUpTexInfo(t.Value));
                    
                    string texPath = AssetDatabase.GetAssetPath(t.Value);
                    ModifySrcTexture(texPath, textureArraySize);
                }
            }
            finally 
            {
                AssetDatabase.StopAssetEditing();
                AssetDatabase.Refresh();
            }
            

            Texture2DArray texArray = new Texture2DArray(textureArraySize, textureArraySize, textureDict.Count,
                format, true, false);
            texArray.filterMode = filterMode;
            texArray.wrapMode = wrapMode;

            idx = 0;
            List<Texture2D> sortList = new List<Texture2D>();
            sortList.AddRange(textureDict.Values);
            sortList.Sort(SortTexture);
            foreach (var t in sortList)
            {
                combineData.SortList.Add(AssetDatabase.GetAssetPath(t));
            }
            foreach(var t in sortList)
            {
                for (int m = 0; m < t.mipmapCount; m++)
                {
                    Graphics.CopyTexture(t, 0, m, texArray, idx, m);
                }

                idx++;
            }
            texArray.Apply(false, true);
            combineData.Texture2DArray = texArray;
            
            

            try
            {
                AssetDatabase.StartAssetEditing();
                foreach (var t in combineData.InfoDict)
                {
                    string texPath = AssetDatabase.GetAssetPath(t.Key);
                    RecoverSrcTexture(texPath, t.Value.Size);
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
                AssetDatabase.Refresh();
            }

            return combineData;
        }

        static MaterialData<T> CombineMaterialData<T>(Dictionary<string, Material> materialDict, TextureData textureData) where T : IShaderBackUp
        {
            Texture2DArray texture2DArray = textureData.Texture2DArray;
            //TODO 合并属性相同的材质块
            var materialData = MaterialData<T>.Create();
            Dictionary<string, T> dict = new Dictionary<string, T>();
            foreach (var matKV in materialDict)
            {
                var data = Activator.CreateInstance<T>();
                data.BackUpMaterial(matKV.Value);
                data.SetTextureIndex(GetMainTextureInArrayIndexByMaterial(matKV.Value, textureData.SortList));
                materialData.InfoDict.TryAdd(matKV.Value, data);
            }

            var list = materialDict.Values.ToList();
            list.Sort(SortMat);
            foreach (var mat in list)
            {
                var matPath = AssetDatabase.GetAssetPath(mat);
                materialData.MaterialSortList.Add(matPath);
                materialData.BlockBackUpList.Add((T)materialData.InfoDict[mat]);
            }

            //创建材质
            Material combineMat = new Material(Shader.Find("Sultan/Scene/BuildUnLit_MeshCombine"));
            // combineMat.EnableKeyword("COMBINE_MESH_ON");
            // combineMat.SetInt("COMBINE_MESH_ON", 1);
            // combineMat.EnableKeyword("_ALPHATEST_ON");
            combineMat.SetFloat("_Cutoff",0.5f);
            combineMat.SetTexture("_BaseMapArray", texture2DArray);
            // combineMat.EnableKeyword("_SHADOW_MODDEL_BILLBOARD");
            combineMat.SetInt("_SHADOW_MODDEL_BILLBOARD", 1);
            // combineMat.EnableKeyword("_RECEIVE_SHADOWS");
            // combineMat.SetInt("_RECEIVE_SHADOWS", 1);
            combineMat.SetVector("_AnchorPosition", new Vector4(0,0,-0.6f, -0.1f));
            // combineMat.EnableKeyword("PALETTE_COLOR_ON");
            // combineMat.SetInt("PALETTE_COLOR_ON", 1);
            combineMat.enableInstancing = true;

            combineMat.renderQueue = (int)RenderQueue.AlphaTest;
            
            foreach (var keyword in ShaderVariantsStrip.AlwaysNoUsedKeyWord)
            {
                combineMat.DisableKeyword(keyword.name);
            }

            materialData.Material = combineMat;
            return materialData;
        }
        
        public static int GetMainTextureInArrayIndexByMaterial(Material mat, List<string> textureList)
        {
            var mainTexture = mat.GetTexture("_BaseMap");
            if (!mainTexture)
            {
                Debug.Log("Missing BaseMap" + mat.name);
                return -1;
            }

            var path = AssetDatabase.GetAssetPath(mainTexture);
            int idx = System.Array.IndexOf(textureList.ToArray(), path);
            return idx;
        }

        public static int GetMaterialBlockIndexByMaterial(Material mat, List<string> backUpMaterialList)
        {
            var path = AssetDatabase.GetAssetPath(mat);
            int idx = System.Array.IndexOf(backUpMaterialList.ToArray(), path);
            return idx;
        }

        
        static void  ModifySrcTexture(string texPath, int Size)
        {

            string[] PlatformsStr = {"Android", "iPhone"};
           
            TextureImporter ti = (TextureImporter)AssetImporter.GetAtPath(texPath);
            if (!ti.isReadable)
            {
                ti.compressionQuality = (int)TextureCompressionQuality.Normal;
                foreach (var Platform in PlatformsStr)
                {
                    TextureImporterPlatformSettings settings = new TextureImporterPlatformSettings();
                    settings.name = Platform;
                    settings.overridden = true;
                    settings.maxTextureSize = Size;
                    settings.format = TextureImporterFormat.ASTC_6x6;
                    settings.compressionQuality = (int)TextureCompressionQuality.Normal;
                    
                    ti.SetPlatformTextureSettings(settings);
                }
                ti.isReadable = true;
                ti.SaveAndReimport();
            }
        } 
        
        static void RecoverSrcTexture(string texPath, int Size)
        {

            string[] PlatformsStr = {"Android", "iPhone"};
           
            TextureImporter ti = (TextureImporter)AssetImporter.GetAtPath(texPath);
            if (ti.isReadable)
            {
                ti.compressionQuality = (int)TextureCompressionQuality.Normal;
                foreach (var Platform in PlatformsStr)
                {
                    TextureImporterPlatformSettings settings = new TextureImporterPlatformSettings();
                    settings.name = Platform;
                    settings.overridden = true;
                    settings.maxTextureSize = Size;
                    settings.format = TextureImporterFormat.ASTC_6x6;
                    settings.compressionQuality = (int)TextureCompressionQuality.Normal;
                    
                    ti.SetPlatformTextureSettings(settings);
                }
                ti.isReadable = false;
                ti.SaveAndReimport();
            }

        }
        #endregion


        #region Mesh

        
        public static Mesh CombineMesh(GameObject root, CombineMaterialData materialData)
        {
            if (root == null || materialData == null)
                return null;
            

            List<MeshData> CollectList = new List<MeshData>();

            CollectMeshData(root, BUILD_UNLIT_SHAER_NAME, ref CollectList);
            var finalMesh = CombineMesh(CollectList, materialData);
            return finalMesh;
        }

        public static void CollectMeshData(GameObject root, ref List<MeshData> CollectList)
        {
            CollectMeshData(root,  BUILD_UNLIT_SHAER_NAME, ref CollectList);
        }
        
        public static void CollectMeshData(GameObject root, string ShaderName, ref List<MeshData> CollectList)
        {
            // List<MeshData> CollectList = new List<MeshData>();
            MeshFilter[] mfs = root.GetComponentsInChildren<MeshFilter>();

            foreach (var mf in mfs)
            {
                if (!mf.sharedMesh) continue;
                
                GameObject curObj = mf.gameObject;
                MeshRenderer mr = curObj.GetComponent<MeshRenderer>();

                if (mr.sharedMaterial && mr.sharedMaterial.shader.name == ShaderName)
                {
                    var scale = curObj.transform.lossyScale;
                    if (!scale.x.Equals(scale.z))
                    {
                        Log.Warn($"{curObj.name} scale error , scale xz must equal");
                    }
                    CollectList.Add(new MeshData()
                    {
                        Mesh = mf.sharedMesh,
                        Material = mr.sharedMaterial,
                        LocalToWorld = mr.localToWorldMatrix,
                        scale = curObj.transform.lossyScale
                    });
                }
            }
        }

        public static Mesh CombineMesh(List<MeshData> meshList, CombineMaterialData materialData)
        {
            if (meshList == null || meshList.Count == 0 || materialData == null)
                return null;
            
            List<Vector4> vertexList = new List<Vector4>();
            List<Vector2> uvList = new List<Vector2>();
            List<Color> colorList = new List<Color>();
            List<ushort> triangleList = new List<ushort>();
            List<Vector2Int> indexList = new List<Vector2Int>();
            List<Vector2> shadowDataList = new List<Vector2>();
            List<Vector4> transList = new List<Vector4>();
            
            int triOffset = 0;
            float maxX = float.MinValue, maxY = float.MinValue, maxZ = float.MinValue;
            float minX = float.MaxValue, minY = float.MaxValue, minZ = float.MaxValue;
            int MaxTextureCount = 256;

            bool hasColor = false;
            for (int i = 0; i < meshList.Count; i++)
            {
                if (meshList[i].Mesh.HasVertexAttribute(VertexAttribute.Color))
                {
                    hasColor = true;
                    break;
                }
            }
            hasColor = false;
 
            for (int i = 0; i < meshList.Count; i++)
            {
                Mesh curMesh = meshList[i].Mesh;
                Material curMaterial = meshList[i].Material;
                // int mainTextureIndex = GetMainTextureInArrayIndexByMaterial(curMaterial, materialData.TextureList);
                // if (mainTextureIndex == -1)
                // {
                //     Debug.LogError("Find Combined Tex index faild:" + curMesh.name);
                //     continue;
                // }

                int blockIdx = GetMaterialBlockIndexByMaterial(curMaterial, materialData.BackUpMaterialList);
                if (blockIdx == -1)
                    blockIdx = 0;
                
                bool shadowCast = curMaterial.GetShaderPassEnabled("CSMShadowCaster");

                //vertex
                List<Vector3> curVertexList = new List<Vector3>();
                curMesh.GetVertices(curVertexList);

                //uv
                List<Vector2> curUVList = new List<Vector2>();
                curMesh.GetUVs(0, curUVList);
                
                //color
                List<Color> curColorList = new List<Color>();
                curMesh.GetColors(curColorList);
                
                //tri
                List<int> curTriangle = new List<int>();
                curMesh.GetTriangles(curTriangle, 0);
                
                //collect
                uvList.AddRange(curUVList);
                colorList.AddRange(curColorList);

                var trans = meshList[i].LocalToWorld.GetPosition();
                var scale = meshList[i].scale.x;
                var transNode = new Vector4(trans.x, trans.y, trans.z, scale); 
                for (int j = 0; j < curVertexList.Count; j++)
                {
                    transList.Add(transNode);
                }
                
                Matrix4x4 local2World = meshList[i].LocalToWorld;
                for (int v = 0; v < curVertexList.Count; v++)
                {
                    Vector3 localPos = curVertexList[v];
                    Vector3 worldPos = local2World * new Vector4(localPos.x, localPos.y, localPos.z, 1);

                    maxX = Mathf.Max(maxX, worldPos.x);
                    maxY = Mathf.Max(maxY, worldPos.y);
                    maxZ = Mathf.Max(maxZ, worldPos.z);
                    
                    minX = Mathf.Min(minX, worldPos.x);
                    minY = Mathf.Min(minY, worldPos.y);
                    minZ = Mathf.Min(minZ, worldPos.z);
                    
                    shadowDataList.Add(new Vector2(shadowCast ? 1 : 0, 0));
                    // float combineIdx = (float)(blockIdx * 256 + mainTextureIndex) / 65536;
                    // indexList.Add(new Vector2Int(blockIdx, mainTextureIndex));
                    // vertexList.Add(new Vector4(worldPos.x, worldPos.y, worldPos.z, mainTextureIndex));
                    // vertexList.Add(new Vector4(worldPos.x, worldPos.y, worldPos.z, combineIdx));
                    vertexList.Add(new Vector4(worldPos.x, worldPos.y, worldPos.z, blockIdx));
                }

                
                for (int t = 0; t < curTriangle.Count; t++)
                {
                    triangleList.Add((ushort)(curTriangle[t] + triOffset));
                }
                triOffset += curVertexList.Count;
            }

            VertexAttributeDescriptor[] layout = null;
            if (hasColor)
            {
                layout = new[]
                {
                    new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float16, 4),
                    new VertexAttributeDescriptor(VertexAttribute.Color, VertexAttributeFormat.Float16, 4),
                    new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float16, 2),
                    new VertexAttributeDescriptor(VertexAttribute.TexCoord1, VertexAttributeFormat.Float16, 4),
                };
            }
            else
            {
                layout = new[]
                {
                    new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float16, 4),
                    new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float16, 2),
                    new VertexAttributeDescriptor(VertexAttribute.TexCoord1, VertexAttributeFormat.Float16, 4),
                    new VertexAttributeDescriptor(VertexAttribute.TexCoord2, VertexAttributeFormat.Float16, 2),
                };
            }
            Mesh finalMesh = new Mesh();
            finalMesh.SetVertexBufferParams(vertexList.Count, layout);

            //先不处理顶点色
            if (hasColor)
            {
                NativeArray<ColorCustomVertex> finalVertexList = new NativeArray<ColorCustomVertex>(vertexList.Count, Allocator.Persistent);
                for (int i = 0; i < vertexList.Count; i++)
                {
                    ColorCustomVertex vertex = new ColorCustomVertex();
                    vertex.posX = new half(vertexList[i].x);
                    vertex.posY = new half(vertexList[i].y);
                    vertex.posZ = new half(vertexList[i].z);
                    vertex.posW = new half(vertexList[i].w);
                    
                    vertex.R = (half)colorList[i].r;
                    vertex.G = (half)colorList[i].g;
                    vertex.B = (half)colorList[i].b;
                    vertex.A = (half)colorList[i].a;

                    vertex.uvX = new half(uvList[i].x);
                    vertex.uvY = new half(uvList[i].y);

                    vertex.transX = new half(transList[i].x);
                    vertex.transY = new half(transList[i].y);
                    vertex.transZ = new half(transList[i].z);
                    vertex.transW = new half(0);//new half(transList[i].z);

                    finalVertexList[i] = vertex;
                }    
                finalMesh.SetVertexBufferData(finalVertexList, 0, 0, finalVertexList.Length);
                finalVertexList.Dispose();
            }
            else
            {
                NativeArray<CustomVertex> finalVertexList = new NativeArray<CustomVertex>(vertexList.Count, Allocator.Persistent);
                for (int i = 0; i < vertexList.Count; i++)
                {
                    CustomVertex vertex = new CustomVertex();
                    vertex.posX = new half(vertexList[i].x);
                    vertex.posY = new half(vertexList[i].y);
                    vertex.posZ = new half(vertexList[i].z);
                    vertex.posW = new half(vertexList[i].w);

                    vertex.uvX = new half(uvList[i].x);
                    vertex.uvY = new half(uvList[i].y);
                    vertex.shadowCast = new half(shadowDataList[i].x);
                    vertex.unused = new half(shadowDataList[i].y);
                    
                    vertex.transX = new half(transList[i].x);
                    vertex.transY = new half(transList[i].y);
                    vertex.transZ = new half(transList[i].z);
                    vertex.scaleXZ = new half(transList[i].w);//new half(transList[i].z);

                    finalVertexList[i] = vertex;
                }    
                finalMesh.SetVertexBufferData(finalVertexList, 0, 0, finalVertexList.Length);
                finalVertexList.Dispose();
            }
            
            finalMesh.SetIndexBufferParams(triangleList.Count, IndexFormat.UInt16);
            finalMesh.SetIndexBufferData(triangleList.ToArray(), 0, 0, triangleList.Count);
           
            finalMesh.subMeshCount = 1;
            Bounds finalMeshBound = new Bounds();
            finalMeshBound.SetMinMax(new Vector3(minX, minY, minZ), new Vector3(maxX, maxY, maxZ));
            var subMeshDesc = new SubMeshDescriptor(0, triangleList.Count, MeshTopology.Triangles);
            subMeshDesc.bounds = finalMeshBound;
            finalMesh.SetSubMesh(0, subMeshDesc);
            finalMesh.bounds = finalMeshBound;
            finalMesh.RecalculateUVDistributionMetrics();
            finalMesh.UploadMeshData(true);
            
            return finalMesh;
        }
        #endregion
        public static int SortMat(Material A, Material B)
        {
            return A.GetHashCode() < B.GetHashCode() ? -1 : 1;
        }

        public static int SortShader(Shader A, Shader B)
        {
            return A.GetHashCode() < B.GetHashCode() ? -1 : 1;
        }

        public static int SortTexture(Texture2D A, Texture2D B)
        {
            return A.GetHashCode() < B.GetHashCode() ? -1 : 1;
        }

        public static void CreateDir(string path, string dirName)
        {
            if (!AssetDatabase.IsValidFolder(Path.Combine(path, dirName)))
            {
                AssetDatabase.CreateFolder(path, dirName);
            }
        }
        

    }
}
#endif