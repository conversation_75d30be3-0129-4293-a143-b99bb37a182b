using System;
using System.Collections.Generic;
using Sultan.BuildTools;
using UnityEngine;

namespace Sultan.Tools
{
    public enum CombineRuleType : byte
    {
        Default,
        Regex,
    }
    
    [Serializable]
    public class CombineConfig
    {
        public CombineRuleType RuleType;
    }

    [Serializable]
    public class CustomCombineConfig : CombineConfig
    {
        public List<string> CombineNodes;
        public GameObject Prefab;
        public List<string> MaterialWorkPath;
        public int TextureArraySize = 256;
        public CombineOutputSetting DefaultOutputSetting;
    }


    
    
    
    [CreateAssetMenu(menuName = "Sultan/Combine/Create CombineSetting")]
    public class MeshCombineSetting : ScriptableObject, IProcessingPipelineContent
    {
        [Header("合并导出路径")]
        public string CombineSavePath;
        
        [Header("参与合并的材质球路径")]
        public List<string> MaterialWorkPath;

        [Header("合并材质筛选")] public List<string> regexRule;
        
        [Header("参与合并的材质球")]
        public List<Material> Materials;
        [Header("Texture2DArrray纹理大小")]
        public int TextureArraySize = 256;

        // public CombineOutputSetting DefaultOutputSetting;
        // public List<CombineConfig> CombineConfigs;
        // public List<CustomCombineConfig> CustomCombineConfigs;

        [Header("建筑合并配置")]
        public List<Sultan.Tools.CombineBuildConfig> BuildPathList;
    }
    
    
}