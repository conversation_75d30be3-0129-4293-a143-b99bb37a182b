//file: MeshIndexBufferGenPipe.cs
//Author: Aoicocoon
//Date: 2024-10-18 10:42

using Sultan.BuildTools;
using UnityEditor;
using UnityEditor.Formats.Fbx.Exporter;
using UnityEngine;

namespace Sultan.ToolsPipe
{
    [ProcessingPipeline("CombineTools", "建筑减面", 2)]
    public class MeshIndexBufferGenPipe : IProcessingPipeline
    {
        public void OnEnable(IProcessingPipelineContent content)
        {
           
        }

        public void Start()
        {
            //return;
            Debug.Log("Start WOrk");
            ExtractMeshToFBX.MenuExtractToFBX();
            AssetDatabase.Refresh();
            //ExtractMeshToFBX.ClearSMRMATS();
        }

        public void OnGUI()
        {
            
        }

        public void Clear()
        {
            
        }
    }
}