using System;
using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.IO;
using UnityEditor.Formats.Fbx.Exporter;

// using Unity.Formats.Fbx.Editor;

namespace Sultan.Test.Editor
{
    public class RemoveMeshChannelsWindow : EditorWindow
    {
        ExportModelOptions _exportModelOptions = new ExportModelOptions();
        private bool removeVertexColors = false;
        private bool removeUV2 = false;
        private bool removeUV3 = false;
        private bool removeUV4 = false;

        [MenuItem("Assets/TA Tools/Remove Mesh Channels", true)]
        private static bool ValidateRemoveMeshChannels()
        {
            // 验证所有选中对象是否都是FBX模型
            if (Selection.objects.Length == 0) return false;

            foreach (var obj in Selection.objects)
            {
                if (obj.GetType() != typeof(GameObject)) return false;
                string path = AssetDatabase.GetAssetPath(obj);
                if (!path.ToLower().EndsWith(".fbx")) return false;
            }

            return true;
        }

        [MenuItem("Assets/TA Tools/Remove Mesh Channels", false, 30)]
        private static void OpenWindow()
        {
            var window = GetWindow<RemoveMeshChannelsWindow>();
            window.titleContent = new GUIContent("Remove Mesh Channels");
            window.minSize = new Vector2(260f, 220f);
            window.maxSize = window.minSize;
            window.Show();
        }

        void OnGUI()
        {
            GUILayout.Label($"Processing {Selection.objects.Length} FBX Files:", EditorStyles.boldLabel);
            EditorGUILayout.Space(20);
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();

            EditorGUILayout.BeginVertical();
            removeUV2 = EditorGUILayout.ToggleLeft("UV2", removeUV2);
            removeUV3 = EditorGUILayout.ToggleLeft("UV3", removeUV3);
            removeUV4 = EditorGUILayout.ToggleLeft("UV4", removeUV4);
            removeVertexColors = EditorGUILayout.ToggleLeft("Vertex Colors", removeVertexColors);
            EditorGUILayout.EndVertical();

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space(20);
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            if (GUILayout.Button("Apply", GUILayout.MaxWidth(180f), GUILayout.MaxHeight(50f)))
            {
                ApplyChanges();
                Close();
            }

            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();
        }

        private void ApplyChanges()
        {
            try
            {
                int total = Selection.objects.Length;
                for (int i = 0; i < total; i++)
                {
                    var obj = Selection.objects[i];
                    // 更新进度条
                    EditorUtility.DisplayProgressBar(
                        "Processing FBX Files",
                        $"Processing: {obj.name} ({i + 1}/{total})",
                        (float)i / total
                    );

                    ProcessSingleFBX(obj as GameObject);
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.Refresh();
            }
        }

        private void ProcessSingleFBX(GameObject originalModel)
        {
            if (originalModel == null) return;

            try
            {
                string assetPath = AssetDatabase.GetAssetPath(originalModel);
                string fullPath = Path.GetFullPath(assetPath);

                // 实例化模型
                GameObject instance = Instantiate(originalModel);
                instance.hideFlags = HideFlags.HideAndDontSave;

                // 处理网格
                List<Mesh> modifiedMeshes = ProcessMeshesInHierarchy(instance);

                // 导出FBX
                ExportModelToFBX(instance, fullPath);

                // 清理资源
                DestroyImmediate(instance);
                foreach (var mesh in modifiedMeshes)
                {
                    DestroyImmediate(mesh);
                }

                Debug.Log($"Success: {assetPath}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to process {originalModel.name}: {e.Message}");
            }
        }

        // 以下方法保持原有实现不变
        private List<Mesh> ProcessMeshesInHierarchy(GameObject root)
        {
            List<Mesh> modifiedMeshes = new List<Mesh>();
            foreach (var renderer in root.GetComponentsInChildren<SkinnedMeshRenderer>())
            {
                Mesh mesh = ProcessMesh(renderer.sharedMesh);
                renderer.sharedMesh = mesh;
                modifiedMeshes.Add(mesh);
            }

            foreach (var filter in root.GetComponentsInChildren<MeshFilter>())
            {
                Mesh mesh = ProcessMesh(filter.sharedMesh);
                filter.sharedMesh = mesh;
                modifiedMeshes.Add(mesh);
            }

            return modifiedMeshes;
        }

        private Mesh ProcessMesh(Mesh originalMesh)
        {
            Mesh newMesh = Instantiate(originalMesh);
            newMesh.name = originalMesh.name;

            if (removeVertexColors) newMesh.colors = null;
            HandleUVChannels(newMesh, removeUV2, removeUV3, removeUV4);
            newMesh.UploadMeshData(true);
            return newMesh;
        }

        private void HandleUVChannels(Mesh mesh, bool uv2, bool uv3, bool uv4)
        {
            List<Vector2>[] uvs = new List<Vector2>[4];
            for (int i = 0; i < 4; i++)
            {
                uvs[i] = new List<Vector2>();
                mesh.GetUVs(i, uvs[i]);
                if ((i == 1 && uv2) || (i == 2 && uv3) || (i == 3 && uv4))
                    uvs[i].Clear();
                mesh.SetUVs(i, uvs[i]);
            }
        }

        private void ExportModelToFBX(GameObject model, string exportPath)
        {
            _exportModelOptions.UseMayaCompatibleNames = false;
            Directory.CreateDirectory(Path.GetDirectoryName(exportPath) ?? string.Empty);
            ModelExporter.ExportObject(exportPath, model , _exportModelOptions);
        }
        
    }
    
}
