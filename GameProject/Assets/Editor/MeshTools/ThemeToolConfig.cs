using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using NUnit.Framework;
using Rendering.CustomRender;
using Sirenix.OdinInspector;
using Sultan.BuildTools;
using Sultan.Core;
using Sultan.Render;
using Unity.Mathematics;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

namespace Sultan.Tools
{

    
    [CreateAssetMenu(menuName = "Sultan/Combine/Create ThemeToolConfig")]
    public class ThemeToolConfig: ScriptableObject
    {
        [Header("主题资源路径")]
        public string ThemeSourcePath;
        [Header("主题导出路径")]
        public string ThemeOutputPath;
        [Header("主题预制路径")]
        public string ThemePrefabPath;

        [Header("通用网格")] public Mesh ThemeMesh;
        
        [Header("Texture2DArrray纹理大小")]
        public int TextureArraySize = 256;


        private IEnumerable ShowThemeList
        {
            get
            {
                if(_ThemeList == null)
                    RefreshTheme();
                return _ThemeList;
            }
        }

        public ValueDropdownList<string> _ThemeList;
        
        [ValueDropdown("ShowThemeList"), LabelText("主题列表")]
        public string m_SelectTheme;


        [HideInInspector]
        public List<string> ThemeKeys = new List<string>();
        [Header("主题配置")]
        public List<ThemeRenderConfig> ThemeConfigs = new List<ThemeRenderConfig>();
        
        
        // public Dictionary<string, ThemeRenderConfig> ThemeConfigs = new Dictionary<string, ThemeRenderConfig>();

        
        public static bool GetThemeName(string fileName, out string themeName)
        {
            themeName = "";
            if (!fileName.StartsWith("adorn_theme"))
                return false;

            var fixFileName = fileName.Substring(6);
            var ext = Path.GetExtension(fixFileName);
            fixFileName = fixFileName.Replace(ext, "");
            var idx = fixFileName.IndexOf("_");

            themeName = fixFileName.Substring(0, idx);
            return true;
        }
        
        [Button("刷新主题列表")]
        public void RefreshTheme()
        {
            if (_ThemeList == null)
            {
                _ThemeList = new ValueDropdownList<string>();
            }
            _ThemeList.Clear();
            _ThemeList.Add("全部主题");
            int i = 0;
            var guids = AssetDatabase.FindAssets("t:Material", new []{ThemeSourcePath});
            HashSet<string> themeSet = new HashSet<string>();
            foreach (var guid in guids)
            {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var fileName = Path.GetFileName(path);
                if (GetThemeName(fileName, out var themeName))
                {
                    themeSet.Add(themeName);
                }
            }

            foreach (var themeName in themeSet)
            {
                _ThemeList.Add(themeName);
            }
        }

        private void ExecuteThemeAction(Action<string> action)
        {
            if(action == null)
                return;
            if (m_SelectTheme == _ThemeList[0].Value)
            {
                for (int i = 1; i < _ThemeList.Count; i++)
                {
                    action(_ThemeList[i].Value);
                }
            }
            else
            {
                action(m_SelectTheme);
            }
        }

        [Button("合并主题贴图")]
        public void CombineThemeTexture()
        {
            ExecuteThemeAction(CombineThemeTextureArray);
            AssetDatabase.SaveAssetIfDirty(this);
        }
        
        [Button("合并主题材质数据")]
        public void CombineThemeData()
        {
            ExecuteThemeAction(CombineThemeMaterials);
            AssetDatabase.SaveAssetIfDirty(this);
        }

        [Button("导出主题预制")]
        public void ExportThemePrefab()
        {
            ExecuteThemeAction(ExportThemePrefab);
        }

        [Button("更新主题预制")]
        public void FixThemePrefab()
        {
            ExecuteThemeAction(FixThemePrefab);
        }
        

        public bool GetThemeData(string themeName, bool autoCreate, out ThemeRenderConfig config)
        {
            config = null;
            if (ThemeKeys.Contains(themeName))
            {
                config = ThemeConfigs[ThemeKeys.IndexOf(themeName)];
            }
            else if(autoCreate)
            {
                config = new ThemeRenderConfig();
                config.ThemeName = themeName;
                ThemeConfigs.Add(config);
                ThemeKeys.Add(themeName);
            }
            else
                return false;

            return true;
        }

        public void CombineThemeTextureArray(string themeName)
        {
            GetThemeData(themeName, true, out var themeConfig);
            Dictionary<string, Texture2D> packedTextureDict = new Dictionary<string, Texture2D>();
            Dictionary<int, Dictionary<string, Texture2D>> splitDict = new Dictionary<int, Dictionary<string, Texture2D>>();
            var guids = AssetDatabase.FindAssets($"t:Material {themeName}", new []{ThemeSourcePath});
            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                Material mat = AssetDatabase.LoadAssetAtPath<Material>(path);
                if (mat && mat.shader.name == "Sultan/Scene/BuildUnLit")
                {
                    var mainTexture = mat.GetTexture("_BaseMap");
                    if (mainTexture)
                    {
                        string texName = mainTexture.name;
                        int size = mainTexture.width;
                        var idx = ThemeRenderConfig.TransTextureSizeToIndex(size);
                        if (!splitDict.TryGetValue(idx, out var dict))
                        {
                            dict = new Dictionary<string, Texture2D>();
                            dict.Add(texName, mainTexture as Texture2D);
                            splitDict.Add(idx, dict);
                        }
                        else
                            dict.Add(texName, mainTexture as Texture2D);
                    }
                }
            }

            foreach (var dict in splitDict)
            {
                var subConfig = themeConfig.SubThemeRenderList[dict.Key];
                _CombineThemeTextureArray(dict.Value, themeName, subConfig);
            }
        }

        private void _CombineThemeTextureArray(Dictionary<string, Texture2D> packedTextureDict,
            string themeName, SubThemeRenderConfig subRenderConfig)
        {
           // Dictionary<string, Texture2D> packedTextureDict = new Dictionary<string, Texture2D>();
           var size = subRenderConfig.TextureSize;
            var textureData = CombineHelper.CombineTextureArray(packedTextureDict, size);
            subRenderConfig.textureList = textureData.SortList;
            var texture2DArrayPath = $"{ThemeOutputPath}/textures/{themeName}_{size}.asset";
            CombineHelper.CreateDir(ThemeOutputPath, "textures");
            AssetDatabase.CreateAsset(textureData.Texture2DArray, texture2DArrayPath);
            AssetDatabase.Refresh();
            subRenderConfig.Texture2DArray = AssetDatabase.LoadAssetAtPath<Texture>(texture2DArrayPath);
            
            var matPath = $"{ThemeOutputPath}/materials/{themeName}_{size}.mat";
            var combineMat = AssetDatabase.LoadAssetAtPath<Material>(matPath);
            if (combineMat == null)
            {
                combineMat = new Material(Shader.Find("Sultan/Scene/BuildCombineInstance"));           
                combineMat.SetTexture("_BaseMapArray", subRenderConfig.Texture2DArray);
                combineMat.enableInstancing = true;
                combineMat.renderQueue = (int)RenderQueue.AlphaTest;
                CombineHelper.CreateDir(ThemeOutputPath, "materials");
                AssetDatabase.CreateAsset(combineMat, matPath);
                AssetDatabase.Refresh();     
                combineMat = AssetDatabase.LoadAssetAtPath<Material>(matPath);
            }
            else
            {
                combineMat.SetTexture("_BaseMapArray", subRenderConfig.Texture2DArray);
            }

            subRenderConfig.CombineMaterial = combineMat;
            AssetDatabase.SaveAssetIfDirty(combineMat); 
        }
        

        public void CombineThemeMaterials(string themeName)
        {
            if (!GetThemeData(themeName, false, out var themeConfig))
            {
                Debug.LogError("未找到对应主题的配置信息");
                return;
            }

            themeConfig.Clear();
            
            var guids = AssetDatabase.FindAssets($"t:Material {themeName}", new []{ThemeSourcePath});
            Dictionary<string, Material> matDict = new Dictionary<string, Material>();

            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                Material mat = AssetDatabase.LoadAssetAtPath<Material>(path);
                if (mat && mat.shader.name == "Sultan/Scene/BuildUnLit")
                {
                    matDict.TryAdd(path, mat);
                }
            }
            
            foreach (var matKV in matDict)
            {
                ThemeInstanceProp prop = new ThemeInstanceProp();
                prop.BackUpMaterialProp(matKV.Value);
                var tex = matKV.Value.GetTexture("_BaseMap");
                prop.ThemeName = themeName;
                prop.TextureGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(tex)); 
                prop.MatGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(matKV.Value)); 
                prop.TextureArrayIdx = GetMainTextureInArrayIndexByMaterial(matKV.Value, themeConfig, out int size);
                prop.TextureSize = size;
                themeConfig.AddMatPropCfg(matKV.Key, size,  prop);
            }
        }
        
        private int GetMainTextureInArrayIndexByMaterial(Material mat, ThemeRenderConfig renderConfig, out int size)
        {
            var mainTexture = mat.GetTexture("_BaseMap");
            if (!mainTexture)
            {
                Debug.Log("Missing BaseMap" + mat.name);
                size = 0;
                return -1;
            }

            size = mainTexture.width;
            var subConfig = renderConfig.GetSubConfig(mainTexture.width); 
            var path = AssetDatabase.GetAssetPath(mainTexture);
            int idx = System.Array.IndexOf(subConfig.textureList.ToArray(), path);
            return idx;
        }

        private void ExportThemePrefab(string themeName)
        {
            // List<string> targetList = new List<string>();
            CombineHelper.CreateDir(ThemeOutputPath, "prefabs");
            try
            {
                AssetDatabase.StartAssetEditing();
                var guids = AssetDatabase.FindAssets($"t:Prefab {themeName}", new[] { ThemePrefabPath });

                foreach (var guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    {
                        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                        if (!prefab)
                            continue;

                        var savePath = $"{ThemeOutputPath}/prefabs/{prefab.name}.prefab";
                        bool isCopy = AssetDatabase.CopyAsset(path, savePath);
                    }
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
                AssetDatabase.Refresh();
            }
        }

        private void FixThemePrefab(string themeName)
        {
            try
            {
                AssetDatabase.StartAssetEditing();
                var guids = AssetDatabase.FindAssets($"t:Prefab {themeName}", new[] { $"{ThemeOutputPath}/prefabs" });

                foreach (var guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    var prefabObj = PrefabUtility.LoadPrefabContents(path);
                    if(prefabObj == null)
                        continue;

                    var staticObj = prefabObj.transform.FindEx("static");
                    if (staticObj != null)
                    {
                        var renderers = staticObj.GetComponentsInChildren<Renderer>(); 
                        foreach (var renderer in renderers)
                        {
                            // if (PrefabUtility.IsPartOfAnyPrefab(renderer.gameObject))
                            // {
                            //     var testRoot = PrefabUtility.GetNearestPrefabInstanceRoot(renderer.gameObject);
                            //     PrefabUtility.UnpackPrefabInstance(testRoot, PrefabUnpackMode.OutermostRoot, InteractionMode.AutomatedAction);
                            // }

                            var matPath = AssetDatabase.GetAssetPath(renderer.sharedMaterial);
                            bool bfind = false;
                            ThemeInstanceProp prop = default;
                            SubThemeRenderConfig curCfg = null;
                            var com = renderer.gameObject.GetComponent<ThemeInstanceComponent>();

                            //已有渲染组件，更新材质属性
                            if (com != null)
                            {
                                matPath = AssetDatabase.GUIDToAssetPath(com.Prop.MatGuid);
                            }
                            
                            foreach (var cfg in ThemeConfigs)
                            {
                                if(cfg.GetMaterialProp(matPath, out curCfg, out prop))
                                {
                                    bfind = true;
                                    break;
                                }
                            }

                            if (bfind)
                            {
                                renderer.sharedMaterial = curCfg.CombineMaterial;
                                if(com == null)
                                    com = renderer.gameObject.AddComponent<ThemeInstanceComponent>();
                                com.Prop = prop;

                                var meshFilter = renderer.GetComponent<MeshFilter>();
                                if (meshFilter)
                                    meshFilter.sharedMesh = ThemeMesh;
                                renderer.gameObject.layer = LayerMask.NameToLayer("PreDepth");
                            }

                        }
                    }
                    PrefabUtility.SaveAsPrefabAsset(prefabObj, path);
                    PrefabUtility.UnloadPrefabContents(prefabObj);
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
                AssetDatabase.Refresh();
                
            }
        }
    }
}