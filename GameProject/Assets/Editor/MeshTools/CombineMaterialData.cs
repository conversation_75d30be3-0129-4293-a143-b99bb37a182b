using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace Sultan.Tools
{
    public interface IShaderBackUp
    {
        public void BackUpMaterial(Material mat);
        public float[] TransToTexData();

        public void SetTextureIndex(int idx);

    }
    
    [Serializable]
    public struct BuildUnLitBackUp : IShaderBackUp
    {
        public int TextureArrayIdx;
        //PALETE_COLOE
        public float Saturation;
        public float Contrast;
        public float Brightness;

        //ALPHA_TEST
        public bool AlphaTestOn;
        public float CuteOff;
            
        //SHADOW
        public bool ReceiveShadow;
        public bool BillboardShadow;
        public Vector4 AnchorPosition;
        public float ShadowFix;

        public bool ShadowCasterOn;
            
        public void BackUpMaterial(Material mat) 
        {
            if (mat.IsKeywordEnabled("PALETTE_COLOR_ON"))
            {
                Saturation = mat.GetFloat("_Saturation");
                Contrast = mat.GetFloat("_Contrast");
                Brightness = mat.GetFloat("_Brightness");
            }
            else
            {
                Brightness = 1.0f;
                Contrast = 1.0f;
                Saturation = 1.0f;
            }
               
            AnchorPosition = mat.GetVector("_AnchorPosition");
            
            ReceiveShadow = !mat.IsKeywordEnabled("_RECEIVE_SHADOWS_OFF");
            ShadowFix = ReceiveShadow ? mat.GetFloat("_shadowFix") : 0.0f;
            // ShadowFix = mat.GetFloat("_shadowFix");
            BillboardShadow = mat.IsKeywordEnabled("_SHADOW_MODDEL_BILLBOARD");

            CuteOff = mat.GetFloat("_Cutoff");

            ShadowCasterOn = mat.GetShaderPassEnabled("CSMShadowCaster");
        }

        public void SetTextureIndex(int idx)
        {
            TextureArrayIdx = idx;
        }
        
        public float[] TransToTexData()
        {
            return new float[]
            {
                TextureArrayIdx,
                ReceiveShadow ? 1.0f : 0.0f,
                BillboardShadow ? 1.0f : 0.0f,
                CuteOff,
                Saturation,
                Contrast,
                Brightness,
                ShadowFix,
                AnchorPosition.x ,
                AnchorPosition.y ,
                AnchorPosition.z ,
                AnchorPosition.w ,
            };
        }
    }
    
    public class CombineMaterialData : ScriptableObject
    {
        public Texture2DArray ArrayData;
        public Texture2D DataTexture;
        public Material CombineMaterial;
        public List<BuildUnLitBackUp> BackUpData;
        public List<string> BackUpMaterialList;
        public List<string> TextureList;

        [NonSerialized] public float[] BlockArray;
        [NonSerialized] public int BlockSize;

#if UNITY_EDITOR
        

#endif
    }

    
}