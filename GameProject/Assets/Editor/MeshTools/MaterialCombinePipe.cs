using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Sultan.BuildTools;
using Sultan.Tools;
using UnityEditor;
using UnityEngine;

namespace Sultan.ToolsPipe
{
    [ProcessingPipeline("CombineTools", "合并材质球", 0)]
    public class MaterialCombinePipe : IProcessingPipeline
    {
        private MeshCombineSetting m_ContentPipe = null;
        
        private List<Material> m_SortMat = new List<Material>();

        public void OnEnable(IProcessingPipelineContent content)
        {
            m_ContentPipe = content as MeshCombineSetting;
        }

        public void Start()
        {
            if(!m_ContentPipe)
                return;

             
            m_SortMat.Clear();
            Dictionary<string, Material> matDict = new Dictionary<string, Material>();
            if (m_ContentPipe.MaterialWorkPath.Count > 0)
            {
                var guids = AssetDatabase.FindAssets("t:Material", m_ContentPipe.MaterialWorkPath.ToArray());
                foreach (var gid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(gid);
                    foreach (var rule in m_ContentPipe.regexRule)
                    {
                        string fileName = System.IO.Path.GetFileName(path);
                        if (Regex.IsMatch(fileName, rule))
                        {
                            Material mat = AssetDatabase.LoadAssetAtPath<Material>(path);

                            if (mat && mat.shader.name == "Sultan/Scene/BuildUnLit")
                            {
                                m_SortMat.Add(mat);
                                matDict.TryAdd(path, mat);
                            }

                            break;
                        }
                    }
                
                }     
            }
            

            foreach (var mat in m_ContentPipe.Materials)
            {
                if (mat && mat.shader.name == "Sultan/Scene/BuildUnLit")
                {
                    m_SortMat.Add(mat);
                    string path = AssetDatabase.GetAssetPath(mat);
                    matDict.TryAdd(path, mat);
                }
            }

            CombineHelper.CombineMaterial(matDict.Values.ToList(), m_ContentPipe.CombineSavePath,m_ContentPipe.TextureArraySize);

            // m_SortMat.Sort(SortMat);
        }

        public void Clear()
        {
        }

        public void OnGUI()
        {
        }
    }
}