using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Sultan.BuildTools;
using Sultan.Core;
using Sultan.Tools;
using UnityEditor;
using UnityEngine;
using Console = System.Console;

namespace Sultan.ToolsPipe
{
    [ProcessingPipeline("CombineTools", "合并建筑网格", 1)]
    public class BuildCombinePipe : IProcessingPipeline
    {
        
        public static string S_DEFAULT_BUILD_PATH =
            "Assets/ArtTmp/Scenes/Prefabs/Buildcombined/";
        public CombineMaterialData m_CombineMaterialData;
        public List<string> m_BuildPathList;
        private MeshCombineSetting m_ContentPipe = null;
        private SerializedObject m_SerializedObject;
        private bool ExportAll = false;


        public void OnEnable(IProcessingPipelineContent content)
        {
            m_ContentPipe = content as MeshCombineSetting;
            ExportAll = true;
        }

        public void Start()
        {
            if (!m_CombineMaterialData)
                return;

            CombineBuild();
        }

        public void Clear()
        {
        }

        public void OnGUI()
        {
            m_CombineMaterialData = (CombineMaterialData)EditorGUILayout.ObjectField("合并材质球配置", m_CombineMaterialData, typeof(CombineMaterialData), false);
            ExportAll = EditorGUILayout.Toggle("强制导出预制", ExportAll);
        }

        void CombineBuild()
        {
            //Collect all prefabs
            Dictionary<string, string> meshDict = new Dictionary<string, string>();
            foreach (var buildConfig in m_ContentPipe.BuildPathList)
            {
                List<string> targetList = new List<string>();

                try
                {
                    AssetDatabase.StartAssetEditing();
                    bool regex = !buildConfig.RegexRule.Equals("");
                    var guids = AssetDatabase.FindAssets("t:Prefab", new []{buildConfig.SrcBuildPath});
                    meshDict.Clear();
                    foreach (var guid in guids)
                    {
                        string path = AssetDatabase.GUIDToAssetPath(guid);
                        if (regex && !Regex.IsMatch(path, buildConfig.RegexRule))
                            continue;

                        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                        if (!prefab)
                            continue;

                        var savePath = $"{buildConfig.ExportPath}/{prefab.name}.prefab";
                        AssetDatabase.CopyAsset(path, savePath);
                        targetList.Add(savePath);
                    }
                    AssetDatabase.StopAssetEditing();
                    AssetDatabase.Refresh();

                    AssetDatabase.StartAssetEditing();
                    //基于合并材质进行网格合并
                    foreach (var savePath in targetList)
                    {
                        // var copyed = AssetDatabase.LoadAssetAtPath<GameObject>(savePath);
                        var rootObj = PrefabUtility.LoadPrefabContents(savePath);
                        // using (var editingScope = new PrefabUtility.EditPrefabContentsScope(savePath))
                        {
                            // var copyed = editingScope.prefabContentsRoot;
                            var copyed = rootObj;
                            if(!copyed)
                                continue;
                            // copyed.name = prefab.name;
                        
                            bool saveDone = false;
                            List<CombineHelper.MeshData> meshList = new List<CombineHelper.MeshData>();
                            foreach (var nodeName in buildConfig.CombineNodeList)
                            {
                                bool find = false;
                                int i = 0;
                                do
                                {
                                    Transform node = copyed.transform.FindEx(nodeName);
                                    find = false;
                                    if (node)
                                    {
                                        find = true;
                                        if(PrefabUtility.IsPartOfAnyPrefab(node.gameObject))
                                        {
                                            var testRoot = PrefabUtility.GetNearestPrefabInstanceRoot(node.gameObject);
                                            PrefabUtility.UnpackPrefabInstance(testRoot, PrefabUnpackMode.OutermostRoot, InteractionMode.AutomatedAction);
                                        }
                                        CombineHelper.CollectMeshData(node.gameObject, ref meshList);
                                        // node.gameObject.transform.parent = null;
                                        //delete old node
                                        UnityEngine.Object.DestroyImmediate(node.gameObject);
                                    }

                                    i++;
                                } while (find && i < 10);
                            
                            }

                            var finalMesh = CombineHelper.CombineMesh(meshList, m_CombineMaterialData);
                            if (finalMesh)
                            {
                                var meshOutputPath =
                                    $"{m_ContentPipe.CombineSavePath}/mesh/{copyed.name}_{buildConfig.ExportNodeName}.asset"; 
                                meshDict.Add(savePath, meshOutputPath);
                                Mesh oldMesh = AssetDatabase.LoadAssetAtPath<Mesh>(meshOutputPath);
                                if (oldMesh)
                                {
                                    AssetDatabase.DeleteAsset(meshOutputPath);
                                }
                                AssetDatabase.CreateAsset(finalMesh, meshOutputPath);
                            }else if (ExportAll)
                            {
                                meshDict.Add(savePath, "");
                            }
                            PrefabUtility.SaveAsPrefabAsset(rootObj, savePath);
                            PrefabUtility.UnloadPrefabContents(rootObj);
                        }
                        // GameObject copyed = GameObject.Instantiate(prefab);
                    }
                    
                   
                }
                finally
                {
                    AssetDatabase.StopAssetEditing();
                    AssetDatabase.Refresh();
                }

                CombineStaticMesh(targetList, buildConfig);

                try
                {
                    AssetDatabase.StartAssetEditing();
                    foreach (var kv in meshDict)
                    {
                        if (!kv.Value.Equals(""))
                        {
                            var finalMesh = AssetDatabase.LoadAssetAtPath<Mesh>(kv.Value);
                            using (var editingScope = new PrefabUtility.EditPrefabContentsScope(kv.Key))
                            {
                                //add newNode
                                GameObject newEnvironNode = new GameObject(buildConfig.ExportNodeName);
                                if(buildConfig.StaticTag)
                                    newEnvironNode.tag = "ShadowStatic";
                                var mf = newEnvironNode.AddComponent<MeshFilter>();
                                var mr = newEnvironNode.AddComponent<MeshRenderer>();
                                mf.sharedMesh = finalMesh;
                                mr.sharedMaterial = m_CombineMaterialData.CombineMaterial;
                                newEnvironNode.transform.parent = editingScope.prefabContentsRoot.transform;    
                            }
                            
                        }
                    }
                }
                finally
                {
                    AssetDatabase.StopAssetEditing();
                    AssetDatabase.Refresh();
                }
                
                
            }
            
        }
        
        
        public static GameObject GetCorrespondingGameObjectInInstance(Transform root, GameObject assetObj)
        {
            if (PrefabUtility.GetCorrespondingObjectFromOriginalSource(root.gameObject) == assetObj)
            {
                return root.gameObject;
            }
            foreach (Transform child in root)
            {
                var ret = GetCorrespondingGameObjectInInstance(child, assetObj);
                if (ret != null)
                    return ret;
            }
            return null;
        }
        public void CombineStaticMesh(List<string> targetList, CombineBuildConfig buildConfig)
        {
            //网格合并
            List<Mesh> combineList = new List<Mesh>();

            foreach (var savePath in targetList)
            {
                var rootObj = PrefabUtility.LoadPrefabContents(savePath);
                if (rootObj)
                {
                    foreach (var nodeName in buildConfig.CombineMeshNodeList)
                    {
                        Transform node = rootObj.transform.FindEx(nodeName);
                        if (node != null)
                        {
                            Dictionary<Material, List<CombineInstance>> renderDataDict = new Dictionary<Material, List<CombineInstance>>();

                            var meshRenders = node.GetComponentsInChildren<MeshRenderer>();
                            foreach (var render in meshRenders)
                            {
                                MeshFilter meshFilter = render.gameObject.GetComponent<MeshFilter>();
                                if (render.sharedMaterial != null && meshFilter != null && meshFilter.sharedMesh != null)
                                {
                                    var m = render.sharedMaterial;
                                    if(!renderDataDict.TryGetValue(m, out var combineInstances))
                                    {
                                        combineInstances = new List<CombineInstance>();
                                        renderDataDict.Add(m, combineInstances);
                                    }
            
                                    combineInstances.Add(new CombineInstance()
                                    {
                                        mesh = meshFilter.sharedMesh,
                                        transform = meshFilter.transform.localToWorldMatrix
                                    });
                                    if(PrefabUtility.IsPartOfAnyPrefab(render.gameObject))
                                    {
                                        var testRoot = PrefabUtility.GetNearestPrefabInstanceRoot(render.gameObject);
                                        PrefabUtility.UnpackPrefabInstance(testRoot, PrefabUnpackMode.OutermostRoot, InteractionMode.AutomatedAction);
                                    }
                                    UnityEngine.Object.DestroyImmediate(render.gameObject);
                                }
                            }
                            
                            int i = 0;
                            Dictionary<GameObject, string> exportDict = new Dictionary<GameObject, string>(); 
                            try
                            {
                                AssetDatabase.StartAssetEditing();
                                foreach (var kv in renderDataDict)
                                {
                                    var combineInstances = kv.Value;
                                    var combineMesh = new Mesh();
                                    // combineMesh.Optimize();
                                    // combineMesh.UploadMeshData(false);
                                    var array = combineInstances.ToArray();
                                    combineMesh.CombineMeshes(array, true, true, false);
                                    var meshOutputPath =
                                        $"{m_ContentPipe.CombineSavePath}/mesh/{rootObj.name}_{nodeName}_c{i}.asset"; 
                                    

                                    AssetDatabase.CreateAsset(combineMesh, meshOutputPath);
                                
                                    var go = new GameObject($"CombineMesh_{i}");
                                    go.tag = "ShadowStatic";
                                    i++;
                                    go.transform.SetParent(node);
                                    var meshFilter = go.AddComponent<MeshFilter>();
                                    meshFilter.sharedMesh = null;
                
                                    var meshRenderer = go.AddComponent<MeshRenderer>();
                                    meshRenderer.sharedMaterial = kv.Key;
                                
                                    exportDict.Add(go, meshOutputPath);
                                }
                            }
                            finally
                            {
                                AssetDatabase.StopAssetEditing();    
                                AssetDatabase.Refresh();

                                foreach (var kv in exportDict)
                                {
                                    var m = AssetDatabase.LoadAssetAtPath<Mesh>(kv.Value);
                                    kv.Key.GetComponent<MeshFilter>().sharedMesh = m;
                                }
                                
                            }
                        }
                    }
                            
                    PrefabUtility.SaveAsPrefabAsset(rootObj, savePath);
                    PrefabUtility.UnloadPrefabContents(rootObj); 

                }
            }
                    
        }
    }
}