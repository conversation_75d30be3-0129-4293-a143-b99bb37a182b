using System;
using System.IO;
using Sultan.Core;
using Sultan.Game;
using Unity.Collections;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Sultan.Tools
{
    [CustomEditor(typeof(GridMakerTools))]
    public class GridMakeToolsEditor : UnityEditor.Editor
    {
        public GameObject TemplateCell;
        private GameObject RootObj;

        private static string MESH_COMBINE_STUDIO_PATH = "Assets/ThirdPlugins/MeshCombineStudio/MeshCombineStudio.prefab";
        private static string MESH_EXPORT_PATH = "Assets/UnUsed/Test/CityGridMesh.asset";
        private RectInt FixRange;
        public void OnEnable()
        {
            GridMakerTools tools = (GridMakerTools)target;
            tools.TemplateCell = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Res/Build/NewPrefabs/CellTemplate.prefab");
        }

        public override void OnInspectorGUI()
        {
            GridMakerTools tools = (GridMakerTools)target;
            base.OnInspectorGUI();
            tools.TemplateCell = (GameObject)EditorGUILayout.ObjectField("网格预制模板", tools.TemplateCell, typeof(GameObject));

            var gridSize = tools.GridSize;
            if (GUILayout.Button("创建初始网格"))
            {
                if (gridSize.x > 0 && gridSize.y > 0 && tools.TemplateCell != null)
                {
                    tools.FixGridSize();
                    tools.CreateGrid(new RectInt(0,0,gridSize.x, gridSize.y));
                }
            }

            if (GUILayout.Button("根据配置生成初始网格"))
            {
                if (gridSize.x > 0 && gridSize.y > 0 && tools.TemplateCell != null)
                    tools.LoadAreaCfg();
            }
            

            if (GUILayout.Button("生成预制"))
            {
                var mesh = tools.DoMeshCombine();
                SaveMeshCombine(mesh);
            }

            if (GUILayout.Button("修改网格大小"))
            {
                tools.FixGridSize();
            }

            FixRange = EditorGUILayout.RectIntField("修正区域", FixRange);
            if (GUILayout.Button("添加修正点"))
            {
                tools.CreateGrid(FixRange);
            }
        }


        private void SaveMeshCombine(Mesh mesh)
        {
            if (File.Exists(MESH_EXPORT_PATH))
            {
                AssetDatabase.DeleteAsset(MESH_EXPORT_PATH);
            }
            AssetDatabase.CreateAsset(mesh, MESH_EXPORT_PATH);
            GridMakerTools tools = (GridMakerTools)target;
            var gridSize = tools.GridSize;
 
            GameObject tmp = new GameObject();
            GameObject root = new GameObject();
            tmp.transform.localPosition = new Vector3(-0.5f * gridSize.x + 0.5f, 0, -0.5f * gridSize.y + 0.5f);
            tmp.SetParent(root.transform);
            var render = tools.TemplateCell.GetComponent<Renderer>();
            tmp.AddComponent<MeshRenderer>().sharedMaterial = render.sharedMaterial;
            tmp.AddComponent<MeshFilter>().sharedMesh = AssetDatabase.LoadAssetAtPath<Mesh>(MESH_EXPORT_PATH);
            PrefabUtility.SaveAsPrefabAsset(root, "Assets/UnUsed/Test/CityGridPrefab.prefab");

        }
        
    }
}