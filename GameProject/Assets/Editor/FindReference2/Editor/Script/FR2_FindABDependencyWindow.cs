using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Settings;
using UnityEditor.SceneManagement;
using UnityEngine;

namespace vietlabs.fr2
{
    public class FR2_FindABDependencyWindow : EditorWindow
    {
        public string guidFrom;
        public string guidTo;
        public string pathFrom;
        public string pathTo;
        public string depencencyInfo;
        public Object fromObject;
        public Object toObject;
        public int eventPicker = 0;

        public List<string> dependPaths;

        internal static void AddToDict(Dictionary<string, Object> dict, params FR2_Asset[] list)
        {
            for (var j = 0; j < list.Length; j++)
            {
                string guid = list[j].guid;
                if (!dict.ContainsKey(guid))
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    dict.Add(guid, FR2_Unity.LoadAssetAtPath<Object>(assetPath));
                }
            }
        }
        
        public static List<string> GetAddressableGroupsByObjDict(Dictionary<string, Object> objs)
        {
            List<string> groupList = new List<string>();
            AddressableAssetSettings settings = AddressableAssetSettingsDefaultObject.Settings;
            if (settings)
            {
                List<AddressableAssetGroup> groups = settings.groups;
                foreach (var group in groups)
                {
                    foreach (var entry in group.entries)
                    {
                        if (objs.ContainsKey(entry.guid))
                        {
                            if (!groupList.Contains(group.name))
                            {
                                groupList.Add(group.name); 
                            }
                        }
                    }
                }
            }

            return groupList;
        }
        
        public void OnGUI()
        {
            EditorGUILayout.BeginHorizontal();
            guidFrom = EditorGUILayout.TextField("源资源:",guidFrom);
            if (GUILayout.Button("选择源"))
            {
                eventPicker = 1;
                EditorGUIUtility.ShowObjectPicker<Object>(fromObject, false, "", 0);
            }

            if (Event.current.commandName == "ObjectSelectorUpdated" && eventPicker == 1)
            {
                fromObject = EditorGUIUtility.GetObjectPickerObject();
                if (fromObject != null)
                {
                    pathFrom = AssetDatabase.GetAssetPath(fromObject);
                    guidFrom = AssetDatabase.AssetPathToGUID(pathFrom);
                }
            }

            EditorGUILayout.EndHorizontal();
            
            if (pathFrom != "")
            {
               EditorGUILayout.LabelField("源路径:" + pathFrom);
            }
            
            
            EditorGUILayout.BeginHorizontal();
            guidTo = EditorGUILayout.TextField("目标资源:",guidTo);
            if (GUILayout.Button("选择目标"))
            {
                eventPicker = 2;
                EditorGUIUtility.ShowObjectPicker<Object>(toObject, false, "", 0);
            }

            if (Event.current.commandName == "ObjectSelectorUpdated" && eventPicker == 2)
            {
                toObject = EditorGUIUtility.GetObjectPickerObject();
                if (toObject != null)
                {
                    pathTo = AssetDatabase.GetAssetPath(toObject);
                    guidTo = AssetDatabase.AssetPathToGUID(pathTo);
                }
            }

            EditorGUILayout.EndHorizontal();
            if (pathTo != "")
            {
               EditorGUILayout.LabelField("目标路径:" + pathTo);
            }

            if (guidFrom != "" && guidTo != "")
            {
                if (GUILayout.Button("分析依赖路径"))
                {
                    //1.guidFrom 依赖的group: 我依赖谁
                    List<string> pathList;// = new List<string>();
                    bool bFound = FR2_Export.CalculateABDependency(guidFrom, guidTo, out pathList);
                    if (bFound)
                    {
                        dependPaths = pathList;
                        string pathStr = string.Join("->", pathList);
                        Debug.Log($"{pathFrom} ---> {pathTo}的依赖路径:\n {pathStr}");
                        // EditorUtility.DisplayDialog("依赖如下",
                        //     pathStr, "ok");
                    }
                    else
                    {
                        dependPaths = new List<string>();
                        EditorUtility.DisplayDialog("依赖未找到",
                            "依赖未找到", "ok");
                    }
                }
            }

            if (dependPaths != null && dependPaths.Count > 0)
            {
                EditorGUILayout.BeginVertical();
                EditorGUILayout.Separator();
                int i = 0;
                foreach (var path in dependPaths)
                {
                    EditorGUILayout.TextField(path);
                    if (i < dependPaths.Count -1)
                    {
                        // EditorGUILayout.LabelField("🔽");
                        // 绘制向下箭头
                        GUILayout.Space(5); // 添加一些空白
                        EditorGUILayout.TextField("------->", GUILayout.Height(16)); // 创建一个高度为16的空标签
                        GUILayout.Space(5); // 添加一些空白
                    }
                    i++;
                }
                EditorGUILayout.EndVertical();
            }
        }
    }
}