using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using Unity.Entities; // Ensure you have this namespace for the ConvertToEntity component.
using System.IO;

public class PrefabEntityCheck : EditorWindow
{
    private static List<KeyValuePair<string, GameObject>> errorPrefabs = new();
    private Vector2 scrollPosition;

    [MenuItem("Assets/Check for ConvertToEntity in Prefabs", false, 0)]
    public static void CheckForConvertToEntity()
    {
        /*
        errorPrefabs.Clear();
        if (Selection.assetGUIDs.Length == 0)
        {
            Debug.LogError("No assets or folders selected.");
            return;
        }

        string assetPath = AssetDatabase.GUIDToAssetPath(Selection.assetGUIDs[0]);

        if (string.IsNullOrEmpty(assetPath))
        {
            Debug.LogError("Invalid asset path.");
            return;
        }

        if (Directory.Exists(assetPath)) // If it's a directory, search within the directory
        {
            string[] prefabFiles = Directory.GetFiles(assetPath, "*.prefab", SearchOption.AllDirectories);
            foreach (string prefabFile in prefabFiles)
            {
                CheckPrefabForConvertToEntity(prefabFile);
            }
        }
        else if (assetPath.EndsWith(".prefab")) // If it's a single prefab file
        {
            CheckPrefabForConvertToEntity(assetPath);
        }
        else
        {
            Debug.LogWarning("Selected asset is not a prefab or a folder.");
        }
        
        
        GetWindow(typeof(PrefabEntityCheck));
        */
    }
    /*

    private static void CheckPrefabForConvertToEntity(string prefabPath)
    {
        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
        if (prefab != null)
        {
            ConvertToEntity[] components = prefab.GetComponentsInChildren<ConvertToEntity>(true);
            foreach (var component in components)
            {
                string nodePath = GetTransformPath(component.transform);
                Debug.LogError($"Prefab '{prefabPath}' contains ConvertToEntity component at '{nodePath}'.");
                errorPrefabs.Add(new KeyValuePair<string, GameObject>(nodePath, prefab));
            }
        }
    }*/

    private static string GetTransformPath(Transform transform)
    {
        string path = transform.name;
        while (transform.parent != null)
        {
            transform = transform.parent;
            path = transform.name + "/" + path;
        }
        return path;
    }

    private void OnGUI()
    {
        scrollPosition = GUILayout.BeginScrollView(scrollPosition);

        foreach (var item in errorPrefabs)
        {
            GameObject prefab = item.Value;

            GUILayout.Label(item.Key);
            if (GUILayout.Button(prefab.name, GUILayout.Height(40)))
            {
                EditorGUIUtility.PingObject(prefab);
            }
        }

        GUILayout.EndScrollView();
    }
}