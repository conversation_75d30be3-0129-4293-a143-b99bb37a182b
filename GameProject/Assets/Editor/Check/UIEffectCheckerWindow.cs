using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace Checker
{
    public class UIEffectCheckerWindow : EditorWindow
    {
        private string folderPath = "Assets/ArtTmp/Effect/Prefabs/Ui"; // 默认文件夹路径
        private List<MipMapInfo> mipMapTextures = new List<MipMapInfo>();
        private bool isCheckPure = false;

        [MenuItem("Check/UIEffect Checker")]
        public static void ShowWindow()
        {
            GetWindow<UIEffectCheckerWindow>("UIEffect Checker");
        }

        private void OnGUI()
        {
            GUILayout.Label("检查指定文件夹中的 Prefab 所引用的纹理", EditorStyles.boldLabel);
            folderPath = EditorGUILayout.TextField("文件夹路径", folderPath);

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("检查 Mipmap 设置"))
            {
                CheckMipMapSettings(folderPath);
            }
            
            isCheckPure = GUILayout.Toggle(isCheckPure, "只检查纹理是否为 UI 特效纹理");
            GUILayout.EndHorizontal();
            
            
            // 按钮修改全部 mipmap 设置
            if (GUILayout.Button("全部设置 MipMaps"))
            {
                foreach (var info in mipMapTextures)
                {
                    if (!isCheckPure ||info.IsPureUIEffect())
                    {
                        info.importer.mipmapEnabled = false;
                        AssetDatabase.ImportAsset(info.TexturePath, ImportAssetOptions.ForceUpdate);    
                    }
                }
                Debug.Log("全部开启 MipMaps 成功。");
                // 保存刷新
                AssetDatabase.Refresh();
            }
            
            GUILayout.Space(20);
            GUILayout.Label("开启 MipMaps 的纹理和对应的 Prefab 和材质：", EditorStyles.boldLabel);
            this.scrollViewPosition = GUILayout.BeginScrollView(this.scrollViewPosition);

            // 创建一个图片预览区域
            foreach (var info in mipMapTextures)
            {
                GUILayout.BeginHorizontal();
                // GUILayout.BeginVertical();
                GUI.color = (!isCheckPure || info.IsPureUIEffect())? Color.white : Color.red;
                if (GUILayout.Button($"{info.texure.name}"))
                {
                    Selection.activeObject = info.texure;
                    EditorGUIUtility.PingObject(Selection.activeObject);
                }

                GUI.color = Color.white;
                var imageTexture = info.texure;//AssetPreview.GetAssetPreview(info.texure);
                // 计算图片的宽高比
                float aspectRatio = (float)imageTexture.width / (float)imageTexture.height;
            
                // 定义想要设置的宽度，可以根据需要修改
                float desiredWidth = 200f; 
                // 根据宽高比计算高度
                float desiredHeight = desiredWidth / aspectRatio;

                // 绘制图片
                Rect rect = GUILayoutUtility.GetRect(desiredWidth, desiredHeight);
                GUI.DrawTexture(rect, imageTexture);
                // GUILayout.EndVertical();

                GUILayout.BeginVertical();
                foreach (var item in info.refList)
                {
                    GUI.color = Color.white;
                    GUILayout.BeginHorizontal();
                    if (GUILayout.Button($"M {item.material.name}.mat"))
                    {
                        Selection.activeObject = item.material;
                        EditorGUIUtility.PingObject(Selection.activeObject);
                    }
                    if (GUILayout.Button($"{item.prefab.name}.prefab"))
                    {
                        Selection.activeObject = item.prefab;
                        EditorGUIUtility.PingObject(Selection.activeObject);
                    }
                    GUILayout.EndHorizontal();
                }
                if (info.mat!= null)
                {
                    GUILayout.BeginHorizontal();
                    GUI.color = Color.red;
                    if (GUILayout.Button($"M {info.mat.name}"))
                    {
                        Selection.activeObject = info.mat;
                        EditorGUIUtility.PingObject(Selection.activeObject);
                    }
                    GUILayout.EndHorizontal();
                }
                
                GUILayout.EndVertical();
                GUILayout.EndHorizontal();
            }
            GUILayout.EndScrollView();
            GUI.color = Color.white;
        }

        public Vector2 scrollViewPosition { get; set; }

        private void CheckMipMapSettings(string path)
        {
            mipMapTextures.Clear(); // 清空之前的结果

            // 找到指定文件夹中的所有 prefab 文件
            string[] prefabPaths = AssetDatabase.FindAssets("t:Prefab", new[] { path });

            foreach (string prefabGuid in prefabPaths)
            {
                string prefabPath = AssetDatabase.GUIDToAssetPath(prefabGuid);
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);

                // 遍历 prefab 中的所有材质
                Renderer[] renderers = prefab.GetComponentsInChildren<Renderer>();
                foreach (var renderer in renderers)
                {
                    foreach (Material material in renderer.sharedMaterials)
                    {
                        // 检查材质的主纹理
                        if (material != null && material.mainTexture != null)
                        {
                            Texture2D texture = material.mainTexture as Texture2D;

                            if (texture != null)
                            {
                                // 获取纹理导入设置
                                string texturePath = AssetDatabase.GetAssetPath(texture);
                                TextureImporter importer = AssetImporter.GetAtPath(texturePath) as TextureImporter;

                                if (importer != null && importer.mipmapEnabled)
                                {
                                    var info = GetTexture(texture, material, prefab, texturePath, prefabPath, importer);
                                    if (info != null)
                                        mipMapTextures.Add(info);
                                }
                            }
                        }
                    }
                }
            }

            // 如果没有找到任何开启 MipMaps 的纹理，清空列表
            if (mipMapTextures.Count == 0)
            {
                Debug.Log("没有找到开启 MipMaps 的纹理。");
            }
        }

        private Dictionary<Texture2D, MipMapInfo> _dictionary = new();

        private MipMapInfo GetTexture(Texture2D texture, Material material, GameObject prefab, string texturePath, string prefabPath, TextureImporter importer)
        {
            if (!_dictionary.TryGetValue(texture, out var mipMapInfo))
            {
                mipMapInfo = new MipMapInfo
                {
                    texure = texture,
                    TextureName = texture.name,
                    TexturePath = texturePath,
                    importer = importer,
                };
                mipMapInfo.refList.Add(new TextureRefInfo
                {
                    material = material,
                    PrefabName = prefab.name,
                    MaterialName = material.name,
                    PrefabPath = prefabPath,
                    prefab = prefab
                });
                _dictionary.Add(texture, mipMapInfo);
                return mipMapInfo;
            }

            foreach (var refInfo in mipMapInfo.refList)
            {
                if (refInfo.material == material && refInfo.prefab == prefab)
                {
                    return null;
                }
            }
            mipMapInfo.refList.Add(new TextureRefInfo { 
                material = material,
                PrefabName = prefab.name,
                MaterialName = material.name,
                PrefabPath = prefabPath,
                prefab = prefab 
            });
            return null;
        }
    }

    // 用于存储 mipmap 信息的类
    public class MipMapInfo
    {
        public Texture2D texure;
        public string TextureName { get; set; }
        public string TexturePath { get; set; }
        public TextureImporter importer { get; set; }
        public List<TextureRefInfo> refList = new();

        public Material mat;

        private int calcPure = -1;
        public bool IsPureUIEffect()
        {
            if (calcPure >= 0)
                return calcPure > 0;

            HashSet<Material> hashSet = new();
            foreach (var item in refList)
            {
                if (!hashSet.Contains(item.material))
                {
                    hashSet.Add(item.material);
                }
            }
            
            
            string[] allMaterialPaths = AssetDatabase.GetAllAssetPaths();
            // List<Material> materialsUsingTexture = new List<Material>();

            foreach (string path in allMaterialPaths)
            {
                if (path.EndsWith(".mat"))
                {
                    Material material = AssetDatabase.LoadAssetAtPath<Material>(path);
                    if (material != null && material.HasProperty("_MainTex") && material.mainTexture == texure)
                    {
                        // materialsUsingTexture.Add(material);
                        if (!hashSet.Contains(material))
                        {
                            mat = material;
                            calcPure = 0;
                            return false;
                        }
                    }
                }
            }
            calcPure = 1;
            return true;
        }
    }

    public class TextureRefInfo
    {
        public Material material;
        public GameObject prefab;
        public string PrefabName { get; set; }
        public string PrefabPath { get; set; }
        public string MaterialName { get; set; }
    }        

}




