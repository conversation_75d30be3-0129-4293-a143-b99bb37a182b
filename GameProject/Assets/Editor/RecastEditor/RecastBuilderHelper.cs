using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using UnityEditor;
using UnityEngine;

/// <summary>
/// �����������ɸ���
/// Author:Aoicocon
/// </summary>

public class RecastBuilderHelper
{
    static MethodInfo meth_IntersectRayMesh;

    public RecastBuilderHelper()
    {
        var editorTypes = typeof(Editor).Assembly.GetTypes();
        var type_HandleUtility = editorTypes.FirstOrDefault(t => t.Name == "HandleUtility");
        meth_IntersectRayMesh = type_HandleUtility.GetMethod("IntersectRayMesh", BindingFlags.Static | BindingFlags.NonPublic);
    }

    public static void ShowProgressBar(float progress, float maxProgress)
    {
        float p = progress / maxProgress;
        EditorUtility.DisplayProgressBar("Creating Terrain...", Mathf.RoundToInt(p * 100f) + " %", p);
    }

    public static ushort[] GenerateHeightMap(RecastBuilder.MapBounding bounding, int scaleUnit, bool Debug, GameObject[] walkAreaObjects)
    {
        if (walkAreaObjects == null || walkAreaObjects.Length == 0) {
            return null;
        }

        //if (reacastGenCollider == null)
        //{
        //    UnityEngine.Debug.LogError("Not Found RecastNavMesh Collider, HeightMap incomplete");
        //}

        InitDepthCamera();
        List<Vector3> debugMatList = new List<Vector3>();
       /* RecastBuilderHelperMono debugHeightMapMono = null;
        if (Debug)
        {
            GameObject debugHeightMap = GameObject.Find("DebugHeightMap");
            if (!debugHeightMap)
            {
                debugHeightMap = new GameObject();
                debugHeightMap.name = "DebugHeightMap";
            }

            debugHeightMapMono = debugHeightMap.GetComponent<RecastBuilderHelperMono>();
            if (!debugHeightMapMono)
            {
                debugHeightMapMono = debugHeightMap.AddComponent<RecastBuilderHelperMono>();
            }
        }*/

        float shiftHeight = 1 / (float)ushort.MaxValue;

        float boundingWidth = (bounding.MaxX - bounding.MinX) / 100.0f;
        float boundingLength = (bounding.MaxZ - bounding.MinZ) / 100.0f;
        float boundingHeight = (bounding.MaxY - bounding.MinY) / 100.0f;


        boundingHeight = Mathf.Max(1, boundingHeight);

        ushort[] heights = new ushort[(int)(boundingWidth * scaleUnit) * (int)(boundingLength * scaleUnit)];

        Ray ray = new Ray(new Vector3(bounding.MinX / 100.0f, bounding.MaxY / 100.0f + boundingHeight, bounding.MinZ / 100.0f), -Vector3.up);
        RaycastHit hit = new RaycastHit();
        float meshHeightInverse = 1 / boundingHeight;
        Vector3 rayOrigin = ray.origin;

        int maxWidth = (int)(boundingWidth * scaleUnit);
        int maxLength = (int)(boundingLength * scaleUnit);

        Vector2 stepXZ = new Vector2(boundingWidth / maxWidth, boundingLength / maxLength);

        ShowProgressBar(1, 100);

        MeshCollider[] colldierArr = new MeshCollider[walkAreaObjects.Length];
        for (int exporterIndex = 0; exporterIndex < walkAreaObjects.Length; ++exporterIndex)
        {
            GameObject selfGameobject = walkAreaObjects[exporterIndex];
            MeshCollider collider = selfGameobject.GetComponent<MeshCollider>();
            selfGameobject.GetComponent<MeshRenderer>().enabled = false;
            if (!collider)
            {
                collider = selfGameobject.AddComponent<MeshCollider>();
            }

            colldierArr[exporterIndex] = collider;
        }

        Vector3 camPos = new Vector3();
        for (int zCount = 0; zCount < maxLength; zCount++)
        {
            ShowProgressBar(zCount, maxLength);

            for (int xCount = 0; xCount < maxWidth; xCount++)
            {
                ushort height = 0;

                for (int i = 0; i < colldierArr.Length; i++)
                {
                    MeshCollider collider = colldierArr[i];

                    if (collider.Raycast(ray, out hit, boundingHeight * 3))
                    {
                        float pointY = hit.point.y;
                        camPos.Set(hit.point.x, pointY + ShootDistance, hit.point.z);
                        float cameraHeight = CameraShotDepth(camPos);
                        if ((cameraHeight >= pointY) || (Math.Abs(cameraHeight - pointY) < 1f))
                        {
                            pointY = cameraHeight;
                        }

                        float curHeight = (pointY - bounding.MinY / 100.0f) * meshHeightInverse;

                        //clamp
                        if (curHeight <= 0)
                        {
                            curHeight = shiftHeight;
                        }

                        float fHeight = curHeight * (float)ushort.MaxValue;
                        fHeight = Mathf.Min((float)ushort.MaxValue, fHeight);

                        if (fHeight > height)
                        {
                            height = (ushort)fHeight;
                        }
                    }
                }

                int idx = xCount + zCount * maxWidth;
                ushort fixedHeight = 0;

                //if (reacastGenCollider && height == 0)
                //{
                //    if (reacastGenCollider.Raycast(ray, out hit, boundingHeight * 3))
                //    {
                //        float curHeight = (hit.point.y - bounding.MinY / 100.0f) * meshHeightInverse;
                //        //clamp
                //        if (curHeight <= 0)
                //        {
                //            curHeight = shiftHeight;
                //        }

                //        float fHeight = curHeight * (float)ushort.MaxValue;
                //        fHeight = Mathf.Min((float)ushort.MaxValue, fHeight);

                //        if (fHeight > fixedHeight)
                //        {
                //            fixedHeight = (ushort)fHeight;
                //        }
                //    }
                //}

                if (fixedHeight > height)
                {
                    height = fixedHeight;
                }

                heights[idx] = height;

                rayOrigin.x += stepXZ[0];
                ray.origin = rayOrigin;
            }

            rayOrigin.z += stepXZ[1];
            rayOrigin.x = bounding.MinX / 100.0f;
            ray.origin = rayOrigin;
        }

        ushort[] lerpHeightArr = new ushort[heights.Length];
        int iMapCount = maxLength * maxWidth;
        //离线差值
        for (int zCount = 0; zCount < maxLength; zCount++)
        {
            for (int xCount = 0; xCount < maxWidth; xCount++)
            {
                int sAverageHeight = 0;
                int samplerCount = 0;
                int curIdx = xCount + zCount * maxWidth;
                if (heights[curIdx] != 0)
                    continue;

                for (int i = -1; i < 2; i++)
                {
                    for (int j = -1; j < 2; j++)
                    {
                        int newX = xCount + i;
                        int newZ = zCount + j;

                        int colIdx = newX + newZ * maxWidth;

                        if (colIdx < 0 || colIdx >= iMapCount)
                            continue;

                        ushort samplerHeight = heights[colIdx];
                        if (samplerHeight > 0)
                        {
                            sAverageHeight += samplerHeight;
                            ++samplerCount;
                        }
                    }
                }

                ushort lerpHeight = 0;
                if (samplerCount != 0)
                {
                    lerpHeight = (ushort)(sAverageHeight / samplerCount);
                }

                if (heights[curIdx] == 0)
                {
                    lerpHeightArr[curIdx] = lerpHeight;
                }
            }
        }

        for (int zCount = 0; zCount < maxLength; zCount++)
        {
            for (int xCount = 0; xCount < maxWidth; xCount++)
            {
                int curIdx = xCount + zCount * maxWidth;
                heights[curIdx] += lerpHeightArr[curIdx];
            }
        }

        for (int zCount = 0; zCount < maxLength; zCount++)
        {
            for (int xCount = 0; xCount < maxWidth; xCount++)
            {
                int curIdx = xCount + zCount * maxWidth;

                if (heights[curIdx] > shiftHeight)
                {
                    if (Debug)
                    {
                        float dataScale = 100.0f * scaleUnit;
                        float wx = xCount / (float)scaleUnit + bounding.MinX / 100.0f;
                        float wz = zCount / (float)scaleUnit + bounding.MinZ / 100.0f;
                        Vector3 worldPos = new Vector3(wx, 0, wz);
                        worldPos.y += bounding.MinY / 100.0f;
                        worldPos.y += (heights[curIdx] / (float)ushort.MaxValue) * ((bounding.MaxY - bounding.MinY) / 100.0f);

                        debugMatList.Add(worldPos);
                    }
                }
            }
        }

       /* if (Debug && debugHeightMapMono)
        {
            debugHeightMapMono.HeightPoint = debugMatList.ToArray();
            debugHeightMapMono.ScaleUnit = scaleUnit;
        }*/

        //Texture2D tex = new Texture2D(maxWidth, maxLength, TextureFormat.Alpha8, false);
        //for (int i = 0; i < maxLength * maxWidth; i++)
        //{
        //    int idxX = i % maxWidth;
        //    int idxZ = i / maxWidth;
        //    float height = heights[i];
        //    tex.SetPixel(idxX, idxZ, new Color(0, 0, 0, height));
        //}
        //tex.Apply();
        //AssetDatabase.CreateAsset(tex, "Assets/height.asset");
        EditorUtility.ClearProgressBar();
        DestroyDepthCamera();
        return heights;
    }

    #region [深度相机]
    const int DepthCameraWidth = 5;
    const int DepthCameraHeight = 5;
    const int DepthCameraWidthMiddle = 2;
    const int DepthCameraHeightMiddle = 2;
    const float FarClipPlane = 4f;
    const float NearClipPlane = 0.1f;
    const float ShootDistance = 2f;
    const string DepCamName = "DepthCamera";
    static private RenderTexture colorRenderTex;
    static private RenderTexture depthRenderTex;
    static private RenderTexture duplicateRenderTex;
    static private Texture2D sampleTex2D;
    static private Rect readRect;
    static private Camera depthCamera;

    /// <summary>
    /// 初始化深度相机
    /// </summary>
    private static void InitDepthCamera()
    {
        GameObject RecastCamObj = GameObject.Find(DepCamName);
        if (RecastCamObj == null)
        {
            RecastCamObj = new GameObject("RecastCamera", typeof(Camera))
            {
                name = DepCamName
            };
        }

        depthCamera = RecastCamObj.GetComponent<Camera>();
        depthCamera.orthographic = true;
        depthCamera.orthographicSize = 0.1f;
        depthCamera.clearFlags = CameraClearFlags.SolidColor;
        depthCamera.depthTextureMode = DepthTextureMode.None;
        depthCamera.renderingPath = RenderingPath.Forward;
        depthCamera.allowHDR = false;
        depthCamera.allowMSAA = false;
        depthCamera.useOcclusionCulling = false;
        depthCamera.allowHDR = false;
        depthCamera.farClipPlane = FarClipPlane;
        depthCamera.nearClipPlane = NearClipPlane;
        depthCamera.depth = -100;
        depthCamera.SetReplacementShader(Shader.Find("WarWolf/Recast/FuckAlphaTest"), "RenderType");

        colorRenderTex = new RenderTexture(DepthCameraWidth, DepthCameraHeight, 0, RenderTextureFormat.ARGB32);
        depthRenderTex = new RenderTexture(DepthCameraWidth, DepthCameraHeight, 24, RenderTextureFormat.Depth);
        duplicateRenderTex = new RenderTexture(DepthCameraWidth, DepthCameraHeight, 24, RenderTextureFormat.ARGBFloat);
        sampleTex2D = new Texture2D(DepthCameraWidth, DepthCameraHeight, TextureFormat.ARGB32, false);
        readRect = new Rect(0, 0, DepthCameraWidth, DepthCameraHeight);

        depthCamera.SetTargetBuffers(colorRenderTex.colorBuffer, depthRenderTex.depthBuffer);
    }

    /// <summary>
    /// 通过相机深度纹理获取高度
    /// </summary>
    /// <param name="varCamPos">相机位置</param>    
    /// <returns>返回从Origin点向下方拍摄的一张图片， 取图片中心点在世界坐标下的高度</returns>
    private static float CameraShotDepth(Vector3 varCamPos)
    {
        if (null == depthCamera)
        {
            Debug.LogError("depthCamera not found");
            return 0f;
        }

        sampleTex2D.ReadPixels(readRect, 0, 0);
        sampleTex2D.Apply();

        depthCamera.transform.position = varCamPos;
        depthCamera.transform.rotation = Quaternion.LookRotation(Vector3.down);
        depthCamera.Render();

        Graphics.Blit(depthRenderTex, duplicateRenderTex);
        RenderTexture.active = duplicateRenderTex;
        //Color center = sampleTex2D.GetPixel(DepthCameraWidth / 2 - 1, DepthCameraHeight / 2 - 1);
        Color center = sampleTex2D.GetPixel(DepthCameraWidthMiddle, DepthCameraHeightMiddle);
        if (center.r == 0f)
            return 0f;

        Matrix4x4 projectionToWorldMatrix = (depthCamera.projectionMatrix * depthCamera.worldToCameraMatrix).inverse;
        Vector4 curCoord = new Vector4(0f, 0f, center.r * 2f - 1f, 1f);
        Vector4 worldCoord = projectionToWorldMatrix * curCoord;
        return worldCoord.y;
    }

    /// <summary>
    /// 销毁深度相机
    /// </summary>
    private static void DestroyDepthCamera()
    {
        GameObject RecastCamObj = GameObject.Find(DepCamName);
        if (null != RecastCamObj)
            GameObject.DestroyImmediate(RecastCamObj);
    }
    #endregion

    public static void FillMeshDatasTo(GameObject[] walkAreaObjects, ref int meshVerticesCount, ref int meshTriangleIndexCount, float[] meshVertices, int[] meshTriangleIndexs)
    {
        if (walkAreaObjects == null || walkAreaObjects.Length == 0) {
            return;
        }

        int currentVerticesCount = 0;
        int currentTriangleIndexsCount = 0;
        Array.Clear(meshVertices, 0, meshVertices.Length);
        Array.Clear(meshTriangleIndexs, 0, meshTriangleIndexs.Length);
        
        for (int i = 0; i < walkAreaObjects.Length; ++i)
        {
            GameObject walkAreaObject = walkAreaObjects[i];
            Transform walkAreaTransform = walkAreaObject.transform;
            MeshFilter meshFilter = walkAreaObject.GetComponent<MeshFilter>();
            Mesh mesh = meshFilter.sharedMesh;

            //fill triangleBuffer
            int[] triangles = mesh.triangles;          

            for (int triangleIndex = 0; triangleIndex < triangles.Length; ++triangleIndex)
            {
                meshTriangleIndexs[meshTriangleIndexCount + triangleIndex] = triangles[triangleIndex] + (int)currentVerticesCount;
               
            }
            currentTriangleIndexsCount += triangles.Length;

            //fill vertexBuffer
            Vector3[] vertices = mesh.vertices;
           // Debug.LogError("vertices.Length==="+ vertices.Length);
            for (int vertexIndex = 0; vertexIndex < vertices.Length; ++vertexIndex)
            {
                Vector3 destPosition = walkAreaTransform.localToWorldMatrix.MultiplyPoint(vertices[vertexIndex]);
                meshVertices[currentVerticesCount + vertexIndex * 3] = destPosition.x;
                meshVertices[currentVerticesCount + vertexIndex * 3 + 1] = destPosition.y;
                meshVertices[currentVerticesCount + vertexIndex * 3 + 2] = destPosition.z;
            }
            currentVerticesCount += vertices.Length * 3;
        }

        meshVerticesCount = currentVerticesCount;
        meshTriangleIndexCount = currentTriangleIndexsCount;
    }
    public static void SetAllObstacle(uint sceneId)
    {
      /*  RecastDLL.ClearUnityObstacle(sceneId);
        var cubeObs = Resources.FindObjectsOfTypeAll(typeof(CubeObstacle));
        List<float> listMatirx = new List<float>(16);
        int[] dataInfo = new int[7];
        int dataIdx = 0;
        for (int i = 0; i < cubeObs.Length; ++i)
        {
            CubeObstacle cubeobs = cubeObs[i] as CubeObstacle;
            GameObject go = cubeobs.gameObject;
            Matrix4x4 matrix = go.transform.worldToLocalMatrix;
            listMatirx.Clear();
            for (int row = 0; row < 4; ++row)
            {
                for (int column = 0; column < 4; ++column)
                {
                    listMatirx.Add(matrix[row, column]);
                }
            }
            BoxCollider bc = go.GetComponent<BoxCollider>();
            dataIdx = 0;
            //0
            dataInfo[dataIdx++] = cubeobs.id;
            //1~3
            dataInfo[dataIdx++] = (int)(go.transform.position.x * 100);
            dataInfo[dataIdx++] = (int)(go.transform.position.y * 100);
            dataInfo[dataIdx++] = (int)(go.transform.position.z * 100);
            //4~6
            dataInfo[dataIdx++] = (int)(bc.size.x / 2 * 100);
            dataInfo[dataIdx++] = (int)(bc.size.y / 2 * 100);
            dataInfo[dataIdx++] = (int)(bc.size.z / 2 * 100);
            RecastDLL.AddUnityCubeObstacle(sceneId, listMatirx.ToArray(), dataInfo, cubeobs.u64mask);
        }

        var sphereObs = Resources.FindObjectsOfTypeAll(typeof(SphereObstacle));
        int[] sphereData = new int[4];
        for (int i = 0; i < sphereObs.Length; ++i)
        {
            SphereObstacle sphereOb = sphereObs[i] as SphereObstacle;
            GameObject go = sphereOb.gameObject;
            Vector3 v3Pos = go.transform.position;
            dataIdx = 0;
            //0
            sphereData[dataIdx++] = sphereOb.id;
            //1~3
            sphereData[dataIdx++] = (int)(v3Pos.x * 100);
            sphereData[dataIdx++] = (int)(v3Pos.y * 100);
            sphereData[dataIdx++] = (int)(v3Pos.z * 100);
            SphereCollider co = go.GetComponent<SphereCollider>();
            RecastDLL.AddUnitySphereObstacle(sceneId, sphereData, (int)(co.radius * go.transform.localScale.x * 100), sphereOb.u64mask);
        }*/
    }
}