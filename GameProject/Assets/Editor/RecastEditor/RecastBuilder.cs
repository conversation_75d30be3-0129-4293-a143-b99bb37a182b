using System.Runtime.InteropServices;
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using System;
using UnityEditor.SceneManagement;
using System.Collections.Generic;
//using OnemtDevelop;
using System.Linq;
//using OfficeOpenXml;

public class InternalRecastHeightmap
{
    public int DataLen;
    public int DataScale;
    public int width;
    public int height;
    public ushort[] Data;
}

public  class RecastBuilder : EditorWindow
{
    [Serializable]
    public class EditorConfig
    {
        // RECAST相关设置
        public float CellSize = 20f;
        public float CellHeight = 10f;

        public float AgentHeight = 200.0f;
        public float AgentRadius = 10f;
        public float AgentMaxClimb = 40f;
        public float AgentMaxSlope = 90.0f;

        public float MinRegionSize = 100.0f;
        public float RegionMergeSize = 3000.0f;

        public float MaxEdgeLength = 1200.0f;
        public float MaxEdgeError = 1.3f;
        public uint VerticesPerPoly = 6;

        public float DetailSampleDistance = 6.0f;
        public float DetailSampleMaxError = 1.0f;

        public int TileSize = 1024;

        // 高度图设置
        public bool IsCreateHeightMap = false;
        public int HeightMapScale = 3;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct AgentCubeData
    {
        [MarshalAs(UnmanagedType.U4)]
        public ulong Mask;
        [MarshalAs(UnmanagedType.I4)]
        public int Id;
        [MarshalAs(UnmanagedType.ByValArray, ArraySubType = UnmanagedType.I4, SizeConst = 3)]
        public int[] Center;
        [MarshalAs(UnmanagedType.I4)]
        public int Width;
        [MarshalAs(UnmanagedType.I4)]
        public int Height;
        [MarshalAs(UnmanagedType.I4)]
        public int Length;
        [MarshalAs(UnmanagedType.ByValArray, ArraySubType = UnmanagedType.I4, SizeConst = 16)]
        public float[] WorldTolocalMatrix;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct AgentSphereData
    {
        [MarshalAs(UnmanagedType.U4)]
        public ulong Mask;
        [MarshalAs(UnmanagedType.I4)]
        public int Id;
        [MarshalAs(UnmanagedType.ByValArray, ArraySubType = UnmanagedType.I4, SizeConst = 3)]
        public int[] Center;
        [MarshalAs(UnmanagedType.I4)]
        public int Radius;
    }

    public struct MapBounding
    {
        public float MinX;
        public float MinY;
        public float MinZ;
        public float MaxX;
        public float MaxY;
        public float MaxZ;

        public MapBounding(float[] boundMin, float[] boundMax)
        {
            MinX = boundMin[0];
            MinY = boundMin[1];
            MinZ = boundMin[2];

            MaxX = boundMax[0];
            MaxY = boundMax[1];
            MaxZ = boundMax[2];
        }
    }

    public struct ObstacleEffectInfo
    {        
        public int IdIndex;
        public int FxPosIndex;
        public int FxDirIndex;
        public int FxScaleIndex;
    }

    private enum DrawType
    {
        POLY_NONE,
        POLY_MESH,
        TILE_MESH,
        POLY_MESH_DETAIL_BLUE,
        POLY_MESH_DETAIL_COLORFUL,
    }

    private const string _configFilePathName = "Assets/StreamingAssets/Setting/AStar/navConfig.bytes";
   // private const string _existNavMeshDataPath = "Assets/StreamingAssets/Setting/AStar/";
    // private const string _serverNavMeshDataPath = "../../../config/data/server/custom/scene/";
    private const string _serverNavMeshDataPath = "../../../../sultan_common/navMeshByte/";
    private const string _existNavMeshDataPath = "E:/AStar/";

    private EditorConfig _editorConfig = new EditorConfig();

    private Material _polyMaterial;
    private float _paramWidth = 230f;
    private int _rise = 0;
    private bool _isShowBuildConfigs = true;
    private bool _isShowHeightMapConfigs = true;
    private DrawType _drawType = DrawType.POLY_MESH_DETAIL_BLUE;

    private Dictionary<int, Material> _obstacleMeshMaterials = new Dictionary<int, Material>();
    private Dictionary<int, Transform> _obstacleEffectTransforms = new Dictionary<int, Transform>();

    private string _sceneName;

    private float[] _meshVertices = new float[10240000 * 3];
    private int[] _meshTriangleIndexs = new int[10240000 * 3];
    private float[] _navMeshPolyVertices = new float[10240000 * 3];

    private float[] _mapBoundMin = new float[3];
    private float[] _mapBoundMax = new float[3];

    private float _navMeshPolyVerticesCount = 0;

    private string nameNavMeshFile = "TerrainNavMesh";//网格文件名,支持用户输入

    [MenuItem("WarWolf/RecastBuilder")]
    public static void Init()
    {
        EditorWindow.GetWindow(typeof(RecastBuilder));
    }

    private void OnEnable()
    {
        LoadRecastBuildConfig();

        // 创建材质用于GL函数使用
        Shader shader = Shader.Find("Hidden/Internal-Colored");
        _polyMaterial = new Material(shader);
        _polyMaterial.hideFlags = HideFlags.HideAndDontSave;
        _polyMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        _polyMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        _polyMaterial.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        _polyMaterial.SetInt("_ZWrite", 0);

        SceneView.onSceneGUIDelegate += OnSceneGUI;     
    }

    private void OnDisable()
    {
        SceneView.onSceneGUIDelegate -= OnSceneGUI;      

        RecastDLL.RemoveNAVMesh(0);
    }

    private void OnGUI()
    {
        DrawEditorOptions();
        DrawHeightMapConfigs();
        DrawRecastConfigs();
    }

    private void OnSceneGUI(SceneView sceneView)
    {
        DrawNavMesh();
    }

    private void DrawEditorOptions()
    {
        EditorGUILayout.BeginVertical();

        if (GUILayout.Button("导出并保存NavMesh网格"))
        {
            BuildTerrainNavMesh();
            SaveBuildConfigAndNavMesh();
        }
        EditorGUILayout.Space();

        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(new GUIContent("NameFile 文件名"));
        nameNavMeshFile = EditorGUILayout.TextField(nameNavMeshFile);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        ++EditorGUI.indentLevel;
        --EditorGUI.indentLevel;
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        ++EditorGUI.indentLevel;
        EditorGUILayout.LabelField(new GUIContent("DrawType"), GUILayout.Width(_paramWidth));
        DrawType drawType = (DrawType)EditorGUILayout.EnumPopup(_drawType);
        if (drawType != _drawType)
        {
            _drawType = drawType;
            SceneView.RepaintAll();
        }
        --EditorGUI.indentLevel;
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        ++EditorGUI.indentLevel;
        EditorGUILayout.LabelField(new GUIContent("Rise"), GUILayout.Width(_paramWidth));
        int rise = EditorGUILayout.IntSlider(_rise, 0, 100);
        if (rise != _rise)
        {
            _rise = rise;
            SceneView.RepaintAll();
        }
        --EditorGUI.indentLevel;
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.EndVertical();
    }

    private void DrawHeightMapConfigs()
    {
        EditorGUILayout.Space();
        _isShowHeightMapConfigs = EditorGUILayout.Foldout(_isShowHeightMapConfigs, "HeightMap Configs");
        if (!_isShowHeightMapConfigs) {
            return;
        }

        EditorGUILayout.BeginVertical("box");
        ++EditorGUI.indentLevel;

        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(new GUIContent("HightMap Scale"), GUILayout.Width(_paramWidth));
        _editorConfig.HeightMapScale = EditorGUILayout.IntField(_editorConfig.HeightMapScale);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(new GUIContent("Create HeigthMap"), GUILayout.Width(_paramWidth));
        _editorConfig.IsCreateHeightMap = EditorGUILayout.Toggle(_editorConfig.IsCreateHeightMap);
        EditorGUILayout.EndHorizontal();

        --EditorGUI.indentLevel;
        EditorGUILayout.EndVertical();
    }

    private void DrawRecastConfigs()
    {
        EditorGUILayout.Space();
        _isShowBuildConfigs = EditorGUILayout.Foldout(_isShowBuildConfigs, "Build Configs");
        if (!_isShowBuildConfigs) {
            return;
        }

        EditorGUILayout.BeginVertical("box");

        EditorGUILayout.LabelField("Rasteriztion");
        {
            ++EditorGUI.indentLevel;

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("CellSize 体素的宽", "该值越小，导航网格的精度越高"), GUILayout.Width(_paramWidth));
            _editorConfig.CellSize = EditorGUILayout.FloatField(_editorConfig.CellSize);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("CellHeight 体素的高"), GUILayout.Width(_paramWidth));
            _editorConfig.CellHeight = EditorGUILayout.FloatField(_editorConfig.CellHeight);
            EditorGUILayout.EndHorizontal();

            --EditorGUI.indentLevel;
        }

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Agent");
        {
            ++EditorGUI.indentLevel;

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("AgentHeight 对象的高度"), GUILayout.Width(_paramWidth));
            _editorConfig.AgentHeight = EditorGUILayout.FloatField(_editorConfig.AgentHeight);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(
                new GUIContent("AgentRadius 对象的宽度", "该值的大小会影响生成的导航网格。如果该值很大，那么生成的导航网格会比美术定义的可行走区域小"), GUILayout.Width(_paramWidth));
            _editorConfig.AgentRadius = EditorGUILayout.FloatField(_editorConfig.AgentRadius);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("AgentMaxClimb 对象可以爬升的高度"), GUILayout.Width(_paramWidth));
            _editorConfig.AgentMaxClimb = EditorGUILayout.FloatField(_editorConfig.AgentMaxClimb);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("AgentMaxSlope 对象可以爬升的坡度"), GUILayout.Width(_paramWidth));
            _editorConfig.AgentMaxSlope = EditorGUILayout.FloatField(_editorConfig.AgentMaxSlope);
            EditorGUILayout.EndHorizontal();

            --EditorGUI.indentLevel;
        }

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Region");
        {
            ++EditorGUI.indentLevel;

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("MinRegionSize 最小区域的尺寸", "当一个区域小于该值时，在生成网格时不会被考虑进去"), GUILayout.Width(_paramWidth));
            _editorConfig.MinRegionSize = EditorGUILayout.IntField((int)_editorConfig.MinRegionSize);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("RegionMergeSize 最大可合并区域的尺寸", "当一个区域小于该值时，如果可以，则会被合并进一个大的区域中"), GUILayout.Width(_paramWidth));
            _editorConfig.RegionMergeSize = EditorGUILayout.IntField((int)_editorConfig.RegionMergeSize);
            EditorGUILayout.EndHorizontal();

            --EditorGUI.indentLevel;
        }

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Polygonization");
        {
            ++EditorGUI.indentLevel;

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("MaxEdgeLength 多边形边长最大值", "当该值>0时，如果有一条边的长度大于该值，那么该条边会被拆分成多条，这样有助于减少细长三角形的数量。等于0则不拆分。"),
                GUILayout.Width(_paramWidth));
            _editorConfig.MaxEdgeLength = EditorGUILayout.IntField((int)_editorConfig.MaxEdgeLength);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("MaxEdgeError"), GUILayout.Width(_paramWidth));
            _editorConfig.MaxEdgeError = Mathf.Clamp(EditorGUILayout.FloatField(_editorConfig.MaxEdgeError), 0.1f, 3f);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("VerticesPerPoly 多边形最大顶点数"), GUILayout.Width(_paramWidth));
            _editorConfig.VerticesPerPoly = (uint)Mathf.Clamp(EditorGUILayout.IntField((int)_editorConfig.VerticesPerPoly), 3, 12);
            EditorGUILayout.EndHorizontal();

            --EditorGUI.indentLevel;
        }

        EditorGUILayout.Space();
        EditorGUILayout.LabelField(new GUIContent("Polygonization", "细节网格相关参数"));
        {
            ++EditorGUI.indentLevel;

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("DetailSampleDistance"), GUILayout.Width(_paramWidth));
            _editorConfig.DetailSampleDistance = EditorGUILayout.FloatField(_editorConfig.DetailSampleDistance);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("DetailSampleMaxError"), GUILayout.Width(_paramWidth));
            _editorConfig.DetailSampleMaxError = EditorGUILayout.FloatField(_editorConfig.DetailSampleMaxError);
            EditorGUILayout.EndHorizontal();

            --EditorGUI.indentLevel;
        }

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Tiling");
        {
            ++EditorGUI.indentLevel;
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("TileSize"), GUILayout.Width(_paramWidth));
            int tileSize = Mathf.Clamp(EditorGUILayout.IntField((int)_editorConfig.TileSize), 16, 1024);
            _editorConfig.TileSize = tileSize / 16 * 16;
            EditorGUILayout.EndHorizontal();
            --EditorGUI.indentLevel;
        }

        EditorGUILayout.EndVertical();
    }

    private void DrawNavMesh()
    {
        if (_navMeshPolyVerticesCount == 0) {
            return;
        }

        switch(_drawType)
        {
            case DrawType.POLY_MESH:
                DrawTileOrPolyMeshes(false);
                break;

            case DrawType.TILE_MESH:
                DrawTileOrPolyMeshes(true);
                break;

            case DrawType.POLY_MESH_DETAIL_BLUE:
                DrawPolyMeshDetails(false);
                break;

            case DrawType.POLY_MESH_DETAIL_COLORFUL:
                DrawPolyMeshDetails(true);
                break;
            default:
                break;
        }
    }

    private void DrawTileOrPolyMeshes(bool isTileMesh)
    {
        _polyMaterial.SetPass(0);

        GL.PushMatrix();

        GL.Begin(GL.TRIANGLES);

        int offset = isTileMesh ? 10 : 9;
        for (int i = 0; i < _navMeshPolyVerticesCount; i += 11)
        {
            float x = (float)_navMeshPolyVertices[i];
            float y = (float)_navMeshPolyVertices[i + 1] + _rise;
            float z = (float)_navMeshPolyVertices[i + 2];

            float x1 = (float)_navMeshPolyVertices[i + 3];
            float y1 = (float)_navMeshPolyVertices[i + 4] + _rise;
            float z1 = (float)_navMeshPolyVertices[i + 5];

            float x2 = (float)_navMeshPolyVertices[i + 6];
            float y2 = (float)_navMeshPolyVertices[i + 7] + _rise;
            float z2 = (float)_navMeshPolyVertices[i + 8];

            int value = (int)_navMeshPolyVertices[i + offset];
            Color polyColor = ToColor(value, 192);
            GL.Color(polyColor);

            GL.Vertex3(x, y, z);
            GL.Vertex3(x1, y1, z1);
            GL.Vertex3(x2, y2, z2);
        }
        GL.End();

        GL.PopMatrix();
    }

    private void DrawPolyMeshDetails(bool isColourful)
    {
        _polyMaterial.SetPass(0);

        GL.PushMatrix();

        GL.Begin(GL.TRIANGLES);

        if (!isColourful)
        {
            Color blurColor = new Color(28 / 255f, 137f / 255f, 211 / 255f, 192 / 255f);
            GL.Color(blurColor);
        }        

        for (int i = 0; i < _navMeshPolyVerticesCount; i += 11)
        {
            float x = (float)_navMeshPolyVertices[i];
            float y = (float)_navMeshPolyVertices[i + 1] + _rise;
            float z = (float)_navMeshPolyVertices[i + 2];

            float x1 = (float)_navMeshPolyVertices[i + 3];
            float y1 = (float)_navMeshPolyVertices[i + 4] + _rise;
            float z1 = (float)_navMeshPolyVertices[i + 5];

            float x2 = (float)_navMeshPolyVertices[i + 6];
            float y2 = (float)_navMeshPolyVertices[i + 7] + _rise;
            float z2 = (float)_navMeshPolyVertices[i + 8];

            if (isColourful)
            {
                int value = (int)_navMeshPolyVertices[i + 9];
                Color polyColor = ToColor(value, 192);
                GL.Color(polyColor);
            }

            GL.Vertex3(x, y, z);
            GL.Vertex3(x1, y1, z1);
            GL.Vertex3(x2, y2, z2);
        }
        GL.End();

        // 用线用三角形
        GL.Begin(GL.LINES);
        GL.Color(Color.black);
        for (int i = 0; i < _navMeshPolyVerticesCount; i += 11)
        {
            float x = (float)_navMeshPolyVertices[i];
            float y = (float)_navMeshPolyVertices[i + 1] + _rise;
            float z = (float)_navMeshPolyVertices[i + 2];

            float x1 = (float)_navMeshPolyVertices[i + 3];
            float y1 = (float)_navMeshPolyVertices[i + 4] + _rise;
            float z1 = (float)_navMeshPolyVertices[i + 5];

            float x2 = (float)_navMeshPolyVertices[i + 6];
            float y2 = (float)_navMeshPolyVertices[i + 7] + _rise;
            float z2 = (float)_navMeshPolyVertices[i + 8];

            GL.Vertex3(x, y, z);
            GL.Vertex3(x1, y1, z1);

            GL.Vertex3(x1, y1, z1);
            GL.Vertex3(x2, y2, z2);

            GL.Vertex3(x2, y2, z2);
            GL.Vertex3(x, y, z);
        }
        GL.End();

        GL.PopMatrix();
    }

   public void BuildTerrainNavMesh()
   {      
        GameObject navMeshSurfaceGo = GameObject.Find("TerrainNavMeshSurface");      
        if (!navMeshSurfaceGo)
        {
            return;
        }

        GameObject[] walkAreaObjects = new GameObject[1];
        walkAreaObjects[0] = navMeshSurfaceGo;      
        _navMeshPolyVerticesCount = 0;

        int meshVerticesCount = 0;
        int meshTriangleIndesCount = 0;
        RecastBuilderHelper.FillMeshDatasTo(walkAreaObjects, ref meshVerticesCount, ref meshTriangleIndesCount, _meshVertices, _meshTriangleIndexs);
        if (_meshVertices.Length > 0 && _meshTriangleIndexs.Length > 0)
        {
            string recastBuildConfig = RecastBuildConfitToString();
            RecastDLL.BuildNavMeshFromMeshDatas(
                    0,
                    recastBuildConfig,
                    _meshVertices, meshVerticesCount, _meshTriangleIndexs, meshTriangleIndesCount, 1,
                    _mapBoundMin, _mapBoundMax,
                    _navMeshPolyVertices
                    
                    , ref _navMeshPolyVerticesCount);

            SceneView.RepaintAll();
        }
    }

    private void SaveBuildConfigAndNavMesh()
    {
        SaveRecastBuildConfig();

        if (!Directory.Exists(_existNavMeshDataPath)) {
            Directory.CreateDirectory(_existNavMeshDataPath);
        }

        /* string currentSceneName = GetCurrentSceneName();
         string navMeshFileName = currentSceneName.ToLower() + ".bytes";
         string navMeshFilePathName = _existNavMeshDataPath + navMeshFileName;*/

        string navMeshFileName = nameNavMeshFile.ToLower() + ".bytes";
        string navMeshFilePathName = _existNavMeshDataPath + navMeshFileName;

        RecastBuilderHelper.SetAllObstacle(0);
        RecastDLL.SaveNAVMeshFile(0, navMeshFilePathName);
        AssetDatabase.Refresh();

        if (File.Exists(navMeshFilePathName)) {
                File.Copy(navMeshFilePathName, _serverNavMeshDataPath + navMeshFileName, true);
            }
    }

    private void LoadRecastBuildConfig()
    {
        if (File.Exists(_configFilePathName))
        {
            try
            {
                using (Stream stream = File.Open(_configFilePathName, FileMode.Open))
                {
                    BinaryFormatter binary = new BinaryFormatter();
                    EditorConfig recastBuildConfig = (EditorConfig)binary.Deserialize(stream);
                    _editorConfig = recastBuildConfig;
                }
            }
            catch (Exception)
            {
            }
        }
    }

    private void SaveRecastBuildConfig()
    {
        DirectoryInfo directoryInfo = Directory.GetParent(_configFilePathName);
        if (!directoryInfo.Exists) {
            directoryInfo.Create();
        }

        using (Stream stream = File.Open(_configFilePathName, FileMode.OpenOrCreate))
        {
            BinaryFormatter bin = new BinaryFormatter();
            bin.Serialize(stream, _editorConfig);
        }
    }

    private string RecastBuildConfitToString()
    {
        System.Text.StringBuilder stringBuilder = new System.Text.StringBuilder();
        stringBuilder.AppendFormat("CellSize={0}", _editorConfig.CellSize);
        stringBuilder.AppendFormat(",CellHeight={0}", _editorConfig.CellHeight);
        stringBuilder.AppendFormat(",AgentHeight={0}", _editorConfig.AgentHeight);
        stringBuilder.AppendFormat(",AgentRadius={0}", _editorConfig.AgentRadius);
        stringBuilder.AppendFormat(",AgentMaxClimb={0}", _editorConfig.AgentMaxClimb);
        stringBuilder.AppendFormat(",AgentMaxSlope={0}", _editorConfig.AgentMaxSlope);
        stringBuilder.AppendFormat(",RegionMinSize={0}", _editorConfig.MinRegionSize);
        stringBuilder.AppendFormat(",RegionMergeSize={0}", _editorConfig.RegionMergeSize);
        stringBuilder.AppendFormat(",EdgeMaxLen={0}", _editorConfig.MaxEdgeLength);
        stringBuilder.AppendFormat(",EdgeMaxError={0}", _editorConfig.MaxEdgeError);
        stringBuilder.AppendFormat(",VertsPerPoly={0}", _editorConfig.VerticesPerPoly);
        stringBuilder.AppendFormat(",DetailSampleDist={0}", _editorConfig.DetailSampleDistance);
        stringBuilder.AppendFormat(",DetailSampleMaxError={0}", _editorConfig.DetailSampleMaxError);
        stringBuilder.AppendFormat(",TileSize={0}", _editorConfig.TileSize);
        return stringBuilder.ToString();
    }
    
    private string GetCurrentSceneName()
    {
        string currentSceneName = EditorSceneManager.GetActiveScene().name;
        return currentSceneName.ToLower();
    }

    private int bit(int a, int b)
    {
        return (a & (1 << b)) >> b;
    }

    private Color ToColor(int i, int a)
    {
        int r = bit(i, 1) + bit(i, 3) * 2 + 1;
        int g = bit(i, 2) + bit(i, 4) * 2 + 1;
        int b = bit(i, 0) + bit(i, 5) * 2 + 1;
        return new Color(r * 63 / 255f, g * 63 / 255f, b * 63 / 255f, a / 255f);
    }
}