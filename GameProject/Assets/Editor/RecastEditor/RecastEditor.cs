using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

namespace RecastWrapper
{

    static class RecastConfigMgr
    {
        public static string walkAreaName = "walkArea";
        public static string recastMeshName = "RecastDebugMesh";
        public static string layerMaskName = "Player";
        public static ulong defaultMask = 1;
    }


   // [CustomEditor(typeof(RecastExporter))]
    class RecastExporterEditor : Editor
    {
        bool NeedBlocking = false;
        static Color[] colorCache;
        void OnSceneGUI()
        {
            if (Event.current.type == EventType.MouseDown && Event.current.control && Event.current.button <= 1)
            {
                Ray mouseRay = HandleUtility.GUIPointToWorldRay(Event.current.mousePosition);
                bool ret = Physics.Raycast(mouseRay, out RaycastHit hit, float.MaxValue, 1 << LayerMask.NameToLayer(RecastConfigMgr.layerMaskName));
                if (ret)
                {
                    RecastEditor win = EditorWindow.GetWindow<RecastEditor>();
                    if (Event.current.button == 0)
                        win.RaycastCollider(hit);
                    else
                        win.GetRecastMask(hit);
                }
                else
                    Debug.LogError("δ�͵��������ཻ");

               //NeedBlocking = true;
            }       
            //else if (e.type == EventType.MouseDrag && !Event.current.control)
            //{
            //    if (NeedBlocking)
            //    {
            //        Event.current.Use();
            //        NeedBlocking = false;

            //        Debug.Log("UP");
            //    }
            //}
            //else if (e.type == EventType.Layout)
            //{
            //    HandleUtility.AddDefaultControl(GUIUtility.GetControlID(GetHashCode(), FocusType.Passive));
            //}
        }        
    }


    class RecastEditor : EditorWindow
    {
        #region [construction]
        public RecastEditor()
        {
            titleContent.text = "Recast�༭����";
            titleContent.tooltip = "�ɱ༭RecastMesh��Mask";
            wantsMouseEnterLeaveWindow = false;
            wantsMouseMove = false;
        }
        #endregion

        #region [GUI]
        private void OnGUI()
        {
            if (GUILayout.Button("��ȡRecast����"))
            {
                GetRecastMesh();
                GetFocus();
            }

            if (null == recastGo ||  Selection.activeGameObject == null 
                || Selection.activeGameObject.name != RecastConfigMgr.walkAreaName)
                return;

            if (GUILayout.Button("������洢��������"))
            {
                //RecastBuilder.SaveNavMeshMask();
            }

            GUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("ͿĨ��ɫ");
            paintColor = EditorGUILayout.ColorField(paintColor);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            GUILayout.Label("��������");
            inputMask = EditorGUILayout.LongField(inputMask);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            GUILayout.Label("��ǰ����������");
            GUILayout.Label(clickMask);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("������������������Ϊ"))
            {
                SetAllTrianglesMask();
            }
            allMeshMask = EditorGUILayout.LongField(allMeshMask);            
            GUILayout.EndHorizontal();           
        }
        #endregion

        #region [external Interface]
        /// <summary>
        /// ������������
        /// </summary>
        /// <param name="hit"></param>
        public void RaycastCollider(RaycastHit hit)
        {
            if (null == sharedMesh)
            {
                Debug.LogError("δ��ȡ��Recast����������������ɣ����Ȼ�ȡ��������");
                return;
            }
                        
            ulong umask = (ulong)inputMask;
           // RecastMask[hit.triangleIndex] = umask;
            Color color;
            if (RecastConfigMgr.defaultMask == umask)
                color = Color.white;
            else
                color = paintColor;

            for (int i = 0; i < 3; ++i)
            {
                s_colorCache[hit.triangleIndex * 3 + i] = color;
            }

            sharedMesh.colors = s_colorCache;
        }

        /// <summary>
        /// ��ȡ��������
        /// </summary>
        /// <param name="hit"></param>
        public void GetRecastMask(RaycastHit hit)
        {
            if (null == sharedMesh)
            {
                Debug.LogError("δ��ȡ��Recast����������������ɣ����Ȼ�ȡ��������");
                return;
            }

            //clickMask = RecastMask[hit.triangleIndex].ToString();
        }
        #endregion

        #region [Business]
        /// <summary>
        /// ��ȡRecast����
        /// </summary>
        private void GetRecastMesh()
        {
            recastGo = GameObject.Find(RecastConfigMgr.recastMeshName);
            if (recastGo != null)
            {
                sharedMesh = recastGo.GetComponent<MeshFilter>().sharedMesh;
                sharedMesh.MarkDynamic();
                recastGo.layer = LayerMask.NameToLayer(RecastConfigMgr.layerMaskName);
               // clickMask = RecastMask[0].ToString();
                s_colorCache = sharedMesh.colors;     
            }
            else
                Debug.LogError("δ�ҵ�Recast��������");
        }

        /// <summary>
        /// ��ȡ����
        /// </summary>
        private void GetFocus()
        {
            GameObject walkArea = GameObject.Find(RecastConfigMgr.walkAreaName);
            if (null != walkArea)
            {
                Selection.activeGameObject = walkArea;
                EditorGUIUtility.PingObject(walkArea);
            }
            else
                Debug.LogError("Not Found " + RecastConfigMgr.walkAreaName);
        }

        /// <summary>
        /// ������������Mask
        /// </summary>
        private void SetAllTrianglesMask()
        {
            if(sharedMesh == null)
            {
                Debug.LogError("��ѡ��ȡrecast����");
                return;
            }

            int vertexCount = sharedMesh.vertexCount/3;
            ulong mask = (ulong)allMeshMask;            
            for (int i=0; i < vertexCount; ++i)
            {
               // RecastMask[i] = mask;
                s_colorCache[i * 3] = Color.white;
                s_colorCache[i * 3 + 1] = Color.white;
                s_colorCache[i * 3 + 2] = Color.white;
            }

            sharedMesh.colors = s_colorCache;
            EditorUtility.DisplayDialog("����Mask", "���������Ӱ��������", "ȷ��");
        }
        #endregion

        #region [Property]
        //private ulong[] RecastMask
        //{
        //   get { return RecastBuilder._triangleFlag; }            
        //}
        #endregion

        #region [Fields]
        public Mesh sharedMesh;
        public GameObject recastGo;
        public static Color[] s_colorCache;

        private string clickMask = "1";
        private long inputMask = 2;
        private long allMeshMask = 1;
        private Color paintColor = Color.red;
        #endregion
    }
}
