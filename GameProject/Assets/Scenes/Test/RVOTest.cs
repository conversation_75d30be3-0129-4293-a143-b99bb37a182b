using System.Collections;
using System.Collections.Generic;
using Pathfinding.RVO;
using UnityEngine;

public class RVOTest : MonoBehaviour
{
    private Simulator sim;

    public List<GameObject> players;
    public List<GameObject> targets;

    public List<GameObject> heros;


    private List<IAgent> agents;
    private List<Vector3> goals;
    
    
    Vector2[] interpolatedVelocities;
    Vector2[] interpolatedRotations;
    
    public float maxSpeed = 5f;
    
    // Start is called before the first frame update
    void Start()
    {
        sim = GetComponent<RVOSimulator>().GetSimulator();
        int agentCount = players.Count;
        agents = new List<IAgent>(agentCount);
        goals = new List<Vector3>(agentCount);
        
        for (int i = 0; i < players.Count; i++)
        {
            Vector3 pos = players[i].transform.position;
            IAgent agent = sim.AddAgent(new Vector2(pos.x, pos.z), pos.y);
            agents.Add(agent);
            
            agent.Radius = 0.5f;
            
            // All agents are on the same plane
            agent.ElevationCoordinate = 0;
            agent.DebugDraw = true;

            int targetIndex = 0;
            if (i >= (int) (players.Count / 2))
            {
                targetIndex = 1;
            }
            Vector3 posTarget = targets[targetIndex].transform.position;
            // agent.SetTarget(new Vector2(posTarget.x, posTarget.z), 2, 2);
            goals.Add(posTarget);
        }


        for (int i = 0; i < heros.Count; i ++)
        {
            Vector3 pos = heros[i].transform.position;
            IAgent agent = sim.AddAgent(new Vector2(pos.x, pos.z), pos.y);
            // agents.Add(agent);
            
            agent.Radius = 1.5f;
            
            // All agents are on the same plane
            agent.ElevationCoordinate = 0;
            agent.DebugDraw = true;

            int targetIndex = 1;
 
            Vector3 posTarget = targets[targetIndex].transform.position;
            agent.SetTarget(new Vector2(posTarget.x, posTarget.z), 2, 2);
            // goals.Add(posTarget);
        }
        
    }

    // Update is called once per frame
    void Update()
    {
        
        // Make sure the array is large enough
        if (interpolatedVelocities == null || interpolatedVelocities.Length < agents.Count) {
            var velocities = new Vector2[agents.Count];
            var directions = new Vector2[agents.Count];
            // Copy over the old velocities
            if (interpolatedVelocities != null) for (int i = 0; i < interpolatedVelocities.Length; i++) velocities[i] = interpolatedVelocities[i];
            if (interpolatedRotations != null) for (int i = 0; i < interpolatedRotations.Length; i++) directions[i] = interpolatedRotations[i];
            interpolatedVelocities = velocities;
            interpolatedRotations = directions;
        }

        for (int i = 0; i < agents.Count; i++)
        {
            IAgent agent = agents[i];
            // This is the responsibility of this script, not the RVO system
            Vector2 pos = agent.Position;
            
            var deltaPosition = Vector2.ClampMagnitude(agent.CalculatedTargetPoint - pos, agent.CalculatedSpeed * Time.deltaTime);
            pos += deltaPosition;
            agent.Position = pos;
            
            // Set the desired velocity for all agents
            var target = new Vector2(goals[i].x, goals[i].z);
            var dist = (target - pos).magnitude;
            agent.SetTarget(target, Mathf.Min(dist, maxSpeed), maxSpeed*1.1f);
            
            interpolatedVelocities[i] += deltaPosition;
            if (interpolatedVelocities[i].magnitude > maxSpeed*0.1f) {
                interpolatedVelocities[i] = Vector2.ClampMagnitude(interpolatedVelocities[i], maxSpeed*0.1f);
                interpolatedRotations[i] = Vector2.Lerp(interpolatedRotations[i], interpolatedVelocities[i], agent.CalculatedSpeed * Time.deltaTime*4f);
            }
#if UNITY_EDITOR
            Debug.DrawRay(new Vector3(pos.x, 0, pos.y), new Vector3(interpolatedVelocities[i].x, 0, interpolatedVelocities[i].y) * 10); 
#endif
            players[i].transform.position = new Vector3(agent.Position.x, 0, agent.Position.y);
            players[i].transform.rotation = Quaternion.LookRotation(new Vector3(interpolatedRotations[i].x, 0, interpolatedRotations[i].y));
        }
    }
}
