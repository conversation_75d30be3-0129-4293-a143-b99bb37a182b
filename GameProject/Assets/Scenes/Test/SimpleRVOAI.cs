using System.Collections;
using System.Collections.Generic;
using Pathfinding.RVO;
using UnityEngine;

public class SimpleRVOAI : MonoBehaviour
{

    [SerializeField]
	private GameObject _targetGameObject;

    RVOController controller;

    // Use this for initialization
    void Awake () {
        controller = GetComponent<RVOController>();
    }

    // Update is called once per frame
    public void Update () {
        if (Vector3.Distance(transform.position, _targetGameObject.transform.position) <= 3.0) {
            controller.locked = true;
            return;
        }
        // Just some point far away
        var targetPoint = _targetGameObject.transform.position;

        // Set the desired point to move towards using a desired speed of 10 and a max speed of 12
        controller.SetTarget(targetPoint, 10, 12);

        // Calculate how much to move during this frame
        // This information is based on movement commands from earlier frames
        // as local avoidance is calculated globally at regular intervals by the RVOSimulator component
        var delta = controller.CalculateMovementDelta(transform.position, Time.deltaTime);
        transform.position = transform.position + delta;
    }
}
