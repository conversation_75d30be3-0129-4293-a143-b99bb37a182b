using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using Naiwen.TAA;
using Sultan.Core;
using Sultan.Init;
using Sultan.Manager;
using TextureStreaming;
using UnityEngine;
using UnityEngine.Rendering;

public partial class SROptions 
{
    // Start is called before the first frame update
    [Category("游戏管理"), DisplayName("作弊页面")]
    public void OpenCheat()
    {
        //在这里执行游戏中的逻辑
        LuaManager.Instance.executeString(@"
                                          if uiManager:isShow('Cheat') then
        require_ex(""cheat""):closeCheatUI()
        else
        require_ex(""cheat""):openCheatUI()
        end");
        SRDebug.Instance.HideDebugPanel();
    }
    
    
    public float _rotateSpeed = 1;
    [Category("拼接玩法滑动速度")]
    [NumberRange(0, 10)]
    public float RotateSpeed
    {
        get { return _rotateSpeed; }
        set
        {
            _rotateSpeed = value;
            LuaManager.Instance.executeString($"_G['MontageRotateSpeed'] = {_rotateSpeed * 0.001}");
        }
    }

    [Category("TAA反馈值")]
    public float TAAFeedBackValue
    {
        get { 
        {
            var feature = SceneLoadManager.Instance.GetRenderFeatureByName<TAAFeature>();
            if (feature)
            {
                return feature.FeedBackValue;
            }

            return 0;
        }}

        set
        {
            var feature = SceneLoadManager.Instance.GetRenderFeatureByName<TAAFeature>();
            if (feature)
            {
                feature.FeedBackValue = value;
            }
        }
    }

    [Category("打印流式纹理数据"), DisplayName("打印流式纹理数据")]
    public void PrintTextureStreming()
    {
        CustomTextureStreamingManager.Instance.PrintDebug();
    }

    #if UNITY_IOS && DEVELOPMENT_BUILD && DISABLE_UWA_SDK
    [Category("IOS抓帧")]
    public bool IOSFrameCapture
    {
        get
        {
            return false;
        }
        set
        {
            var obj = GameObject.FindObjectOfType<GameLaunch>();
            if (obj)
            {
                MetalFrameCapturer capturer = obj.gameObject.GetComponent<MetalFrameCapturer>();
                if (!capturer)
                {
                    capturer = obj.gameObject.AddComponent<MetalFrameCapturer>();
                }

                if (capturer)
                {
                    capturer.StartCapture(3);
                }
            }
        }
    }
    #endif
    
    [Category("开启Inspect")]
    public bool EnableInspect
    {
        get {
        {
            var obj = GameObject.FindWithTag("DebugInspect");
            if(obj)
                return obj.activeSelf;
            return false;
        }}

        set
        {
            var obj = GameObject.FindWithTag("DebugInspect");
            if (obj == null && value == true)
            {
                ResManager.Instance.LoadAsset("Debug/DebugInspect.prefab", (asset, op) =>
                {
                    if(asset == null)
                        return;
                    
                    var go = GameObject.Instantiate(asset) as GameObject;
                    go.transform.localPosition = Vector3.zero;
                    go.transform.localScale = Vector3.one;
                    go.transform.localRotation = Quaternion.identity;
                    go.tag = "DebugInspect";
                    go.SetActive(value);
                });
            }else if(obj != null)
            {
                obj.SetActive(value);
            }
        }
    }
   /* 
    [Category("HBAO 偏移值")]
    public float HBAO_AngleBias
    {
        get { 
        {
            
            var ambientOcclusion = VolumeManager.instance.stack.GetComponent<AmbientOcclusion>();
            if (ambientOcclusion)
            {
                return ambientOcclusion.angleBias.value;
            }

            return 0;
        }}

        set
        {
            if(GameObject.Find("Global Volume").TryGetComponent(out Volume volume))
            {
                if (volume.profile.TryGet<AmbientOcclusion>(out var  ambientOcclusion))
                {
                    ambientOcclusion.angleBias.Override(value); 
                }
            }
            
        }
    }

    */

}