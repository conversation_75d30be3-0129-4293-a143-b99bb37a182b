Shader "Aoi/Unlit/MeshToTextureShader"
{
    SubShader
    {
        Tags { "RenderType"="Opaque" }

        Pass
        {
            Cull Off
            ZTest Always
            
            
            CGPROGRAM

            #define MAX_LIGHT 16
            
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"
            #pragma enable_d3d11_debug_symbols

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float3 vertexWS : TEXCOORD1;
            };

            
            float4 _LightPos[16];
            float4 _LightColorAndRange[16];
            float4x4 _LocalToWorld;

            v2f vert (appdata v)
            {

                float3 positionWS = mul(_LocalToWorld, float4(v.vertex.xyz,1));
                
                v2f output;
                float2 texcoord = v.uv.xy;
                texcoord.y = 1.0 - texcoord.y;
                texcoord = texcoord * 2.0 - 1.0;
                output.vertex = float4(texcoord, 0.0, 1.0);
                output.uv = v.uv;
                output.vertexWS = positionWS;
                
                return output;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                float3 color = 0;

                UNITY_UNROLL
                for (int l = 0; l < MAX_LIGHT; l++)
                {
                    float3 lightPos = _LightPos[l].xyz;
                    float4 lightColorAndRange = _LightColorAndRange[l].xyzw;
                    float distanceToLight = distance(i.vertexWS, lightPos);
                    float dis = 1 - saturate(distanceToLight / lightColorAndRange.w);
                    color += lightColorAndRange.xyz * dis;
                }

                return float4(color, 1);
            }
            ENDCG
        }
    }
}
