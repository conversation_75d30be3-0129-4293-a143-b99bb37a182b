// FileName:MeshToTexture.cs
// Author:Aoicocoon
// Date:2025-05-13 15::55::55

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Rendering.Universal;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer))]
public class MeshToTexture : MonoBehaviour
{
    [Header("MaxLight:16")]
    public List<Light> TargetLight = null;
    public int OutputTextureResolution = 16;
    public Texture2D OutputTexture = null;

    public bool DoGenerate = false;
    
    private Material _material = null;

    private void OnValidate()
    {
        if (DoGenerate)
        {
            DoGenerate = false;
            GenerateTexture();
        }
    }

    void GenerateTexture()
    {
        #if UNITY_EDITOR
        if (null == _material)
        {
            _material = new Material(Shader.Find("Aoi/Unlit/MeshToTextureShader"));
        }

        if (null == TargetLight || TargetLight.Count == 0) return;

        int maxLight = 16;

        int loopCount = Mathf.Min(maxLight, TargetLight.Count);
        Vector4[] lightPositions = new Vector4[maxLight];
        Vector4[] lightColorAndRange = new Vector4[maxLight];
        
        for (int i = 0; i < loopCount; i++)
        {
            lightPositions[i] = TargetLight[i].transform.position;
            lightColorAndRange[i] = new Vector4(
                TargetLight[i].color.r,
                TargetLight[i].color.g,
                TargetLight[i].color.b,
                TargetLight[i].range
            );
        }
        
        _material.SetVectorArray("_LightPos", lightPositions.ToArray());
        _material.SetVectorArray("_LightColorAndRange", lightColorAndRange.ToArray());
        _material.SetMatrix("_LocalToWorld", this.transform.localToWorldMatrix);

        int Resolution = OutputTextureResolution;
        var SourceMesh = this.GetComponent<MeshFilter>().sharedMesh;
        
        RenderTexture renderTexture = new RenderTexture(Resolution, Resolution, 0);
        renderTexture.name = "MeshToTexture";
        renderTexture.Create();

       // UnityEditorInternal.RenderDoc.BeginCaptureRenderDoc(RenderDocHelper.GetMainGameView());
        
        RenderTexture currentTexture = RenderTexture.active;
        RenderTexture.active = renderTexture;
        GL.Clear(false, true, Color.black, 1.0f);
        _material.SetPass(0);
        Graphics.DrawMeshNow(SourceMesh, Vector3.zero, Quaternion.identity);
        
        var texture = new Texture2D(Resolution, Resolution, TextureFormat.ARGB32, false);
        RenderTexture.active = renderTexture;
        texture.ReadPixels( new Rect(0, 0, Resolution, Resolution), 0, 0);
        RenderTexture.active = currentTexture;
        byte[] bytes = texture.EncodeToPNG();
        System.IO.File.WriteAllBytes(System.IO.Path.Combine(Application.dataPath, "VertexColors.png"), bytes);
        
        DestroyImmediate(texture);
        renderTexture.Release();
        
        UnityEditor.AssetDatabase.Refresh();
        OutputTexture = UnityEditor.AssetDatabase.LoadAssetAtPath<Texture2D>("Assets/VertexColors.png");
        
        #endif
       // UnityEditorInternal.RenderDoc.EndCaptureRenderDoc(RenderDocHelper.GetMainGameView());

    }
    
    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        
    }
}
