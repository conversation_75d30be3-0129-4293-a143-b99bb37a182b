{"autoInstanceEnum": 66, "ConfigList": [{"fieldList": [{"Name": "GitBranch", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 1}, {"Name": "Platform", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 2}, {"Name": "Date", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 3}, {"Name": "GitSHA", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 4}, {"Name": "Version", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 5}], "autoInstanceOrder": 5, "enumValue": 55, "TypeName": "BuildVersionData", "TypeFullName": ""}, {"fieldList": [{"Name": "Guid", "FieldTypeName": "String", "FieldTypeFullName": "UnityEditor.GUID", "Order": 1}, {"Name": "Path", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 2}, {"Name": "Change", "FieldTypeName": "Boolean", "FieldTypeFullName": "System.Boolean", "Order": 3}, {"Name": "DependencyChange", "FieldTypeName": "Boolean", "FieldTypeFullName": "System.Boolean", "Order": 4}], "autoInstanceOrder": 4, "enumValue": 56, "TypeName": "AssetEntryState", "TypeFullName": ""}, {"fieldList": [{"Name": "dependencies", "FieldTypeName": "ArrayAssetEntryState", "FieldTypeFullName": "Sultan.BuildTools.AssetEntryState[]", "Order": 1}, {"Name": "groupGuid", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 2}, {"Name": "bundleFileId", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 3}, {"Name": "asset", "FieldTypeName": "AssetEntryState", "FieldTypeFullName": "", "Order": 4}], "autoInstanceOrder": 4, "enumValue": 57, "TypeName": "Cached<PERSON><PERSON>tEnt<PERSON>", "TypeFullName": ""}, {"fieldList": [{"Name": "PlayerVersion", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 1}, {"Name": "EditorVersion", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 2}, {"Name": "RemoteCatalogLoadPath", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 3}, {"Name": "CachedInfos", "FieldTypeName": "List_Sultan.BuildTools.CachedAseetEntry", "FieldTypeFullName": "System.Collections.Generic.List`1[[Sultan.BuildTools.CachedAseetEntry, sultan.buildtools.Editor, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "Order": 4}, {"Name": "VersionData", "FieldTypeName": "BuildVersionData", "FieldTypeFullName": "", "Order": 5}], "autoInstanceOrder": 5, "enumValue": 58, "TypeName": "BuildContentData", "TypeFullName": ""}, {"fieldList": [{"Name": "m_Data", "FieldTypeName": "ArrayByte", "FieldTypeFullName": "System.Byte[]", "Order": 1}], "autoInstanceOrder": 1, "enumValue": 60, "TypeName": "SubData", "TypeFullName": ""}, {"fieldList": [{"Name": "m_ExtraData", "FieldTypeName": "Dict_System.String_Sultan.Core.SubData", "FieldTypeFullName": "System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]", "Order": 1}, {"Name": "m_RemovedData", "FieldTypeName": "HashSet`1", "FieldTypeFullName": "", "Order": 2}], "autoInstanceOrder": 2, "enumValue": 59, "TypeName": "FileCombineData", "TypeFullName": ""}, {"fieldList": [{"Name": "Url", "FieldTypeName": "List_System.String", "FieldTypeFullName": "System.Collections.Generic.List`1[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]", "Order": 1}, {"Name": "TestUrl", "FieldTypeName": "List_System.String", "FieldTypeFullName": "System.Collections.Generic.List`1[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]", "Order": 2}, {"Name": "Version", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 3}, {"Name": "RemoteLoadPath", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 4}, {"Name": "WhiteRemoteLoadPath", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 5}], "autoInstanceOrder": 5, "enumValue": 61, "TypeName": "VersionsData", "TypeFullName": ""}, {"fieldList": [{"Name": "textures", "FieldTypeName": "List_SimpleTP.AtlasTextureData", "FieldTypeFullName": "System.Collections.Generic.List`1[[SimpleTP.AtlasTextureData, AppFacade, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "Order": 1}, {"Name": "sprites", "FieldTypeName": "List_SimpleTP.AtlasSpriteData", "FieldTypeFullName": "System.Collections.Generic.List`1[[SimpleTP.AtlasSpriteData, AppFacade, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "Order": 2}], "autoInstanceOrder": 2, "enumValue": 51, "TypeName": "AtlasData", "TypeFullName": ""}, {"fieldList": [{"Name": "name", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 1}, {"Name": "width", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 2}, {"Name": "height", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 3}], "autoInstanceOrder": 3, "enumValue": 52, "TypeName": "AtlasTextureData", "TypeFullName": ""}, {"fieldList": [{"Name": "name", "FieldTypeName": "String", "FieldTypeFullName": "System.String", "Order": 1}, {"Name": "width", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 2}, {"Name": "height", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 3}, {"Name": "subs", "FieldTypeName": "List_SimpleTP.AtlasSpriteSubData", "FieldTypeFullName": "System.Collections.Generic.List`1[[SimpleTP.AtlasSpriteSubData, AppFacade, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "Order": 4}], "autoInstanceOrder": 4, "enumValue": 53, "TypeName": "AtlasSpriteData", "TypeFullName": ""}, {"fieldList": [{"Name": "x", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 1}, {"Name": "y", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 2}, {"Name": "width", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 3}, {"Name": "height", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 4}, {"Name": "uvX", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 5}, {"Name": "uvY", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 6}], "autoInstanceOrder": 6, "enumValue": 54, "TypeName": "AtlasSpriteSubData", "TypeFullName": ""}, {"fieldList": [{"Name": "buildId", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 1}, {"Name": "x", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 2}, {"Name": "y", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 3}], "autoInstanceOrder": 3, "enumValue": 65, "TypeName": "BuildData", "TypeFullName": ""}, {"fieldList": [{"Name": "sizeW", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 1}, {"Name": "sizeH", "FieldTypeName": "Int32", "FieldTypeFullName": "System.Int32", "Order": 2}, {"Name": "blueprintBuildObjDatas", "FieldTypeName": "List_Sultan.BlueprintBuild.BuildData", "FieldTypeFullName": "System.Collections.Generic.List`1[[Sultan.BlueprintBuild.BuildData, AppFacade, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "Order": 3}, {"Name": "floorAreaInfos", "FieldTypeName": "List_Sultan.BlueprintBuild.BuildData", "FieldTypeFullName": "System.Collections.Generic.List`1[[Sultan.BlueprintBuild.BuildData, AppFacade, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "Order": 4}], "autoInstanceOrder": 4, "enumValue": 66, "TypeName": "BlueprintBuildData", "TypeFullName": ""}, {"fieldList": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "FieldTypeName": "UInt32", "FieldTypeFullName": "System.UInt32", "Order": 1}, {"Name": "Max<PERSON>ell", "FieldTypeName": "UInt32", "FieldTypeFullName": "System.UInt32", "Order": 2}, {"Name": "CentenX", "FieldTypeName": "UInt32", "FieldTypeFullName": "System.UInt32", "Order": 3}, {"Name": "CentenY", "FieldTypeName": "UInt32", "FieldTypeFullName": "System.UInt32", "Order": 4}, {"Name": "MaxServerId", "FieldTypeName": "UInt32", "FieldTypeFullName": "System.UInt32", "Order": 5}], "autoInstanceOrder": 5, "enumValue": 62, "TypeName": "AreaConfig", "TypeFullName": ""}, {"fieldList": [{"Name": "Pos", "FieldTypeName": "uint2", "FieldTypeFullName": "Unity.Mathematics.uint2", "Order": 1}, {"Name": "ServerId", "FieldTypeName": "UInt32", "FieldTypeFullName": "System.UInt32", "Order": 2}, {"Name": "Enable", "FieldTypeName": "Boolean", "FieldTypeFullName": "System.Boolean", "Order": 3}, {"Name": "X", "FieldTypeName": "UInt32", "FieldTypeFullName": "", "Order": 4}, {"Name": "Y", "FieldTypeName": "UInt32", "FieldTypeFullName": "", "Order": 5}], "autoInstanceOrder": 5, "enumValue": 63, "TypeName": "AreaInfo", "TypeFullName": ""}, {"fieldList": [{"Name": "KingdomAreaConfig", "FieldTypeName": "AreaConfig", "FieldTypeFullName": "Kingdom.AreaConfig", "Order": 1}, {"Name": "AreaInfos", "FieldTypeName": "List_Kingdom.AreaInfo", "FieldTypeFullName": "System.Collections.Generic.List`1[[Kingdom.AreaInfo, terrain, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "Order": 2}, {"Name": "ServerIdIndex", "FieldTypeName": "Dict_System.UInt32_System.Int32", "FieldTypeFullName": "System.Collections.Generic.Dictionary`2[[System.UInt32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]", "Order": 3}, {"Name": "KingdomCullAreas", "FieldTypeName": "List_Kingdom.KingdomChunkData", "FieldTypeFullName": "", "Order": 4}], "autoInstanceOrder": 4, "enumValue": 64, "TypeName": "KingdomAreaData", "TypeFullName": ""}]}