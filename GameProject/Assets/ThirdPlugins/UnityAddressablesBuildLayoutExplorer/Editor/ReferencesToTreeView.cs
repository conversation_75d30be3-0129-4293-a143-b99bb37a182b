//
// Addressables Build Layout Explorer for Unity. Copyright (c) 2021 <PERSON> (www.console-dev.de). See LICENSE.md
// https://github.com/pschraut/UnityAddressablesBuildLayoutExplorer
//
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

namespace Oddworm.EditorFramework.BuildLayoutExplorer
{
    public class ReferencesToTreeView : ReferencesTreeView
    {
        public ReferencesToTreeView(BuildLayoutWindow window) 
            : base(window)
        {
        }
        
        protected override void OnContextMenu(ContextMenuArgs args)
        {
            base.OnContextMenu(args);
            AddCopyContextMenuItem2(args);
        }
        
        void AddCopyContextMenuItem2(ContextMenuArgs args)
        {
            args.menu.AddItem(new GUIContent("Dependency"), false, OnCopyCellText, new CopyCellTextArgs() { item = args.item, column = args.column });

            void OnCopyCellText(object o)
            {
                var ctx = o as CopyCellTextArgs;
                var name = ctx.item.ToString(ctx.column);
                if (name.EndsWith(".bundle"))
                {
                    var archive = m_TargetObject as RichBuildLayout.Archive;
                    var dependencyArchive = ctx.item.GetObject() as RichBuildLayout.Archive;
                    if (archive == null || dependencyArchive == null)
                        return;
                    // Debug.Log("ctx.item.ToString(ctx.column):" + archive.name + ", :" + dependencyArchive.name);

                    foreach (var asset in archive.explicitAssets)
                    {
                        // foreach (var dependencyAsset in asset.re)
                        foreach (var assetDep in asset.externalReferences)
                        {
                            foreach (var assetInDependencyArachive in dependencyArchive.explicitAssets)
                            {
                                if (assetDep.name == assetInDependencyArachive.name)
                                {
                                    Debug.Log($"[{asset.name}]依赖于:[{assetDep.name}]");
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
