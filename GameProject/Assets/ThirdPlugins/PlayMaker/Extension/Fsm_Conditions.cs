using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    public partial class Fsm
    {
        [SerializeField] 
        private List<ConditionData> startConditionsData = new List<ConditionData>();
        [SerializeField] 
        private List<ConditionData> conditionsData = new List<ConditionData>();
        [SerializeField] 
        private string guideID;

        public ConditionData[] StartConditions => this.startConditionsData.ToArray();
        public ConditionData[] Conditions => this.conditionsData.ToArray();
        public string GuideID => this.guideID;

        public void AddStartConditionData(ConditionData conditionData)
        {
            startConditionsData.Add(ConditionData.CopyFrom(conditionData));
        }

        public void AddConditionData(ConditionData conditionData)
        {
            conditionsData.Add(ConditionData.CopyFrom(conditionData));
        }

        public void RemoveConditionData(ConditionData data)
        {
            if (!startConditionsData.Remove(data))
            {
                conditionsData.Remove(data);
            }
        }
    }

    [Serializable]
    public class ConditionData
    {
        public ConditionType conditionType;
        public List<int> conditionData = new List<int>();
        public CompareType compareType;
        public List<int> compareData = new List<int>();

        public static ConditionData CopyFrom(ConditionData conditionData)
        {
            var data = new ConditionData();
            data.Reset();
            data.conditionType = conditionData.conditionType;
            data.conditionData.AddRange(conditionData.conditionData);
            data.compareType = conditionData.compareType;
            data.compareData.AddRange(conditionData.compareData);
            return data;
        }

        public void Reset()
        {
            conditionType = ConditionType.Resource;
            compareType = CompareType.GreaterEqual;
            conditionData.Clear();
            compareData.Clear();
        }
    }

    [Serializable]
    public enum ConditionType
    {
        None = 0, // 无
        Resource = 1,  // 资源(id,num)
        Lord = 2, // 领主等级(lv)
        Building = 3, // 建筑(id,lv)
        Guide = 4, // 引导打点(step)
        HeroStarLevel = 5, // 英雄星级(starLv)
        HaveBuff = 6, // 拥有buff(buffCfgId)
        // HeroEquipRankLv = 7, // 英雄装备阶级(equipRankLv)
        HeroLv = 8, // 英雄等级
        ScienceLevel = 9, // 科技等级
        RecruitCount = 10, // 招募次数
        ItemCount = 11, // 物品数量
        KillNormalMonterLevelCount = 12, // 击杀普通怪物等级数量
        BarrierComplete = 13, // 副本完成
        TaskComplete = 14,  // 任务完成
        TideComplete = 15,  // 潮汐完成
        PveLevel = 19, // PVE副本通关
        ChapterTaskComplete = 23,  // 整个章节任务完成并领取奖励
        SystemOneTimeAward = 24,  // 系统一性次奖励
        BuildingStartUpgrading = 28, // 建筑触发升级
        LocalStorage = 99, // 本地存储(key, value)
        SystemUnlock = 100, // 系统解锁
        
        AllianceMemberNum = 1001,       // 成员数量
        AllianceTotalPower = 1002,      // 联盟总战力
        AllianceLevel = 1003,           // 联盟等级
        AllianceTechnologyLv = 1004,    // 联盟科技等级
        AllianceHaveBuild = 1005,       // 联盟拥有建筑
    }
    
    [Serializable]
    public enum CompareType 
    { 
        None = 0,   // 无
        GreaterEqual = 1, // 大于等于
        Equal = 2,  // 等于
        LessEqual = 3, // 小于等于
        Greater = 4, // 大于
        Less = 5, // 小于
        NotEqual = 6, // 不等于
        Range = 7, // 范围内,包含边界[],data需要配置两个参数
    }
}
