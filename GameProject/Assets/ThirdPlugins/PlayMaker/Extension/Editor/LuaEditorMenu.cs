using System;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using HutongGames.PlayMaker;
using HutongGames.PlayMakerEditor;
using Sultan;
using Sultan.Init;
using UnityEditor.Callbacks;
using UnityEngine.SceneManagement;

namespace HutongGames.PlayMakerEditor
{
    public static class LuaEditorMenu
    {
        private static string KEY_LOAD_FROM_LUA = "_KEY_LOAD_FROM_LUA_";

        [MenuItem("Tools/配置加载方式/从lua读取", true)]
        public static bool OnCheckLoadFromCI()
        {
            var loadFromLua = PlayerPrefs.GetInt(KEY_LOAD_FROM_LUA) > 0;
            Menu.SetChecked("Tools/配置加载方式/从lua读取", loadFromLua);
            return true;
        }
        
        [MenuItem("Tools/配置加载方式/从lua读取")]
        public static void OnLoadFromCI()
        {
            var loadFromLua = PlayerPrefs.GetInt(KEY_LOAD_FROM_LUA) > 0;
            PlayerPrefs.SetInt(KEY_LOAD_FROM_LUA, loadFromLua ?  1 : 0);
        }
    }
}