using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    public static class FsmVariableExtension
    {
        public static string ToLua(this FsmVector3 v)
        {
            return string.Format("cs.Vector3({0}, {1}, {2})", v.Value.x, v.Value.y, v.Value.z);
        }

        public static string ToLua(this FsmVector2 v)
        {
            return string.Format("cs.Vector2({0}, {1})", v.Value.x, v.Value.y);
        }

        public static string ToLua(this FsmColor v)
        {
            return string.Format("cs.Color({0}, {1}, {2}, {3})", v.Value.r, v.Value.g, v.Value.b, v.Value.a);
        }

        public static string ToLua(this FsmString v)
        {
            return string.Format("{\"{0}\"", v.Value);
        }

        public static string ToLua(this FsmBool v)
        {
            return v.Value.ToString().ToLower();
        }

        public static string To<PERSON><PERSON>(this FsmFloat v)
        {
            return v.ToString();
        }

        public static string To<PERSON>ua(this FsmInt v)
        {
            return v.ToString();
        }

        public static string To<PERSON>ua(this FsmVar v)
        {
            switch (v.Type)
            {
                case VariableType.Vector3:
                    return string.Format("cs.Vector3({0}, {1}, {2})", v.vector3Value.x, v.vector3Value.y,
                        v.vector3Value.z);
                case VariableType.Vector2:
                    return string.Format("cs.Vector2({0}, {1})", v.vector2Value.x, v.vector2Value.y);
                case VariableType.Color:
                    return string.Format("cs.Color({0}, {1}, {2}, {3})", v.colorValue.r, v.colorValue.g, v.colorValue.b,
                        v.colorValue.a);
                case VariableType.Quaternion:
                    var eulerAngles = v.quaternionValue.eulerAngles;
                    return string.Format("cs.Vector3({0}, {1}, {2})", eulerAngles.x, eulerAngles.y, eulerAngles.z);
                case VariableType.String:
                    return string.Format("\"{0}\"", v.stringValue);
                case VariableType.Bool:
                    return v.boolValue.ToString().ToLower();
            }

            return v.GetValue().ToString();
        }
    }
}
