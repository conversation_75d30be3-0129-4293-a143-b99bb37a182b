using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    public class FsmEventCenter : Singleton<FsmEventCenter>
    {
        public event Action<string> OnFinishEvent;
        public event Action<string> OnStartEvent;
        public event Action<string> OnEnterStateEvent;
        
        public void FireFinishEvent(string actionID)
        {
            OnFinishEvent?.Invoke(actionID);
        }

        public void FireStartEvent(string actionID)
        {
            OnStartEvent?.Invoke(actionID);
        }

        public void FireStateEvent(string stateID)
        {
            OnEnterStateEvent?.Invoke(stateID);
        }
    }
}
