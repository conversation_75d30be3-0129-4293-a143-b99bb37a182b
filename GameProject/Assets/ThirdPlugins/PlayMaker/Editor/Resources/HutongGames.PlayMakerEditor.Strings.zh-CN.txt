<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="WelcomeWindow_Forums" xml:space="preserve">
    <value>论坛</value>
  </data>
  <data name="FsmEditorSettings_Debug_Look_At_Color" xml:space="preserve">
    <value>调试Look At颜色</value>
  </data>
  <data name="FsmEditorSettings_Draw_Playmaker_Gizmos_in_Scene_View" xml:space="preserve">
    <value>在场景视图中绘制Playmaker线框</value>
  </data>
  <data name="Menu_Preview_Version" xml:space="preserve">
    <value>预览版本-编辑已禁用</value>
  </data>
  <data name="Label_GameObject" xml:space="preserve">
    <value>GameObject（游戏物体）</value>
  </data>
  <data name="FsmEditorSettings_Link_Style_Tooltip" xml:space="preserve">
    <value>用于绘制各状态间的链接的默认样式。可以每个过渡不同。</value>
  </data>
  <data name="Label_KeepDelayedEvents" xml:space="preserve">
    <value>在状态中退出时保留延迟的事件</value>
  </data>
  <data name="VersionInfo_Indie_Version_Notes" xml:space="preserve">
    <value>独立版本： 你不能使自定义动作，但在其他方面功能完整。无水印。</value>
  </data>
  <data name="Menu_New_Event" xml:space="preserve">
    <value>新事件......</value>
  </data>
  <data name="DebugToolbar_Label_Debug_Tooltip" xml:space="preserve">
    <value>调试选项</value>
  </data>
  <data name="Tooltip_EventManager_Edit_Event" xml:space="preserve">
    <value>编辑选定的事件。注意： 这只重命名此状态机中的事件。使用事件浏览器重命名全项目的事件。</value>
  </data>
  <data name="CustomActionWizard_Add_Methods" xml:space="preserve">
    <value>添加方法</value>
  </data>
  <data name="Dialog_Delete_Variable_Are_you_sure" xml:space="preserve">
    <value>此变量已被使用 ！是否确实要删除它？</value>
  </data>
  <data name="ActionEditor_EditFsmEventTarget_FSM_Name_Tooltip" xml:space="preserve">
    <value>游戏对象上状态机的可选名称。如果游戏对象具有多个状态机，使用该选项。</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Prefab_Restrictions_Tooltip" xml:space="preserve">
    <value>场景中对象Prefab引用检查。</value>
  </data>
  <data name="FsmEditorSettings_Load_All_PlayMakerFSM_Prefabs_When_Refactoring" xml:space="preserve">
    <value>重构时加载所有 PlayMakerFSM Prefabs。</value>
  </data>
  <data name="Hint_Broadcast_Event" xml:space="preserve">
    <value>广播事件到所有的状态机。注意： 事件必须在事件浏览器或事件检查面板中被标记为全局事件。</value>
  </data>
  <data name="ActionReportWindow_Action_Changes_Title" xml:space="preserve">
    <value>动作更改：</value>
  </data>
  <data name="Menu_Step_Single_Frame" xml:space="preserve">
    <value>步进一帧</value>
  </data>
  <data name="Tooltip_Globals" xml:space="preserve">
    <value>此状态机所使用的全局变量。</value>
  </data>
  <data name="FsmEditorSettings_Jump_to_Breakpoint_Error" xml:space="preserve">
    <value>跳转到断点或错误</value>
  </data>
  <data name="Menu_Unused_Variable" xml:space="preserve">
    <value>没有任何状态使用此变量...</value>
  </data>
  <data name="Dialog_Option_Continue" xml:space="preserve">
    <value>继续</value>
  </data>
  <data name="Dialogs_PREVIEW_VERSION" xml:space="preserve">
    <value>预览版本</value>
  </data>
  <data name="FsmEditorSettings_Enable_Transition_Effects" xml:space="preserve">
    <value>启用过渡效果</value>
  </data>
  <data name="WelcomeWindow_Photon_Cloud" xml:space="preserve">
    <value>Photon Cloud</value>
  </data>
  <data name="FsmEditorSettings_Link_Style" xml:space="preserve">
    <value>链接样式</value>
  </data>
  <data name="Label_Controls" xml:space="preserve">
    <value>控制</value>
  </data>
  <data name="FsmEditorSettings_DoGraphViewSettings_Tooltip" xml:space="preserve">
    <value>默认情况下鼠标滚轮缩放图表视图。
如果您希望是滚动图表视图，并使用 Ctrl + 鼠标滚轮进行缩放，请选中此选项。
注意： 这也适用于触控板和鼠标手势。</value>
  </data>
  <data name="WelcomeWindow_Tutorials" xml:space="preserve">
    <value>视频教程</value>
  </data>
  <data name="FsmErrorChecker_PrefabReferencingSceneObjectError" xml:space="preserve">
    <value>Prefab预设不应引用场景对象，只能实用其他Prefab预设和项目资源。对场景对象的引用都将在保存或加载后丢失 ！</value>
  </data>
  <data name="Dialog_Replace_Start_State" xml:space="preserve">
    <value>替换起始状态吗？</value>
  </data>
  <data name="Command_Set_Transition_Target" xml:space="preserve">
    <value>设置过渡目标</value>
  </data>
  <data name="DragAndDrop_AddAction" xml:space="preserve">
    <value>添加动作</value>
  </data>
  <data name="Variable" xml:space="preserve">
    <value>变量</value>
  </data>
  <data name="DebugToolbar_Button_Next_Tooltip" xml:space="preserve">
    <value>下一个过渡</value>
  </data>
  <data name="Dialog_Are_you_sure" xml:space="preserve">
    <value>是否确定？</value>
  </data>
  <data name="Label_Camera_Distance" xml:space="preserve">
    <value>距离</value>
  </data>
  <data name="Dialog_Save_Template" xml:space="preserve">
    <value>保存模板</value>
  </data>
  <data name="CustomActionWizard_Select_Category" xml:space="preserve">
    <value>选择类别</value>
  </data>
  <data name="Label_Global_Variables" xml:space="preserve">
    <value>全局变量</value>
  </data>
  <data name="Hint_Send_Event_to_FsmComponent" xml:space="preserve">
    <value>直接向 PlayMakerFSM 组件发送事件。选择或将组件拖到该字段。注意： 事件必须在事件浏览器或事件检查面板中被标记为全局事件。</value>
  </data>
  <data name="CustomActionWizard_Generated_Code_Folder" xml:space="preserve">
    <value>存储生成代码的文件夹</value>
  </data>
  <data name="Command_Set_State_Color" xml:space="preserve">
    <value>设置状态颜色</value>
  </data>
  <data name="CustomActionWizard_Find_File_Tooltip" xml:space="preserve">
    <value>找到项目视图中的脚本文件。</value>
  </data>
  <data name="Menu_None" xml:space="preserve">
    <value>无</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Missing_Actions" xml:space="preserve">
    <value>检查丢失的动作</value>
  </data>
  <data name="ToolWindow_Title" xml:space="preserve">
    <value>工具</value>
  </data>
  <data name="Menu_Paste_Actions_Replace" xml:space="preserve">
    <value>粘贴替换动作</value>
  </data>
  <data name="WelcomeWindow_Addons" xml:space="preserve">
    <value>扩展包</value>
  </data>
  <data name="Menu_Check_for_Missing_Actions" xml:space="preserve">
    <value>检查丢失的动作</value>
  </data>
  <data name="ActionEditor_EditFsmProperty_Target_Object_Tooltip" xml:space="preserve">
    <value>选择一个目标对象</value>
  </data>
  <data name="Menu_Enable_Breakpoints" xml:space="preserve">
    <value>启用断点</value>
  </data>
  <data name="FsmEditorSettings_Default_Screenshots_Path" xml:space="preserve">
    <value>PlayMaker/截图/</value>
  </data>
  <data name="Tooltip_ManualUpdate" xml:space="preserve">
    <value>不要使用 MonoBehaviour.Update() 来更新状态机。相反你可以手动更新状态机，你可以随时使用 PlayMakerFSM.Fsm.Update。例如，您可以在协程中使用这更新状态机。</value>
  </data>
  <data name="ActionEditor_EditLayoutOption_Option" xml:space="preserve">
    <value>选项</value>
  </data>
  <data name="ActionEditor_Add_Component_XXX" xml:space="preserve">
    <value>添加组件: {0}</value>
  </data>
  <data name="Label_Editing_Prefab_Instance" xml:space="preserve">
    <value>编辑预设的实例。请参阅首选项。</value>
  </data>
  <data name="FsmEditorSettings_Show_Scrollbars_All_The_Time_Tooltip" xml:space="preserve">
    <value>当禁用时，只有当您拖动设计区时显示滚动条。注： 使用鼠标中键来拖动设计区。</value>
  </data>
  <data name="ToolWindow_Header_Transition_Tools" xml:space="preserve">
    <value>过渡工具：</value>
  </data>
  <data name="AboutPlaymaker_Special_Thanks" xml:space="preserve">
    <value>特别感谢: {0}</value>
  </data>
  <data name="BugReportWindow_How_often_does_it_happen" xml:space="preserve">
    <value>发生的频率如何</value>
  </data>
  <data name="Label_Note_disable_in_preferences" xml:space="preserve">
    <value>注： 您可以在首选项中关闭此项。</value>
  </data>
  <data name="Hint_Inspector_Usage" xml:space="preserve">
    <value>将变量和事件显示在PlayMaker状态机检查面板上来生成一个自定义的状态机控制面板。</value>
  </data>
  <data name="CustomActionWizard_Error_Could_not_create_directory" xml:space="preserve">
    <value>无法创建目录：</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Duplicate_Transition_Events" xml:space="preserve">
    <value>检查重复转换事件</value>
  </data>
  <data name="Tooltip_Use_Variable" xml:space="preserve">
    <value>使用变量</value>
  </data>
  <data name="ActionEditor_Method_Name" xml:space="preserve">
    <value>方法名称</value>
  </data>
  <data name="VersionInfo_NaCL_Version_Notes" xml:space="preserve">
    <value>NaCl 版本：在NaCl 版本中网络组件不支持</value>
  </data>
  <data name="FsmEditorSettings_Show_FSM_Description_in_Graph_View" xml:space="preserve">
    <value>在图表视图中显示状态机描述</value>
  </data>
  <data name="FsmEditorSettings_Enable_DebugFlow_Tooltip" xml:space="preserve">
    <value>DebugFlow 模式中开启记录变量和其他状态信息。这是一个全局的设置；如果启用后，你仍可以在单独的状态机启用或禁用他。注意： 禁用可以提高编辑器性能，在生成的程序中永远都是禁用的。</value>
  </data>
  <data name="ErrorSelector_Filter_Selected_FSM" xml:space="preserve">
    <value>仅显示选定状态机中的错误。</value>
  </data>
  <data name="Command_Export_Globals" xml:space="preserve">
    <value>导出全局变量</value>
  </data>
  <data name="GlobalVariablesWindow_Refresh_Tooltip" xml:space="preserve">
    <value>重新计算用法。可能需要几秒钟......</value>
  </data>
  <data name="ActionEditor_Send_Event_to_FSM_on_GameObject_Tooltip" xml:space="preserve">
    <value>将事件发送到特定游戏对象的状态机上。</value>
  </data>
  <data name="Label_This_FSM_is_Locked" xml:space="preserve">
    <value>该状态机处于锁定状态。输入密码并按解锁按钮。</value>
  </data>
  <data name="FsmEditorSettings_Default_State_Name" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="Hint_Enable_inspector_when_playing" xml:space="preserve">
    <value>播放时启用检查面板</value>
  </data>
  <data name="Menu_Check_for_Transitions_Missing_Events" xml:space="preserve">
    <value>检查丢失事件设置的转换</value>
  </data>
  <data name="Dialog_Delete_Extra_PlayMakerGUI_GameObject" xml:space="preserve">
    <value>删除： {0}？</value>
  </data>
  <data name="Menu_Debug_Variable_Values" xml:space="preserve">
    <value>调试变量值</value>
  </data>
  <data name="Error_Event_already_used" xml:space="preserve">
    <value>已使用的事件 ！</value>
  </data>
  <data name="FsmSelector_no_active_state" xml:space="preserve">
    <value>[没有活动的状态]</value>
  </data>
  <data name="Error_Some_actions_have_changed_since_FSMs_were_saved" xml:space="preserve">
    <value>一些动作在状态机保存后产生了更改！请检查PlayMaker控制台中的错误......</value>
  </data>
  <data name="File_Invalid_Path" xml:space="preserve">
    <value>无效的路径: {0}</value>
  </data>
  <data name="Label_Password" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="Label_Prefab_postfix" xml:space="preserve">
    <value> (Prefab预设)</value>
  </data>
  <data name="Menu_Close_Window_After_Adding_Action" xml:space="preserve">
    <value>添加动作后关闭窗口</value>
  </data>
  <data name="Command_Add_FSM_to_Selected" xml:space="preserve">
    <value>给选择的物体添加状态机</value>
  </data>
  <data name="Menu_Create_Object" xml:space="preserve">
    <value>创建对象</value>
  </data>
  <data name="Hint_EventManager_Expose_Events" xml:space="preserve">
    <value>将PlayMaker状态机的事件和变量显示在检查面板选项，来建立一个状态机的自定义控制面板。</value>
  </data>
  <data name="Hint_State_Inspector" xml:space="preserve">
    <value>此面板允许您编辑状态。
在图表视图中选择或下面用鼠标右键单击选择一种状态......</value>
  </data>
  <data name="ActionUtility_AddAction_Missing_Action" xml:space="preserve">
    <value>PlayMaker缺少动作: {0}</value>
  </data>
  <data name="Menu_Paste_Variables" xml:space="preserve">
    <value>粘贴变量</value>
  </data>
  <data name="VersionInfo_INDIE_VERSION" xml:space="preserve">
    <value>独立版本</value>
  </data>
  <data name="Menu_Disable_Error_Checker_When_Game_Is_Playing" xml:space="preserve">
    <value>在游戏运行时禁用错误检查</value>
  </data>
  <data name="BugReportWindow_Submit_Button" xml:space="preserve">
    <value>提交报告</value>
  </data>
  <data name="DebugToolbar_Error_Count_Tooltip" xml:space="preserve">
    <value>错误检查</value>
  </data>
  <data name="Tooltip_Variable_Type" xml:space="preserve">
    <value>查看或编辑变量的类型。</value>
  </data>
  <data name="Tooltip_Variable_Name" xml:space="preserve">
    <value>用鼠标右键单击变量，浏览使用该变量的状态。</value>
  </data>
  <data name="FsmEditorSettings_Translators" xml:space="preserve">
    <value>翻译：</value>
  </data>
  <data name="ActionEditor_Store_Enum" xml:space="preserve">
    <value>存储枚举</value>
  </data>
  <data name="ActionEditor_Store_Bool" xml:space="preserve">
    <value>存储布尔值</value>
  </data>
  <data name="FsmEditorSettings_Max_State_Width_Tooltip" xml:space="preserve">
    <value>状态框可以自动增长，以适应其内容。默认值是 200。</value>
  </data>
  <data name="ActionEditor_Store_Rect" xml:space="preserve">
    <value>存储 Rect</value>
  </data>
  <data name="Menu_No_Animator_Parameters" xml:space="preserve">
    <value>没有 {0} 参数...</value>
  </data>
  <data name="Command_Move_Actions" xml:space="preserve">
    <value>移动动作</value>
  </data>
  <data name="ActionEditor_Store_Vector2" xml:space="preserve">
    <value>存储 Vector2（二维向量）</value>
  </data>
  <data name="ActionEditor_Store_Vector3" xml:space="preserve">
    <value>存储 Vector3</value>
  </data>
  <data name="Menu_GraphView_Set_Start_State" xml:space="preserve">
    <value>设置为起始状态</value>
  </data>
  <data name="Command_Reset_Action" xml:space="preserve">
    <value>重置动作</value>
  </data>
  <data name="DebugToolbar_Button_Prev_Toolrip" xml:space="preserve">
    <value>以前的过渡</value>
  </data>
  <data name="Dialog_Make_Local_Variable" xml:space="preserve">
    <value>使变量本地化</value>
  </data>
  <data name="VersionInfo_Student_Version_Notes" xml:space="preserve">
    <value>学生版： 你不能使自定义动作，但在其他方面功能完整。
注意： 只能用于非商业用途 ！</value>
  </data>
  <data name="Menu_GraphView_Delete_Template" xml:space="preserve">
    <value>删除模板</value>
  </data>
  <data name="Dialog_No_unused_variables___" xml:space="preserve">
    <value>没有未使用的变量...</value>
  </data>
  <data name="Error_Missing_Action__Get_Property" xml:space="preserve">
    <value>PlayMaker缺少动作： Get Property获取属性</value>
  </data>
  <data name="Menu_Copy_Selected_Actions" xml:space="preserve">
    <value>复制选定的动作</value>
  </data>
  <data name="FsmErrorChecker_DuplicateTransitionEventError" xml:space="preserve">
    <value>在同一个状态中，同一个转换被使用多次！</value>
  </data>
  <data name="FsmEditorSettings_Select_Game_Objects_With_FSMs_in_Game_View_Tooltip" xml:space="preserve">
    <value>允许您通过单击正在运行中的游戏物体来选择其状态机。注意： 仅适用于带碰撞的游戏物体。</value>
  </data>
  <data name="FsmEditorSettings_Enable_Watermarks" xml:space="preserve">
    <value>启用水印</value>
  </data>
  <data name="FsmEditorSettings_Disable_PlayMaker_Editor_When_Game_Is_Playing_Tooltip" xml:space="preserve">
    <value>如果您发现编辑器中运行的性能太差，您可以关闭这个选项。</value>
  </data>
  <data name="FsmEditorSettings_Color_Links_With_State_Color" xml:space="preserve">
    <value>状态颜色与连接颜色</value>
  </data>
  <data name="Command_Enable_Action" xml:space="preserve">
    <value>启用动作</value>
  </data>
  <data name="Label_Confirm_Editing_Prefab_Instances" xml:space="preserve">
    <value>确认编辑预设的实例</value>
  </data>
  <data name="Menu_None_FSM" xml:space="preserve">
    <value>无 (状态机)</value>
  </data>
  <data name="FsmEditorSettings_New_State_Name_Tooltip" xml:space="preserve">
    <value>新状态的默认名称。注： 每个状态机中状态名称必须是唯一的。状态将自动命名并在必要时追加数字。</value>
  </data>
  <data name="Menu_Add_Event_to_FSM" xml:space="preserve">
    <value>将事件添加到状态机</value>
  </data>
  <data name="Label_Editor_disabled_when_playing" xml:space="preserve">
    <value>播放时禁用编辑器。请参阅首选项。</value>
  </data>
  <data name="Command_Toggle_Breakpoint" xml:space="preserve">
    <value>切换断点</value>
  </data>
  <data name="Menu_Auto_Refresh_Globals" xml:space="preserve">
    <value>焦点自动刷新</value>
  </data>
  <data name="Menu_Move_To_Global_Variables" xml:space="preserve">
    <value>移动到全局变量</value>
  </data>
  <data name="Tooltip_Use_Template" xml:space="preserve">
    <value>模板使您可以在游戏物体之间共享相同的状态机。</value>
  </data>
  <data name="Hint_Send_Event_to_FSM_on_GameObject" xml:space="preserve">
    <value>将事件发送到所有GameObject的状态机 上。注意： 事件必须在事件浏览器或事件检查面板中被标记为全局事件。</value>
  </data>
  <data name="FsmEditorSettings_DoGraphViewSettings_Zoom_Speed_Tooltip" xml:space="preserve">
    <value>控制图表视图缩放速度。</value>
  </data>
  <data name="Menu_Buy_Playmaker" xml:space="preserve">
    <value>在HutongGames.com购买PlayMaker</value>
  </data>
  <data name="FsmTemplateEditor_Open_In_Editor" xml:space="preserve">
    <value>在PlayMaker状态机编辑器中进行编辑</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Required_Components" xml:space="preserve">
    <value>检查所需的组件</value>
  </data>
  <data name="Label_Postfix_FSMs_Plural" xml:space="preserve">
    <value>状态机！</value>
  </data>
  <data name="Label_Watermarks_Are_Disabled" xml:space="preserve">
    <value>在首选项中水印被禁用。</value>
  </data>
  <data name="Dialog_Add_FSM_to_multiple_objects_" xml:space="preserve">
    <value>给多个对象添加状态机吗？</value>
  </data>
  <data name="Menu_FSM_Browser" xml:space="preserve">
    <value>状态机浏览器</value>
  </data>
  <data name="Label_Preview_GUI_Actions_While_Editing" xml:space="preserve">
    <value>编辑时预览 GUI 动作</value>
  </data>
  <data name="Hint_DebugFlow" xml:space="preserve">
    <value>Debug Flow模式：当进入状态时，动作面板显示变量的值</value>
  </data>
  <data name="Dialog_Editing_Prefab_while_game_is_running" xml:space="preserve">
    <value>当游戏正在播放时，您正在编辑Prefab。更改将在您停止播放后保留！</value>
  </data>
  <data name="ActionSelector_Title" xml:space="preserve">
    <value>动作</value>
  </data>
  <data name="FsmEditorSettings_Scrolling" xml:space="preserve">
    <value>滚动</value>
  </data>
  <data name="Menu_GlobalsRoot" xml:space="preserve">
    <value>全局/</value>
  </data>
  <data name="Command_Delete_All_Actions" xml:space="preserve">
    <value>删除所有动作</value>
  </data>
  <data name="Variable_Tooltip_Warning" xml:space="preserve">
    <value>注意 ︰ 不能重命名全局变量，因为他们可能在其他场景中使用。目前你要创建一个新的变量来引用它。我们将在以后的更新中添加重命名功能。</value>
  </data>
  <data name="FsmGraphView_Click_to_Edit_Template" xml:space="preserve">
    <value>[单击此处编辑模板]</value>
  </data>
  <data name="Hint_Use_Event_Browser_to_rename_globally" xml:space="preserve">
    <value>注意： 使用事件浏览器来重命名事件会影响整个项目。编辑下面的名称仅影响此状态机。</value>
  </data>
  <data name="FsmEditorSettings_Color_Scheme_Tooltip" xml:space="preserve">
    <value>匹配Pro版或Indie版默认的外观配色方案。</value>
  </data>
  <data name="FsmEditorSettings_Select_State_On_Activated" xml:space="preserve">
    <value>激活时选择状态</value>
  </data>
  <data name="Tooltip_New_Variable" xml:space="preserve">
    <value>将变量添加到此状态机。变量由动作使用来存储信息</value>
  </data>
  <data name="Tooltip_Global_Variables_Header" xml:space="preserve">
    <value>单击此项可按名称进行排序。
用鼠标右键单击变量以查看使用该变量的状态机。</value>
  </data>
  <data name="Hint_Action_Browser_Workflow" xml:space="preserve">
    <value>工作流程提示：打开动作浏览器，在动作列表中输入文字筛选，使用向上/向下箭头键来选择一项动作，并按 enter 键，以将其添加到状态。新动作会插入到列表所选动作之前。</value>
  </data>
  <data name="Menu_Check_for_Prefab_Restrictions" xml:space="preserve">
    <value>Prefab限制检查</value>
  </data>
  <data name="Menu_Auto_Name" xml:space="preserve">
    <value>自动命名</value>
  </data>
  <data name="Menu_Select_Prefab_Parent" xml:space="preserve">
    <value>选择Prefab预设父物体</value>
  </data>
  <data name="SettingsButton_Tooltip" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="FsmSelector_Title" xml:space="preserve">
    <value>状态机浏览器</value>
  </data>
  <data name="FsmEditorSettings_Debug_Raypick_Color" xml:space="preserve">
    <value>调试 Raypick 颜色</value>
  </data>
  <data name="Command_Select_GameObject" xml:space="preserve">
    <value>选择 GameObject</value>
  </data>
  <data name="ActionReportWindow_Sort_By_FSM" xml:space="preserve">
    <value>按状态机排序</value>
  </data>
  <data name="Label_Event_Name" xml:space="preserve">
    <value>事件名称</value>
  </data>
  <data name="Hint_Select_FSM" xml:space="preserve">
    <value>选择状态机...</value>
  </data>
  <data name="Error_Failed_to_load_Object_Property_Drawer" xml:space="preserve">
    <value>未能加载对象属性绘制检查面板参数类型。</value>
  </data>
  <data name="CustomActionWizard_Invalid_Action_Name" xml:space="preserve">
    <value>请输入一个有效的动作名称。</value>
  </data>
  <data name="Dialog_Import_Globals" xml:space="preserve">
    <value>导入全局变量</value>
  </data>
  <data name="Label_Model_Prefab_Instance_disconnected" xml:space="preserve">
    <value>模型Prefab预设实例 （断开连接）</value>
  </data>
  <data name="Command_Move_Down" xml:space="preserve">
    <value>向下移动</value>
  </data>
  <data name="FsmEditorSettings_Load_All_PlayMakerFSM_Prefabs_When_Refactoring_Tooltip" xml:space="preserve">
    <value>重命名事件和变量时加载项目中所有的prefabs。</value>
  </data>
  <data name="Label_States_Count" xml:space="preserve">
    <value>状态 [{0}]</value>
  </data>
  <data name="Menu_Check_for_Required_Components" xml:space="preserve">
    <value>检查所需的组件</value>
  </data>
  <data name="Tooltip_Max_Loop_Override" xml:space="preserve">
    <value>覆盖默认的最大循环次数阈值1000。设置为0时保持默认值。</value>
  </data>
  <data name="BugReportWindow_MissingEmail" xml:space="preserve">
    <value>请输入您的电子邮件地址</value>
  </data>
  <data name="Error_Multiple_PlayMakerGUI_components" xml:space="preserve">
    <value>场景中有多个 PlayMakerGUI ！删除其他实例吗？</value>
  </data>
  <data name="Tooltip_Action_Browser" xml:space="preserve">
    <value>使用动作浏览器添加动作到状态中。</value>
  </data>
  <data name="Menu_Disable_Window_When_Playing" xml:space="preserve">
    <value>播放时禁用窗口</value>
  </data>
  <data name="BugReportWindow_MissingTitle" xml:space="preserve">
    <value>请输入标题</value>
  </data>
  <data name="ErrorSelector_Filter_Selected_FSM_Only" xml:space="preserve">
    <value>只选定状态机</value>
  </data>
  <data name="Label_Select_GameObject" xml:space="preserve">
    <value>选择 GameObject</value>
  </data>
  <data name="Menu_Online_Help" xml:space="preserve">
    <value>联机帮助</value>
  </data>
  <data name="Label_Manual_Update" xml:space="preserve">
    <value>手动更新</value>
  </data>
  <data name="Menu_Delete_Category" xml:space="preserve">
    <value>删除类别</value>
  </data>
  <data name="Label_Importing_" xml:space="preserve">
    <value>导入 ︰ </value>
  </data>
  <data name="Command_Lock_Selected_FSM" xml:space="preserve">
    <value>锁定</value>
  </data>
  <data name="Label_Reset_On_Disable" xml:space="preserve">
    <value>禁用时重置</value>
  </data>
  <data name="Menu_Paste_Actions_After" xml:space="preserve">
    <value>在之后粘贴动作</value>
  </data>
  <data name="FsmEditorSettings_Show_Comments_in_Graph_View" xml:space="preserve">
    <value>在图表视图显示备注</value>
  </data>
  <data name="Menu_Show_Full_FSM_Path" xml:space="preserve">
    <value>显示完整的状态机路径</value>
  </data>
  <data name="Hint_Network_Sync_Variables" xml:space="preserve">
    <value>勾选变量中的NetworkSync选项，以自动将变量同步到网络中。
注意： GameObject 需要一个Network View并且监视PlayMakerFSM (拖动 PlayMakerFSM 组件到被观察字段)。</value>
  </data>
  <data name="Hint_System_Events_cannot_be_renamed" xml:space="preserve">
    <value>注意： 系统事件不能重命名 ！</value>
  </data>
  <data name="Dialog_Edit_Variable_Type_Are_you_sure" xml:space="preserve">
    <value>此变量已被使用 ！确实要更改其类型吗?</value>
  </data>
  <data name="Menu_Step_To_Next_State_Change_in_this_FSM" xml:space="preserve">
    <value>步进到此状态机中下一个改变的状态</value>
  </data>
  <data name="Menu_Move_Action_To_Bottom" xml:space="preserve">
    <value>将动作移到底部</value>
  </data>
  <data name="Label_None_In_Table" xml:space="preserve">
    <value>[无]</value>
  </data>
  <data name="Label_Enable_Tool_Windows_When_Playing" xml:space="preserve">
    <value>播放时启用工具窗口</value>
  </data>
  <data name="Hint_Use_Action_Browser_To_Add_Actions" xml:space="preserve">
    <value>使用动作浏览器添加动作到状态中。</value>
  </data>
  <data name="Dialog_Rename_Event" xml:space="preserve">
    <value>重命名事件</value>
  </data>
  <data name="Menu_Add_After_Selected_Action" xml:space="preserve">
    <value>添加到选定的动作之后</value>
  </data>
  <data name="Label_Globals" xml:space="preserve">
    <value>全局</value>
  </data>
  <data name="Command_Edit_Event_Global_Setting" xml:space="preserve">
    <value>编辑全局事件设定</value>
  </data>
  <data name="Menu_NewGlobalEvent" xml:space="preserve">
    <value>新建全局事件。。。</value>
  </data>
  <data name="Label_Variable_Type" xml:space="preserve">
    <value>变量类型</value>
  </data>
  <data name="ErrorSelector_Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="Hint_Right_Click_to_Add_FSM_to_" xml:space="preserve">
    <value>用鼠标右键单击添加状态机到</value>
  </data>
  <data name="ActionEditor_EditFsmEventTarget_FSM_Name" xml:space="preserve">
    <value>状态机名称</value>
  </data>
  <data name="Dialog_Use_Saved_Template_in_this_FSM" xml:space="preserve">
    <value>在状态机中使用保存的模板？</value>
  </data>
  <data name="Menu_Add_FSM_to__" xml:space="preserve">
    <value>添加状态机到 {0}</value>
  </data>
  <data name="BugReportWindow_MissingDescription" xml:space="preserve">
    <value>请输入描述</value>
  </data>
  <data name="BugReportWindow_Where_does_it_happen" xml:space="preserve">
    <value>在哪里发生的</value>
  </data>
  <data name="Dialog_Add_FSM_Template" xml:space="preserve">
    <value>添加状态机模板</value>
  </data>
  <data name="FsmEditorSettings_Minimap_Size_Tooltip" xml:space="preserve">
    <value>迷你地图大小 （以像素为单位）。</value>
  </data>
  <data name="Dialog_Editing_FSM_while_game_is_running" xml:space="preserve">
    <value>当游戏正在播放时，您正在编辑状态机。当你停止播放时，更改将会丢失 ！</value>
  </data>
  <data name="Menu_Move_Action_To_Top" xml:space="preserve">
    <value>将动作移至顶部</value>
  </data>
  <data name="Command_Paste_FSM" xml:space="preserve">
    <value>粘贴状态机</value>
  </data>
  <data name="FsmEditorSettings_Category_Selection" xml:space="preserve">
    <value>选择</value>
  </data>
  <data name="FsmEditorSettings_Cetegory_Paths" xml:space="preserve">
    <value>路径</value>
  </data>
  <data name="Menu_Show_State_Loop_Counts" xml:space="preserve">
    <value>显示状态循环统计次数</value>
  </data>
  <data name="FsmEditorSettings_Add_Prefab_Labels_Tooltip" xml:space="preserve">
    <value>为prefab添加标签。</value>
  </data>
  <data name="Label_Loading_Watermark_Textures___" xml:space="preserve">
    <value>加载水印纹理...</value>
  </data>
  <data name="Menu_GraphView_Link_Direction_Right" xml:space="preserve">
    <value>链接方向/锁定到右面</value>
  </data>
  <data name="Tooltip_Draw_Active_State_Labels" xml:space="preserve">
    <value>在游戏视图中物体上绘制当前处于活动的状态名称。您可以在每个状态机的PlayMakerFSM检查面板中启用或禁用它。</value>
  </data>
  <data name="Menu_GraphView_CustomEvents" xml:space="preserve">
    <value>自定义事件</value>
  </data>
  <data name="FsmEditorSettings_Preferences_Reset" xml:space="preserve">
    <value>PlayMaker ︰ 首选项重置 ！</value>
  </data>
  <data name="Tooltip_Network_Sync_Not_Supported" xml:space="preserve">
    <value>此变量的类型不可用于网络同步。</value>
  </data>
  <data name="ActionEditor_Send_To_Children" xml:space="preserve">
    <value>发送到子物体</value>
  </data>
  <data name="FsmEditorSettings_Show_Comments_in_Graph_View_Tooltip" xml:space="preserve">
    <value>将描述添加到状态机和状态，并且可以在图表视图中看到</value>
  </data>
  <data name="Menu_No_FSMs_Use_This_Event" xml:space="preserve">
    <value>没有状态机使用此事件...</value>
  </data>
  <data name="Toggle" xml:space="preserve">
    <value>切换</value>
  </data>
  <data name="ActionSelector_Count_Postfix" xml:space="preserve">
    <value>[{0}]</value>
  </data>
  <data name="Command_Save_Selection_as_Template" xml:space="preserve">
    <value>将选择另存为模板</value>
  </data>
  <data name="Label_Category___" xml:space="preserve">
    <value>类别</value>
  </data>
  <data name="Command_New_Global_Event" xml:space="preserve">
    <value>新建全局事件</value>
  </data>
  <data name="Error_Name_is_too_long" xml:space="preserve">
    <value>名称太长 ！</value>
  </data>
  <data name="Label_Prefab_Instance_disconnected" xml:space="preserve">
    <value>Prefab预设实例 （断开连接）</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Network_Setup_Errors" xml:space="preserve">
    <value>检查网络设置错误</value>
  </data>
  <data name="Title_Screenshots" xml:space="preserve">
    <value>屏幕截图</value>
  </data>
  <data name="Label_Enum_Type" xml:space="preserve">
    <value>枚举类型</value>
  </data>
  <data name="Menu_GraphView_Add_Transition" xml:space="preserve">
    <value>添加过渡</value>
  </data>
  <data name="Tooltip_Tooltip" xml:space="preserve">
    <value>用于检查面板和动作编辑器的提示。不支持 Vector3。</value>
  </data>
  <data name="Dialog_Delete_Variable" xml:space="preserve">
    <value>删除变量</value>
  </data>
  <data name="Menu_Play_Sound" xml:space="preserve">
    <value>播放声音</value>
  </data>
  <data name="Menu_Edit_Tool_Window" xml:space="preserve">
    <value>编辑工具窗口</value>
  </data>
  <data name="Menu_Enable_Logging" xml:space="preserve">
    <value>启用日志记录</value>
  </data>
  <data name="Dialog_UnlockFSM_Wrong_Password" xml:space="preserve">
    <value>无法解锁状态机。
请检查密码。</value>
  </data>
  <data name="AboutPlaymaker_Hutong_Games_Link" xml:space="preserve">
    <value>Hutong Games（胡同游戏）</value>
  </data>
  <data name="FsmEditorSettings_Color_Scheme" xml:space="preserve">
    <value>配色方案</value>
  </data>
  <data name="Label_GUIText_State_Labels" xml:space="preserve">
    <value>GUIText 状态标签</value>
  </data>
  <data name="Hint_Graph_View_Settings" xml:space="preserve">
    <value>这些设置控制建立状态机时图表视图的外观和行为。</value>
  </data>
  <data name="EventMenu_Global_Events" xml:space="preserve">
    <value>全局事件</value>
  </data>
  <data name="Menu_Inherited_Submenu" xml:space="preserve">
    <value>继承 / {0}</value>
  </data>
  <data name="Label_No_unused_variables" xml:space="preserve">
    <value>没有未使用的变量...</value>
  </data>
  <data name="ErrorSelector_Total_Errors" xml:space="preserve">
    <value>总计: {0}</value>
  </data>
  <data name="Menu_GraphView_Add_FSM_Component_New_FSM" xml:space="preserve">
    <value>添加状态机组件/新状态机</value>
  </data>
  <data name="Menu_GameObject_Actions" xml:space="preserve">
    <value>GameObject 动作</value>
  </data>
  <data name="Command_Rename_Action" xml:space="preserve">
    <value>重命名动作</value>
  </data>
  <data name="Error_Could_Not_Find_Action_Field" xml:space="preserve">
    <value>找不到动作的参数字段 ︰ {0}
注意 ︰ 字段必须是公共参数。</value>
  </data>
  <data name="Label_Modified_postfix" xml:space="preserve">
    <value>（修改）</value>
  </data>
  <data name="Label_Logging_is_disabled_in_Preferences" xml:space="preserve">
    <value>在首选项中的日志记录被禁用...</value>
  </data>
  <data name="Tooltip_Events_Used" xml:space="preserve">
    <value>对事件作出响应的状态机数量。</value>
  </data>
  <data name="Label_General" xml:space="preserve">
    <value>常用</value>
  </data>
  <data name="CustomActionWizard_Find_File" xml:space="preserve">
    <value>查找文件</value>
  </data>
  <data name="Menu_State_Browser" xml:space="preserve">
    <value>状态浏览器</value>
  </data>
  <data name="Error_Could_Not_Set_Action_Field_Value" xml:space="preserve">
    <value>无法设置动作参数字段 ︰ {0} 
值得类型不兼容。</value>
  </data>
  <data name="Command_Add_Event" xml:space="preserve">
    <value>添加事件</value>
  </data>
  <data name="Command_Add_State" xml:space="preserve">
    <value>添加状态</value>
  </data>
  <data name="Dialog_Make_Global_Variable" xml:space="preserve">
    <value>使变量全局化</value>
  </data>
  <data name="FsmEditorSettings_Edge_Scroll_Speed" xml:space="preserve">
    <value>边缘滚动条的速度</value>
  </data>
  <data name="Menu_NewGlobalVariable" xml:space="preserve">
    <value>新建全局变量。。。</value>
  </data>
  <data name="Hint_GraphView_Shortcut_Description" xml:space="preserve">
    <value>快捷键提示 ：
添加状态 ︰
添加FINISHED事件 ︰
添加过渡状态 ︰
快速删除 ：
对齐网格 ︰
约束拖动 ︰
选择默认启动状态 ︰
跟踪过渡 ︰
锁定链接方向 ︰
移动选定的过渡 ︰</value>
  </data>
  <data name="StateSelector_Title" xml:space="preserve">
    <value>状态浏览器</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>类别</value>
  </data>
  <data name="FsmEditorSettings_Category_Components_and_Gizmos" xml:space="preserve">
    <value>组件和线框</value>
  </data>
  <data name="Hint_Export_Globals_Notes" xml:space="preserve">
    <value>导出和导入的全局变量：
若要将全局变量复制到没有全局变量的新项目中，只需要将这一资源包含在导出的 unitypackage中。
如果新项目已有全局变量并且不想覆盖，在此项目中使用导出全局变量并包括在导出的unitypackage中，在新项目中使用导入全局变量。</value>
  </data>
  <data name="FsmEditorSettings_Select_GameObject_When_FSM_Selected" xml:space="preserve">
    <value>选择状态机时选择对应的 GameObject</value>
  </data>
  <data name="Menu_No_FSMs_use_this_variable" xml:space="preserve">
    <value>没有状态机使用此变量...</value>
  </data>
  <data name="Menu_Remove_Unused_Events" xml:space="preserve">
    <value>删除未使用的事件</value>
  </data>
  <data name="CustomActionWizard_Label_Action_Name" xml:space="preserve">
    <value>动作名称</value>
  </data>
  <data name="VariableManager_MoveToGlobals_Warning" xml:space="preserve">
    <value>已经存在一个重名但类型不同的全局变量！</value>
  </data>
  <data name="FsmLogger_Title" xml:space="preserve">
    <value>状态机日志</value>
  </data>
  <data name="Hint_Paste_Template" xml:space="preserve">
    <value>在模板窗口中选择一个模板以激活粘贴模版的功能...</value>
  </data>
  <data name="Hint_Variable_Panel" xml:space="preserve">
    <value>此面板显示状态机所使用的变量。
用鼠标右键单击变量来选择使用该变量的状态。</value>
  </data>
  <data name="Hint_Variable_Usage" xml:space="preserve">
    <value>生成动作中使用的变量。提示： 在状态面板中，滚动动作参数浏览所需变量的类型。</value>
  </data>
  <data name="Menu_Templates_Browser" xml:space="preserve">
    <value>模板浏览器</value>
  </data>
  <data name="FsmEditorSettings_Zooming" xml:space="preserve">
    <value>缩放中</value>
  </data>
  <data name="FsmEditorSettings_Disable_Undo_Redo_Tooltip" xml:space="preserve">
    <value>如果在速度较慢的电脑上使用大型项目，编辑器的性能较慢，您可以关闭撤消/重复（undo/redo）支持更好的性能。</value>
  </data>
  <data name="Command_Delete_Event_From_All_FSMs" xml:space="preserve">
    <value>从所有状态机中删除事件</value>
  </data>
  <data name="Menu_Paste_FSM" xml:space="preserve">
    <value>粘贴状态机</value>
  </data>
  <data name="Menu_Rename_Category" xml:space="preserve">
    <value>重命名类别</value>
  </data>
  <data name="FsmEditorSettings_Disable_Undo_Redo" xml:space="preserve">
    <value>禁用撤消/重做</value>
  </data>
  <data name="ActionEditor_Cannot_Add_Component_Type_XXX" xml:space="preserve">
    <value>不能添加组件类型: {0}</value>
  </data>
  <data name="Label_FINISHED" xml:space="preserve">
    <value>[结束]</value>
  </data>
  <data name="Menu_With_Global_Transition" xml:space="preserve">
    <value>添加全局过渡</value>
  </data>
  <data name="Command_Set_Start_State" xml:space="preserve">
    <value>设置起始状态</value>
  </data>
  <data name="DebugToolbar_Button_Next" xml:space="preserve">
    <value>下一步</value>
  </data>
  <data name="DebugToolbar_Button_Prev" xml:space="preserve">
    <value>上一步</value>
  </data>
  <data name="Dialog_Delete_Unused_Global_Variables" xml:space="preserve">
    <value>删除未使用的全局变量</value>
  </data>
  <data name="FsmEditorSettings_Edge_Scroll_Speed_Tooltip" xml:space="preserve">
    <value>设置在图表视图边缘拖动状态或过渡时视图滚动的速度。默认是 10，越小越慢。</value>
  </data>
  <data name="FsmLog_Label_Sent_By" xml:space="preserve">
    <value>来自： </value>
  </data>
  <data name="Error_Cannot_Rename_State" xml:space="preserve">
    <value>不能重命名状态 ！</value>
  </data>
  <data name="Menu_Event_Browser" xml:space="preserve">
    <value>事件浏览器</value>
  </data>
  <data name="FsmEditorSettings_Auto_Frame_Selected_State" xml:space="preserve">
    <value>自动框选选择的状态</value>
  </data>
  <data name="Tooltip_Editor_Windows" xml:space="preserve">
    <value>编辑器窗口</value>
  </data>
  <data name="Command_Set_Link_Style" xml:space="preserve">
    <value>设置链接样式</value>
  </data>
  <data name="Menu_GraphView_Link_Direction_Auto" xml:space="preserve">
    <value>链接方向/自动方向</value>
  </data>
  <data name="Menu_GraphView_Link_Direction_Left" xml:space="preserve">
    <value>链接方向/锁定到左面</value>
  </data>
  <data name="Error_Invalid_Event_Name" xml:space="preserve">
    <value>无效的事件名称 ！</value>
  </data>
  <data name="FsmEditorSettings_Draw_Playmaker_Gizmos_in_Scene_View_Tooltip" xml:space="preserve">
    <value>在场景视图中带有状态机的物体上绘制Playmaker线框。</value>
  </data>
  <data name="ActionEditor_Store_Texture" xml:space="preserve">
    <value>存储纹理</value>
  </data>
  <data name="Tooltip_Variables_Debug" xml:space="preserve">
    <value>显示动作参数中使用的变量的当前值。</value>
  </data>
  <data name="VersionInfo_Trial_Version_Notes" xml:space="preserve">
    <value>试用版： 完全功能的试用版。生成的程序显示PlayMaker水印。
注意： 水印使用Unity的 GUI，在某些设备上可能产生不利性能的影响。</value>
  </data>
  <data name="Menu_Copy_Value" xml:space="preserve">
    <value>复制值</value>
  </data>
  <data name="CustomActionWizard_Copy_Code_Tooltip" xml:space="preserve">
    <value>将生成的代码复制到剪贴板。</value>
  </data>
  <data name="FsmEditorSettings_New_State_Name" xml:space="preserve">
    <value>新的状态名称</value>
  </data>
  <data name="Label_Debugging" xml:space="preserve">
    <value>调试</value>
  </data>
  <data name="Command_Reset" xml:space="preserve">
    <value>重置</value>
  </data>
  <data name="Label_New_Variable" xml:space="preserve">
    <value>新变量</value>
  </data>
  <data name="Command_Paste" xml:space="preserve">
    <value>粘贴</value>
  </data>
  <data name="Error_Name_contains_illegal_character" xml:space="preserve">
    <value>名称中含有非法字符： \</value>
  </data>
  <data name="Hint_Action_Shortcuts" xml:space="preserve">
    <value>双击动作标题编辑动作名称。
按住 Ctrl 键单击折页展开或折叠所有动作。
按住 Ctrl 键单击启用复选框可以启用或禁用的所有动作。</value>
  </data>
  <data name="Dialog_Delete_Event" xml:space="preserve">
    <value>删除事件</value>
  </data>
  <data name="EventsWindow_Enable_When_Playing" xml:space="preserve">
    <value>播放时启用事件浏览器。</value>
  </data>
  <data name="Label_None_Action" xml:space="preserve">
    <value>无 (动作)</value>
  </data>
  <data name="Menu_Show_Action_Parameters" xml:space="preserve">
    <value>显示动作参数</value>
  </data>
  <data name="FsmErrorChecker_InvalidEventError" xml:space="preserve">
    <value>不被此状态使用或任何全局过渡使用的事件 ！</value>
  </data>
  <data name="Dialog_Delete_Event_Are_you_sure" xml:space="preserve">
    <value>是否确定？{0}</value>
  </data>
  <data name="Label_Global_Events" xml:space="preserve">
    <value>全局事件</value>
  </data>
  <data name="ActionSelector_Add_Action_To_State" xml:space="preserve">
    <value>添加动作到状态中</value>
  </data>
  <data name="Command_Adjust_iTween_Path" xml:space="preserve">
    <value>调整iTween路径</value>
  </data>
  <data name="FsmEditorSettings_Disable_PlayMaker_Editor_When_Game_Is_Playing" xml:space="preserve">
    <value>游戏运行时，禁用PlayMaker编辑器</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Obsolete_Actions_Tooltip" xml:space="preserve">
    <value>查找已标记为淘汰的所有动作。</value>
  </data>
  <data name="FsmErrorChecker_StateHasMissingActionError" xml:space="preserve">
    <value>状态有丢失的动作。</value>
  </data>
  <data name="Tooltip_Reset_On_Disable" xml:space="preserve">
    <value>FSM 应重置或保持其当前状态启用或禁用。如果您想要启用或禁用 PlayMakerFSM 组件来暂停状态机，取消选中此选项。</value>
  </data>
  <data name="ActionEditor_Parameter" xml:space="preserve">
    <value>参数</value>
  </data>
  <data name="CustomActionWizard_Error_InvalidPath" xml:space="preserve">
    <value>无效的路径: {0}</value>
  </data>
  <data name="Action_Sequence" xml:space="preserve">
    <value>动作序列</value>
  </data>
  <data name="Command_Set_Transition_Color" xml:space="preserve">
    <value>设置过渡颜色</value>
  </data>
  <data name="Menu_GraphView_Copy_FSM" xml:space="preserve">
    <value>复制状态机</value>
  </data>
  <data name="FilterMenu_Recently_Selected" xml:space="preserve">
    <value>最近选择的</value>
  </data>
  <data name="Command_Set_Transition_Event" xml:space="preserve">
    <value>设置过渡事件</value>
  </data>
  <data name="EditFsmArray_Unknown_FsmArray_Type" xml:space="preserve">
    <value>未知的状态机数组类型 ︰ 使用数组编辑器来指定变量类型。</value>
  </data>
  <data name="EventsWindow_Disabled_When_Playing" xml:space="preserve">
    <value>播放时禁用。请参阅事件浏览器设置菜单。</value>
  </data>
  <data name="Menu_GraphView_Add_FSM_Component_Use_Template" xml:space="preserve">
    <value>添加状态机组件/使用模板 / {0} / {1}</value>
  </data>
  <data name="Label_Quaternion" xml:space="preserve">
    <value>Quaternion（四元数）</value>
  </data>
  <data name="Command_DeleteTemplate" xml:space="preserve">
    <value>删除模板吗？
注意： 您无法撤消此操作 ！</value>
  </data>
  <data name="Command_Open_Screenshot" xml:space="preserve">
    <value>打开屏幕截图</value>
  </data>
  <data name="Menu_FsmLog_Show_Sent_By" xml:space="preserve">
    <value>显示发送者</value>
  </data>
  <data name="Label_Property" xml:space="preserve">
    <value>属性</value>
  </data>
  <data name="ActionEditor_EditFsmEventTarget_FSM_Component_Tooltip" xml:space="preserve">
    <value>直接向 PlayMakerFSM 组件发送事件。</value>
  </data>
  <data name="Tooltip_Browse_Templates" xml:space="preserve">
    <value>选择一个模板。
模板使您可以在游戏物体之间共享相同的状态机。</value>
  </data>
  <data name="TemplateSelector_Title" xml:space="preserve">
    <value>模板</value>
  </data>
  <data name="Command_Event_Browser" xml:space="preserve">
    <value>事件浏览器</value>
  </data>
  <data name="Hint_GraphView_Shortcuts_OSX" xml:space="preserve">
    <value>F1 显示/隐藏
Cmd 单击编辑区
Cmd 单击状态
Cmd 拖放过渡
Cmd Shift 单击
Cmd 拖放状态
Shift 拖放状态
Home
Alt 单击过渡
Cmd 左/右
Cmd 下
Cmd 上</value>
  </data>
  <data name="FsmEditorSettings_Disable_Tool_Windows_When_Game_Is_Playing_Tooltip" xml:space="preserve">
    <value>如果您发现编辑器中运行的性能太差，您可以尝试禁用工具窗口。请注意，某些工具窗口可以单独在菜单中启用或禁用。</value>
  </data>
  <data name="DebugToolbar_Button_Open_Log" xml:space="preserve">
    <value>打开日志</value>
  </data>
  <data name="Dialog_Template_Missing_Start_State" xml:space="preserve">
    <value>模板未定义起始状态......
设置第一个状态为开始状态。</value>
  </data>
  <data name="Hint_Events_Window" xml:space="preserve">
    <value>此窗口显示在您的项目中使用的事件。
用鼠标右键单击一个事件以选择使用该事件的状态机。
使用全局复选框在状态机之间发送事件。</value>
  </data>
  <data name="Hint_Context_Tools" xml:space="preserve">
    <value>编辑工具的上下文内容取决于当前选择的内容。一些用户喜欢使用右键单击上下文菜单方式的编辑。</value>
  </data>
  <data name="Title_NewEvent" xml:space="preserve">
    <value>新建事件</value>
  </data>
  <data name="Dialog_Error_Variable_with_same_name_already_exists" xml:space="preserve">
    <value>已存在具有相同名称的变量 ！</value>
  </data>
  <data name="Menu_Select_All_Actions" xml:space="preserve">
    <value>选择所有动作</value>
  </data>
  <data name="CustomActionWizard_Title" xml:space="preserve">
    <value>PlayMaker</value>
  </data>
  <data name="Menu_Global_Variables" xml:space="preserve">
    <value>全局变量</value>
  </data>
  <data name="ActionEditor_Store_GameObject" xml:space="preserve">
    <value>存储 GameObject</value>
  </data>
  <data name="ActionEditor_Edit_Template" xml:space="preserve">
    <value>编辑模板</value>
  </data>
  <data name="BugReportWindow_Progress" xml:space="preserve">
    <value>提交 bug 报告...</value>
  </data>
  <data name="ActionEditor_Event_Target" xml:space="preserve">
    <value>事件目标</value>
  </data>
  <data name="Menu_Select_Prefab" xml:space="preserve">
    <value>选择Prefab预设</value>
  </data>
  <data name="FsmEditorSettings_Show_Editing_While_Running_Warning_Tooltip" xml:space="preserve">
    <value>在运行游戏时第一次编辑状态机的时候给出一个非常态的警告。一个友善的提醒 ！</value>
  </data>
  <data name="Label_Control_Mouse_Cursor" xml:space="preserve">
    <value>控制鼠标光标</value>
  </data>
  <data name="Label_Draw_Active_State_Labels" xml:space="preserve">
    <value>绘制活动状态标签</value>
  </data>
  <data name="Dialog_Loading_FSM_Prefabs" xml:space="preserve">
    <value>正在加载所有PlayMakerFSM 的Prefab...</value>
  </data>
  <data name="Error_Failed_to_load_Custom_Action_Editor" xml:space="preserve">
    <value>未能加载自定义动作编辑器检查面板参数类型。</value>
  </data>
  <data name="InspectorPanel_FSM_Uses_Template" xml:space="preserve">
    <value>该状态机使用模板。
单击图表视图选择并且编辑模板。
注 ︰ 此模板可能与其他状态机共享 ！</value>
  </data>
  <data name="Menu_Select_Script" xml:space="preserve">
    <value>选择脚本</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Transitions_Missing_Targets" xml:space="preserve">
    <value>检查丢失目标的转换</value>
  </data>
  <data name="Menu_Select_GameObject" xml:space="preserve">
    <value>选择 GameObject</value>
  </data>
  <data name="Command_Clear_Breakpoints" xml:space="preserve">
    <value>清除断点</value>
  </data>
  <data name="Event_Tooltip_Warning" xml:space="preserve">
    <value>注意 ︰ 不能重命名全局变量，因为他们可能在其他场景中使用。目前你要创建一个新的事件来引用它。我们将在以后的更新中添加重命名功能。</value>
  </data>
  <data name="Log_No_Transition_To_State" xml:space="preserve">
    <value>没有过渡到状态: {0}</value>
  </data>
  <data name="FsmEditorSettings_Snap_To_Grid" xml:space="preserve">
    <value>对齐到网格</value>
  </data>
  <data name="Menu_Add_to_Top_of_Action_List" xml:space="preserve">
    <value>添加到动作列表的顶端</value>
  </data>
  <data name="Command_Set_Selected_States_Color" xml:space="preserve">
    <value>设置所选状态的颜色</value>
  </data>
  <data name="Menu_Disabled_Add_FSM_to_selected_object" xml:space="preserve">
    <value>添加状态机到所选的对象...</value>
  </data>
  <data name="Label_Global_postfix" xml:space="preserve">
    <value>（全局）</value>
  </data>
  <data name="Command_Add_Variable" xml:space="preserve">
    <value>添加变量</value>
  </data>
  <data name="FsmErrorChecker_TransitionMissingEventError" xml:space="preserve">
    <value>过渡缺少事件 ！</value>
  </data>
  <data name="Error_Invalid_path__" xml:space="preserve">
    <value>无效的路径: {0}</value>
  </data>
  <data name="Command_Import_Globals" xml:space="preserve">
    <value>导入全局变量</value>
  </data>
  <data name="FsmEditorSettings_Enable_Logging" xml:space="preserve">
    <value>启用日志记录</value>
  </data>
  <data name="ActionReportWindow_Description" xml:space="preserve">
    <value>此日志可帮助您跟踪更改保存项目后动作改变造成的潜在问题。单击状态机、状态、动作以在主编辑窗口中选择它并快速跳转到变化的位置</value>
  </data>
  <data name="Hint_Watermarks" xml:space="preserve">
    <value>水印可以帮助您快速识别状态机。
放置自定义水印在以下目录：PlayMaker/Editor/Watermarks。
使用现有的水印作为参考。
注意： 水印可以在首选项中关闭。</value>
  </data>
  <data name="Error_Unrecognized_variable_type__" xml:space="preserve">
    <value>无法识别的变量类型: {0}</value>
  </data>
  <data name="Menu_Copy_Variables" xml:space="preserve">
    <value>复制变量</value>
  </data>
  <data name="Tooltip_Browse_FSMs_on_GameObject" xml:space="preserve">
    <value>浏览GameObject上的状态机</value>
  </data>
  <data name="Label_Enabled" xml:space="preserve">
    <value>已启用</value>
  </data>
  <data name="Menu_No_Drag_and_Drop_Actions_Found_For_This_Asset_Type" xml:space="preserve">
    <value>对此资源类型没有找到可用的拖放动作...</value>
  </data>
  <data name="LOG_Auto_Added_Playmaker_GUI" xml:space="preserve">
    <value>自动添加 PlayMakerGUI 到场景。请注意，如果要看PlayMaker线框、OnGUI 动作和 iTween 路径的编辑，您需要场景中的 PlayMakerGUI。在首选项中，可以将此选项关闭。</value>
  </data>
  <data name="MainMenu_PlayMaker_Editor" xml:space="preserve">
    <value>PlayMaker/PlayMaker Editor</value>
  </data>
  <data name="Command_Enable_Watermarks" xml:space="preserve">
    <value>启用水印</value>
  </data>
  <data name="CustomActionWizard_Custom_Category" xml:space="preserve">
    <value>自定义类别</value>
  </data>
  <data name="Menu_GraphView_Transition_Target" xml:space="preserve">
    <value>过渡目标 /</value>
  </data>
  <data name="iTween_Path_Editing_Label_Begin" xml:space="preserve">
    <value>'{0}' 开始</value>
  </data>
  <data name="Label_Confirm_Editing_of_Prefab_Instance" xml:space="preserve">
    <value>确认编辑预设的实例。请参阅首选项。</value>
  </data>
  <data name="WelcomeWindow_Show_at_Startup" xml:space="preserve">
    <value>在启动时显示</value>
  </data>
  <data name="Dialog_ImportGlobals_Remove_extra_PlayMakerGlobals_" xml:space="preserve">
    <value>删除额外的 PlayMakerGlobals 吗？</value>
  </data>
  <data name="Label_No_search_results_for__" xml:space="preserve">
    <value>没有任何结果关于搜索 ︰ </value>
  </data>
  <data name="FsmSelector_Label_Url_to_docs" xml:space="preserve">
    <value>指向文档的 Url</value>
  </data>
  <data name="Label_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Hint_Globals_Refresh" xml:space="preserve">
    <value>用于统计任何加载的状态机中使用的全局变量次数。注意︰可能会在其他场景中使用变量 ！使用刷新按钮来手动更新使用计数。</value>
  </data>
  <data name="FsmEditorSettings_Add_Prefab_Labels" xml:space="preserve">
    <value>添加prefab标签</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Required_Action_Fields_Tooltip" xml:space="preserve">
    <value>检查动作所需的参数，动作正确工作所必须指定的参数</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Missing_Actions_Tooltip" xml:space="preserve">
    <value>找出所有丢失的动作</value>
  </data>
  <data name="ToolWindow_Header_Global_Transitions" xml:space="preserve">
    <value>全局过渡：</value>
  </data>
  <data name="Tooltip_Lock_Selected_FSM" xml:space="preserve">
    <value>保持选定此状态机</value>
  </data>
  <data name="Error_Duplicate_Event_Found__" xml:space="preserve">
    <value>发现重复的事件：{0}</value>
  </data>
  <data name="Tooltip_EventManager_Add_Event" xml:space="preserve">
    <value>将事件添加到此状态机。使用浏览按钮或事件浏览器快速添加现有事件。</value>
  </data>
  <data name="AboutPlaymaker_Title" xml:space="preserve">
    <value>关于</value>
  </data>
  <data name="Tooltip_Select_FSM" xml:space="preserve">
    <value>选择状态机...</value>
  </data>
  <data name="WelcomeWindow_Forums_More" xml:space="preserve">
    <value>加入PlayMaker社区 ！</value>
  </data>
  <data name="Tooltip_Show_State_Label" xml:space="preserve">
    <value>在游戏视图中显示活动状态标签。
注意: PlayMakerGUI必须在场景中。</value>
  </data>
  <data name="Menu_Reset" xml:space="preserve">
    <value>重置</value>
  </data>
  <data name="Hint_Right_Click_to_Add_FSM" xml:space="preserve">
    <value>用鼠标右键单击添加状态机</value>
  </data>
  <data name="CustomActionWizard_Full_Title" xml:space="preserve">
    <value>自定义动作向导</value>
  </data>
  <data name="Log_Saving_FSM_Screenshot__" xml:space="preserve">
    <value>保存状态机截图：</value>
  </data>
  <data name="CustomActionWizard_Action_Name_contains_invalid_characters" xml:space="preserve">
    <value>动作名称包含无效字符。</value>
  </data>
  <data name="CustomActionWizard_Log_Creating_custom_action__" xml:space="preserve">
    <value>创建自定义动作：</value>
  </data>
  <data name="FsmEditorSettings_Selected_language_not_yet_supported_in_menus" xml:space="preserve">
    <value>在Unity菜单中并不支持语言选择...</value>
  </data>
  <data name="FsmLog_Clear" xml:space="preserve">
    <value>清除</value>
  </data>
  <data name="Tooltip_Edit_Variable" xml:space="preserve">
    <value>编辑所选的变量。</value>
  </data>
  <data name="Command_Browse_Screenshots" xml:space="preserve">
    <value>浏览</value>
  </data>
  <data name="Command_Rename_State" xml:space="preserve">
    <value>重命名状态</value>
  </data>
  <data name="Menu_Check_for_Setup_Errors_With_Collision_Events" xml:space="preserve">
    <value>检查碰撞事件的设置错误</value>
  </data>
  <data name="Menu_Find_Script" xml:space="preserve">
    <value>查找脚本</value>
  </data>
  <data name="Menu_Make_Animation_States" xml:space="preserve">
    <value>添加动画状态</value>
  </data>
  <data name="VariableManager_MoveToGlobals_Confirm" xml:space="preserve">
    <value>全局变量已经存在。
使用全局变量并且删除本地变量吗？</value>
  </data>
  <data name="Dialog_Saved_FSM_Screenshot" xml:space="preserve">
    <value>保存状态机截图: {0}</value>
  </data>
  <data name="Menu_GraphView_Add_FSM_Component" xml:space="preserve">
    <value>添加状态机组件</value>
  </data>
  <data name="Menu_No_Context_Actions_Found" xml:space="preserve">
    <value>没有发现关联的动作！</value>
  </data>
  <data name="Command_Rename_Event" xml:space="preserve">
    <value>重命名事件</value>
  </data>
  <data name="FsmErrorChecker_ControllerCollisionEventsNeedController" xml:space="preserve">
    <value>所有者需要一个 Character Controller角色控制器对控制器碰撞事件作出回应 ！</value>
  </data>
  <data name="Error_Name_is_empty" xml:space="preserve">
    <value>名称为空!</value>
  </data>
  <data name="FsmEditorSettings_Draw_Frame_Around_FSM" xml:space="preserve">
    <value>绘制状态机框架</value>
  </data>
  <data name="Menu_GraphView_Add_To_Selected" xml:space="preserve">
    <value>添加到所选 GameObject</value>
  </data>
  <data name="FsmEditorSettings_Auto_Add_PlayMakerGUI_to_Scene" xml:space="preserve">
    <value>自动将 PlayMakerGUI 添加到场景</value>
  </data>
  <data name="Error_Name_already_used_in_this_FSM" xml:space="preserve">
    <value>在此状态机中名称已经被使用！</value>
  </data>
  <data name="Hint_Inspector_disabled_when_playing" xml:space="preserve">
    <value>播放时禁用检查面板。请参阅首选项。</value>
  </data>
  <data name="VersionInfo_Preview_Version_Notes" xml:space="preserve">
    <value>预览版本： 编辑功能被禁用，但您可以在其他版本中查看和运行创作的状态机。这是为方便那些不需要完整版本，但需要运行状态机的团队成员。或者您想要将项目共享与没有PlayMaker的人。</value>
  </data>
  <data name="Label_PlayMaker_Supported_Languages" xml:space="preserve">
    <value>PlayMaker支持的语言 ︰</value>
  </data>
  <data name="Error_Missing_Action__Set_Property" xml:space="preserve">
    <value>PlayMaker缺少动作： Set Property设置属性</value>
  </data>
  <data name="Error_Variable_name_already_exists_and_is_of_different_type" xml:space="preserve">
    <value>变量名称已经存在，而且是不同的类型: {0}</value>
  </data>
  <data name="Dialog_Export_Globals" xml:space="preserve">
    <value>导出全局变量</value>
  </data>
  <data name="ActionEditor_Undo_Resize_Array" xml:space="preserve">
    <value>调整数组的大小</value>
  </data>
  <data name="Command_Edit_Action" xml:space="preserve">
    <value>编辑动作: {0}</value>
  </data>
  <data name="Command_Remove_FSM_Component" xml:space="preserve">
    <value>删除状态机组件</value>
  </data>
  <data name="FsmEditorSettings_Select_GameObject_When_FSM_Selected_Tooltip" xml:space="preserve">
    <value>选择状态机时自动选择拥有对应状态机的 GameObject</value>
  </data>
  <data name="CustomActionWizard_Action_Folder" xml:space="preserve">
    <value>动作文件夹</value>
  </data>
  <data name="FsmEditorSettings_Color_Links_With_State_Color_Tooltip" xml:space="preserve">
    <value>使用状态颜色来设置过渡链接的颜色。</value>
  </data>
  <data name="Tooltip_Filter_State_Labels_With_Distance" xml:space="preserve">
    <value>当您在游戏视图中移动时如果你只想看到附近状态的标签，这是很有用的。</value>
  </data>
  <data name="Hint_FsmSelector" xml:space="preserve">
    <value>状态机浏览器可以让你快速查看所有已加载的状态机和它们当前的状态。单击状态机并在主编辑器中选择它。
提示： 使用上面的筛选器列表，例如显示最近使用的状态机。</value>
  </data>
  <data name="Label_Fix_Network_Observe_This_FSM" xml:space="preserve">
    <value>GameObject NetworkView 组件必须监视此 PlayMakerFSM。将 PlayMakerFSM 拖到被观察字段，或单击此框以修复。</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>PlayMaker</value>
  </data>
  <data name="Dialog_Saved_Screenshot" xml:space="preserve">
    <value>保存屏幕截图</value>
  </data>
  <data name="FsmEditorSettings_Creating_PlayMakerPrefs_Asset" xml:space="preserve">
    <value>创建 PlayMakerPrefs asset 资源： </value>
  </data>
  <data name="Tooltip_Global_Event_Flag" xml:space="preserve">
    <value>全局标志。全局事件可以在状态机之间发送。</value>
  </data>
  <data name="ActionEditor_Add_Component_MeshRenderer" xml:space="preserve">
    <value>添加组件： MeshRenderer</value>
  </data>
  <data name="Label_Description___" xml:space="preserve">
    <value>说明</value>
  </data>
  <data name="Command_Edit_Global_Variable" xml:space="preserve">
    <value>编辑全局变量</value>
  </data>
  <data name="Command_Delete_Unused_Variables" xml:space="preserve">
    <value>删除未使用的变量</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Required_Components_Tooltip" xml:space="preserve">
    <value>检查所需的组件。如果需要在实时运行时添加组件，关闭该选项会跳过这个检查</value>
  </data>
  <data name="Tooltip_Global_Variables_Type" xml:space="preserve">
    <value>查看或编辑变量的类型。
单击此处按类型排序。</value>
  </data>
  <data name="BugReportWindow_FrequencyChoices_Sometimes__but_not_always" xml:space="preserve">
    <value>有时，但并不总是</value>
  </data>
  <data name="Menu_GraphView_Add_FSM_Component_Paste_FSM" xml:space="preserve">
    <value>添加状态机组件/粘贴状态机</value>
  </data>
  <data name="Tooltip_Controls" xml:space="preserve">
    <value>显示在检查面板中的事件和变量。</value>
  </data>
  <data name="Menu_GraphView_Add_FSM_Component_Add_Template" xml:space="preserve">
    <value>添加状态机组件/添加模板</value>
  </data>
  <data name="Path_Resources_Folder" xml:space="preserve">
    <value>资源</value>
  </data>
  <data name="Hint_Send_Event_to_GameObject" xml:space="preserve">
    <value>将事件发送到所有GameObject的状态机 上。注意： 事件必须在事件浏览器或事件检查面板标记为全局事件。</value>
  </data>
  <data name="FsmEditorSettings_Maximize_File_Compatibility_Tooltip" xml:space="preserve">
    <value>将状态机保存成旧的格式，这样可以使用旧版本的PlayMaker打开。文件的尺寸会变大。</value>
  </data>
  <data name="Menu_Enable_Real_Time_Error_Checker" xml:space="preserve">
    <value>启用实时错误检查器</value>
  </data>
  <data name="Category_Tooltip" xml:space="preserve">
    <value>使用类别来组织分类变量。</value>
  </data>
  <data name="Menu_Show_Preview" xml:space="preserve">
    <value>显示预览</value>
  </data>
  <data name="FsmEditorSettings_Auto_Frame_Selected_State_Tooltip" xml:space="preserve">
    <value>图表视图中自动框选选择的状态</value>
  </data>
  <data name="ActionReportWindow_Effected_States_Title" xml:space="preserve">
    <value>受影响的状态：</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Label_Load_All_Templates" xml:space="preserve">
    <value>加载所有模板</value>
  </data>
  <data name="Label_Edit_Prefab" xml:space="preserve">
    <value>编辑预设</value>
  </data>
  <data name="FsmEditorSettings_Minimap_Size" xml:space="preserve">
    <value>迷你地图大小</value>
  </data>
  <data name="ActionEditor_Store_Color" xml:space="preserve">
    <value>存储颜色</value>
  </data>
  <data name="ActionEditor_Store_Float" xml:space="preserve">
    <value>存储浮点值</value>
  </data>
  <data name="BugReportWindow_FrequencyChoices_This_is_the_first_time" xml:space="preserve">
    <value>这是首次</value>
  </data>
  <data name="Hint_Exporting_Templates" xml:space="preserve">
    <value>导出模板：
使用unitypackage格式导出模板资源，并导入到新的项目。请确保勾选'Include Dependencies.'（包括依赖关系）。
如果此模板使用的全局变量，请参阅导出和导入 PlayMakerGlobals 检查面板中的全局变量。</value>
  </data>
  <data name="MainToolbar_Recent" xml:space="preserve">
    <value>最近</value>
  </data>
  <data name="FsmEditorSettings_Infinite_Loop_Threshold" xml:space="preserve">
    <value>无限循环阈值</value>
  </data>
  <data name="Path_PlayMakerGlobals_Asset" xml:space="preserve">
    <value>/Resources/PlayMakerGlobals.asset</value>
  </data>
  <data name="GlobalVariablesWindow_Note_Asset_Location" xml:space="preserve">
    <value>注意 ︰ 全局变量存储在 PlayMaker/Resources/PlayMakerGlobals.asset</value>
  </data>
  <data name="Menu_Make_Local_Variable" xml:space="preserve">
    <value>使变量本地化</value>
  </data>
  <data name="FsmEditorSettings_Custom_Colors" xml:space="preserve">
    <value>自定义颜色</value>
  </data>
  <data name="Tooltip_Browse_variables_in_FSM" xml:space="preserve">
    <value>浏览状态机中的变量</value>
  </data>
  <data name="ActionEditor_EditFsmProperty_Object_Type" xml:space="preserve">
    <value>对象类型</value>
  </data>
  <data name="FsmEditorSettings_FSM_Screenshots_Directory" xml:space="preserve">
    <value>状态机截图目录</value>
  </data>
  <data name="DebugToolbar_Label_Errors" xml:space="preserve">
    <value>错误</value>
  </data>
  <data name="Tooltip_Add_Global_Variable" xml:space="preserve">
    <value>将变量添加到此状态机。变量由动作使用来存储信息</value>
  </data>
  <data name="Help_Experimental_Warning" xml:space="preserve">
    <value>注 ︰ 实验性的功能在将来的版本中可能被删除或修改！</value>
  </data>
  <data name="Menu_Revert_To_Prefab" xml:space="preserve">
    <value>还原到Prefab预设</value>
  </data>
  <data name="FsmErrorChecker_CollisionEventsNeedCollider" xml:space="preserve">
    <value>所有者需要Collider碰撞或RigidBody刚体 对collision碰撞或trigger/触发器事件作出回应 ！</value>
  </data>
  <data name="Label_Rename_Variable_Category" xml:space="preserve">
    <value>重命名变量的类别</value>
  </data>
  <data name="CustomActionWizard_Custom_Error_Checker" xml:space="preserve">
    <value>自定义错误检查器</value>
  </data>
  <data name="Menu_Check_for_Required_Action_Fields" xml:space="preserve">
    <value>检查动作所需的参数</value>
  </data>
  <data name="Tooltip_Doc_Button" xml:space="preserve">
    <value>文档链接</value>
  </data>
  <data name="ActionEditor_Set_Event_Target" xml:space="preserve">
    <value>设置事件目标</value>
  </data>
  <data name="FsmErrorChecker_CollisionEventsNeedCollider2D" xml:space="preserve">
    <value>所有者需要2D Collider碰撞或RigidBody刚体 对2D collision碰撞或trigger/触发器事件作出回应 ！</value>
  </data>
  <data name="FsmEditorSettings_Category_Prefabs" xml:space="preserve">
    <value>Prefabs（预设物体）</value>
  </data>
  <data name="Tooltip_Preview_GUI_Actions_While_Editing" xml:space="preserve">
    <value>这允许您在编辑时预览 GUI 动作。注意： 这是一项试验功能，所以你可能会碰到一些 bug ！</value>
  </data>
  <data name="FsmEditorSettings_Auto_Load_Prefabs_in_Scene_Tooltip" xml:space="preserve">
    <value>自动加载场景中任何使用的prefabs。</value>
  </data>
  <data name="FsmEditorSettings_Auto_Load_Prefabs_in_Scene" xml:space="preserve">
    <value>在场景中自动加载Prefabs</value>
  </data>
  <data name="Label_Importing_Globals_" xml:space="preserve">
    <value>导入全局变量...</value>
  </data>
  <data name="BugReportWindow_Your_E_mail_Tooltip" xml:space="preserve">
    <value>于是您可以获取对您的 bug 报告的更新。</value>
  </data>
  <data name="FsmErrorChecker_TargetFsmMissingEventError" xml:space="preserve">
    <value>不被目标状态机使用的事件 ！</value>
  </data>
  <data name="Dialog_Delete_Multiple_States" xml:space="preserve">
    <value>删除多个状态</value>
  </data>
  <data name="ActionEditor_Undo_Resize_Compound_Array" xml:space="preserve">
    <value>调整复合数组的大小</value>
  </data>
  <data name="Label_Fix_Missing_Network_Component" xml:space="preserve">
    <value>GameObject 需要一个 NetworkView 组件。
单击修复。</value>
  </data>
  <data name="Command_Delete_States" xml:space="preserve">
    <value>删除状态</value>
  </data>
  <data name="Label_Create_Variable" xml:space="preserve">
    <value>创建变量</value>
  </data>
  <data name="CustomActionWizard_Group_Name_and_Description" xml:space="preserve">
    <value>名称和说明</value>
  </data>
  <data name="FsmErrorChecker_RequiredFieldError" xml:space="preserve">
    <value>必填的字段...</value>
  </data>
  <data name="FsmEditorSettings_Disable_Error_Checker_When_Game_Is_Playing_Tooltip" xml:space="preserve">
    <value>禁用实时错误检查，以提高在编辑器中的播放性能。</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Events_Not_Used_by_Target_FSM" xml:space="preserve">
    <value>检查目标状态机中没有被使用的事件</value>
  </data>
  <data name="Hint_Debugger_Settings" xml:space="preserve">
    <value>这些设置控制运行时调试程序的行为。</value>
  </data>
  <data name="WelcomeWindow_Samples_More" xml:space="preserve">
    <value>下载示例场景运行。</value>
  </data>
  <data name="Hint_Template_Selector" xml:space="preserve">
    <value>模板浏览器会显示所有保存在您项目的模板。在图表视图上空白处用鼠标右键单击将状态机另存为模板。</value>
  </data>
  <data name="ToolWindow_Header_State_Tools" xml:space="preserve">
    <value>状态工具：</value>
  </data>
  <data name="FSM" xml:space="preserve">
    <value>状态机</value>
  </data>
  <data name="Tab" xml:space="preserve">
    <value>	</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Tooltip_Choose_Watermark" xml:space="preserve">
    <value>请选择一个水印图像...</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>事件</value>
  </data>
  <data name="Command_Add_FSM_Component" xml:space="preserve">
    <value>添加状态机组件</value>
  </data>
  <data name="Menu_Select_a_State_to_add_Actions" xml:space="preserve">
    <value>选择要将要添加动作的状态......</value>
  </data>
  <data name="Label" xml:space="preserve">
    <value>标签</value>
  </data>
  <data name="ActionSelector_Add_Action_To_State_Tooltip" xml:space="preserve">
    <value>将选定的动作添加到选定的状态中。注： 您还可以拖放动作。</value>
  </data>
  <data name="ActionEditor_Click_to_Add_Required_Component" xml:space="preserve">
    <value>[单击以添加所需的组件]</value>
  </data>
  <data name="ActionEditor_Error_Unsupported_Type_XXX" xml:space="preserve">
    <value>不支持的类型: {0}</value>
  </data>
  <data name="Menu_GraphView_GlobalEvents" xml:space="preserve">
    <value>全局事件</value>
  </data>
  <data name="FsmEditorSettings_Max_State_Width" xml:space="preserve">
    <value>最大状态标签宽度</value>
  </data>
  <data name="CustomActionWizard_File_Path_Prefix" xml:space="preserve">
    <value>文件: Assets/</value>
  </data>
  <data name="Menu_Check_for_Transitions_Missing_Targets" xml:space="preserve">
    <value>检查转换中丢失的目标</value>
  </data>
  <data name="Menu_Remove_Action" xml:space="preserve">
    <value>删除动作</value>
  </data>
  <data name="Menu_Dim_Unused_Parameters" xml:space="preserve">
    <value>将未使用的参数显示成深灰色</value>
  </data>
  <data name="DebugToolbar_No_errors" xml:space="preserve">
    <value>没有错误</value>
  </data>
  <data name="DebugToolbar_Label_Debug" xml:space="preserve">
    <value>调试</value>
  </data>
  <data name="Label_Prefab_Instance" xml:space="preserve">
    <value>Prefab预设的实例</value>
  </data>
  <data name="DebugToolbar_Label_Error" xml:space="preserve">
    <value>错误</value>
  </data>
  <data name="Error_You_cannot_reference_Scene_Objects_in_Project_Assets" xml:space="preserve">
    <value>您不能引用项目资产中的场景物体。</value>
  </data>
  <data name="Menu_FSM_Editor_Log" xml:space="preserve">
    <value>编辑日志</value>
  </data>
  <data name="Label_Add_Event" xml:space="preserve">
    <value>添加事件</value>
  </data>
  <data name="FsmEditorSettings_Show_Editing_While_Running_Warning" xml:space="preserve">
    <value>在运行警告时同时显示编辑</value>
  </data>
  <data name="EventsWindow_Title" xml:space="preserve">
    <value>事件</value>
  </data>
  <data name="Error_Unknown_variable_type" xml:space="preserve">
    <value>未知的变量类型 ！</value>
  </data>
  <data name="ActionReportWindow_Title" xml:space="preserve">
    <value>编辑日志</value>
  </data>
  <data name="Command_Rename" xml:space="preserve">
    <value>重命名</value>
  </data>
  <data name="Label_Filter_State_Labels_With_Distance" xml:space="preserve">
    <value>根据距离筛选状态标签</value>
  </data>
  <data name="Label_Show_State_Label" xml:space="preserve">
    <value>显示状态标签</value>
  </data>
  <data name="EventMenu_Custom_Events" xml:space="preserve">
    <value>自定义事件</value>
  </data>
  <data name="Hint_Action_Shortcuts_OSX" xml:space="preserve">
    <value>双击动作标题编辑动作名称。
按住 Cmd键单击折页展开或折叠所有动作。
按住 Cmd键单击启用复选框可以启用或禁用的所有动作。</value>
  </data>
  <data name="BugReportWindow_Your_E_mail" xml:space="preserve">
    <value>您的电子邮件</value>
  </data>
  <data name="Label_Value" xml:space="preserve">
    <value>值</value>
  </data>
  <data name="Tootlip_Action_Renamed" xml:space="preserve">
    <value>标题: {0}
动作: {1}
{2}</value>
  </data>
  <data name="Menu_GraphView_Set_Color" xml:space="preserve">
    <value>设置颜色 /</value>
  </data>
  <data name="Label_State" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="Menu_Paste_Actions_Before" xml:space="preserve">
    <value>在之前粘贴动作</value>
  </data>
  <data name="Label_NOTES" xml:space="preserve">
    <value>备注</value>
  </data>
  <data name="Error_Missing_PlayMaker_dll" xml:space="preserve">
    <value>找不到 PlayMaker.dll...</value>
  </data>
  <data name="Label_Event" xml:space="preserve">
    <value>事件</value>
  </data>
  <data name="Label_Red" xml:space="preserve">
    <value>红色</value>
  </data>
  <data name="Label_Int" xml:space="preserve">
    <value>Int（整数值）</value>
  </data>
  <data name="Label_Add" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="Label_Debug" xml:space="preserve">
    <value>调试</value>
  </data>
  <data name="Label_Green" xml:space="preserve">
    <value>绿色</value>
  </data>
  <data name="Tooltip_Browse_Animator_Parameters" xml:space="preserve">
    <value>在GameObject上浏览 {0} 参数</value>
  </data>
  <data name="Label_Float" xml:space="preserve">
    <value>Float（浮点值）</value>
  </data>
  <data name="Label_Color" xml:space="preserve">
    <value>Color（颜色）</value>
  </data>
  <data name="Tooltip_Control_Mouse_Cursor" xml:space="preserve">
    <value>如果您有需要控制鼠标光标的脚本禁用此选项。</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Network_Setup_Errors_Tooltip" xml:space="preserve">
    <value>如果使用的其他网络库，例如photon，禁用此选项。</value>
  </data>
  <data name="FsmBuilder_Postfix_Copy" xml:space="preserve">
    <value>-副本</value>
  </data>
  <data name="ProductCopyright" xml:space="preserve">
    <value>© Hutong Games LLC. （胡同游戏）保留所有权利。</value>
  </data>
  <data name="Label_Max_Loop_Override" xml:space="preserve">
    <value>覆盖最大循环次数参数</value>
  </data>
  <data name="FilterMenu_All_FSMs" xml:space="preserve">
    <value>所有的状态机</value>
  </data>
  <data name="CustomActionWizard_Group_Category" xml:space="preserve">
    <value>类别</value>
  </data>
  <data name="Label_Enable_GUILayout" xml:space="preserve">
    <value>启用 GUILayout</value>
  </data>
  <data name="Menu_GraphView_Link_Style_Default" xml:space="preserve">
    <value>链接样式/默认值</value>
  </data>
  <data name="Label_Inspector" xml:space="preserve">
    <value>检查面板</value>
  </data>
  <data name="BugReportWindow_How_can_we_reproduce_it" xml:space="preserve">
    <value>2、我们如何重现它</value>
  </data>
  <data name="PasteVariables_Warning_Some_variables_already_exist__overwrite_values" xml:space="preserve">
    <value>一些变量已经存在，是否覆盖值？</value>
  </data>
  <data name="Menu_Find_Action" xml:space="preserve">
    <value>查找...</value>
  </data>
  <data name="Fixed_Graph_View_Bounds" xml:space="preserve">
    <value>PlayMaker ︰ 固定图表视图的边界。</value>
  </data>
  <data name="ActionEditor_Required_Field_Warning" xml:space="preserve">
    <value>*</value>
  </data>
  <data name="FsmErrorChecker_MouseEventsNeedCollider" xml:space="preserve">
    <value>所有者需要碰撞或 GUI 元素来响应鼠标事件 ！</value>
  </data>
  <data name="FsmTimeline_Title" xml:space="preserve">
    <value>状态机时间轴</value>
  </data>
  <data name="Hint_State_Inspector_Workflow" xml:space="preserve">
    <value>若要快速编辑名称选择一个状态后按 tab 键。
使用该名称旁边的设置菜单或右键单击下方空白获得更多的选项。</value>
  </data>
  <data name="Menu_Make_Instance" xml:space="preserve">
    <value>实例化</value>
  </data>
  <data name="Tooltip_Global_Variables_Used_Count" xml:space="preserve">
    <value>使用全局变量的状态机的数目。</value>
  </data>
  <data name="Command_Reset_Action_Name" xml:space="preserve">
    <value>重置动作名称</value>
  </data>
  <data name="Menu_GraphView_Link_Style_Circuit" xml:space="preserve">
    <value>链接样式/电路</value>
  </data>
  <data name="Label_Use_Template" xml:space="preserve">
    <value>使用模板</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Prefab_Restrictions" xml:space="preserve">
    <value>Prefab限制检查</value>
  </data>
  <data name="ActionUtility_Add_Animation_Clip_to_GameObject" xml:space="preserve">
    <value>将动画剪辑添加到 GameObject 吗？</value>
  </data>
  <data name="Label_Create_Event" xml:space="preserve">
    <value>创建事件</value>
  </data>
  <data name="ErrorSelector_Setup_Errors" xml:space="preserve">
    <value>设置错误: {0}</value>
  </data>
  <data name="Tooltip_Enable_Breakpoints" xml:space="preserve">
    <value>在状态机中启用/禁用断点。注意 ︰ 使用 首选项 &gt; 断点启用 进行全局启用/禁用。</value>
  </data>
  <data name="Command_Add_Global_Variable" xml:space="preserve">
    <value>添加全局变量</value>
  </data>
  <data name="Tooltip_Camera_Distance" xml:space="preserve">
    <value>距离从主相机开始计算</value>
  </data>
  <data name="FsmEditorSettings_Disable_Tool_Windows_When_Game_Is_Playing" xml:space="preserve">
    <value>当游戏运行时，禁用工具窗口</value>
  </data>
  <data name="WelcomeWindow_Docs_More" xml:space="preserve">
    <value>浏览全面的在线文档。</value>
  </data>
  <data name="Hint_Required_Fields" xml:space="preserve">
    <value>* 必填字段</value>
  </data>
  <data name="Tooltip_Edit_Variable_Type" xml:space="preserve">
    <value>选择存储的信息的类型。滚动动作参数浏览每个参数的类型。</value>
  </data>
  <data name="Log_Loading_all_FSMs" xml:space="preserve">
    <value>加载所有PlayMakerFSM 组件的GameObject...</value>
  </data>
  <data name="FsmEditorSettings_Draw_Links_Behind_States" xml:space="preserve">
    <value>在状态后绘制连接</value>
  </data>
  <data name="FsmEditorSettings_Zoom_Speed" xml:space="preserve">
    <value>缩放速度</value>
  </data>
  <data name="FsmEditorSettings_Zoom_Range" xml:space="preserve">
    <value>缩放范围</value>
  </data>
  <data name="Menu_NewVariable" xml:space="preserve">
    <value>新建变量。。。</value>
  </data>
  <data name="FsmEditorSettings_Custom_Colors_Name_Hint" xml:space="preserve">
    <value>在菜单中显示自定义的颜色名称。</value>
  </data>
  <data name="FsmEditorSettings_Enable_Real_Time_Error_Checker" xml:space="preserve">
    <value>启用实时错误检查器</value>
  </data>
  <data name="Menu_Hide_Obsolete_Actions" xml:space="preserve">
    <value>隐藏淘汰的动作</value>
  </data>
  <data name="Dialog_Delete_Unused_Events_Are_you_sure" xml:space="preserve">
    <value>是否确实要删除 {0} 未使用的事件吗？</value>
  </data>
  <data name="ActionEditor_Click_to_Add_Transition_to_State" xml:space="preserve">
    <value>[单击后给状态添加过渡]</value>
  </data>
  <data name="Error_Bad_screenshot_texture" xml:space="preserve">
    <value>损坏的截图！</value>
  </data>
  <data name="Label_Tool_Windows_disabled_when_playing" xml:space="preserve">
    <value>播放时禁用工具窗口。请参阅首选项。</value>
  </data>
  <data name="FsmEditorSettings_DoDebuggingSettings_Infinite_Loop_Threshold_Tooltip" xml:space="preserve">
    <value>状态立即进入自身状态，被认为是无限循环后会强制退出，该数值确定可以进入循环的最大次数</value>
  </data>
  <data name="Warning_Some_actions_have_changed_since_FSMs_were_saved" xml:space="preserve">
    <value>一些动作在状态机保存后产生了更改！已成功更新所有参数。检查PlayMaker控制台获取详细信息。注意： 您必须在生成程序之前重新保存状态机！</value>
  </data>
  <data name="Menu_Use_Template" xml:space="preserve">
    <value>使用模板/{0}/{1}</value>
  </data>
  <data name="FsmEditorSettings_Draw_Playmaker_Gizmos_in_Hierarchy" xml:space="preserve">
    <value>在Hierarchy视图中绘制Playmaker线框</value>
  </data>
  <data name="Label_Network_Sync" xml:space="preserve">
    <value>网络同步</value>
  </data>
  <data name="ActionEditor_EditFsmProperty_Target_Object" xml:space="preserve">
    <value>目标对象</value>
  </data>
  <data name="Menu_Show_State_Labels_in_Game_View" xml:space="preserve">
    <value>在游戏视图中显示状态标签</value>
  </data>
  <data name="Dialog_Delete_Unused_Global_Variables_Are_you_sure" xml:space="preserve">
    <value>是否确实要删除 {0} 未使用的全局变量吗？</value>
  </data>
  <data name="Command_Finished" xml:space="preserve">
    <value>打完收工！</value>
  </data>
  <data name="ActionEditor_Label_Input" xml:space="preserve">
    <value>输入</value>
  </data>
  <data name="Hint_Error_Checker_Settings" xml:space="preserve">
    <value>这些设置控制编辑时错误检查器的行为。错误检查器查找常见设置错误。单击图表视图左下角的错误计数以打开错误检查窗口。</value>
  </data>
  <data name="Menu_FsmLog_Show_State_Exit" xml:space="preserve">
    <value>显示状态退出</value>
  </data>
  <data name="Tooltip_Action" xml:space="preserve">
    <value>动作: {0}
{1}</value>
  </data>
  <data name="Command_Global_Variables" xml:space="preserve">
    <value>全局变量</value>
  </data>
  <data name="Label_Object_Type" xml:space="preserve">
    <value>对象类型</value>
  </data>
  <data name="Label_Set_Value" xml:space="preserve">
    <value>设置值</value>
  </data>
  <data name="Label_GUITexture_State_Labels" xml:space="preserve">
    <value>GUITexture 状态标签</value>
  </data>
  <data name="Menu_Paste_Actions" xml:space="preserve">
    <value>粘贴动作</value>
  </data>
  <data name="FsmEditorSettings_Select_Game_Objects_With_FSMs_in_Game_View" xml:space="preserve">
    <value>在游戏视图中选择拥有状态机的游戏对象</value>
  </data>
  <data name="Menu_Add_FSM" xml:space="preserve">
    <value>添加状态机</value>
  </data>
  <data name="ActionReportWindow_No_warnings_or_errors___" xml:space="preserve">
    <value>没有警告或错误......</value>
  </data>
  <data name="Menu_Prefab_Actions" xml:space="preserve">
    <value>预设的动作</value>
  </data>
  <data name="Menu_Edit_Name" xml:space="preserve">
    <value>编辑名称</value>
  </data>
  <data name="FsmEditorSettings_Mouse_Wheel_Scrolls_Graph_View" xml:space="preserve">
    <value>鼠标滚轮滚动图表视图</value>
  </data>
  <data name="Tooltip_Event_Browser_Button_in_Events_Tab" xml:space="preserve">
    <value>使用事件浏览器来管理您的项目中的事件。</value>
  </data>
  <data name="Menu_Find_Action_In_Browser" xml:space="preserve">
    <value>在浏览器中的查找动作</value>
  </data>
  <data name="Menu_Reconnect_to_Prefab" xml:space="preserve">
    <value>重新连接到Prefab预设</value>
  </data>
  <data name="FsmErrorChecker_RequiresComponentError" xml:space="preserve">
    <value>需要提供GameObject</value>
  </data>
  <data name="ErrorSelector_Title" xml:space="preserve">
    <value>错误检查</value>
  </data>
  <data name="ActionEditor_Store_Object" xml:space="preserve">
    <value>存储对象</value>
  </data>
  <data name="Menu_Auto_Refresh_Action_Usage" xml:space="preserve">
    <value>自动刷新动作使用方法</value>
  </data>
  <data name="Menu_Paste_Value" xml:space="preserve">
    <value>粘贴值</value>
  </data>
  <data name="Hint_Select_a_Game_Object___" xml:space="preserve">
    <value>选择游戏对象</value>
  </data>
  <data name="FsmEditorSettings_Show_Scrollbars_All_The_Time" xml:space="preserve">
    <value>任何时候都显示滚动条</value>
  </data>
  <data name="FsmEditorSettings_Draw_Playmaker_Gizmos_in_Hierarchy_Tooltip" xml:space="preserve">
    <value>在Hierarchy视图中带状态机的物体上绘制Playmaker线框</value>
  </data>
  <data name="Command_Delete_Action" xml:space="preserve">
    <value>删除动作</value>
  </data>
  <data name="CustomActionWizard_Code_Preview" xml:space="preserve">
    <value>代码预览</value>
  </data>
  <data name="Label_Set_Variable_Category" xml:space="preserve">
    <value>设置变量的类别</value>
  </data>
  <data name="AboutPlaymaker_Version_Info" xml:space="preserve">
    <value>版本: {0}</value>
  </data>
  <data name="Menu_Transition_Event" xml:space="preserve">
    <value>过渡事件</value>
  </data>
  <data name="ActionSelector_Disabled_when_playing" xml:space="preserve">
    <value>播放时禁用。请参阅动作浏览器设置菜单。</value>
  </data>
  <data name="Tooltip_Inspector" xml:space="preserve">
    <value>在 PlayMakerFSM 检查面板中显示此变量</value>
  </data>
  <data name="Menu_NewEvent" xml:space="preserve">
    <value>新事件......</value>
  </data>
  <data name="VersionInfo_PREVIEW_VERSION" xml:space="preserve">
    <value>预览版本</value>
  </data>
  <data name="Error_Failed_to_load_texture__" xml:space="preserve">
    <value>未能加载纹理 ︰ </value>
  </data>
  <data name="ErrorSelector_Runtime_Errors" xml:space="preserve">
    <value>运行时错误: {0}</value>
  </data>
  <data name="FsmEditorSettings_Enable_Real_Time_Error_Checker_Tooltip" xml:space="preserve">
    <value>如果较大的项目在编辑器中开始慢下来，您可以禁用实时错误检查并在需要时点击错误检查窗口中的手动刷新按钮。</value>
  </data>
  <data name="Command_Delete_Variables_Are_you_sure" xml:space="preserve">
    <value>是否确实要删除 {0} 未使用的变量吗？</value>
  </data>
  <data name="Command_Add_Global_Transition" xml:space="preserve">
    <value>添加全局过渡</value>
  </data>
  <data name="Menu_Move_Transition_Up" xml:space="preserve">
    <value>向上移动过渡</value>
  </data>
  <data name="Label_Events_Count" xml:space="preserve">
    <value>事件 [{0}]</value>
  </data>
  <data name="Command_Change_Global_Variable_Type" xml:space="preserve">
    <value>更改全局变量类型</value>
  </data>
  <data name="Error_Invalid_Name" xml:space="preserve">
    <value>无效的名称 ！</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Transitions_Missing_Targets_Tooltip" xml:space="preserve">
    <value>检查丢失目标状态的转换</value>
  </data>
  <data name="Tooltip_GUITexture_State_Labels" xml:space="preserve">
    <value>在 GUITextures 上绘制活动状态标签。</value>
  </data>
  <data name="Dialog_Delete_Unused_Events" xml:space="preserve">
    <value>删除未使用的事件</value>
  </data>
  <data name="Command_Add_Action" xml:space="preserve">
    <value>添加动作</value>
  </data>
  <data name="CustomActionWizard_Label_Description" xml:space="preserve">
    <value>说明</value>
  </data>
  <data name="AboutPlaymaker_Special_Thanks_People" xml:space="preserve">
    <value>艾琳 · 柯、 凯末尔 · 阿玛阿星汉、 布鲁斯 · 布隆伯格、 史蒂夫 · 格隆林思琪、 李赫普勒、 巴特 · 西蒙、 卢卡斯哲、 约阿希姆 · 安特、 杰得阿利、 詹姆斯 · 默奇森、 郑小杭、 安杰伊 · 卢卡斯基、 凡妮莎韦斯利、 马立· 乐迪维娜、 鲍勃·巴克比勒、 让 · 维恩、 马多克斯和PlayMaker官方论坛！</value>
  </data>
  <data name="Menu_Delete_Unused_Variables" xml:space="preserve">
    <value>删除未使用的变量</value>
  </data>
  <data name="Label_Enable_Editor_When_Playing" xml:space="preserve">
    <value>播放时启用编辑器</value>
  </data>
  <data name="GlobalVariablesWindow_Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="Command_Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="WelcomeWindow_Tutorials_More" xml:space="preserve">
    <value>订阅我们的YouTube 频道更新。</value>
  </data>
  <data name="Menu_Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="FsmEditorSettings_Show_Minimap" xml:space="preserve">
    <value>显示迷你地图</value>
  </data>
  <data name="Error_Editing_Action" xml:space="preserve">
    <value>错误编辑动作</value>
  </data>
  <data name="FsmEditorSettings_Restore_Default_Settings" xml:space="preserve">
    <value>恢复默认设置</value>
  </data>
  <data name="FsmEditorSettings_Reset_Default_Colors" xml:space="preserve">
    <value>重置默认颜色</value>
  </data>
  <data name="ActionEditor_Show_FSM" xml:space="preserve">
    <value>显示状态机</value>
  </data>
  <data name="Label_New_Template" xml:space="preserve">
    <value>新模板</value>
  </data>
  <data name="Command_Browse" xml:space="preserve">
    <value>浏览</value>
  </data>
  <data name="Command_Set_Link_Constraint" xml:space="preserve">
    <value>设置链接约束</value>
  </data>
  <data name="Menu_Add_Template" xml:space="preserve">
    <value>添加模板 / {0} / {1}</value>
  </data>
  <data name="Label_Yellow" xml:space="preserve">
    <value>黄色</value>
  </data>
  <data name="Hint_Expose_Events_and_Variables" xml:space="preserve">
    <value>勾选事件和变量选项卡中的检查面板选项，将事件和变量显示在检查面板中。
运行游戏时，您可以使用他们来控制和微调状态机。</value>
  </data>
  <data name="Command_Console" xml:space="preserve">
    <value>控制台</value>
  </data>
  <data name="Label_Editing_of_Prefab_Instance_is_disabled" xml:space="preserve">
    <value>编辑预设实例的功能禁用。请参阅首选项。</value>
  </data>
  <data name="AboutPlaymaker_Release_Notes" xml:space="preserve">
    <value>发行说明</value>
  </data>
  <data name="Menu_Move_Transition_Down" xml:space="preserve">
    <value>向下移动过渡</value>
  </data>
  <data name="FsmEditorSettings_Forward_Playmaker_Log_to_Unity_Log" xml:space="preserve">
    <value>推送Playmaker日志到Unity的日志中</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Transitions_Missing_Events_Tooltip" xml:space="preserve">
    <value>检查丢失事件设置的转换</value>
  </data>
  <data name="Menu_GraphView_Toggle_Breakpoint" xml:space="preserve">
    <value>切换断点</value>
  </data>
  <data name="Command_Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="Label_Refresh_Template" xml:space="preserve">
    <value>刷新模板</value>
  </data>
  <data name="Label_Variable" xml:space="preserve">
    <value>变量</value>
  </data>
  <data name="Menu_Clear_Transition_Event" xml:space="preserve">
    <value>清除过渡事件</value>
  </data>
  <data name="Label_FSM_Uses_Template" xml:space="preserve">
    <value>使用模板</value>
  </data>
  <data name="FilterMenu_On_Selected_Objects" xml:space="preserve">
    <value>在选定对象上</value>
  </data>
  <data name="Dialog_Option_Select_Template" xml:space="preserve">
    <value>选择模板</value>
  </data>
  <data name="Dialog_Edit_Variable_Type" xml:space="preserve">
    <value>编辑变量类型</value>
  </data>
  <data name="Command_Show" xml:space="preserve">
    <value>显示</value>
  </data>
  <data name="Command_Open" xml:space="preserve">
    <value>打开</value>
  </data>
  <data name="Command_Edit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="Command_Copy" xml:space="preserve">
    <value>拷贝</value>
  </data>
  <data name="Tooltip_Variables_Set_To_Network_Sync" xml:space="preserve">
    <value>在变量选项卡中设置为 NetworkSync 的变量。</value>
  </data>
  <data name="FsmEditorSettings_Snap_Grid_Size_Tooltip" xml:space="preserve">
    <value>按住ctrl 键拖动状态时捕捉网格的尺寸。</value>
  </data>
  <data name="Menu_Make_Animation_State" xml:space="preserve">
    <value>添加动画状态</value>
  </data>
  <data name="ActionEditor_Store_Quaternion" xml:space="preserve">
    <value>存储四元数</value>
  </data>
  <data name="FsmEditorSettings_Forward_Playmaker_Log_to_Unity_Log_Tooltip" xml:space="preserve">
    <value>Playmaker日志可以很容易根据状态机筛选，但Unity日志可以查看在上下文中的所有日志事件。</value>
  </data>
  <data name="Label_PREVIEW_VERSION" xml:space="preserve">
    <value>预览版本-编辑已禁用</value>
  </data>
  <data name="Label_Unlock" xml:space="preserve">
    <value>解锁</value>
  </data>
  <data name="Tooltip_Network_Sync" xml:space="preserve">
    <value>通过网络同步此变量。注意： 观察此 PlayMakerFSM组件的GameObject上需要 NetworkView 组件。</value>
  </data>
  <data name="Hint_Select_Game_Object" xml:space="preserve">
    <value>选择游戏对象</value>
  </data>
  <data name="Command_New" xml:space="preserve">
    <value>新增</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Setup_Errors_With_Mouse_Events_Tooltip" xml:space="preserve">
    <value>检查鼠标事件的所有者设置。</value>
  </data>
  <data name="Label_Vector3" xml:space="preserve">
    <value>Vector3（三维向量）</value>
  </data>
  <data name="Label_Vector2" xml:space="preserve">
    <value>Vector2（二维向量）</value>
  </data>
  <data name="Dialog_Paste_Variables" xml:space="preserve">
    <value>粘贴变量</value>
  </data>
  <data name="Error_Failed_to_draw_inspector" xml:space="preserve">
    <value>绘制检查面板失败！</value>
  </data>
  <data name="Menu_Debug_Variables" xml:space="preserve">
    <value>调试变量值</value>
  </data>
  <data name="Menu_GraphView_Set_Watermark" xml:space="preserve">
    <value>设置水印</value>
  </data>
  <data name="Menu_GraphView_Link_Style_Bezier" xml:space="preserve">
    <value>链接样式/贝塞尔曲线</value>
  </data>
  <data name="Menu_GraphView_Sent_By" xml:space="preserve">
    <value>发送自....../</value>
  </data>
  <data name="ActionEditor_Store_Material" xml:space="preserve">
    <value>存储材质</value>
  </data>
  <data name="ActionEditor_Store_String" xml:space="preserve">
    <value>存储字符串</value>
  </data>
  <data name="Hint_FsmLog" xml:space="preserve">
    <value>使用状态机日志检查状态机在运行时的行为。
当调试流启用时，你可以暂停游戏并在状态改变间步进和查看变量值！</value>
  </data>
  <data name="Menu_FsmLog_Show_TimeCode" xml:space="preserve">
    <value>显示时间码</value>
  </data>
  <data name="ActionEditor_Tooltip_Parameter_XXX" xml:space="preserve">
    <value>参数: {0}</value>
  </data>
  <data name="Dialog_Globals_Created" xml:space="preserve">
    <value>PlayMaker全局资源创建：
{0}/Resources/PlayMakerGlobals.asset
注意： 复制此文件或使用 工具 &gt; 导出全局  在不同的项目之间传输全局变量。</value>
  </data>
  <data name="WelcomeWindow_Samples" xml:space="preserve">
    <value>示例场景</value>
  </data>
  <data name="FsmErrorChecker_MissingActionError" xml:space="preserve">
    <value>损坏的动作数组 ！缺少动作 ！</value>
  </data>
  <data name="FsmEditorSettings_Disable_Error_Checker_When_Game_Is_Playing" xml:space="preserve">
    <value>在游戏运行时禁用错误检查</value>
  </data>
  <data name="Label_Material" xml:space="preserve">
    <value>Material（材质）</value>
  </data>
  <data name="VersionInfo_NACL_VERSION" xml:space="preserve">
    <value>NaCl 版本</value>
  </data>
  <data name="BugReportWindow_Bug_Description_Label" xml:space="preserve">
    <value>说明</value>
  </data>
  <data name="GlobalsWindow_Title" xml:space="preserve">
    <value>全局</value>
  </data>
  <data name="ActionEditor_Tooltip_Send_Event_to_GameObject" xml:space="preserve">
    <value>将事件发送到此游戏对象的所有状态机上。</value>
  </data>
  <data name="Label_DISABLED" xml:space="preserve">
    <value>[已禁用]</value>
  </data>
  <data name="Menu_GraphView_Copy_States" xml:space="preserve">
    <value>拷贝状态</value>
  </data>
  <data name="Labels_Use_Import_Globals_" xml:space="preserve">
    <value>使用 Import Globals 将导出的全局变量导入到另一个项目中。</value>
  </data>
  <data name="Tooltip_Hide_Unused" xml:space="preserve">
    <value>隐藏未使用的可选动作参数 （每个状态）。</value>
  </data>
  <data name="Menu_GraphView_Delete_States" xml:space="preserve">
    <value>删除状态</value>
  </data>
  <data name="Tooltip_Lock_and_password_protect_FSM" xml:space="preserve">
    <value>使用密码锁保护状态机</value>
  </data>
  <data name="Tooltip_Enable_GUILayout" xml:space="preserve">
    <value>禁用 GUILayout 可以提高GUI 动作的性能，特别是在移动设备上。注意： 您不能在禁用GUILayout时使用GUILayout动作。</value>
  </data>
  <data name="Dialog_CreateTemplate_Errors" xml:space="preserve">
    <value>创建的模板有一些错误 ！
请选择要查看错误日志的模板。
注 ︰ 模板不能引用场景对象 ！</value>
  </data>
  <data name="Error_Missing_Animation_Component" xml:space="preserve">
    <value>丢失动画组件</value>
  </data>
  <data name="Label_Model_Prefab_Instance" xml:space="preserve">
    <value>模型Prefab预设实例</value>
  </data>
  <data name="Tooltip_Events_Used_By_States" xml:space="preserve">
    <value>使用该事件的状态的数目。</value>
  </data>
  <data name="Command_Add_Template_to_Selected" xml:space="preserve">
    <value>将模板添加到所选物体</value>
  </data>
  <data name="FsmEditorSettings_Dim_Finished_Actions_Tooltip" xml:space="preserve">
    <value>在状态选项卡上已经完成的动作将会变暗。注意：并未禁用UI，您仍然可以点击控制这些动作。</value>
  </data>
  <data name="Menu_Delete_Transition" xml:space="preserve">
    <value>删除过渡</value>
  </data>
  <data name="Option_Use_Variable" xml:space="preserve">
    <value>使用变量</value>
  </data>
  <data name="Menu_Type_Actions" xml:space="preserve">
    <value>{0} 动作</value>
  </data>
  <data name="Error_Could_not_create_directory__" xml:space="preserve">
    <value>不能创建目录: {0}</value>
  </data>
  <data name="Dialog_No_Globals_to_import" xml:space="preserve">
    <value>没有要导入的全局变量 ！</value>
  </data>
  <data name="Command_Paste_States" xml:space="preserve">
    <value>粘贴状态</value>
  </data>
  <data name="FsmEditorSettings_Minimap" xml:space="preserve">
    <value>迷你地图</value>
  </data>
  <data name="Command_Set_As_Start_State" xml:space="preserve">
    <value>设置为起始状态</value>
  </data>
  <data name="WelcomeWindow_Title" xml:space="preserve">
    <value>欢迎光临PlayMaker</value>
  </data>
  <data name="Label_You_cannot_undo_this_action" xml:space="preserve">
    <value>您无法撤消此操作 ！</value>
  </data>
  <data name="FsmSelector_Tootlip_State" xml:space="preserve">
    <value>开始或当前状态</value>
  </data>
  <data name="Label_Default" xml:space="preserve">
    <value>默认</value>
  </data>
  <data name="Menu_Check_for_Setup_Errors_With_Mouse_Events" xml:space="preserve">
    <value>检查鼠标事件的设置错误</value>
  </data>
  <data name="Label_Purple" xml:space="preserve">
    <value>紫色</value>
  </data>
  <data name="ActionEditor_EditFsmEventTarget_Exclude_Self" xml:space="preserve">
    <value>排除自己</value>
  </data>
  <data name="FsmEditorSettings_Category_Graph_Styles" xml:space="preserve">
    <value>图表样式</value>
  </data>
  <data name="Label_Prefab" xml:space="preserve">
    <value>Prefab预设</value>
  </data>
  <data name="BugReportWindow_Copy_Tooltip" xml:space="preserve">
    <value>将报告复制到剪贴板。</value>
  </data>
  <data name="FsmEditorSettings_Snap_Grid_Size" xml:space="preserve">
    <value>网格捕捉尺寸</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Setup_Errors_With_Collision_Events_Tooltip" xml:space="preserve">
    <value>检查碰撞事件的所有者设置。</value>
  </data>
  <data name="ActionEditor_Send_To_Children_Tooltip" xml:space="preserve">
    <value>将事件发送到所有子物体</value>
  </data>
  <data name="Tooltip_EventManager_Inspector_Checkbox" xml:space="preserve">
    <value>勾选后将此事件显示在检查面板中成为一个事件按钮</value>
  </data>
  <data name="File_Failed_To_Create_Directory" xml:space="preserve">
    <value>不能创建目录: {0}</value>
  </data>
  <data name="CustomActionWizard_FileExists_Error" xml:space="preserve">
    <value>动作文件已存在 ！</value>
  </data>
  <data name="Menu_GraphView_Link_Color" xml:space="preserve">
    <value>链接颜色 /</value>
  </data>
  <data name="Label_Selected_Template" xml:space="preserve">
    <value>模版: {0}</value>
  </data>
  <data name="Command_Move_State" xml:space="preserve">
    <value>移动状态</value>
  </data>
  <data name="Command_Add_New_State" xml:space="preserve">
    <value>添加新的状态</value>
  </data>
  <data name="Menu_Check_for_Duplicate_Transition_Events" xml:space="preserve">
    <value>检查重复转换事件</value>
  </data>
  <data name="Label_Language" xml:space="preserve">
    <value>语言 （beta）</value>
  </data>
  <data name="Error_Globals_Cannot_Be_Scene_Objects" xml:space="preserve">
    <value>全局变量不能作为场景对象 ！使用prefabs预设或在运行时查找该场景对象......</value>
  </data>
  <data name="ActionEditor_Create_New_Template" xml:space="preserve">
    <value>创建新的模板</value>
  </data>
  <data name="CustomActionWizard_Copy_Code" xml:space="preserve">
    <value>复制代码</value>
  </data>
  <data name="Menu_Hide_Unused_Events" xml:space="preserve">
    <value>隐藏未使用的事件</value>
  </data>
  <data name="BugReportWindow_FrequencyChoices_Always" xml:space="preserve">
    <value>总是</value>
  </data>
  <data name="VersionInfol_STUDENT_VERSION" xml:space="preserve">
    <value>学生版</value>
  </data>
  <data name="Command_State_Browser" xml:space="preserve">
    <value>状态浏览器</value>
  </data>
  <data name="FsmEditorSettings_Enable_Transition_Effects_Tooltip" xml:space="preserve">
    <value>在状态过度时启用一个渐变效果。用于更容易的观察状态历史。如果在播放时Playmaker编辑器导致速度变慢可以尝试关闭该选项。</value>
  </data>
  <data name="ActionReportWindow_Sort_By_Action" xml:space="preserve">
    <value>按动作排序</value>
  </data>
  <data name="Label_Postfix_FSM" xml:space="preserve">
    <value>状态机</value>
  </data>
  <data name="FsmEditorSettings_Dim_Finished_Actions" xml:space="preserve">
    <value>将完成的动作标记成暗色</value>
  </data>
  <data name="Dialog_Replace_Start_State_Description" xml:space="preserve">
    <value>模板已定义起始状态。您想要替换当前的起始状态吗？</value>
  </data>
  <data name="Dialog_SaveGlobals_Created" xml:space="preserve">
    <value>PlayMakerGlobals 全局变量创建 ︰
{0}
使用菜单PlayMaker &gt; Tools &gt; Export and Import Globals命令，在项目工程之间复制全局变量。</value>
  </data>
  <data name="FsmEditorSettings_Select_State_On_Activated_Tooltip" xml:space="preserve">
    <value>激活的状态会被自动选择，选择的状态在inspector面板可见</value>
  </data>
  <data name="Dialog_Templates_can_only_be_saved_in_the_Project_s_Assets_folder" xml:space="preserve">
    <value>只可将模板保存在工程的Assets目录中 ！</value>
  </data>
  <data name="Menu_None_State" xml:space="preserve">
    <value>无 (状态)</value>
  </data>
  <data name="ToolWindow_Header_Transitions" xml:space="preserve">
    <value>过渡：</value>
  </data>
  <data name="Menu_None_Event" xml:space="preserve">
    <value>无 (事件)</value>
  </data>
  <data name="Tooltip_KeepDelayedEvents" xml:space="preserve">
    <value>通常退出活动状态时，删除延迟的事件。如果你想要保留延迟事件勾选该选项。</value>
  </data>
  <data name="Menu_Select_Next_FSM" xml:space="preserve">
    <value>选择下一个状态机</value>
  </data>
  <data name="Command_Delete_State" xml:space="preserve">
    <value>删除状态</value>
  </data>
  <data name="Dialog_Import_Globals_From" xml:space="preserve">
    <value>导入全局变量，来自：</value>
  </data>
  <data name="VersionInfo_TRIAL_VERSION" xml:space="preserve">
    <value>试用版</value>
  </data>
  <data name="Hint_Select_a_GameObject" xml:space="preserve">
    <value>选择一个 GameObject</value>
  </data>
  <data name="Label_Select" xml:space="preserve">
    <value>选择</value>
  </data>
  <data name="Menu_Check_for_Obsolete_Actions" xml:space="preserve">
    <value>检查淘汰的动作</value>
  </data>
  <data name="Command_Delete_Event" xml:space="preserve">
    <value>删除事件</value>
  </data>
  <data name="Label_String" xml:space="preserve">
    <value>String（字符串）</value>
  </data>
  <data name="FsmEditorSettings_Disable_the_Inspector_Panel_When_Game_Is_Playing" xml:space="preserve">
    <value>在运行游戏时禁用Inspector面板</value>
  </data>
  <data name="Command_Add_Transition" xml:space="preserve">
    <value>添加过渡</value>
  </data>
  <data name="FsmEditorSettings_Experimental" xml:space="preserve">
    <value>实验功能</value>
  </data>
  <data name="Command_Paste_Template" xml:space="preserve">
    <value>粘贴模板</value>
  </data>
  <data name="PlayMaker_installation_has_moved" xml:space="preserve">
    <value>PlayMaker安装目录已经移动，查找新的目标位置...</value>
  </data>
  <data name="Title_NewGlobalVariable" xml:space="preserve">
    <value>新建全局变量</value>
  </data>
  <data name="Error_Missing_Script" xml:space="preserve">
    <value>找不到动作脚本: {0}</value>
  </data>
  <data name="Could_not_find_PlayMaker_dll_" xml:space="preserve">
    <value>找不到 PlayMaker. dll...</value>
  </data>
  <data name="ActionEditor_Store_Int" xml:space="preserve">
    <value>存储整数值</value>
  </data>
  <data name="Label_Model_Prefab" xml:space="preserve">
    <value>模型Prefab预设</value>
  </data>
  <data name="Found_Prefab_with_FSM" xml:space="preserve">
    <value>发现带状态机的预设: </value>
  </data>
  <data name="Command_Move_Action_Up" xml:space="preserve">
    <value>向上移动动作</value>
  </data>
  <data name="Label_Property_Tooltip" xml:space="preserve">
    <value>属性的名称。
提示 ︰ 使用Unity的脚本参考手册查阅有关该属性的详细信息。</value>
  </data>
  <data name="Label_Enable_Breakpoints" xml:space="preserve">
    <value>启用断点</value>
  </data>
  <data name="Menu_GraphView_Paste_States" xml:space="preserve">
    <value>粘贴状态</value>
  </data>
  <data name="CustomActionWizard_Root_Folder" xml:space="preserve">
    <value>根目录</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Duplicate_Transition_Events_Tooltip" xml:space="preserve">
    <value>检查使用同一事件的状态转换。</value>
  </data>
  <data name="ToolWindow_Header_FSM_Tools" xml:space="preserve">
    <value>状态机工具：</value>
  </data>
  <data name="Error_Action_is_null_" xml:space="preserve">
    <value>动作为空（null）！</value>
  </data>
  <data name="Action_Enable_Logging" xml:space="preserve">
    <value>启用日志记录</value>
  </data>
  <data name="Title_NewVariable" xml:space="preserve">
    <value>新变量</value>
  </data>
  <data name="FsmEditorSettings_Default_Colors" xml:space="preserve">
    <value>默认颜色</value>
  </data>
  <data name="Label_Hide_Unused" xml:space="preserve">
    <value>隐藏未使用</value>
  </data>
  <data name="Dialog_ImportGlobals_Error" xml:space="preserve">
    <value>导入全局变量的错误 ︰</value>
  </data>
  <data name="Menu_Unused_Event" xml:space="preserve">
    <value>没有任何状态使用此事件...</value>
  </data>
  <data name="Command_Delete_Actions" xml:space="preserve">
    <value>删除动作</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Obsolete_Actions" xml:space="preserve">
    <value>检查淘汰的动作</value>
  </data>
  <data name="Label_Variables_Count" xml:space="preserve">
    <value>变量 [{0}]</value>
  </data>
  <data name="Command_Toggle_Hints" xml:space="preserve">
    <value>提示[F1]</value>
  </data>
  <data name="FsmEditorSettings_Enable_Logging_Tooltip" xml:space="preserve">
    <value>启用事件、状态变化等的日志记录。禁用该功能可以提高性能。</value>
  </data>
  <data name="Label_Orange" xml:space="preserve">
    <value>橙色</value>
  </data>
  <data name="GlobalVariablesWindow_Refresh_Used_Count_In_This_Scene" xml:space="preserve">
    <value>刷新在这个场景中使用的次数统计</value>
  </data>
  <data name="FsmLog_Label_Target" xml:space="preserve">
    <value>发送目标： </value>
  </data>
  <data name="Could_not_load_resource" xml:space="preserve">
    <value>无法加载资源 ︰ </value>
  </data>
  <data name="Menu_Check_for_Events_Not_Used_by_Target_FSM" xml:space="preserve">
    <value>检查目标状态机中没有被使用的事件</value>
  </data>
  <data name="Command_Move_Action_Down" xml:space="preserve">
    <value>向下移动动作</value>
  </data>
  <data name="Label_Object" xml:space="preserve">
    <value>Object（对象）</value>
  </data>
  <data name="Command_Paste_FSM_to_Selected" xml:space="preserve">
    <value>粘贴状态机到所选物体</value>
  </data>
  <data name="Label_Edit_Instance" xml:space="preserve">
    <value>编辑实例</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Transitions_Missing_Events" xml:space="preserve">
    <value>检查丢失事件设置的转换</value>
  </data>
  <data name="Label_No_Animations_On_Object" xml:space="preserve">
    <value>对象上没有动画</value>
  </data>
  <data name="Label_Edit_Variable" xml:space="preserve">
    <value>编辑变量</value>
  </data>
  <data name="Dialogs_Missing_Action" xml:space="preserve">
    <value>丢失动作</value>
  </data>
  <data name="Menu_Sent_By" xml:space="preserve">
    <value>发送自...</value>
  </data>
  <data name="Dialog_Rename_Event_Are_you_sure" xml:space="preserve">
    <value>这是一个全局事件，它将在全局范围内重命名...</value>
  </data>
  <data name="Label_Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="Menu_With_Global_Transitions" xml:space="preserve">
    <value>添加全局过渡</value>
  </data>
  <data name="Hint_Send_Event_To_Host" xml:space="preserve">
    <value>将事件发送到状态机并启动该状态机 （请参见运行状态机动作）。注意： 必须将事件标记为全局。</value>
  </data>
  <data name="Tooltip_Variable_Used_Count" xml:space="preserve">
    <value>在状态机中使用该变量的次数。</value>
  </data>
  <data name="Menu_Add_Before_Selected_Action" xml:space="preserve">
    <value>添加到选定的动作之前</value>
  </data>
  <data name="Menu_GraphView_Paste_Template_None" xml:space="preserve">
    <value>粘贴模板</value>
  </data>
  <data name="Command_Add_Selected_Event_To_FSM" xml:space="preserve">
    <value>将选定的事件添加到状态机</value>
  </data>
  <data name="Label_Check_Edit_Global_Variable" xml:space="preserve">
    <value>在其他场景中有可能使用此全局变量 ！
你确定你想要编辑此变量？</value>
  </data>
  <data name="Label_Events" xml:space="preserve">
    <value>事件</value>
  </data>
  <data name="Dialog_Paste_FSM_to_multiple_objects" xml:space="preserve">
    <value>粘贴状态机到多个对象？</value>
  </data>
  <data name="Command_Delete_Global_Transition" xml:space="preserve">
    <value>删除全局过渡</value>
  </data>
  <data name="Tooltip_Browse_Animations_on_GameObject" xml:space="preserve">
    <value>浏览GameObject上的动画</value>
  </data>
  <data name="Hint_Send_Event_To_SubFSMs" xml:space="preserve">
    <value>将事件发送到状态机的子状态机 （请参见运行状态机动作）。注意： 必须将事件标记为全局。</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Setup_Errors_With_Collision_Events" xml:space="preserve">
    <value>检查碰撞事件的设置错误</value>
  </data>
  <data name="SubMenu_More_" xml:space="preserve">
    <value>/ 更多...</value>
  </data>
  <data name="Label_Used" xml:space="preserve">
    <value>使用</value>
  </data>
  <data name="Label_Type" xml:space="preserve">
    <value>类型</value>
  </data>
  <data name="Label_Misc" xml:space="preserve">
    <value>杂项</value>
  </data>
  <data name="Label_Lock" xml:space="preserve">
    <value>锁定</value>
  </data>
  <data name="Label_Size" xml:space="preserve">
    <value>大小</value>
  </data>
  <data name="Label_Rect" xml:space="preserve">
    <value>Rect（矩形变量）</value>
  </data>
  <data name="Label_Info" xml:space="preserve">
    <value>信息</value>
  </data>
  <data name="Label_Name" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="Label_None" xml:space="preserve">
    <value>无</value>
  </data>
  <data name="Label_Enum" xml:space="preserve">
    <value>枚举</value>
  </data>
  <data name="Label_Edit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="Label_Cyan" xml:space="preserve">
    <value>青色</value>
  </data>
  <data name="Label_Blue" xml:space="preserve">
    <value>蓝色</value>
  </data>
  <data name="Label_Bool" xml:space="preserve">
    <value>Bool（布尔值）</value>
  </data>
  <data name="Hint_Global_Variables" xml:space="preserve">
    <value>全局变量可以被任何状态机访问。
右键单击某个变量查看使用它的状态机。</value>
  </data>
  <data name="Dialog_Add_FSM" xml:space="preserve">
    <value>添加状态机</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Required_Action_Fields" xml:space="preserve">
    <value>检查动作所需的参数</value>
  </data>
  <data name="Label_Action_Browser" xml:space="preserve">
    <value>动作浏览器</value>
  </data>
  <data name="Tooltip_Toggle_Minimap" xml:space="preserve">
    <value>切换迷你地图</value>
  </data>
  <data name="Command_Ping_Template_Asset" xml:space="preserve">
    <value>跟踪到模板资源文件</value>
  </data>
  <data name="Hint_GlobalsInspector_Shows_DEFAULT_Values" xml:space="preserve">
    <value>此检查面板显示全局变量的默认值。播放时若要查看当前值，请使用PlayMaker编辑器 （全局变量窗口，调试字段等。）</value>
  </data>
  <data name="Tooltip_Disable_editing_of_prefab_intances" xml:space="preserve">
    <value>禁用编辑预设的实例，除非用户明确确认。</value>
  </data>
  <data name="Menu_Set_State_Color" xml:space="preserve">
    <value>设置状态颜色 / {0}</value>
  </data>
  <data name="CustomActionWizard_Save_Button" xml:space="preserve">
    <value>保存自定义动作</value>
  </data>
  <data name="BugReportWindow_Bug_Title_Label" xml:space="preserve">
    <value>标题</value>
  </data>
  <data name="Menu_Paste_Template" xml:space="preserve">
    <value>粘贴模板 / {0} / {1}</value>
  </data>
  <data name="Tooltip_Documentation_Url" xml:space="preserve">
    <value>文档 Url...</value>
  </data>
  <data name="Hint_Right_Click_to_Add_FSM_to_Selected_Objects_" xml:space="preserve">
    <value>用鼠标右键单击为选定的对象添加状态机</value>
  </data>
  <data name="Menu_Select_Previous_FSM" xml:space="preserve">
    <value>选择之前的状态机</value>
  </data>
  <data name="Menu_Edit_Script" xml:space="preserve">
    <value>编辑脚本...</value>
  </data>
  <data name="Dialog_Delete_Event_Used_By" xml:space="preserve">
    <value>使用该事件的是</value>
  </data>
  <data name="Hint_EventManager" xml:space="preserve">
    <value>此面板显示选定状态机所使用的事件。
用鼠标右键单击一个事件选择使用该事件的状态。</value>
  </data>
  <data name="Menu_Clear_Transition_Target" xml:space="preserve">
    <value>清除过渡目标</value>
  </data>
  <data name="Command_Enable_All_Actions" xml:space="preserve">
    <value>启用所有动作</value>
  </data>
  <data name="Tooltip_GUIText_State_Labels" xml:space="preserve">
    <value>在 GUITexts 上绘制活动状态标签。</value>
  </data>
  <data name="BugReportWindow_Error" xml:space="preserve">
    <value>错误提交报告: {0}</value>
  </data>
  <data name="Command_Move_Up" xml:space="preserve">
    <value>向上移动</value>
  </data>
  <data name="BugReportWindow_Title" xml:space="preserve">
    <value>提交 Bug 报告</value>
  </data>
  <data name="FsmEditorSettings_Disable_the_Inspector_Panel_When_Game_Is_Playing_Tooltip" xml:space="preserve">
    <value>防止在游戏中查看图表视图时因为inspector面板减慢游戏速度。</value>
  </data>
  <data name="BugReportWindow_Success" xml:space="preserve">
    <value>非常感谢 Bug 报告 ！您应该很快就收到一封确认邮件...</value>
  </data>
  <data name="Menu_Action_Browser" xml:space="preserve">
    <value>动作浏览器</value>
  </data>
  <data name="Tooltip_Global_Variables" xml:space="preserve">
    <value>使用全局变量窗口来管理可以被任何状态机访问的全局变量。</value>
  </data>
  <data name="Error_Name_already_exists" xml:space="preserve">
    <value>名称已存在 ！</value>
  </data>
  <data name="Label_Minimap_Short" xml:space="preserve">
    <value>地图</value>
  </data>
  <data name="Command_Settings" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Setup_Errors_With_Mouse_Events" xml:space="preserve">
    <value>检查鼠标事件的设置错误</value>
  </data>
  <data name="Hint_PlayMakerGUI_Notes" xml:space="preserve">
    <value>-一个场景只应该有一个 PlayMakerGUI 物体。
-PlayMaker会自动添加该物体。
-在首选项中禁用自动添加。</value>
  </data>
  <data name="FsmEditorSettings_Jump_to_Breakpoint_Error_Tooltip" xml:space="preserve">
    <value>编辑器自动跳转到断点或错误。注意：这通常意味着切换到不同的状态机。</value>
  </data>
  <data name="Error_Failed_to_load_Property_Drawer" xml:space="preserve">
    <value>未能加载属性绘制检查面板参数类型。</value>
  </data>
  <data name="Menu_GraphView_Save_Screenshot" xml:space="preserve">
    <value>保存屏幕截图</value>
  </data>
  <data name="WelcomeWindow_Docs" xml:space="preserve">
    <value>在线文档</value>
  </data>
  <data name="Dialog_Cannot_Delete_Start_State" xml:space="preserve">
    <value>不能删除起始状态 ！</value>
  </data>
  <data name="Hint_Sequence_States" xml:space="preserve">
    <value>动作序列依次执行每个动作。当一个动作执行完成，接下来的动作开始执行。</value>
  </data>
  <data name="FsmEditorSettings_Enable_Watermarks_Tooltip" xml:space="preserve">
    <value>启用背景中的大标题文本和图形水印。注： 在速度较慢的计算机上禁用这些能提高编辑器速度。</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Events_Not_Used_by_Target_FSM_Tooltip" xml:space="preserve">
    <value>检查目标状态机中没有被使用的事件，例如检查动作发出的事件</value>
  </data>
  <data name="ActionEditor_EditFsmEventTarget_FSM_Component" xml:space="preserve">
    <value>状态机组件</value>
  </data>
  <data name="iTween_Path_Editing_Label_End" xml:space="preserve">
    <value>'{0}' 结束</value>
  </data>
  <data name="Hint_GraphView_Getting_Started" xml:space="preserve">
    <value>1.在任何视图中选择 GameObject。
2.在此视图中右键单击添加状态机。
3.建立状态机来控制 GameObject。</value>
  </data>
  <data name="WelcomeWindow_Addons_More" xml:space="preserve">
    <value>使用这些强大的扩展包来扩充PlayMaker的功能</value>
  </data>
  <data name="FsmEditorSettings_Show_State_Labels_in_Game_View" xml:space="preserve">
    <value>在游戏视图中显示状态标签</value>
  </data>
  <data name="Menu_Play_Animation" xml:space="preserve">
    <value>播放动画</value>
  </data>
  <data name="FsmEditorHint_General_Settings" xml:space="preserve">
    <value>PlayMaker常用设置。使用上方的下拉列表选择其他类别。</value>
  </data>
  <data name="Label_Enum_Value" xml:space="preserve">
    <value>枚举值</value>
  </data>
  <data name="Label_Global" xml:space="preserve">
    <value>全局</value>
  </data>
  <data name="Menu_Open_Log_Window" xml:space="preserve">
    <value>打开日志窗口</value>
  </data>
  <data name="Hint_GraphView_Shortcuts" xml:space="preserve">
    <value>F1 显示/隐藏
Ctrl 单击编辑区
Ctrl 单击状态
Ctrl 拖放过渡
Ctrl Shift 单击
Ctrl 拖放状态
Shift 拖放状态
Home
Alt 单击过渡
Ctrl 左/右
Ctrl 下
Ctrl 上</value>
  </data>
  <data name="FsmEditorSettings_FSM_Screenshots_Directory_Tooltip" xml:space="preserve">
    <value>状态机截图的保存位置。该路径是Unity项目的相对目录。注： 不建议使用Assets目录下的路径，因为Unity会将其添加到该项目的资源中。</value>
  </data>
  <data name="Label_Select_A_Watermark" xml:space="preserve">
    <value>选择一个水印：</value>
  </data>
  <data name="Command_Insert_Action" xml:space="preserve">
    <value>插入动作</value>
  </data>
  <data name="CustomActionWizard_Same_as_Category" xml:space="preserve">
    <value>和类别相同</value>
  </data>
  <data name="Menu_Show_State_Description" xml:space="preserve">
    <value>显示状态说明</value>
  </data>
  <data name="ActionSelector_Enable_Action_Browser_When_Playing" xml:space="preserve">
    <value>播放时启用动作浏览器</value>
  </data>
  <data name="Label_Missing_Dll_resource__" xml:space="preserve">
    <value>缺少 Dll resource: {0}</value>
  </data>
  <data name="Menu_GraphView_Save_Template" xml:space="preserve">
    <value>保存模板</value>
  </data>
  <data name="Command_Preferences" xml:space="preserve">
    <value>首选项</value>
  </data>
  <data name="Menu_Clear_Breakpoints" xml:space="preserve">
    <value>清除断点</value>
  </data>
  <data name="FilterMenu_FSMs_With_Errors" xml:space="preserve">
    <value>有错误的状态机</value>
  </data>
  <data name="Hint_State_Selector" xml:space="preserve">
    <value>状态浏览器可以让你快速在选定的状态机中选择任意状态。</value>
  </data>
  <data name="BugReportWindow_What_happened" xml:space="preserve">
    <value>1、发生了什么</value>
  </data>
  <data name="Command_Clear_Watermark" xml:space="preserve">
    <value>清除水印</value>
  </data>
  <data name="Menu_GraphView_Remove_FSM_Component" xml:space="preserve">
    <value>删除状态机组件</value>
  </data>
  <data name="FsmErrorChecker_TransitionMissingTargetError" xml:space="preserve">
    <value>过渡缺少目标状态 ！</value>
  </data>
  <data name="Tooltip_Event_GUI" xml:space="preserve">
    <value>选择或双击要添加的事件到选定的状态机中。用鼠标右键单击事件快速导航到使用该事件的状态机。</value>
  </data>
  <data name="FsmEditorSettings_Category_When_Game_Is_Playing" xml:space="preserve">
    <value>当游戏运行时</value>
  </data>
  <data name="Menu_Log_Window" xml:space="preserve">
    <value>日志窗口</value>
  </data>
  <data name="Label_Tooltip" xml:space="preserve">
    <value>提示</value>
  </data>
  <data name="Tooltip_Edit_in_the_PlayMaker_Editor" xml:space="preserve">
    <value>在PlayMaker编辑器中编辑</value>
  </data>
  <data name="Menu_Add_to_End_of_Action_List" xml:space="preserve">
    <value>将添加到动作列表的末尾</value>
  </data>
  <data name="Label_Variables" xml:space="preserve">
    <value>变量</value>
  </data>
  <data name="FsmEditorSettings_Enable_DebugFlow" xml:space="preserve">
    <value>启用 DebugFlow</value>
  </data>
  <data name="Error_Variable_Name_Taken" xml:space="preserve">
    <value>已使用的变量名称 ！</value>
  </data>
  <data name="Dialog_Delete_Template" xml:space="preserve">
    <value>删除模板</value>
  </data>
  <data name="FsmEditorSettings_Maximize_File_Compatibility" xml:space="preserve">
    <value>最大化文件兼容性</value>
  </data>
  <data name="FsmEditorSettings_DoDebuggingSettings_Show_State_Labels_Tooltip" xml:space="preserve">
    <value>在游戏视图中打开、关闭所有的状态标签</value>
  </data>
  <data name="Menu_Delete_All_Actions" xml:space="preserve">
    <value>删除所有动作</value>
  </data>
  <data name="Menu_Step_To_Next_State_Change_in_any_FSM" xml:space="preserve">
    <value>步进到任意状态机中下一个改变的状态</value>
  </data>
  <data name="ActionEditor_EditArray_Element_XXX" xml:space="preserve">
    <value>元素: {0}</value>
  </data>
  <data name="Dialog_No_unused_events" xml:space="preserve">
    <value>没有未使用的事件......</value>
  </data>
  <data name="Dialog_Add_FSM_Template_to_multiple_objects_" xml:space="preserve">
    <value>为多个对象添加状态机模版吗？</value>
  </data>
  <data name="Error_Could_not_load_watermarks" xml:space="preserve">
    <value>不能加载水印 ！</value>
  </data>
  <data name="WelcomeWindow_Photon_Cloud_More" xml:space="preserve">
    <value>构建可伸缩的MMOG、Fps或者其他的多人游戏
应用程序支持PC、 Mac、 浏览器、 移动设备或主机。</value>
  </data>
  <data name="ActionSelector_Preview" xml:space="preserve">
    <value>预览</value>
  </data>
  <data name="Dialogs_PreviewVersion_Note" xml:space="preserve">
    <value>注意 ︰此版本的PlayMaker中编辑功能被禁用 ！
请访问 HutongGames.com 了解更多关于PlayMaker的信息。</value>
  </data>
  <data name="Label_Browse" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="Label_Texture" xml:space="preserve">
    <value>Texture（纹理）</value>
  </data>
  <data name="FilterMenu_FSMs_in_Scene" xml:space="preserve">
    <value>场景中的状态机</value>
  </data>
  <data name="Tab2" xml:space="preserve">
    <value>		</value>
  </data>
  <data name="Tab3" xml:space="preserve">
    <value>			</value>
  </data>
  <data name="Menu_GraphView_Add_State" xml:space="preserve">
    <value>添加状态</value>
  </data>
  <data name="FsmEditorSettings_Auto_Add_PlayMakerGUI_to_Scene_Tooltip" xml:space="preserve">
    <value>当您添加或选择状态机时如果有必要将自动添加 PlayMakerGUI 物体到场景中。请注意，查看线框、OnGUI 动作和 iTween 路径的编辑需要场景中的 PlayMakerGUI 物体。</value>
  </data>
  <data name="Tooltip_Fsm_Docs" xml:space="preserve">
    <value>状态机文档</value>
  </data>
  <data name="Hint_DebugFlow_Disabled" xml:space="preserve">
    <value>Debug Flow模式： 当前禁用变量记录和其他状态信息记录。在首选项中启用此设置并且在你想要跟踪的每个状态机中单独设置。</value>
  </data>
  <data name="Tooltip_Refresh_Template" xml:space="preserve">
    <value>如果你已经更新了模板的内容，但看不到变化，请使用这个功能。</value>
  </data>
  <data name="FsmEditorSettings_Category_Editing" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="Menu_GraphView_Add_Global_Transition" xml:space="preserve">
    <value>添加全局过渡</value>
  </data>
  <data name="Menu_Paste_Variable_Values" xml:space="preserve">
    <value>粘贴变量值</value>
  </data>
</root>