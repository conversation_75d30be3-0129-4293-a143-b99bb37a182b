// Decompiled with JetBrains decompiler
// Type: PlayMakerProxyBase
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System.Collections.Generic;
using UnityEngine;

public abstract class PlayMakerProxyBase : MonoBehaviour
{
  public List<PlayMakerFSM> TargetFSMs = new List<PlayMakerFSM>();

  protected PlayMakerFSM[] playMakerFSMs => this.TargetFSMs.ToArray();

  private event PlayMakerProxyBase.TriggerEvent TriggerEventCallback;

  private event PlayMakerProxyBase.CollisionEvent CollisionEventCallback;

  private event PlayMakerProxyBase.ParticleCollisionEvent ParticleCollisionEventCallback;

  private event PlayMakerProxyBase.ControllerCollisionEvent ControllerCollisionEventCallback;

  private event PlayMakerProxyBase.Trigger2DEvent Trigger2DEventCallback;

  private event PlayMakerProxyBase.Collision2DEvent Collision2DEventCallback;

  public void AddTarget(PlayMakerFSM fsmTarget)
  {
    if (this.TargetFSMs.Contains(fsmTarget))
      return;
    this.TargetFSMs.Add(fsmTarget);
  }

  public bool HasTriggerEventDelegates() => this.TriggerEventCallback != null;

  public void AddTriggerEventCallback(PlayMakerProxyBase.TriggerEvent triggerEvent) => this.TriggerEventCallback += triggerEvent;

  public void RemoveTriggerEventCallback(PlayMakerProxyBase.TriggerEvent triggerEvent) => this.TriggerEventCallback -= triggerEvent;

  public void DoTriggerEventCallback(Collider other)
  {
    if (this.TriggerEventCallback == null)
      return;
    this.TriggerEventCallback(other);
  }

  public bool HasTrigger2DEventDelegates() => this.Trigger2DEventCallback != null;

  public void AddTrigger2DEventCallback(PlayMakerProxyBase.Trigger2DEvent triggerEvent) => this.Trigger2DEventCallback += triggerEvent;

  public void RemoveTrigger2DEventCallback(PlayMakerProxyBase.Trigger2DEvent triggerEvent) => this.Trigger2DEventCallback -= triggerEvent;

  public void DoTrigger2DEventCallback(Collider2D other)
  {
    if (this.Trigger2DEventCallback == null)
      return;
    this.Trigger2DEventCallback(other);
  }

  public bool HasCollisionEventDelegates() => this.CollisionEventCallback != null;

  public void AddCollisionEventCallback(PlayMakerProxyBase.CollisionEvent collisionEvent) => this.CollisionEventCallback += collisionEvent;

  public void RemoveCollisionEventCallback(PlayMakerProxyBase.CollisionEvent collisionEvent) => this.CollisionEventCallback -= collisionEvent;

  public void DoCollisionEventCallback(Collision collisionInfo)
  {
    if (this.CollisionEventCallback == null)
      return;
    this.CollisionEventCallback(collisionInfo);
  }

  public bool HasCollision2DEventDelegates() => this.Collision2DEventCallback != null;

  public void AddCollision2DEventCallback(PlayMakerProxyBase.Collision2DEvent collisionEvent) => this.Collision2DEventCallback += collisionEvent;

  public void RemoveCollision2DEventCallback(PlayMakerProxyBase.Collision2DEvent collisionEvent) => this.Collision2DEventCallback -= collisionEvent;

  public void DoCollision2DEventCallback(Collision2D collisionInfo)
  {
    if (this.Collision2DEventCallback == null)
      return;
    this.Collision2DEventCallback(collisionInfo);
  }

  public bool HasParticleCollisionEventDelegates() => this.ParticleCollisionEventCallback != null;

  public void AddParticleCollisionEventCallback(
    PlayMakerProxyBase.ParticleCollisionEvent collisionEvent)
  {
    this.ParticleCollisionEventCallback += collisionEvent;
  }

  public void RemoveParticleCollisionEventCallback(
    PlayMakerProxyBase.ParticleCollisionEvent collisionEvent)
  {
    this.ParticleCollisionEventCallback -= collisionEvent;
  }

  public void DoParticleCollisionEventCallback(GameObject go)
  {
    if (this.ParticleCollisionEventCallback == null)
      return;
    this.ParticleCollisionEventCallback(go);
  }

  public bool HasControllerCollisionEventDelegates() => this.ControllerCollisionEventCallback != null;

  public void AddControllerCollisionEventCallback(
    PlayMakerProxyBase.ControllerCollisionEvent collisionEvent)
  {
    this.ControllerCollisionEventCallback += collisionEvent;
  }

  public void RemoveControllerCollisionEventCallback(
    PlayMakerProxyBase.ControllerCollisionEvent collisionEvent)
  {
    this.ControllerCollisionEventCallback -= collisionEvent;
  }

  public void DoControllerCollisionEventCallback(ControllerColliderHit hitCollider)
  {
    if (this.ControllerCollisionEventCallback == null)
      return;
    this.ControllerCollisionEventCallback(hitCollider);
  }

  public delegate void TriggerEvent(Collider other);

  public delegate void CollisionEvent(Collision collisionInfo);

  public delegate void Trigger2DEvent(Collider2D other);

  public delegate void Collision2DEvent(Collision2D collisionInfo);

  public delegate void ParticleCollisionEvent(GameObject gameObject);

  public delegate void ControllerCollisionEvent(ControllerColliderHit hitCollider);
}
