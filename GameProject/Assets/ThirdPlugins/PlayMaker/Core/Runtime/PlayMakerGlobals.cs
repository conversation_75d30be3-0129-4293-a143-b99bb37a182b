// Decompiled with JetBrains decompiler
// Type: PlayMakerGlobals
// Assembly: PlayMaker, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using HutongGames;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;

public class PlayMakerGlobals : ScriptableObject
{
  private static PlayMakerGlobals instance;
  [NonSerialized]
  private bool initialized;
  [SerializeField]
  private FsmVariables variables = new FsmVariables();
  [SerializeField]
  private List<string> events = new List<string>();

  public static bool Initialized => (UnityEngine.Object) PlayMakerGlobals.instance != (UnityEngine.Object) null && PlayMakerGlobals.instance.initialized;

  public static bool IsPlayingInEditor { get; private set; }

  public static bool IsPlaying { get; private set; }

  public static bool IsEditor { get; private set; }

  public static bool IsBuilding { get; set; }

  public static void InitApplicationFlags()
  {
    PlayMakerGlobals.IsPlayingInEditor = Application.isEditor && Application.isPlaying;
    PlayMakerGlobals.IsPlaying = Application.isPlaying || PlayMakerGlobals.IsBuilding;
    PlayMakerGlobals.IsEditor = Application.isEditor;
  }

  public static void Initialize()
  {
    if (PlayMakerGlobals.Initialized)
      return;
    PlayMakerGlobals.InitApplicationFlags();
    UnityEngine.Object @object = Resources.Load(nameof (PlayMakerGlobals), typeof (PlayMakerGlobals));
    if (@object != (UnityEngine.Object) null)
    {
      if (PlayMakerGlobals.IsPlayingInEditor)
      {
        if ((UnityEngine.Object) PlayMakerGlobals.instance != (UnityEngine.Object) null)
          Resources.UnloadAsset((UnityEngine.Object) PlayMakerGlobals.instance);
        PlayMakerGlobals playMakerGlobals = (PlayMakerGlobals) @object;
        PlayMakerGlobals.instance = ScriptableObject.CreateInstance<PlayMakerGlobals>();
        PlayMakerGlobals.instance.Variables = new FsmVariables(playMakerGlobals.variables);
        PlayMakerGlobals.instance.Events = new List<string>((IEnumerable<string>) playMakerGlobals.Events);
      }
      else
        PlayMakerGlobals.instance = @object as PlayMakerGlobals;
    }
    else
      PlayMakerGlobals.instance = ScriptableObject.CreateInstance<PlayMakerGlobals>();
    if ((UnityEngine.Object) PlayMakerGlobals.instance != (UnityEngine.Object) null)
      PlayMakerGlobals.instance.initialized = true;
    else
      UnityEngine.Debug.LogError((object) "Couldn't make PlayMakerGlobals Instance!");
  }

  public static PlayMakerGlobals Instance
  {
    get
    {
      PlayMakerGlobals.Initialize();
      return PlayMakerGlobals.instance;
    }
  }

  public FsmVariables Variables
  {
    get => this.variables;
    set => this.variables = value;
  }

  public List<string> Events
  {
    get => this.events;
    set => this.events = value;
  }

  public FsmEvent AddEvent(string eventName)
  {
    if (!this.events.Contains(eventName))
      this.events.Add(eventName);
    FsmEvent fsmEvent = FsmEvent.FindEvent(eventName) ?? FsmEvent.GetFsmEvent(eventName);
    fsmEvent.IsGlobal = true;
    return fsmEvent;
  }

  public static void AddGlobalEvent(string eventName)
  {
    if (PlayMakerGlobals.Instance.Events.Contains(eventName))
      return;
    PlayMakerGlobals.Instance.Events.Add(eventName);
  }

  public static void RemoveGlobalEvent(string eventName) => PlayMakerGlobals.Instance.events.RemoveAll((Predicate<string>) (m => m == eventName));

  public void OnEnable()
  {
  }

  public void OnDisable()
  {
    if (!((UnityEngine.Object) PlayMakerGlobals.instance == (UnityEngine.Object) this))
      return;
    PlayMakerGlobals.instance = (PlayMakerGlobals) null;
  }

  public void OnDestroy()
  {
    if (!((UnityEngine.Object) PlayMakerGlobals.instance == (UnityEngine.Object) this))
      return;
    PlayMakerGlobals.instance = (PlayMakerGlobals) null;
  }

  [Conditional("DEBUG_LOG")]
  private void DebugLog(object message, LogColor logColor = LogColor.None)
  {
  }

  public static void ResetInstance() => PlayMakerGlobals.instance = (PlayMakerGlobals) null;
}
