// Decompiled with JetBrains decompiler
// Type: PlayMakerGUI
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using UnityEngine;

[ExecuteInEditMode]
[AddComponentMenu("PlayMaker/PlayMakerGUI")]
public class PlayMakerGUI : MonoBehaviour
{
  private static readonly List<PlayMakerFSM> fsmList = new List<PlayMakerFSM>();
  public static Fsm SelectedFSM;
  private static readonly GUIContent labelContent = new GUIContent();
  public bool previewOnGUI = true;
  public bool enableGUILayout;
  public bool drawStateLabels = true;
  public bool enableStateLabelsInBuilds;
  public bool GUITextureStateLabels;
  public bool GUITextStateLabels;
  public bool filterLabelsWithDistance;
  public float maxLabelDistance = 10f;
  public bool controlMouseCursor = true;
  public float labelScale = 1f;
  private static readonly List<PlayMakerFSM> SortedFsmList = new List<PlayMakerFSM>();
  private static GameObject labelGameObject;
  private static float fsmLabelIndex;
  private static PlayMakerGUI instance;
  private static GUISkin guiSkin;
  private static Color guiColor = Color.white;
  private static Color guiBackgroundColor = Color.white;
  private static Color guiContentColor = Color.white;
  private static Matrix4x4 guiMatrix = Matrix4x4.identity;
  private const float MaxLabelWidth = 200f;
  private static GUIStyle stateLabelStyle;
  private static Texture2D stateLabelBackground;
  private float initLabelScale;

  public static bool Exists => (UnityEngine.Object) PlayMakerGUI.instance != (UnityEngine.Object) null;

  public static bool EnableStateLabels
  {
    get
    {
      PlayMakerGUI.InitInstance();
      return Application.isEditor ? (UnityEngine.Object) PlayMakerGUI.instance != (UnityEngine.Object) null && PlayMakerGUI.instance.enabled && PlayMakerGUI.instance.drawStateLabels : (UnityEngine.Object) PlayMakerGUI.instance != (UnityEngine.Object) null && PlayMakerGUI.instance.enabled && PlayMakerGUI.instance.drawStateLabels && PlayMakerGUI.instance.enableStateLabelsInBuilds;
    }
    set
    {
      PlayMakerGUI.InitInstance();
      if (!((UnityEngine.Object) PlayMakerGUI.instance != (UnityEngine.Object) null))
        return;
      PlayMakerGUI.instance.drawStateLabels = value;
    }
  }

  public static bool EnableStateLabelsInBuild
  {
    get
    {
      PlayMakerGUI.InitInstance();
      return (UnityEngine.Object) PlayMakerGUI.instance != (UnityEngine.Object) null && PlayMakerGUI.instance.enabled && PlayMakerGUI.instance.enableStateLabelsInBuilds;
    }
    set
    {
      PlayMakerGUI.InitInstance();
      if (!((UnityEngine.Object) PlayMakerGUI.instance != (UnityEngine.Object) null))
        return;
      PlayMakerGUI.instance.enableStateLabelsInBuilds = value;
    }
  }

  private static void InitInstance()
  {
    if (!((UnityEngine.Object) PlayMakerGUI.instance == (UnityEngine.Object) null))
      return;
    PlayMakerGUI.instance = (PlayMakerGUI) UnityEngine.Object.FindObjectOfType(typeof (PlayMakerGUI));
  }

  public static PlayMakerGUI Instance
  {
    get
    {
      PlayMakerGUI.InitInstance();
      if ((UnityEngine.Object) PlayMakerGUI.instance == (UnityEngine.Object) null)
        PlayMakerGUI.instance = new GameObject(nameof (PlayMakerGUI)).AddComponent<PlayMakerGUI>();
      return PlayMakerGUI.instance;
    }
  }

  public static bool Enabled => (UnityEngine.Object) PlayMakerGUI.instance != (UnityEngine.Object) null && PlayMakerGUI.instance.enabled;

  public static GUISkin GUISkin
  {
    get => PlayMakerGUI.guiSkin;
    set => PlayMakerGUI.guiSkin = value;
  }

  public static Color GUIColor
  {
    get => PlayMakerGUI.guiColor;
    set => PlayMakerGUI.guiColor = value;
  }

  public static Color GUIBackgroundColor
  {
    get => PlayMakerGUI.guiBackgroundColor;
    set => PlayMakerGUI.guiBackgroundColor = value;
  }

  public static Color GUIContentColor
  {
    get => PlayMakerGUI.guiContentColor;
    set => PlayMakerGUI.guiContentColor = value;
  }

  public static Matrix4x4 GUIMatrix
  {
    get => PlayMakerGUI.guiMatrix;
    set => PlayMakerGUI.guiMatrix = value;
  }

  public static Texture MouseCursor { get; set; }

  public static bool LockCursor { get; set; }

  public static bool HideCursor { get; set; }

  private void InitLabelStyle()
  {
    if ((UnityEngine.Object) PlayMakerGUI.stateLabelBackground != (UnityEngine.Object) null)
      UnityEngine.Object.Destroy((UnityEngine.Object) PlayMakerGUI.stateLabelBackground);
    PlayMakerGUI.stateLabelBackground = new Texture2D(1, 1);
    PlayMakerGUI.stateLabelBackground.SetPixel(0, 0, Color.white);
    PlayMakerGUI.stateLabelBackground.Apply();
    PlayMakerGUI.stateLabelStyle = new GUIStyle()
    {
      normal = {
        background = PlayMakerGUI.stateLabelBackground,
        textColor = Color.white
      },
      fontSize = (int) (10.0 * (double) this.labelScale),
      alignment = TextAnchor.MiddleLeft,
      padding = new RectOffset(4, 4, 1, 1)
    };
    this.initLabelScale = this.labelScale;
  }

  private void DrawStateLabels()
  {
    PlayMakerGUI.SortedFsmList.Clear();
    int count1 = PlayMakerFSM.FsmList.Count;
    for (int index = 0; index < count1; ++index)
    {
      PlayMakerFSM fsm = PlayMakerFSM.FsmList[index];
      if (fsm.Active)
        PlayMakerGUI.SortedFsmList.Add(fsm);
    }
    PlayMakerGUI.SortedFsmList.Sort((Comparison<PlayMakerFSM>) ((x, y) => string.CompareOrdinal(x.gameObject.name, y.gameObject.name)));
    PlayMakerGUI.labelGameObject = (GameObject) null;
    int count2 = PlayMakerGUI.SortedFsmList.Count;
    for (int index = 0; index < count2; ++index)
    {
      PlayMakerFSM sortedFsm = PlayMakerGUI.SortedFsmList[index];
      if (sortedFsm.Fsm.ShowStateLabel)
        this.DrawStateLabel(sortedFsm);
    }
  }

  private void DrawStateLabel(PlayMakerFSM fsm)
  {
    if (PlayMakerGUI.stateLabelStyle == null || (double) Math.Abs(this.initLabelScale - this.labelScale) > 0.100000001490116)
      this.InitLabelStyle();
    if ((UnityEngine.Object) Camera.main == (UnityEngine.Object) null || (UnityEngine.Object) fsm.gameObject == (UnityEngine.Object) Camera.main)
      return;
    if ((UnityEngine.Object) fsm.gameObject == (UnityEngine.Object) PlayMakerGUI.labelGameObject)
    {
      ++PlayMakerGUI.fsmLabelIndex;
    }
    else
    {
      PlayMakerGUI.fsmLabelIndex = 0.0f;
      PlayMakerGUI.labelGameObject = fsm.gameObject;
    }
    string stateLabel = PlayMakerGUI.GenerateStateLabel(fsm);
    if (string.IsNullOrEmpty(stateLabel))
      return;
    Vector2 vector2_1 = new Vector2();
    PlayMakerGUI.labelContent.text = stateLabel;
    Vector2 vector2_2 = PlayMakerGUI.stateLabelStyle.CalcSize(PlayMakerGUI.labelContent);
    vector2_2.x = Mathf.Clamp(vector2_2.x, 10f * this.labelScale, 200f * this.labelScale);
    if (this.GUITextureStateLabels)
    {
      vector2_1.x = fsm.gameObject.transform.position.x * (float) Screen.width;
      vector2_1.y = fsm.gameObject.transform.position.y * (float) Screen.height;
    }
    else if (this.GUITextStateLabels)
    {
      vector2_1.x = fsm.gameObject.transform.position.x * (float) Screen.width;
      vector2_1.y = fsm.gameObject.transform.position.y * (float) Screen.height;
    }
    else
    {
      if (this.filterLabelsWithDistance && (double) Vector3.Distance(Camera.main.transform.position, fsm.transform.position) > (double) this.maxLabelDistance || (double) Camera.main.transform.InverseTransformPoint(fsm.transform.position).z <= 0.0)
        return;
      vector2_1 = (Vector2) Camera.main.WorldToScreenPoint(fsm.transform.position);
      vector2_1.x -= vector2_2.x * 0.5f;
    }
    vector2_1.y = (float) ((double) Screen.height - (double) vector2_1.y - (double) PlayMakerGUI.fsmLabelIndex * 15.0 * (double) this.labelScale);
    Color backgroundColor = GUI.backgroundColor;
    Color color1 = GUI.color;
    int index = 0;
    if (fsm.Fsm.ActiveState != null)
      index = fsm.Fsm.ActiveState.ColorIndex;
    Color color2 = PlayMakerPrefs.Colors[index];
    GUI.backgroundColor = new Color(color2.r, color2.g, color2.b, 0.5f);
    GUI.contentColor = Color.white;
    GUI.Label(new Rect(vector2_1.x, vector2_1.y, vector2_2.x, vector2_2.y), stateLabel, PlayMakerGUI.stateLabelStyle);
    GUI.backgroundColor = backgroundColor;
    GUI.color = color1;
  }

  private static string GenerateStateLabel(PlayMakerFSM fsm) => fsm.Fsm.ActiveState == null ? "[DISABLED]" : fsm.Fsm.ActiveState.Name;

  private void Awake()
  {
    if ((UnityEngine.Object) PlayMakerGUI.instance == (UnityEngine.Object) null)
      PlayMakerGUI.instance = this;
    else
      Debug.LogWarning((object) "There should only be one PlayMakerGUI active at a time!");
  }

  private void OnEnable()
  {
  }

  private void OnGUI()
  {
    this.useGUILayout = this.enableGUILayout;
    if ((UnityEngine.Object) PlayMakerGUI.GUISkin != (UnityEngine.Object) null)
      GUI.skin = PlayMakerGUI.GUISkin;
    GUI.color = PlayMakerGUI.GUIColor;
    GUI.backgroundColor = PlayMakerGUI.GUIBackgroundColor;
    GUI.contentColor = PlayMakerGUI.GUIContentColor;
    if (this.previewOnGUI && !Application.isPlaying)
    {
      PlayMakerGUI.DoEditGUI();
    }
    else
    {
      PlayMakerGUI.fsmList.Clear();
      PlayMakerGUI.fsmList.AddRange((IEnumerable<PlayMakerFSM>) PlayMakerFSM.FsmList);
      for (int index1 = 0; index1 < PlayMakerGUI.fsmList.Count; ++index1)
      {
        PlayMakerFSM fsm = PlayMakerGUI.fsmList[index1];
        if (!((UnityEngine.Object) fsm == (UnityEngine.Object) null) && fsm.Active && (fsm.Fsm.ActiveState != null && !fsm.Fsm.HandleOnGUI))
        {
          this.CallOnGUI(fsm.Fsm);
          for (int index2 = 0; index2 < fsm.Fsm.SubFsmList.Count; ++index2)
            this.CallOnGUI(fsm.Fsm.SubFsmList[index2]);
        }
      }
      if (!Application.isPlaying || Event.current.type != EventType.Repaint)
        return;
      Matrix4x4 matrix = GUI.matrix;
      GUI.matrix = Matrix4x4.identity;
      if ((UnityEngine.Object) PlayMakerGUI.MouseCursor != (UnityEngine.Object) null)
        GUI.DrawTexture(new Rect(Input.mousePosition.x - (float) PlayMakerGUI.MouseCursor.width * 0.5f, (float) ((double) Screen.height - (double) Input.mousePosition.y - (double) PlayMakerGUI.MouseCursor.height * 0.5), (float) PlayMakerGUI.MouseCursor.width, (float) PlayMakerGUI.MouseCursor.height), PlayMakerGUI.MouseCursor);
      if (this.drawStateLabels && PlayMakerGUI.EnableStateLabels)
        this.DrawStateLabels();
      GUI.matrix = matrix;
      PlayMakerGUI.GUIMatrix = Matrix4x4.identity;
      if (!this.controlMouseCursor)
        return;
      Cursor.lockState = PlayMakerGUI.LockCursor ? CursorLockMode.Locked : CursorLockMode.None;
      Cursor.visible = !PlayMakerGUI.HideCursor;
    }
  }

  private void CallOnGUI(Fsm fsm)
  {
    if (fsm.ActiveState == null)
      return;
    foreach (FsmStateAction action in fsm.ActiveState.Actions)
    {
      if (action.Active)
        action.OnGUI();
    }
  }

  private void OnDisable()
  {
    if (!((UnityEngine.Object) PlayMakerGUI.instance == (UnityEngine.Object) this))
      return;
    PlayMakerGUI.instance = (PlayMakerGUI) null;
  }

  private static void DoEditGUI()
  {
    if (PlayMakerGUI.SelectedFSM == null || PlayMakerGUI.SelectedFSM.HandleOnGUI)
      return;
    FsmState editState = PlayMakerGUI.SelectedFSM.EditState;
    if (editState == null || !editState.IsInitialized)
      return;
    foreach (FsmStateAction action in editState.Actions)
    {
      if (action.Enabled)
        action.OnGUI();
    }
  }

  public void OnApplicationQuit()
  {
    if (!((UnityEngine.Object) PlayMakerGUI.instance == (UnityEngine.Object) this))
      return;
    PlayMakerGUI.instance = (PlayMakerGUI) null;
  }
}
