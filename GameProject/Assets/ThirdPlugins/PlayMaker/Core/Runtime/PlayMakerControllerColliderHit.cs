// Decompiled with JetBrains decompiler
// Type: PlayMakerControllerColliderHit
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using UnityEngine;

[AddComponentMenu("PlayMaker/Event Handlers/ControllerColliderHit")]
public class PlayMakerControllerColliderHit : PlayMakerProxyBase
{
  public void OnControllerColliderHit(ControllerColliderHit hitCollider)
  {
    for (int index = 0; index < this.TargetFSMs.Count; ++index)
    {
      PlayMakerFSM targetFsM = this.TargetFSMs[index];
      if (!((Object) targetFsM == (Object) null) && targetFsM.Fsm != null && (targetFsM.Active && targetFsM.Fsm.HandleControllerColliderHit))
        targetFsM.Fsm.OnControllerColliderHit(hitCollider);
    }
    this.DoControllerCollisionEventCallback(hitCollider);
  }
}
