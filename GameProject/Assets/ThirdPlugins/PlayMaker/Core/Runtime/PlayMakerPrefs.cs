// Decompiled with JetBrains decompiler
// Type: PlayMakerPrefs
// Assembly: PlayMaker, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System.Collections.Generic;
using UnityEngine;

public class PlayMakerPrefs : ScriptableObject
{
  private static PlayMakerPrefs instance;
  private static readonly Color[] defaultColors = new Color[8]
  {
    Color.grey,
    new Color(0.5450981f, 0.6705883f, 0.9411765f),
    new Color(0.2431373f, 0.7607843f, 0.6901961f),
    new Color(0.4313726f, 0.7607843f, 0.2431373f),
    new Color(1f, 0.8745098f, 0.1882353f),
    new Color(1f, 0.5529412f, 0.1882353f),
    new Color(0.7607843f, 0.2431373f, 0.2509804f),
    new Color(0.5450981f, 0.2431373f, 0.7607843f)
  };
  private static readonly string[] defaultColorNames = new string[8]
  {
    "Default",
    "Blue",
    "Cyan",
    "Green",
    "Yellow",
    "Orange",
    "Red",
    "Purple"
  };
  private static Color[] minimapColors;
  [Tooltip("Output performance warnings to Unity log.\nNote, logging can cause hitches, so you should disabled this in final builds!")]
  [SerializeField]
  private bool logPerformanceWarnings = true;
  [Tooltip("Show Event Handler Components automatically added on GameObjects.\nNormally you want to hide these to keep the Inspector cleaner.")]
  [SerializeField]
  private bool showEventHandlerComponents;
  [Tooltip("How long debug lines are visible for (in seconds).")]
  [SerializeField]
  private float debugLinesDuration = 0.5f;
  [Tooltip("Colors used by States etc.")]
  [SerializeField]
  private Color[] colors = new Color[24]
  {
    Color.grey,
    new Color(0.5450981f, 0.6705883f, 0.9411765f),
    new Color(0.2431373f, 0.7607843f, 0.6901961f),
    new Color(0.4313726f, 0.7607843f, 0.2431373f),
    new Color(1f, 0.8745098f, 0.1882353f),
    new Color(1f, 0.5529412f, 0.1882353f),
    new Color(0.7607843f, 0.2431373f, 0.2509804f),
    new Color(0.5450981f, 0.2431373f, 0.7607843f),
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey,
    Color.grey
  };
  [Tooltip("Descriptive names for each color.")]
  [SerializeField]
  private string[] colorNames = new string[24]
  {
    "Default",
    "Blue",
    "Cyan",
    "Green",
    "Yellow",
    "Orange",
    "Red",
    "Purple",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    ""
  };
  [Tooltip("Color used for Tween From Handles.")]
  [SerializeField]
  private Color tweenFromColor = new Color(0.007843138f, 0.4117647f, 0.9843137f);
  [Tooltip("Color used for Tween To Handles.")]
  [SerializeField]
  private Color tweenToColor = new Color(0.9921569f, 0.5882353f, 0.01568628f);
  [Tooltip("Color used to draw arrows. E.g., velocity, force, direction...")]
  [SerializeField]
  private Color arrowColor = new Color(0.9921569f, 0.5882353f, 0.01568628f);
  [SerializeField]
  private List<string> oldActionNames = new List<string>();
  [SerializeField]
  private List<string> newActionNames = new List<string>();
  [Tooltip("Organize Pools in the hierarchy , and store stacked instances per pool\nNote, this has little impact on performances, but better for organization")]
  [SerializeField]
  private bool organizePoolsInHierarchy = true;
  [Tooltip("If true, will provide a default name to the instances based on their pool index and prefab name\nNote, this has an impact on performance and should be turned off for maximum efficiency")]
  [SerializeField]
  private bool autoNamePoolInstances = true;
  [Tooltip("If true, hides pools organization ( if OrganizePoolsInHierarchy is true) and all pool stacked instances\nNote, this has no impact on performance, but account for a clean hierarchy, you don't need generally to see")]
  [SerializeField]
  private bool hidePoolsInHierarchy = true;

  public static PlayMakerPrefs Instance
  {
    get
    {
      if ((Object) PlayMakerPrefs.instance == (Object) null)
      {
        PlayMakerPrefs.instance = Resources.Load(nameof (PlayMakerPrefs)) as PlayMakerPrefs;
        if ((Object) PlayMakerPrefs.instance == (Object) null)
          PlayMakerPrefs.instance = ScriptableObject.CreateInstance<PlayMakerPrefs>();
      }
      return PlayMakerPrefs.instance;
    }
  }

  public static float DebugLinesDuration
  {
    get => PlayMakerPrefs.Instance.debugLinesDuration;
    set => PlayMakerPrefs.Instance.debugLinesDuration = value;
  }

  public static bool LogPerformanceWarnings
  {
    get => PlayMakerPrefs.Instance.logPerformanceWarnings;
    set => PlayMakerPrefs.Instance.logPerformanceWarnings = value;
  }

  public static bool ShowEventHandlerComponents
  {
    get => PlayMakerPrefs.Instance.showEventHandlerComponents;
    set => PlayMakerPrefs.Instance.showEventHandlerComponents = value;
  }

  public static Color TweenFromColor
  {
    get => PlayMakerPrefs.Instance.tweenFromColor;
    set => PlayMakerPrefs.Instance.tweenFromColor = value;
  }

  public static Color TweenToColor
  {
    get => PlayMakerPrefs.Instance.tweenToColor;
    set => PlayMakerPrefs.Instance.tweenToColor = value;
  }

  public static Color ArrowColor
  {
    get => PlayMakerPrefs.Instance.arrowColor;
    set => PlayMakerPrefs.Instance.arrowColor = value;
  }

  public static Color[] Colors
  {
    get => PlayMakerPrefs.Instance.colors;
    set => PlayMakerPrefs.Instance.colors = value;
  }

  public static string[] ColorNames
  {
    get => PlayMakerPrefs.Instance.colorNames;
    set => PlayMakerPrefs.Instance.colorNames = value;
  }

  public static Color[] MinimapColors
  {
    get
    {
      if (PlayMakerPrefs.minimapColors == null)
        PlayMakerPrefs.UpdateMinimapColors();
      return PlayMakerPrefs.minimapColors;
    }
  }

  public static bool OrganizePoolsInHierarchy
  {
    get => PlayMakerPrefs.Instance.organizePoolsInHierarchy;
    set => PlayMakerPrefs.Instance.organizePoolsInHierarchy = value;
  }

  public static bool AutoNamePoolInstances
  {
    get => PlayMakerPrefs.Instance.autoNamePoolInstances;
    set => PlayMakerPrefs.Instance.autoNamePoolInstances = value;
  }

  public static bool HidePoolsInHierarchy
  {
    get => PlayMakerPrefs.Instance.hidePoolsInHierarchy;
    set => PlayMakerPrefs.Instance.hidePoolsInHierarchy = value;
  }

  public static void SaveChanges() => PlayMakerPrefs.UpdateMinimapColors();

  public void ResetDefaultColors()
  {
    this.tweenFromColor = new Color(0.9372549f, 0.345098f, 0.007843138f);
    this.tweenToColor = new Color(0.9921569f, 0.5882353f, 0.01568628f);
    this.arrowColor = new Color(0.9921569f, 0.5882353f, 0.01568628f);
    for (int index = 0; index < PlayMakerPrefs.defaultColors.Length; ++index)
    {
      this.colors[index] = PlayMakerPrefs.defaultColors[index];
      this.colorNames[index] = PlayMakerPrefs.defaultColorNames[index];
    }
  }

  public void AddActionRenameRule(string oldName, string newName)
  {
    this.oldActionNames.Add(oldName);
    this.newActionNames.Add(newName);
  }

  public void DeleteActionRenameRule(int index)
  {
    this.oldActionNames.RemoveAt(index);
    this.newActionNames.RemoveAt(index);
  }

  public string GetNewActionName(string oldName)
  {
    if (this.oldActionNames.Count == 0)
      return oldName;
    string oldName1 = oldName;
    string str;
    do
    {
      str = oldName1;
      oldName1 = this.TryGetNewActionName(oldName1);
    }
    while (oldName1 != str);
    return oldName1;
  }

  private static void UpdateMinimapColors()
  {
    PlayMakerPrefs.minimapColors = new Color[PlayMakerPrefs.Colors.Length];
    for (int index = 0; index < PlayMakerPrefs.Colors.Length; ++index)
    {
      Color color = PlayMakerPrefs.Colors[index];
      PlayMakerPrefs.minimapColors[index] = new Color(color.r, color.g, color.b, 0.5f);
    }
  }

  private string TryGetNewActionName(string oldName)
  {
    for (int index = 0; index < this.oldActionNames.Count; ++index)
    {
      if (this.oldActionNames[index] == oldName)
        return this.newActionNames[index];
    }
    return oldName;
  }
}
