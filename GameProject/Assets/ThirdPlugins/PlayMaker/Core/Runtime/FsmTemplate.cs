// Decompiled with JetBrains decompiler
// Type: FsmTemplate
// Assembly: PlayMaker, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using HutongGames.PlayMaker;
using System;
using UnityEngine;

[Serializable]
public class FsmTemplate : ScriptableObject
{
  [Delayed]
  [SerializeField]
  private string category;
  public Fsm fsm;

  public string Description => this.fsm == null ? "" : this.fsm.Description;

  public string Category
  {
    get => this.category;
    set => this.category = value;
  }

  public void OnEnable()
  {
    if (this.fsm == null)
      return;
    this.fsm.UsedInTemplate = this;
  }
}
