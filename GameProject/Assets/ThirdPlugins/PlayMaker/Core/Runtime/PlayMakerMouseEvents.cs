// Decompiled with JetBrains decompiler
// Type: PlayMakerMouseEvents
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using HutongGames.PlayMaker;
using UnityEngine;

[AddComponentMenu("PlayMaker/Event Handlers/Mouse Events")]
public class PlayMakerMouseEvents : PlayMakerProxyBase
{
  public void OnMouseEnter()
  {
    for (int index = 0; index < this.TargetFSMs.Count; ++index)
    {
      PlayMakerFSM targetFsM = this.TargetFSMs[index];
      if (!((Object) targetFsM == (Object) null) && targetFsM.Fsm != null && targetFsM.Fsm.MouseEvents)
        targetFsM.Fsm.Event(FsmEvent.MouseEnter);
    }
  }

  public void OnMouseDown()
  {
    for (int index = 0; index < this.TargetFSMs.Count; ++index)
    {
      PlayMakerFSM targetFsM = this.TargetFSMs[index];
      if (!((Object) targetFsM == (Object) null) && targetFsM.Fsm != null && targetFsM.Fsm.MouseEvents)
        targetFsM.Fsm.Event(FsmEvent.MouseDown);
    }
  }

  public void OnMouseUp()
  {
    for (int index = 0; index < this.TargetFSMs.Count; ++index)
    {
      PlayMakerFSM targetFsM = this.TargetFSMs[index];
      if (!((Object) targetFsM == (Object) null) && targetFsM.Fsm != null && targetFsM.Fsm.MouseEvents)
      {
        targetFsM.Fsm.Event(FsmEvent.MouseUp);
        Fsm.LastClickedObject = this.gameObject;
      }
    }
  }

  public void OnMouseUpAsButton()
  {
    for (int index = 0; index < this.TargetFSMs.Count; ++index)
    {
      PlayMakerFSM targetFsM = this.TargetFSMs[index];
      if (!((Object) targetFsM == (Object) null) && targetFsM.Fsm != null && targetFsM.Fsm.MouseEvents)
      {
        targetFsM.Fsm.Event(FsmEvent.MouseUpAsButton);
        Fsm.LastClickedObject = this.gameObject;
      }
    }
  }

  public void OnMouseExit()
  {
    for (int index = 0; index < this.TargetFSMs.Count; ++index)
    {
      PlayMakerFSM targetFsM = this.TargetFSMs[index];
      if (!((Object) targetFsM == (Object) null) && targetFsM.Fsm != null && targetFsM.Fsm.MouseEvents)
        targetFsM.Fsm.Event(FsmEvent.MouseExit);
    }
  }

  public void OnMouseDrag()
  {
    for (int index = 0; index < this.TargetFSMs.Count; ++index)
    {
      PlayMakerFSM targetFsM = this.TargetFSMs[index];
      if (!((Object) targetFsM == (Object) null) && targetFsM.Fsm != null && targetFsM.Fsm.MouseEvents)
        targetFsM.Fsm.Event(FsmEvent.MouseDrag);
    }
  }

  public void OnMouseOver()
  {
    for (int index = 0; index < this.TargetFSMs.Count; ++index)
    {
      PlayMakerFSM targetFsM = this.TargetFSMs[index];
      if (!((Object) targetFsM == (Object) null) && targetFsM.Fsm != null && targetFsM.Fsm.MouseEvents)
        targetFsM.Fsm.Event(FsmEvent.MouseOver);
    }
  }
}
