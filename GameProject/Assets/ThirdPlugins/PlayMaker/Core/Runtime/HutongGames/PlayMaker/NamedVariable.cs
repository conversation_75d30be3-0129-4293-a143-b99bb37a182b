// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.NamedVariable
// Assembly: PlayMaker, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class NamedVariable : INameable, INamedVariable, IComparable
  {
    [SerializeField]
    private bool useVariable;
    [SerializeField]
    private string name;
    [SerializeField]
    [TextArea(0, 10)]
    private string tooltip = "";
    [SerializeField]
    private bool showInInspector;
    [SerializeField]
    private bool networkSync;
    [NonSerialized]
    protected object obj;

    public NamedVariable CastVariable
    {
      get => this.obj as NamedVariable;
      set => this.obj = (object) value;
    }

    public string Name
    {
      get => this.name;
      set => Debug.LogWarning((object) ("Trying to set variable name directly: " + value + "\nNormally you should create a new variable with that name! Otherwise you might overwrite the name of the current variable this points to: " + this.name + "\nIf you definitely mean to rename the variable (e.g., in an editor tool) use SetName instead."));
    }

    public void SetName(string newName) => this.name = newName;

    public virtual VariableType VariableType => throw new Exception("VariableType not implemented: " + this.GetType().FullName);

    public virtual System.Type ObjectType
    {
      get => (System.Type) null;
      set
      {
      }
    }

    public virtual VariableType TypeConstraint => this.VariableType;

    public virtual object RawValue
    {
      set => throw new NotImplementedException();
      get => throw new NotImplementedException();
    }

    public string Tooltip
    {
      get => this.tooltip;
      set => this.tooltip = value;
    }

    public bool UseVariable
    {
      get => this.useVariable;
      set => this.useVariable = value;
    }

    public bool ShowInInspector
    {
      get => this.showInInspector;
      set => this.showInInspector = value;
    }

    public bool NetworkSync
    {
      get => this.networkSync;
      set => this.networkSync = value;
    }

    public static bool IsNullOrNone(NamedVariable variable) => variable == null || variable.IsNone;

    public bool IsNone => this.useVariable && string.IsNullOrEmpty(this.name);

    public bool UsesVariable => this.useVariable && !string.IsNullOrEmpty(this.name);

    public NamedVariable()
    {
      this.name = "";
      this.tooltip = "";
    }

    public NamedVariable(string name)
    {
      this.name = name;
      if (string.IsNullOrEmpty(name))
        return;
      this.useVariable = true;
    }

    public NamedVariable(NamedVariable source)
    {
      if (source == null)
        return;
      this.useVariable = source.useVariable;
      this.name = source.name;
      this.showInInspector = source.showInInspector;
      this.tooltip = source.tooltip;
      this.networkSync = source.networkSync;
    }

    public virtual void Init()
    {
    }

    public virtual bool TestTypeConstraint(VariableType variableType, System.Type objectType = null) => variableType == VariableType.Unknown || this.TypeConstraint == variableType;

    public virtual void SafeAssign(object val) => throw new NotImplementedException();

    public virtual NamedVariable Clone() => throw new NotImplementedException();

    public NamedVariable Copy()
    {
      System.Type type = this.GetType();
      if (type == typeof (FsmMaterial))
        return (NamedVariable) new FsmMaterial((FsmObject) this);
      if (type == typeof (FsmTexture))
        return (NamedVariable) new FsmTexture((FsmObject) this);
      if (type == typeof (FsmFloat))
        return (NamedVariable) new FsmFloat((FsmFloat) this);
      if (type == typeof (FsmInt))
        return (NamedVariable) new FsmInt((FsmInt) this);
      if (type == typeof (FsmBool))
        return (NamedVariable) new FsmBool((FsmBool) this);
      if (type == typeof (FsmString))
        return (NamedVariable) new FsmString((FsmString) this);
      if (type == typeof (FsmGameObject))
        return (NamedVariable) new FsmGameObject((FsmGameObject) this);
      if (type == typeof (FsmVector2))
        return (NamedVariable) new FsmVector2((FsmVector2) this);
      if (type == typeof (FsmVector3))
        return (NamedVariable) new FsmVector3((FsmVector3) this);
      if (type == typeof (FsmRect))
        return (NamedVariable) new FsmRect((FsmRect) this);
      if (type == typeof (FsmQuaternion))
        return (NamedVariable) new FsmQuaternion((FsmQuaternion) this);
      if (type == typeof (FsmColor))
        return (NamedVariable) new FsmColor((FsmColor) this);
      if (type == typeof (FsmArray))
        return (NamedVariable) new FsmArray((FsmArray) this);
      if (type == typeof (FsmEnum))
        return (NamedVariable) new FsmEnum((FsmEnum) this);
      if (type == typeof (FsmObject))
        return (NamedVariable) new FsmObject((FsmObject) this);
      Debug.LogError((object) "Unknown variable type!");
      return (NamedVariable) null;
    }

    public string GetDisplayName() => string.IsNullOrEmpty(this.Name) ? "None" : this.Name;

    public virtual float ToFloat() => 0.0f;

    public virtual int ToInt() => 0;

    public virtual string DebugString() => this.ToString();

    public virtual void Clear() => throw new NotImplementedException();

    public int CompareTo(object target) => !(target is NamedVariable namedVariable) ? 0 : string.CompareOrdinal(this.name, namedVariable.name);
  }
}
