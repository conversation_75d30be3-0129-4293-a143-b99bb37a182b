// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.ActionReport
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using System.Collections.Generic;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  public class ActionReport
  {
    public static readonly List<ActionReport> ActionReportList = new List<ActionReport>();
    public static int InfoCount;
    public static int ErrorCount;
    public Fsm fsm;
    public FsmState state;
    public FsmStateAction action;
    public int actionIndex;
    public string logText;
    public bool isError;
    public string parameter;

    public static void Start()
    {
      ActionReport.ActionReportList.Clear();
      ActionReport.InfoCount = 0;
      ActionReport.ErrorCount = 0;
    }

    public static ActionReport Log(
      Fsm fsm,
      FsmState state,
      FsmStateAction action,
      int actionIndex,
      string parameter,
      string logLine,
      bool isError = false)
    {
      if (!PlayMakerGlobals.IsEditor)
        return (ActionReport) null;
      if (fsm == null)
      {
        Debug.LogWarning((object) "Cannot log report: Fsm == null!");
        return (ActionReport) null;
      }
      ActionReport report = new ActionReport()
      {
        fsm = fsm,
        state = state,
        action = action,
        actionIndex = actionIndex,
        parameter = parameter,
        logText = logLine,
        isError = isError
      };
      if (ActionReport.ActionReportContains(report))
        return (ActionReport) null;
      ActionReport.ActionReportList.Add(report);
      ++ActionReport.InfoCount;
      return report;
    }

    private static bool ActionReportContains(ActionReport report)
    {
      foreach (ActionReport actionReport in ActionReport.ActionReportList)
      {
        if (actionReport.SameAs(report))
          return true;
      }
      return false;
    }

    private bool SameAs(ActionReport actionReport) => actionReport.fsm == this.fsm && actionReport.state == this.state && (actionReport.actionIndex == this.actionIndex && actionReport.logText == this.logText) && actionReport.isError == this.isError && actionReport.parameter == this.parameter;

    public static void LogWarning(
      Fsm fsm,
      FsmState state,
      FsmStateAction action,
      int actionIndex,
      string parameter,
      string logLine)
    {
      ActionReport.Log(fsm, state, action, actionIndex, parameter, logLine, true);
      Debug.LogWarning((object) (FsmUtility.GetPath(state, action) + logLine), fsm.OwnerObject);
      ++ActionReport.ErrorCount;
    }

    public static void LogError(
      Fsm fsm,
      FsmState state,
      FsmStateAction action,
      int actionIndex,
      string parameter,
      string logLine)
    {
      ActionReport.Log(fsm, state, action, actionIndex, parameter, logLine, true);
      Debug.LogError((object) (FsmUtility.GetPath(state, action) + logLine), fsm.OwnerObject);
      ++ActionReport.ErrorCount;
    }

    public static void LogError(
      Fsm fsm,
      FsmState state,
      FsmStateAction action,
      int actionIndex,
      string logLine)
    {
      ActionReport.Log(fsm, state, action, actionIndex, logLine, "", true);
      Debug.LogError((object) (FsmUtility.GetPath(state, action) + logLine), fsm.OwnerObject);
      ++ActionReport.ErrorCount;
    }

    public static void Clear() => ActionReport.ActionReportList.Clear();

    public static void Remove(Fsm fsm) => ActionReport.ActionReportList.RemoveAll((Predicate<ActionReport>) (x => x.fsm == fsm));

    public static int GetCount() => ActionReport.ActionReportList.Count;
  }
}
