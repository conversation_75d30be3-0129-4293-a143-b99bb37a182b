// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.ObjectTypeAttribute
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;

namespace HutongGames.PlayMaker
{
  [AttributeUsage(AttributeTargets.Field)]
  public sealed class ObjectTypeAttribute : Attribute
  {
    private readonly Type objectType;

    public Type ObjectType => this.objectType;

    public ObjectTypeAttribute(Type objectType) => this.objectType = objectType;
  }
}
