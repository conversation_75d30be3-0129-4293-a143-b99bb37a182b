// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmInt
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmInt : NamedVariable
  {
    [SerializeField]
    private int value;

    public int Value
    {
      get => this.CastVariable == null ? this.value : this.CastVariable.ToInt();
      set => this.value = value;
    }

    public override object RawValue
    {
      get => (object) this.value;
      set => this.value = (int) value;
    }

    public override void SafeAssign(object val)
    {
      if (val is int num)
        this.value = num;
      if (!(val is float f))
        return;
      this.value = Mathf.FloorToInt(f);
    }

    public FsmInt()
    {
    }

    public FsmInt(string name)
      : base(name)
    {
    }

    public FsmInt(FsmInt source)
      : base((NamedVariable) source)
    {
      if (source == null)
        return;
      this.value = source.value;
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmInt(this);

    public override VariableType VariableType => VariableType.Int;

    public override string ToString() => this.Value.ToString();

    public override float ToFloat() => (float) this.value;

    public override int ToInt() => this.value;

    public override void Clear() => this.value = 0;

    public static implicit operator FsmInt(int value) => new FsmInt(string.Empty)
    {
      value = value
    };
  }
}
