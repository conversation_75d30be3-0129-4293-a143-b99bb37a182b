// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.ActionCategoryAttribute
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;

namespace HutongGames.PlayMaker
{
  [AttributeUsage(AttributeTargets.Class)]
  public sealed class ActionCategoryAttribute : Attribute
  {
    private readonly string category;

    public string Category => this.category;

    public ActionCategoryAttribute(string category) => this.category = category;

    public ActionCategoryAttribute(ActionCategory category) => this.category = category.ToString();
  }
}
