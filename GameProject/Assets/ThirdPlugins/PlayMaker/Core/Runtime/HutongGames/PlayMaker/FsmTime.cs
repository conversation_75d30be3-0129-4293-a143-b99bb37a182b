// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmTime
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  public static class FsmTime
  {
    private static float totalEditorPlayerPausedTime;
    private static float realtimeLastUpdate;
    private static int frameCountLastUpdate;

    public static float RealtimeSinceStartup => Time.realtimeSinceStartup - FsmTime.totalEditorPlayerPausedTime;

    public static void Update()
    {
      float realtimeSinceStartup = Time.realtimeSinceStartup;
      if (Time.frameCount == FsmTime.frameCountLastUpdate)
        FsmTime.totalEditorPlayerPausedTime += realtimeSinceStartup - FsmTime.realtimeLastUpdate;
      FsmTime.frameCountLastUpdate = Time.frameCount;
      FsmTime.realtimeLastUpdate = Time.realtimeSinceStartup;
    }

    public static string FormatTime(float time) => new DateTime((long) ((double) time * 10000000.0)).ToString("mm:ss:ff");

    public static void DebugLog()
    {
      Debug.Log((object) ("LastFrameCount: " + (object) FsmTime.frameCountLastUpdate));
      Debug.Log((object) ("PausedTime: " + (object) FsmTime.totalEditorPlayerPausedTime));
      Debug.Log((object) ("Realtime: " + (object) FsmTime.RealtimeSinceStartup));
    }

    [Obsolete("Workaround for old bug")]
    public static void RealtimeBugFix()
    {
    }
  }
}
