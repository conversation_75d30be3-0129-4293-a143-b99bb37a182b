// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.CompoundArrayAttribute
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;

namespace HutongGames.PlayMaker
{
  [AttributeUsage(AttributeTargets.Field)]
  public sealed class CompoundArrayAttribute : Attribute
  {
    private readonly string name;
    private readonly string firstArrayName;
    private readonly string secondArrayName;

    public string Name => this.name;

    public string FirstArrayName => this.firstArrayName;

    public string SecondArrayName => this.secondArrayName;

    public CompoundArrayAttribute(string name, string firstArrayName, string secondArrayName)
    {
      this.name = name;
      this.firstArrayName = firstArrayName;
      this.secondArrayName = secondArrayName;
    }
  }
}
