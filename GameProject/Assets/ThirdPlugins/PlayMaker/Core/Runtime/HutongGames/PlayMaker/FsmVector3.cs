// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmVector3
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmVector3 : NamedVariable
  {
    [SerializeField]
    private Vector3 value;

    public Vector3 Value
    {
      get => this.CastVariable == null ? this.value : (Vector3) (Vector2) this.CastVariable.RawValue;
      set => this.value = value;
    }

    public override object RawValue
    {
      get => (object) this.value;
      set => this.value = (Vector3) value;
    }

    public FsmVector3()
    {
    }

    public FsmVector3(string name)
      : base(name)
    {
    }

    public FsmVector3(FsmVector3 source)
      : base((NamedVariable) source)
    {
      if (source == null)
        return;
      this.value = source.value;
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmVector3(this);

    public override void Clear() => this.value = new Vector3();

    public override VariableType VariableType => VariableType.Vector3;

    public override string ToString() => this.Value.ToString();

    public static implicit operator FsmVector3(Vector3 value) => new FsmVector3(string.Empty)
    {
      value = value
    };
  }
}
