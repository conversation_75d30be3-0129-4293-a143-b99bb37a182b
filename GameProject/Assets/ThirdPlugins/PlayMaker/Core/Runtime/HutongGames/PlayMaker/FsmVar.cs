// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmVar
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmVar
  {
    public string variableName;
    public string objectType;
    public bool useVariable;
    [NonSerialized]
    private NamedVariable namedVar;
    [NonSerialized]
    private System.Type namedVarType;
    private System.Type enumType;
    private Enum enumValue;
    [NonSerialized]
    private System.Type _objectType;
    [SerializeField]
    private VariableType type = VariableType.Unknown;
    public float floatValue;
    public int intValue;
    public bool boolValue;
    public string stringValue;
    public Vector4 vector4Value;
    public UnityEngine.Object objectReference;
    public FsmArray arrayValue;
    private Vector2 vector2;
    private Vector3 vector3;
    private Rect rect;

    public NamedVariable NamedVar
    {
      get
      {
        if (this.namedVar == null)
          this.InitNamedVar();
        return this.namedVar;
      }
      set
      {
        if (value != null)
        {
          this.UpdateType((INamedVariable) value);
          this.namedVar = value;
          this.namedVarType = value.GetType();
          this.variableName = this.namedVar.Name;
          this.useVariable = value.UseVariable;
        }
        else
        {
          this.namedVar = (NamedVariable) null;
          this.namedVarType = (System.Type) null;
          this.variableName = (string) null;
        }
        this.UpdateValue();
      }
    }

    public System.Type NamedVarType
    {
      get
      {
        if (this.namedVarType == null && this.NamedVar != null)
          this.namedVarType = this.NamedVar.GetType();
        return this.namedVarType;
      }
    }

    public System.Type EnumType
    {
      get
      {
        if (this.enumType == null)
          this.InitEnumType();
        return this.enumType;
      }
      set
      {
        if (this.enumType != value)
        {
          this.enumType = value ?? typeof (None);
          this.ObjectType = this.enumType;
          this.intValue = 0;
          this.enumValue = (Enum) null;
        }
        if (this.NamedVar is FsmEnum)
          ((FsmEnum)namedVar).EnumType = this.enumType;
        if (!(this.NamedVar is FsmArray))
          return;
        namedVar.ObjectType = this.enumType;
      }
    }

    public Enum EnumValue
    {
      get
      {
        if (this.enumValue == null)
          this.enumValue = (Enum) Enum.ToObject(this.EnumType, (object) this.intValue);
        return this.enumValue;
      }
      set
      {
        if (value != null)
        {
          this.EnumType = value.GetType();
          this.enumValue = value;
          this.intValue = Convert.ToInt32((object) value);
        }
        else
          this.enumValue = (Enum) Activator.CreateInstance(this.EnumType);
      }
    }

    public System.Type ObjectType
    {
      get
      {
        if (this._objectType == null)
          this._objectType = ReflectionUtils.GetGlobalType(this.objectType);
        if (this._objectType == null)
        {
          this._objectType = typeof (UnityEngine.Object);
          this.objectType = this._objectType.FullName;
        }
        return this._objectType;
      }
      set
      {
        this._objectType = value;
        if (this._objectType != null)
        {
          this.objectType = this._objectType.FullName;
        }
        else
        {
          this._objectType = typeof (UnityEngine.Object);
          this.objectType = this._objectType.FullName;
        }
        if (this.namedVar == null)
          return;
        this.NamedVar.ObjectType = this._objectType;
      }
    }

    public VariableType Type
    {
      get => this.type;
      set
      {
        if (value == this.type)
          return;
        this.type = value;
        this.InitNamedVar();
      }
    }

    public System.Type RealType
    {
      get
      {
        switch (this.type)
        {
          case VariableType.Unknown:
            return (System.Type) null;
          case VariableType.Float:
            return typeof (float);
          case VariableType.Int:
            return typeof (int);
          case VariableType.Bool:
            return typeof (bool);
          case VariableType.GameObject:
            return typeof (GameObject);
          case VariableType.String:
            return typeof (string);
          case VariableType.Vector2:
            return typeof (Vector2);
          case VariableType.Vector3:
            return typeof (Vector3);
          case VariableType.Color:
            return typeof (Color);
          case VariableType.Rect:
            return typeof (Rect);
          case VariableType.Material:
            return typeof (Material);
          case VariableType.Texture:
            return typeof (Texture);
          case VariableType.Quaternion:
            return typeof (Quaternion);
          case VariableType.Object:
            return this.ObjectType;
          case VariableType.Array:
            return this.arrayValue.RealType();
          case VariableType.Enum:
            return this.EnumType;
          default:
            throw new ArgumentOutOfRangeException();
        }
      }
    }

    public bool IsNone => this.useVariable && string.IsNullOrEmpty(this.variableName);

    public Vector2 vector2Value
    {
      get
      {
        this.vector2.Set(this.vector4Value.x, this.vector4Value.y);
        return this.vector2;
      }
      set => this.vector4Value.Set(value.x, value.y, 0.0f, 0.0f);
    }

    public Vector3 vector3Value
    {
      get
      {
        this.vector3.Set(this.vector4Value.x, this.vector4Value.y, this.vector4Value.z);
        return this.vector3;
      }
      set => this.vector4Value.Set(value.x, value.y, value.z, 0.0f);
    }

    public Color colorValue
    {
      get => new Color(this.vector4Value.x, this.vector4Value.y, this.vector4Value.z, this.vector4Value.w);
      set => this.vector4Value.Set(value.r, value.g, value.b, value.a);
    }

    public Rect rectValue
    {
      get
      {
        this.rect.Set(this.vector4Value.x, this.vector4Value.y, this.vector4Value.z, this.vector4Value.w);
        return this.rect;
      }
      set => this.vector4Value.Set(value.x, value.y, value.width, value.height);
    }

    public Quaternion quaternionValue
    {
      get => new Quaternion(this.vector4Value.x, this.vector4Value.y, this.vector4Value.z, this.vector4Value.w);
      set => this.vector4Value.Set(value.x, value.y, value.z, value.w);
    }

    public GameObject gameObjectValue
    {
      get => this.objectReference as GameObject;
      set => this.objectReference = (UnityEngine.Object) value;
    }

    public Material materialValue
    {
      get => this.objectReference as Material;
      set => this.objectReference = (UnityEngine.Object) value;
    }

    public Texture textureValue
    {
      get => this.objectReference as Texture;
      set => this.objectReference = (UnityEngine.Object) value;
    }

    public FsmVar()
    {
    }

    public FsmVar(System.Type type)
    {
      this.type = FsmVar.GetVariableType(type);
      if (type.IsEnum)
        this.EnumType = type;
      else if (type.IsArray)
      {
        System.Type elementType = type.GetElementType();
        this.arrayValue = new FsmArray()
        {
          ElementType = FsmVar.GetVariableType(elementType)
        };
        if (!elementType.IsEnum && !typeof (UnityEngine.Object).IsAssignableFrom(elementType))
          return;
        this.arrayValue.ObjectType = elementType;
      }
      else
      {
        if (!type.IsSubclassOf(typeof (UnityEngine.Object)))
          return;
        this.ObjectType = type;
      }
    }

    public FsmVar(FsmVar source)
    {
      this.variableName = source.variableName;
      this.useVariable = source.useVariable;
      this.type = source.type;
      this.GetValueFrom((INamedVariable) source.NamedVar);
    }

    public FsmVar(INamedVariable variable)
    {
      this.type = variable.VariableType;
      this.ObjectType = variable.ObjectType;
      this.variableName = variable.Name;
      this.GetValueFrom(variable);
    }

    public void Init(NamedVariable variable)
    {
      if (variable != null)
      {
        this.type = variable.VariableType;
        this.variableName = variable.Name;
      }
      else
        this.variableName = "";
      this.NamedVar = variable;
    }

    private void UpdateType(INamedVariable sourceVar)
    {
      if (sourceVar == null)
      {
        this.Type = VariableType.Unknown;
      }
      else
      {
        this.Type = sourceVar.VariableType;
        this.ObjectType = sourceVar.ObjectType;
      }
    }

    private void InitNamedVar()
    {
      switch (this.type)
      {
        case VariableType.Unknown:
          this.namedVar = (NamedVariable) null;
          this.namedVarType = (System.Type) null;
          return;
        case VariableType.Float:
          this.namedVar = (NamedVariable) new FsmFloat(this.variableName)
          {
            Value = this.floatValue
          };
          break;
        case VariableType.Int:
          this.namedVar = (NamedVariable) new FsmInt(this.variableName)
          {
            Value = this.intValue
          };
          break;
        case VariableType.Bool:
          this.namedVar = (NamedVariable) new FsmBool(this.variableName)
          {
            Value = this.boolValue
          };
          break;
        case VariableType.GameObject:
          this.namedVar = (NamedVariable) new FsmGameObject(this.variableName)
          {
            Value = this.gameObjectValue
          };
          break;
        case VariableType.String:
          this.namedVar = (NamedVariable) new FsmString(this.variableName)
          {
            Value = this.stringValue
          };
          break;
        case VariableType.Vector2:
          this.namedVar = (NamedVariable) new FsmVector2(this.variableName)
          {
            Value = this.vector2Value
          };
          break;
        case VariableType.Vector3:
          this.namedVar = (NamedVariable) new FsmVector3(this.variableName)
          {
            Value = this.vector3Value
          };
          break;
        case VariableType.Color:
          this.namedVar = (NamedVariable) new FsmColor(this.variableName)
          {
            Value = this.colorValue
          };
          break;
        case VariableType.Rect:
          this.namedVar = (NamedVariable) new FsmRect(this.variableName)
          {
            Value = this.rectValue
          };
          break;
        case VariableType.Material:
          this.namedVar = (NamedVariable) new FsmMaterial(this.variableName)
          {
            Value = this.materialValue
          };
          break;
        case VariableType.Texture:
          this.namedVar = (NamedVariable) new FsmTexture(this.variableName)
          {
            Value = this.textureValue
          };
          break;
        case VariableType.Quaternion:
          this.namedVar = (NamedVariable) new FsmQuaternion(this.variableName)
          {
            Value = this.quaternionValue
          };
          break;
        case VariableType.Object:
          FsmObject fsmObject = new FsmObject(this.variableName);
          fsmObject.ObjectType = this.ObjectType;
          fsmObject.Value = this.objectReference;
          this.namedVar = (NamedVariable) fsmObject;
          break;
        case VariableType.Array:
          FsmArray fsmArray1 = new FsmArray(this.variableName);
          fsmArray1.ElementType = this.arrayValue.ElementType;
          fsmArray1.ObjectType = this.arrayValue.ObjectType;
          FsmArray fsmArray2 = fsmArray1;
          fsmArray2.CopyValues(this.arrayValue);
          fsmArray2.SaveChanges();
          this.namedVar = (NamedVariable) fsmArray2;
          break;
        case VariableType.Enum:
          this.namedVar = (NamedVariable) new FsmEnum(this.variableName)
          {
            EnumType = this.EnumType,
            Value = this.EnumValue
          };
          break;
        default:
          throw new ArgumentOutOfRangeException("Type");
      }
      if (this.namedVar == null)
        return;
      this.namedVarType = this.namedVar.GetType();
      this.namedVar.UseVariable = this.useVariable;
    }

    private void InitEnumType()
    {
      this.enumType = ReflectionUtils.GetGlobalType(this.objectType);
      if (this.enumType != null && !this.enumType.IsAbstract && this.enumType.IsEnum)
        return;
      this.enumType = typeof (None);
      this.objectType = this.enumType.FullName;
    }

    public object GetValue()
    {
      if (this.namedVar == null)
        this.InitNamedVar();
      switch (this.type)
      {
        case VariableType.Unknown:
          return (object) null;
        case VariableType.Float:
          return (object) this.floatValue;
        case VariableType.Int:
          return (object) this.intValue;
        case VariableType.Bool:
          return (object) this.boolValue;
        case VariableType.GameObject:
          return (object) this.gameObjectValue;
        case VariableType.String:
          return (object) this.stringValue;
        case VariableType.Vector2:
          return (object) this.vector2Value;
        case VariableType.Vector3:
          return (object) this.vector3Value;
        case VariableType.Color:
          return (object) this.colorValue;
        case VariableType.Rect:
          return (object) this.rectValue;
        case VariableType.Material:
          return (object) this.materialValue;
        case VariableType.Texture:
          return (object) this.textureValue;
        case VariableType.Quaternion:
          return (object) this.quaternionValue;
        case VariableType.Object:
          return (object) this.objectReference;
        case VariableType.Array:
          return (object) this.arrayValue.Values;
        case VariableType.Enum:
          return (object) this.enumValue;
        default:
          throw new ArgumentOutOfRangeException();
      }
    }

    public void GetValueFrom(INamedVariable variable)
    {
      if (variable == null)
        return;
      switch (this.type)
      {
        case VariableType.Unknown:
          break;
        case VariableType.Float:
          this.floatValue = variable.VariableType == VariableType.Float ? ((FsmFloat) variable).Value : ((NamedVariable) variable).ToFloat();
          break;
        case VariableType.Int:
          this.intValue = variable.VariableType == VariableType.Int ? ((FsmInt) variable).Value : ((NamedVariable) variable).ToInt();
          break;
        case VariableType.Bool:
          this.boolValue = variable.VariableType == VariableType.Bool ? ((FsmBool) variable).Value : ((NamedVariable) variable).ToInt() > 0;
          break;
        case VariableType.GameObject:
          this.objectReference = variable.VariableType == VariableType.GameObject ? (UnityEngine.Object) ((FsmGameObject) variable).Value : (UnityEngine.Object) (variable.RawValue as GameObject);
          break;
        case VariableType.String:
          this.stringValue = variable.VariableType == VariableType.String ? ((FsmString) variable).Value : ((NamedVariable) variable).ToString();
          break;
        case VariableType.Vector2:
          this.vector2Value = ((FsmVector2) variable).Value;
          break;
        case VariableType.Vector3:
          this.vector3Value = variable.VariableType == VariableType.Vector3 ? ((FsmVector3) variable).Value : (Vector3) (Vector2) variable.RawValue;
          break;
        case VariableType.Color:
          this.colorValue = ((FsmColor) variable).Value;
          break;
        case VariableType.Rect:
          this.rectValue = ((FsmRect) variable).Value;
          break;
        case VariableType.Material:
          this.objectReference = (UnityEngine.Object) ((FsmMaterial) variable).Value;
          break;
        case VariableType.Texture:
          this.objectReference = (UnityEngine.Object) ((FsmTexture) variable).Value;
          break;
        case VariableType.Quaternion:
          this.quaternionValue = ((FsmQuaternion) variable).Value;
          break;
        case VariableType.Object:
          this.objectReference = ((FsmObject) variable).Value;
          break;
        case VariableType.Array:
          this.arrayValue = new FsmArray((FsmArray) variable);
          break;
        case VariableType.Enum:
          this.EnumValue = ((FsmEnum) variable).Value;
          break;
        default:
          throw new ArgumentOutOfRangeException();
      }
    }

    public void UpdateValue() => this.GetValueFrom((INamedVariable) this.NamedVar);

    public void ApplyValueTo(INamedVariable targetVariable)
    {
      if (targetVariable == null)
        return;
      switch (targetVariable.VariableType)
      {
        case VariableType.Unknown:
          break;
        case VariableType.Float:
          ((FsmFloat) targetVariable).Value = this.floatValue;
          break;
        case VariableType.Int:
          ((FsmInt) targetVariable).Value = this.intValue;
          break;
        case VariableType.Bool:
          ((FsmBool) targetVariable).Value = this.boolValue;
          break;
        case VariableType.GameObject:
          ((FsmGameObject) targetVariable).Value = this.objectReference as GameObject;
          break;
        case VariableType.String:
          ((FsmString) targetVariable).Value = this.stringValue;
          break;
        case VariableType.Vector2:
          ((FsmVector2) targetVariable).Value = this.vector2Value;
          break;
        case VariableType.Vector3:
          ((FsmVector3) targetVariable).Value = this.vector3Value;
          break;
        case VariableType.Color:
          ((FsmColor) targetVariable).Value = this.colorValue;
          break;
        case VariableType.Rect:
          ((FsmRect) targetVariable).Value = this.rectValue;
          break;
        case VariableType.Material:
          ((FsmMaterial) targetVariable).Value = this.objectReference as Material;
          break;
        case VariableType.Texture:
          ((FsmTexture) targetVariable).Value = this.objectReference as Texture;
          break;
        case VariableType.Quaternion:
          ((FsmQuaternion) targetVariable).Value = this.quaternionValue;
          break;
        case VariableType.Object:
          ((FsmObject) targetVariable).Value = this.objectReference;
          break;
        case VariableType.Array:
          ((FsmArray) targetVariable).CopyValues(this.arrayValue);
          break;
        case VariableType.Enum:
          ((FsmEnum) targetVariable).Value = this.EnumValue;
          break;
        default:
          throw new ArgumentOutOfRangeException();
      }
    }

    public string DebugString() => string.IsNullOrEmpty(this.variableName) ? "None" : this.variableName + ": " + (object) this.NamedVar;

    public override string ToString() => this.NamedVar != null ? this.NamedVar.ToString() : "None";

    public void SetValue(object value)
    {
      switch (this.type)
      {
        case VariableType.Unknown:
          Debug.LogError((object) ("Unsupported type: " + (value != null ? value.GetType().ToString() : "null")));
          break;
        case VariableType.Float:
          this.floatValue = value != null ? (float) value : 0.0f;
          break;
        case VariableType.Int:
          this.intValue = value != null ? (int) value : 0;
          break;
        case VariableType.Bool:
          this.boolValue = value != null && (bool) value;
          break;
        case VariableType.GameObject:
          this.gameObjectValue = value as GameObject;
          break;
        case VariableType.String:
          this.stringValue = value as string;
          break;
        case VariableType.Vector2:
          this.vector2Value = value != null ? (Vector2) value : Vector2.zero;
          break;
        case VariableType.Vector3:
          this.vector3Value = value != null ? (Vector3) value : Vector3.zero;
          break;
        case VariableType.Color:
          this.colorValue = value != null ? (Color) value : Color.white;
          break;
        case VariableType.Rect:
          this.rectValue = value != null ? (Rect) value : new Rect();
          break;
        case VariableType.Material:
          this.materialValue = value as Material;
          break;
        case VariableType.Texture:
          this.textureValue = value as Texture;
          break;
        case VariableType.Quaternion:
          this.quaternionValue = value != null ? (Quaternion) value : Quaternion.identity;
          break;
        case VariableType.Object:
          this.objectReference = value as UnityEngine.Object;
          break;
        case VariableType.Array:
          if (value is Array array)
          {
            object[] objArray = new object[array.Length];
            for (int index = 0; index < array.Length; ++index)
              objArray[index] = array.GetValue(index);
            this.arrayValue.Values = objArray;
            this.arrayValue.SaveChanges();
            break;
          }
          break;
        case VariableType.Enum:
          this.EnumValue = value as Enum;
          break;
        default:
          throw new ArgumentOutOfRangeException();
      }
      this.ApplyValueTo((INamedVariable) this.namedVar);
    }

    private void DebugLog()
    {
      Debug.Log((object) ("Type: " + (object) this.type));
      Debug.Log((object) ("UseVariable: " + this.useVariable.ToString()));
    }

    public static VariableType GetVariableType(System.Type type)
    {
      if (type == typeof (Material))
        return VariableType.Material;
      if (type == typeof (Texture))
        return VariableType.Texture;
      if (type == typeof (float))
        return VariableType.Float;
      if (type == typeof (int))
        return VariableType.Int;
      if (type == typeof (bool))
        return VariableType.Bool;
      if (type == typeof (string))
        return VariableType.String;
      if (type == typeof (GameObject))
        return VariableType.GameObject;
      if (type == typeof (Vector2))
        return VariableType.Vector2;
      if (type == typeof (Vector3))
        return VariableType.Vector3;
      if (type == typeof (Rect))
        return VariableType.Rect;
      if (type == typeof (Quaternion))
        return VariableType.Quaternion;
      if (type == typeof (Color))
        return VariableType.Color;
      if (typeof (UnityEngine.Object).IsAssignableFrom(type))
        return VariableType.Object;
      if (type.IsEnum)
        return VariableType.Enum;
      return type.IsArray ? VariableType.Array : VariableType.Unknown;
    }
  }
}
