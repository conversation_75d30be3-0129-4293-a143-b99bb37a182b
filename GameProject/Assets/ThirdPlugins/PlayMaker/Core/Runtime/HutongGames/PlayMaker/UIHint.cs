// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.UIHint
// Assembly: PlayMaker, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

namespace HutongGames.PlayMaker
{
  public enum UIHint
  {
    None,
    TextArea,
    Behaviour,
    Script,
    Method,
    Coroutine,
    Animation,
    Tag,
    Layer,
    Description,
    Variable,
    ScriptComponent,
    Comment,
    NamedColor,
    NamedTexture,
    FsmName,
    FsmEvent,
    FsmFloat,
    FsmInt,
    FsmBool,
    FsmString,
    FsmVector3,
    FsmGameObject,
    FsmColor,
    FsmRect,
    FsmMaterial,
    FsmTexture,
    FsmQuaternion,
    FsmObject,
    FsmVector2,
    FsmEnum,
    FsmArray,
    Animator<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    AnimatorTrigger,
    Sorting<PERSON>ayer,
    TagMenu,
    LayerMask,
  }
}
