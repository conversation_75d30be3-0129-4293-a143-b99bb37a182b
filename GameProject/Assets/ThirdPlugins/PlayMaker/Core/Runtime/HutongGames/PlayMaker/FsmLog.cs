// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmLog
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System.Collections.Generic;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  public class FsmLog
  {
    public static int MaxSize = 10000;
    private static readonly List<FsmLog> Logs = new List<FsmLog>();
    private static readonly FsmLogEntry[] logEntryPool;
    private static int nextLogEntryPoolIndex;
    private List<FsmLogEntry> entries = new List<FsmLogEntry>();

    static FsmLog()
    {
      FsmLog.LoggingEnabled = !Application.isEditor;
      FsmLog.logEntryPool = new FsmLogEntry[FsmLog.MaxSize];
      for (int index = 0; index < FsmLog.logEntryPool.Length; ++index)
        FsmLog.logEntryPool[index] = new FsmLogEntry();
    }

    public static bool LoggingEnabled { get; set; }

    public static bool MirrorDebugLog { get; set; }

    public static bool EnableDebugFlow { get; set; }

    public Fsm Fsm { get; private set; }

    public List<FsmLogEntry> Entries => this.entries;

    private FsmLog(Fsm fsm) => this.Fsm = fsm;

    public static FsmLog GetLog(Fsm fsm)
    {
      if (fsm == null)
        return (FsmLog) null;
      foreach (FsmLog log in FsmLog.Logs)
      {
        if (log.Fsm == fsm)
          return log;
      }
      FsmLog fsmLog = new FsmLog(fsm);
      FsmLog.Logs.Add(fsmLog);
      return fsmLog;
    }

    public static void ClearLogs()
    {
      foreach (FsmLog log in FsmLog.Logs)
        log.Clear();
    }

    private void AddEntry(FsmLogEntry entry, bool sendToUnityLog = false)
    {
      if (this.entries == null)
        return;
      entry.Log = this;
      entry.Time = FsmTime.RealtimeSinceStartup;
      entry.FrameCount = Time.frameCount;
      FsmEvent fsmEvent = entry.Event;
      if (fsmEvent != null)
      {
        if (fsmEvent.IsCollisionEvent)
        {
          entry.GameObject = entry.Fsm.CollisionGO;
          entry.GameObjectName = entry.Fsm.CollisionName;
        }
        else if (fsmEvent.IsTriggerEvent)
        {
          entry.GameObject = entry.Fsm.TriggerGO;
          entry.GameObjectName = entry.Fsm.TriggerName;
        }
        else if (fsmEvent.IsCollision2DEvent)
        {
          entry.GameObject = entry.Fsm.Collision2dGO;
          entry.GameObjectName = entry.Fsm.Collision2dName;
        }
        else if (fsmEvent.IsTrigger2DEvent)
        {
          entry.GameObject = entry.Fsm.Trigger2dGO;
          entry.GameObjectName = entry.Fsm.Trigger2dName;
        }
      }
      this.entries.Add(entry);
      switch (entry.LogType)
      {
        case FsmLogType.Warning:
          Debug.LogWarning((object) this.FormatUnityLogString(entry.Text), entry.Fsm.OwnerObject);
          break;
        case FsmLogType.Error:
          Debug.LogError((object) this.FormatUnityLogString(entry.Text), entry.Fsm.OwnerObject);
          break;
        default:
          if (!(FsmLog.MirrorDebugLog | sendToUnityLog) || entry.LogType == FsmLogType.Transition)
            break;
          Debug.Log((object) this.FormatUnityLogString(entry.Text), entry.Fsm.OwnerObject);
          break;
      }
    }

    private FsmLogEntry NewFsmLogEntry(FsmLogType logType)
    {
      FsmLogEntry entry = FsmLog.logEntryPool[FsmLog.nextLogEntryPoolIndex];
      if (entry.Log != null)
      {
        entry.Log.RemoveEntry(entry);
        entry.Reset();
      }
      entry.Log = this;
      entry.LogType = logType;
      ++FsmLog.nextLogEntryPoolIndex;
      if (FsmLog.nextLogEntryPoolIndex >= FsmLog.logEntryPool.Length)
        FsmLog.nextLogEntryPoolIndex = 0;
      return entry;
    }

    private void RemoveEntry(FsmLogEntry entry)
    {
      if (this.entries == null)
        return;
      this.entries.Remove(entry);
    }

    public void LogEvent(FsmEvent fsmEvent, FsmState state)
    {
      FsmLogEntry entry = this.NewFsmLogEntry(FsmLogType.Event);
      entry.State = state;
      entry.SentByState = Fsm.EventData.SentByState;
      entry.Action = Fsm.EventData.SentByAction;
      entry.Event = fsmEvent;
      this.AddEntry(entry);
    }

    public void LogSendEvent(FsmState state, FsmEvent fsmEvent, FsmEventTarget eventTarget)
    {
      if (state == null || fsmEvent == null || fsmEvent.IsSystemEvent)
        return;
      FsmLogEntry entry = this.NewFsmLogEntry(FsmLogType.SendEvent);
      entry.State = state;
      entry.Event = fsmEvent;
      entry.EventTarget = new FsmEventTarget(eventTarget);
      this.AddEntry(entry);
    }

    public void LogExitState(FsmState state)
    {
      if (state == null)
        return;
      FsmLogEntry entry = this.NewFsmLogEntry(FsmLogType.ExitState);
      entry.State = state;
      entry.StateTime = FsmTime.RealtimeSinceStartup - state.RealStartTime;
      if (FsmLog.EnableDebugFlow && state.Fsm != null && (state.Fsm.EnableDebugFlow && !PlayMakerFSM.ApplicationIsQuitting))
      {
        entry.FsmVariablesCopy = new FsmVariables(state.Fsm.Variables);
        entry.GlobalVariablesCopy = new FsmVariables(FsmVariables.GlobalVariables);
      }
      this.AddEntry(entry);
    }

    public void LogEnterState(FsmState state)
    {
      if (state == null)
        return;
      FsmLogEntry entry = this.NewFsmLogEntry(FsmLogType.EnterState);
      entry.State = state;
      if (FsmLog.EnableDebugFlow && state.Fsm != null && state.Fsm.EnableDebugFlow)
      {
        entry.FsmVariablesCopy = new FsmVariables(state.Fsm.Variables);
        entry.GlobalVariablesCopy = new FsmVariables(FsmVariables.GlobalVariables);
      }
      this.AddEntry(entry);
    }

    public void LogTransition(FsmState fromState, FsmTransition transition)
    {
      FsmLogEntry entry = this.NewFsmLogEntry(FsmLogType.Transition);
      entry.State = fromState;
      entry.Transition = transition;
      this.AddEntry(entry);
    }

    public void LogBreak()
    {
      FsmLogEntry entry = this.NewFsmLogEntry(FsmLogType.Break);
      entry.State = FsmExecutionStack.ExecutingState;
      Debug.Log((object) ("BREAK: " + this.FormatUnityLogString("Breakpoint")));
      this.AddEntry(entry);
    }

    public void LogAction(FsmLogType logType, string text, bool sendToUnityLog = false)
    {
      if (FsmExecutionStack.ExecutingAction == null)
      {
        switch (logType)
        {
          case FsmLogType.Info:
            Debug.Log((object) text);
            break;
          case FsmLogType.Warning:
            Debug.LogWarning((object) text);
            break;
          case FsmLogType.Error:
            Debug.LogError((object) text);
            break;
          default:
            Debug.Log((object) text);
            break;
        }
      }
      else
      {
        FsmLogEntry entry = this.NewFsmLogEntry(logType);
        entry.State = FsmExecutionStack.ExecutingState;
        entry.Action = FsmExecutionStack.ExecutingAction;
        entry.Text = FsmUtility.StripNamespace(FsmExecutionStack.ExecutingAction.ToString()) + " : " + text;
        this.AddEntry(entry, sendToUnityLog);
      }
    }

    public void Log(FsmLogType logType, string text)
    {
      FsmLogEntry entry = this.NewFsmLogEntry(logType);
      entry.State = FsmExecutionStack.ExecutingState;
      entry.Text = text;
      this.AddEntry(entry);
    }

    public void LogStart(FsmState startState)
    {
      FsmLogEntry entry = this.NewFsmLogEntry(FsmLogType.Start);
      entry.State = startState;
      this.AddEntry(entry);
    }

    public void LogStop() => this.AddEntry(this.NewFsmLogEntry(FsmLogType.Stop));

    public void Log(string text) => this.Log(FsmLogType.Info, text);

    public void LogWarning(string text) => this.Log(FsmLogType.Warning, text);

    public void LogError(string text) => this.Log(FsmLogType.Error, text);

    private string FormatUnityLogString(string text)
    {
      string str = Fsm.GetFullFsmLabel(this.Fsm);
      if (FsmExecutionStack.ExecutingState != null)
        str = str + " : " + FsmExecutionStack.ExecutingStateName;
      if (FsmExecutionStack.ExecutingAction != null)
        str += FsmExecutionStack.ExecutingAction.Name;
      return str + " : " + text;
    }

    public void Clear()
    {
      if (this.entries == null)
        return;
      this.entries.Clear();
    }

    public void OnDestroy()
    {
      FsmLog.Logs.Remove(this);
      this.Clear();
      this.entries = (List<FsmLogEntry>) null;
      this.Fsm = (Fsm) null;
    }
  }
}
