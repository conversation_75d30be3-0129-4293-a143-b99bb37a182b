// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.DelayedEvent
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using UnityEngine;

namespace HutongGames.PlayMaker
{
  public class DelayedEvent
  {
    private readonly Fsm fsm;
    private readonly FsmEvent fsmEvent;
    private readonly FsmEventTarget eventTarget;
    private FsmEventData eventData;
    private float timer;
    private float delay;
    private bool eventFired;

    public FsmEvent FsmEvent => this.fsmEvent;

    public float Timer => this.timer;

    public DelayedEvent(Fsm fsm, FsmEvent fsmEvent, float delay)
    {
      this.fsm = fsm;
      this.timer = delay;
      this.delay = delay;
      this.fsmEvent = fsmEvent;
      this.eventData = new FsmEventData(Fsm.EventData)
      {
        SentByFsm = FsmExecutionStack.ExecutingFsm,
        SentByState = FsmExecutionStack.ExecutingState,
        SentByAction = FsmExecutionStack.ExecutingAction
      };
    }

    public DelayedEvent(Fsm fsm, string fsmEventName, float delay)
      : this(fsm, FsmEvent.GetFsmEvent(fsmEventName), delay)
    {
    }

    public DelayedEvent(Fsm fsm, FsmEventTarget eventTarget, FsmEvent fsmEvent, float delay)
      : this(fsm, fsmEvent, delay)
      => this.eventTarget = eventTarget;

    public DelayedEvent(Fsm fsm, FsmEventTarget eventTarget, string fsmEvent, float delay)
      : this(fsm, fsmEvent, delay)
      => this.eventTarget = eventTarget;

    public DelayedEvent(PlayMakerFSM fsm, FsmEvent fsmEvent, float delay)
      : this(fsm.Fsm, fsmEvent, delay)
    {
    }

    public DelayedEvent(PlayMakerFSM fsm, string fsmEventName, float delay)
      : this(fsm.Fsm, fsmEventName, delay)
    {
    }

    public void Update()
    {
      this.timer -= Time.deltaTime;
      if ((double) this.timer >= 0.0)
        return;
      FsmEventData eventData = Fsm.EventData;
      Fsm.EventData = this.eventData;
      if (this.eventTarget == null)
        this.fsm.Event(this.fsmEvent);
      else
        this.fsm.Event(this.eventTarget, this.fsmEvent);
      this.fsm.UpdateStateChanges();
      this.eventFired = true;
      this.eventData = (FsmEventData) null;
      Fsm.EventData = eventData;
    }

    public float GetProgress() => (float) (1.0 - (double) this.timer / (double) this.delay);

    public static bool WasSent(DelayedEvent delayedEvent) => delayedEvent == null || delayedEvent.eventFired;

    public bool Finished => this.eventFired;
  }
}
