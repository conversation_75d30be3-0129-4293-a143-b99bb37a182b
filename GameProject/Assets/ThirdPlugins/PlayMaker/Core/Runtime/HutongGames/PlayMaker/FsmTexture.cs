// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmTexture
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmTexture : FsmObject
  {
    public override System.Type ObjectType => typeof (Texture);

    public Texture Value
    {
      get => base.Value as Texture;
      set => this.Value = value;
    }

    public FsmTexture()
    {
    }

    public FsmTexture(string name)
      : base(name)
    {
    }

    public FsmTexture(FsmObject source)
      : base(source)
    {
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmTexture((FsmObject) this);

    public override VariableType VariableType => VariableType.Texture;

    public override bool TestTypeConstraint(VariableType variableType, System.Type _objectType = null) => variableType == VariableType.Unknown || variableType == VariableType.Texture;
  }
}
