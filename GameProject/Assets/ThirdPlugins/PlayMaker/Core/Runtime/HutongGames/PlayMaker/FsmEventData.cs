// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmEventData
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using UnityEngine;

namespace HutongGames.PlayMaker
{
  public class FsmEventData
  {
    public GameObject SentByGameObject;
    public Fsm SentByFsm;
    public FsmState SentByState;
    public FsmStateAction SentByAction;
    public bool BoolData;
    public int IntData;
    public float FloatData;
    public Vector2 Vector2Data;
    public Vector3 Vector3Data;
    public string StringData;
    public Quaternion QuaternionData;
    public Rect RectData;
    public Color ColorData;
    public Object ObjectData;
    public GameObject GameObjectData;
    public Material MaterialData;
    public Texture TextureData;

    public FsmEventData()
    {
    }

    public FsmEventData(FsmEventData source)
    {
      if (source == null)
        return;
      this.SentByGameObject = source.SentByGameObject;
      this.SentByFsm = source.SentByFsm;
      this.SentByState = source.SentByState;
      this.SentByAction = source.SentByAction;
      this.BoolData = source.BoolData;
      this.IntData = source.IntData;
      this.FloatData = source.FloatData;
      this.Vector2Data = source.Vector2Data;
      this.Vector3Data = source.Vector3Data;
      this.StringData = source.StringData;
      this.QuaternionData = source.QuaternionData;
      this.RectData = source.RectData;
      this.ColorData = source.ColorData;
      this.ObjectData = source.ObjectData;
      this.GameObjectData = source.GameObjectData;
      this.MaterialData = source.MaterialData;
      this.TextureData = source.TextureData;
    }

    public void DebugLog()
    {
      Debug.Log((object) ("Sent By FSM: " + (this.SentByFsm != null ? this.SentByFsm.Name : "None")));
      Debug.Log((object) ("Sent By State: " + (this.SentByState != null ? this.SentByState.Name : "None")));
      Debug.Log((object) ("Sent By Action: " + (this.SentByAction != null ? this.SentByAction.GetType().Name : "None")));
    }
  }
}
