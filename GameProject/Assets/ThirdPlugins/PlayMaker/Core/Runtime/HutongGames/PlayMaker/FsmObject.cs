// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmObject
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmObject : NamedVariable
  {
    [SerializeField]
    private string typeName;
    [SerializeField]
    private UnityEngine.Object value;
    private System.Type objectType;

    public override System.Type ObjectType
    {
      get
      {
        if (this.objectType == null)
        {
          if (string.IsNullOrEmpty(this.typeName))
            this.typeName = typeof (UnityEngine.Object).FullName;
          this.objectType = ReflectionUtils.GetGlobalType(this.typeName);
        }
        return this.objectType;
      }
      set
      {
        this.objectType = value;
        if (this.objectType == null)
          this.objectType = typeof (UnityEngine.Object);
        if ((object) this.value != null)
        {
          System.Type type = this.value.GetType();
          if (!type.IsAssignableFrom(this.objectType) && !type.IsSubclassOf(this.objectType))
            this.value = (UnityEngine.Object) null;
        }
        this.typeName = this.objectType.FullName;
      }
    }

    public string TypeName => this.typeName;

    public UnityEngine.Object Value
    {
      get => this.CastVariable == null ? this.value : ((FsmObject) this.CastVariable).Value;
      set => this.value = value;
    }

    public override object RawValue
    {
      get => (object) this.value;
      set => this.value = (UnityEngine.Object) value;
    }

    public FsmObject()
    {
    }

    public FsmObject(string name)
      : base(name)
    {
      this.typeName = typeof (UnityEngine.Object).FullName;
      this.objectType = typeof (UnityEngine.Object);
    }

    public FsmObject(FsmObject source)
      : base((NamedVariable) source)
    {
      this.value = source.value;
      this.typeName = source.typeName;
      this.objectType = source.objectType;
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmObject(this);

    public override void Clear()
    {
      this.typeName = (string) null;
      this.value = (UnityEngine.Object) null;
    }

    public override VariableType VariableType => VariableType.Object;

    public override string ToString() => !(this.Value == (UnityEngine.Object) null) ? this.Value.ToString() : "None";

    public static implicit operator FsmObject(UnityEngine.Object value) => new FsmObject()
    {
      value = value
    };

    public override bool TestTypeConstraint(VariableType variableType, System.Type _objectType = null)
    {
      if (variableType == VariableType.Unknown)
        return true;
      if (!base.TestTypeConstraint(variableType, this.objectType))
        return false;
      return _objectType == null || _objectType == typeof (UnityEngine.Object) || this.ObjectType == _objectType || _objectType.IsAssignableFrom(this.ObjectType);
    }
  }
}
