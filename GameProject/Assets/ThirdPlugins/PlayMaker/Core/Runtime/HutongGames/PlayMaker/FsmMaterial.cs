// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmMaterial
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmMaterial : FsmObject
  {
    public override System.Type ObjectType => typeof (Material);

    public Material Value
    {
      get => base.Value as Material;
      set => this.Value = value;
    }

    public FsmMaterial()
    {
    }

    public FsmMaterial(string name)
      : base(name)
    {
    }

    public FsmMaterial(FsmObject source)
      : base(source)
    {
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmMaterial((FsmObject) this);

    public override VariableType VariableType => VariableType.Material;

    public override bool TestTypeConstraint(VariableType variableType, System.Type _objectType = null) => variableType == VariableType.Unknown || variableType == VariableType.Material;
  }
}
