// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmBool
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmBool : NamedVariable
  {
    [SerializeField]
    private bool value;

    public bool Value
    {
      get => this.CastVariable == null ? this.value : this.CastVariable.ToInt() > 0;
      set => this.value = value;
    }

    public override object RawValue
    {
      get => (object) this.value;
      set => this.value = (bool) value;
    }

    public FsmBool()
    {
    }

    public FsmBool(string name)
      : base(name)
    {
    }

    public FsmBool(FsmBool source)
      : base((NamedVariable) source)
    {
      if (source == null)
        return;
      this.value = source.value;
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmBool(this);

    public override VariableType VariableType => VariableType.Bool;

    public override string ToString() => this.Value.ToString();

    public override int ToInt() => !this.value ? 0 : 1;

    public override void Clear() => this.value = false;

    public static implicit operator FsmBool(bool value) => new FsmBool(string.Empty)
    {
      value = value
    };
  }
}
