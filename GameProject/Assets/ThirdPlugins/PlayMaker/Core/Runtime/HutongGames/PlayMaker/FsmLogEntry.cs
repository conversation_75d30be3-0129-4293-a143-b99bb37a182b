// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmLogEntry
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  public class FsmLogEntry
  {
    private string text;
    private string textWithTimecode;

    public FsmLog Log { get; set; }

    public FsmLogType LogType { get; set; }

    public Fsm Fsm => this.Log.Fsm;

    public FsmState State { get; set; }

    public FsmState SentByState { get; set; }

    public FsmStateAction Action { get; set; }

    public FsmEvent Event { get; set; }

    public FsmTransition Transition { get; set; }

    public FsmEventTarget EventTarget { get; set; }

    public float Time { get; set; }

    public float StateTime { get; set; }

    public int FrameCount { get; set; }

    public FsmVariables FsmVariablesCopy { get; set; }

    public FsmVariables GlobalVariablesCopy { get; set; }

    public GameObject GameObject { get; set; }

    public string GameObjectName { get; set; }

    public Texture GameObjectIcon { get; set; }

    public string Text
    {
      get
      {
        if (this.text == null)
        {
          switch (this.LogType)
          {
            case FsmLogType.Info:
            case FsmLogType.Warning:
            case FsmLogType.Error:
            case FsmLogType.Transition:
              break;
            case FsmLogType.Event:
              this.text = "EVENT: " + this.Event.Name;
              break;
            case FsmLogType.ExitState:
              this.text = string.Format("EXIT: {0} [{1:f2}s]", (object) this.State.Name, (object) this.StateTime);
              break;
            case FsmLogType.EnterState:
              this.text = "ENTER: " + this.State.Name;
              break;
            case FsmLogType.Break:
              this.text = "BREAK: " + this.State.Name;
              break;
            case FsmLogType.SendEvent:
              this.text = "SEND EVENT: " + this.Event.Name;
              break;
            case FsmLogType.Start:
              this.text = "START";
              break;
            case FsmLogType.Stop:
              this.text = "STOP";
              break;
            default:
              throw new ArgumentOutOfRangeException();
          }
        }
        return this.text;
      }
      set => this.text = value;
    }

    public string Text2 { get; set; }

    public string TextWithTimecode => this.textWithTimecode ?? (this.textWithTimecode = FsmTime.FormatTime(this.Time) + " " + this.Text);

    public int GetIndex()
    {
      for (int index = 0; index < this.Log.Entries.Count; ++index)
      {
        if (this.Log.Entries[index] == this)
          return index;
      }
      return -1;
    }

    public void Reset()
    {
      this.Log = (FsmLog) null;
      this.State = (FsmState) null;
      this.SentByState = (FsmState) null;
      this.Action = (FsmStateAction) null;
      this.Event = (FsmEvent) null;
      this.Transition = (FsmTransition) null;
      this.EventTarget = (FsmEventTarget) null;
      this.Time = 0.0f;
      this.StateTime = 0.0f;
      this.FrameCount = 0;
      this.FsmVariablesCopy = (FsmVariables) null;
      this.GlobalVariablesCopy = (FsmVariables) null;
      this.GameObject = (GameObject) null;
      this.GameObjectName = (string) null;
      this.GameObjectIcon = (Texture) null;
      this.Text = (string) null;
      this.Text2 = (string) null;
      this.textWithTimecode = (string) null;
    }

    public void DebugLog() => Debug.Log((object) ("Sent By: " + FsmUtility.GetPath(this.SentByState) + " : " + (this.Action != null ? this.Action.Name : "None (Action)")));
  }
}
