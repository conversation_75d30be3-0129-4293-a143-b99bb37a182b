// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.TooltipAttribute
// Assembly: PlayMaker, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using HutongGames.Utility;
using System;

namespace HutongGames.PlayMaker
{
  [AttributeUsage(AttributeTargets.All)]
  public sealed class TooltipAttribute : Attribute
  {
    private readonly string text;
    private readonly string codedText;

    public string Text => this.text;

    public string CodedText => this.codedText;

    public TooltipAttribute(string text)
    {
      this.codedText = StringUtils.Escape(text);
      this.text = StringUtils.StripHtmlAndMarkdown(text.Replace("</li>", "\n"));
    }
  }
}
