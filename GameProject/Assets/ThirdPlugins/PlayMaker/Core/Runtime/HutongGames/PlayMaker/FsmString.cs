// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmString
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmString : NamedVariable
  {
    [SerializeField]
    private string value = "";

    public string Value
    {
      get => this.CastVariable == null ? this.value : this.CastVariable.ToString();
      set => this.value = value;
    }

    public override object RawValue
    {
      get => (object) this.value;
      set => this.value = (string) value;
    }

    public FsmString()
    {
    }

    public FsmString(string name)
      : base(name)
    {
    }

    public FsmString(FsmString source)
      : base((NamedVariable) source)
    {
      if (source == null)
        return;
      this.value = source.value;
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmString(this);

    public override VariableType VariableType => VariableType.String;

    public override string ToString() => this.Value;

    public override int ToInt()
    {
      float result;
      float.TryParse(this.value, out result);
      return (int) result;
    }

    public override void Clear() => this.value = "";

    public override float ToFloat()
    {
      float result;
      float.TryParse(this.value, out result);
      return result;
    }

    public static implicit operator FsmString(string value) => new FsmString(string.Empty)
    {
      value = value
    };

    public static bool IsNullOrEmpty(FsmString fsmString) => fsmString == null || fsmString.IsNone || string.IsNullOrEmpty(fsmString.value);
  }
}
