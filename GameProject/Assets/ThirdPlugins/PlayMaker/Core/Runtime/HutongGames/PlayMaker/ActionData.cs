// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.ActionData
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using HutongGames.PlayMaker.Actions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;
using System.Text;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class ActionData
  {
    private const string autoNameString = "~AutoName";
    private static readonly Dictionary<string, System.Type> ActionTypeLookup = new Dictionary<string, System.Type>();
    public static readonly Dictionary<System.Type, FieldInfo[]> ActionFieldsLookup = new Dictionary<System.Type, FieldInfo[]>();
    public static readonly Dictionary<System.Type, int> ActionHashCodeLookup = new Dictionary<System.Type, int>();
    private static bool resaveActionData;
    private static readonly List<int> UsedIndices = new List<int>();
    private static readonly List<FieldInfo> LoadedFields = new List<FieldInfo>();
    [SerializeField]
    private List<string> actionNames = new List<string>();
    [SerializeField]
    private List<string> customNames = new List<string>();
    [SerializeField]
    private List<bool> actionEnabled = new List<bool>();
    [SerializeField]
    private List<bool> actionIsOpen = new List<bool>();
    [SerializeField]
    private List<int> actionStartIndex = new List<int>();
    [SerializeField]
    private List<int> actionHashCodes = new List<int>();
    [SerializeField]
    private List<UnityEngine.Object> unityObjectParams;
    [SerializeField]
    private List<FsmGameObject> fsmGameObjectParams;
    [SerializeField]
    private List<FsmOwnerDefault> fsmOwnerDefaultParams;
    [SerializeField]
    private List<FsmAnimationCurve> animationCurveParams;
    [SerializeField]
    private List<FunctionCall> functionCallParams;
    [SerializeField]
    private List<FsmTemplateControl> fsmTemplateControlParams;
    [SerializeField]
    private List<FsmEventTarget> fsmEventTargetParams;
    [SerializeField]
    private List<FsmProperty> fsmPropertyParams;
    [SerializeField]
    private List<LayoutOption> layoutOptionParams;
    [SerializeField]
    private List<FsmString> fsmStringParams;
    [SerializeField]
    private List<FsmObject> fsmObjectParams;
    [SerializeField]
    private List<FsmVar> fsmVarParams;
    [SerializeField]
    private List<FsmArray> fsmArrayParams;
    [SerializeField]
    private List<FsmEnum> fsmEnumParams;
    [SerializeField]
    private List<FsmFloat> fsmFloatParams;
    [SerializeField]
    private List<FsmInt> fsmIntParams;
    [SerializeField]
    private List<FsmBool> fsmBoolParams;
    [SerializeField]
    private List<FsmVector2> fsmVector2Params;
    [SerializeField]
    private List<FsmVector3> fsmVector3Params;
    [SerializeField]
    private List<FsmColor> fsmColorParams;
    [SerializeField]
    private List<FsmRect> fsmRectParams;
    [SerializeField]
    private List<FsmQuaternion> fsmQuaternionParams;
    [SerializeField]
    private List<string> stringParams;
    [SerializeField]
    private List<byte> byteData = new List<byte>();
    [NonSerialized]
    private byte[] byteDataAsArray;
    [SerializeField]
    private List<int> arrayParamSizes;
    [SerializeField]
    private List<string> arrayParamTypes;
    [SerializeField]
    private List<int> customTypeSizes;
    [SerializeField]
    private List<string> customTypeNames;
    [SerializeField]
    private List<ParamDataType> paramDataType = new List<ParamDataType>();
    [SerializeField]
    private List<string> paramName = new List<string>();
    [SerializeField]
    private List<int> paramDataPos = new List<int>();
    [SerializeField]
    private List<int> paramByteDataSize = new List<int>();
    private int nextParamIndex;
    private const int MUST_BE_LESS_THAN = 100000000;

    public int ActionCount => this.actionNames.Count;

    public List<string> ActionNames => this.actionNames;

    public ActionData Copy() => new ActionData()
    {
      actionNames = new List<string>((IEnumerable<string>) this.actionNames),
      customNames = new List<string>((IEnumerable<string>) this.customNames),
      actionEnabled = new List<bool>((IEnumerable<bool>) this.actionEnabled),
      actionIsOpen = new List<bool>((IEnumerable<bool>) this.actionIsOpen),
      actionStartIndex = new List<int>((IEnumerable<int>) this.actionStartIndex),
      actionHashCodes = new List<int>((IEnumerable<int>) this.actionHashCodes),
      fsmFloatParams = this.CopyFsmFloatParams(),
      fsmIntParams = this.CopyFsmIntParams(),
      fsmBoolParams = this.CopyFsmBoolParams(),
      fsmColorParams = this.CopyFsmColorParams(),
      fsmVector2Params = this.CopyFsmVector2Params(),
      fsmVector3Params = this.CopyFsmVector3Params(),
      fsmRectParams = this.CopyFsmRectParams(),
      fsmQuaternionParams = this.CopyFsmQuaternionParams(),
      stringParams = this.CopyStringParams(),
      byteData = new List<byte>((IEnumerable<byte>) this.byteData),
      unityObjectParams = this.unityObjectParams != null ? new List<UnityEngine.Object>((IEnumerable<UnityEngine.Object>) this.unityObjectParams) : (List<UnityEngine.Object>) null,
      fsmStringParams = this.CopyFsmStringParams(),
      fsmObjectParams = this.CopyFsmObjectParams(),
      fsmGameObjectParams = this.CopyFsmGameObjectParams(),
      fsmOwnerDefaultParams = this.CopyFsmOwnerDefaultParams(),
      animationCurveParams = this.CopyAnimationCurveParams(),
      functionCallParams = this.CopyFunctionCallParams(),
      fsmTemplateControlParams = this.CopyFsmTemplateControlParams(),
      fsmVarParams = this.CopyFsmVarParams(),
      fsmArrayParams = this.CopyFsmArrayParams(),
      fsmEnumParams = this.CopyFsmEnumParams(),
      fsmPropertyParams = this.CopyFsmPropertyParams(),
      fsmEventTargetParams = this.CopyFsmEventTargetParams(),
      layoutOptionParams = this.CopyLayoutOptionParams(),
      arrayParamSizes = this.arrayParamSizes != null ? new List<int>((IEnumerable<int>) this.arrayParamSizes) : (List<int>) null,
      arrayParamTypes = this.arrayParamTypes != null ? new List<string>((IEnumerable<string>) this.arrayParamTypes) : (List<string>) null,
      customTypeSizes = this.customTypeSizes != null ? new List<int>((IEnumerable<int>) this.customTypeSizes) : (List<int>) null,
      customTypeNames = this.customTypeNames != null ? new List<string>((IEnumerable<string>) this.customTypeNames) : (List<string>) null,
      paramName = new List<string>((IEnumerable<string>) this.paramName),
      paramDataPos = new List<int>((IEnumerable<int>) this.paramDataPos),
      paramByteDataSize = new List<int>((IEnumerable<int>) this.paramByteDataSize),
      paramDataType = new List<ParamDataType>((IEnumerable<ParamDataType>) this.paramDataType)
    };

    private List<string> CopyStringParams() => this.stringParams == null ? (List<string>) null : new List<string>((IEnumerable<string>) this.stringParams);

    private List<FsmFloat> CopyFsmFloatParams()
    {
      if (this.fsmFloatParams == null)
        return (List<FsmFloat>) null;
      List<FsmFloat> fsmFloatList = new List<FsmFloat>();
      foreach (FsmFloat fsmFloatParam in this.fsmFloatParams)
        fsmFloatList.Add(new FsmFloat(fsmFloatParam));
      return fsmFloatList;
    }

    private List<FsmInt> CopyFsmIntParams()
    {
      if (this.fsmIntParams == null)
        return (List<FsmInt>) null;
      List<FsmInt> fsmIntList = new List<FsmInt>();
      foreach (FsmInt fsmIntParam in this.fsmIntParams)
        fsmIntList.Add(new FsmInt(fsmIntParam));
      return fsmIntList;
    }

    private List<FsmBool> CopyFsmBoolParams()
    {
      if (this.fsmBoolParams == null)
        return (List<FsmBool>) null;
      List<FsmBool> fsmBoolList = new List<FsmBool>();
      foreach (FsmBool fsmBoolParam in this.fsmBoolParams)
        fsmBoolList.Add(new FsmBool(fsmBoolParam));
      return fsmBoolList;
    }

    private List<FsmVector2> CopyFsmVector2Params()
    {
      if (this.fsmVector2Params == null)
        return (List<FsmVector2>) null;
      List<FsmVector2> fsmVector2List = new List<FsmVector2>();
      foreach (FsmVector2 fsmVector2Param in this.fsmVector2Params)
        fsmVector2List.Add(new FsmVector2(fsmVector2Param));
      return fsmVector2List;
    }

    private List<FsmVector3> CopyFsmVector3Params()
    {
      if (this.fsmVector3Params == null)
        return (List<FsmVector3>) null;
      List<FsmVector3> fsmVector3List = new List<FsmVector3>();
      foreach (FsmVector3 fsmVector3Param in this.fsmVector3Params)
        fsmVector3List.Add(new FsmVector3(fsmVector3Param));
      return fsmVector3List;
    }

    private List<FsmColor> CopyFsmColorParams()
    {
      if (this.fsmColorParams == null)
        return (List<FsmColor>) null;
      List<FsmColor> fsmColorList = new List<FsmColor>();
      foreach (FsmColor fsmColorParam in this.fsmColorParams)
        fsmColorList.Add(new FsmColor(fsmColorParam));
      return fsmColorList;
    }

    private List<FsmRect> CopyFsmRectParams()
    {
      if (this.fsmRectParams == null)
        return (List<FsmRect>) null;
      List<FsmRect> fsmRectList = new List<FsmRect>();
      foreach (FsmRect fsmRectParam in this.fsmRectParams)
        fsmRectList.Add(new FsmRect(fsmRectParam));
      return fsmRectList;
    }

    private List<FsmQuaternion> CopyFsmQuaternionParams()
    {
      if (this.fsmQuaternionParams == null)
        return (List<FsmQuaternion>) null;
      List<FsmQuaternion> fsmQuaternionList = new List<FsmQuaternion>();
      foreach (FsmQuaternion fsmQuaternionParam in this.fsmQuaternionParams)
        fsmQuaternionList.Add(new FsmQuaternion(fsmQuaternionParam));
      return fsmQuaternionList;
    }

    private List<FsmString> CopyFsmStringParams()
    {
      if (this.fsmStringParams == null)
        return (List<FsmString>) null;
      List<FsmString> fsmStringList = new List<FsmString>();
      foreach (FsmString fsmStringParam in this.fsmStringParams)
        fsmStringList.Add(new FsmString(fsmStringParam));
      return fsmStringList;
    }

    private List<FsmObject> CopyFsmObjectParams()
    {
      if (this.fsmObjectParams == null)
        return (List<FsmObject>) null;
      List<FsmObject> fsmObjectList = new List<FsmObject>();
      foreach (FsmObject fsmObjectParam in this.fsmObjectParams)
        fsmObjectList.Add(new FsmObject(fsmObjectParam));
      return fsmObjectList;
    }

    private List<FsmGameObject> CopyFsmGameObjectParams()
    {
      if (this.fsmGameObjectParams == null)
        return (List<FsmGameObject>) null;
      List<FsmGameObject> fsmGameObjectList = new List<FsmGameObject>();
      foreach (FsmGameObject fsmGameObjectParam in this.fsmGameObjectParams)
        fsmGameObjectList.Add(new FsmGameObject(fsmGameObjectParam));
      return fsmGameObjectList;
    }

    private List<FsmOwnerDefault> CopyFsmOwnerDefaultParams()
    {
      if (this.fsmOwnerDefaultParams == null)
        return (List<FsmOwnerDefault>) null;
      List<FsmOwnerDefault> fsmOwnerDefaultList = new List<FsmOwnerDefault>();
      foreach (FsmOwnerDefault ownerDefaultParam in this.fsmOwnerDefaultParams)
        fsmOwnerDefaultList.Add(new FsmOwnerDefault(ownerDefaultParam));
      return fsmOwnerDefaultList;
    }

    private List<FsmAnimationCurve> CopyAnimationCurveParams()
    {
      if (this.animationCurveParams == null)
        return (List<FsmAnimationCurve>) null;
      List<FsmAnimationCurve> fsmAnimationCurveList = new List<FsmAnimationCurve>();
      foreach (FsmAnimationCurve animationCurveParam in this.animationCurveParams)
      {
        FsmAnimationCurve fsmAnimationCurve = new FsmAnimationCurve();
        if (animationCurveParam != null && animationCurveParam.curve != null)
        {
          fsmAnimationCurve.curve.keys = animationCurveParam.curve.keys;
          fsmAnimationCurve.curve.preWrapMode = animationCurveParam.curve.preWrapMode;
          fsmAnimationCurve.curve.postWrapMode = animationCurveParam.curve.postWrapMode;
        }
        fsmAnimationCurveList.Add(fsmAnimationCurve);
      }
      return fsmAnimationCurveList;
    }

    private List<FunctionCall> CopyFunctionCallParams()
    {
      if (this.functionCallParams == null)
        return (List<FunctionCall>) null;
      List<FunctionCall> functionCallList = new List<FunctionCall>();
      foreach (FunctionCall functionCallParam in this.functionCallParams)
        functionCallList.Add(new FunctionCall(functionCallParam));
      return functionCallList;
    }

    private List<FsmTemplateControl> CopyFsmTemplateControlParams()
    {
      if (this.fsmTemplateControlParams == null)
        return (List<FsmTemplateControl>) null;
      List<FsmTemplateControl> fsmTemplateControlList = new List<FsmTemplateControl>();
      foreach (FsmTemplateControl templateControlParam in this.fsmTemplateControlParams)
        fsmTemplateControlList.Add(new FsmTemplateControl(templateControlParam));
      return fsmTemplateControlList;
    }

    private List<FsmVar> CopyFsmVarParams()
    {
      if (this.fsmVarParams == null)
        return (List<FsmVar>) null;
      List<FsmVar> fsmVarList = new List<FsmVar>();
      foreach (FsmVar fsmVarParam in this.fsmVarParams)
        fsmVarList.Add(new FsmVar(fsmVarParam));
      return fsmVarList;
    }

    private List<FsmArray> CopyFsmArrayParams()
    {
      if (this.fsmArrayParams == null)
        return (List<FsmArray>) null;
      List<FsmArray> fsmArrayList = new List<FsmArray>();
      foreach (FsmArray fsmArrayParam in this.fsmArrayParams)
        fsmArrayList.Add(new FsmArray(fsmArrayParam));
      return fsmArrayList;
    }

    private List<FsmEnum> CopyFsmEnumParams()
    {
      if (this.fsmEnumParams == null)
        return (List<FsmEnum>) null;
      List<FsmEnum> fsmEnumList = new List<FsmEnum>();
      foreach (FsmEnum fsmEnumParam in this.fsmEnumParams)
        fsmEnumList.Add(new FsmEnum(fsmEnumParam));
      return fsmEnumList;
    }

    private List<FsmProperty> CopyFsmPropertyParams()
    {
      if (this.fsmPropertyParams == null)
        return (List<FsmProperty>) null;
      List<FsmProperty> fsmPropertyList = new List<FsmProperty>();
      foreach (FsmProperty fsmPropertyParam in this.fsmPropertyParams)
        fsmPropertyList.Add(new FsmProperty(fsmPropertyParam));
      return fsmPropertyList;
    }

    private List<FsmEventTarget> CopyFsmEventTargetParams()
    {
      if (this.fsmEventTargetParams == null)
        return (List<FsmEventTarget>) null;
      List<FsmEventTarget> fsmEventTargetList = new List<FsmEventTarget>();
      foreach (FsmEventTarget eventTargetParam in this.fsmEventTargetParams)
        fsmEventTargetList.Add(new FsmEventTarget(eventTargetParam));
      return fsmEventTargetList;
    }

    private List<LayoutOption> CopyLayoutOptionParams()
    {
      if (this.layoutOptionParams == null)
        return (List<LayoutOption>) null;
      List<LayoutOption> layoutOptionList = new List<LayoutOption>();
      foreach (LayoutOption layoutOptionParam in this.layoutOptionParams)
        layoutOptionList.Add(new LayoutOption(layoutOptionParam));
      return layoutOptionList;
    }

    private void ClearActionData()
    {
      this.actionNames.Clear();
      this.customNames.Clear();
      this.actionEnabled.Clear();
      this.actionIsOpen.Clear();
      this.actionStartIndex.Clear();
      this.actionHashCodes.Clear();
      this.byteData.Clear();
      this.unityObjectParams = (List<UnityEngine.Object>) null;
      this.fsmStringParams = (List<FsmString>) null;
      this.fsmObjectParams = (List<FsmObject>) null;
      this.fsmVarParams = (List<FsmVar>) null;
      this.fsmArrayParams = (List<FsmArray>) null;
      this.fsmEnumParams = (List<FsmEnum>) null;
      this.fsmGameObjectParams = (List<FsmGameObject>) null;
      this.fsmOwnerDefaultParams = (List<FsmOwnerDefault>) null;
      this.animationCurveParams = (List<FsmAnimationCurve>) null;
      this.functionCallParams = (List<FunctionCall>) null;
      this.fsmTemplateControlParams = (List<FsmTemplateControl>) null;
      this.fsmPropertyParams = (List<FsmProperty>) null;
      this.fsmEventTargetParams = (List<FsmEventTarget>) null;
      this.layoutOptionParams = (List<LayoutOption>) null;
      this.arrayParamSizes = (List<int>) null;
      this.arrayParamTypes = (List<string>) null;
      this.customTypeNames = (List<string>) null;
      this.customTypeSizes = (List<int>) null;
      this.fsmFloatParams = (List<FsmFloat>) null;
      this.fsmIntParams = (List<FsmInt>) null;
      this.fsmBoolParams = (List<FsmBool>) null;
      this.fsmVector2Params = (List<FsmVector2>) null;
      this.fsmVector3Params = (List<FsmVector3>) null;
      this.fsmColorParams = (List<FsmColor>) null;
      this.fsmRectParams = (List<FsmRect>) null;
      this.fsmQuaternionParams = (List<FsmQuaternion>) null;
      this.stringParams = (List<string>) null;
      this.paramDataPos.Clear();
      this.paramByteDataSize.Clear();
      this.paramDataType.Clear();
      this.paramName.Clear();
      this.nextParamIndex = 0;
    }

    public static System.Type GetActionType(string actionName)
    {
      System.Type type;
      if (ActionData.ActionTypeLookup.TryGetValue(actionName, out type))
        return type;
      System.Type globalType = ReflectionUtils.GetGlobalType(actionName);
      if (globalType == null)
        return (System.Type) null;
      ActionData.ActionTypeLookup[actionName] = globalType;
      return globalType;
    }

    public static FieldInfo[] GetFields(System.Type actionType)
    {
      FieldInfo[] fieldInfoArray;
      if (ActionData.ActionFieldsLookup.TryGetValue(actionType, out fieldInfoArray))
        return fieldInfoArray;
      FieldInfo[] publicFields = actionType.GetPublicFields();
      ActionData.ActionFieldsLookup[actionType] = publicFields;
      return publicFields;
    }

    private static int GetActionTypeHashCode(System.Type actionType)
    {
      int num;
      if (ActionData.ActionHashCodeLookup.TryGetValue(actionType, out num))
        return num;
      string s = "";
      foreach (FieldInfo field in ActionData.GetFields(actionType))
        s = s + (object) field.FieldType + "|";
      int stableHash = ActionData.GetStableHash(s);
      ActionData.ActionHashCodeLookup[actionType] = stableHash;
      return stableHash;
    }

    private static int GetStableHash(string s)
    {
      uint num1 = 0;
      foreach (byte num2 in Encoding.Unicode.GetBytes(s))
      {
        uint num3 = num1 + (uint) num2;
        uint num4 = num3 + (num3 << 10);
        num1 = num4 ^ num4 >> 6;
      }
      uint num5 = num1 + (num1 << 3);
      uint num6 = num5 ^ num5 >> 11;
      return (int) ((num6 + (num6 << 15)) % 100000000U);
    }

    public FsmStateAction[] LoadActions(FsmState state)
    {
      ActionData.Context context = new ActionData.Context()
      {
        currentState = state,
        currentFsm = state.Fsm
      };
      int count = this.actionNames.Count;
      FsmStateAction[] actions = new FsmStateAction[count];
      this.byteDataAsArray = this.byteData.ToArray();
      ActionData.resaveActionData = false;
      for (int actionIndex = 0; actionIndex < count; ++actionIndex)
      {
        FsmStateAction action = this.CreateAction(context, actionIndex);
        if (action != null)
          actions[actionIndex] = action;
      }
      if (ActionData.resaveActionData && !PlayMakerGlobals.IsBuilding)
      {
        this.SaveActions(state, actions);
        actions = this.LoadActions(state);
        state.Fsm.setDirty = true;
      }
      return actions;
    }

    public FsmStateAction CreateAction(FsmState state, int actionIndex) => this.CreateAction(new ActionData.Context()
    {
      currentState = state,
      currentFsm = state.Fsm
    }, actionIndex);

    public FsmStateAction CreateAction(ActionData.Context context, int actionIndex)
    {
      string info = string.Empty;
      context.currentActionIndex = actionIndex;
      FsmState currentState = context.currentState;
      if (currentState.Fsm == null)
        UnityEngine.Debug.LogError((object) "state.Fsm == null");
      string str1 = this.actionNames[actionIndex];
      System.Type actionType = ActionData.GetActionType(str1);
      if (actionType == null)
      {
        string actionName = ActionData.TryFixActionName(str1);
        actionType = ActionData.GetActionType(actionName);
        if (actionType == null)
        {
          MissingAction instance = (MissingAction) Activator.CreateInstance(typeof (MissingAction));
          string str2 = FsmUtility.StripNamespace(str1);
          instance.actionName = str1;
          context.currentAction = (FsmStateAction) instance;
          ActionData.LogError(context, "Could Not Create Action: " + str2 + " (Maybe the script was removed?)");
          UnityEngine.Debug.LogWarning((object) ("Could Not Create Action: " + FsmUtility.GetPath(currentState) + str2 + " (Maybe the script was removed?)"));
          return (FsmStateAction) instance;
        }
        info = "Action : " + str1 + " Updated To: " + actionName;
        str1 = actionName;
        ActionData.resaveActionData = true;
      }
      if (!(Activator.CreateInstance(actionType) is FsmStateAction action))
      {
        MissingAction instance = (MissingAction) Activator.CreateInstance(typeof (MissingAction));
        string str2 = FsmUtility.StripNamespace(str1);
        instance.actionName = str2;
        context.currentAction = (FsmStateAction) instance;
        ActionData.LogError(context, "Could Not Create Action: " + str2 + " (Maybe the script was removed?)");
        UnityEngine.Debug.LogError((object) ("Could Not Create Action: " + FsmUtility.GetPath(currentState) + str2 + " (Maybe the script was removed?)"));
        return (FsmStateAction) instance;
      }
      context.currentAction = action;
      if (!string.IsNullOrEmpty(info))
        ActionData.LogInfo(context, info);
      bool flag = true;
      if (this.paramDataType.Count != this.paramDataPos.Count || this.paramName.Count != this.paramDataPos.Count)
      {
        ActionData.resaveActionData = true;
        flag = false;
      }
      if (this.actionHashCodes[actionIndex] != ActionData.GetActionTypeHashCode(actionType))
      {
        action.Reset();
        ActionData.resaveActionData = true;
        flag = false;
        if (this.paramDataType.Count != this.paramDataPos.Count)
        {
          ActionData.LogError(context, "Action has changed since FSM was saved. Could not recover parameters. Parameters reset to default values.");
          UnityEngine.Debug.LogError((object) ("Action script has changed since Fsm was saved: " + FsmUtility.GetPath(currentState) + FsmUtility.StripNamespace(str1) + ". Parameters reset to default values..."));
        }
        else
        {
          try
          {
            action = this.TryRecoverAction(context, actionType, action, actionIndex);
          }
          catch
          {
            ActionData.LogError(context, "Action has changed since FSM was saved. Could not recover parameters. Parameters reset to default values.");
            throw;
          }
        }
      }
      this.nextParamIndex = this.actionStartIndex[actionIndex];
      if (flag)
      {
        foreach (FieldInfo field in ActionData.GetFields(actionType))
        {
          try
          {
            context.currentParameter = field.Name;
            this.LoadActionField(context.currentFsm, (object) action, field, this.nextParamIndex);
          }
          catch (Exception ex)
          {
            UnityEngine.Debug.LogError((object) ("Error Loading Action: " + currentState.Name + " : " + str1 + " : " + field.Name + "\n" + (object) ex));
          }
          ++this.nextParamIndex;
        }
      }
      if (this.actionEnabled.Count > actionIndex)
        action.Enabled = this.actionEnabled[actionIndex];
      if (PlayMakerGlobals.IsEditor)
        this.InitEditorData(action, actionIndex);
      return action;
    }

    public void InitEditorData(FsmState state)
    {
      for (int actionIndex = 0; actionIndex < state.Actions.Length; ++actionIndex)
        this.InitEditorData(state.Actions[actionIndex], actionIndex);
    }

    private void InitEditorData(FsmStateAction action, int actionIndex)
    {
      if (action.GetType() == typeof (MissingAction))
      {
        action.IsOpen = true;
      }
      else
      {
        if (this.customNames.Count > actionIndex)
        {
          action.Name = this.customNames[actionIndex];
          if (!PlayMakerGlobals.IsBuilding && !PlayMakerFSM.NotMainThread && action.Name == "~AutoName")
          {
            action.Name = action.AutoName();
            action.IsAutoNamed = true;
          }
        }
        action.IsOpen = this.actionIsOpen.Count <= actionIndex || this.actionIsOpen[actionIndex];
      }
    }

    private string GetStringParam(int paramIndex)
    {
      int index = paramIndex < 0 || paramIndex >= this.paramDataPos.Count ? -1 : this.paramDataPos[paramIndex];
      return index < 0 || this.stringParams == null || index >= this.stringParams.Count ? "" : this.stringParams[index];
    }

    private void LoadActionField(Fsm fsm, object obj, FieldInfo field, int paramIndex)
    {
      System.Type fieldType = field.FieldType;
      object obj1 = (object) null;
      if (fieldType == typeof (FsmGameObject))
        obj1 = (object) this.GetFsmGameObject(fsm, paramIndex);
      else if (fieldType == typeof (FsmEvent))
      {
        if (fsm.DataVersion > 1)
        {
          string stringParam = this.GetStringParam(paramIndex);
          obj1 = string.IsNullOrEmpty(stringParam) ? (object) (FsmEvent) null : (object) FsmEvent.GetFsmEvent(stringParam);
        }
        else
        {
          try
          {
            obj1 = (object) FsmUtility.ByteArrayToFsmEvent(this.byteDataAsArray, this.paramDataPos[paramIndex], this.paramByteDataSize[paramIndex]);
          }
          catch
          {
            obj1 = (object) null;
          }
        }
      }
      else if (fieldType == typeof (FsmFloat))
        obj1 = (object) this.GetFsmFloat(fsm, paramIndex);
      else if (fieldType == typeof (FsmInt))
        obj1 = (object) this.GetFsmInt(fsm, paramIndex);
      else if (fieldType == typeof (FsmBool))
        obj1 = (object) this.GetFsmBool(fsm, paramIndex);
      else if (fieldType == typeof (FsmVector2))
        obj1 = (object) this.GetFsmVector2(fsm, paramIndex);
      else if (fieldType == typeof (FsmVector3))
        obj1 = (object) this.GetFsmVector3(fsm, paramIndex);
      else if (fieldType == typeof (FsmRect))
        obj1 = (object) this.GetFsmRect(fsm, paramIndex);
      else if (fieldType == typeof (FsmQuaternion))
        obj1 = (object) this.GetFsmQuaternion(fsm, paramIndex);
      else if (fieldType == typeof (FsmColor))
        obj1 = (object) this.GetFsmColor(fsm, paramIndex);
      else if (fieldType == typeof (FsmObject))
        obj1 = (object) this.GetFsmObject(fsm, paramIndex);
      else if (fieldType == typeof (FsmMaterial))
        obj1 = (object) this.GetFsmMaterial(fsm, paramIndex);
      else if (fieldType == typeof (FsmTexture))
        obj1 = (object) this.GetFsmTexture(fsm, paramIndex);
      else if (fieldType == typeof (FunctionCall))
        obj1 = (object) this.GetFunctionCall(fsm, paramIndex);
      else if (fieldType == typeof (FsmTemplateControl))
        obj1 = (object) this.GetFsmTemplateControl(fsm, paramIndex);
      else if (fieldType == typeof (FsmVar))
        obj1 = (object) this.GetFsmVar(fsm, paramIndex);
      else if (fieldType == typeof (FsmEnum))
        obj1 = (object) this.GetFsmEnum(fsm, paramIndex);
      else if (fieldType == typeof (FsmArray))
        obj1 = (object) this.GetFsmArray(fsm, paramIndex);
      else if (fieldType == typeof (FsmProperty))
        obj1 = (object) this.GetFsmProperty(fsm, paramIndex);
      else if (fieldType == typeof (FsmEventTarget))
        obj1 = (object) this.GetFsmEventTarget(fsm, paramIndex);
      else if (fieldType == typeof (LayoutOption))
        obj1 = (object) this.GetLayoutOption(fsm, paramIndex);
      else if (fieldType == typeof (FsmOwnerDefault))
        obj1 = (object) this.GetFsmOwnerDefault(fsm, paramIndex);
      else if (fieldType == typeof (FsmAnimationCurve))
        obj1 = (object) this.GetFsmAnimationCurve(paramIndex);
      else if (fieldType == typeof (FsmString))
        obj1 = (object) this.GetFsmString(fsm, paramIndex);
      else if (fieldType == typeof (float))
      {
        try
        {
          obj1 = (object) FsmUtility.BitConverter.ToSingle(this.byteDataAsArray, this.paramDataPos[paramIndex]);
        }
        catch
        {
          obj1 = (object) 0.0f;
        }
      }
      else if (fieldType == typeof (int))
      {
        try
        {
          obj1 = (object) FsmUtility.BitConverter.ToInt32(this.byteDataAsArray, this.paramDataPos[paramIndex]);
        }
        catch (Exception ex)
        {
          obj1 = (object) 0;
        }
      }
      else if (fieldType == typeof (bool))
      {
        try
        {
          obj1 = (object) FsmUtility.BitConverter.ToBoolean(this.byteDataAsArray, this.paramDataPos[paramIndex]);
        }
        catch
        {
          obj1 = (object) false;
        }
      }
      else if (fieldType == typeof (Color))
      {
        try
        {
          obj1 = (object) FsmUtility.ByteArrayToColor(this.byteDataAsArray, this.paramDataPos[paramIndex]);
        }
        catch
        {
          obj1 = (object) new Color();
        }
      }
      else if (fieldType == typeof (Vector2))
      {
        try
        {
          obj1 = (object) FsmUtility.ByteArrayToVector2(this.byteDataAsArray, this.paramDataPos[paramIndex]);
        }
        catch
        {
          obj1 = (object) new Vector2();
        }
      }
      else if (fieldType == typeof (Vector3))
      {
        try
        {
          obj1 = (object) FsmUtility.ByteArrayToVector3(this.byteDataAsArray, this.paramDataPos[paramIndex]);
        }
        catch
        {
          obj1 = (object) new Vector3();
        }
      }
      else if (fieldType == typeof (Vector4))
      {
        try
        {
          obj1 = (object) FsmUtility.ByteArrayToVector4(this.byteDataAsArray, this.paramDataPos[paramIndex]);
        }
        catch
        {
          obj1 = (object) new Vector4();
        }
      }
      else if (fieldType == typeof (Rect))
      {
        try
        {
          obj1 = (object) FsmUtility.ByteArrayToRect(this.byteDataAsArray, this.paramDataPos[paramIndex]);
        }
        catch
        {
          obj1 = (object) new Rect();
        }
      }
      else if (fieldType == typeof (string))
      {
        if (fsm.DataVersion > 1 && this.stringParams != null && this.stringParams.Count > 0)
        {
          obj1 = (object) this.GetStringParam(paramIndex);
        }
        else
        {
          try
          {
            obj1 = (object) FsmUtility.ByteArrayToString(this.byteDataAsArray, this.paramDataPos[paramIndex], this.paramByteDataSize[paramIndex]);
          }
          catch
          {
            obj1 = (object) "";
          }
        }
      }
      else if (fieldType.IsEnum)
      {
        try
        {
          obj1 = Enum.ToObject(fieldType, (object) FsmUtility.BitConverter.ToInt32(this.byteDataAsArray, this.paramDataPos[paramIndex]));
        }
        catch
        {
          obj1 = Activator.CreateInstance(fieldType);
        }
      }
      else if (typeof (FsmObject).IsAssignableFrom(fieldType))
        field.SetValue(obj, (object) this.GetFsmObject(fsm, paramIndex));
      else if (typeof (UnityEngine.Object).IsAssignableFrom(fieldType))
      {
        try
        {
          UnityEngine.Object unityObjectParam = this.unityObjectParams[this.paramDataPos[paramIndex]];
          if ((object) unityObjectParam != null)
          {
            if (unityObjectParam.GetType() == typeof (UnityEngine.Object))
              return;
            field.SetValue(obj, (object) unityObjectParam);
            return;
          }
        }
        catch
        {
          field.SetValue(obj, (object) null);
          return;
        }
      }
      else if (fieldType.IsArray)
      {
        try
        {
          System.Type globalType = ReflectionUtils.GetGlobalType(this.arrayParamTypes[this.paramDataPos[paramIndex]]);
          int arrayParamSiz = this.arrayParamSizes[this.paramDataPos[paramIndex]];
          Array instance = Array.CreateInstance(globalType, arrayParamSiz);
          for (int elementIndex = 0; elementIndex < arrayParamSiz; ++elementIndex)
          {
            ++this.nextParamIndex;
            this.LoadArrayElement(fsm, instance, globalType, elementIndex, this.nextParamIndex);
          }
          field.SetValue(obj, (object) instance);
          return;
        }
        catch
        {
          field.SetValue(obj, (object) null);
          return;
        }
      }
      else if (fieldType.IsClass)
      {
        try
        {
          System.Type globalType = ReflectionUtils.GetGlobalType(this.customTypeNames[this.paramDataPos[paramIndex]]);
          object instance = Activator.CreateInstance(globalType);
          int customTypeSiz = this.customTypeSizes[this.paramDataPos[paramIndex]];
          for (int index = 0; index < customTypeSiz; ++index)
          {
            ++this.nextParamIndex;
            FieldInfo field1 = globalType.GetField(this.paramName[this.nextParamIndex]);
            if (field1 != null)
              this.LoadActionField(fsm, instance, field1, this.nextParamIndex);
          }
          field.SetValue(obj, instance);
          return;
        }
        catch
        {
          field.SetValue(obj, (object) null);
          return;
        }
      }
      else
      {
        UnityEngine.Debug.LogError((object) ("ActionData: Missing LoadActionField for type: " + (object) fieldType));
        field.SetValue(obj, (object) null);
        return;
      }
      field.SetValue(obj, obj1);
    }

    private void LoadArrayElement(
      Fsm fsm,
      Array field,
      System.Type fieldType,
      int elementIndex,
      int paramIndex)
    {
      if (elementIndex >= field.GetLength(0) || paramIndex >= this.paramDataPos.Count)
        return;
      if (fieldType == typeof (FsmGameObject))
        field.SetValue((object) this.GetFsmGameObject(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FunctionCall))
        field.SetValue((object) this.GetFunctionCall(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmProperty))
        field.SetValue((object) this.GetFsmProperty(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (LayoutOption))
        field.SetValue((object) this.GetLayoutOption(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmOwnerDefault))
        field.SetValue((object) this.GetFsmOwnerDefault(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmAnimationCurve))
        field.SetValue((object) this.GetFsmAnimationCurve(paramIndex), elementIndex);
      else if (fieldType == typeof (FsmVar))
        field.SetValue((object) this.GetFsmVar(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmArray))
        field.SetValue((object) this.GetFsmArray(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmString))
        field.SetValue((object) this.GetFsmString(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmObject))
        field.SetValue((object) this.GetFsmObject(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmMaterial))
        field.SetValue((object) this.GetFsmMaterial(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmTexture))
        field.SetValue((object) this.GetFsmTexture(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmEnum))
        field.SetValue((object) this.GetFsmEnum(fsm, paramIndex), elementIndex);
      else if (fieldType.IsArray)
        UnityEngine.Debug.LogError((object) "Nested arrays are not supported!");
      else if (fieldType == typeof (FsmEvent))
      {
        if (fsm.DataVersion > 1)
        {
          string stringParam = this.GetStringParam(paramIndex);
          field.SetValue(string.IsNullOrEmpty(stringParam) ? (object) (FsmEvent) null : (object) FsmEvent.GetFsmEvent(stringParam), elementIndex);
        }
        else
        {
          try
          {
            field.SetValue((object) FsmUtility.ByteArrayToFsmEvent(this.byteDataAsArray, this.paramDataPos[paramIndex], this.paramByteDataSize[paramIndex]), elementIndex);
          }
          catch
          {
            field.SetValue((object) null, elementIndex);
          }
        }
      }
      else if (fieldType == typeof (FsmFloat))
        field.SetValue((object) this.GetFsmFloat(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmInt))
        field.SetValue((object) this.GetFsmInt(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmBool))
        field.SetValue((object) this.GetFsmBool(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmVector2))
        field.SetValue((object) this.GetFsmVector2(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmVector3))
        field.SetValue((object) this.GetFsmVector3(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmRect))
        field.SetValue((object) this.GetFsmRect(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmQuaternion))
        field.SetValue((object) this.GetFsmQuaternion(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (FsmColor))
        field.SetValue((object) this.GetFsmColor(fsm, paramIndex), elementIndex);
      else if (fieldType == typeof (float))
      {
        try
        {
          field.SetValue((object) FsmUtility.BitConverter.ToSingle(this.byteDataAsArray, this.paramDataPos[paramIndex]), elementIndex);
        }
        catch
        {
          field.SetValue((object) 0.0f, elementIndex);
        }
      }
      else if (fieldType == typeof (int))
      {
        try
        {
          field.SetValue((object) FsmUtility.BitConverter.ToInt32(this.byteDataAsArray, this.paramDataPos[paramIndex]), elementIndex);
        }
        catch
        {
          field.SetValue((object) 0, elementIndex);
        }
      }
      else if (fieldType == typeof (bool))
      {
        try
        {
          field.SetValue((object) FsmUtility.BitConverter.ToBoolean(this.byteDataAsArray, this.paramDataPos[paramIndex]), elementIndex);
        }
        catch
        {
          field.SetValue((object) false, elementIndex);
        }
      }
      else if (fieldType == typeof (Color))
      {
        try
        {
          field.SetValue((object) FsmUtility.ByteArrayToColor(this.byteDataAsArray, this.paramDataPos[paramIndex]), elementIndex);
        }
        catch
        {
          field.SetValue((object) new Color(), elementIndex);
        }
      }
      else if (fieldType == typeof (Vector2))
      {
        try
        {
          field.SetValue((object) FsmUtility.ByteArrayToVector2(this.byteDataAsArray, this.paramDataPos[paramIndex]), elementIndex);
        }
        catch
        {
          field.SetValue((object) new Vector2(), elementIndex);
        }
      }
      else if (fieldType == typeof (Vector3))
      {
        try
        {
          field.SetValue((object) FsmUtility.ByteArrayToVector3(this.byteDataAsArray, this.paramDataPos[paramIndex]), elementIndex);
        }
        catch
        {
          field.SetValue((object) new Vector3(), elementIndex);
        }
      }
      else if (fieldType == typeof (Vector4))
      {
        try
        {
          field.SetValue((object) FsmUtility.ByteArrayToVector4(this.byteDataAsArray, this.paramDataPos[paramIndex]), elementIndex);
        }
        catch
        {
          field.SetValue((object) new Vector4(), elementIndex);
        }
      }
      else if (fieldType == typeof (Rect))
      {
        try
        {
          field.SetValue((object) FsmUtility.ByteArrayToRect(this.byteDataAsArray, this.paramDataPos[paramIndex]), elementIndex);
        }
        catch
        {
          field.SetValue((object) new Rect(), elementIndex);
        }
      }
      else if (fieldType == typeof (string))
      {
        if (fsm.DataVersion > 1)
        {
          field.SetValue((object) this.GetStringParam(paramIndex), elementIndex);
        }
        else
        {
          try
          {
            field.SetValue((object) FsmUtility.ByteArrayToString(this.byteDataAsArray, this.paramDataPos[paramIndex], this.paramByteDataSize[paramIndex]), elementIndex);
          }
          catch
          {
            field.SetValue((object) "", elementIndex);
          }
        }
      }
      else if (fieldType.IsEnum)
      {
        try
        {
          object obj = Enum.ToObject(fieldType, (object) FsmUtility.BitConverter.ToInt32(this.byteDataAsArray, this.paramDataPos[paramIndex]));
          field.SetValue(obj, elementIndex);
        }
        catch
        {
          field.SetValue(Activator.CreateInstance(fieldType), elementIndex);
        }
      }
      else if (typeof (FsmObject).IsAssignableFrom(fieldType))
        field.SetValue((object) this.GetFsmObject(fsm, paramIndex), elementIndex);
      else if (typeof (UnityEngine.Object).IsAssignableFrom(fieldType))
      {
        try
        {
          UnityEngine.Object unityObjectParam = this.unityObjectParams[this.paramDataPos[paramIndex]];
          if ((object) unityObjectParam == null || unityObjectParam.GetType() == typeof (UnityEngine.Object))
            return;
          field.SetValue((object) unityObjectParam, elementIndex);
        }
        catch
        {
          field.SetValue((object) null, elementIndex);
        }
      }
      else if (fieldType.IsClass)
      {
        try
        {
          System.Type globalType = ReflectionUtils.GetGlobalType(this.customTypeNames[this.paramDataPos[paramIndex]]);
          object instance = Activator.CreateInstance(globalType);
          int customTypeSiz = this.customTypeSizes[this.paramDataPos[paramIndex]];
          for (int index = 0; index < customTypeSiz; ++index)
          {
            ++this.nextParamIndex;
            FieldInfo field1 = globalType.GetField(this.paramName[this.nextParamIndex]);
            if (field1 != null)
              this.LoadActionField(fsm, instance, field1, this.nextParamIndex);
          }
          field.SetValue(instance, elementIndex);
        }
        catch
        {
          field.SetValue((object) null, elementIndex);
        }
      }
      else
        field.SetValue((object) null, elementIndex);
    }

    public static void LogError(ActionData.Context context, string error)
    {
      if (context.currentState == null || context.currentAction == null || (context.currentFsm == null || context.currentFsm.Owner == null))
        return;
      ActionReport.LogError(context.currentFsm, context.currentState, context.currentAction, context.currentActionIndex, context.currentParameter, error);
    }

    private static void LogInfo(ActionData.Context context, string info)
    {
      if (context.currentFsm == null || context.currentState == null || context.currentAction == null)
        UnityEngine.Debug.LogWarning((object) "Missing context!");
      else
        ActionReport.Log(context.currentFsm, context.currentState, context.currentAction, context.currentActionIndex, context.currentParameter, info);
    }

    private FsmFloat GetFsmFloat(Fsm fsm, int paramIndex)
    {
      if (fsm == null)
      {
        UnityEngine.Debug.LogError((object) "fsm == null!");
        return new FsmFloat();
      }
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmFloat();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (fsm.DataVersion > 1)
      {
        if (paramDataPo < 0 || this.fsmFloatParams == null || this.fsmFloatParams.Count <= paramDataPo)
          return new FsmFloat();
        FsmFloat fsmFloatParam = this.fsmFloatParams[paramDataPo];
        if (fsmFloatParam == null)
          return new FsmFloat();
        return string.IsNullOrEmpty(fsmFloatParam.Name) ? fsmFloatParam : fsm.GetFsmFloat(fsmFloatParam.Name);
      }
      if (this.paramByteDataSize != null)
        return FsmUtility.ByteArrayToFsmFloat(fsm, this.byteDataAsArray, paramDataPo, this.paramByteDataSize[paramIndex]);
      UnityEngine.Debug.LogError((object) ("paramByteDataSize == null! Data Version: " + (object) fsm.DataVersion));
      return new FsmFloat();
    }

    private FsmInt GetFsmInt(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmInt();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (fsm.DataVersion <= 1)
        return FsmUtility.ByteArrayToFsmInt(fsm, this.byteDataAsArray, paramDataPo, this.paramByteDataSize[paramIndex]);
      if (paramDataPo < 0 || this.fsmIntParams == null || this.fsmIntParams.Count <= paramDataPo)
        return new FsmInt();
      FsmInt fsmIntParam = this.fsmIntParams[paramDataPo];
      if (fsmIntParam == null)
        return new FsmInt();
      return string.IsNullOrEmpty(fsmIntParam.Name) ? fsmIntParam : fsm.GetFsmInt(fsmIntParam.Name);
    }

    private FsmBool GetFsmBool(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmBool();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (fsm.DataVersion <= 1)
        return FsmUtility.ByteArrayToFsmBool(fsm, this.byteDataAsArray, paramDataPo, this.paramByteDataSize[paramIndex]);
      if (paramDataPo < 0 || this.fsmBoolParams == null || this.fsmBoolParams.Count <= paramDataPo)
        return new FsmBool();
      FsmBool fsmBoolParam = this.fsmBoolParams[paramDataPo];
      if (fsmBoolParam == null)
        return new FsmBool();
      return string.IsNullOrEmpty(fsmBoolParam.Name) ? fsmBoolParam : fsm.GetFsmBool(fsmBoolParam.Name);
    }

    private FsmVector2 GetFsmVector2(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmVector2();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (fsm.DataVersion <= 1)
        return FsmUtility.ByteArrayToFsmVector2(fsm, this.byteDataAsArray, paramDataPo, this.paramByteDataSize[paramIndex]);
      if (paramDataPo < 0 || this.fsmVector2Params == null || this.fsmVector2Params.Count <= paramDataPo)
        return new FsmVector2();
      FsmVector2 fsmVector2Param = this.fsmVector2Params[paramDataPo];
      if (fsmVector2Param == null)
        return new FsmVector2();
      return string.IsNullOrEmpty(fsmVector2Param.Name) ? fsmVector2Param : fsm.GetFsmVector2(fsmVector2Param.Name);
    }

    private FsmVector3 GetFsmVector3(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmVector3();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (fsm.DataVersion <= 1)
        return FsmUtility.ByteArrayToFsmVector3(fsm, this.byteDataAsArray, paramDataPo, this.paramByteDataSize[paramIndex]);
      if (paramDataPo < 0 || this.fsmVector3Params == null || this.fsmVector3Params.Count <= paramDataPo)
        return new FsmVector3();
      FsmVector3 fsmVector3Param = this.fsmVector3Params[paramDataPo];
      if (fsmVector3Param == null)
        return new FsmVector3();
      return string.IsNullOrEmpty(fsmVector3Param.Name) ? fsmVector3Param : fsm.GetFsmVector3(fsmVector3Param.Name);
    }

    private FsmColor GetFsmColor(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmColor();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (fsm.DataVersion <= 1)
        return FsmUtility.ByteArrayToFsmColor(fsm, this.byteDataAsArray, paramDataPo, this.paramByteDataSize[paramIndex]);
      if (paramDataPo < 0 || this.fsmColorParams == null || this.fsmColorParams.Count <= paramDataPo)
        return new FsmColor();
      FsmColor fsmColorParam = this.fsmColorParams[paramDataPo];
      if (fsmColorParam == null)
        return new FsmColor();
      return string.IsNullOrEmpty(fsmColorParam.Name) ? fsmColorParam : fsm.GetFsmColor(fsmColorParam.Name);
    }

    private FsmAnimationCurve GetFsmAnimationCurve(int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmAnimationCurve();
      int paramDataPo = this.paramDataPos[paramIndex];
      return paramDataPo >= 0 && this.animationCurveParams != null && this.animationCurveParams.Count > paramDataPo ? this.animationCurveParams[paramDataPo] ?? new FsmAnimationCurve() : new FsmAnimationCurve();
    }

    private FsmRect GetFsmRect(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmRect();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (fsm.DataVersion <= 1)
        return FsmUtility.ByteArrayToFsmRect(fsm, this.byteDataAsArray, paramDataPo, this.paramByteDataSize[paramIndex]);
      if (paramDataPo < 0 || this.fsmRectParams == null || this.fsmRectParams.Count <= paramDataPo)
        return new FsmRect();
      FsmRect fsmRectParam = this.fsmRectParams[paramDataPo];
      if (fsmRectParam == null)
        return new FsmRect();
      return string.IsNullOrEmpty(fsmRectParam.Name) ? fsmRectParam : fsm.GetFsmRect(fsmRectParam.Name);
    }

    private FsmQuaternion GetFsmQuaternion(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmQuaternion();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (fsm.DataVersion <= 1)
        return FsmUtility.ByteArrayToFsmQuaternion(fsm, this.byteDataAsArray, paramDataPo, this.paramByteDataSize[paramIndex]);
      if (paramDataPo < 0 || this.fsmQuaternionParams == null || this.fsmQuaternionParams.Count <= paramDataPo)
        return new FsmQuaternion();
      FsmQuaternion fsmQuaternionParam = this.fsmQuaternionParams[paramDataPo];
      if (fsmQuaternionParam == null)
        return new FsmQuaternion();
      return string.IsNullOrEmpty(fsmQuaternionParam.Name) ? fsmQuaternionParam : fsm.GetFsmQuaternion(fsmQuaternionParam.Name);
    }

    private FsmGameObject GetFsmGameObject(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmGameObject();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmGameObjectParams == null || this.fsmGameObjectParams.Count <= paramDataPo)
        return new FsmGameObject();
      FsmGameObject fsmGameObjectParam = this.fsmGameObjectParams[paramDataPo];
      if (fsmGameObjectParam == null)
        return new FsmGameObject();
      return string.IsNullOrEmpty(fsmGameObjectParam.Name) ? fsmGameObjectParam : fsm.GetFsmGameObject(fsmGameObjectParam.Name);
    }

    private FsmTemplateControl GetFsmTemplateControl(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmTemplateControl();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmTemplateControlParams == null || this.fsmTemplateControlParams.Count <= paramDataPo)
        return new FsmTemplateControl();
      FsmTemplateControl templateControlParam = this.fsmTemplateControlParams[paramDataPo];
      if (templateControlParam == null)
        return new FsmTemplateControl();
      if (templateControlParam.inputVariables != null)
      {
        foreach (FsmVarOverride inputVariable in templateControlParam.inputVariables)
        {
          if (templateControlParam.fsmTemplate != null && templateControlParam.fsmTemplate.fsm != null && inputVariable.variable.UsesVariable)
            inputVariable.variable = templateControlParam.fsmTemplate.fsm.Variables.GetVariable(inputVariable.variable.Name);
          if (inputVariable.fsmVar.NamedVar.UsesVariable)
            inputVariable.fsmVar.NamedVar = fsm.Variables.GetVariable(inputVariable.fsmVar.Type, inputVariable.fsmVar.variableName);
        }
      }
      return templateControlParam;
    }

    private FsmVar GetFsmVar(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmVar();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmVarParams == null || this.fsmVarParams.Count <= paramDataPo)
        return new FsmVar();
      FsmVar fsmVar = this.fsmVarParams[paramDataPo] ?? new FsmVar();
      if (!string.IsNullOrEmpty(fsmVar.variableName))
        fsmVar.NamedVar = fsm.Variables.GetVariable(fsmVar.variableName);
      return fsmVar;
    }

    private FsmArray GetFsmArray(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmArray();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmArrayParams == null || this.fsmArrayParams.Count <= paramDataPo)
        return new FsmArray();
      FsmArray fsmArray = this.fsmArrayParams[paramDataPo] ?? new FsmArray();
      return string.IsNullOrEmpty(fsmArray.Name) ? fsmArray : fsm.GetFsmArray(fsmArray.Name);
    }

    private FsmEnum GetFsmEnum(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmEnum();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmEnumParams == null || this.fsmEnumParams.Count <= paramDataPo)
        return new FsmEnum();
      FsmEnum fsmEnum = this.fsmEnumParams[paramDataPo] ?? new FsmEnum();
      return string.IsNullOrEmpty(fsmEnum.Name) ? fsmEnum : fsm.GetFsmEnum(fsmEnum.Name);
    }

    private FunctionCall GetFunctionCall(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FunctionCall();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.functionCallParams == null || this.functionCallParams.Count <= paramDataPo)
        return new FunctionCall();
      FunctionCall functionCallParam = this.functionCallParams[paramDataPo];
      if (functionCallParam == null)
        return new FunctionCall();
      if (!string.IsNullOrEmpty(functionCallParam.BoolParameter.Name))
        functionCallParam.BoolParameter = fsm.GetFsmBool(functionCallParam.BoolParameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.FloatParameter.Name))
        functionCallParam.FloatParameter = fsm.GetFsmFloat(functionCallParam.FloatParameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.GameObjectParameter.Name))
        functionCallParam.GameObjectParameter = fsm.GetFsmGameObject(functionCallParam.GameObjectParameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.IntParameter.Name))
        functionCallParam.IntParameter = fsm.GetFsmInt(functionCallParam.IntParameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.MaterialParameter.Name))
        functionCallParam.MaterialParameter = fsm.GetFsmMaterial(functionCallParam.MaterialParameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.ObjectParameter.Name))
        functionCallParam.ObjectParameter = fsm.GetFsmObject(functionCallParam.ObjectParameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.QuaternionParameter.Name))
        functionCallParam.QuaternionParameter = fsm.GetFsmQuaternion(functionCallParam.QuaternionParameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.RectParamater.Name))
        functionCallParam.RectParamater = fsm.GetFsmRect(functionCallParam.RectParamater.Name);
      if (!string.IsNullOrEmpty(functionCallParam.StringParameter.Name))
        functionCallParam.StringParameter = fsm.GetFsmString(functionCallParam.StringParameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.TextureParameter.Name))
        functionCallParam.TextureParameter = fsm.GetFsmTexture(functionCallParam.TextureParameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.Vector2Parameter.Name))
        functionCallParam.Vector2Parameter = fsm.GetFsmVector2(functionCallParam.Vector2Parameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.Vector3Parameter.Name))
        functionCallParam.Vector3Parameter = fsm.GetFsmVector3(functionCallParam.Vector3Parameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.EnumParameter.Name))
        functionCallParam.EnumParameter = fsm.GetFsmEnum(functionCallParam.EnumParameter.Name);
      if (!string.IsNullOrEmpty(functionCallParam.ArrayParameter.Name))
        functionCallParam.ArrayParameter = fsm.GetFsmArray(functionCallParam.ArrayParameter.Name);
      return functionCallParam;
    }

    private FsmProperty GetFsmProperty(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmProperty();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmPropertyParams == null || this.fsmPropertyParams.Count <= paramDataPo)
        return new FsmProperty();
      FsmProperty fsmPropertyParam = this.fsmPropertyParams[paramDataPo];
      if (fsmPropertyParam == null)
        return new FsmProperty();
      if (!string.IsNullOrEmpty(fsmPropertyParam.TargetObject.Name))
        fsmPropertyParam.TargetObject = fsm.GetFsmObject(fsmPropertyParam.TargetObject.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.BoolParameter.Name))
        fsmPropertyParam.BoolParameter = fsm.GetFsmBool(fsmPropertyParam.BoolParameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.FloatParameter.Name))
        fsmPropertyParam.FloatParameter = fsm.GetFsmFloat(fsmPropertyParam.FloatParameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.GameObjectParameter.Name))
        fsmPropertyParam.GameObjectParameter = fsm.GetFsmGameObject(fsmPropertyParam.GameObjectParameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.IntParameter.Name))
        fsmPropertyParam.IntParameter = fsm.GetFsmInt(fsmPropertyParam.IntParameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.MaterialParameter.Name))
        fsmPropertyParam.MaterialParameter = fsm.GetFsmMaterial(fsmPropertyParam.MaterialParameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.ObjectParameter.Name))
        fsmPropertyParam.ObjectParameter = fsm.GetFsmObject(fsmPropertyParam.ObjectParameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.QuaternionParameter.Name))
        fsmPropertyParam.QuaternionParameter = fsm.GetFsmQuaternion(fsmPropertyParam.QuaternionParameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.RectParamater.Name))
        fsmPropertyParam.RectParamater = fsm.GetFsmRect(fsmPropertyParam.RectParamater.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.StringParameter.Name))
        fsmPropertyParam.StringParameter = fsm.GetFsmString(fsmPropertyParam.StringParameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.TextureParameter.Name))
        fsmPropertyParam.TextureParameter = fsm.GetFsmTexture(fsmPropertyParam.TextureParameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.ColorParameter.Name))
        fsmPropertyParam.ColorParameter = fsm.GetFsmColor(fsmPropertyParam.ColorParameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.Vector2Parameter.Name))
        fsmPropertyParam.Vector2Parameter = fsm.GetFsmVector2(fsmPropertyParam.Vector2Parameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.Vector3Parameter.Name))
        fsmPropertyParam.Vector3Parameter = fsm.GetFsmVector3(fsmPropertyParam.Vector3Parameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.EnumParameter.Name))
        fsmPropertyParam.EnumParameter = fsm.GetFsmEnum(fsmPropertyParam.EnumParameter.Name);
      if (!string.IsNullOrEmpty(fsmPropertyParam.ArrayParameter.Name))
        fsmPropertyParam.ArrayParameter = fsm.GetFsmArray(fsmPropertyParam.ArrayParameter.Name);
      return fsmPropertyParam;
    }

    private FsmEventTarget GetFsmEventTarget(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmEventTarget();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmEventTargetParams == null || this.fsmEventTargetParams.Count <= paramDataPo)
        return new FsmEventTarget();
      FsmEventTarget eventTargetParam = this.fsmEventTargetParams[paramDataPo];
      if (eventTargetParam == null)
        return new FsmEventTarget();
      if (!string.IsNullOrEmpty(eventTargetParam.excludeSelf.Name))
        eventTargetParam.excludeSelf = fsm.GetFsmBool(eventTargetParam.excludeSelf.Name);
      string name = eventTargetParam.gameObject.GameObject.Name;
      if (!string.IsNullOrEmpty(name))
        eventTargetParam.gameObject.GameObject = fsm.GetFsmGameObject(name);
      if (!string.IsNullOrEmpty(eventTargetParam.fsmName.Name))
        eventTargetParam.fsmName = fsm.GetFsmString(eventTargetParam.fsmName.Name);
      if (!string.IsNullOrEmpty(eventTargetParam.sendToChildren.Name))
        eventTargetParam.sendToChildren = fsm.GetFsmBool(eventTargetParam.sendToChildren.Name);
      return eventTargetParam;
    }

    private LayoutOption GetLayoutOption(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new LayoutOption();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.layoutOptionParams == null || this.layoutOptionParams.Count <= paramDataPo)
        return new LayoutOption();
      LayoutOption layoutOptionParam = this.layoutOptionParams[paramDataPo];
      if (layoutOptionParam == null)
        return new LayoutOption();
      if (!string.IsNullOrEmpty(layoutOptionParam.boolParam.Name))
        layoutOptionParam.boolParam = fsm.GetFsmBool(layoutOptionParam.boolParam.Name);
      if (!string.IsNullOrEmpty(layoutOptionParam.floatParam.Name))
        layoutOptionParam.floatParam = fsm.GetFsmFloat(layoutOptionParam.floatParam.Name);
      return layoutOptionParam;
    }

    private FsmOwnerDefault GetFsmOwnerDefault(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmOwnerDefault();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmOwnerDefaultParams == null || this.fsmOwnerDefaultParams.Count <= paramDataPo)
        return new FsmOwnerDefault();
      FsmOwnerDefault ownerDefaultParam = this.fsmOwnerDefaultParams[paramDataPo];
      if (ownerDefaultParam == null)
        return new FsmOwnerDefault();
      if (ownerDefaultParam.OwnerOption != OwnerDefaultOption.UseOwner)
      {
        string name = ownerDefaultParam.GameObject.Name;
        if (!string.IsNullOrEmpty(name))
          ownerDefaultParam.GameObject = fsm.GetFsmGameObject(name);
      }
      return ownerDefaultParam;
    }

    private FsmString GetFsmString(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmString();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmStringParams == null || this.fsmStringParams.Count <= paramDataPo)
        return new FsmString();
      FsmString fsmStringParam = this.fsmStringParams[this.paramDataPos[paramIndex]];
      if (fsmStringParam == null)
        return new FsmString();
      return string.IsNullOrEmpty(fsmStringParam.Name) ? fsmStringParam : fsm.GetFsmString(fsmStringParam.Name);
    }

    private FsmObject GetFsmObject(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmObject();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmObjectParams == null || this.fsmObjectParams.Count <= paramDataPo)
        return new FsmObject();
      FsmObject fsmObjectParam = this.fsmObjectParams[paramDataPo];
      if (fsmObjectParam == null)
        return new FsmObject();
      return string.IsNullOrEmpty(fsmObjectParam.Name) ? fsmObjectParam : fsm.GetFsmObject(fsmObjectParam.Name);
    }

    private FsmMaterial GetFsmMaterial(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmMaterial();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmObjectParams == null || this.fsmObjectParams.Count <= paramDataPo)
        return new FsmMaterial();
      FsmObject fsmObjectParam = this.fsmObjectParams[paramDataPo];
      if (fsmObjectParam == null)
        return new FsmMaterial();
      return string.IsNullOrEmpty(fsmObjectParam.Name) ? new FsmMaterial(fsmObjectParam) : fsm.GetFsmMaterial(fsmObjectParam.Name);
    }

    private FsmTexture GetFsmTexture(Fsm fsm, int paramIndex)
    {
      if (paramIndex < 0 || paramIndex >= this.paramDataPos.Count)
        return new FsmTexture();
      int paramDataPo = this.paramDataPos[paramIndex];
      if (paramDataPo < 0 || this.fsmObjectParams == null || this.fsmObjectParams.Count <= paramDataPo)
        return new FsmTexture();
      FsmObject fsmObjectParam = this.fsmObjectParams[this.paramDataPos[paramIndex]];
      if (fsmObjectParam == null)
        return new FsmTexture();
      return string.IsNullOrEmpty(fsmObjectParam.Name) ? new FsmTexture(fsmObjectParam) : fsm.GetFsmTexture(fsmObjectParam.Name);
    }

    public bool UsesDataVersion2()
    {
      if (this.fsmArrayParams != null && this.fsmArrayParams.Count > 0 || this.fsmEnumParams != null && this.fsmEnumParams.Count > 0 || (this.fsmFloatParams != null && this.fsmFloatParams.Count > 0 || this.fsmIntParams != null && this.fsmIntParams.Count > 0) || (this.fsmBoolParams != null && this.fsmBoolParams.Count > 0 || this.fsmVector2Params != null && this.fsmVector2Params.Count > 0 || (this.fsmVector3Params != null && this.fsmVector3Params.Count > 0 || this.fsmColorParams != null && this.fsmColorParams.Count > 0)) || (this.fsmRectParams != null && this.fsmRectParams.Count > 0 || this.fsmQuaternionParams != null && this.fsmQuaternionParams.Count > 0))
        return true;
      return this.stringParams != null && this.stringParams.Count > 0;
    }

    private static string TryFixActionName(string actionName)
    {
      if (actionName == "HutongGames.PlayMaker.Actions.ScreenWrap2d")
        return "HutongGames.PlayMaker.Actions.ScreenWrap";
      return actionName == "HutongGames.PlayMaker.Actions.FloatAddMutiple" ? "HutongGames.PlayMaker.Actions.FloatAddMultiple" : "HutongGames.PlayMaker.Actions." + actionName;
    }

    private FsmStateAction TryRecoverAction(
      ActionData.Context context,
      System.Type actionType,
      FsmStateAction action,
      int actionIndex)
    {
      ActionData.UsedIndices.Clear();
      ActionData.LoadedFields.Clear();
      int num1 = this.actionStartIndex[actionIndex];
      int num2 = actionIndex < this.actionNames.Count - 1 ? this.actionStartIndex[actionIndex + 1] : this.paramDataPos.Count;
      if (this.paramName.Count == this.paramDataPos.Count)
      {
        for (int paramIndex = num1; paramIndex < num2; ++paramIndex)
        {
          FieldInfo field = this.FindField(actionType, paramIndex);
          if (field != null)
          {
            this.nextParamIndex = paramIndex;
            this.LoadActionField(context.currentFsm, (object) action, field, paramIndex);
            ActionData.UsedIndices.Add(paramIndex);
            ActionData.LoadedFields.Add(field);
          }
        }
        for (int index = num1; index < num2; ++index)
        {
          if (!ActionData.UsedIndices.Contains(index))
          {
            FieldInfo field = ActionData.FindField(actionType, this.paramName[index]);
            if (field != null)
            {
              this.nextParamIndex = index;
              if (this.TryConvertParameter(context, action, field, index))
              {
                ActionData.UsedIndices.Add(index);
                ActionData.LoadedFields.Add(field);
              }
            }
          }
        }
      }
      FieldInfo[] fields = ActionData.GetFields(actionType);
      int length = fields.Length;
      if (length > ActionData.LoadedFields.Count)
      {
        foreach (FieldInfo fieldInfo in fields)
        {
          if (!ActionData.LoadedFields.Contains(fieldInfo))
            ActionData.LogInfo(context, "New parameter: " + fieldInfo.Name + " (set to default value).");
        }
      }
      else if (num2 - num1 > length)
      {
        for (int index = num1; index < num2; ++index)
        {
          bool flag = false;
          string str = this.paramName[index];
          foreach (FieldInfo fieldInfo in fields)
          {
            if (!(str != fieldInfo.Name))
            {
              flag = true;
              break;
            }
          }
          if (!flag)
            ActionData.LogInfo(context, "Parameter removed: " + str);
        }
      }
      return action;
    }

    private FieldInfo FindField(System.Type actionType, int paramIndex)
    {
      string str = this.paramName[paramIndex];
      ParamDataType paramDataType1 = this.paramDataType[paramIndex];
      foreach (FieldInfo field in ActionData.GetFields(actionType))
      {
        ParamDataType paramDataType2 = ActionData.GetParamDataType(field.FieldType);
        if (field.Name == str && paramDataType2 == paramDataType1 && !ActionData.LoadedFields.Contains(field))
        {
          if (paramDataType2 != ParamDataType.Array)
            return field;
          System.Type elementType = field.GetType().GetElementType();
          if (elementType == null)
            return (FieldInfo) null;
          int num = this.arrayParamTypes[paramIndex] == elementType.FullName ? 1 : 0;
          return field;
        }
      }
      return (FieldInfo) null;
    }

    private static FieldInfo FindField(System.Type actionType, string name)
    {
      foreach (FieldInfo field in ActionData.GetFields(actionType))
      {
        if (field.Name == name && !ActionData.LoadedFields.Contains(field))
          return field;
      }
      return (FieldInfo) null;
    }

    private bool TryConvertParameter(
      ActionData.Context context,
      FsmStateAction action,
      FieldInfo field,
      int paramIndex)
    {
      System.Type fieldType = field.FieldType;
      ParamDataType paramDataType1 = this.paramDataType[paramIndex];
      ParamDataType paramDataType2 = ActionData.GetParamDataType(fieldType);
      if (paramDataType2 != ParamDataType.Array && paramDataType1 == paramDataType2)
        this.LoadActionField(context.currentFsm, (object) action, field, paramIndex);
      else if (paramDataType1 == ParamDataType.Enum && paramDataType2 == ParamDataType.FsmEnum)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from Enum to FsmEnum");
        object[] customAttributes = field.GetCustomAttributes(true);
        System.Type enumType = typeof (Enum);
        foreach (object obj in customAttributes)
        {
          if (obj is ObjectTypeAttribute objectTypeAttribute)
          {
            enumType = objectTypeAttribute.ObjectType;
            break;
          }
        }
        field.SetValue((object) action, (object) new FsmEnum("", enumType, FsmUtility.BitConverter.ToInt32(this.byteDataAsArray, this.paramDataPos[paramIndex])));
      }
      if (paramDataType1 == ParamDataType.String && paramDataType2 == ParamDataType.FsmString)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from string to FsmString");
        if (context.currentFsm.DataVersion > 1 && this.stringParams != null && this.stringParams.Count > 0)
          field.SetValue((object) action, (object) new FsmString()
          {
            Value = this.GetStringParam(paramIndex)
          });
        else
          field.SetValue((object) action, (object) new FsmString()
          {
            Value = FsmUtility.ByteArrayToString(this.byteDataAsArray, this.paramDataPos[paramIndex], this.paramByteDataSize[paramIndex])
          });
      }
      else if (paramDataType1 == ParamDataType.Integer && paramDataType2 == ParamDataType.FsmInt)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from int to FsmInt");
        field.SetValue((object) action, (object) new FsmInt()
        {
          Value = FsmUtility.BitConverter.ToInt32(this.byteDataAsArray, this.paramDataPos[paramIndex])
        });
      }
      else if (paramDataType1 == ParamDataType.Float && paramDataType2 == ParamDataType.FsmFloat)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from float to FsmFloat");
        field.SetValue((object) action, (object) new FsmFloat()
        {
          Value = FsmUtility.BitConverter.ToSingle(this.byteDataAsArray, this.paramDataPos[paramIndex])
        });
      }
      else if (paramDataType1 == ParamDataType.Boolean && paramDataType2 == ParamDataType.FsmBool)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from bool to FsmBool");
        field.SetValue((object) action, (object) new FsmBool()
        {
          Value = FsmUtility.BitConverter.ToBoolean(this.byteDataAsArray, this.paramDataPos[paramIndex])
        });
      }
      else if (paramDataType1 == ParamDataType.GameObject && paramDataType2 == ParamDataType.FsmGameObject)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from from GameObject to FsmGameObject");
        field.SetValue((object) action, (object) new FsmGameObject()
        {
          Value = (GameObject) this.unityObjectParams[this.paramDataPos[paramIndex]]
        });
      }
      else if (paramDataType1 == ParamDataType.GameObject && paramDataType2 == ParamDataType.FsmOwnerDefault)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from GameObject to FsmOwnerDefault");
        FsmOwnerDefault fsmOwnerDefault = new FsmOwnerDefault()
        {
          GameObject = new FsmGameObject()
          {
            Value = (GameObject) this.unityObjectParams[this.paramDataPos[paramIndex]]
          }
        };
        field.SetValue((object) action, (object) fsmOwnerDefault);
      }
      else if (paramDataType1 == ParamDataType.FsmGameObject && paramDataType2 == ParamDataType.FsmOwnerDefault)
      {
        ActionData.LogInfo(context, field.Name + ": Converted from FsmGameObject to FsmOwnerDefault");
        FsmOwnerDefault fsmOwnerDefault = new FsmOwnerDefault()
        {
          GameObject = this.fsmGameObjectParams[this.paramDataPos[paramIndex]],
          OwnerOption = OwnerDefaultOption.SpecifyGameObject
        };
        field.SetValue((object) action, (object) fsmOwnerDefault);
      }
      else if (paramDataType1 == ParamDataType.Vector3 && paramDataType2 == ParamDataType.FsmVector3)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from Vector3 to FsmVector3");
        field.SetValue((object) action, (object) new FsmVector3()
        {
          Value = FsmUtility.ByteArrayToVector3(this.byteDataAsArray, this.paramDataPos[paramIndex])
        });
      }
      else if (paramDataType1 == ParamDataType.Vector2 && paramDataType2 == ParamDataType.FsmVector2)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from Vector2 to FsmVector2");
        field.SetValue((object) action, (object) new FsmVector2()
        {
          Value = FsmUtility.ByteArrayToVector2(this.byteDataAsArray, this.paramDataPos[paramIndex])
        });
      }
      else if (paramDataType1 == ParamDataType.Rect && paramDataType2 == ParamDataType.FsmRect)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from Rect to FsmRect");
        field.SetValue((object) action, (object) new FsmRect()
        {
          Value = FsmUtility.ByteArrayToRect(this.byteDataAsArray, this.paramDataPos[paramIndex])
        });
      }
      else if (paramDataType1 == ParamDataType.Quaternion && paramDataType2 == ParamDataType.Quaternion)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from Quaternion to FsmQuaternion");
        field.SetValue((object) action, (object) new FsmQuaternion()
        {
          Value = FsmUtility.ByteArrayToQuaternion(this.byteDataAsArray, this.paramDataPos[paramIndex])
        });
      }
      else if (paramDataType1 == ParamDataType.Color && paramDataType2 == ParamDataType.FsmColor)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from Color to FsmColor");
        field.SetValue((object) action, (object) new FsmColor()
        {
          Value = FsmUtility.ByteArrayToColor(this.byteDataAsArray, this.paramDataPos[paramIndex])
        });
      }
      else if (paramDataType2 == ParamDataType.FsmMaterial && paramDataType1 == ParamDataType.ObjectReference)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from Material to FsmMaterial");
        field.SetValue((object) action, (object) new FsmMaterial()
        {
          Value = (this.unityObjectParams[this.paramDataPos[paramIndex]] as Material)
        });
      }
      else if (paramDataType2 == ParamDataType.FsmTexture && paramDataType1 == ParamDataType.ObjectReference)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from Texture to FsmTexture");
        field.SetValue((object) action, (object) new FsmTexture()
        {
          Value = (this.unityObjectParams[this.paramDataPos[paramIndex]] as Texture)
        });
      }
      else if (paramDataType2 == ParamDataType.FsmObject && paramDataType1 == ParamDataType.ObjectReference)
      {
        ActionData.LogInfo(context, field.Name + ": Upgraded from Object to FsmObject");
        field.SetValue((object) action, (object) new FsmObject()
        {
          Value = this.unityObjectParams[this.paramDataPos[paramIndex]]
        });
      }
      else
      {
        if (paramDataType2 != ParamDataType.Array)
          return false;
        System.Type globalType = ReflectionUtils.GetGlobalType(this.arrayParamTypes[this.paramDataPos[paramIndex]]);
        System.Type elementType = fieldType.GetElementType();
        if (elementType == null)
        {
          ActionData.LogError(context, "Could not make array: " + field.Name);
          return false;
        }
        int arrayParamSiz = this.arrayParamSizes[this.paramDataPos[paramIndex]];
        Array instance = Array.CreateInstance(elementType, arrayParamSiz);
        if (globalType != elementType)
        {
          ParamDataType paramDataType3 = ActionData.GetParamDataType(globalType);
          ParamDataType paramDataType4 = ActionData.GetParamDataType(elementType);
          for (int elementIndex = 0; elementIndex < arrayParamSiz; ++elementIndex)
          {
            ++this.nextParamIndex;
            if (!this.TryConvertArrayElement(context.currentFsm, instance, paramDataType3, paramDataType4, elementIndex, this.nextParamIndex))
            {
              ActionData.LogError(context, "Failed to convert Array: " + field.Name + " From: " + (object) paramDataType3 + " To: " + (object) paramDataType4);
              return false;
            }
            ActionData.LogInfo(context, field.Name + ": Upgraded Array from " + globalType.FullName + " to " + (object) paramDataType4);
          }
        }
        else
        {
          for (int elementIndex = 0; elementIndex < arrayParamSiz; ++elementIndex)
          {
            ++this.nextParamIndex;
            this.LoadArrayElement(context.currentFsm, instance, globalType, elementIndex, this.nextParamIndex);
          }
        }
        field.SetValue((object) action, (object) instance);
      }
      return true;
    }

    private bool TryConvertArrayElement(
      Fsm fsm,
      Array field,
      ParamDataType originalParamType,
      ParamDataType currentParamType,
      int elementIndex,
      int paramIndex)
    {
      if (elementIndex >= field.GetLength(0))
      {
        UnityEngine.Debug.LogError((object) ("Bad array index: " + (object) elementIndex));
        return false;
      }
      if (paramIndex >= this.paramDataPos.Count)
      {
        UnityEngine.Debug.LogError((object) ("Bad param index: " + (object) paramIndex));
        return false;
      }
      object obj = this.ConvertType(fsm, originalParamType, currentParamType, paramIndex);
      if (obj == null)
        return false;
      field.SetValue(obj, elementIndex);
      return true;
    }

    private object ConvertType(
      Fsm fsm,
      ParamDataType originalParamType,
      ParamDataType currentParamType,
      int paramIndex)
    {
      if (originalParamType == ParamDataType.String && currentParamType == ParamDataType.FsmString)
      {
        string str = fsm.DataVersion <= 1 || this.stringParams == null || this.stringParams.Count <= 0 ? FsmUtility.ByteArrayToString(this.byteDataAsArray, this.paramDataPos[paramIndex], this.paramByteDataSize[paramIndex]) : this.stringParams[this.paramDataPos[paramIndex]];
        return (object) new FsmString() { Value = str };
      }
      if (originalParamType == ParamDataType.Integer && currentParamType == ParamDataType.FsmInt)
        return (object) new FsmInt()
        {
          Value = FsmUtility.BitConverter.ToInt32(this.byteDataAsArray, this.paramDataPos[paramIndex])
        };
      if (originalParamType == ParamDataType.Float && currentParamType == ParamDataType.FsmFloat)
        return (object) new FsmFloat()
        {
          Value = FsmUtility.BitConverter.ToSingle(this.byteDataAsArray, this.paramDataPos[paramIndex])
        };
      if (originalParamType == ParamDataType.Boolean && currentParamType == ParamDataType.FsmBool)
        return (object) new FsmBool()
        {
          Value = FsmUtility.BitConverter.ToBoolean(this.byteDataAsArray, this.paramDataPos[paramIndex])
        };
      if (originalParamType == ParamDataType.GameObject && currentParamType == ParamDataType.FsmGameObject)
        return (object) new FsmGameObject()
        {
          Value = (GameObject) this.unityObjectParams[this.paramDataPos[paramIndex]]
        };
      if (originalParamType == ParamDataType.GameObject && currentParamType == ParamDataType.FsmOwnerDefault)
        return (object) new FsmOwnerDefault()
        {
          GameObject = new FsmGameObject()
          {
            Value = (GameObject) this.unityObjectParams[this.paramDataPos[paramIndex]]
          },
          OwnerOption = OwnerDefaultOption.SpecifyGameObject
        };
      if (originalParamType == ParamDataType.FsmGameObject && currentParamType == ParamDataType.FsmOwnerDefault)
        return (object) new FsmOwnerDefault()
        {
          GameObject = this.fsmGameObjectParams[this.paramDataPos[paramIndex]],
          OwnerOption = OwnerDefaultOption.SpecifyGameObject
        };
      if (originalParamType == ParamDataType.Vector2 && currentParamType == ParamDataType.FsmVector2)
        return (object) new FsmVector2()
        {
          Value = FsmUtility.ByteArrayToVector2(this.byteDataAsArray, this.paramDataPos[paramIndex])
        };
      if (originalParamType == ParamDataType.Vector3 && currentParamType == ParamDataType.FsmVector3)
        return (object) new FsmVector3()
        {
          Value = FsmUtility.ByteArrayToVector3(this.byteDataAsArray, this.paramDataPos[paramIndex])
        };
      if (originalParamType == ParamDataType.Rect && currentParamType == ParamDataType.FsmRect)
        return (object) new FsmRect()
        {
          Value = FsmUtility.ByteArrayToRect(this.byteDataAsArray, this.paramDataPos[paramIndex])
        };
      if (originalParamType == ParamDataType.Quaternion && currentParamType == ParamDataType.Quaternion)
        return (object) new FsmQuaternion()
        {
          Value = FsmUtility.ByteArrayToQuaternion(this.byteDataAsArray, this.paramDataPos[paramIndex])
        };
      if (originalParamType == ParamDataType.Color && currentParamType == ParamDataType.FsmColor)
        return (object) new FsmColor()
        {
          Value = FsmUtility.ByteArrayToColor(this.byteDataAsArray, this.paramDataPos[paramIndex])
        };
      if (currentParamType == ParamDataType.FsmMaterial && originalParamType == ParamDataType.ObjectReference)
        return (object) new FsmMaterial()
        {
          Value = (this.unityObjectParams[this.paramDataPos[paramIndex]] as Material)
        };
      if (currentParamType == ParamDataType.FsmTexture && originalParamType == ParamDataType.ObjectReference)
        return (object) new FsmTexture()
        {
          Value = (this.unityObjectParams[this.paramDataPos[paramIndex]] as Texture)
        };
      if (currentParamType != ParamDataType.FsmObject || originalParamType != ParamDataType.ObjectReference)
        return (object) null;
      return (object) new FsmObject()
      {
        Value = this.unityObjectParams[this.paramDataPos[paramIndex]]
      };
    }

    public void SaveActions(FsmState state, FsmStateAction[] actions)
    {
      this.ClearActionData();
      foreach (FsmStateAction action in actions)
        this.SaveAction(state.Fsm, action);
    }

    private void SaveAction(Fsm fsm, FsmStateAction action)
    {
      if (action == null)
        return;
      System.Type type = action.GetType();
      ActionData.ActionHashCodeLookup.Remove(type);
      this.actionNames.Add(type.ToString());
      this.customNames.Add(action.IsAutoNamed ? "~AutoName" : action.Name);
      this.actionEnabled.Add(action.Enabled);
      this.actionIsOpen.Add(action.IsOpen);
      this.actionStartIndex.Add(this.nextParamIndex);
      this.actionHashCodes.Add(ActionData.GetActionTypeHashCode(type));
      foreach (FieldInfo field in ActionData.GetFields(type))
      {
        System.Type fieldType = field.FieldType;
        object obj = field.GetValue((object) action);
        this.paramName.Add(field.Name);
        this.SaveActionField(fsm, fieldType, obj);
        ++this.nextParamIndex;
      }
    }

    private void SaveActionField(Fsm fsm, System.Type fieldType, object obj)
    {
      if (fieldType == typeof (FsmAnimationCurve))
      {
        if (this.animationCurveParams == null)
          this.animationCurveParams = new List<FsmAnimationCurve>();
        this.paramDataType.Add(ParamDataType.FsmAnimationCurve);
        this.paramDataPos.Add(this.animationCurveParams.Count);
        this.paramByteDataSize.Add(0);
        this.animationCurveParams.Add(obj as FsmAnimationCurve);
      }
      else if (typeof (UnityEngine.Object).IsAssignableFrom(fieldType))
      {
        if (this.unityObjectParams == null)
          this.unityObjectParams = new List<UnityEngine.Object>();
        this.paramDataType.Add(fieldType == typeof (GameObject) ? ParamDataType.GameObject : ParamDataType.ObjectReference);
        this.paramDataPos.Add(this.unityObjectParams.Count);
        this.paramByteDataSize.Add(0);
        this.unityObjectParams.Add(obj as UnityEngine.Object);
      }
      else if (fieldType == typeof (FunctionCall))
      {
        if (this.functionCallParams == null)
          this.functionCallParams = new List<FunctionCall>();
        this.paramDataType.Add(ParamDataType.FunctionCall);
        this.paramDataPos.Add(this.functionCallParams.Count);
        this.paramByteDataSize.Add(0);
        this.functionCallParams.Add(obj as FunctionCall);
      }
      else if (fieldType == typeof (FsmTemplateControl))
      {
        if (this.fsmTemplateControlParams == null)
          this.fsmTemplateControlParams = new List<FsmTemplateControl>();
        this.paramDataType.Add(ParamDataType.FsmTemplateControl);
        this.paramDataPos.Add(this.fsmTemplateControlParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmTemplateControlParams.Add(obj as FsmTemplateControl);
      }
      else if (fieldType == typeof (FsmVar))
      {
        if (this.fsmVarParams == null)
          this.fsmVarParams = new List<FsmVar>();
        this.paramDataType.Add(ParamDataType.FsmVar);
        this.paramDataPos.Add(this.fsmVarParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmVarParams.Add(obj as FsmVar);
      }
      else if (fieldType == typeof (FsmProperty))
      {
        if (this.fsmPropertyParams == null)
          this.fsmPropertyParams = new List<FsmProperty>();
        this.paramDataType.Add(ParamDataType.FsmProperty);
        this.paramDataPos.Add(this.fsmPropertyParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmPropertyParams.Add(obj as FsmProperty);
      }
      else if (fieldType == typeof (FsmEventTarget))
      {
        if (this.fsmEventTargetParams == null)
          this.fsmEventTargetParams = new List<FsmEventTarget>();
        this.paramDataType.Add(ParamDataType.FsmEventTarget);
        this.paramDataPos.Add(this.fsmEventTargetParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmEventTargetParams.Add(obj as FsmEventTarget);
      }
      else if (fieldType == typeof (LayoutOption))
      {
        if (this.layoutOptionParams == null)
          this.layoutOptionParams = new List<LayoutOption>();
        this.paramDataType.Add(ParamDataType.LayoutOption);
        this.paramDataPos.Add(this.layoutOptionParams.Count);
        this.paramByteDataSize.Add(0);
        this.layoutOptionParams.Add(obj as LayoutOption);
      }
      else if (fieldType == typeof (FsmGameObject))
      {
        if (this.fsmGameObjectParams == null)
          this.fsmGameObjectParams = new List<FsmGameObject>();
        this.paramDataType.Add(ParamDataType.FsmGameObject);
        this.paramDataPos.Add(this.fsmGameObjectParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmGameObjectParams.Add(obj as FsmGameObject);
      }
      else if (fieldType == typeof (FsmOwnerDefault))
      {
        if (this.fsmOwnerDefaultParams == null)
          this.fsmOwnerDefaultParams = new List<FsmOwnerDefault>();
        this.paramDataType.Add(ParamDataType.FsmOwnerDefault);
        this.paramDataPos.Add(this.fsmOwnerDefaultParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmOwnerDefaultParams.Add(obj as FsmOwnerDefault);
      }
      else if (fieldType == typeof (FsmString))
      {
        if (this.fsmStringParams == null)
          this.fsmStringParams = new List<FsmString>();
        this.paramDataType.Add(ParamDataType.FsmString);
        this.paramDataPos.Add(this.fsmStringParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmStringParams.Add(obj as FsmString);
      }
      else if (fieldType.IsArray)
      {
        System.Type elementType = fieldType.GetElementType();
        if (elementType == null)
          return;
        Array array = obj == null ? Array.CreateInstance(elementType, 0) : (Array) obj;
        if (this.arrayParamSizes == null)
        {
          this.arrayParamSizes = new List<int>();
          this.arrayParamTypes = new List<string>();
        }
        this.paramDataType.Add(ParamDataType.Array);
        this.paramDataPos.Add(this.arrayParamSizes.Count);
        this.paramByteDataSize.Add(0);
        this.arrayParamSizes.Add(array.Length);
        this.arrayParamTypes.Add(elementType.FullName);
        foreach (object obj1 in array)
        {
          ++this.nextParamIndex;
          this.paramName.Add("");
          this.SaveActionField(fsm, elementType, obj1);
        }
      }
      else if (fieldType == typeof (float))
      {
        this.paramDataType.Add(ParamDataType.Float);
        this.AddByteData((ICollection<byte>) FsmUtility.BitConverter.GetBytes((float) obj));
      }
      else if (fieldType == typeof (int))
      {
        this.paramDataType.Add(ParamDataType.Integer);
        this.AddByteData((ICollection<byte>) FsmUtility.BitConverter.GetBytes((int) obj));
      }
      else if (fieldType == typeof (bool))
      {
        this.paramDataType.Add(ParamDataType.Boolean);
        this.AddByteData((ICollection<byte>) FsmUtility.BitConverter.GetBytes((bool) obj));
      }
      else if (fieldType == typeof (Color))
      {
        this.paramDataType.Add(ParamDataType.Color);
        this.AddByteData(FsmUtility.ColorToByteArray((Color) obj));
      }
      else if (fieldType == typeof (Vector2))
      {
        this.paramDataType.Add(ParamDataType.Vector2);
        this.AddByteData(FsmUtility.Vector2ToByteArray((Vector2) obj));
      }
      else if (fieldType == typeof (Vector3))
      {
        this.paramDataType.Add(ParamDataType.Vector3);
        this.AddByteData(FsmUtility.Vector3ToByteArray((Vector3) obj));
      }
      else if (fieldType == typeof (Vector4))
      {
        this.paramDataType.Add(ParamDataType.Vector4);
        this.AddByteData(FsmUtility.Vector4ToByteArray((Vector4) obj));
      }
      else if (fieldType == typeof (Rect))
      {
        this.paramDataType.Add(ParamDataType.Rect);
        this.AddByteData(FsmUtility.RectToByteArray((Rect) obj));
      }
      else if (fieldType == typeof (FsmFloat))
      {
        this.paramDataType.Add(ParamDataType.FsmFloat);
        if (fsm.DataVersion > 1)
        {
          if (this.fsmFloatParams == null)
            this.fsmFloatParams = new List<FsmFloat>();
          this.paramDataPos.Add(this.fsmFloatParams.Count);
          this.paramByteDataSize.Add(0);
          this.fsmFloatParams.Add(obj as FsmFloat);
        }
        else
          this.AddByteData(FsmUtility.FsmFloatToByteArray(obj as FsmFloat));
      }
      else if (fieldType == typeof (FsmInt))
      {
        this.paramDataType.Add(ParamDataType.FsmInt);
        if (fsm.DataVersion > 1)
        {
          if (this.fsmIntParams == null)
            this.fsmIntParams = new List<FsmInt>();
          this.paramDataPos.Add(this.fsmIntParams.Count);
          this.paramByteDataSize.Add(0);
          this.fsmIntParams.Add(obj as FsmInt);
        }
        else
          this.AddByteData(FsmUtility.FsmIntToByteArray(obj as FsmInt));
      }
      else if (fieldType == typeof (FsmBool))
      {
        this.paramDataType.Add(ParamDataType.FsmBool);
        if (fsm.DataVersion > 1)
        {
          if (this.fsmBoolParams == null)
            this.fsmBoolParams = new List<FsmBool>();
          this.paramDataPos.Add(this.fsmBoolParams.Count);
          this.paramByteDataSize.Add(0);
          this.fsmBoolParams.Add(obj as FsmBool);
        }
        else
          this.AddByteData(FsmUtility.FsmBoolToByteArray(obj as FsmBool));
      }
      else if (fieldType == typeof (FsmVector2))
      {
        this.paramDataType.Add(ParamDataType.FsmVector2);
        if (fsm.DataVersion > 1)
        {
          if (this.fsmVector2Params == null)
            this.fsmVector2Params = new List<FsmVector2>();
          this.paramDataPos.Add(this.fsmVector2Params.Count);
          this.paramByteDataSize.Add(0);
          this.fsmVector2Params.Add(obj as FsmVector2);
        }
        else
          this.AddByteData(FsmUtility.FsmVector2ToByteArray(obj as FsmVector2));
      }
      else if (fieldType == typeof (FsmVector3))
      {
        this.paramDataType.Add(ParamDataType.FsmVector3);
        if (fsm.DataVersion > 1)
        {
          if (this.fsmVector3Params == null)
            this.fsmVector3Params = new List<FsmVector3>();
          this.paramDataPos.Add(this.fsmVector3Params.Count);
          this.paramByteDataSize.Add(0);
          this.fsmVector3Params.Add(obj as FsmVector3);
        }
        else
          this.AddByteData(FsmUtility.FsmVector3ToByteArray(obj as FsmVector3));
      }
      else if (fieldType == typeof (FsmRect))
      {
        this.paramDataType.Add(ParamDataType.FsmRect);
        if (fsm.DataVersion > 1)
        {
          if (this.fsmRectParams == null)
            this.fsmRectParams = new List<FsmRect>();
          this.paramDataPos.Add(this.fsmRectParams.Count);
          this.paramByteDataSize.Add(0);
          this.fsmRectParams.Add(obj as FsmRect);
        }
        else
          this.AddByteData(FsmUtility.FsmRectToByteArray(obj as FsmRect));
      }
      else if (fieldType == typeof (FsmQuaternion))
      {
        this.paramDataType.Add(ParamDataType.FsmQuaternion);
        if (fsm.DataVersion > 1)
        {
          if (this.fsmQuaternionParams == null)
            this.fsmQuaternionParams = new List<FsmQuaternion>();
          this.paramDataPos.Add(this.fsmQuaternionParams.Count);
          this.paramByteDataSize.Add(0);
          this.fsmQuaternionParams.Add(obj as FsmQuaternion);
        }
        else
          this.AddByteData(FsmUtility.FsmQuaternionToByteArray(obj as FsmQuaternion));
      }
      else if (fieldType == typeof (FsmColor))
      {
        this.paramDataType.Add(ParamDataType.FsmColor);
        if (fsm.DataVersion > 1)
        {
          if (this.fsmColorParams == null)
            this.fsmColorParams = new List<FsmColor>();
          this.paramDataPos.Add(this.fsmColorParams.Count);
          this.paramByteDataSize.Add(0);
          this.fsmColorParams.Add(obj as FsmColor);
        }
        else
          this.AddByteData(FsmUtility.FsmColorToByteArray(obj as FsmColor));
      }
      else if (fieldType == typeof (FsmEvent))
      {
        this.paramDataType.Add(ParamDataType.FsmEvent);
        if (fsm.DataVersion > 1)
          this.SaveString(obj != null ? ((FsmEvent) obj).Name : string.Empty);
        else
          this.AddByteData(FsmUtility.FsmEventToByteArray(obj as FsmEvent));
      }
      else if (fieldType == typeof (string))
      {
        this.paramDataType.Add(ParamDataType.String);
        if (fsm.DataVersion > 1)
          this.SaveString(obj as string);
        else
          this.AddByteData((ICollection<byte>) FsmUtility.StringToByteArray(obj as string));
      }
      else if (fieldType == typeof (FsmObject))
      {
        if (this.fsmObjectParams == null)
          this.fsmObjectParams = new List<FsmObject>();
        this.paramDataType.Add(ParamDataType.FsmObject);
        this.paramDataPos.Add(this.fsmObjectParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmObjectParams.Add(obj as FsmObject);
      }
      else if (fieldType == typeof (FsmArray))
      {
        if (this.fsmArrayParams == null)
          this.fsmArrayParams = new List<FsmArray>();
        this.paramDataType.Add(ParamDataType.FsmArray);
        this.paramDataPos.Add(this.fsmArrayParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmArrayParams.Add(obj as FsmArray);
      }
      else if (fieldType == typeof (FsmEnum))
      {
        if (this.fsmEnumParams == null)
          this.fsmEnumParams = new List<FsmEnum>();
        this.paramDataType.Add(ParamDataType.FsmEnum);
        this.paramDataPos.Add(this.fsmEnumParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmEnumParams.Add(obj as FsmEnum);
      }
      else if (fieldType == typeof (FsmMaterial))
      {
        if (this.fsmObjectParams == null)
          this.fsmObjectParams = new List<FsmObject>();
        this.paramDataType.Add(ParamDataType.FsmMaterial);
        this.paramDataPos.Add(this.fsmObjectParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmObjectParams.Add(obj as FsmObject);
      }
      else if (fieldType == typeof (FsmTexture))
      {
        if (this.fsmObjectParams == null)
          this.fsmObjectParams = new List<FsmObject>();
        this.paramDataType.Add(ParamDataType.FsmTexture);
        this.paramDataPos.Add(this.fsmObjectParams.Count);
        this.paramByteDataSize.Add(0);
        this.fsmObjectParams.Add(obj as FsmObject);
      }
      else if (fieldType.IsEnum)
      {
        this.paramDataType.Add(ParamDataType.Enum);
        this.AddByteData((ICollection<byte>) FsmUtility.BitConverter.GetBytes(Convert.ToInt32(obj)));
      }
      else if (fieldType.IsClass)
      {
        if (this.customTypeSizes == null)
        {
          this.customTypeSizes = new List<int>();
          this.customTypeNames = new List<string>();
        }
        if (obj == null)
          obj = Activator.CreateInstance(fieldType);
        this.paramDataType.Add(ParamDataType.CustomClass);
        this.paramDataPos.Add(this.customTypeSizes.Count);
        this.customTypeNames.Add(fieldType.FullName);
        this.paramByteDataSize.Add(0);
        FieldInfo[] fields = ActionData.GetFields(fieldType);
        this.customTypeSizes.Add(fields.Length);
        foreach (FieldInfo fieldInfo in fields)
        {
          ++this.nextParamIndex;
          this.paramName.Add(fieldInfo.Name);
          this.SaveActionField(fsm, fieldInfo.FieldType, fieldInfo.GetValue(obj));
        }
      }
      else if (obj != null)
      {
        UnityEngine.Debug.LogError((object) ("Save Action: Unsupported parameter type: " + (object) fieldType));
        this.paramDataType.Add(ParamDataType.Unsupported);
        this.paramDataPos.Add(this.byteData.Count);
        this.paramByteDataSize.Add(0);
      }
      else
      {
        this.paramDataType.Add(ParamDataType.Unsupported);
        this.paramDataPos.Add(this.byteData.Count);
        this.paramByteDataSize.Add(0);
      }
    }

    private void AddByteData(ICollection<byte> bytes)
    {
      this.paramDataPos.Add(this.byteData.Count);
      if (bytes != null)
      {
        this.paramByteDataSize.Add(bytes.Count);
        this.byteData.AddRange((IEnumerable<byte>) bytes);
      }
      else
        this.paramByteDataSize.Add(0);
    }

    private void SaveString(string str)
    {
      if (this.stringParams == null)
        this.stringParams = new List<string>();
      this.paramDataPos.Add(this.stringParams.Count);
      this.paramByteDataSize.Add(0);
      this.stringParams.Add(str ?? string.Empty);
    }

    private static ParamDataType GetParamDataType(System.Type type)
    {
      if (type == typeof (FsmOwnerDefault))
        return ParamDataType.FsmOwnerDefault;
      if (type == typeof (FsmEventTarget))
        return ParamDataType.FsmEventTarget;
      if (type == typeof (FsmEvent))
        return ParamDataType.FsmEvent;
      if (type == typeof (FsmFloat))
        return ParamDataType.FsmFloat;
      if (type == typeof (FsmInt))
        return ParamDataType.FsmInt;
      if (type == typeof (FsmBool))
        return ParamDataType.FsmBool;
      if (type == typeof (FsmString))
        return ParamDataType.FsmString;
      if (type == typeof (FsmGameObject))
        return ParamDataType.FsmGameObject;
      if (type == typeof (FunctionCall))
        return ParamDataType.FunctionCall;
      if (type == typeof (FsmProperty))
        return ParamDataType.FsmProperty;
      if (type == typeof (FsmVector2))
        return ParamDataType.FsmVector2;
      if (type == typeof (FsmVector3))
        return ParamDataType.FsmVector3;
      if (type == typeof (FsmRect))
        return ParamDataType.FsmRect;
      if (type == typeof (FsmQuaternion))
        return ParamDataType.FsmQuaternion;
      if (type == typeof (FsmObject))
        return ParamDataType.FsmObject;
      if (type == typeof (FsmMaterial))
        return ParamDataType.FsmMaterial;
      if (type == typeof (FsmTexture))
        return ParamDataType.FsmTexture;
      if (type == typeof (FsmColor))
        return ParamDataType.FsmColor;
      if (type == typeof (int))
        return ParamDataType.Integer;
      if (type == typeof (bool))
        return ParamDataType.Boolean;
      if (type == typeof (float))
        return ParamDataType.Float;
      if (type == typeof (string))
        return ParamDataType.String;
      if (type == typeof (Color))
        return ParamDataType.Color;
      if (type == typeof (LayerMask))
        return ParamDataType.LayerMask;
      if (type == typeof (Vector2))
        return ParamDataType.Vector2;
      if (type == typeof (Vector3))
        return ParamDataType.Vector3;
      if (type == typeof (Vector4))
        return ParamDataType.Vector4;
      if (type == typeof (Quaternion))
        return ParamDataType.Quaternion;
      if (type == typeof (Rect))
        return ParamDataType.Rect;
      if (type == typeof (AnimationCurve))
        return ParamDataType.AnimationCurve;
      if (type == typeof (GameObject))
        return ParamDataType.GameObject;
      if (type == typeof (LayoutOption))
        return ParamDataType.LayoutOption;
      if (type == typeof (FsmVar))
        return ParamDataType.FsmVar;
      if (type == typeof (FsmEnum))
        return ParamDataType.FsmEnum;
      if (type == typeof (FsmArray))
        return ParamDataType.FsmArray;
      if (type == typeof (FsmTemplateControl))
        return ParamDataType.FsmTemplateControl;
      if (type == typeof (FsmAnimationCurve))
        return ParamDataType.FsmAnimationCurve;
      if (type == typeof(FsmSValue))
        return ParamDataType.FsmSValue;
      if (type.IsArray)
        return ParamDataType.Array;
      if (type.IsSubclassOf(typeof (UnityEngine.Object)))
        return ParamDataType.ObjectReference;
      if (type.IsEnum)
        return ParamDataType.Enum;
      return type.IsClass ? ParamDataType.CustomClass : ParamDataType.Unsupported;
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLogWarning(object message)
    {
    }

    public class Context
    {
      public Fsm currentFsm;
      public FsmState currentState;
      public FsmStateAction currentAction;
      public int currentActionIndex;
      public string currentParameter;

      public override string ToString() => FsmUtility.GetFullFsmLabel(this.currentFsm) + ": " + (this.currentState != null ? this.currentState.Name : "Missing State") + ": " + (this.currentAction != null ? this.currentAction.Name : "Missing Action") + ": " + (object) this.currentActionIndex + ": " + this.currentParameter;
    }
  }
}
