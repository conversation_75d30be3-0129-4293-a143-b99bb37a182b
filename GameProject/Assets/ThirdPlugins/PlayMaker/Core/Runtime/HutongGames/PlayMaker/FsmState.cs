// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmState
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using System.Collections.Generic;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public partial class FsmState : INameable
  {
    private bool active;
    private bool finished;
    private FsmStateAction activeAction;
    private int activeActionIndex;
    [NonSerialized]
    private Fsm fsm;
    [SerializeField]
    private string name;
    [SerializeField]
    [TextArea(0, 5)]
    private string description;
    [SerializeField] 
    private string guideStepID;
    [SerializeField]
    private byte colorIndex;
    [SerializeField]
    private Rect position;
    [SerializeField]
    private bool isBreakpoint;
    [SerializeField]
    private bool isSequence = true;
    [SerializeField]
    private bool hideUnused;
    [SerializeField]
    private FsmTransition[] transitions = new FsmTransition[0];
    [NonSerialized]
    private FsmStateAction[] actions;
    [SerializeField]
    private ActionData actionData = new ActionData();
    [NonSerialized]
    public bool HandlesOnEvent;
    [NonSerialized]
    private List<FsmStateAction> activeActions;
    [NonSerialized]
    private List<FsmStateAction> _finishedActions;

    public float StateTime { get; private set; }

    public float StateRealTime => FsmTime.RealtimeSinceStartup - this.RealStartTime;

    public float RealStartTime { get; private set; }

    public int loopCount { get; private set; }

    public int maxLoopCount { get; private set; }

    public bool HasErrors { get; set; }

    public static string GetFullStateLabel(FsmState state) => state == null ? "None (State)" : Fsm.GetFullFsmLabel(state.Fsm) + " : " + state.Name;

    public FsmState(Fsm fsm) => this.fsm = fsm;

    public FsmState(FsmState source)
    {
      this.fsm = source.Fsm;
      this.name = source.Name;
      this.description = source.description;
      this.colorIndex = source.colorIndex;
      this.position = new Rect(source.position);
      this.hideUnused = source.hideUnused;
      this.isBreakpoint = source.isBreakpoint;
      this.isSequence = source.isSequence;
      this.transitions = new FsmTransition[source.transitions.Length];
      for (int index = 0; index < source.Transitions.Length; ++index)
        this.transitions[index] = new FsmTransition(source.Transitions[index]);
      this.actionData = source.actionData.Copy();
    }

    public void CopyActionData(FsmState state) => this.actionData = state.actionData.Copy();

    public void LoadActions() => this.actions = this.actionData.LoadActions(this);

    public void SaveActions()
    {
      if (this.actions == null)
        return;
      this.actionData.SaveActions(this, this.actions);
    }

    public List<FsmStateAction> ActiveActions => this.activeActions ?? (this.activeActions = new List<FsmStateAction>());

    private List<FsmStateAction> finishedActions => this._finishedActions ?? (this._finishedActions = new List<FsmStateAction>());

    public void OnEnter()
    {
      ++this.loopCount;
      if (this.loopCount > this.maxLoopCount)
        this.maxLoopCount = this.loopCount;
      this.active = true;
      this.finished = false;
      this.finishedActions.Clear();
      this.RealStartTime = FsmTime.RealtimeSinceStartup;
      this.StateTime = 0.0f;
      this.ActiveActions.Clear();
      if (!this.ActivateActions(0))
        return;
      this.CheckAllActionsFinished();
    }

    private bool ActivateActions(int startIndex)
    {
      for (int index = startIndex; index < this.Actions.Length; ++index)
      {
        this.activeActionIndex = index;
        FsmStateAction action = this.Actions[index];
        if (!action.Enabled)
        {
          action.Finished = true;
        }
        else
        {
          this.activeAction = action;
          action.Index = index;
          action.Active = true;
          action.Finished = false;
          action.Init(this);
          action.Entered = true;
          action.OnEnter();
          if (!action.Finished)
            this.ActiveActions.Add(action);
          if (this.Fsm.IsSwitchingState || !action.Finished && this.isSequence)
            return false;
        }
      }
      return true;
    }

    public bool OnEvent(FsmEvent fsmEvent)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
      {
        FsmStateAction activeAction = this.ActiveActions[index];
        if (activeAction != null && activeAction.Event(fsmEvent))
          return true;
      }
      return this.fsm.IsSwitchingState;
    }

    public void OnFixedUpdate()
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].OnFixedUpdate();
      this.CheckAllActionsFinished();
    }

    public void OnUpdate()
    {
      if (this.finished && this.ActiveActions.Count == 0)
        return;
      this.StateTime += Time.deltaTime;
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].OnUpdate();
      this.CheckAllActionsFinished();
    }

    public void OnLateUpdate()
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].OnLateUpdate();
      this.CheckAllActionsFinished();
    }

    public bool OnAnimatorMove()
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoAnimatorMove();
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnAnimatorIK(int layerIndex)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoAnimatorIK(layerIndex);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnCollisionEnter(Collision collisionInfo)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoCollisionEnter(collisionInfo);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnCollisionStay(Collision collisionInfo)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoCollisionStay(collisionInfo);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnCollisionExit(Collision collisionInfo)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoCollisionExit(collisionInfo);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnTriggerEnter(Collider other)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoTriggerEnter(other);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnTriggerStay(Collider other)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoTriggerStay(other);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnTriggerExit(Collider other)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoTriggerExit(other);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnParticleCollision(GameObject other)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoParticleCollision(other);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnCollisionEnter2D(Collision2D collisionInfo)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoCollisionEnter2D(collisionInfo);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnCollisionStay2D(Collision2D collisionInfo)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoCollisionStay2D(collisionInfo);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnCollisionExit2D(Collision2D collisionInfo)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoCollisionExit2D(collisionInfo);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnTriggerEnter2D(Collider2D other)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoTriggerEnter2D(other);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnTriggerStay2D(Collider2D other)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoTriggerStay2D(other);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnTriggerExit2D(Collider2D other)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoTriggerExit2D(other);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnControllerColliderHit(ControllerColliderHit collider)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoControllerColliderHit(collider);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnJointBreak(float force)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoJointBreak(force);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public bool OnJointBreak2D(Joint2D joint)
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].DoJointBreak2D(joint);
      this.RemoveFinishedActions();
      return this.fsm.IsSwitchingState;
    }

    public void OnGUI()
    {
      for (int index = 0; index < this.ActiveActions.Count; ++index)
        this.ActiveActions[index].OnGUI();
      this.RemoveFinishedActions();
    }

    public void FinishAction(FsmStateAction action) => this.finishedActions.Add(action);

    private void RemoveFinishedActions()
    {
      for (int index = 0; index < this.finishedActions.Count; ++index)
        this.ActiveActions.Remove(this.finishedActions[index]);
      this.finishedActions.Clear();
    }

    private void CheckAllActionsFinished()
    {
      if (this.finished || !this.active || this.fsm.IsSwitchingState)
        return;
      this.RemoveFinishedActions();
      if (this.ActiveActions.Count == 0)
      {
        if (this.isSequence && (++this.activeActionIndex < this.actions.Length && !this.ActivateActions(this.activeActionIndex)))
          return;
        this.finished = true;
        this.fsm.Event(FsmEventTarget.TargetSelf, FsmEvent.Finished);
      }
      else
      {
        for (int index = 0; index < this.ActiveActions.Count; ++index)
        {
          if (this.ActiveActions[index].BlocksFinish)
            return;
        }
        this.finished = true;
        this.fsm.Event(FsmEventTarget.TargetSelf, FsmEvent.Finished);
      }
    }

    public void OnExit()
    {
      this.active = false;
      this.finished = false;
      foreach (FsmStateAction action in this.Actions)
      {
        if (action.Entered)
        {
          this.activeAction = action;
          action.OnExit();
        }
      }
    }

    public void ResetLoopCount() => this.loopCount = 0;

    public FsmTransition GetTransition(int transitionIndex) => transitionIndex < 0 || transitionIndex > this.transitions.Length - 1 ? (FsmTransition) null : this.transitions[transitionIndex];

    public FsmEvent GetTransitionEvent(int transitionIndex) => this.GetTransition(transitionIndex)?.FsmEvent;

    public int GetTransitionIndex(FsmTransition transition)
    {
      if (transition == null)
        return -1;
      for (int index = 0; index < this.transitions.Length; ++index)
      {
        if (this.transitions[index] == transition)
          return index;
      }
      return -1;
    }

    public bool Active => this.active;

    public FsmStateAction ActiveAction => this.activeAction;

    public bool IsInitialized => this.fsm != null;

    public Fsm Fsm
    {
      get
      {
        if (this.fsm == null)
          Debug.LogError((object) ("get_fsm: Fsm not initialized: " + this.name));
        return this.fsm;
      }
      set
      {
        if (value == null)
          Debug.LogWarning((object) ("set_fsm: value == null: " + this.name));
        this.fsm = value;
      }
    }

    public string Name
    {
      get => this.name;
      set => this.name = value;
    }

    public bool IsSequence
    {
      get => this.isSequence;
      set => this.isSequence = value;
    }

    public int ActiveActionIndex => this.activeActionIndex;

    public Rect Position
    {
      get => this.position;
      set
      {
        if (float.IsNaN(value.x) || float.IsNaN(value.y))
          return;
        this.position = value;
      }
    }

    public bool IsBreakpoint
    {
      get => this.isBreakpoint;
      set => this.isBreakpoint = value;
    }

    public bool HideUnused
    {
      get => this.hideUnused;
      set => this.hideUnused = value;
    }

    public FsmStateAction[] Actions
    {
      get
      {
        if (this.fsm == null)
          Debug.LogError((object) ("get_actions: Fsm not initialized: " + this.name));
        return this.actions ?? (this.actions = this.actionData.LoadActions(this));
      }
      set => this.actions = value;
    }

    public bool ActionsLoaded => this.actions != null;

    public ActionData ActionData => this.actionData;

    public FsmTransition[] Transitions
    {
      get => this.transitions;
      set => this.transitions = value;
    }

    public string Description
    {
      get => this.description ?? (this.description = "");
      set => this.description = value;
    }
    
    public string GuideStepID 
    {
      get => this.guideStepID ?? (this.guideStepID = "");
      set => this.guideStepID = value;
    }

    public int ColorIndex
    {
      get => (int) this.colorIndex;
      set => this.colorIndex = (byte) value;
    }

    public static int GetStateIndex(FsmState state)
    {
      if (state.Fsm == null)
        return -1;
      for (int index = 0; index < state.Fsm.States.Length; ++index)
      {
        if (state.Fsm.States[index] == state)
          return index;
      }
      Debug.LogError((object) "State not in FSM!");
      return -1;
    }

    public bool HasTransition(FsmTransition transition)
    {
      foreach (FsmTransition transition1 in this.transitions)
      {
        if (transition1 == transition)
          return true;
      }
      return false;
    }
    
    public bool HasFinishedTransition()
    {
      foreach (FsmTransition transition in this.transitions)
      {
        if (transition.FsmEvent.Name == FsmEvent.Finished.Name)
          return true;
      }
      return false;
    }

    public List<FsmTransition> GetGlobalTransitions() => this.fsm.GetGlobalTransitionsToState(this);

    public bool HasGlobalTransition(string eventName)
    {
      var transitions = GetGlobalTransitions();
      foreach (var transition in transitions)
      {
        if (transition.FsmEvent.IsGlobal && transition.FsmEvent.Name == eventName)
          return true;
      }
      return false;
    }

    public bool IsPrepareState() => HasGlobalTransition("PREPARE");
    public bool IsCleanupState() => HasGlobalTransition("CLEANUP");
  }
}
