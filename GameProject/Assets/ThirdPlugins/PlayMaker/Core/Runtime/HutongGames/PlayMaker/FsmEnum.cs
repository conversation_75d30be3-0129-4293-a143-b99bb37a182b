// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmEnum
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using System.Globalization;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmEnum : NamedVariable
  {
    [SerializeField]
    private string enumName;
    [SerializeField]
    private int intValue;
    private Enum value;
    private int parsedIntValue = -1;
    private System.Type enumType;

    public override object RawValue
    {
      get => (object) this.Value;
      set => this.Value = (Enum) value;
    }

    public System.Type EnumType
    {
      get
      {
        if (this.enumType == null || this.enumType.IsAbstract || !this.enumType.IsEnum)
          this.InitEnumType();
        return this.enumType;
      }
      set
      {
        if (this.enumType == null || this.enumType.IsAbstract || !this.enumType.IsEnum)
          this.InitEnumType();
        if (this.enumType == value)
          return;
        this.enumType = value ?? typeof (None);
        this.enumName = this.enumType.FullName;
        this.InitEnumType();
        this.Value = (Enum) Activator.CreateInstance(this.enumType);
      }
    }

    public override void Init() => this.InitEnumType();

    private void InitEnumType()
    {
      this.enumType = ReflectionUtils.GetGlobalType(this.enumName);
      if (this.enumType != null && !this.enumType.IsAbstract && this.enumType.IsEnum)
        return;
      this.enumType = typeof (None);
      this.EnumName = this.enumType.FullName;
    }

    public string EnumName
    {
      get => this.enumName;
      set => this.enumName = value;
    }

    public Enum Value
    {
      get
      {
        if (this.parsedIntValue != this.intValue)
          this.value = (Enum) null;
        if (this.value == null)
        {
          this.value = (Enum) Enum.Parse(this.EnumType, this.intValue.ToString((IFormatProvider) CultureInfo.InvariantCulture));
          this.parsedIntValue = this.intValue;
        }
        return this.value;
      }
      set
      {
        this.value = value;
        this.intValue = Convert.ToInt32((object) value);
      }
    }

    public void ResetValue() => this.value = (Enum) Enum.Parse(this.EnumType, this.intValue.ToString((IFormatProvider) CultureInfo.InvariantCulture));

    public FsmEnum()
    {
    }

    public FsmEnum(string name, System.Type enumType, int intValue)
      : base(name)
    {
      this.EnumType = enumType;
      this.Value = (Enum) Enum.Parse(this.EnumType, intValue.ToString((IFormatProvider) CultureInfo.InvariantCulture));
    }

    public FsmEnum(string name)
      : base(name)
    {
      this.enumName = typeof (Enum).FullName;
      this.enumType = typeof (Enum);
    }

    public FsmEnum(FsmEnum source)
      : base((NamedVariable) source)
    {
      this.EnumType = source.EnumType;
      this.Value = source.Value;
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmEnum(this);

    public override string ToString() => this.Value.ToString();

    public override int ToInt() => this.intValue;

    public override void Clear()
    {
    }

    public override VariableType VariableType => VariableType.Enum;

    public override System.Type ObjectType
    {
      get => this.EnumType;
      set => this.EnumType = value;
    }

    public override bool TestTypeConstraint(VariableType variableType, System.Type _enumType = null)
    {
      if (variableType == VariableType.Unknown)
        return true;
      if (!base.TestTypeConstraint(variableType, this.enumType))
        return false;
      return this.enumType == typeof (Enum) || _enumType == this.EnumType || _enumType == null;
    }

    public static implicit operator FsmEnum(Enum value) => new FsmEnum()
    {
      Value = value
    };
  }
}
