// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.NoteAttribute
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;

namespace HutongGames.PlayMaker
{
  [AttributeUsage(AttributeTargets.All)]
  public sealed class NoteAttribute : Attribute
  {
    private readonly string text;

    public string Text => this.text;

    public NoteAttribute(string text) => this.text = text;
  }
}
