// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmEventMapping
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmEventMapping
  {
    public FsmEvent fromEvent;
    public FsmEvent toEvent;

    public FsmEventMapping()
    {
    }

    public FsmEventMapping(FsmEvent fromEvent, FsmEvent toEvent)
    {
      this.fromEvent = fromEvent;
      this.toEvent = toEvent;
    }

    public FsmEventMapping(FsmEventMapping source)
    {
      this.fromEvent = source.fromEvent;
      this.toEvent = source.toEvent;
    }

    public FsmEventMapping Init()
    {
      this.fromEvent = FsmEvent.GetFsmEvent(this.fromEvent);
      this.toEvent = FsmEvent.GetFsmEvent(this.toEvent);
      return this;
    }
  }
}
