// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmExecutionStack
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System.Collections.Generic;

namespace HutongGames.PlayMaker
{
  public static class FsmExecutionStack
  {
    private static readonly Stack<Fsm> fsmExecutionStack = new Stack<Fsm>(256);

    public static Fsm ExecutingFsm => FsmExecutionStack.fsmExecutionStack.Count <= 0 ? (Fsm) null : FsmExecutionStack.fsmExecutionStack.Peek();

    public static FsmState ExecutingState => FsmExecutionStack.ExecutingFsm == null ? (FsmState) null : FsmExecutionStack.ExecutingFsm.ActiveState;

    public static string ExecutingStateName => FsmExecutionStack.ExecutingFsm == null ? "[none]" : FsmExecutionStack.ExecutingFsm.ActiveStateName;

    public static FsmStateAction ExecutingAction => FsmExecutionStack.ExecutingState == null ? (FsmStateAction) null : FsmExecutionStack.ExecutingState.ActiveAction;

    public static int StackCount => FsmExecutionStack.fsmExecutionStack.Count;

    public static int MaxStackCount { get; private set; }

    public static void Reset() => FsmExecutionStack.fsmExecutionStack.Clear();

    public static void PushFsm(Fsm executingFsm)
    {
      FsmExecutionStack.fsmExecutionStack.Push(executingFsm);
      if (FsmExecutionStack.fsmExecutionStack.Count <= FsmExecutionStack.MaxStackCount)
        return;
      FsmExecutionStack.MaxStackCount = FsmExecutionStack.fsmExecutionStack.Count;
    }

    public static void PopFsm() => FsmExecutionStack.fsmExecutionStack.Pop();

    public static string GetDebugString() => "" + "\nExecutingFsm: " + (object) FsmExecutionStack.ExecutingFsm + "\nExecutingState: " + FsmExecutionStack.ExecutingStateName;
  }
}
