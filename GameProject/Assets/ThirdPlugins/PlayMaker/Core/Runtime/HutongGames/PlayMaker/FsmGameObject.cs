// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmGameObject
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmGameObject : NamedVariable
  {
    [SerializeField]
    private GameObject value;

    public event Action OnChange;

    public GameObject Value
    {
      get => this.CastVariable == null ? this.value : this.CastVariable.RawValue as GameObject;
      set
      {
        if (value == this.value)
          return;
        this.value = value;
        if (this.OnChange == null)
          return;
        this.OnChange();
      }
    }

    public override System.Type ObjectType => typeof (GameObject);

    public override object RawValue
    {
      get => (object) this.value;
      set => this.value = value as GameObject;
    }

    public override void SafeAssign(object val) => this.value = val as GameObject;

    public FsmGameObject()
    {
    }

    public FsmGameObject(string name)
      : base(name)
    {
    }

    public FsmGameObject(FsmGameObject source)
      : base((NamedVariable) source)
    {
      if (source == null)
        return;
      this.value = source.value;
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmGameObject(this);

    public override void Clear() => this.value = (GameObject) null;

    public override VariableType VariableType => VariableType.GameObject;

    public override string ToString() => !((UnityEngine.Object) this.Value == (UnityEngine.Object) null) ? this.Value.name : "None";

    public static implicit operator FsmGameObject(GameObject value) => new FsmGameObject(string.Empty)
    {
      value = value
    };
  }
}
