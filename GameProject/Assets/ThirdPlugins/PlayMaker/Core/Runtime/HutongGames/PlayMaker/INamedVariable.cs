// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.INamedVariable
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;

namespace HutongGames.PlayMaker
{
  public interface INamedVariable
  {
    string Name { get; }

    bool UseVariable { get; set; }

    bool UsesVariable { get; }

    bool NetworkSync { get; set; }

    bool IsNone { get; }

    string GetDisplayName();

    string DebugString();

    VariableType VariableType { get; }

    VariableType TypeConstraint { get; }

    Type ObjectType { get; set; }

    object RawValue { get; set; }

    bool TestTypeConstraint(VariableType variableType, Type objectType = null);

    void SafeAssign(object val);
  }
}
