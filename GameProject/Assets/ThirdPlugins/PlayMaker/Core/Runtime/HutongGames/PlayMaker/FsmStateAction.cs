// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmStateAction
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using System.Collections;
using System.Diagnostics;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  public abstract class FsmStateAction : IFsmStateAction
  {
    public static Color ActiveHighlightColor;
    public static bool Repaint;
    private string name;
    private int index = -1;
    private bool enabled = true;
    private bool isOpen = true;
    private bool active;
    private bool finished;
    private bool autoName;
    private bool blocksFinish = true;
    private GameObject owner;
    [NonSerialized]
    private FsmState fsmState;
    [NonSerialized]
    private Fsm fsm;
    [NonSerialized]
    private PlayMakerFSM fsmComponent;

    public virtual void Init(FsmState state)
    {
      this.fsmState = state;
      this.fsm = state.Fsm;
      this.owner = this.fsm.GameObject;
      this.fsmComponent = this.fsm.FsmComponent;
    }

    public virtual void InitEditor(Fsm fsmOwner)
    {
    }

    public virtual void Reset()
    {
    }

    public void BaseReset()
    {
      this.autoName = false;
      this.name = "";
    }

    public virtual void OnPreprocess()
    {
    }

    public virtual void Awake()
    {
    }

    public virtual bool Event(FsmEvent fsmEvent) => false;

    public void Finish()
    {
      if (this.finished)
        return;
      this.active = false;
      this.finished = true;
      this.State.FinishAction(this);
    }

    public Coroutine StartCoroutine(IEnumerator routine) => this.fsmComponent.StartCoroutine("DoCoroutine", (object) routine);

    public void StopCoroutine(Coroutine routine) => this.fsmComponent.StopCoroutine(routine);

    public virtual void OnEnter()
    {
    }

    public virtual void OnFixedUpdate()
    {
    }

    public virtual void OnUpdate()
    {
    }

    public virtual void OnGUI()
    {
    }

    public virtual void OnLateUpdate()
    {
    }

    public virtual void OnExit()
    {
    }

    public virtual void OnDrawActionGizmos()
    {
    }

    public virtual void OnDrawActionGizmosSelected()
    {
    }

    public virtual string AutoName() => (string) null;

    public virtual void OnActionTargetInvoked(object targetObject)
    {
    }

    public virtual float GetProgress() => 0.0f;

    public virtual void DoCollisionEnter(Collision collisionInfo)
    {
    }

    public virtual void DoCollisionStay(Collision collisionInfo)
    {
    }

    public virtual void DoCollisionExit(Collision collisionInfo)
    {
    }

    public virtual void DoTriggerEnter(Collider other)
    {
    }

    public virtual void DoTriggerStay(Collider other)
    {
    }

    public virtual void DoTriggerExit(Collider other)
    {
    }

    public virtual void DoParticleCollision(GameObject other)
    {
    }

    public virtual void DoCollisionEnter2D(Collision2D collisionInfo)
    {
    }

    public virtual void DoCollisionStay2D(Collision2D collisionInfo)
    {
    }

    public virtual void DoCollisionExit2D(Collision2D collisionInfo)
    {
    }

    public virtual void DoTriggerEnter2D(Collider2D other)
    {
    }

    public virtual void DoTriggerStay2D(Collider2D other)
    {
    }

    public virtual void DoTriggerExit2D(Collider2D other)
    {
    }

    public virtual void DoControllerColliderHit(ControllerColliderHit collider)
    {
    }

    public virtual void DoJointBreak(float force)
    {
    }

    public virtual void DoJointBreak2D(Joint2D joint)
    {
    }

    public virtual void DoAnimatorMove()
    {
    }

    public virtual void DoAnimatorIK(int layerIndex)
    {
    }

    public void Log(string text)
    {
      if (!FsmLog.LoggingEnabled)
        return;
      this.fsm.MyLog.LogAction(FsmLogType.Info, text);
    }

    public void LogWarning(string text)
    {
      if (!FsmLog.LoggingEnabled)
        return;
      this.fsm.MyLog.LogAction(FsmLogType.Warning, text);
    }

    public void LogError(string text)
    {
      if (!FsmLog.LoggingEnabled)
        return;
      this.fsm.MyLog.LogAction(FsmLogType.Error, text);
    }

    public virtual string ErrorCheck() => string.Empty;

    protected static bool TagMatches(FsmString tag, Component other) => FsmString.IsNullOrEmpty(tag) || other.gameObject.CompareTag(tag.Value);

    protected static bool TagMatches(FsmString tag, Collision collisionInfo) => FsmString.IsNullOrEmpty(tag) || collisionInfo.collider.gameObject.CompareTag(tag.Value);

    protected static bool TagMatches(FsmString tag, Collision2D collisionInfo) => FsmString.IsNullOrEmpty(tag) || collisionInfo.collider.gameObject.CompareTag(tag.Value);

    protected static bool TagMatches(FsmString tag, ControllerColliderHit collisionInfo) => FsmString.IsNullOrEmpty(tag) || collisionInfo.collider.gameObject.CompareTag(tag.Value);

    protected static bool TagMatches(FsmString tag, GameObject go) => FsmString.IsNullOrEmpty(tag) || go.CompareTag(tag.Value);

    public string Name
    {
      get => this.name;
      set => this.name = value;
    }

    public string DisplayName { get; set; }

    public Fsm Fsm
    {
      get => this.fsm;
      set => this.fsm = value;
    }

    public GameObject Owner
    {
      get => this.owner;
      set => this.owner = value;
    }

    public FsmState State
    {
      get => this.fsmState;
      set => this.fsmState = value;
    }

    public bool Enabled
    {
      get => this.enabled;
      set => this.enabled = value;
    }

    public bool IsOpen
    {
      get => this.isOpen;
      set => this.isOpen = value;
    }

    public bool IsAutoNamed
    {
      get => this.autoName;
      set => this.autoName = value;
    }

    public bool Entered { get; set; }

    public bool Finished
    {
      get => this.finished;
      set
      {
        if (value)
          this.active = false;
        this.finished = value;
      }
    }

    public bool BlocksFinish
    {
      get => this.blocksFinish;
      set => this.blocksFinish = value;
    }

    public bool HandlesOnEvent
    {
      set
      {
        if (this.State == null)
          return;
        this.State.HandlesOnEvent = value;
      }
    }

    public bool Active
    {
      get => this.active;
      set => this.active = value;
    }
    
    public int Index
    {
      get => this.index;
      set => this.index = value;
    }

    [Conditional("DEBUG_LOG")]
    private void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }
  }
}
