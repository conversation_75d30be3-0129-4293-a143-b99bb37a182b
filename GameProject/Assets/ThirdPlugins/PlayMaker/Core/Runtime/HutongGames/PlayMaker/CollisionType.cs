// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.CollisionType
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

namespace HutongGames.PlayMaker
{
  public enum CollisionType
  {
    OnCollisionEnter,
    OnCollisionStay,
    OnCollisionExit,
    OnControllerColliderHit,
    OnParticleCollision,
  }
}
