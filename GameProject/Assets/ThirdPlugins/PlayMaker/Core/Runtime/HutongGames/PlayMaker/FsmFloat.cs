// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmFloat
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmFloat : NamedVariable
  {
    [SerializeField]
    private float value;

    public float Value
    {
      get => this.CastVariable == null ? this.value : this.CastVariable.ToFloat();
      set => this.value = value;
    }

    public override object RawValue
    {
      get => (object) this.value;
      set => this.value = (float) value;
    }

    public override void SafeAssign(object val)
    {
      if (val is float)
        this.value = (float)val;
      if (!(val is int))
        return;
      this.value = (float)val;
    }

    public FsmFloat()
    {
    }

    public FsmFloat(string name)
      : base(name)
    {
    }

    public FsmFloat(FsmFloat source)
      : base((NamedVariable) source)
    {
      if (source == null)
        return;
      this.value = source.value;
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmFloat(this);

    public override VariableType VariableType => VariableType.Float;

    public override string ToString() => this.Value.ToString();

    public override string DebugString() => string.Format("{0:0.0###########}", (object) this.Value);

    public override int ToInt() => (int) this.value;

    public override void Clear() => this.value = 0.0f;

    public static implicit operator FsmFloat(float value) => new FsmFloat(string.Empty)
    {
      value = value
    };
  }
}
