// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.IFsmStateAction
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using UnityEngine;

namespace HutongGames.PlayMaker
{
  public interface IFsmStateAction
  {
    bool Enabled { get; set; }

    void Init(FsmState state);

    void Reset();

    void OnEnter();

    void OnUpdate();

    void OnGUI();

    void OnFixedUpdate();

    void OnLateUpdate();

    void OnExit();

    bool Event(FsmEvent fsmEvent);

    void DoControllerColliderHit(ControllerColliderHit collider);

    void DoCollisionEnter(Collision collisionInfo);

    void DoCollisionStay(Collision collisionInfo);

    void DoCollisionExit(Collision collisionInfo);

    void DoTriggerEnter(Collider other);

    void DoTriggerStay(Collider other);

    void DoTriggerExit(Collider other);

    void Log(string text);

    void LogWarning(string text);

    void LogError(string text);

    string ErrorCheck();
  }
}
