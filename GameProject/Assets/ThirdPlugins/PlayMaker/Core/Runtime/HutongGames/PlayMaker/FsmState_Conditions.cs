using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    public partial class FsmState
    {
        [SerializeField] 
        private List<ConditionData> startConditionsData = new List<ConditionData>();
        [SerializeField] 
        private List<ConditionData> conditionsData = new List<ConditionData>();
        
        public ConditionData[] StartConditions
        {
            get
            {
                if (this.startConditionsData != null)
                {
                    return this.startConditionsData.ToArray();
                }
                return null;
            }
        }

        public ConditionData[] Conditions
        {
            get
            {
                if (this.conditionsData != null)
                {
                    return this.conditionsData.ToArray();
                }
                return null;
            }
        }
        
        public void AddStartConditionData(ConditionData conditionData)
        {
            startConditionsData.Add(ConditionData.CopyFrom(conditionData));
        }

        public void AddConditionData(ConditionData conditionData)
        {
            conditionsData.Add(ConditionData.CopyFrom(conditionData));
        }

        public void RemoveConditionData(ConditionData data)
        {
            startConditionsData.Remove(data);
            conditionsData.Remove(data);
        }
    }
}
