// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.LayoutOption
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class LayoutOption
  {
    public LayoutOption.LayoutOptionType option;
    public FsmFloat floatParam;
    public FsmBool boolParam;

    public LayoutOption() => this.ResetParameters();

    public LayoutOption(LayoutOption source)
    {
      this.option = source.option;
      this.floatParam = new FsmFloat(source.floatParam);
      this.boolParam = new FsmBool(source.boolParam);
    }

    public void ResetParameters()
    {
      this.floatParam = (FsmFloat) 0.0f;
      this.boolParam = (FsmBool) false;
    }

    public GUILayoutOption GetGUILayoutOption()
    {
      switch (this.option)
      {
        case LayoutOption.LayoutOptionType.Width:
          return GUILayout.Width(this.floatParam.Value);
        case LayoutOption.LayoutOptionType.Height:
          return GUILayout.Height(this.floatParam.Value);
        case LayoutOption.LayoutOptionType.MinWidth:
          return GUILayout.MinWidth(this.floatParam.Value);
        case LayoutOption.LayoutOptionType.MaxWidth:
          return GUILayout.MaxWidth(this.floatParam.Value);
        case LayoutOption.LayoutOptionType.MinHeight:
          return GUILayout.MinHeight(this.floatParam.Value);
        case LayoutOption.LayoutOptionType.MaxHeight:
          return GUILayout.MaxHeight(this.floatParam.Value);
        case LayoutOption.LayoutOptionType.ExpandWidth:
          return GUILayout.ExpandWidth(this.boolParam.Value);
        case LayoutOption.LayoutOptionType.ExpandHeight:
          return GUILayout.ExpandHeight(this.boolParam.Value);
        default:
          return (GUILayoutOption) null;
      }
    }

    public enum LayoutOptionType
    {
      Width,
      Height,
      MinWidth,
      MaxWidth,
      MinHeight,
      MaxHeight,
      ExpandWidth,
      ExpandHeight,
    }
  }
}
