// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmColor
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmColor : NamedVariable
  {
    [SerializeField]
    private Color value = Color.black;

    public Color Value
    {
      get => this.value;
      set => this.value = value;
    }

    public override object RawValue
    {
      get => (object) this.value;
      set => this.value = (Color) value;
    }

    public FsmColor()
    {
    }

    public FsmColor(string name)
      : base(name)
    {
    }

    public FsmColor(FsmColor source)
      : base((NamedVariable) source)
    {
      if (source == null)
        return;
      this.value = source.value;
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmColor(this);

    public override void Clear() => this.value = Color.white;

    public override VariableType VariableType => VariableType.Color;

    public override string ToString() => this.value.ToString();

    public static implicit operator FsmColor(Color value) => new FsmColor(string.Empty)
    {
      value = value
    };
  }
}
