// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmEvent
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using System.Collections.Generic;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmEvent : IComparable, INameable
  {
    private static Dictionary<string, FsmEvent> _eventLookup;
    private static readonly object syncObj = new object();
    [SerializeField]
    private string name;
    [SerializeField]
    private bool isSystemEvent;
    [SerializeField]
    private bool isGlobal;
    [NonSerialized]
    private string _path;

    public static PlayMakerGlobals GlobalsComponent => PlayMakerGlobals.Instance;

    public static List<string> globalEvents => PlayMakerGlobals.Instance.Events;

    private static Dictionary<string, FsmEvent> eventLookup
    {
      get
      {
        if (FsmEvent._eventLookup == null)
        {
          FsmEvent._eventLookup = new Dictionary<string, FsmEvent>(100);
          FsmEvent.Initialize();
        }
        return FsmEvent._eventLookup;
      }
    }

    public static List<FsmEvent> EventList => new List<FsmEvent>((IEnumerable<FsmEvent>) FsmEvent.eventLookup.Values);

    private static void Initialize()
    {
      PlayMakerGlobals.Initialize();
      // FsmEvent.AddSystemEvents();
      // FsmEvent.AddGlobalEvents();
    }

    public string Name
    {
      get => this.name;
      set => this.name = value;
    }

    public bool IsUnityEvent => this.isSystemEvent && this.Name != "FINISHED";

    public bool IsSystemEvent
    {
      get => this.isSystemEvent;
      set => this.isSystemEvent = value;
    }

    public bool IsMouseEvent => this == FsmEvent.MouseDown || this == FsmEvent.MouseDrag || (this == FsmEvent.MouseEnter || this == FsmEvent.MouseExit) || (this == FsmEvent.MouseOver || this == FsmEvent.MouseUp) || this == FsmEvent.MouseUpAsButton;

    public bool IsApplicationEvent => this == FsmEvent.ApplicationFocus || this == FsmEvent.ApplicationPause;

    public bool IsLegacyNetworkEvent => this == FsmEvent.NetworkInstantiate || this == FsmEvent.PlayerConnected || (this == FsmEvent.PlayerDisconnected || this == FsmEvent.ConnectedToServer) || (this == FsmEvent.DisconnectedFromServer || this == FsmEvent.FailedToConnect || (this == FsmEvent.FailedToConnectToMasterServer || this == FsmEvent.MasterServerEvent)) || this == FsmEvent.ServerInitialized;

    public bool IsCollisionEvent => this == FsmEvent.CollisionEnter || this == FsmEvent.CollisionStay || this == FsmEvent.CollisionExit;

    public bool IsTriggerEvent => this == FsmEvent.TriggerEnter || this == FsmEvent.TriggerStay || this == FsmEvent.TriggerExit;

    public bool IsCollision2DEvent => this == FsmEvent.CollisionEnter2D || this == FsmEvent.CollisionStay2D || this == FsmEvent.CollisionExit2D;

    public bool IsTrigger2DEvent => this == FsmEvent.TriggerEnter2D || this == FsmEvent.TriggerStay2D || this == FsmEvent.TriggerExit2D;

    public bool IsUIEvent => this == FsmEvent.UiClick || this == FsmEvent.UiBeginDrag || (this == FsmEvent.UiDrag || this == FsmEvent.UiEndDrag) || (this == FsmEvent.UiDrop || this == FsmEvent.UiPointerUp || (this == FsmEvent.UiPointerClick || this == FsmEvent.UiPointerDown)) || (this == FsmEvent.UiPointerEnter || this == FsmEvent.UiPointerExit || (this == FsmEvent.UiBoolValueChanged || this == FsmEvent.UiFloatValueChanged) || (this == FsmEvent.UiIntValueChanged || this == FsmEvent.UiVector2ValueChanged)) || this == FsmEvent.UiEndEdit;

    public bool IsGlobal
    {
      get => this.isGlobal;
      set
      {
        if (value)
          PlayMakerGlobals.AddGlobalEvent(this.name);
        else
          PlayMakerGlobals.RemoveGlobalEvent(this.name);
        this.isGlobal = value;
        FsmEvent.SanityCheckEventList();
      }
    }

    public static bool IsNullOrEmpty(FsmEvent fsmEvent) => fsmEvent == null || string.IsNullOrEmpty(fsmEvent.name);

    public string Path
    {
      get => this._path;
      set => this._path = value;
    }

    public FsmEvent(string name)
    {
      lock (FsmEvent.syncObj)
      {
        this.name = name;
        if (FsmEvent.eventLookup.ContainsKey(name))
          return;
        FsmEvent.eventLookup[name] = this;
      }
    }

    public FsmEvent(FsmEvent source)
    {
      lock (FsmEvent.syncObj)
      {
        this.name = source.name;
        this.isSystemEvent = source.isSystemEvent;
        this.isGlobal = source.isGlobal;
        FsmEvent fsmEvent;
        if (FsmEvent.eventLookup.TryGetValue(source.name, out fsmEvent))
          fsmEvent.isGlobal |= this.isGlobal;
        else
          FsmEvent.eventLookup.Add(source.name, source);
      }
    }

    int IComparable.CompareTo(object obj)
    {
      FsmEvent fsmEvent = (FsmEvent) obj;
      if (this.isSystemEvent && !fsmEvent.isSystemEvent)
        return -1;
      return !this.isSystemEvent && fsmEvent.isSystemEvent ? 1 : string.Compare(this.name, fsmEvent.name, StringComparison.OrdinalIgnoreCase);
    }

    public static bool EventListContainsEvent(List<FsmEvent> fsmEventList, string fsmEventName)
    {
      lock (FsmEvent.syncObj)
      {
        if (fsmEventList == null || string.IsNullOrEmpty(fsmEventName))
          return false;
        for (int index = 0; index < fsmEventList.Count; ++index)
        {
          if (fsmEventList[index].Name == fsmEventName)
            return true;
        }
        return false;
      }
    }

    public static void RemoveEventFromEventList(FsmEvent fsmEvent)
    {
      if (fsmEvent.isSystemEvent)
        Debug.LogError((object) ("RemoveEventFromEventList: Trying to delete System Event: " + fsmEvent.Name));
      FsmEvent.eventLookup.Remove(fsmEvent.name);
    }

    public static FsmEvent FindEvent(string eventName)
    {
      lock (FsmEvent.syncObj)
      {
        FsmEvent fsmEvent;
        FsmEvent.eventLookup.TryGetValue(eventName, out fsmEvent);
        return fsmEvent;
      }
    }

    public static bool IsEventGlobal(string eventName) => FsmEvent.globalEvents.Contains(eventName);

    public static void SetEventIsGlobal(string eventName)
    {
      FsmEvent fsmEvent;
      if (FsmEvent.eventLookup.TryGetValue(eventName, out fsmEvent))
        fsmEvent.isGlobal = true;
      PlayMakerGlobals.AddGlobalEvent(eventName);
    }

    public static bool EventListContains(string eventName) => FsmEvent.FindEvent(eventName) != null;

    public static FsmEvent GetFsmEvent(string eventName)
    {
      lock (FsmEvent.syncObj)
      {
        if (string.IsNullOrEmpty(eventName))
          return new FsmEvent("");
        FsmEvent source1;
        if (FsmEvent.eventLookup.TryGetValue(eventName, out source1))
          return PlayMakerGlobals.IsPlaying ? source1 : new FsmEvent(source1);
        FsmEvent source2 = new FsmEvent(eventName);
        return PlayMakerGlobals.IsPlaying ? source2 : new FsmEvent(source2);
      }
    }

    public static FsmEvent GetFsmEvent(FsmEvent fsmEvent)
    {
      if (fsmEvent == null || fsmEvent.name == null)
        return (FsmEvent) null;
      lock (FsmEvent.syncObj)
      {
        FsmEvent fsmEvent1;
        if (FsmEvent.eventLookup.TryGetValue(fsmEvent.Name, out fsmEvent1))
        {
          fsmEvent1.isGlobal |= fsmEvent.isGlobal;
          fsmEvent.isGlobal |= fsmEvent1.isGlobal;
          return PlayMakerGlobals.IsPlaying ? fsmEvent1 : fsmEvent;
        }
        FsmEvent.eventLookup[fsmEvent.name] = fsmEvent;
        return PlayMakerGlobals.IsPlaying ? fsmEvent : new FsmEvent(fsmEvent);
      }
    }

    public static FsmEvent GetOrCreateGlobalEvent(string eventName)
    {
      var fsmEvent = FsmEvent.FindEvent(eventName);
      if (fsmEvent == null)
      {
        fsmEvent = FsmEvent.GetFsmEvent(eventName);
        fsmEvent.IsGlobal = true;
        fsmEvent = FsmEvent.GetFsmEvent(fsmEvent);
      }
      return fsmEvent;
    }

    public static FsmEvent AddFsmEvent(FsmEvent fsmEvent)
    {
      FsmEvent.eventLookup.Add(fsmEvent.name, fsmEvent);
      return fsmEvent;
    }

    private static void AddSystemEvents()
    {
      FsmEvent.Finished = FsmEvent.AddSystemEvent("FINISHED", "System Events");
      FsmEvent.Disable = FsmEvent.AddSystemEvent("DISABLE", "System Events");
      FsmEvent.BecameInvisible = FsmEvent.AddSystemEvent("BECAME INVISIBLE", "System Events");
      FsmEvent.BecameVisible = FsmEvent.AddSystemEvent("BECAME VISIBLE", "System Events");
      FsmEvent.LevelLoaded = FsmEvent.AddSystemEvent("LEVEL LOADED", "System Events");
      FsmEvent.MouseDown = FsmEvent.AddSystemEvent("MOUSE DOWN", "System Events");
      FsmEvent.MouseDrag = FsmEvent.AddSystemEvent("MOUSE DRAG", "System Events");
      FsmEvent.MouseEnter = FsmEvent.AddSystemEvent("MOUSE ENTER", "System Events");
      FsmEvent.MouseExit = FsmEvent.AddSystemEvent("MOUSE EXIT", "System Events");
      FsmEvent.MouseOver = FsmEvent.AddSystemEvent("MOUSE OVER", "System Events");
      FsmEvent.MouseUp = FsmEvent.AddSystemEvent("MOUSE UP", "System Events");
      FsmEvent.MouseUpAsButton = FsmEvent.AddSystemEvent("MOUSE UP AS BUTTON", "System Events");
      FsmEvent.CollisionEnter = FsmEvent.AddSystemEvent("COLLISION ENTER", "System Events");
      FsmEvent.CollisionExit = FsmEvent.AddSystemEvent("COLLISION EXIT", "System Events");
      FsmEvent.CollisionStay = FsmEvent.AddSystemEvent("COLLISION STAY", "System Events");
      FsmEvent.ControllerColliderHit = FsmEvent.AddSystemEvent("CONTROLLER COLLIDER HIT", "System Events");
      FsmEvent.TriggerEnter = FsmEvent.AddSystemEvent("TRIGGER ENTER", "System Events");
      FsmEvent.TriggerExit = FsmEvent.AddSystemEvent("TRIGGER EXIT", "System Events");
      FsmEvent.TriggerStay = FsmEvent.AddSystemEvent("TRIGGER STAY", "System Events");
      FsmEvent.CollisionEnter2D = FsmEvent.AddSystemEvent("COLLISION ENTER 2D", "System Events");
      FsmEvent.CollisionExit2D = FsmEvent.AddSystemEvent("COLLISION EXIT 2D", "System Events");
      FsmEvent.CollisionStay2D = FsmEvent.AddSystemEvent("COLLISION STAY 2D", "System Events");
      FsmEvent.TriggerEnter2D = FsmEvent.AddSystemEvent("TRIGGER ENTER 2D", "System Events");
      FsmEvent.TriggerExit2D = FsmEvent.AddSystemEvent("TRIGGER EXIT 2D", "System Events");
      FsmEvent.TriggerStay2D = FsmEvent.AddSystemEvent("TRIGGER STAY 2D", "System Events");
      FsmEvent.PlayerConnected = FsmEvent.AddSystemEvent("PLAYER CONNECTED", "Network Events");
      FsmEvent.ServerInitialized = FsmEvent.AddSystemEvent("SERVER INITIALIZED", "Network Events");
      FsmEvent.ConnectedToServer = FsmEvent.AddSystemEvent("CONNECTED TO SERVER", "Network Events");
      FsmEvent.PlayerDisconnected = FsmEvent.AddSystemEvent("PLAYER DISCONNECTED", "Network Events");
      FsmEvent.DisconnectedFromServer = FsmEvent.AddSystemEvent("DISCONNECTED FROM SERVER", "Network Events");
      FsmEvent.FailedToConnect = FsmEvent.AddSystemEvent("FAILED TO CONNECT", "Network Events");
      FsmEvent.FailedToConnectToMasterServer = FsmEvent.AddSystemEvent("FAILED TO CONNECT TO MASTER SERVER", "Network Events");
      FsmEvent.MasterServerEvent = FsmEvent.AddSystemEvent("MASTER SERVER EVENT", "Network Events");
      FsmEvent.NetworkInstantiate = FsmEvent.AddSystemEvent("NETWORK INSTANTIATE", "Network Events");
      FsmEvent.ApplicationFocus = FsmEvent.AddSystemEvent("APPLICATION FOCUS", "System Events");
      FsmEvent.ApplicationPause = FsmEvent.AddSystemEvent("APPLICATION PAUSE", "System Events");
      FsmEvent.ApplicationQuit = FsmEvent.AddSystemEvent("APPLICATION QUIT", "System Events");
      FsmEvent.ParticleCollision = FsmEvent.AddSystemEvent("PARTICLE COLLISION", "System Events");
      FsmEvent.JointBreak = FsmEvent.AddSystemEvent("JOINT BREAK", "System Events");
      FsmEvent.JointBreak2D = FsmEvent.AddSystemEvent("JOINT BREAK 2D", "System Events");
      FsmEvent.UiBeginDrag = FsmEvent.AddSystemEvent("UI BEGIN DRAG", "UI Events");
      FsmEvent.UiDrag = FsmEvent.AddSystemEvent("UI DRAG", "UI Events");
      FsmEvent.UiEndDrag = FsmEvent.AddSystemEvent("UI END DRAG", "UI Events");
      FsmEvent.UiClick = FsmEvent.AddSystemEvent("UI CLICK", "UI Events");
      FsmEvent.UiDrop = FsmEvent.AddSystemEvent("UI DROP", "UI Events");
      FsmEvent.UiPointerClick = FsmEvent.AddSystemEvent("UI POINTER CLICK", "UI Events");
      FsmEvent.UiPointerDown = FsmEvent.AddSystemEvent("UI POINTER DOWN", "UI Events");
      FsmEvent.UiPointerEnter = FsmEvent.AddSystemEvent("UI POINTER ENTER", "UI Events");
      FsmEvent.UiPointerExit = FsmEvent.AddSystemEvent("UI POINTER EXIT", "UI Events");
      FsmEvent.UiPointerUp = FsmEvent.AddSystemEvent("UI POINTER UP", "UI Events");
      FsmEvent.UiBoolValueChanged = FsmEvent.AddSystemEvent("UI BOOL VALUE CHANGED", "UI Events");
      FsmEvent.UiFloatValueChanged = FsmEvent.AddSystemEvent("UI FLOAT VALUE CHANGED", "UI Events");
      FsmEvent.UiIntValueChanged = FsmEvent.AddSystemEvent("UI INT VALUE CHANGED", "UI Events");
      FsmEvent.UiVector2ValueChanged = FsmEvent.AddSystemEvent("UI VECTOR2 VALUE CHANGED", "UI Events");
      FsmEvent.UiEndEdit = FsmEvent.AddSystemEvent("UI END EDIT", "UI Events");
    }

    private static FsmEvent AddSystemEvent(string eventName, string path = "") => new FsmEvent(eventName)
    {
      IsSystemEvent = true,
      Path = path == "" ? "" : path + "/"
    };

    public static FsmEvent BecameInvisible { get; private set; }

    public static FsmEvent BecameVisible { get; private set; }

    public static FsmEvent CollisionEnter { get; private set; }

    public static FsmEvent CollisionExit { get; private set; }

    public static FsmEvent CollisionStay { get; private set; }

    public static FsmEvent CollisionEnter2D { get; private set; }

    public static FsmEvent CollisionExit2D { get; private set; }

    public static FsmEvent CollisionStay2D { get; private set; }

    public static FsmEvent ControllerColliderHit { get; private set; }

    public static FsmEvent Finished { get; private set; }

    public static FsmEvent LevelLoaded { get; private set; }

    public static FsmEvent MouseDown { get; private set; }

    public static FsmEvent MouseDrag { get; private set; }

    public static FsmEvent MouseEnter { get; private set; }

    public static FsmEvent MouseExit { get; private set; }

    public static FsmEvent MouseOver { get; private set; }

    public static FsmEvent MouseUp { get; private set; }

    public static FsmEvent MouseUpAsButton { get; private set; }

    public static FsmEvent TriggerEnter { get; private set; }

    public static FsmEvent TriggerExit { get; private set; }

    public static FsmEvent TriggerStay { get; private set; }

    public static FsmEvent TriggerEnter2D { get; private set; }

    public static FsmEvent TriggerExit2D { get; private set; }

    public static FsmEvent TriggerStay2D { get; private set; }

    public static FsmEvent ApplicationFocus { get; private set; }

    public static FsmEvent ApplicationPause { get; private set; }

    public static FsmEvent ApplicationQuit { get; private set; }

    public static FsmEvent ParticleCollision { get; private set; }

    public static FsmEvent JointBreak { get; private set; }

    public static FsmEvent JointBreak2D { get; private set; }

    public static FsmEvent Disable { get; private set; }

    public static FsmEvent PlayerConnected { get; private set; }

    public static FsmEvent ServerInitialized { get; private set; }

    public static FsmEvent ConnectedToServer { get; private set; }

    public static FsmEvent PlayerDisconnected { get; private set; }

    public static FsmEvent DisconnectedFromServer { get; private set; }

    public static FsmEvent FailedToConnect { get; private set; }

    public static FsmEvent FailedToConnectToMasterServer { get; private set; }

    public static FsmEvent MasterServerEvent { get; private set; }

    public static FsmEvent NetworkInstantiate { get; private set; }

    public static FsmEvent UiBeginDrag { get; private set; }

    public static FsmEvent UiDrag { get; private set; }

    public static FsmEvent UiEndDrag { get; private set; }

    public static FsmEvent UiClick { get; private set; }

    public static FsmEvent UiDrop { get; private set; }

    public static FsmEvent UiPointerClick { get; private set; }

    public static FsmEvent UiPointerDown { get; private set; }

    public static FsmEvent UiPointerEnter { get; private set; }

    public static FsmEvent UiPointerExit { get; private set; }

    public static FsmEvent UiPointerUp { get; private set; }

    public static FsmEvent UiBoolValueChanged { get; private set; }

    public static FsmEvent UiFloatValueChanged { get; private set; }

    public static FsmEvent UiIntValueChanged { get; private set; }

    public static FsmEvent UiVector2ValueChanged { get; private set; }

    public static FsmEvent UiEndEdit { get; private set; }

    private static void AddGlobalEvents()
    {
      for (int index = 0; index < FsmEvent.globalEvents.Count; ++index)
        new FsmEvent(FsmEvent.globalEvents[index]).isGlobal = true;
    }

    public static void SanityCheckEventList()
    {
      foreach (FsmEvent fsmEvent in FsmEvent.EventList)
      {
        if (FsmEvent.IsEventGlobal(fsmEvent.name))
          fsmEvent.isGlobal = true;
        if (fsmEvent.isGlobal && !FsmEvent.globalEvents.Contains(fsmEvent.name))
          FsmEvent.globalEvents.Add(fsmEvent.name);
      }
    }
  }
}
