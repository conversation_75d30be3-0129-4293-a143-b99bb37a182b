// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FunctionCall
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FunctionCall
  {
    public string FunctionName = "";
    [SerializeField]
    private string parameterType;
    public FsmBool BoolParameter;
    public FsmFloat FloatParameter;
    public FsmInt IntParameter;
    public FsmGameObject GameObjectParameter;
    public FsmObject ObjectParameter;
    public FsmString StringParameter;
    public FsmVector2 Vector2Parameter;
    public FsmVector3 Vector3Parameter;
    public FsmRect RectParamater;
    public FsmQuaternion QuaternionParameter;
    public FsmMaterial MaterialParameter;
    public FsmTexture TextureParameter;
    public FsmColor ColorParameter;
    public FsmEnum EnumParameter;
    public FsmArray ArrayParameter;

    public FunctionCall() => this.ResetParameters();

    public FunctionCall(FunctionCall source)
    {
      this.FunctionName = source.FunctionName;
      this.parameterType = source.parameterType;
      this.BoolParameter = new FsmBool(source.BoolParameter);
      this.FloatParameter = new FsmFloat(source.FloatParameter);
      this.IntParameter = new FsmInt(source.IntParameter);
      this.GameObjectParameter = new FsmGameObject(source.GameObjectParameter);
      this.ObjectParameter = source.ObjectParameter;
      this.StringParameter = new FsmString(source.StringParameter);
      this.Vector2Parameter = new FsmVector2(source.Vector2Parameter);
      this.Vector3Parameter = new FsmVector3(source.Vector3Parameter);
      this.RectParamater = new FsmRect(source.RectParamater);
      this.QuaternionParameter = new FsmQuaternion(source.QuaternionParameter);
      this.MaterialParameter = new FsmMaterial((FsmObject) source.MaterialParameter);
      this.TextureParameter = new FsmTexture((FsmObject) source.TextureParameter);
      this.ColorParameter = new FsmColor(source.ColorParameter);
      this.EnumParameter = new FsmEnum(source.EnumParameter);
      this.ArrayParameter = new FsmArray(source.ArrayParameter);
    }

    public void ResetParameters()
    {
      this.BoolParameter = (FsmBool) false;
      this.FloatParameter = (FsmFloat) 0.0f;
      this.IntParameter = (FsmInt) 0;
      this.StringParameter = (FsmString) "";
      this.GameObjectParameter = new FsmGameObject("");
      this.ObjectParameter = (FsmObject) null;
      this.Vector2Parameter = new FsmVector2();
      this.Vector3Parameter = new FsmVector3();
      this.RectParamater = new FsmRect();
      this.QuaternionParameter = new FsmQuaternion();
      this.MaterialParameter = new FsmMaterial();
      this.TextureParameter = new FsmTexture();
      this.ColorParameter = new FsmColor();
      this.EnumParameter = new FsmEnum();
      this.ArrayParameter = new FsmArray();
    }

    public string ParameterType
    {
      get => this.parameterType;
      set => this.parameterType = value;
    }
  }
}
