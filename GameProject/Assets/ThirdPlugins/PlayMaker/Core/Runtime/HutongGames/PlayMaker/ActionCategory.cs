// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.ActionCategory
// Assembly: PlayMaker, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

namespace HutongGames.PlayMaker
{
  public enum ActionCategory
  {
    PlayMakerInternal,
    Animation,
    Audio,
    Debug,
    Events,
    GameObject,
    GUI,
    Input,
    Math,
    Movement,
    Physics,
    Renderer,
    ScriptControl,
    StateMachine,
    Effects,
    Transform,
    GameLogic,
    String,
    Convert,
    GUIElement,
    Vector3,
    Material,
    Lights,
    Camera,
    RenderSettings,
    Color,
    Level,
    GUILayout,
    Logic,
    World,
    Screen,
    Movie,
    Time,
    Character,
    Device,
    Controller,
    CharacterController,
    iTween,
    AnimateVariables,
    InputDevice,
    Rect,
    UnityObject,
    Application,
    NavMesh,
    NavMeshAgent,
    Network,
    Vector2,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Animator,
    Quaternion,
    Enum,
    Physics2D,
    Trigonometry,
    Scene,
    UI,
    Tween,
    Pool,
    Video,
    SpriteRenderer,
    Storage,
  }
}
