// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.ReflectionUtils
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  public static class ReflectionUtils
  {
    private static List<string> assemblyNames;
    private static Assembly[] loadedAssemblies;
    private static readonly Dictionary<string, System.Type> typeLookup = new Dictionary<string, System.Type>();

    public static Assembly[] GetLoadedAssemblies() => AppDomain.CurrentDomain.GetAssemblies();

    [Localizable(false)]
    public static System.Type GetGlobalType(string typeName)
    {
      if (string.IsNullOrEmpty(typeName))
        return (System.Type) null;
      System.Type type;
      ReflectionUtils.typeLookup.TryGetValue(typeName, out type);
      if (type != null)
        return type;
      type = (System.Type.GetType(typeName + ",Assembly-CSharp") ?? System.Type.GetType(typeName + ",PlayMaker")) ?? System.Type.GetType(typeName + ",Assembly-CSharp-firstpass") ?? System.Type.GetType(typeName);
      if (type == null)
      {
        if (ReflectionUtils.assemblyNames == null)
        {
          ReflectionUtils.assemblyNames = new List<string>();
          ReflectionUtils.loadedAssemblies = AppDomain.CurrentDomain.GetAssemblies();
          foreach (Assembly loadedAssembly in ReflectionUtils.loadedAssemblies)
            ReflectionUtils.assemblyNames.Add(loadedAssembly.FullName);
        }
        foreach (string assemblyName in ReflectionUtils.assemblyNames)
        {
          type = System.Type.GetType(typeName + "," + assemblyName);
          if (type != null)
            break;
        }
        if (type == null)
        {
          for (int index1 = 0; index1 < ReflectionUtils.loadedAssemblies.Length; ++index1)
          {
            try
            {
              System.Type[] types = ReflectionUtils.loadedAssemblies[index1].GetTypes();
              for (int index2 = 0; index2 < types.Length; ++index2)
              {
                if (types[index2].Name == typeName && (types[index2].Namespace == "UnityEngine" || types[index2].Namespace == "HutongGames.PlayMaker" || types[index2].Namespace == "HutongGames.PlayMaker.Actions"))
                {
                  type = types[index2];
                  ReflectionUtils.typeLookup[typeName] = type;
                  return type;
                }
              }
            }
            catch (Exception ex)
            {
              Debug.Log((object) ("Failed to GetTypes: " + ReflectionUtils.loadedAssemblies[index1].FullName + "\n" + ex.Message));
            }
          }
        }
      }
      ReflectionUtils.typeLookup.Remove(typeName);
      ReflectionUtils.typeLookup[typeName] = type;
      return type;
    }

    public static System.Type GetPropertyType(System.Type type, string path)
    {
      string str = path;
      char[] chArray = new char[1]{ '.' };
      foreach (string name in str.Split(chArray))
      {
        PropertyInfo property = type.GetProperty(name);
        if (property != null)
        {
          type = property.PropertyType;
        }
        else
        {
          FieldInfo field = type.GetField(name);
          if (field == null)
            return (System.Type) null;
          type = field.FieldType;
        }
      }
      return type;
    }

    public static MemberInfo[] GetMemberInfo(System.Type type, string path)
    {
      if (type == null)
        return (MemberInfo[]) null;
      string[] strArray = path.Split('.');
      MemberInfo[] memberInfoArray = new MemberInfo[strArray.Length];
      for (int index = 0; index < strArray.Length; ++index)
      {
        string name = strArray[index];
        PropertyInfo property = type.GetProperty(name);
        if (property != null)
        {
          memberInfoArray[index] = (MemberInfo) property;
          type = property.PropertyType;
        }
        else
        {
          FieldInfo field = type.GetField(name);
          if (field == null)
            return (MemberInfo[]) null;
          memberInfoArray[index] = (MemberInfo) field;
          type = field.FieldType;
        }
      }
      return memberInfoArray;
    }

    public static bool CanReadMemberValue(MemberInfo member)
    {
      switch (member.MemberType)
      {
        case MemberTypes.Field:
          return true;
        case MemberTypes.Property:
          return ((PropertyInfo) member).CanRead;
        default:
          return false;
      }
    }

    public static bool CanSetMemberValue(MemberInfo member)
    {
      switch (member.MemberType)
      {
        case MemberTypes.Field:
          return true;
        case MemberTypes.Property:
          return ((PropertyInfo) member).CanWrite;
        default:
          return false;
      }
    }

    public static bool CanGetMemberValue(MemberInfo member)
    {
      switch (member.MemberType)
      {
        case MemberTypes.Field:
          return true;
        case MemberTypes.Property:
          return ((PropertyInfo) member).CanRead;
        default:
          return false;
      }
    }

    public static System.Type GetMemberUnderlyingType(MemberInfo member)
    {
      switch (member.MemberType)
      {
        case MemberTypes.Event:
          return ((EventInfo) member).EventHandlerType;
        case MemberTypes.Field:
          return ((FieldInfo) member).FieldType;
        case MemberTypes.Property:
          return ((PropertyInfo) member).PropertyType;
        default:
          throw new ArgumentException("MemberInfo must be of type FieldInfo, PropertyInfo or EventInfo", nameof (member));
      }
    }

    public static object GetMemberValue(MemberInfo[] memberInfo, object target)
    {
      for (int index = 0; index < memberInfo.Length; ++index)
        target = ReflectionUtils.GetMemberValue(memberInfo[index], target);
      return target;
    }

    public static object GetMemberValue(MemberInfo member, object target)
    {
      switch (member.MemberType)
      {
        case MemberTypes.Field:
          return ((FieldInfo) member).GetValue(target);
        case MemberTypes.Property:
          try
          {
            return ((PropertyInfo) member).GetValue(target, (object[]) null);
          }
          catch (TargetParameterCountException ex)
          {
            throw new ArgumentException("MemberInfo has index parameters", nameof (member), (Exception) ex);
          }
        default:
          throw new ArgumentException("MemberInfo is not of type FieldInfo or PropertyInfo", nameof (member));
      }
    }

    public static void SetMemberValue(MemberInfo member, object target, object value)
    {
      switch (member.MemberType)
      {
        case MemberTypes.Field:
          ((FieldInfo) member).SetValue(target, value);
          break;
        case MemberTypes.Property:
          ((PropertyInfo) member).SetValue(target, value, (object[]) null);
          break;
        default:
          throw new ArgumentException("MemberInfo must be if type FieldInfo or PropertyInfo", nameof (member));
      }
    }

    public static void SetMemberValue(MemberInfo[] memberInfo, object target, object value)
    {
      object parent = (object) null;
      MemberInfo targetInfo = (MemberInfo) null;
      for (int index = 0; index < memberInfo.Length - 1; ++index)
      {
        parent = target;
        targetInfo = memberInfo[index];
        target = ReflectionUtils.GetMemberValue(memberInfo[index], target);
      }
      if (target.GetType().IsValueType)
        ReflectionUtils.SetBoxedMemberValue(parent, targetInfo, target, memberInfo[memberInfo.Length - 1], value);
      else
        ReflectionUtils.SetMemberValue(memberInfo[memberInfo.Length - 1], target, value);
    }

    public static void SetBoxedMemberValue(
      object parent,
      MemberInfo targetInfo,
      object target,
      MemberInfo propertyInfo,
      object value)
    {
      object target1 = target;
      ReflectionUtils.SetMemberValue(propertyInfo, target1, value);
      ReflectionUtils.SetMemberValue(targetInfo, parent, target1);
    }

    public static List<MemberInfo> GetFieldsAndProperties<T>(BindingFlags bindingAttr) => ReflectionUtils.GetFieldsAndProperties(typeof (T), bindingAttr);

    public static List<MemberInfo> GetFieldsAndProperties(
      System.Type type,
      BindingFlags bindingAttr)
    {
      List<MemberInfo> memberInfoList = new List<MemberInfo>();
      memberInfoList.AddRange((IEnumerable<MemberInfo>) type.GetFields(bindingAttr));
      memberInfoList.AddRange((IEnumerable<MemberInfo>) type.GetProperties(bindingAttr));
      return memberInfoList;
    }

    public static FieldInfo[] GetPublicFields(this System.Type type) => type.GetFields(BindingFlags.Instance | BindingFlags.Public);

    public static PropertyInfo[] GetPublicProperties(this System.Type type) => type.GetProperties(BindingFlags.Instance | BindingFlags.Public);

    public static bool ImplementsMethod(this System.Type type, string methodName) => type.GetMethod(methodName).DeclaringType == type;
  }
}
