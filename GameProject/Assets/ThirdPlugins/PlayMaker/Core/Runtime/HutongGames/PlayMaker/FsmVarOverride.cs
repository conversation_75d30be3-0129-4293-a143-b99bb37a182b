// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmVarOverride
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmVarOverride
  {
    public NamedVariable variable;
    public FsmVar fsmVar;
    public bool isEdited;

    public string Name => this.variable == null ? "" : this.variable.Name;

    public FsmVarOverride(FsmVarOverride source)
    {
      this.variable = new NamedVariable(source.variable.Name);
      this.fsmVar = new FsmVar(source.fsmVar);
      this.isEdited = source.isEdited;
    }

    public FsmVarOverride(NamedVariable namedVar)
    {
      this.variable = namedVar;
      this.fsmVar = new FsmVar((INamedVariable) this.variable);
      this.isEdited = false;
    }

    public FsmVarOverride(NamedVariable namedVar, string variableName)
    {
      this.variable = namedVar;
      this.fsmVar = new FsmVar((INamedVariable) this.variable);
      this.fsmVar.variableName = variableName;
      this.isEdited = false;
    }

    public void Apply(FsmVariables variables)
    {
      this.variable = variables.GetVariable(this.variable.Name);
      this.fsmVar.ApplyValueTo((INamedVariable) this.variable);
    }

    public void Update(FsmVariables fromVariables, FsmVariables toVariables)
    {
      this.variable = fromVariables.GetVariable(this.variable.Name);
      this.fsmVar.NamedVar = toVariables.GetVariable(this.fsmVar.NamedVar.Name);
      this.fsmVar.GetValueFrom((INamedVariable) this.variable);
      this.fsmVar.ApplyValueTo((INamedVariable) this.fsmVar.NamedVar);
    }
  }
}
