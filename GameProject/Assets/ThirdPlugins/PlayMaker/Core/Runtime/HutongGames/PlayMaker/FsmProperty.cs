// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmProperty
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using System.Reflection;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmProperty
  {
    public FsmObject TargetObject = new FsmObject();
    public string TargetTypeName = "";
    public System.Type TargetType;
    public string PropertyName = "";
    public System.Type PropertyType;
    public FsmBool BoolParameter;
    public FsmFloat FloatParameter;
    public FsmInt IntParameter;
    public FsmGameObject GameObjectParameter;
    public FsmString StringParameter;
    public FsmVector2 Vector2Parameter;
    public FsmVector3 Vector3Parameter;
    public FsmRect RectParamater;
    public FsmQuaternion QuaternionParameter;
    public FsmObject ObjectParameter;
    public FsmMaterial MaterialParameter;
    public FsmTexture TextureParameter;
    public FsmColor ColorParameter;
    public FsmEnum EnumParameter;
    public FsmArray ArrayParameter;
    public bool setProperty;
    private bool initialized;
    [NonSerialized]
    private UnityEngine.Object targetObjectCached;
    private MemberInfo[] memberInfo;

    public FsmProperty() => this.ResetParameters();

    public FsmProperty(FsmProperty source)
    {
      this.setProperty = source.setProperty;
      this.TargetObject = new FsmObject(source.TargetObject);
      this.TargetTypeName = source.TargetTypeName;
      this.TargetType = source.TargetType;
      this.PropertyName = source.PropertyName;
      this.PropertyType = source.PropertyType;
      this.BoolParameter = new FsmBool(source.BoolParameter);
      this.FloatParameter = new FsmFloat(source.FloatParameter);
      this.IntParameter = new FsmInt(source.IntParameter);
      this.GameObjectParameter = new FsmGameObject(source.GameObjectParameter);
      this.StringParameter = new FsmString(source.StringParameter);
      this.Vector2Parameter = new FsmVector2(source.Vector2Parameter);
      this.Vector3Parameter = new FsmVector3(source.Vector3Parameter);
      this.RectParamater = new FsmRect(source.RectParamater);
      this.QuaternionParameter = new FsmQuaternion(source.QuaternionParameter);
      this.ObjectParameter = new FsmObject(source.ObjectParameter);
      this.MaterialParameter = new FsmMaterial((FsmObject) source.MaterialParameter);
      this.TextureParameter = new FsmTexture((FsmObject) source.TextureParameter);
      this.ColorParameter = new FsmColor(source.ColorParameter);
      this.EnumParameter = new FsmEnum(source.EnumParameter);
      this.ArrayParameter = new FsmArray(source.ArrayParameter);
    }

    public void SetVariable(NamedVariable variable)
    {
      if (variable == null)
      {
        this.ResetParameters();
      }
      else
      {
        switch (variable.VariableType)
        {
          case VariableType.Unknown:
            break;
          case VariableType.Float:
            this.FloatParameter = variable as FsmFloat;
            break;
          case VariableType.Int:
            this.IntParameter = variable as FsmInt;
            break;
          case VariableType.Bool:
            this.BoolParameter = variable as FsmBool;
            break;
          case VariableType.GameObject:
            this.GameObjectParameter = variable as FsmGameObject;
            break;
          case VariableType.String:
            this.StringParameter = variable as FsmString;
            break;
          case VariableType.Vector2:
            this.Vector2Parameter = variable as FsmVector2;
            break;
          case VariableType.Vector3:
            this.Vector3Parameter = variable as FsmVector3;
            break;
          case VariableType.Color:
            this.ColorParameter = variable as FsmColor;
            break;
          case VariableType.Rect:
            this.RectParamater = variable as FsmRect;
            break;
          case VariableType.Material:
            this.MaterialParameter = variable as FsmMaterial;
            break;
          case VariableType.Texture:
            this.TextureParameter = variable as FsmTexture;
            break;
          case VariableType.Quaternion:
            this.QuaternionParameter = variable as FsmQuaternion;
            break;
          case VariableType.Object:
            this.ObjectParameter = variable as FsmObject;
            break;
          case VariableType.Array:
            this.ArrayParameter = variable as FsmArray;
            break;
          case VariableType.Enum:
            this.EnumParameter = variable as FsmEnum;
            break;
          default:
            throw new ArgumentOutOfRangeException();
        }
      }
    }

    public NamedVariable GetVariable()
    {
      this.CheckForReinitialize();
      if (this.PropertyType == null)
        return (NamedVariable) null;
      if (this.PropertyType.IsAssignableFrom(typeof (bool)))
        return (NamedVariable) this.BoolParameter;
      if (this.PropertyType.IsAssignableFrom(typeof (int)))
        return (NamedVariable) this.IntParameter;
      if (this.PropertyType.IsAssignableFrom(typeof (float)))
        return (NamedVariable) this.FloatParameter;
      if (this.PropertyType.IsAssignableFrom(typeof (string)))
        return (NamedVariable) this.StringParameter;
      if (this.PropertyType.IsAssignableFrom(typeof (Vector2)))
        return (NamedVariable) this.Vector2Parameter;
      if (this.PropertyType.IsAssignableFrom(typeof (Vector3)))
        return (NamedVariable) this.Vector3Parameter;
      if (this.PropertyType.IsAssignableFrom(typeof (Rect)))
        return (NamedVariable) this.RectParamater;
      if (this.PropertyType.IsAssignableFrom(typeof (Quaternion)))
        return (NamedVariable) this.QuaternionParameter;
      if (this.PropertyType == typeof (GameObject))
        return (NamedVariable) this.GameObjectParameter;
      if (this.PropertyType == typeof (Material))
        return (NamedVariable) this.MaterialParameter;
      if (this.PropertyType == typeof (Texture))
        return (NamedVariable) this.TextureParameter;
      if (this.PropertyType == typeof (Color))
        return (NamedVariable) this.ColorParameter;
      if (this.PropertyType.IsSubclassOf(typeof (UnityEngine.Object)))
        return (NamedVariable) this.ObjectParameter;
      if (this.PropertyType.IsArray)
        return (NamedVariable) this.ArrayParameter;
      return this.PropertyType.IsEnum ? (NamedVariable) this.EnumParameter : (NamedVariable) null;
    }

    public void SetPropertyName(string propertyName)
    {
      this.ResetParameters();
      this.PropertyName = propertyName;
      if (!string.IsNullOrEmpty(this.PropertyName))
      {
        if (this.TargetType != null)
        {
          this.PropertyType = ReflectionUtils.GetPropertyType(this.TargetType, this.PropertyName);
          if (this.TargetType.IsSubclassOf(typeof (UnityEngine.Object)) && this.PropertyType != null)
            this.ObjectParameter.ObjectType = this.PropertyType;
          else if (this.PropertyType.IsArray)
            this.ArrayParameter.ElementType = FsmVar.GetVariableType(this.PropertyType.GetElementType());
        }
      }
      else
        this.PropertyType = (System.Type) null;
      this.Init();
    }

    public void SetValue()
    {
      this.CheckForReinitialize();
      if (this.targetObjectCached == (UnityEngine.Object) null || this.memberInfo == null)
        return;
      if (this.PropertyType.IsAssignableFrom(typeof (bool)) && !this.BoolParameter.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.BoolParameter.Value);
      else if (this.PropertyType.IsAssignableFrom(typeof (int)) && !this.IntParameter.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.IntParameter.Value);
      else if (this.PropertyType.IsAssignableFrom(typeof (float)) && !this.FloatParameter.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.FloatParameter.Value);
      else if (this.PropertyType.IsAssignableFrom(typeof (string)) && !this.StringParameter.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.StringParameter.Value);
      else if (this.PropertyType.IsAssignableFrom(typeof (Vector2)) && !this.Vector2Parameter.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.Vector2Parameter.Value);
      else if (this.PropertyType.IsAssignableFrom(typeof (Vector3)) && !this.Vector3Parameter.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.Vector3Parameter.Value);
      else if (this.PropertyType.IsAssignableFrom(typeof (Rect)) && !this.RectParamater.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.RectParamater.Value);
      else if (this.PropertyType.IsAssignableFrom(typeof (Quaternion)) && !this.QuaternionParameter.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.QuaternionParameter.Value);
      else if (this.PropertyType == typeof (GameObject) && !this.GameObjectParameter.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.GameObjectParameter.Value);
      else if (this.PropertyType == typeof (Material) && !this.MaterialParameter.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.MaterialParameter.Value);
      else if (this.PropertyType == typeof (Texture) && !this.TextureParameter.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.TextureParameter.Value);
      else if (this.PropertyType == typeof (Color) && !this.ColorParameter.IsNone)
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.ColorParameter.Value);
      else if (this.PropertyType.IsSubclassOf(typeof (UnityEngine.Object)) && !this.ObjectParameter.IsNone)
      {
        if (this.ObjectParameter.Value == (UnityEngine.Object) null)
          ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) null);
        else
          ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.ObjectParameter.Value);
      }
      else if (this.PropertyType.IsArray && !this.ArrayParameter.IsNone)
      {
        object[] values = this.ArrayParameter.Values;
        Array instance = Array.CreateInstance(this.PropertyType.GetElementType(), values.Length);
        for (int index = 0; index < values.Length; ++index)
          instance.SetValue(values[index], index);
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) instance);
      }
      else
      {
        if (!this.PropertyType.IsEnum || this.EnumParameter.IsNone)
          return;
        ReflectionUtils.SetMemberValue(this.memberInfo, (object) this.targetObjectCached, (object) this.EnumParameter.Value);
      }
    }

    public void GetValue()
    {
      this.CheckForReinitialize();
      if (this.targetObjectCached == (UnityEngine.Object) null || this.memberInfo == null)
        return;
      if (this.PropertyType.IsAssignableFrom(typeof (bool)))
        this.BoolParameter.Value = (bool) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType.IsAssignableFrom(typeof (int)))
        this.IntParameter.Value = (int) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType.IsAssignableFrom(typeof (float)))
        this.FloatParameter.Value = (float) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType.IsAssignableFrom(typeof (string)))
        this.StringParameter.Value = (string) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType.IsAssignableFrom(typeof (Vector2)))
        this.Vector2Parameter.Value = (Vector2) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType.IsAssignableFrom(typeof (Vector3)))
        this.Vector3Parameter.Value = (Vector3) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType.IsAssignableFrom(typeof (Rect)))
        this.RectParamater.Value = (Rect) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType.IsAssignableFrom(typeof (Quaternion)))
        this.QuaternionParameter.Value = (Quaternion) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType == typeof (GameObject))
        this.GameObjectParameter.Value = (GameObject) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType == typeof (Material))
        this.MaterialParameter.Value = (Material) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType == typeof (Texture))
        this.TextureParameter.Value = (Texture) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType == typeof (Color))
        this.ColorParameter.Value = (Color) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType.IsEnum)
        this.EnumParameter.Value = (Enum) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      else if (this.PropertyType.IsArray)
      {
        Array memberValue = (Array) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
        object[] objArray = new object[memberValue.Length];
        for (int index = 0; index < memberValue.Length; ++index)
          objArray[index] = memberValue.GetValue(index);
        this.ArrayParameter.Values = objArray;
      }
      else
      {
        if (!this.PropertyType.IsSubclassOf(typeof (UnityEngine.Object)))
          return;
        this.ObjectParameter.Value = (UnityEngine.Object) ReflectionUtils.GetMemberValue(this.memberInfo, (object) this.targetObjectCached);
      }
    }

    public void Init()
    {
      if (this.TargetObject == null)
        return;
      this.initialized = true;
      this.targetObjectCached = this.TargetObject.Value;
      if (this.TargetObject.UseVariable)
      {
        this.TargetTypeName = this.TargetObject.TypeName;
        this.TargetType = this.TargetObject.ObjectType;
      }
      else if (this.TargetObject.Value != (UnityEngine.Object) null)
      {
        this.TargetType = this.TargetObject.Value.GetType();
        this.TargetTypeName = this.TargetType.FullName;
      }
      if (!string.IsNullOrEmpty(this.PropertyName))
      {
        this.memberInfo = ReflectionUtils.GetMemberInfo(this.TargetType, this.PropertyName);
        if (this.memberInfo == null)
        {
          this.PropertyName = "";
          this.PropertyType = (System.Type) null;
          this.ResetParameters();
          return;
        }
        this.PropertyType = ReflectionUtils.GetMemberUnderlyingType(this.memberInfo[this.memberInfo.Length - 1]);
      }
      if (this.PropertyType == null || !this.PropertyType.IsEnum || FsmString.IsNullOrEmpty(this.StringParameter))
        return;
      this.EnumParameter = new FsmEnum("")
      {
        EnumType = this.PropertyType,
        Value = (Enum) Enum.Parse(this.PropertyType, this.StringParameter.Value)
      };
      this.StringParameter.Value = (string) null;
    }

    public void CheckForReinitialize()
    {
      if (this.initialized && !(this.targetObjectCached != this.TargetObject.Value) && (!this.TargetObject.UseVariable || this.TargetType == this.TargetObject.ObjectType))
        return;
      this.Init();
    }

    public void ResetParameters()
    {
      this.BoolParameter = (FsmBool) false;
      this.FloatParameter = (FsmFloat) 0.0f;
      this.IntParameter = (FsmInt) 0;
      this.StringParameter = (FsmString) "";
      this.GameObjectParameter = new FsmGameObject("");
      this.Vector2Parameter = new FsmVector2();
      this.Vector3Parameter = new FsmVector3();
      this.RectParamater = new FsmRect();
      this.QuaternionParameter = new FsmQuaternion();
      this.ObjectParameter = new FsmObject();
      this.MaterialParameter = new FsmMaterial();
      this.TextureParameter = new FsmTexture();
      this.ColorParameter = new FsmColor();
      this.EnumParameter = new FsmEnum();
      this.ArrayParameter = new FsmArray();
    }
  }
}
