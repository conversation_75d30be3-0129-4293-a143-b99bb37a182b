// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.Fsm
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.SceneManagement;
using Debug = UnityEngine.Debug;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public partial class Fsm : INameable, IComparable
  {
    public const int CurrentDataVersion = 2;
    public const int DefaultMaxLoops = 1000;
    private MethodInfo updateHelperSetDirty;
    private const string StartStateName = "State1";
    public static FsmEventData EventData = new FsmEventData();
    public Action<FsmEvent> OnOutputEvent;
    [SerializeField]
    private int dataVersion;
    [NonSerialized]
    private MonoBehaviour owner;
    [SerializeField]
    private FsmTemplate usedInTemplate;
    [SerializeField]
    private string name = "FSM";
    [SerializeField]
    private string startState;
    [SerializeField]
    private FsmState[] states = new FsmState[1];
    [SerializeField]
    private FsmEvent[] events = new FsmEvent[0];
    [SerializeField]
    private FsmTransition[] globalTransitions = new FsmTransition[0];
    [SerializeField]
    private FsmVariables variables = new FsmVariables();
    [SerializeField]
    [TextArea(0, 10)]
    private string description = "";
    [SerializeField]
    private string docUrl;
    [SerializeField]
    private bool showStateLabel;
    [SerializeField]
    private int maxLoopCount;
    [SerializeField]
    private string watermark = "";
    [SerializeField]
    private string password;
    [SerializeField]
    private bool locked;
    [SerializeField]
    private bool manualUpdate;
    [SerializeField]
    private int[] outVariableIndices = new int[0];
    [NonSerialized]
    private NamedVariable[] outputVariables;
    [SerializeField]
    private bool keepDelayedEventsOnStateExit;
    [SerializeField]
    private bool preprocessed;
    [NonSerialized]
    private Fsm host;
    [NonSerialized]
    private Fsm rootFsm;
    [NonSerialized]
    private List<Fsm> subFsmList;
    [NonSerialized]
    public bool setDirty;
    private bool activeStateEntered;
    public List<FsmEvent> ExposedEvents = new List<FsmEvent>();
    public List<FsmEvent> OutputEvents = new List<FsmEvent>();
    private FsmLog myLog;
    public bool RestartOnEnable = true;
    public bool ResetVariablesOnEnable;
    [NonSerialized]
    private FsmVariables _defaultVariableValues;
    [NonSerialized]
    private bool hasErrors;
    public bool EnableDebugFlow;
    public bool EnableBreakpoints = true;
    [NonSerialized]
    public bool StepFrame;
    [NonSerialized]
    private readonly List<HutongGames.PlayMaker.DelayedEvent> delayedEvents = new List<HutongGames.PlayMaker.DelayedEvent>();
    [NonSerialized]
    private readonly List<HutongGames.PlayMaker.DelayedEvent> updateEvents = new List<HutongGames.PlayMaker.DelayedEvent>();
    [NonSerialized]
    private readonly List<HutongGames.PlayMaker.DelayedEvent> removeEvents = new List<HutongGames.PlayMaker.DelayedEvent>();
    [SerializeField]
    private Fsm.EditorFlags editorFlags = Fsm.EditorFlags.nameIsExpanded | Fsm.EditorFlags.inputsIsExpanded | Fsm.EditorFlags.outputsIsExpanded | Fsm.EditorFlags.eventsIsExpanded;
    [NonSerialized]
    private bool initialized;
    [SerializeField]
    private string activeStateName;
    [NonSerialized]
    private FsmState activeState;
    [NonSerialized]
    private FsmState switchToState;
    [NonSerialized]
    private FsmState previousActiveState;
    public Action<FsmState> StateChanged;
    [NonSerialized]
    private FsmState editState;
    [SerializeField]
    private bool mouseEvents;
    [SerializeField]
    private bool handleLevelLoaded;
    [SerializeField]
    private bool handleTriggerEnter2D;
    [SerializeField]
    private bool handleTriggerExit2D;
    [SerializeField]
    private bool handleTriggerStay2D;
    [SerializeField]
    private bool handleCollisionEnter2D;
    [SerializeField]
    private bool handleCollisionExit2D;
    [SerializeField]
    private bool handleCollisionStay2D;
    [SerializeField]
    private bool handleTriggerEnter;
    [SerializeField]
    private bool handleTriggerExit;
    [SerializeField]
    private bool handleTriggerStay;
    [SerializeField]
    private bool handleCollisionEnter;
    [SerializeField]
    private bool handleCollisionExit;
    [SerializeField]
    private bool handleCollisionStay;
    [SerializeField]
    private bool handleParticleCollision;
    [SerializeField]
    private bool handleControllerColliderHit;
    [SerializeField]
    private bool handleJointBreak;
    [SerializeField]
    private bool handleJointBreak2D;
    [SerializeField]
    private bool handleOnGUI;
    [SerializeField]
    private bool handleFixedUpdate;
    [SerializeField]
    private bool handleLateUpdate;
    [SerializeField]
    private bool handleApplicationEvents;
    [SerializeField]
    private UiEvents handleUiEvents;
    [SerializeField]
    private bool handleLegacyNetworking;
    private static Dictionary<Fsm, RaycastHit2D> lastRaycastHit2DInfoLUT;
    [SerializeField]
    private bool handleAnimatorMove;
    [SerializeField]
    private bool handleAnimatorIK;
    private bool disableSent;
    private static readonly FsmEventTarget targetSelf = new FsmEventTarget();
    [Obsolete("Use PlayMakerPrefs.Colors instead.")]
    public static readonly Color[] StateColors = new Color[8]
    {
      Color.grey,
      new Color(0.5450981f, 0.6705883f, 0.9411765f),
      new Color(0.2431373f, 0.7607843f, 0.6901961f),
      new Color(0.4313726f, 0.7607843f, 0.2431373f),
      new Color(1f, 0.8745098f, 0.1882353f),
      new Color(1f, 0.5529412f, 0.1882353f),
      new Color(0.7607843f, 0.2431373f, 0.2509804f),
      new Color(0.5450981f, 0.2431373f, 0.7607843f)
    };

    public static List<Fsm> FsmList
    {
      get
      {
        List<Fsm> fsmList = new List<Fsm>();
        foreach (PlayMakerFSM fsm in PlayMakerFSM.FsmList)
        {
          if ((UnityEngine.Object) fsm != (UnityEngine.Object) null && fsm.Fsm != null)
            fsmList.Add(fsm.Fsm);
        }
        return fsmList;
      }
    }

    public static List<Fsm> SortedFsmList
    {
      get
      {
        List<Fsm> fsmList = Fsm.FsmList;
        fsmList.Sort();
        return fsmList;
      }
    }

    private MethodInfo UpdateHelperSetDirty => this.updateHelperSetDirty ?? (this.updateHelperSetDirty = ReflectionUtils.GetGlobalType("HutongGames.PlayMaker.UpdateHelper").GetMethod("SetDirty"));

    public void ClearOutputFlags() => this.outVariableIndices = new int[0];

    public NamedVariable[] GetOutputVariables(bool refresh = true)
    {
      if (!refresh && this.outputVariables != null)
        return this.outputVariables;
      this.variables.Reinitialize();
      NamedVariable[] allNamedVariables = this.variables.GetAllNamedVariables();
      this.outputVariables = new NamedVariable[this.outVariableIndices.Length];
      for (int index = 0; index < this.outVariableIndices.Length; ++index)
      {
        int outVariableIndex = this.outVariableIndices[index];
        if (outVariableIndex != -1 && outVariableIndex < allNamedVariables.Length)
          this.outputVariables[index] = allNamedVariables[outVariableIndex];
      }
      return this.outputVariables;
    }

    public string[] GetOutputVariableNames()
    {
      NamedVariable[] outputVariables = this.GetOutputVariables();
      string[] strArray = new string[outputVariables.Length];
      for (int index = 0; index < outputVariables.Length; ++index)
      {
        if (outputVariables[index] != null)
          strArray[index] = outputVariables[index].Name;
      }
      return strArray;
    }

    public void SetOutputVariables(string[] outputVarNames)
    {
      List<int> intList = new List<int>();
      foreach (string outputVarName in outputVarNames)
        intList.Add(this.variables.GetVariableIndex(outputVarName));
      this.outVariableIndices = intList.ToArray();
    }

    public string SanityCheckOutputIndices()
    {
      if (!this.OutVariableIndicesNeedFixing())
        return "";
      List<int> intList = new List<int>();
      foreach (int outVariableIndex in this.outVariableIndices)
      {
        if (outVariableIndex != -1)
          intList.Add(outVariableIndex);
      }
      this.outVariableIndices = intList.ToArray();
      return "\nFixed missing output variables.";
    }

    private bool OutVariableIndicesNeedFixing()
    {
      foreach (int outVariableIndex in this.outVariableIndices)
      {
        if (outVariableIndex == -1)
          return true;
      }
      return false;
    }

    public bool ManualUpdate
    {
      get => this.manualUpdate;
      set => this.manualUpdate = value;
    }

    public bool KeepDelayedEventsOnStateExit
    {
      get => this.keepDelayedEventsOnStateExit;
      set => this.keepDelayedEventsOnStateExit = value;
    }

    public bool Preprocessed
    {
      get => this.preprocessed;
      set => this.preprocessed = value;
    }

    public Fsm Host
    {
      get => this.host;
      private set => this.host = value;
    }

    public string Password => this.password;

    public bool Locked => this.locked;

    public void Lock(string pass)
    {
      if (this.Locked)
        return;
      this.password = pass;
      this.locked = true;
    }

    public void Unlock(string pass)
    {
      if (!string.IsNullOrEmpty(this.password) && !(pass == this.password))
        return;
      this.locked = false;
    }

    public FsmTemplate Template => !((UnityEngine.Object) this.Owner != (UnityEngine.Object) null) ? (FsmTemplate) null : ((PlayMakerFSM) this.Owner).FsmTemplate;

    public bool IsSubFsm => this.host != null;

    public Fsm RootFsm => this.rootFsm ?? (this.rootFsm = this.GetRootFsm());

    public List<Fsm> SubFsmList => this.subFsmList ?? (this.subFsmList = new List<Fsm>());

    public FsmVariables DefaultVariableValues
    {
      get => this._defaultVariableValues;
      private set => this._defaultVariableValues = value;
    }

    public bool HasErrors
    {
      get => this.hasErrors;
      set
      {
        if (!value)
        {
          foreach (FsmState state in this.States)
            state.HasErrors = false;
        }
        this.hasErrors = value;
      }
    }

    public bool Started { get; private set; }

    public List<HutongGames.PlayMaker.DelayedEvent> DelayedEvents => this.delayedEvents;

    public void KillDelayedEvents() => this.delayedEvents.Clear();

    public int DataVersion
    {
      get => this.dataVersion;
      set => this.dataVersion = value;
    }

    public MonoBehaviour Owner
    {
      get => this.owner;
      set => this.owner = value;
    }

    public bool NameIsExpanded
    {
      get => (uint) (this.editorFlags & Fsm.EditorFlags.nameIsExpanded) > 0U;
      set
      {
        if (value)
          this.editorFlags |= Fsm.EditorFlags.nameIsExpanded;
        else
          this.editorFlags &= ~Fsm.EditorFlags.nameIsExpanded;
      }
    }

    public bool ControlsIsExpanded
    {
      get => (uint) (this.editorFlags & Fsm.EditorFlags.controlsIsExpanded) > 0U;
      set
      {
        if (value)
          this.editorFlags |= Fsm.EditorFlags.controlsIsExpanded;
        else
          this.editorFlags &= ~Fsm.EditorFlags.controlsIsExpanded;
      }
    }

    public bool DebugIsExpanded
    {
      get => (uint) (this.editorFlags & Fsm.EditorFlags.debugIsExpanded) > 0U;
      set
      {
        if (value)
          this.editorFlags |= Fsm.EditorFlags.debugIsExpanded;
        else
          this.editorFlags &= ~Fsm.EditorFlags.debugIsExpanded;
      }
    }

    public bool ExperimentalIsExpanded
    {
      get => (uint) (this.editorFlags & Fsm.EditorFlags.experimentalIsExpanded) > 0U;
      set
      {
        if (value)
          this.editorFlags |= Fsm.EditorFlags.experimentalIsExpanded;
        else
          this.editorFlags &= ~Fsm.EditorFlags.experimentalIsExpanded;
      }
    }

    public bool InfoIsExpanded
    {
      get => (uint) (this.editorFlags & Fsm.EditorFlags.infoIsExpanded) > 0U;
      set
      {
        if (value)
          this.editorFlags |= Fsm.EditorFlags.infoIsExpanded;
        else
          this.editorFlags &= ~Fsm.EditorFlags.infoIsExpanded;
      }
    }

    public bool InputsIsExpanded
    {
      get => (uint) (this.editorFlags & Fsm.EditorFlags.inputsIsExpanded) > 0U;
      set
      {
        if (value)
          this.editorFlags |= Fsm.EditorFlags.inputsIsExpanded;
        else
          this.editorFlags &= ~Fsm.EditorFlags.inputsIsExpanded;
      }
    }

    public bool OutputsIsExpanded
    {
      get => (uint) (this.editorFlags & Fsm.EditorFlags.outputsIsExpanded) > 0U;
      set
      {
        if (value)
          this.editorFlags |= Fsm.EditorFlags.outputsIsExpanded;
        else
          this.editorFlags &= ~Fsm.EditorFlags.outputsIsExpanded;
      }
    }

    public bool EventsIsExpanded
    {
      get => (uint) (this.editorFlags & Fsm.EditorFlags.eventsIsExpanded) > 0U;
      set
      {
        if (value)
          this.editorFlags |= Fsm.EditorFlags.eventsIsExpanded;
        else
          this.editorFlags &= ~Fsm.EditorFlags.eventsIsExpanded;
      }
    }

    public bool SettingsIsExpanded
    {
      get => (uint) (this.editorFlags & Fsm.EditorFlags.settingsIsExpanded) > 0U;
      set
      {
        if (value)
          this.editorFlags |= Fsm.EditorFlags.settingsIsExpanded;
        else
          this.editorFlags &= ~Fsm.EditorFlags.settingsIsExpanded;
      }
    }
    
    public bool ConditionsIsExpanded
    {
      get => (uint) (this.editorFlags & Fsm.EditorFlags.controlsIsExpanded) > 0U;
      set
      {
        if (value)
          this.editorFlags |= Fsm.EditorFlags.controlsIsExpanded;
        else
          this.editorFlags &= ~Fsm.EditorFlags.controlsIsExpanded;
      }
    }

    public string Name
    {
      get => this.name;
      set => this.name = value;
    }

    public FsmTemplate UsedInTemplate
    {
      get => this.usedInTemplate;
      set => this.usedInTemplate = value;
    }

    public string StartState
    {
      get => this.startState;
      set => this.startState = value;
    }

    public FsmState[] States
    {
      get => this.states;
      set => this.states = value;
    }

    public FsmEvent[] Events
    {
      get => this.events;
      set => this.events = value;
    }

    public FsmTransition[] GlobalTransitions
    {
      get => this.globalTransitions;
      set => this.globalTransitions = value;
    }

    public FsmVariables Variables
    {
      get => this.variables;
      set => this.variables = value;
    }

    public FsmEventTarget EventTarget { get; set; }

    public bool Initialized => this.initialized;

    public bool Active => (UnityEngine.Object) this.owner != (UnityEngine.Object) null && (UnityEngine.Object) this.owner.gameObject != (UnityEngine.Object) null && !this.Finished && this.ActiveState != null;

    public bool Finished { get; private set; }

    public bool IsSwitchingState => this.switchToState != null;

    public FsmState ActiveState
    {
      get
      {
        if (this.activeState == null && this.activeStateName != "")
          this.activeState = this.GetState(this.activeStateName);
        return this.activeState;
      }
      private set
      {
        this.activeState = value;
        this.activeStateName = this.activeState == null ? "" : this.activeState.Name;
      }
    }

    public string ActiveStateName => this.activeStateName;

    public FsmState PreviousActiveState
    {
      get => this.previousActiveState;
      private set => this.previousActiveState = value;
    }

    public FsmTransition LastTransition { get; private set; }

    public int MaxLoopCount => this.maxLoopCount <= 0 ? 1000 : this.maxLoopCount;

    public int MaxLoopCountOverride
    {
      get => this.maxLoopCount;
      set => this.maxLoopCount = Mathf.Max(0, value);
    }

    public string OwnerName => !((UnityEngine.Object) this.owner != (UnityEngine.Object) null) ? "" : this.owner.name;

    public string OwnerDebugName
    {
      get
      {
        if (PlayMakerFSM.NotMainThread)
          return "";
        return !((UnityEngine.Object) this.owner != (UnityEngine.Object) null) ? "[missing Owner]" : this.owner.name;
      }
    }

    public GameObject GameObject => !((UnityEngine.Object) this.Owner != (UnityEngine.Object) null) ? (GameObject) null : this.Owner.gameObject;

    public string GameObjectName => !((UnityEngine.Object) this.Owner != (UnityEngine.Object) null) ? "[missing GameObject]" : this.Owner.gameObject.name;

    public UnityEngine.Object OwnerObject => (bool) (UnityEngine.Object) this.UsedInTemplate ? (UnityEngine.Object) this.UsedInTemplate : (UnityEngine.Object) this.Owner;

    public PlayMakerFSM FsmComponent => this.Owner as PlayMakerFSM;

    public FsmLog MyLog => this.myLog ?? (this.myLog = FsmLog.GetLog(this));

    public bool IsModifiedPrefabInstance { get; set; }

    public string Description
    {
      get => this.description;
      set => this.description = value;
    }

    public string Watermark
    {
      get => this.watermark;
      set => this.watermark = value;
    }

    public bool ShowStateLabel
    {
      get => this.showStateLabel;
      set => this.showStateLabel = value;
    }

    private string GuiLabel => this.OwnerName + " : " + this.Name;

    public string DocUrl
    {
      get => this.docUrl;
      set => this.docUrl = value;
    }

    public FsmState EditState
    {
      get => this.editState;
      set => this.editState = value;
    }

    public static GameObject LastClickedObject { get; set; }

    public static bool BreakpointsEnabled { get; set; }

    public static bool HitBreakpoint { get; set; }

    public static Fsm BreakAtFsm { get; private set; }

    public static FsmState BreakAtState { get; private set; }

    public static bool IsBreak { get; private set; }

    public static bool IsErrorBreak { get; private set; }

    public static string LastError { get; private set; }

    public static bool StepToStateChange { get; set; }

    public static Fsm StepFsm { get; set; }

    public bool SwitchedState { get; set; }

    public bool MouseEvents
    {
      get => this.mouseEvents;
      set
      {
        this.preprocessed = false;
        this.mouseEvents = value;
        if (this.host == null)
          return;
        this.host.MouseEvents |= value;
      }
    }

    public bool HandleLevelLoaded
    {
      get => this.handleLevelLoaded;
      set
      {
        this.handleLevelLoaded = value;
        if (this.host == null)
          return;
        this.host.HandleLevelLoaded |= value;
      }
    }

    public bool HandleTriggerEnter2D
    {
      get => this.handleTriggerEnter2D;
      set
      {
        this.preprocessed = false;
        this.handleTriggerEnter2D = value;
        if (this.host == null)
          return;
        this.host.HandleTriggerEnter2D |= value;
      }
    }

    public bool HandleTriggerExit2D
    {
      get => this.handleTriggerExit2D;
      set
      {
        this.preprocessed = false;
        this.handleTriggerExit2D = value;
        if (this.host == null)
          return;
        this.host.HandleTriggerExit2D |= value;
      }
    }

    public bool HandleTriggerStay2D
    {
      get => this.handleTriggerStay2D;
      set
      {
        this.preprocessed = false;
        this.handleTriggerStay2D = value;
        if (this.host == null)
          return;
        this.host.HandleTriggerStay2D |= value;
      }
    }

    public bool HandleCollisionEnter2D
    {
      get => this.handleCollisionEnter2D;
      set
      {
        this.preprocessed = false;
        this.handleCollisionEnter2D = value;
        if (this.host == null)
          return;
        this.host.HandleCollisionEnter2D |= value;
      }
    }

    public bool HandleCollisionExit2D
    {
      get => this.handleCollisionExit2D;
      set
      {
        this.preprocessed = false;
        this.handleCollisionExit2D = value;
        if (this.host == null)
          return;
        this.host.HandleCollisionExit2D |= value;
      }
    }

    public bool HandleCollisionStay2D
    {
      get => this.handleCollisionStay2D;
      set
      {
        this.preprocessed = false;
        this.handleCollisionStay2D = value;
        if (this.host == null)
          return;
        this.host.HandleCollisionStay2D |= value;
      }
    }

    public bool HandleTriggerEnter
    {
      get => this.handleTriggerEnter;
      set
      {
        this.preprocessed = false;
        this.handleTriggerEnter = value;
        if (this.host == null)
          return;
        this.host.HandleTriggerEnter |= value;
      }
    }

    public bool HandleTriggerExit
    {
      get => this.handleTriggerExit;
      set
      {
        this.preprocessed = false;
        this.handleTriggerExit = value;
        if (this.host == null)
          return;
        this.host.HandleTriggerExit |= value;
      }
    }

    public bool HandleTriggerStay
    {
      get => this.handleTriggerStay;
      set
      {
        this.preprocessed = false;
        this.handleTriggerStay = value;
        if (this.host == null)
          return;
        this.host.HandleTriggerStay |= value;
      }
    }

    public bool HandleCollisionEnter
    {
      get => this.handleCollisionEnter;
      set
      {
        this.preprocessed = false;
        this.handleCollisionEnter = value;
        if (this.host == null)
          return;
        this.host.HandleCollisionEnter |= value;
      }
    }

    public bool HandleCollisionExit
    {
      get => this.handleCollisionExit;
      set
      {
        this.preprocessed = false;
        this.handleCollisionExit = value;
        if (this.host == null)
          return;
        this.host.HandleCollisionExit |= value;
      }
    }

    public bool HandleCollisionStay
    {
      get => this.handleCollisionStay;
      set
      {
        this.preprocessed = false;
        this.handleCollisionStay = value;
        if (this.host == null)
          return;
        this.host.HandleCollisionStay |= value;
      }
    }

    public bool HandleParticleCollision
    {
      get => this.handleParticleCollision;
      set
      {
        this.preprocessed = false;
        this.handleParticleCollision = value;
        if (this.host == null)
          return;
        this.host.HandleParticleCollision |= value;
      }
    }

    public bool HandleControllerColliderHit
    {
      get => this.handleControllerColliderHit;
      set
      {
        this.preprocessed = false;
        this.handleControllerColliderHit = value;
        if (this.host == null)
          return;
        this.host.HandleControllerColliderHit |= value;
      }
    }

    public bool HandleJointBreak
    {
      get => this.handleJointBreak;
      set
      {
        this.preprocessed = false;
        this.handleJointBreak = value;
        if (this.host == null)
          return;
        this.host.HandleJointBreak |= value;
      }
    }

    public bool HandleJointBreak2D
    {
      get => this.handleJointBreak2D;
      set
      {
        this.preprocessed = false;
        this.handleJointBreak2D = value;
        if (this.host == null)
          return;
        this.host.HandleJointBreak2D |= value;
      }
    }

    public bool HandleOnGUI
    {
      get => this.handleOnGUI;
      set
      {
        this.preprocessed = false;
        this.handleOnGUI = value;
        if (this.host == null)
          return;
        this.host.HandleOnGUI |= value;
      }
    }

    public bool HandleFixedUpdate
    {
      get => this.handleFixedUpdate;
      set
      {
        this.preprocessed = false;
        this.handleFixedUpdate = value;
        if (this.host == null)
          return;
        this.host.HandleFixedUpdate |= value;
      }
    }

    public bool HandleLateUpdate
    {
      get => this.handleLateUpdate;
      set
      {
        this.preprocessed = false;
        this.handleLateUpdate = value;
        if (this.host == null)
          return;
        this.host.HandleLateUpdate |= value;
      }
    }

    public bool HandleApplicationEvents
    {
      get => this.handleApplicationEvents;
      set
      {
        this.preprocessed = false;
        this.handleApplicationEvents = value;
        if (this.host == null)
          return;
        this.host.HandleApplicationEvents |= value;
      }
    }

    public UiEvents HandleUiEvents
    {
      get => this.handleUiEvents;
      set
      {
        this.preprocessed = false;
        this.handleUiEvents = value;
        if (this.host == null)
          return;
        this.host.HandleUiEvents |= value;
      }
    }

    public bool HandleLegacyNetworking
    {
      get => this.handleLegacyNetworking;
      set
      {
        this.preprocessed = false;
        this.handleLegacyNetworking = value;
        if (this.host == null)
          return;
        this.host.HandleLegacyNetworking |= value;
      }
    }

    public void ForcePreprocess() => this.ResetEventHandlerFlags();

    private void ResetEventHandlerFlags()
    {
      this.handleApplicationEvents = false;
      this.handleCollisionEnter = false;
      this.HandleCollisionExit = false;
      this.handleCollisionStay = false;
      this.handleCollisionEnter2D = false;
      this.HandleCollisionExit2D = false;
      this.handleCollisionStay2D = false;
      this.handleTriggerEnter = false;
      this.handleTriggerExit = false;
      this.handleTriggerStay = false;
      this.handleTriggerEnter2D = false;
      this.handleTriggerExit2D = false;
      this.handleTriggerStay2D = false;
      this.handleControllerColliderHit = false;
      this.handleFixedUpdate = false;
      this.handleLateUpdate = false;
      this.handleOnGUI = false;
      this.handleAnimatorIK = false;
      this.handleAnimatorMove = false;
      this.handleJointBreak = false;
      this.handleJointBreak2D = false;
      this.handleParticleCollision = false;
      this.handleLegacyNetworking = false;
      this.handleUiEvents = UiEvents.None;
      this.preprocessed = false;
    }

    public Collision CollisionInfo { get; set; }

    public Collider TriggerCollider { get; set; }

    public Collision2D Collision2DInfo { get; set; }

    public Collider2D TriggerCollider2D { get; set; }

    public float JointBreakForce { get; private set; }

    public Joint2D BrokenJoint2D { get; private set; }

    public GameObject ParticleCollisionGO { get; set; }

    public GameObject CollisionGO => this.CollisionInfo == null ? (GameObject) null : this.CollisionInfo.gameObject;

    public GameObject Collision2dGO => this.Collision2DInfo == null ? (GameObject) null : this.Collision2DInfo.gameObject;

    public GameObject TriggerGO => !((UnityEngine.Object) this.TriggerCollider != (UnityEngine.Object) null) ? (GameObject) null : this.TriggerCollider.gameObject;

    public GameObject Trigger2dGO => !((UnityEngine.Object) this.TriggerCollider2D != (UnityEngine.Object) null) ? (GameObject) null : this.TriggerCollider2D.gameObject;

    public string TriggerName { get; private set; }

    public string CollisionName { get; private set; }

    public string Trigger2dName { get; private set; }

    public string Collision2dName { get; private set; }

    public ControllerColliderHit ControllerCollider { get; set; }

    public RaycastHit RaycastHitInfo { get; set; }

    public static void RecordLastRaycastHit2DInfo(Fsm fsm, RaycastHit2D info)
    {
      if (Fsm.lastRaycastHit2DInfoLUT == null)
        Fsm.lastRaycastHit2DInfoLUT = new Dictionary<Fsm, RaycastHit2D>();
      Fsm.lastRaycastHit2DInfoLUT[fsm] = info;
    }

    public static RaycastHit2D GetLastRaycastHit2DInfo(Fsm fsm) => Fsm.lastRaycastHit2DInfoLUT == null ? new RaycastHit2D() : Fsm.lastRaycastHit2DInfoLUT[fsm];

    public bool HandleAnimatorMove
    {
      get => this.handleAnimatorMove;
      set
      {
        this.preprocessed = false;
        this.handleAnimatorMove = value;
        if (this.host == null)
          return;
        this.host.HandleAnimatorMove |= value;
      }
    }

    public bool HandleAnimatorIK
    {
      get => this.handleAnimatorIK;
      set
      {
        this.preprocessed = false;
        this.handleAnimatorIK = value;
        if (this.host == null)
          return;
        this.host.HandleAnimatorIK |= value;
      }
    }

    public static Fsm NewTempFsm() => new Fsm()
    {
      dataVersion = 2
    };

    public Fsm()
    {
    }

    public Fsm(Fsm source, FsmVariables overrideVariables = null)
    {
      this.dataVersion = source.DataVersion;
      this.owner = source.Owner;
      this.name = source.Name;
      this.description = source.Description;
      this.startState = source.StartState;
      this.docUrl = source.docUrl;
      this.showStateLabel = source.showStateLabel;
      this.maxLoopCount = source.maxLoopCount;
      this.watermark = source.Watermark;
      this.RestartOnEnable = source.RestartOnEnable;
      this.EnableDebugFlow = source.EnableDebugFlow;
      this.EnableBreakpoints = source.EnableBreakpoints;
      this.ResetVariablesOnEnable = source.ResetVariablesOnEnable;
      this.states = new FsmState[source.States.Length];
      for (int index = 0; index < source.States.Length; ++index)
      {
        source.States[index].Fsm = source;
        this.states[index] = new FsmState(source.States[index]);
      }
      this.events = new FsmEvent[source.Events.Length];
      for (int index = 0; index < source.Events.Length; ++index)
        this.events[index] = new FsmEvent(source.Events[index]);
      this.ExposedEvents = new List<FsmEvent>();
      foreach (FsmEvent exposedEvent in source.ExposedEvents)
        this.ExposedEvents.Add(new FsmEvent(exposedEvent));
      this.OutputEvents = new List<FsmEvent>();
      foreach (FsmEvent outputEvent in source.OutputEvents)
        this.OutputEvents.Add(new FsmEvent(outputEvent));
      this.outVariableIndices = new int[source.outVariableIndices.Length];
      Array.Copy((Array) source.outVariableIndices, (Array) this.outVariableIndices, this.outVariableIndices.Length);
      this.globalTransitions = new FsmTransition[source.globalTransitions.Length];
      for (int index = 0; index < this.globalTransitions.Length; ++index)
        this.globalTransitions[index] = new FsmTransition(source.globalTransitions[index]);
      this.variables = new FsmVariables(source.Variables);
      if (overrideVariables == null)
        return;
      this.variables.OverrideVariableValues(overrideVariables);
    }

    public Fsm CreateSubFsm(FsmTemplateControl templateControl)
    {
      Fsm fsm = templateControl.InstantiateFsm();
      fsm.Host = this;
      fsm.Init(this.Owner);
      templateControl.ID = this.SubFsmList.Count;
      this.SubFsmList.Add(fsm);
      return fsm;
    }

    private Fsm GetRootFsm()
    {
      Fsm fsm = this;
      while (fsm.Host != null)
        fsm = fsm.Host;
      return fsm;
    }

    public void CheckIfDirty()
    {
      if (!this.setDirty || this.Owner == null && this.UsedInTemplate == null)
        return;
      this.UpdateHelperSetDirty.Invoke((object) null, new object[1]
      {
        (object) this
      });
      this.setDirty = false;
    }

    public void Reset(MonoBehaviour component)
    {
      this.dataVersion = 2;
      this.owner = component;
      this.name = "FSM";
      this.description = "";
      this.docUrl = "";
      this.globalTransitions = new FsmTransition[0];
      this.events = new FsmEvent[0];
      this.variables = new FsmVariables();
      this.states = new FsmState[1];
      this.States[0] = new FsmState(this)
      {
        Fsm = this,
        Name = "State1",
        Position = new Rect(50f, 100f, 100f, 16f)
      };
      this.startState = "State1";
      this.outVariableIndices = new int[0];
      this.ExposedEvents = new List<FsmEvent>();
      this.OutputEvents = new List<FsmEvent>();
      this.EnableDebugFlow = false;
      this.EnableBreakpoints = true;
      this.ResetVariablesOnEnable = false;
    }

    public void UpdateDataVersion()
    {
      this.dataVersion = 2;
      this.SaveActions();
    }

    public void SaveActions()
    {
      foreach (FsmState state in this.States)
        state.SaveActions();
    }

    public void Clear(MonoBehaviour component)
    {
      this.dataVersion = 2;
      this.owner = component;
      this.description = "";
      this.docUrl = "";
      this.globalTransitions = new FsmTransition[0];
      this.events = new FsmEvent[0];
      this.variables = new FsmVariables();
      this.states = new FsmState[1];
      this.States[0] = new FsmState(this)
      {
        Fsm = this,
        Name = "State1",
        Position = new Rect(50f, 100f, 100f, 16f)
      };
      this.startState = "State1";
      this.outVariableIndices = new int[0];
    }

    private void FixDataVersion()
    {
      this.dataVersion = this.DeduceDataVersion();
      if (PlayMakerGlobals.IsBuilding)
        return;
      this.setDirty = true;
    }

    private int DeduceDataVersion()
    {
      foreach (FsmState state in this.States)
      {
        if (state.ActionData.UsesDataVersion2())
          return 2;
      }
      foreach (FsmState state in this.States)
      {
        if (state.ActionData.ActionCount > 0)
          return 1;
      }
      return 2;
    }

    public void Preprocess(MonoBehaviour component)
    {
      this.ResetEventHandlerFlags();
      this.owner = component;
      if (PlayMakerGlobals.IsBuilding)
        this.Reinitialize();
      else
        this.InitData();
      this.Preprocess();
    }

    private void Preprocess()
    {
      foreach (FsmState state in this.states)
      {
        foreach (FsmStateAction action in state.Actions)
        {
          action.Init(state);
          action.OnPreprocess();
        }
      }
      this.CheckFsmEventsForEventHandlers();
      this.preprocessed = true;
    }

    private void Awake()
    {
      foreach (FsmState state in this.states)
      {
        foreach (FsmStateAction action in state.Actions)
        {
          action.Init(state);
          action.Awake();
        }
      }
      if (this.preprocessed)
        return;
      this.CheckFsmEventsForEventHandlers();
    }

    public void Init(MonoBehaviour component)
    {
      this.owner = component;
      this.InitData();
      if (!this.preprocessed)
        this.Preprocess();
      this.Awake();
    }

    public void Reinitialize()
    {
      if (Application.isPlaying)
        UnityEngine.Debug.LogWarning((object) "PlayMaker: Reinitializing FSM while playing.\nThis could lead to unexpected behaviour!");
      this.initialized = false;
      this.variables.Reinitialize();
      this.InitData();
    }

    public void InitStates()
    {
      foreach (FsmState state in this.states)
        state.Fsm = this;
    }

    public void InitData()
    {
      if (this.dataVersion == 0)
        this.FixDataVersion();
      if (this.Initialized)
        return;
      this.initialized = true;
      this.InitEvents();
      foreach (FsmState state in this.states)
      {
        state.Fsm = this;
        state.LoadActions();
        foreach (FsmTransition transition in state.Transitions)
        {
          if (!string.IsNullOrEmpty(transition.ToState))
            transition.ToFsmState = this.GetState(transition.ToState);
          if (!string.IsNullOrEmpty(transition.EventName))
          {
            FsmEvent fsmEvent = FsmEvent.GetFsmEvent(transition.EventName);
            transition.FsmEvent = fsmEvent;
          }
        }
      }
      foreach (FsmTransition globalTransition in this.globalTransitions)
      {
        if (!string.IsNullOrEmpty(globalTransition.ToState))
          globalTransition.ToFsmState = this.GetState(globalTransition.ToState);
        if (!string.IsNullOrEmpty(globalTransition.EventName))
        {
          FsmEvent fsmEvent = FsmEvent.GetFsmEvent(globalTransition.EventName);
          globalTransition.FsmEvent = fsmEvent;
        }
      }
      this.CheckIfDirty();
    }

    public void InitEvents()
    {
      for (int index = 0; index < this.events.Length; ++index)
        this.events[index] = FsmEvent.GetFsmEvent(this.events[index]);
      for (int index = 0; index < this.OutputEvents.Count; ++index)
      {
        if (this.OutputEvents[index] != null)
          this.OutputEvents[index] = FsmEvent.GetFsmEvent(this.OutputEvents[index]);
      }
      for (int index = 0; index < this.ExposedEvents.Count; ++index)
      {
        if (this.ExposedEvents[index] != null)
          this.ExposedEvents[index] = FsmEvent.GetFsmEvent(this.ExposedEvents[index]);
      }
    }

    private void CheckFsmEventsForEventHandlers()
    {
      foreach (FsmEvent fsmEvent in this.Events)
      {
        if (fsmEvent != null && fsmEvent.IsSystemEvent)
        {
          if (fsmEvent == FsmEvent.TriggerEnter)
            this.RootFsm.HandleTriggerEnter = true;
          else if (fsmEvent == FsmEvent.TriggerExit)
            this.RootFsm.HandleTriggerExit = true;
          else if (fsmEvent == FsmEvent.TriggerStay)
            this.RootFsm.HandleTriggerStay = true;
          else if (fsmEvent == FsmEvent.CollisionEnter)
            this.RootFsm.HandleCollisionEnter = true;
          else if (fsmEvent == FsmEvent.CollisionExit)
            this.RootFsm.HandleCollisionExit = true;
          else if (fsmEvent == FsmEvent.CollisionStay)
            this.RootFsm.HandleCollisionStay = true;
          else if (fsmEvent == FsmEvent.TriggerEnter2D)
            this.RootFsm.HandleTriggerEnter2D = true;
          else if (fsmEvent == FsmEvent.TriggerExit2D)
            this.RootFsm.HandleTriggerExit2D = true;
          else if (fsmEvent == FsmEvent.TriggerStay2D)
            this.RootFsm.HandleTriggerStay2D = true;
          else if (fsmEvent == FsmEvent.CollisionEnter2D)
            this.RootFsm.HandleCollisionEnter2D = true;
          else if (fsmEvent == FsmEvent.CollisionExit2D)
            this.RootFsm.HandleCollisionExit2D = true;
          else if (fsmEvent == FsmEvent.CollisionStay2D)
            this.RootFsm.HandleCollisionStay2D = true;
          else if (fsmEvent == FsmEvent.ParticleCollision)
            this.RootFsm.HandleParticleCollision = true;
          else if (fsmEvent == FsmEvent.ControllerColliderHit)
            this.RootFsm.HandleControllerColliderHit = true;
          else if (fsmEvent == FsmEvent.JointBreak)
            this.RootFsm.HandleJointBreak = true;
          else if (fsmEvent == FsmEvent.JointBreak2D)
            this.RootFsm.HandleJointBreak2D = true;
          else if (fsmEvent.IsMouseEvent)
            this.RootFsm.MouseEvents = true;
          else if (fsmEvent.IsApplicationEvent)
            this.RootFsm.HandleApplicationEvents = true;
          else if (fsmEvent.IsLegacyNetworkEvent)
            this.RootFsm.HandleLegacyNetworking = true;
          else if (fsmEvent == FsmEvent.LevelLoaded)
            this.RootFsm.HandleLevelLoaded = true;
          else if (fsmEvent == FsmEvent.UiClick)
            this.RootFsm.HandleUiEvents |= UiEvents.Click;
          else if (fsmEvent == FsmEvent.UiBeginDrag)
            this.RootFsm.HandleUiEvents |= UiEvents.BeginDrag;
          else if (fsmEvent == FsmEvent.UiDrag)
            this.RootFsm.HandleUiEvents |= UiEvents.Drag;
          else if (fsmEvent == FsmEvent.UiEndDrag)
            this.RootFsm.HandleUiEvents |= UiEvents.EndDrag;
          else if (fsmEvent == FsmEvent.UiDrop)
            this.RootFsm.HandleUiEvents |= UiEvents.Drop;
          else if (fsmEvent == FsmEvent.UiPointerClick)
            this.RootFsm.HandleUiEvents |= UiEvents.PointerClick;
          else if (fsmEvent == FsmEvent.UiPointerDown)
            this.RootFsm.HandleUiEvents |= UiEvents.PointerDown;
          else if (fsmEvent == FsmEvent.UiPointerEnter)
            this.RootFsm.HandleUiEvents |= UiEvents.PointerEnter;
          else if (fsmEvent == FsmEvent.UiPointerExit)
            this.RootFsm.HandleUiEvents |= UiEvents.PointerExit;
          else if (fsmEvent == FsmEvent.UiPointerUp)
            this.RootFsm.HandleUiEvents |= UiEvents.PointerUp;
          else if (fsmEvent == FsmEvent.UiBoolValueChanged)
            this.RootFsm.HandleUiEvents |= UiEvents.BoolValueChanged;
          else if (fsmEvent == FsmEvent.UiFloatValueChanged)
            this.RootFsm.HandleUiEvents |= UiEvents.FloatValueChanged;
          else if (fsmEvent == FsmEvent.UiIntValueChanged)
            this.RootFsm.HandleUiEvents |= UiEvents.IntValueChanged;
          else if (fsmEvent == FsmEvent.UiVector2ValueChanged)
            this.RootFsm.HandleUiEvents |= UiEvents.Vector2ValueChanged;
          else if (fsmEvent == FsmEvent.UiEndEdit)
            this.RootFsm.HandleUiEvents |= UiEvents.EndEdit;
        }
      }
      foreach (Fsm subFsm in this.SubFsmList)
        subFsm.CheckFsmEventsForEventHandlers();
    }

    public void OnEnable()
    {
      this.Finished = false;
      this.disableSent = false;
      if (this.ResetVariablesOnEnable)
      {
        if (this.DefaultVariableValues == null)
          this.DefaultVariableValues = new FsmVariables(this.variables);
        else
          this.variables.ApplyVariableValues(this.DefaultVariableValues);
      }
      if (this.HandleLevelLoaded)
      {
        SceneManager.sceneLoaded -= new UnityAction<Scene, LoadSceneMode>(this.OnSceneLoaded);
        SceneManager.sceneLoaded += new UnityAction<Scene, LoadSceneMode>(this.OnSceneLoaded);
      }
      if (this.ActiveState != null && !this.RestartOnEnable)
        return;
      if (this.ActiveState != null)
        this.ActiveState.OnExit();
      this.ActiveState = this.GetState(this.startState);
      this.activeStateEntered = false;
      if (!this.Started)
        return;
      this.Start();
    }

    private void OnStateActived(string stateID)
    {
      string[] strs = stateID.Split('_');
      if (strs[0] == Name)
      {
        var stateName = strs[1];
        SwitchState(GetState(stateName));
      }
    }

    public void SaveDefaultVariableValues() => this.DefaultVariableValues = new FsmVariables(this.variables);

    public void ResetVariableValues()
    {
      if (this.DefaultVariableValues == null)
        UnityEngine.Debug.LogWarning((object) "Default variable values not saved. Call SaveDefaultVariableValues before ResetVariableValues.");
      else
        this.variables.ApplyVariableValues(this.DefaultVariableValues);
    }

    private void OnSceneLoaded(Scene scene, LoadSceneMode loadSceneMode) => this.Event(FsmEvent.LevelLoaded);

    public void Start()
    {
      if (FsmLog.LoggingEnabled)
        this.MyLog.LogStart(this.ActiveState);
      
      FsmEventCenter.Instance.OnEnterStateEvent += OnStateActived;
      
      this.Started = true;
      this.Finished = false;
      int stackCount1 = FsmExecutionStack.StackCount;
      FsmExecutionStack.PushFsm(this);
      if (this.ActiveState == null)
      {
        this.ActiveState = this.GetState(this.startState);
        this.activeStateEntered = false;
      }
      if (Fsm.BreakpointsEnabled && this.EnableBreakpoints && this.ActiveState.IsBreakpoint)
      {
        this.DoBreakpoint();
      }
      else
      {
        this.switchToState = this.ActiveState;
        this.UpdateStateChanges();
      }
      FsmExecutionStack.PopFsm();
      int stackCount2 = FsmExecutionStack.StackCount;
      if (stackCount2 == stackCount1)
        return;
      UnityEngine.Debug.LogError((object) ("Stack error: " + (object) (stackCount2 - stackCount1)));
    }

    public void Update()
    {
      if ((UnityEngine.Object) this.owner == (UnityEngine.Object) null)
        return;
      int stackCount1 = FsmExecutionStack.StackCount;
      if (Fsm.HitBreakpoint)
        return;
      FsmExecutionStack.PushFsm(this);
      if (!this.activeStateEntered)
        this.Continue();
      this.UpdateDelayedEvents();
      if (this.ActiveState != null)
        this.UpdateState(this.ActiveState);
      FsmExecutionStack.PopFsm();
      int stackCount2 = FsmExecutionStack.StackCount;
      if (stackCount2 == stackCount1)
        return;
      UnityEngine.Debug.LogError((object) ("Stack error: " + (object) (stackCount2 - stackCount1)));
    }

    public void UpdateDelayedEvents()
    {
      this.removeEvents.Clear();
      this.updateEvents.Clear();
      this.updateEvents.AddRange((IEnumerable<HutongGames.PlayMaker.DelayedEvent>) this.delayedEvents);
      for (int index = 0; index < this.updateEvents.Count; ++index)
      {
        HutongGames.PlayMaker.DelayedEvent updateEvent = this.updateEvents[index];
        updateEvent.Update();
        if (updateEvent.Finished)
          this.removeEvents.Add(updateEvent);
      }
      for (int index = 0; index < this.removeEvents.Count; ++index)
        this.delayedEvents.Remove(this.removeEvents[index]);
    }

    public void ClearDelayedEvents() => this.delayedEvents.Clear();

    public void FixedUpdate()
    {
      FsmExecutionStack.PushFsm(this);
      if (this.ActiveState != null && this.activeStateEntered)
        this.FixedUpdateState(this.ActiveState);
      FsmExecutionStack.PopFsm();
    }

    public void LateUpdate()
    {
      FsmExecutionStack.PushFsm(this);
      if (this.ActiveState != null && this.activeStateEntered)
        this.LateUpdateState(this.ActiveState);
      FsmExecutionStack.PopFsm();
    }

    public void OnDisable()
    {
      this.OnOutputEvent = (Action<FsmEvent>) null;
      this.Stop();
      if (!this.HandleLevelLoaded)
        return;
      SceneManager.sceneLoaded -= new UnityAction<Scene, LoadSceneMode>(this.OnSceneLoaded);
    }

    public void SendDisableEvent()
    {
      if (this.disableSent)
        return;
      this.disableSent = true;
      this.ProcessEvent(FsmEvent.Disable);
    }

    public void Stop()
    {
      FsmEventCenter.Instance.OnEnterStateEvent -= OnStateActived;
      
      this.SendDisableEvent();
      if (this.RestartOnEnable)
        this.StopAndReset();
      this.Finished = true;
      if (!FsmLog.LoggingEnabled)
        return;
      this.MyLog.LogStop();
    }

    private void StopAndReset()
    {
      FsmExecutionStack.PushFsm(this);
      if (this.ActiveState != null && this.activeStateEntered)
        this.ExitState(this.ActiveState);
      this.ActiveState = (FsmState) null;
      this.LastTransition = (FsmTransition) null;
      this.SwitchedState = false;
      Fsm.HitBreakpoint = false;
      FsmExecutionStack.PopFsm();
    }

    public bool HasEvent(string eventName)
    {
      if (string.IsNullOrEmpty(eventName))
        return false;
      foreach (FsmEvent fsmEvent in this.events)
      {
        if (fsmEvent.Name == eventName)
          return true;
      }
      return false;
    }

    public void ProcessEvent(FsmEvent fsmEvent, FsmEventData eventData = null)
    {
      if (!this.Active || FsmEvent.IsNullOrEmpty(fsmEvent))
        return;
      if (!this.Started)
        this.Start();
      if (!this.Active)
        return;
      if (eventData != null)
        Fsm.SetEventDataSentByInfo(eventData);
      FsmExecutionStack.PushFsm(this);
      if (this.IsSwitchingState || this.ActiveState.HandlesOnEvent && this.ActiveState.OnEvent(fsmEvent))
      {
        FsmExecutionStack.PopFsm();
      }
      else
      {
        foreach (FsmTransition globalTransition in this.globalTransitions)
        {
          if (globalTransition.FsmEvent == fsmEvent)
          {
            if (FsmLog.LoggingEnabled)
              this.MyLog.LogEvent(fsmEvent, this.activeState);
            if (this.DoTransition(globalTransition, true))
            {
              FsmExecutionStack.PopFsm();
              return;
            }
          }
        }
        foreach (FsmTransition transition in this.ActiveState.Transitions)
        {
          if (transition.FsmEvent == fsmEvent)
          {
            if (FsmLog.LoggingEnabled)
              this.MyLog.LogEvent(fsmEvent, this.activeState);
            if (this.DoTransition(transition, false))
            {
              FsmExecutionStack.PopFsm();
              return;
            }
          }
        }
        FsmExecutionStack.PopFsm();
        if (this.OnOutputEvent == null || !this.OutputEvents.Contains(fsmEvent))
          return;
        this.OnOutputEvent(fsmEvent);
      }
    }

    public static void SetEventDataSentByInfo()
    {
      Fsm.EventData.SentByFsm = FsmExecutionStack.ExecutingFsm;
      Fsm.EventData.SentByState = FsmExecutionStack.ExecutingState;
      Fsm.EventData.SentByAction = FsmExecutionStack.ExecutingAction;
    }

    private static void SetEventDataSentByInfo(FsmEventData eventData)
    {
      Fsm.EventData.SentByFsm = eventData.SentByFsm;
      Fsm.EventData.SentByState = eventData.SentByState;
      Fsm.EventData.SentByAction = eventData.SentByAction;
    }

    private static FsmEventData GetEventDataSentByInfo() => new FsmEventData()
    {
      SentByFsm = FsmExecutionStack.ExecutingFsm,
      SentByState = FsmExecutionStack.ExecutingState,
      SentByAction = FsmExecutionStack.ExecutingAction
    };

    public void Event(FsmEventTarget eventTarget, string fsmEventName)
    {
      if (string.IsNullOrEmpty(fsmEventName))
        return;
      this.Event(eventTarget, FsmEvent.GetFsmEvent(fsmEventName));
    }

    public void Event(GameObject fromGameObject, FsmEventTarget eventTarget, FsmEvent fsmEvent)
    {
      Fsm.EventData.SentByGameObject = fromGameObject;
      this.Event(eventTarget, fsmEvent);
    }

    public void Event(GameObject fromGameObject, FsmEvent fsmEvent)
    {
      Fsm.EventData.SentByGameObject = fromGameObject;
      this.Event(fsmEvent);
    }

    public void Event(FsmEventTarget eventTarget, FsmEvent fsmEvent)
    {
      Fsm.SetEventDataSentByInfo();
      if (eventTarget == null)
        eventTarget = Fsm.targetSelf;
      if (FsmLog.LoggingEnabled && eventTarget.target != FsmEventTarget.EventTarget.Self)
        this.MyLog.LogSendEvent(this.activeState, fsmEvent, eventTarget);
      switch (eventTarget.target)
      {
        case FsmEventTarget.EventTarget.Self:
          this.ProcessEvent(fsmEvent);
          break;
        case FsmEventTarget.EventTarget.GameObject:
          this.BroadcastEventToGameObject(this.GetOwnerDefaultTarget(eventTarget.gameObject), fsmEvent, Fsm.EventData, eventTarget.sendToChildren.Value, eventTarget.excludeSelf.Value);
          break;
        case FsmEventTarget.EventTarget.GameObjectFSM:
          this.SendEventToFsmOnGameObject(this.GetOwnerDefaultTarget(eventTarget.gameObject), eventTarget.fsmName.Value, fsmEvent);
          break;
        case FsmEventTarget.EventTarget.FSMComponent:
          if ((UnityEngine.Object) eventTarget.fsmComponent != (UnityEngine.Object) null)
          {
            eventTarget.fsmComponent.Fsm.ProcessEvent(fsmEvent);
            break;
          }
          break;
        case FsmEventTarget.EventTarget.BroadcastAll:
          this.BroadcastEvent(fsmEvent, eventTarget.excludeSelf.Value);
          break;
        case FsmEventTarget.EventTarget.HostFSM:
          if (this.Host != null)
          {
            this.Host.ProcessEvent(fsmEvent);
            break;
          }
          break;
        case FsmEventTarget.EventTarget.SubFSMs:
          using (List<Fsm>.Enumerator enumerator = new List<Fsm>((IEnumerable<Fsm>) this.SubFsmList).GetEnumerator())
          {
            while (enumerator.MoveNext())
              enumerator.Current.ProcessEvent(fsmEvent);
            break;
          }
      }
      if (FsmExecutionStack.ExecutingFsm == this)
        return;
      FsmExecutionStack.PushFsm(this);
      this.UpdateStateChanges();
      FsmExecutionStack.PopFsm();
    }

    public void Event(string fsmEventName)
    {
      if (string.IsNullOrEmpty(fsmEventName))
        return;
      this.Event(FsmEvent.GetFsmEvent(fsmEventName));
    }

    public void Event(FsmEvent fsmEvent)
    {
      if (fsmEvent == null)
        return;
      this.Event(this.EventTarget, fsmEvent);
    }

    public HutongGames.PlayMaker.DelayedEvent DelayedEvent(
      FsmEvent fsmEvent,
      float delay)
    {
      HutongGames.PlayMaker.DelayedEvent delayedEvent = new HutongGames.PlayMaker.DelayedEvent(this, fsmEvent, delay);
      this.delayedEvents.Add(delayedEvent);
      return delayedEvent;
    }

    public HutongGames.PlayMaker.DelayedEvent DelayedEvent(
      FsmEventTarget eventTarget,
      FsmEvent fsmEvent,
      float delay)
    {
      HutongGames.PlayMaker.DelayedEvent delayedEvent = new HutongGames.PlayMaker.DelayedEvent(this, eventTarget, fsmEvent, delay);
      this.delayedEvents.Add(delayedEvent);
      return delayedEvent;
    }

    public void BroadcastEvent(string fsmEventName, bool excludeSelf = false)
    {
      if (string.IsNullOrEmpty(fsmEventName))
        return;
      this.BroadcastEvent(FsmEvent.GetFsmEvent(fsmEventName), excludeSelf);
    }

    public void BroadcastEvent(FsmEvent fsmEvent, bool excludeSelf = false)
    {
      FsmEventData fsmEventData = new FsmEventData(Fsm.EventData);
      Fsm.SetEventDataSentByInfo();
      FsmEventData source = new FsmEventData(Fsm.EventData);
      foreach (PlayMakerFSM playMakerFsm in new List<PlayMakerFSM>((IEnumerable<PlayMakerFSM>) PlayMakerFSM.FsmList))
      {
        if (!((UnityEngine.Object) playMakerFsm == (UnityEngine.Object) null) && playMakerFsm.Fsm != null && (!excludeSelf || playMakerFsm.Fsm != this))
        {
          Fsm.EventData = new FsmEventData(source);
          playMakerFsm.Fsm.ProcessEvent(fsmEvent);
        }
      }
      Fsm.EventData = fsmEventData;
    }

    public void BroadcastEventToGameObject(
      GameObject go,
      string fsmEventName,
      bool sendToChildren,
      bool excludeSelf = false)
    {
      if (string.IsNullOrEmpty(fsmEventName))
        return;
      this.BroadcastEventToGameObject(go, FsmEvent.GetFsmEvent(fsmEventName), Fsm.EventData, sendToChildren, excludeSelf);
    }

    public void BroadcastEventToGameObject(
      GameObject go,
      FsmEvent fsmEvent,
      FsmEventData eventData,
      bool sendToChildren,
      bool excludeSelf = false)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null)
        return;
      FsmEventData fsmEventData = new FsmEventData(Fsm.EventData);
      Fsm.SetEventDataSentByInfo();
      List<Fsm> fsmList = new List<Fsm>();
      foreach (PlayMakerFSM fsm in PlayMakerFSM.FsmList)
      {
        if (!((UnityEngine.Object) fsm == (UnityEngine.Object) null))
        {
          if ((UnityEngine.Object) fsm.gameObject == (UnityEngine.Object) go)
            fsmList.Add(fsm.Fsm);
          else if (sendToChildren && this.IsFsmChildOfGameObject(fsm, go.transform))
            fsmList.Add(fsm.Fsm);
        }
      }
      if (excludeSelf)
        fsmList.Remove(this);
      foreach (Fsm fsm in fsmList)
      {
        Fsm.EventData = new FsmEventData(eventData);
        FsmEvent fsmEvent1 = fsmEvent;
        fsm.ProcessEvent(fsmEvent1);
      }
      Fsm.EventData = fsmEventData;
    }

    private bool IsFsmChildOfGameObject(PlayMakerFSM fsm, Transform root)
    {
      if ((UnityEngine.Object) fsm == (UnityEngine.Object) null)
        return false;
      GameObject gameObject = fsm.gameObject;
      if ((UnityEngine.Object) gameObject == (UnityEngine.Object) null)
        return false;
      for (Transform transform = gameObject.transform; (UnityEngine.Object) transform != (UnityEngine.Object) null; transform = transform.parent)
      {
        if ((UnityEngine.Object) transform == (UnityEngine.Object) root)
          return true;
      }
      return false;
    }

    public void SendEventToFsmOnGameObject(
      GameObject gameObject,
      string fsmName,
      string fsmEventName)
    {
      if (string.IsNullOrEmpty(fsmEventName))
        return;
      this.SendEventToFsmOnGameObject(gameObject, fsmName, FsmEvent.GetFsmEvent(fsmEventName));
    }

    public void SendEventToFsmOnGameObject(
      GameObject gameObject,
      string fsmName,
      FsmEvent fsmEvent)
    {
      if ((UnityEngine.Object) gameObject == (UnityEngine.Object) null)
        return;
      Fsm.SetEventDataSentByInfo();
      List<PlayMakerFSM> playMakerFsmList = new List<PlayMakerFSM>((IEnumerable<PlayMakerFSM>) PlayMakerFSM.FsmList);
      if (string.IsNullOrEmpty(fsmName))
      {
        foreach (PlayMakerFSM playMakerFsm in playMakerFsmList)
        {
          if ((UnityEngine.Object) playMakerFsm != (UnityEngine.Object) null && (UnityEngine.Object) playMakerFsm.gameObject == (UnityEngine.Object) gameObject)
            playMakerFsm.Fsm.ProcessEvent(fsmEvent);
        }
      }
      else
      {
        foreach (PlayMakerFSM playMakerFsm in playMakerFsmList)
        {
          if ((UnityEngine.Object) playMakerFsm != (UnityEngine.Object) null && (UnityEngine.Object) playMakerFsm.gameObject == (UnityEngine.Object) gameObject && fsmName == playMakerFsm.Fsm.Name)
          {
            playMakerFsm.Fsm.ProcessEvent(fsmEvent);
            break;
          }
        }
      }
    }

    public bool HasState(string stateName)
    {
      foreach (FsmState state in this.States)
      {
        if (state.Name == stateName)
          return true;
      }
      return false;
    }

    public void SetState(string stateName) => this.SwitchState(this.GetState(stateName));

    public void UpdateStateChanges()
    {
      while (this.IsSwitchingState && !Fsm.HitBreakpoint)
        this.SwitchState(this.switchToState);
      for (int index = 0; index < this.States.Length; ++index)
        this.States[index].ResetLoopCount();
      this.EventTarget = (FsmEventTarget) null;
    }

    private bool DoTransition(FsmTransition transition, bool isGlobal)
    {
      FsmState toFsmState = transition.ToFsmState;
      if (toFsmState == null)
        return false;
      this.LastTransition = transition;
      if (PlayMakerGlobals.IsEditor)
        this.MyLog.LogTransition(isGlobal ? (FsmState) null : this.ActiveState, transition);
      this.switchToState = toFsmState;
      if (Fsm.EventData.SentByFsm != this)
        this.UpdateStateChanges();
      return true;
    }

    public void SwitchState(FsmState toState)
    {
      if (toState == null)
        return;
      if (this.ActiveState != null && this.activeStateEntered)
        this.ExitState(this.ActiveState);
      this.ActiveState = toState;
      if (Fsm.BreakpointsEnabled && this.EnableBreakpoints && toState.IsBreakpoint || Fsm.StepToStateChange && (Fsm.StepFsm == null || Fsm.StepFsm == this))
        this.DoBreakpoint();
      else
        this.EnterState(toState);
    }

    public void GotoPreviousState()
    {
      if (this.PreviousActiveState == null)
        return;
      this.SwitchState(this.PreviousActiveState);
    }

    public void ReEnterState() => this.SwitchState(this.ActiveState);

    private void EnterState(FsmState state)
    {
      if (state == null)
        return;
      this.EventTarget = (FsmEventTarget) null;
      this.SwitchedState = true;
      this.activeStateEntered = true;
      this.switchToState = (FsmState) null;
      if (FsmLog.LoggingEnabled)
        this.MyLog.LogEnterState(state);
      if (state.loopCount >= this.MaxLoopCount)
      {
        this.Owner.enabled = false;
        this.MyLog.LogError("Loop count exceeded limit: " + (object) this.MaxLoopCount + ". Override the default limit (1000) in FSM Inspector > Settings > Max Loop Override.");
      }
      else
      {
        state.Fsm = this;
        if (this.StateChanged != null)
          this.StateChanged(state);
        state.OnEnter();
      }
    }

    private void FixedUpdateState(FsmState state)
    {
      state.Fsm = this;
      state.OnFixedUpdate();
      this.UpdateStateChanges();
    }

    private void UpdateState(FsmState state)
    {
      state.Fsm = this;
      state.OnUpdate();
      this.UpdateStateChanges();
    }

    private void LateUpdateState(FsmState state)
    {
      state.Fsm = this;
      state.OnLateUpdate();
      this.UpdateStateChanges();
    }

    private void ExitState(FsmState state)
    {
      this.PreviousActiveState = state;
      state.Fsm = this;
      if (FsmLog.LoggingEnabled)
        this.MyLog.LogExitState(state);
      this.ActiveState = (FsmState) null;
      state.OnExit();
      if (this.keepDelayedEventsOnStateExit)
        return;
      this.KillDelayedEvents();
    }

    public Fsm GetSubFsm(string subFsmName)
    {
      for (int index = 0; index < this.SubFsmList.Count; ++index)
      {
        Fsm subFsm = this.SubFsmList[index];
        if (subFsm != null && subFsm.name == subFsmName)
          return subFsm;
      }
      return (Fsm) null;
    }

    public static string GetFullFsmLabel(Fsm fsm) => fsm == null ? "None (FSM)" : fsm.OwnerName + " : " + fsm.Name;

    public GameObject GetOwnerDefaultTarget(FsmOwnerDefault ownerDefault)
    {
      if (ownerDefault == null)
        return (GameObject) null;
      return ownerDefault.OwnerOption != OwnerDefaultOption.UseOwner ? ownerDefault.GameObject.Value : this.GameObject;
    }

    public FsmState GetState(string stateName)
    {
      foreach (FsmState state in this.states)
      {
        if (state.Name == stateName)
          return state;
      }
      return (FsmState) null;
    }

    public static int GetStateIndex(Fsm fsm, FsmState state)
    {
      if (fsm == null || state == null)
        return -1;
      string name = state.Name;
      for (int index = 0; index < fsm.States.Length; ++index)
      {
        if (fsm.States[index].Name == name)
          return index;
      }
      return -1;
    }

    [Obsolete("Use FsmEvent.GetFsmEvent(eventName) instead.")]
    public FsmEvent GetEvent(string eventName) => FsmEvent.GetFsmEvent(eventName);

    public FsmEvent FindEvent(string eventName)
    {
      foreach (FsmEvent fsmEvent in this.Events)
      {
        if (fsmEvent.Name == eventName)
          return fsmEvent;
      }
      return (FsmEvent) null;
    }

    public int CompareTo(object obj) => obj is Fsm fsm ? this.GuiLabel.CompareTo(fsm.GuiLabel) : 0;

    public List<FsmTransition> GetGlobalTransitionsToState(FsmState state)
    {
      List<FsmTransition> fsmTransitionList = new List<FsmTransition>();
      if (state == null)
        return fsmTransitionList;
      string name = state.Name;
      foreach (FsmTransition globalTransition in this.GlobalTransitions)
      {
        if (globalTransition.ToState == name)
          fsmTransitionList.Add(globalTransition);
      }
      return fsmTransitionList;
    }

    public FsmObject GetFsmObject(string varName) => this.variables.GetFsmObject(varName);

    public FsmMaterial GetFsmMaterial(string varName) => this.variables.GetFsmMaterial(varName);

    public FsmTexture GetFsmTexture(string varName) => this.variables.GetFsmTexture(varName);

    public FsmFloat GetFsmFloat(string varName) => this.variables.GetFsmFloat(varName);

    public FsmInt GetFsmInt(string varName) => this.variables.GetFsmInt(varName);

    public FsmBool GetFsmBool(string varName) => this.variables.GetFsmBool(varName);

    public FsmString GetFsmString(string varName) => this.variables.GetFsmString(varName);

    public FsmVector2 GetFsmVector2(string varName) => this.variables.GetFsmVector2(varName);

    public FsmVector3 GetFsmVector3(string varName) => this.variables.GetFsmVector3(varName);

    public FsmRect GetFsmRect(string varName) => this.variables.GetFsmRect(varName);

    public FsmQuaternion GetFsmQuaternion(string varName) => this.variables.GetFsmQuaternion(varName);

    public FsmColor GetFsmColor(string varName) => this.variables.GetFsmColor(varName);

    public FsmGameObject GetFsmGameObject(string varName) => this.variables.GetFsmGameObject(varName);

    public FsmArray GetFsmArray(string varName) => this.variables.GetFsmArray(varName);

    public FsmEnum GetFsmEnum(string varName) => this.variables.GetFsmEnum(varName);

    public void OnDrawGizmos()
    {
      if ((UnityEngine.Object) this.owner == (UnityEngine.Object) null)
        return;
      if (PlayMakerFSM.DrawGizmos)
        Gizmos.DrawIcon(this.owner.transform.position, "PlaymakerIcon.png");
      if (this.EditState == null)
        return;
      this.EditState.Fsm = this;
      if (this.EditState.ActionData == null)
        return;
      foreach (FsmStateAction action in this.EditState.Actions)
        action.OnDrawActionGizmos();
    }

    public void OnDrawGizmosSelected()
    {
      if (this.EditState == null)
        return;
      this.EditState.Fsm = this;
      if (this.EditState.ActionData == null)
        return;
      foreach (FsmStateAction action in this.EditState.Actions)
        action.OnDrawActionGizmosSelected();
    }

    public void OnCollisionEnter(Collision collisionInfo)
    {
      FsmExecutionStack.PushFsm(this);
      this.CollisionInfo = collisionInfo;
      this.CollisionName = collisionInfo.gameObject.name;
      if (this.ActiveState.OnCollisionEnter(collisionInfo))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.CollisionEnter);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnCollisionStay(Collision collisionInfo)
    {
      FsmExecutionStack.PushFsm(this);
      this.CollisionInfo = collisionInfo;
      this.CollisionName = collisionInfo.gameObject.name;
      if (this.ActiveState.OnCollisionStay(collisionInfo))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.CollisionStay);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnCollisionExit(Collision collisionInfo)
    {
      FsmExecutionStack.PushFsm(this);
      this.CollisionInfo = collisionInfo;
      this.CollisionName = collisionInfo.gameObject.name;
      if (this.ActiveState.OnCollisionExit(collisionInfo))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.CollisionExit);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnTriggerEnter(Collider other)
    {
      FsmExecutionStack.PushFsm(this);
      this.TriggerCollider = other;
      this.TriggerName = other.gameObject.name;
      if (this.ActiveState.OnTriggerEnter(other))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.TriggerEnter);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnTriggerStay(Collider other)
    {
      FsmExecutionStack.PushFsm(this);
      this.TriggerCollider = other;
      this.TriggerName = other.gameObject.name;
      if (this.ActiveState.OnTriggerStay(other))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.TriggerStay);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnTriggerExit(Collider other)
    {
      FsmExecutionStack.PushFsm(this);
      this.TriggerCollider = other;
      this.TriggerName = other.gameObject.name;
      if (this.ActiveState.OnTriggerExit(other))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.TriggerExit);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnParticleCollision(GameObject other)
    {
      FsmExecutionStack.PushFsm(this);
      this.ParticleCollisionGO = other;
      if (this.ActiveState.OnParticleCollision(other))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.ParticleCollision);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnCollisionEnter2D(Collision2D collisionInfo)
    {
      FsmExecutionStack.PushFsm(this);
      this.Collision2DInfo = collisionInfo;
      this.Collision2dName = collisionInfo.gameObject.name;
      if (this.ActiveState.OnCollisionEnter2D(collisionInfo))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.CollisionEnter2D);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnCollisionStay2D(Collision2D collisionInfo)
    {
      FsmExecutionStack.PushFsm(this);
      this.Collision2DInfo = collisionInfo;
      this.Collision2dName = collisionInfo.gameObject.name;
      if (this.ActiveState.OnCollisionStay2D(collisionInfo))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.CollisionStay2D);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnCollisionExit2D(Collision2D collisionInfo)
    {
      FsmExecutionStack.PushFsm(this);
      this.Collision2DInfo = collisionInfo;
      this.Collision2dName = collisionInfo.gameObject.name;
      if (this.ActiveState.OnCollisionExit2D(collisionInfo))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.CollisionExit2D);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnTriggerEnter2D(Collider2D other)
    {
      FsmExecutionStack.PushFsm(this);
      this.TriggerCollider2D = other;
      this.Trigger2dName = other.name;
      if (this.ActiveState.OnTriggerEnter2D(other))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.TriggerEnter2D);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnTriggerStay2D(Collider2D other)
    {
      FsmExecutionStack.PushFsm(this);
      this.TriggerCollider2D = other;
      this.Trigger2dName = other.name;
      if (this.ActiveState.OnTriggerStay2D(other))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.TriggerStay2D);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnTriggerExit2D(Collider2D other)
    {
      FsmExecutionStack.PushFsm(this);
      this.TriggerCollider2D = other;
      this.Trigger2dName = other.name;
      if (this.ActiveState.OnTriggerExit2D(other))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.TriggerExit2D);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnControllerColliderHit(ControllerColliderHit collider)
    {
      FsmExecutionStack.PushFsm(this);
      this.ControllerCollider = collider;
      if (this.ActiveState.OnControllerColliderHit(collider))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.ControllerColliderHit);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnJointBreak(float breakForce)
    {
      FsmExecutionStack.PushFsm(this);
      this.JointBreakForce = breakForce;
      if (this.ActiveState.OnJointBreak(breakForce))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.JointBreak);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnJointBreak2D(Joint2D brokenJoint)
    {
      FsmExecutionStack.PushFsm(this);
      this.BrokenJoint2D = brokenJoint;
      if (this.ActiveState.OnJointBreak2D(brokenJoint))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
      {
        this.Event(FsmEvent.JointBreak2D);
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
    }

    public void OnAnimatorMove()
    {
      FsmExecutionStack.PushFsm(this);
      if (this.ActiveState.OnAnimatorMove())
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
        FsmExecutionStack.PopFsm();
    }

    public void OnAnimatorIK(int layerIndex)
    {
      FsmExecutionStack.PushFsm(this);
      if (this.ActiveState.OnAnimatorIK(layerIndex))
      {
        this.UpdateStateChanges();
        FsmExecutionStack.PopFsm();
      }
      else
        FsmExecutionStack.PopFsm();
    }

    public void OnGUI()
    {
      if (this.ActiveState == null)
        return;
      this.ActiveState.OnGUI();
    }

    private void DoBreakpoint()
    {
      this.activeStateEntered = false;
      this.DoBreak();
    }

    public void DoBreakError(string error)
    {
      Fsm.IsErrorBreak = true;
      Fsm.LastError = error;
      this.DoBreak();
    }

    private void DoBreak()
    {
      Fsm.BreakAtFsm = FsmExecutionStack.ExecutingFsm;
      Fsm.BreakAtState = FsmExecutionStack.ExecutingState;
      Fsm.HitBreakpoint = true;
      Fsm.IsBreak = true;
      if (FsmLog.LoggingEnabled && !PlayMakerFSM.ApplicationIsQuitting)
        this.MyLog.LogBreak();
      Fsm.StepToStateChange = false;
    }

    private void Continue()
    {
      this.activeStateEntered = true;
      Fsm.HitBreakpoint = false;
      Fsm.IsErrorBreak = false;
      Fsm.IsBreak = false;
      this.EnterState(this.ActiveState);
    }

    public void OnDestroy()
    {
      if (this.subFsmList != null)
      {
        foreach (Fsm subFsm in this.subFsmList)
          subFsm.OnDestroy();
        this.subFsmList.Clear();
      }
      if (Fsm.EventData.SentByFsm == this)
        Fsm.EventData = new FsmEventData();
      if (PlayMakerGUI.SelectedFSM == this)
        PlayMakerGUI.SelectedFSM = (Fsm) null;
      if (this.myLog != null)
        this.myLog.OnDestroy();
      if (Fsm.lastRaycastHit2DInfoLUT != null)
        Fsm.lastRaycastHit2DInfoLUT.Remove(this);
      this.owner = (MonoBehaviour) null;
    }

    [Conditional("DEBUG_LOG")]
    private void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }

    [Conditional("DEBUG_EVENTS")]
    private void DebugEvent(object message, LogColor logColor = LogColor.None)
    {
    }

    [Conditional("DEBUG_LIFETIME")]
    private void DebugLifetime(object message, LogColor logColor = LogColor.None)
    {
    }

    [Conditional("DEBUG_DEBUGGER")]
    private void DebugDebugger(object message, LogColor logColor = LogColor.None)
    {
    }

    [Obsolete("Use PlayMakerPrefs.ArrowColor")]
    public static Color DebugLookAtColor { get; set; }

    [Obsolete("Use PlayMakerPrefs.ArrowColor")]
    public static Color DebugRaycastColor { get; set; }

    [Flags]
    [Serializable]
    private enum EditorFlags
    {
      none = 0,
      nameIsExpanded = 1,
      controlsIsExpanded = 2,
      debugIsExpanded = 4,
      experimentalIsExpanded = 8,
      infoIsExpanded = 16, // 0x00000010
      inputsIsExpanded = 32, // 0x00000020
      outputsIsExpanded = 64, // 0x00000040
      eventsIsExpanded = 128, // 0x00000080
      settingsIsExpanded = 256, // 0x00000100
      coditionsIsExpanded = 512, // 0x00000100
    }
  }
}
