// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmDebugUtility
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using UnityEngine;

namespace HutongGames.PlayMaker
{
  public class FsmDebugUtility
  {
    public static void Log(Fsm fsm, string text, bool frameCount = false)
    {
      if ((Object) fsm.GameObject != (Object) null)
        text = text + " : " + fsm.GameObject.name + " : " + fsm.Name;
      FsmDebugUtility.Log(text, frameCount);
    }

    public static void Log(string text, bool frameCount = false)
    {
      if (frameCount)
        text = Time.frameCount.ToString() + " : " + text;
      Debug.Log((object) text);
    }

    public static void Log(Object obj, string text)
    {
      text = obj.name + " : " + text;
      FsmDebugUtility.Log(text);
    }
  }
}
