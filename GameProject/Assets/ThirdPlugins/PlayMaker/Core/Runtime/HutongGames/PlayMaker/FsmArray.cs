// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmArray
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmArray : NamedVariable
  {
    [SerializeField]
    private VariableType type = VariableType.Unknown;
    [SerializeField]
    private string objectTypeName;
    private System.Type objectType;
    public float[] floatValues;
    public int[] intValues;
    public bool[] boolValues;
    public string[] stringValues;
    public Vector4[] vector4Values;
    public UnityEngine.Object[] objectReferences;
    [NonSerialized]
    private Array sourceArray;
    [NonSerialized]
    private object[] values;

    public override object RawValue
    {
      get => (object) this.values;
      set => this.values = (object[]) value;
    }

    public override System.Type ObjectType
    {
      get
      {
        if (this.objectType == null)
          this.InitObjectType();
        return this.objectType;
      }
      set
      {
        if (this.objectType == null)
          this.InitObjectType();
        if (this.objectType == value)
          return;
        this.Reset();
        if (this.ElementType == VariableType.Enum)
        {
          if (value == null)
            value = typeof (None);
          else if (!value.IsEnum)
            value = typeof (None);
        }
        else if (this.ElementType == VariableType.Object)
        {
          if (value == null)
            value = typeof (UnityEngine.Object);
          else if (!typeof (UnityEngine.Object).IsAssignableFrom(value))
            value = typeof (None);
        }
        else if (value == null)
          value = typeof (UnityEngine.Object);
        this.objectType = value;
        this.objectTypeName = this.objectType.FullName;
      }
    }

    private void InitObjectType()
    {
      if (string.IsNullOrEmpty(this.objectTypeName))
        this.objectTypeName = this.ElementType != VariableType.GameObject ? (this.ElementType != VariableType.Material ? (this.ElementType != VariableType.Texture ? (this.ElementType != VariableType.Enum ? typeof (UnityEngine.Object).FullName : typeof (None).FullName) : typeof (Texture).FullName) : typeof (Material).FullName) : typeof (GameObject).FullName;
      this.objectType = ReflectionUtils.GetGlobalType(this.objectTypeName);
    }

    public string ObjectTypeName => this.objectTypeName;

    public object[] Values
    {
      get
      {
        if (this.values == null)
          this.InitArray();
        return this.values;
      }
      set
      {
        this.values = value;
        if (!PlayMakerGlobals.IsEditor)
          return;
        this.SaveChanges();
      }
    }

    public int Length => this.Values.Length;

    public override VariableType TypeConstraint => this.type;

    public VariableType ElementType
    {
      get => this.type;
      set => this.SetType(value);
    }

    private void InitArray()
    {
      this.sourceArray = this.GetSourceArray();
      if (this.sourceArray != null)
      {
        this.values = new object[this.sourceArray.Length];
        for (int index = 0; index < this.values.Length; ++index)
          this.values[index] = this.Load(index);
      }
      else
        this.values = new object[0];
    }

    public override void Init()
    {
      this.InitObjectType();
      this.InitArray();
    }

    public object Get(int index) => this.Values[index];

    public void Set(int index, object value)
    {
      this.Values[index] = value;
      if (!PlayMakerGlobals.IsEditor)
        return;
      this.Save(index, value);
    }

    private object Load(int index)
    {
      switch (this.type)
      {
        case VariableType.Unknown:
          return (object) null;
        case VariableType.Float:
          return (object) this.floatValues[index];
        case VariableType.Int:
          return (object) this.intValues[index];
        case VariableType.Bool:
          return (object) this.boolValues[index];
        case VariableType.GameObject:
          return (object) (this.objectReferences[index] as GameObject);
        case VariableType.String:
          return (object) this.stringValues[index];
        case VariableType.Vector2:
          Vector4 vector4Value1 = this.vector4Values[index];
          return (object) new Vector2(vector4Value1.x, vector4Value1.y);
        case VariableType.Vector3:
          Vector4 vector4Value2 = this.vector4Values[index];
          return (object) new Vector3(vector4Value2.x, vector4Value2.y, vector4Value2.z);
        case VariableType.Color:
          Vector4 vector4Value3 = this.vector4Values[index];
          return (object) new Color(vector4Value3.x, vector4Value3.y, vector4Value3.z, vector4Value3.w);
        case VariableType.Rect:
          Vector4 vector4Value4 = this.vector4Values[index];
          return (object) new Rect(vector4Value4.x, vector4Value4.y, vector4Value4.z, vector4Value4.w);
        case VariableType.Material:
          return (object) (this.objectReferences[index] as Material);
        case VariableType.Texture:
          return (object) (this.objectReferences[index] as Texture);
        case VariableType.Quaternion:
          Vector4 vector4Value5 = this.vector4Values[index];
          return (object) new Quaternion(vector4Value5.x, vector4Value5.y, vector4Value5.z, vector4Value5.w);
        case VariableType.Object:
          return (object) this.objectReferences[index];
        case VariableType.Array:
          Debug.LogError((object) "Nested arrays are not supported yet!");
          return (object) null;
        case VariableType.Enum:
          return Enum.ToObject(this.ObjectType, (object) this.intValues[index]);
        default:
          throw new ArgumentOutOfRangeException();
      }
    }

    private void Save(int index, object value)
    {
      switch (this.type)
      {
        case VariableType.Unknown:
          break;
        case VariableType.Float:
          this.floatValues[index] = value != null ? (float) value : 0.0f;
          break;
        case VariableType.Int:
          this.intValues[index] = value != null ? (int) value : 0;
          break;
        case VariableType.Bool:
          this.boolValues[index] = value != null && (bool) value;
          break;
        case VariableType.GameObject:
          this.objectReferences[index] = (UnityEngine.Object) (value as GameObject);
          break;
        case VariableType.String:
          this.stringValues[index] = value as string;
          break;
        case VariableType.Vector2:
          this.vector4Values[index] = (Vector4) (value != null ? (Vector2) value : Vector2.zero);
          break;
        case VariableType.Vector3:
          this.vector4Values[index] = (Vector4) (value != null ? (Vector3) value : Vector3.zero);
          break;
        case VariableType.Color:
          this.vector4Values[index] = (Vector4) (value != null ? (Color) value : Color.white);
          break;
        case VariableType.Rect:
          Rect rect = value != null ? (Rect) value : new Rect(0.0f, 0.0f, 0.0f, 0.0f);
          this.vector4Values[index] = new Vector4(rect.x, rect.y, rect.width, rect.height);
          break;
        case VariableType.Material:
          this.objectReferences[index] = (UnityEngine.Object) (value as Material);
          break;
        case VariableType.Texture:
          this.objectReferences[index] = (UnityEngine.Object) (value as Texture);
          break;
        case VariableType.Quaternion:
          Quaternion quaternion = value != null ? (Quaternion) value : Quaternion.identity;
          this.vector4Values[index] = new Vector4(quaternion.x, quaternion.y, quaternion.z, quaternion.w);
          break;
        case VariableType.Object:
          this.objectReferences[index] = value as UnityEngine.Object;
          break;
        case VariableType.Array:
          Debug.LogError((object) "Nested arrays are not supported yet!");
          break;
        case VariableType.Enum:
          this.intValues[index] = Convert.ToInt32(value);
          break;
        default:
          throw new ArgumentOutOfRangeException();
      }
    }

    public void SetType(VariableType newType)
    {
      if (this.type == newType)
        return;
      this.type = newType;
      this.ObjectType = (System.Type) null;
      this.Reset();
      this.ConformSourceArraySize();
    }

    public void SaveChanges()
    {
      this.ConformSourceArraySize();
      for (int index = 0; index < this.values.Length; ++index)
      {
        this.Save(index, this.values[index]);
        this.values[index] = this.Load(index);
      }
    }

    public void CopyValues(FsmArray source)
    {
      if (source == null)
        return;
      this.Resize(source.Length);
      object[] values = source.Values;
      for (int index = 0; index < values.Length; ++index)
        this.Set(index, values[index]);
      this.SaveChanges();
    }

    private void ConformSourceArraySize()
    {
      switch (this.type)
      {
        case VariableType.Unknown:
          break;
        case VariableType.Float:
          this.floatValues = new float[this.Values.Length];
          break;
        case VariableType.Int:
        case VariableType.Enum:
          this.intValues = new int[this.Values.Length];
          break;
        case VariableType.Bool:
          this.boolValues = new bool[this.Values.Length];
          break;
        case VariableType.GameObject:
        case VariableType.Material:
        case VariableType.Texture:
        case VariableType.Object:
          this.objectReferences = new UnityEngine.Object[this.Values.Length];
          break;
        case VariableType.String:
          this.stringValues = new string[this.Values.Length];
          break;
        case VariableType.Vector2:
        case VariableType.Vector3:
        case VariableType.Color:
        case VariableType.Rect:
        case VariableType.Quaternion:
          this.vector4Values = new Vector4[this.Values.Length];
          break;
        case VariableType.Array:
          break;
        default:
          throw new ArgumentOutOfRangeException();
      }
    }

    private Array GetSourceArray()
    {
      switch (this.type)
      {
        case VariableType.Unknown:
          return (Array) null;
        case VariableType.Float:
          return (Array) this.floatValues ?? (Array) (this.floatValues = new float[0]);
        case VariableType.Int:
        case VariableType.Enum:
          return (Array) this.intValues ?? (Array) (this.intValues = new int[0]);
        case VariableType.Bool:
          return (Array) this.boolValues ?? (Array) (this.boolValues = new bool[0]);
        case VariableType.GameObject:
        case VariableType.Material:
        case VariableType.Texture:
        case VariableType.Object:
          return (Array) this.objectReferences ?? (Array) (this.objectReferences = new UnityEngine.Object[0]);
        case VariableType.String:
          return (Array) this.stringValues ?? (Array) (this.stringValues = new string[0]);
        case VariableType.Vector2:
        case VariableType.Vector3:
        case VariableType.Color:
        case VariableType.Rect:
        case VariableType.Quaternion:
          return (Array) this.vector4Values ?? (Array) (this.vector4Values = new Vector4[0]);
        case VariableType.Array:
          return (Array) null;
        default:
          Debug.LogError((object) this.type);
          throw new ArgumentOutOfRangeException();
      }
    }

    public void Resize(int newLength)
    {
      if (newLength == this.Values.Length)
        return;
      if (newLength < 0)
        newLength = 0;
      Array instance = Array.CreateInstance(this.Values.GetType().GetElementType(), newLength);
      Array.Copy((Array) this.values, instance, Math.Min(this.values.Length, newLength));
      this.Values = (object[]) instance;
      this.SaveChanges();
    }

    public void InsertItem(object value, int atIndex)
    {
      Array instance = Array.CreateInstance(this.Values.GetType().GetElementType(), this.values.Length + 1);
      if (atIndex > this.values.Length)
        atIndex = this.values.Length;
      if (atIndex > 0)
        Array.Copy((Array) this.values, 0, instance, 0, atIndex);
      if (atIndex < this.values.Length)
        Array.Copy((Array) this.values, atIndex, instance, atIndex + 1, instance.Length - atIndex - 1);
      instance.SetValue(value, atIndex);
      this.Values = (object[]) instance;
      this.SaveChanges();
    }

    public void Reset()
    {
      this.floatValues = (float[]) null;
      this.intValues = (int[]) null;
      this.boolValues = (bool[]) null;
      this.stringValues = (string[]) null;
      this.vector4Values = (Vector4[]) null;
      this.objectReferences = (UnityEngine.Object[]) null;
      this.objectType = (System.Type) null;
      this.objectTypeName = (string) null;
      this.InitArray();
    }

    public FsmArray()
    {
    }

    public FsmArray(string name)
      : base(name)
    {
    }

    public FsmArray(FsmArray source)
      : base((NamedVariable) source)
    {
      if (source == null)
        return;
      this.type = source.type;
      this.ObjectType = source.ObjectType;
      this.CopyValues(source);
      this.SaveChanges();
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmArray(this);

    public override void Clear() => this.Reset();

    public override VariableType VariableType => VariableType.Array;

    public override string ToString()
    {
      string str = string.Empty;
      for (int index = 0; index < this.Values.Length; ++index)
      {
        object obj = this.Values[index];
        if (obj == null)
        {
          str += "null";
        }
        else
        {
          UnityEngine.Object @object = obj as UnityEngine.Object;
          str = !(@object != (UnityEngine.Object) null) ? str + obj.ToString() : str + @object.name;
        }
        if (index < this.Values.Length - 1)
          str += ", ";
      }
      if (str == string.Empty)
        str = "Empty";
      return str;
    }

    public override bool TestTypeConstraint(VariableType variableType, System.Type _objectType = null)
    {
      if (variableType == VariableType.Unknown)
        return true;
      if (!base.TestTypeConstraint(variableType, this.objectType))
        return false;
      if (variableType != VariableType.Object && variableType != VariableType.Enum)
        return variableType == this.ElementType;
      return this.ObjectType == _objectType || _objectType == null;
    }

    public System.Type RealType()
    {
      switch (this.type)
      {
        case VariableType.Unknown:
          return (System.Type) null;
        case VariableType.Float:
          return typeof (float[]);
        case VariableType.Int:
          return typeof (int[]);
        case VariableType.Bool:
          return typeof (bool[]);
        case VariableType.GameObject:
          return typeof (GameObject[]);
        case VariableType.String:
          return typeof (string[]);
        case VariableType.Vector2:
          return typeof (Vector2[]);
        case VariableType.Vector3:
          return typeof (Vector3[]);
        case VariableType.Color:
          return typeof (Color[]);
        case VariableType.Rect:
          return typeof (Rect[]);
        case VariableType.Material:
          return typeof (Material[]);
        case VariableType.Texture:
          return typeof (Texture[]);
        case VariableType.Quaternion:
          return typeof (Quaternion[]);
        case VariableType.Object:
          return this.ObjectType.MakeArrayType();
        case VariableType.Array:
          Debug.LogError((object) "Nested arrays are not supported yet!");
          return (System.Type) null;
        case VariableType.Enum:
          return this.ObjectType.MakeArrayType();
        default:
          throw new ArgumentOutOfRangeException();
      }
    }
  }
}
