// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.VariableType
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

namespace HutongGames.PlayMaker
{
  public enum VariableType
  {
    Unknown = -1, // 0xFFFFFFFF
    Float = 0,
    Int = 1,
    Bool = 2,
    GameObject = 3,
    String = 4,
    Vector2 = 5,
    Vector3 = 6,
    Color = 7,
    Rect = 8,
    Material = 9,
    Texture = 10, // 0x0000000A
    Quaternion = 11, // 0x0000000B
    Object = 12, // 0x0000000C
    Array = 13, // 0x0000000D
    Enum = 14, // 0x0000000E
  }
}
