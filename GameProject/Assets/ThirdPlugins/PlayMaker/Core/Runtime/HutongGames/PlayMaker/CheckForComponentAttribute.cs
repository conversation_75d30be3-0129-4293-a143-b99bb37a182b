// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.CheckForComponentAttribute
// Assembly: PlayMaker, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;

namespace HutongGames.PlayMaker
{
  [AttributeUsage(AttributeTargets.Field, AllowMultiple = true)]
  public sealed class CheckForComponentAttribute : Attribute
  {
    private readonly Type type0;
    private readonly Type type1;
    private readonly Type type2;

    public Type Type0 => this.type0;

    public Type Type1 => this.type1;

    public Type Type2 => this.type2;

    public CheckForComponentAttribute(Type type0) => this.type0 = type0;

    public CheckForComponentAttribute(Type type0, Type type1)
    {
      this.type0 = type0;
      this.type1 = type1;
    }

    public CheckForComponentAttribute(Type type0, Type type1, Type type2)
    {
      this.type0 = type0;
      this.type1 = type1;
      this.type2 = type2;
    }
  }
}
