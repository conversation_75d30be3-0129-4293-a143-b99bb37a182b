// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmRect
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmRect : NamedVariable
  {
    [SerializeField]
    private Rect value;

    public Rect Value
    {
      get => this.value;
      set => this.value = value;
    }

    public override object RawValue
    {
      get => (object) this.value;
      set => this.value = (Rect) value;
    }

    public FsmRect()
    {
    }

    public FsmRect(string name)
      : base(name)
    {
    }

    public FsmRect(FsmRect source)
      : base((NamedVariable) source)
    {
      if (source == null)
        return;
      this.value = source.value;
    }

    public override NamedVariable Clone() => (NamedVariable) new FsmRect(this);

    public override void Clear() => this.value = new Rect();

    public override VariableType VariableType => VariableType.Rect;

    public override string ToString() => this.value.ToString();

    public static implicit operator FsmRect(Rect value) => new FsmRect(string.Empty)
    {
      value = value
    };
  }
}
