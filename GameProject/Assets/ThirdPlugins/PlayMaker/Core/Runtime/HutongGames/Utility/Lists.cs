// Decompiled with JetBrains decompiler
// Type: HutongGames.Utility.Lists`1
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System.Collections.Generic;

namespace HutongGames.Utility
{
  public static class Lists<T>
  {
    public static readonly List<T> Empty = new List<T>();
  }
}
