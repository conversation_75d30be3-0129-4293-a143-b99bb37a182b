// Decompiled with JetBrains decompiler
// Type: HutongGames.Utility.ColorUtils
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using UnityEngine;

namespace HutongGames.Utility
{
  public static class ColorUtils
  {
    public static bool Approximately(Color color1, Color color2) => Mathf.Approximately(color1.r, color2.r) && Mathf.Approximately(color1.g, color2.g) && Mathf.Approximately(color1.b, color2.b) && Mathf.Approximately(color1.a, color2.a);

    public static Color FromIntRGBA(int r, int g, int b, int a) => new Color((float) r / (float) byte.MaxValue, (float) g / (float) byte.MaxValue, (float) b / (float) byte.MaxValue, (float) a / (float) byte.MaxValue);
  }
}
