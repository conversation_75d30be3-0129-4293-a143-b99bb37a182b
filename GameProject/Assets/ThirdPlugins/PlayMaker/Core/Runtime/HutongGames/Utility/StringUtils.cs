// Decompiled with JetBrains decompiler
// Type: HutongGames.Utility.StringUtils
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using System.Text;
using System.Text.RegularExpressions;

namespace HutongGames.Utility
{
  public static class StringUtils
  {
    [ThreadStatic]
    private static StringBuilder escapeBuilder;
    public static bool forceAscii;

    public static string IncrementStringCounter(string s)
    {
      if (string.IsNullOrEmpty(s))
        return "1";
      string[] strArray = s.Split(' ');
      string s1 = strArray[strArray.Length - 1];
      string str = s.Substring(0, s.Length - s1.Length);
      int result;
      if (!int.TryParse(s1, out result))
        return s + " 2";
      int num = result + 1;
      return str + num.ToString(new string('0', s1.Length));
    }

    public static string StripHtml(string input) => Regex.Replace(input, "<.*?>", string.Empty);

    public static string StripMarkdown(string input) => input.Replace("{{", "").Replace("}}", "");

    public static string StripHtmlAndMarkdown(string input) => StringUtils.StripMarkdown(StringUtils.StripHtml(input));

    internal static StringBuilder EscapeBuilder => StringUtils.escapeBuilder ?? (StringUtils.escapeBuilder = new StringBuilder());

    public static string Escape(string aText)
    {
      StringBuilder escapeBuilder = StringUtils.EscapeBuilder;
      escapeBuilder.Length = 0;
      if (escapeBuilder.Capacity < aText.Length + aText.Length / 10)
        escapeBuilder.Capacity = aText.Length + aText.Length / 10;
      foreach (char ch in aText)
      {
        switch (ch)
        {
          case '\b':
            escapeBuilder.Append("\\b");
            break;
          case '\t':
            escapeBuilder.Append("\\t");
            break;
          case '\n':
            escapeBuilder.Append("\\n");
            break;
          case '\f':
            escapeBuilder.Append("\\f");
            break;
          case '\r':
            escapeBuilder.Append("\\r");
            break;
          case '"':
            escapeBuilder.Append("\\\"");
            break;
          case '\\':
            escapeBuilder.Append("\\\\");
            break;
          default:
            if (ch < ' ' || StringUtils.forceAscii && ch > '\x007F')
            {
              ushort num = (ushort) ch;
              escapeBuilder.Append("\\u").Append(num.ToString("X4"));
              break;
            }
            escapeBuilder.Append(ch);
            break;
        }
      }
      string str = escapeBuilder.ToString();
      escapeBuilder.Length = 0;
      return str;
    }
  }
}
