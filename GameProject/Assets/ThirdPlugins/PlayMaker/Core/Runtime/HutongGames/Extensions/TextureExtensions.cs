// Decompiled with JetBrains decompiler
// Type: HutongGames.Extensions.TextureExtensions
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System.Collections.Generic;
using UnityEngine;

namespace HutongGames.Extensions
{
  public static class TextureExtensions
  {
    public static void FloodFillArea(this Texture2D aTex, int aX, int aY, Color32 aFillColor)
    {
      int width = aTex.width;
      int height = aTex.height;
      Color32[] pixels32 = aTex.GetPixels32();
      Color color1 = (Color) pixels32[aX + aY * width];
      Queue<TextureExtensions.Point> pointQueue = new Queue<TextureExtensions.Point>();
      pointQueue.Enqueue(new TextureExtensions.Point(aX, aY));
      while (pointQueue.Count > 0)
      {
        TextureExtensions.Point point = pointQueue.Dequeue();
        for (int x = (int) point.x; x < width; ++x)
        {
          Color color2 = (Color) pixels32[x + (int) point.y * width];
          if (!(color2 != color1) && !(color2 == (Color) aFillColor))
          {
            pixels32[x + (int) point.y * width] = aFillColor;
            if ((int) point.y + 1 < height)
            {
              Color color3 = (Color) pixels32[x + (int) point.y * width + width];
              if (color3 == color1 && color3 != (Color) aFillColor)
                pointQueue.Enqueue(new TextureExtensions.Point(x, (int) point.y + 1));
            }
            if ((int) point.y - 1 >= 0)
            {
              Color color3 = (Color) pixels32[x + (int) point.y * width - width];
              if (color3 == color1 && color3 != (Color) aFillColor)
                pointQueue.Enqueue(new TextureExtensions.Point(x, (int) point.y - 1));
            }
          }
          else
            break;
        }
        for (int aX1 = (int) point.x - 1; aX1 >= 0; --aX1)
        {
          Color color2 = (Color) pixels32[aX1 + (int) point.y * width];
          if (!(color2 != color1) && !(color2 == (Color) aFillColor))
          {
            pixels32[aX1 + (int) point.y * width] = aFillColor;
            if ((int) point.y + 1 < height)
            {
              Color color3 = (Color) pixels32[aX1 + (int) point.y * width + width];
              if (color3 == color1 && color3 != (Color) aFillColor)
                pointQueue.Enqueue(new TextureExtensions.Point(aX1, (int) point.y + 1));
            }
            if ((int) point.y - 1 >= 0)
            {
              Color color3 = (Color) pixels32[aX1 + (int) point.y * width - width];
              if (color3 == color1 && color3 != (Color) aFillColor)
                pointQueue.Enqueue(new TextureExtensions.Point(aX1, (int) point.y - 1));
            }
          }
          else
            break;
        }
      }
      aTex.SetPixels32(pixels32);
    }

    public struct Point
    {
      public short x;
      public short y;

      public Point(short aX, short aY)
      {
        this.x = aX;
        this.y = aY;
      }

      public Point(int aX, int aY)
        : this((short) aX, (short) aY)
      {
      }
    }
  }
}
