// Decompiled with JetBrains decompiler
// Type: HutongGames.Extensions.RectExtensions
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using UnityEngine;

namespace HutongGames.Extensions
{
  public static class RectExtensions
  {
    public static string Debug(this Rect rect) => "(" + (object) (int) rect.x + ", " + (object) (int) rect.y + ", " + (object) (int) rect.width + ", " + (object) (int) rect.height + ")";

    public static Rect BottomRight(this Rect rect, float size) => new Rect(rect.xMax - size, rect.yMax - size, size, size);

    public static Rect RoundToInt(this Rect rect) => new Rect((float) (int) rect.x, (float) (int) rect.y, (float) (int) rect.width, (float) (int) rect.height);

    public static bool IsDifferent(this Rect rect1, Rect rect2) => !Mathf.Approximately(rect1.x, rect2.x) || !Mathf.Approximately(rect1.y, rect2.y) || (!Mathf.Approximately(rect1.width, rect2.width) || !Mathf.Approximately(rect1.height, rect2.height));

    public static bool AreEqual(this Rect rect1, Rect rect2) => !rect1.IsDifferent(rect2);

    public static bool Contains(this Rect rect, float x, float y) => (double) x > (double) rect.xMin && (double) x < (double) rect.xMax && (double) y > (double) rect.yMin && (double) y < (double) rect.yMax;

    public static bool Contains(this Rect rect1, Rect rect2) => (double) rect1.xMin <= (double) rect2.xMin && (double) rect1.yMin <= (double) rect2.yMin && (double) rect1.xMax >= (double) rect2.xMax && (double) rect1.yMax >= (double) rect2.yMax;

    public static bool IntersectsWith(this Rect rect1, Rect rect2) => (double) rect2.xMin <= (double) rect1.xMax && (double) rect2.xMax >= (double) rect1.xMin && (double) rect2.yMin <= (double) rect1.yMax && (double) rect2.yMax >= (double) rect1.yMin;

    public static Rect Union(this Rect rect1, Rect rect2) => Rect.MinMaxRect(Mathf.Min(rect1.xMin, rect2.xMin), Mathf.Min(rect1.yMin, rect2.yMin), Mathf.Max(rect1.xMax, rect2.xMax), Mathf.Max(rect1.yMax, rect2.yMax));

    public static Rect Move(this Rect rect, float x, float y) => new Rect(rect.x + x, rect.y + y, rect.width, rect.height);

    public static Rect Move(this Rect rect, Vector2 delta) => new Rect(rect.x + delta.x, rect.y + delta.y, rect.width, rect.height);

    public static Rect Scale(this Rect rect, float scale) => new Rect(rect.x * scale, rect.y * scale, rect.width * scale, rect.height * scale);

    public static Rect ScaleToInt(this Rect rect, float scale) => new Rect((float) (int) ((double) rect.x * (double) scale), (float) (int) ((double) rect.y * (double) scale), (float) (int) ((double) rect.width * (double) scale), (float) (int) ((double) rect.height * (double) scale));

    public static Rect MinSize(this Rect rect, float minWidth, float minHeight) => new Rect(rect.x, rect.y, Mathf.Max(rect.width, minWidth), Mathf.Max(rect.height, minHeight));

    public static Rect MinSize(this Rect rect, Vector2 minSize) => new Rect(rect.x, rect.y, Mathf.Max(rect.width, minSize.x), Mathf.Max(rect.height, minSize.y));

    public static Rect Expand(this Rect rect, float amount) => new Rect(rect.x - amount, rect.y - amount, rect.width + amount * 2f, rect.height + amount * 2f);

    public static Rect ExpandToFit(this Rect rect, Rect rect2)
    {
      float x = Mathf.Min(rect.x, rect2.x);
      float y = Mathf.Min(rect.y, rect2.y);
      float width = Mathf.Max(rect.xMax - x, rect2.xMax - x);
      float height = Mathf.Max(rect.yMax - y, rect2.yMax - y);
      return new Rect(x, y, width, height);
    }

    public static Rect FitPoints(this Rect rect, params Vector3[] points)
    {
      if (points == null || points.Length == 0)
        return new Rect();
      float num1 = float.PositiveInfinity;
      float a1 = float.NegativeInfinity;
      float num2 = float.PositiveInfinity;
      float a2 = float.NegativeInfinity;
      foreach (Vector3 point in points)
      {
        num1 = Mathf.Min(num1, point.x);
        a1 = Mathf.Max(a1, point.x);
        num2 = Mathf.Min(num2, point.y);
        a2 = Mathf.Max(a2, point.y);
      }
      return new Rect(num1, num2, a1 - num1, a2 - num2);
    }

    public static Vector2 TopLeft(this Rect rect) => new Vector2(rect.xMin, rect.yMin);

    public static Vector2 Center(this Rect rect) => new Vector2(rect.x + rect.width / 2f, rect.y + rect.height / 2f);

    public static Rect ScaleSizeBy(this Rect rect, float scale) => rect.ScaleSizeBy(scale, rect.center);

    public static Rect ScaleSizeBy(this Rect rect, float scale, Vector2 pivotPoint)
    {
      Rect rect1 = rect;
      rect1.x -= pivotPoint.x;
      rect1.y -= pivotPoint.y;
      rect1.xMin *= scale;
      rect1.xMax *= scale;
      rect1.yMin *= scale;
      rect1.yMax *= scale;
      rect1.x += pivotPoint.x;
      rect1.y += pivotPoint.y;
      return rect1;
    }

    public static Rect ScaleSizeBy(this Rect rect, Vector2 scale) => rect.ScaleSizeBy(scale, rect.center);

    public static Rect ScaleSizeBy(this Rect rect, Vector2 scale, Vector2 pivotPoint)
    {
      Rect rect1 = rect;
      rect1.x -= pivotPoint.x;
      rect1.y -= pivotPoint.y;
      rect1.xMin *= scale.x;
      rect1.xMax *= scale.x;
      rect1.yMin *= scale.y;
      rect1.yMax *= scale.y;
      rect1.x += pivotPoint.x;
      rect1.y += pivotPoint.y;
      return rect1;
    }
  }
}
