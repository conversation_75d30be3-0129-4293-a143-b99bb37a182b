// Decompiled with JetBrains decompiler
// Type: PlayMakerTriggerEnter
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using UnityEngine;

[AddComponentMenu("PlayMaker/Event Handlers/TriggerEnter")]
public class PlayMakerTriggerEnter : PlayMakerProxyBase
{
  public void OnTriggerEnter(Collider other)
  {
    for (int index = 0; index < this.TargetFSMs.Count; ++index)
    {
      PlayMakerFSM targetFsM = this.TargetFSMs[index];
      if (!((Object) targetFsM == (Object) null) && targetFsM.Fsm != null && (targetFsM.Active && targetFsM.Fsm.HandleTriggerEnter))
        targetFsM.Fsm.OnTriggerEnter(other);
    }
    this.DoTriggerEventCallback(other);
  }
}
