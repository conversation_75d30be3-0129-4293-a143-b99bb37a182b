// Decompiled with JetBrains decompiler
// Type: PlayMakerApplicationEvents
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using HutongGames.PlayMaker;
using UnityEngine;

[AddComponentMenu("PlayMaker/Event Handlers/Application Events")]
public class PlayMakerApplicationEvents : PlayMakerProxyBase
{
  public void OnApplicationFocus()
  {
    for (int index = 0; index < this.TargetFSMs.Count; ++index)
    {
      PlayMakerFSM targetFsM = this.TargetFSMs[index];
      if (!((Object) targetFsM == (Object) null) && targetFsM.Fsm != null && targetFsM.Fsm.HandleApplicationEvents)
        targetFsM.Fsm.Event(FsmEvent.ApplicationFocus);
    }
  }

  public void OnApplicationPause()
  {
    for (int index = 0; index < this.TargetFSMs.Count; ++index)
    {
      PlayMakerFSM targetFsM = this.TargetFSMs[index];
      if (!((Object) targetFsM == (Object) null) && targetFsM.Fsm != null && targetFsM.Fsm.HandleApplicationEvents)
        targetFsM.Fsm.Event(FsmEvent.ApplicationPause);
    }
  }
}
