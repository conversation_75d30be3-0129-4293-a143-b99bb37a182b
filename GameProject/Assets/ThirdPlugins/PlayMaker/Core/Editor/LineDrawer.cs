// Decompiled with JetBrains decompiler
// Type: LineDrawer
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using UnityEditor;
using UnityEngine;

public class LineDrawer
{
  private Color color = Color.white;
  private Color drawColor = Color.white;
  private Vector3 lineStart;
  private Vector3 lineEnd;

  public void SetColor(Color color)
  {
    this.color = color;
    this.drawColor = color;
    Handles.color = color;
  }

  public void SetAlpha(float alpha)
  {
    this.drawColor = this.color;
    this.drawColor.a = alpha;
    Handles.color = this.drawColor;
  }

  public void DrawVerticalLine(float alpha, float x, float y0, float y1) => this.DrawLine(alpha, x, y0, x, y1);

  public void DrawVerticalLine(float x, float y0, float y1) => this.DrawLine(x, y0, x, y1);

  public void DrawLine(float alpha, float x0, float y0, float x1, float y1)
  {
    this.SetAlpha(alpha);
    this.DrawLine(x0, y0, x1, y1);
  }

  public void DrawLine(float x0, float y0, float x1, float y1)
  {
    this.lineStart.Set(x0, y0, 0.0f);
    this.lineEnd.Set(x1, y1, 0.0f);
    Handles.DrawLine(this.lineStart, this.lineEnd);
  }

  public void DrawThickVerticalLine(float alpha, float x0, float y0, float y1)
  {
    this.SetAlpha(alpha);
    this.DrawThickVerticalLine(x0, y0, y1);
  }

  public void DrawThickVerticalLine(float x0, float y0, float y1)
  {
    this.lineStart.Set(x0, y0, 0.0f);
    this.lineEnd.Set(x0, y1, 0.0f);
    Handles.DrawLine(this.lineStart, this.lineEnd);
    ++this.lineStart.x;
    ++this.lineEnd.x;
    Handles.DrawLine(this.lineStart, this.lineEnd);
  }
}
