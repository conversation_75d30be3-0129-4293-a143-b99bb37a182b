// Decompiled with JetBrains decompiler
// Type: FsmTemplateControlEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using HutongGames.PlayMakerEditor;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;

public class FsmTemplateControlEditor
{
  private static readonly Dictionary<FsmTemplateControl, FsmTemplateControlEditor> editorLookup = new Dictionary<FsmTemplateControl, FsmTemplateControlEditor>();
  private readonly FsmTemplateControl target;
  private List<FsmTemplateControlEditor.Parameter> inputs;
  private List<FsmTemplateControlEditor.Parameter> outputs;
  private readonly object[] outputAttributes;
  private readonly object[] inputAttributes;

  public static void ClearCache() => FsmTemplateControlEditor.editorLookup.Clear();

  public static FsmTemplateControlEditor GetEditor(
    FsmTemplateControl fsmTemplateControl)
  {
    FsmTemplateControlEditor templateControlEditor;
    if (!FsmTemplateControlEditor.editorLookup.TryGetValue(fsmTemplateControl, out templateControlEditor))
    {
      templateControlEditor = new FsmTemplateControlEditor(fsmTemplateControl);
      FsmTemplateControlEditor.editorLookup.Add(fsmTemplateControl, templateControlEditor);
    }
    return templateControlEditor;
  }

  public FsmTemplateControlEditor(FsmTemplateControl fsmTemplateControl)
  {
    this.target = fsmTemplateControl;
    this.inputAttributes = new object[1]
    {
      (object) new HideTypeFilter()
    };
    this.outputAttributes = new object[2]
    {
      (object) new HideTypeFilter(),
      (object) new UIHintAttribute(UIHint.Variable)
    };
    this.Init();
  }

  private void Init()
  {
    this.target.Init();
    this.inputs = new List<FsmTemplateControlEditor.Parameter>();
    foreach (FsmVarOverride inputVariable in this.target.inputVariables)
      this.inputs.Add(new FsmTemplateControlEditor.Parameter(this.target.fsmVariables, inputVariable));
    this.outputs = new List<FsmTemplateControlEditor.Parameter>();
    foreach (FsmVarOverride outputVariable in this.target.outputVariables)
      this.outputs.Add(new FsmTemplateControlEditor.Parameter(this.target.fsmVariables, outputVariable));
    this.inputs = this.inputs.OrderBy<FsmTemplateControlEditor.Parameter, string>((Func<FsmTemplateControlEditor.Parameter, string>) (x => x.category + "_" + x.var.Name)).ToList<FsmTemplateControlEditor.Parameter>();
    this.outputs = this.outputs.OrderBy<FsmTemplateControlEditor.Parameter, string>((Func<FsmTemplateControlEditor.Parameter, string>) (x => x.category + "_" + x.var.Name)).ToList<FsmTemplateControlEditor.Parameter>();
  }

  public void OnGUI()
  {
    GUILayout.Space(2f);
    switch (this.target.targetType)
    {
      case FsmTemplateControl.TargetType.FsmTemplate:
        this.DoTemplateSelection();
        break;
      case FsmTemplateControl.TargetType.PlayMakerFSM:
        this.DoFsmComponentSelection();
        break;
    }
    if (!(this.target.target != (UnityEngine.Object) null))
      return;
    if (this.target.ShowInputs)
      this.DoInputs();
    if (this.target.ShowOutputs)
      this.DoOutputs();
    if (!this.target.ShowEvents)
      return;
    this.DoEvents();
  }

  private void DoTemplateSelection()
  {
    GUILayout.BeginHorizontal();
    FsmTemplate template = (FsmTemplate) EditorGUILayout.ObjectField((UnityEngine.Object) this.target.fsmTemplate, typeof (FsmTemplate), false);
    if (GUILayout.Button(new GUIContent(Strings.Label_Browse, Strings.Tooltip_Browse_Templates), FsmEditorStyles.InlineButton, GUILayout.MaxWidth(30f)))
    {
      Templates.DoSelectTemplateMenu(this.target.fsmTemplate, new GenericMenu.MenuFunction(this.ClearTemplate), new GenericMenu.MenuFunction2(this.SelectTemplate));
      GUIUtility.ExitGUI();
    }
    if ((UnityEngine.Object) template != (UnityEngine.Object) null)
    {
      if (!Application.isPlaying)
      {
        if (GUILayout.Button(Strings.Command_Edit, FsmEditorStyles.InlineButton, GUILayout.Width(45f)))
        {
          FsmEditor.SelectTemplateDelayed(template);
          Keyboard.ResetFocus(true);
        }
      }
      else if (FsmEditorGUILayout.ButtonNoGuiChanged(Strings.Command_Show, FsmEditorStyles.InlineButton, GUILayout.Width(45f)))
      {
        FsmEditor.SelectFsmDelayed(this.target.RunFsm);
        Keyboard.ResetFocus(true);
      }
    }
    else if (GUILayout.Button(Strings.Command_New, FsmEditorStyles.InlineButton, GUILayout.Width(45f)))
    {
      FsmTemplate templateAsset = EditFsmTemplate.CreateTemplateAsset();
      if ((UnityEngine.Object) templateAsset != (UnityEngine.Object) null)
        template = templateAsset;
    }
    GUILayout.EndHorizontal();
    if ((UnityEngine.Object) template != (UnityEngine.Object) null && !string.IsNullOrEmpty(template.Description))
      GUILayout.Label(template.Description, FsmEditorStyles.LabelWithWordWrap);
    if (!((UnityEngine.Object) template != (UnityEngine.Object) this.target.fsmTemplate))
      return;
    this.SelectTemplate((object) template);
    GUIUtility.ExitGUI();
  }

  private void DoFsmComponentSelection()
  {
    GUILayout.BeginHorizontal();
    PlayMakerFSM playMakerFsm = (PlayMakerFSM) EditorGUILayout.ObjectField((UnityEngine.Object) this.target.fsmComponent, typeof (PlayMakerFSM), true);
    if ((UnityEngine.Object) playMakerFsm != (UnityEngine.Object) null)
    {
      if (GUILayout.Button(Strings.Command_Edit, FsmEditorStyles.InlineButton, GUILayout.Width(45f)))
        FsmEditor.SelectFsmDelayed(playMakerFsm.Fsm);
    }
    GUILayout.EndHorizontal();
    if ((UnityEngine.Object) playMakerFsm == (UnityEngine.Object) null)
      EditorGUILayout.HelpBox("Select a PlayMakerFSM in the scene.", MessageType.None);
    else if (!string.IsNullOrEmpty(playMakerFsm.FsmDescription))
      GUILayout.Label(playMakerFsm.FsmDescription, FsmEditorStyles.LabelWithWordWrap);
    if (!((UnityEngine.Object) playMakerFsm != (UnityEngine.Object) this.target.fsmComponent))
      return;
    this.SelectComponent((object) playMakerFsm);
    GUIUtility.ExitGUI();
  }

  private void DoInputs()
  {
    if (this.inputs.Count == 0)
      return;
    GUILayout.Label("INPUTS", EditorStyles.boldLabel);
    FsmEditorGUILayout.LightDivider();
    string str = "";
    foreach (FsmTemplateControlEditor.Parameter input in this.inputs)
    {
      if (input != null)
      {
        if (!string.IsNullOrEmpty(input.category) && input.category != str)
        {
          GUILayout.Label(input.category, EditorStyles.boldLabel);
          str = input.category;
        }
        NamedVariable namedVar = input.var.fsmVar.NamedVar;
        ActionEditor.SetEditingContext((object) input.var.fsmVar, namedVar.ObjectType);
        input.var.fsmVar.NamedVar = (NamedVariable) FsmEditor.ActionEditor.EditField(input.var.variable.Name, namedVar.GetType(), (object) namedVar, this.inputAttributes);
      }
    }
  }

  private void DoOutputs()
  {
    if (this.outputs.Count == 0)
      return;
    GUILayout.Space(5f);
    GUILayout.Label("OUTPUTS", EditorStyles.boldLabel);
    FsmEditorGUILayout.LightDivider();
    string str = "";
    foreach (FsmTemplateControlEditor.Parameter output in this.outputs)
    {
      if (output != null)
      {
        if (!string.IsNullOrEmpty(output.category) && output.category != str)
        {
          GUILayout.Label(output.category, EditorStyles.boldLabel);
          str = output.category;
        }
        NamedVariable namedVar = output.var.fsmVar.NamedVar;
        ActionEditor.SetEditingContext((object) output.var.fsmVar, namedVar.ObjectType);
        output.var.fsmVar.NamedVar = (NamedVariable) FsmEditor.ActionEditor.EditField(output.var.variable.Name, namedVar.GetType(), (object) namedVar, this.outputAttributes);
      }
    }
  }

  private void DoEvents()
  {
    if (target.outputEvents.Length == 0)
      return;
    
    GUILayout.Space(5f);
    GUILayout.Label("EVENTS", EditorStyles.boldLabel);
    FsmEditorGUILayout.LightDivider();
    for (int index = 0; index < this.target.outputEvents.Length; ++index)
    {
      FsmEventMapping outputEvent = this.target.outputEvents[index];
      ActionEditor.SetEditingContext((object) this.target.outputEvents, typeof (FsmEventMapping), index);
      ActionEditor.EventSelector(new GUIContent(outputEvent.fromEvent.Name), outputEvent.toEvent);
    }
  }

  private void ClearTemplate()
  {
    FsmEditor.RecordUndo("Clear Template");
    this.target.SetFsmTemplate((FsmTemplate) null);
  }

  private void SelectTemplate(object userdata)
  {
    FsmEditor.RecordUndo("Select Template");
    this.target.SetFsmTemplate((FsmTemplate) userdata);
    this.Init();
  }

  private void SelectComponent(object userdata)
  {
    FsmEditor.RecordUndo("Select PlayMakerFSM");
    this.target.SetFsmComponent((PlayMakerFSM) userdata);
    this.Init();
  }

  private class Parameter
  {
    public readonly FsmVarOverride var;
    public readonly string category;

    public Parameter(FsmVariables variables, FsmVarOverride var)
    {
      this.var = var;
      this.category = variables.GetCategory(var.variable);
    }
  }
}
