<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="WelcomeWindow_Forums" xml:space="preserve">
    <value>Forums</value>
  </data>
  <data name="Tooltip_Controller_Collider_Hit" xml:space="preserve">
    <value>Sent when the character controller on the GameObject collides with an object.</value>
  </data>
  <data name="FsmEditorSettings_Debug_Look_At_Color" xml:space="preserve">
    <value>Debug Look At Color</value>
  </data>
  <data name="FsmEditorSettings_Draw_Playmaker_Gizmos_in_Scene_View" xml:space="preserve">
    <value>Draw Playmaker Gizmos in Scene View</value>
  </data>
  <data name="Menu_Preview_Version" xml:space="preserve">
    <value>PREVIEW VERSION - EDITING DISABLED</value>
  </data>
  <data name="Tooltip_UI_Bool_Value_Changed" xml:space="preserve">
    <value>Sent when a Toggle value changes. GameObject needs a Toggle component. Use Get Event Bool Data to get the value.</value>
  </data>
  <data name="Label_GameObject" xml:space="preserve">
    <value>GameObject</value>
  </data>
  <data name="Label_Could_Not_Find_Field" xml:space="preserve">
    <value>Could not find field: </value>
  </data>
  <data name="FsmEditorSettings_Link_Style_Tooltip" xml:space="preserve">
    <value>Default style used to draw links between states. Can override per transition.</value>
  </data>
  <data name="FsmEditorSettings_Disconnect_All_Modified_Prefab_Instances_in_Scene" xml:space="preserve">
    <value>Disconnect All Modified Prefab Instances in Scene</value>
  </data>
  <data name="Label_KeepDelayedEvents" xml:space="preserve">
    <value>Keep Delayed Events On State Exit</value>
  </data>
  <data name="VersionInfo_Indie_Version_Notes" xml:space="preserve">
    <value>Indie Version: You cannot make custom actions, but otherwise fully functional. No watermark.</value>
  </data>
  <data name="Menu_New_Event" xml:space="preserve">
    <value>New Event...</value>
  </data>
  <data name="DebugToolbar_Label_Debug_Tooltip" xml:space="preserve">
    <value>Debug Options</value>
  </data>
  <data name="Tooltip_EventManager_Edit_Event" xml:space="preserve">
    <value>Edit the selected Event. NOTE: This only renames the event in this FSM. Use the Event Browser to rename events across the project.</value>
  </data>
  <data name="CustomActionWizard_Add_Methods" xml:space="preserve">
    <value>Add Methods</value>
  </data>
  <data name="Dialog_Delete_Variable_Are_you_sure" xml:space="preserve">
    <value>This variable is used! Are you sure you want to delete it?</value>
  </data>
  <data name="ActionEditor_EditFsmEventTarget_FSM_Name_Tooltip" xml:space="preserve">
    <value>Optional name of FSM on the game object. Use this if the Game Object has more than one FSM.</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Prefab_Restrictions_Tooltip" xml:space="preserve">
    <value>Check for prefabs referencing scene objects.</value>
  </data>
  <data name="FsmEditorSettings_Load_All_PlayMakerFSM_Prefabs_When_Refactoring" xml:space="preserve">
    <value>Load All PlayMakerFSM Prefabs When Refactoring.</value>
  </data>
  <data name="Menu_Tools_Select_Used_Actions_in_Project_View" xml:space="preserve">
    <value>Tools/Select Used Actions in Project View</value>
  </data>
  <data name="Hint_Broadcast_Event" xml:space="preserve">
    <value>Broadcast Event to all FSMs.
NOTE: Event must be marked as global in the Event Browser of Events Inspector.</value>
  </data>
  <data name="ActionReportWindow_Action_Changes_Title" xml:space="preserve">
    <value>	Action Changes:</value>
  </data>
  <data name="Menu_Step_Single_Frame" xml:space="preserve">
    <value>Step Single Frame</value>
  </data>
  <data name="Tooltip_Globals" xml:space="preserve">
    <value>Global variables used by this FSM.</value>
  </data>
  <data name="FsmEditorSettings_Inspector_Wide_Mode" xml:space="preserve">
    <value>Inspector Wide Mode</value>
  </data>
  <data name="FsmEditorSettings_Jump_to_Breakpoint_Error" xml:space="preserve">
    <value>Jump to Breakpoint/Error</value>
  </data>
  <data name="Menu_Unused_Variable" xml:space="preserve">
    <value>No states use this variable...</value>
  </data>
  <data name="Dialog_Option_Continue" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="Dialogs_PREVIEW_VERSION" xml:space="preserve">
    <value>PREVIEW VERSION</value>
  </data>
  <data name="FsmEditorSettings_Enable_Transition_Effects" xml:space="preserve">
    <value>Enable Transition Effect</value>
  </data>
  <data name="WelcomeWindow_Photon_Cloud" xml:space="preserve">
    <value>Photon Cloud</value>
  </data>
  <data name="FsmEditorSettings_Link_Style" xml:space="preserve">
    <value>Link Style</value>
  </data>
  <data name="Label_Controls" xml:space="preserve">
    <value>Controls</value>
  </data>
  <data name="FsmEditorSettings_DoGraphViewSettings_Tooltip" xml:space="preserve">
    <value>By default the mouse wheel zooms the graph view.
Check this option if you'd rather scroll the Graph View and use Ctrl + Mouse Wheel to zoom.
NOTE: This also applies to trackpad and magic mouse gestures.</value>
  </data>
  <data name="WelcomeWindow_Tutorials" xml:space="preserve">
    <value>Video Tutorials</value>
  </data>
  <data name="FsmErrorChecker_PrefabReferencingSceneObjectError" xml:space="preserve">
    <value>Prefabs should not reference scene objects, only other prefabs and project resources. References to scene objects will be lost on save/load!</value>
  </data>
  <data name="Dialog_Replace_Start_State" xml:space="preserve">
    <value>Replace Start State?</value>
  </data>
  <data name="Command_Set_Transition_Target" xml:space="preserve">
    <value>Set Transition Target</value>
  </data>
  <data name="DragAndDrop_AddAction" xml:space="preserve">
    <value>AddAction</value>
  </data>
  <data name="Variable" xml:space="preserve">
    <value>Variable</value>
  </data>
  <data name="DebugToolbar_Button_Next_Tooltip" xml:space="preserve">
    <value>Next Transition</value>
  </data>
  <data name="Dialog_Are_you_sure" xml:space="preserve">
    <value>Are you sure?</value>
  </data>
  <data name="Label_Camera_Distance" xml:space="preserve">
    <value>Distance</value>
  </data>
  <data name="Dialog_Save_Template" xml:space="preserve">
    <value>Save Template</value>
  </data>
  <data name="CustomActionWizard_Select_Category" xml:space="preserve">
    <value>Select Category</value>
  </data>
  <data name="Label_Global_Variables" xml:space="preserve">
    <value>Global Variables</value>
  </data>
  <data name="Label_Inspector_Colors" xml:space="preserve">
    <value>Inspector Colors</value>
  </data>
  <data name="Hint_Send_Event_to_FsmComponent" xml:space="preserve">
    <value>Send Event directly to an PlayMakerFSM component. Select or drag the component onto the field.
NOTE: Event must be marked as global in the Event Browser of Events Inspector.</value>
  </data>
  <data name="CustomActionWizard_Generated_Code_Folder" xml:space="preserve">
    <value>Generated Code Folder</value>
  </data>
  <data name="Command_Set_State_Color" xml:space="preserve">
    <value>Set State Color</value>
  </data>
  <data name="CustomActionWizard_Find_File_Tooltip" xml:space="preserve">
    <value>Ping the script file in the Project View.</value>
  </data>
  <data name="Menu_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Missing_Actions" xml:space="preserve">
    <value>Check for Missing Actions</value>
  </data>
  <data name="ToolWindow_Title" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="Menu_Paste_Actions_Replace" xml:space="preserve">
    <value>Paste Actions Replace</value>
  </data>
  <data name="WelcomeWindow_Addons" xml:space="preserve">
    <value>Add-Ons</value>
  </data>
  <data name="Menu_Check_for_Missing_Actions" xml:space="preserve">
    <value>Check for Missing Actions</value>
  </data>
  <data name="ActionEditor_EditFsmProperty_Target_Object_Tooltip" xml:space="preserve">
    <value>Select a Target Object</value>
  </data>
  <data name="Menu_Enable_Breakpoints" xml:space="preserve">
    <value>Enable Breakpoints</value>
  </data>
  <data name="FsmEditorSettings_Default_Screenshots_Path" xml:space="preserve">
    <value>PlayMaker/Screenshots/</value>
  </data>
  <data name="Label_Experimental" xml:space="preserve">
    <value>Experimental</value>
  </data>
  <data name="Tooltip_ManualUpdate" xml:space="preserve">
    <value>Don't use MonoBehaviour.Update() to update the FSM. Instead you can update the FSM manually whenever you want using PlayMakerFSM.Fsm.Update. E.g., You could use this to update the FSM from a Coroutine.</value>
  </data>
  <data name="ActionEditor_EditLayoutOption_Option" xml:space="preserve">
    <value> Option</value>
  </data>
  <data name="Label_Variable_Input_Tooltip" xml:space="preserve">
    <value>Expose this variable as an Input. Can be set in the FSM Inspector or Template controls.</value>
  </data>
  <data name="ActionEditor_Add_Component_XXX" xml:space="preserve">
    <value>Add Component: {0}</value>
  </data>
  <data name="Label_Editing_Prefab_Instance" xml:space="preserve">
    <value>Editing Prefab Instance. See Preferences.</value>
  </data>
  <data name="FsmEditorSettings_Show_Scrollbars_All_The_Time_Tooltip" xml:space="preserve">
    <value>When disabled, scrollbars are only shown when you drag the canvas. Note: Use the middle mouse button to drag the canvas.</value>
  </data>
  <data name="ToolWindow_Header_Transition_Tools" xml:space="preserve">
    <value>Transition Tools:</value>
  </data>
  <data name="AboutPlaymaker_Special_Thanks" xml:space="preserve">
    <value>Special Thanks:
{0}</value>
  </data>
  <data name="BugReportWindow_How_often_does_it_happen" xml:space="preserve">
    <value>How often does it happen</value>
  </data>
  <data name="Label_Note_disable_in_preferences" xml:space="preserve">
    <value>NOTE: You can turn this off in preferences.</value>
  </data>
  <data name="Tooltip_Use_categories_to_organize_Templates" xml:space="preserve">
    <value>Use categories to organize Templates.</value>
  </data>
  <data name="ActionSelector_Add_Action_to_New_FSM" xml:space="preserve">
    <value>Add Action to New FSM</value>
  </data>
  <data name="Hint_Inspector_Usage" xml:space="preserve">
    <value>Expose Variables and Events in the PlayMaker FSM Inspector to build a custom control panel for an FSM.</value>
  </data>
  <data name="CustomActionWizard_Error_Could_not_create_directory" xml:space="preserve">
    <value>Could not create directory: </value>
  </data>
  <data name="FsmEditorSettings_Check_for_Duplicate_Transition_Events" xml:space="preserve">
    <value>Check for Duplicate Transition Events</value>
  </data>
  <data name="Tooltip_Use_Variable" xml:space="preserve">
    <value>Use Variable</value>
  </data>
  <data name="ActionEditor_Method_Name" xml:space="preserve">
    <value>Method Name</value>
  </data>
  <data name="VersionInfo_NaCL_Version_Notes" xml:space="preserve">
    <value>NaCL Version: Networking is not supported in NaCL builds.</value>
  </data>
  <data name="FsmEditorSettings_Show_FSM_Description_in_Graph_View" xml:space="preserve">
    <value>Show FSM Description in Graph View</value>
  </data>
  <data name="FsmEditorSettings_Enable_DebugFlow_Tooltip" xml:space="preserve">
    <value>Enable recording of variables and other state information for DebugFlow mode. This is a global setting; if enabled, you can still enable/disable it on individual FSMs. NOTE: Disabling this can improve performance in the editor (it is always disabled in standalone builds).</value>
  </data>
  <data name="ErrorSelector_Filter_Selected_FSM" xml:space="preserve">
    <value>Only show errors in selected FSM.</value>
  </data>
  <data name="FsmEditorSettings_Disconnect_Modified_Prefab_Instance_Tooltip" xml:space="preserve">
    <value>Disconnects a Prefab Instance when you make certain edits.E.g., Adding or removing states, transitions, events, variables...</value>
  </data>
  <data name="Command_Export_Globals" xml:space="preserve">
    <value>Export Globals</value>
  </data>
  <data name="GlobalVariablesWindow_Refresh_Tooltip" xml:space="preserve">
    <value>Recalculate usages. Might take a few seconds...</value>
  </data>
  <data name="Tooltip_Trigger_Stay_2D" xml:space="preserve">
    <value>Sent while the GameObject stays inside a 2D Trigger.</value>
  </data>
  <data name="ActionEditor_Send_Event_to_FSM_on_GameObject_Tooltip" xml:space="preserve">
    <value>Send event to a specific FSM on a game object.</value>
  </data>
  <data name="Label_This_FSM_is_Locked" xml:space="preserve">
    <value>This FSM is Locked. Enter password and press Unlock.</value>
  </data>
  <data name="Label_Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Menu_Clear_List" xml:space="preserve">
    <value>Clear List</value>
  </data>
  <data name="FsmEditorSettings_Default_State_Name" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="Hint_Enable_inspector_when_playing" xml:space="preserve">
    <value>Enable Inspector When Playing</value>
  </data>
  <data name="Menu_Check_for_Transitions_Missing_Events" xml:space="preserve">
    <value>Check for Transitions Missing Events</value>
  </data>
  <data name="Menu_Received_By" xml:space="preserve">
    <value>Received By...</value>
  </data>
  <data name="Dialog_Delete_Extra_PlayMakerGUI_GameObject" xml:space="preserve">
    <value>Delete: {0}?</value>
  </data>
  <data name="Menu_Debug_Variable_Values" xml:space="preserve">
    <value>Debug Variable Values</value>
  </data>
  <data name="Error_Event_already_used" xml:space="preserve">
    <value>Event already used!</value>
  </data>
  <data name="FsmSelector_no_active_state" xml:space="preserve">
    <value>[no active state]</value>
  </data>
  <data name="Themes_Flat" xml:space="preserve">
    <value>Flat</value>
  </data>
  <data name="Error_Some_actions_have_changed_since_FSMs_were_saved" xml:space="preserve">
    <value>Some actions have changed since FSMs were saved! Please check the PlayMaker Console for errors...</value>
  </data>
  <data name="File_Invalid_Path" xml:space="preserve">
    <value>Invalid path: {0}</value>
  </data>
  <data name="Label_Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Label_Prefab_postfix" xml:space="preserve">
    <value> (Prefab)</value>
  </data>
  <data name="Menu_Close_Window_After_Adding_Action" xml:space="preserve">
    <value>Close Window After Adding Action</value>
  </data>
  <data name="Command_Add_FSM_to_Selected" xml:space="preserve">
    <value>Add FSM to Selected</value>
  </data>
  <data name="Menu_Create_Object" xml:space="preserve">
    <value>Create Object</value>
  </data>
  <data name="Hint_EventManager_Expose_Events" xml:space="preserve">
    <value>Expose Events and Variables in the PlayMaker FSM Inspector to build a custom control panel for an FSM.</value>
  </data>
  <data name="Label_Variable_Type_Tooltip" xml:space="preserve">
    <value>The variable type.</value>
  </data>
  <data name="Hint_State_Inspector" xml:space="preserve">
    <value>This panel lets you edit a State.
Select a State in the Graph View or Right Click below...</value>
  </data>
  <data name="ActionUtility_AddAction_Missing_Action" xml:space="preserve">
    <value>PlayMaker Missing Action: {0}</value>
  </data>
  <data name="Menu_Paste_Variables" xml:space="preserve">
    <value>Paste Variables</value>
  </data>
  <data name="VersionInfo_INDIE_VERSION" xml:space="preserve">
    <value>INDIE VERSION</value>
  </data>
  <data name="Menu_Disable_Error_Checker_When_Game_Is_Playing" xml:space="preserve">
    <value>Disable Error Checker When Game Is Playing</value>
  </data>
  <data name="BugReportWindow_Submit_Button" xml:space="preserve">
    <value>Submit Report</value>
  </data>
  <data name="DebugToolbar_Error_Count_Tooltip" xml:space="preserve">
    <value>Error Check</value>
  </data>
  <data name="Tooltip_Variable_Type" xml:space="preserve">
    <value>View/Edit the Variable's type.</value>
  </data>
  <data name="Tooltip_Variable_Name" xml:space="preserve">
    <value>Right-click Variable to see States that use the Variable.</value>
  </data>
  <data name="FsmEditorSettings_Translators" xml:space="preserve">
    <value>Translators: </value>
  </data>
  <data name="ActionEditor_Store_Enum" xml:space="preserve">
    <value>Store Enum</value>
  </data>
  <data name="ActionEditor_Store_Bool" xml:space="preserve">
    <value>Store Bool</value>
  </data>
  <data name="FsmEditorSettings_Max_State_Width_Tooltip" xml:space="preserve">
    <value>How wide a State box can grow to accomodate its contents. Default is 200.</value>
  </data>
  <data name="ActionEditor_Store_Rect" xml:space="preserve">
    <value>Store Rect</value>
  </data>
  <data name="Menu_No_Animator_Parameters" xml:space="preserve">
    <value>No {0} Parameters...</value>
  </data>
  <data name="Command_Move_Actions" xml:space="preserve">
    <value>Move Actions</value>
  </data>
  <data name="Tooltip_State_Label_Scale" xml:space="preserve">
    <value>Scale state labels. Useful for Retina/HiDPI screens where state labels can be hard to read.</value>
  </data>
  <data name="ActionEditor_Store_Vector2" xml:space="preserve">
    <value>Store Vector2</value>
  </data>
  <data name="ActionEditor_Store_Vector3" xml:space="preserve">
    <value>Store Vector3</value>
  </data>
  <data name="Menu_GraphView_Set_Start_State" xml:space="preserve">
    <value>Set as Start State</value>
  </data>
  <data name="Command_Reset_Action" xml:space="preserve">
    <value>Reset Action</value>
  </data>
  <data name="DebugToolbar_Button_Prev_Toolrip" xml:space="preserve">
    <value>Previous Transition</value>
  </data>
  <data name="Dialog_Make_Local_Variable" xml:space="preserve">
    <value>Make Local Variable</value>
  </data>
  <data name="VersionInfo_Student_Version_Notes" xml:space="preserve">
    <value>Student Version: You cannot make custom actions, but otherwise fully functional.
NOTE: For non-commercial uses only!</value>
  </data>
  <data name="Menu_GraphView_Delete_Template" xml:space="preserve">
    <value>Delete Template</value>
  </data>
  <data name="Dialog_No_unused_variables___" xml:space="preserve">
    <value>No unused variables...</value>
  </data>
  <data name="Error_Missing_Action__Get_Property" xml:space="preserve">
    <value>PlayMaker Missing Action: Get Property</value>
  </data>
  <data name="Menu_Copy_Selected_Actions" xml:space="preserve">
    <value>Copy Selected Actions</value>
  </data>
  <data name="FsmErrorChecker_DuplicateTransitionEventError" xml:space="preserve">
    <value>A transition is used more than once on the same state!</value>
  </data>
  <data name="FsmEditorSettings_Select_Game_Objects_With_FSMs_in_Game_View_Tooltip" xml:space="preserve">
    <value>Lets you click on game objects in a running game to select their state machines. NOTE: only works on game objects with colliders.</value>
  </data>
  <data name="FsmEditorSettings_Enable_Watermarks" xml:space="preserve">
    <value>Enable Watermarks</value>
  </data>
  <data name="FsmEditorSettings_Disable_PlayMaker_Editor_When_Game_Is_Playing_Tooltip" xml:space="preserve">
    <value>If you find performance too slow with the editor running, you can turn it off here.</value>
  </data>
  <data name="FsmEditorSettings_Color_Links_With_State_Color" xml:space="preserve">
    <value>Color Links With State Color</value>
  </data>
  <data name="Command_Enable_Action" xml:space="preserve">
    <value>Enable Action</value>
  </data>
  <data name="Label_Confirm_Editing_Prefab_Instances" xml:space="preserve">
    <value>Confirm Editing Prefab Instances</value>
  </data>
  <data name="Menu_None_FSM" xml:space="preserve">
    <value>None (FSM)</value>
  </data>
  <data name="FsmEditorSettings_New_State_Name_Tooltip" xml:space="preserve">
    <value>The default name for new states. Note: State names must be unique within each Fsm. Auto named states will append numbers when necessary.</value>
  </data>
  <data name="Menu_Add_Event_to_FSM" xml:space="preserve">
    <value>Add Event to FSM</value>
  </data>
  <data name="FsmEditorSettings_Inspector_Panel_Width" xml:space="preserve">
    <value>Inspector Panel Width</value>
  </data>
  <data name="Label_Editor_disabled_when_playing" xml:space="preserve">
    <value>Editor disabled when playing. See Preferences.</value>
  </data>
  <data name="Command_Toggle_Breakpoint" xml:space="preserve">
    <value>Toggle Breakpoint</value>
  </data>
  <data name="Menu_Auto_Refresh_Globals" xml:space="preserve">
    <value>Auto Refresh On Focus</value>
  </data>
  <data name="Menu_Move_To_Global_Variables" xml:space="preserve">
    <value>Move To Global Variables</value>
  </data>
  <data name="Tooltip_Use_Template" xml:space="preserve">
    <value>Templates let you share the same FSM across GameObjects.</value>
  </data>
  <data name="Hint_Send_Event_to_FSM_on_GameObject" xml:space="preserve">
    <value>Send Event to a specific FSM on a GameObject.
NOTE: Event must be marked as global in the Event Browser or Events Inspector.</value>
  </data>
  <data name="FsmEditorSettings_DoGraphViewSettings_Zoom_Speed_Tooltip" xml:space="preserve">
    <value>Controls how fast the Graph View zooms in/out.</value>
  </data>
  <data name="Menu_Buy_Playmaker" xml:space="preserve">
    <value>Buy PlayMaker at HutongGames.com</value>
  </data>
  <data name="FsmTemplateEditor_Open_In_Editor" xml:space="preserve">
    <value>Edit in PlayMaker FSM Editor</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Required_Components" xml:space="preserve">
    <value>Check for Required Components</value>
  </data>
  <data name="Label_Postfix_FSMs_Plural" xml:space="preserve">
    <value> FSMs!</value>
  </data>
  <data name="Label_Watermarks_Are_Disabled" xml:space="preserve">
    <value>Watermarks are disabled in Preferences.</value>
  </data>
  <data name="FsmEditorSettings_Auto_Summarize_Tooltip" xml:space="preserve">
    <value>Summarize an action in its title bar when collapsed. Not supported by all actions yet; Actions needs an AutoName method. Still experimental.</value>
  </data>
  <data name="Dialog_Add_FSM_to_multiple_objects_" xml:space="preserve">
    <value>Add FSM to multiple objects?</value>
  </data>
  <data name="Menu_FSM_Browser" xml:space="preserve">
    <value>FSM Browser</value>
  </data>
  <data name="Label_System_Event" xml:space="preserve">
    <value>System Event</value>
  </data>
  <data name="FsmEditorSettings_Show_FSM_Password_Controls" xml:space="preserve">
    <value>Show FSM Password Controls in FSM Inspector</value>
  </data>
  <data name="Label_Preview_GUI_Actions_While_Editing" xml:space="preserve">
    <value>Preview GUI Actions While Editing</value>
  </data>
  <data name="Hint_DebugFlow" xml:space="preserve">
    <value>Debug Flow Mode: Action panel shows variable values when state was entered.</value>
  </data>
  <data name="Dialog_Editing_Prefab_while_game_is_running" xml:space="preserve">
    <value>You are editing a Prefab while the game is playing.
Changes WILL persist after you stop playing!</value>
  </data>
  <data name="ActionSelector_Title" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="FsmEditorSettings_Scrolling" xml:space="preserve">
    <value>Scrolling</value>
  </data>
  <data name="Menu_GlobalsRoot" xml:space="preserve">
    <value>Globals/</value>
  </data>
  <data name="Menus_TemplateUsageInFsmMenu_Find_in_Template_Browser" xml:space="preserve">
    <value>Find in Template Browser</value>
  </data>
  <data name="Command_Delete_All_Actions" xml:space="preserve">
    <value>Delete All Actions</value>
  </data>
  <data name="Label_Debug_Tooltip" xml:space="preserve">
    <value>Controls for debugging in editor.</value>
  </data>
  <data name="Variable_Tooltip_Warning" xml:space="preserve">
    <value>NOTE: You cannot rename globals since they might be used in other scenes. For now you have to make a new variable and reference that. We will add Rename in a future update.</value>
  </data>
  <data name="FsmGraphView_Click_to_Edit_Template" xml:space="preserve">
    <value>[Click to Edit Template]</value>
  </data>
  <data name="Hint_Use_Event_Browser_to_rename_globally" xml:space="preserve">
    <value>NOTE: Use the Event Browser to rename Events across the project. Edit the name below to affect only this FSM.</value>
  </data>
  <data name="FsmEditorSettings_Color_Scheme_Tooltip" xml:space="preserve">
    <value>Default pickes a color scheme that matched Pro/Indie skins.</value>
  </data>
  <data name="FsmEditorSettings_Select_State_On_Activated" xml:space="preserve">
    <value>Select State On Activated</value>
  </data>
  <data name="Tooltip_New_Variable" xml:space="preserve">
    <value>Add a Variable to this FSM. Variables are used by Actions to store information</value>
  </data>
  <data name="Tooltip_Global_Variables_Header" xml:space="preserve">
    <value>Click to sort by name.
Right-click Variable to see FSMs that use the Variable.</value>
  </data>
  <data name="Hint_Action_Browser_Workflow" xml:space="preserve">
    <value>Workflow Tip: Hit ~ to open the Action Browser, start typing to filter the Action list, use Up/Down keys to select an Action, and hit Enter to add it to the State. Actions are inserted before any selected Action in the list.</value>
  </data>
  <data name="Menu_Check_for_Prefab_Restrictions" xml:space="preserve">
    <value>Check for Prefab Restrictions</value>
  </data>
  <data name="Menu_Auto_Name" xml:space="preserve">
    <value>Auto Name</value>
  </data>
  <data name="Menu_Select_Prefab_Parent" xml:space="preserve">
    <value>Select Prefab Parent</value>
  </data>
  <data name="SettingsButton_Tooltip" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="FsmSelector_Title" xml:space="preserve">
    <value>FSM Browser</value>
  </data>
  <data name="FsmEditorSettings_Debug_Raypick_Color" xml:space="preserve">
    <value>Debug Raypick Color</value>
  </data>
  <data name="Command_Select_GameObject" xml:space="preserve">
    <value>Select GameObject</value>
  </data>
  <data name="ActionReportWindow_Sort_By_FSM" xml:space="preserve">
    <value>Sort By FSM</value>
  </data>
  <data name="FsmEditorSettings_Show_Send_Events_Icons" xml:space="preserve">
    <value>Show Send Events Icons on States</value>
  </data>
  <data name="Label_Event_Name" xml:space="preserve">
    <value>Event Name</value>
  </data>
  <data name="Hint_Select_FSM" xml:space="preserve">
    <value>Select FSM...</value>
  </data>
  <data name="Error_Failed_to_load_Object_Property_Drawer" xml:space="preserve">
    <value>Failed to load Object Property Drawer inspected type.</value>
  </data>
  <data name="CustomActionWizard_Invalid_Action_Name" xml:space="preserve">
    <value>Please enter a valid action name.</value>
  </data>
  <data name="Dialog_Import_Globals" xml:space="preserve">
    <value>Import Globals</value>
  </data>
  <data name="Tooltip_UI_Pointer_Exit" xml:space="preserve">
    <value>Sent when the mouse stops hovering over the GameObject.</value>
  </data>
  <data name="Tooltip_UI_Pointer_Down" xml:space="preserve">
    <value>Sent during ongoing mouse clicks until release of the mouse button.</value>
  </data>
  <data name="Tooltip_Application_Focus" xml:space="preserve">
    <value>Sent when the Application gets focus.</value>
  </data>
  <data name="Label_Model_Prefab_Instance_disconnected" xml:space="preserve">
    <value>Model Prefab Instance (disconnected)</value>
  </data>
  <data name="Command_Move_Down" xml:space="preserve">
    <value>Move Down</value>
  </data>
  <data name="FsmEditorSettings_Load_All_PlayMakerFSM_Prefabs_When_Refactoring_Tooltip" xml:space="preserve">
    <value>Load all prefabs in the project when renaming events and variables.</value>
  </data>
  <data name="Label_States_Count" xml:space="preserve">
    <value>States [{0}]</value>
  </data>
  <data name="Tooltip_Application_Pause" xml:space="preserve">
    <value>Sent when the Application is paused.</value>
  </data>
  <data name="Menu_Check_for_Required_Components" xml:space="preserve">
    <value>Check for Required Components</value>
  </data>
  <data name="Label_Data_Version" xml:space="preserve">
    <value>Data Version: </value>
  </data>
  <data name="Tooltip_Max_Loop_Override" xml:space="preserve">
    <value>Override the default maximum loop threshold of 1000. Set to 0 to keep default.</value>
  </data>
  <data name="BugReportWindow_MissingEmail" xml:space="preserve">
    <value>Please Enter your Email Address
</value>
  </data>
  <data name="Error_Multiple_PlayMakerGUI_components" xml:space="preserve">
    <value>The scene has more than one PlayMakerGUI!
Remove other instances?</value>
  </data>
  <data name="Tooltip_Action_Browser" xml:space="preserve">
    <value>Open the Action Browser to add actions to this State.</value>
  </data>
  <data name="Menu_Disable_Window_When_Playing" xml:space="preserve">
    <value>Disable Window When Playing</value>
  </data>
  <data name="Tooltip_UI_Vector2_Value_Changed" xml:space="preserve">
    <value>Sent when a ScrollRect value changes. GameObject needs a ScrollRect component. Use Get Event Vector2 Data to get the value.</value>
  </data>
  <data name="BugReportWindow_MissingTitle" xml:space="preserve">
    <value>Please Enter a Title
</value>
  </data>
  <data name="ErrorSelector_Filter_Selected_FSM_Only" xml:space="preserve">
    <value>Selected FSM Only</value>
  </data>
  <data name="Label_Select_GameObject" xml:space="preserve">
    <value>Select GameObject</value>
  </data>
  <data name="Menu_Online_Help" xml:space="preserve">
    <value>Online Help</value>
  </data>
  <data name="Label_Manual_Update" xml:space="preserve">
    <value>Manual Update</value>
  </data>
  <data name="Menu_Delete_Category" xml:space="preserve">
    <value>Delete Category</value>
  </data>
  <data name="Label_Importing_" xml:space="preserve">
    <value>Importing: </value>
  </data>
  <data name="Menu_Recent_Category_Size" xml:space="preserve">
    <value>Recent Category Size</value>
  </data>
  <data name="Command_Lock_Selected_FSM" xml:space="preserve">
    <value>Lock</value>
  </data>
  <data name="Label_Reset_On_Disable" xml:space="preserve">
    <value>Reset State On Disable</value>
  </data>
  <data name="Menu_Paste_Actions_After" xml:space="preserve">
    <value>Paste Actions After</value>
  </data>
  <data name="FsmEditorSettings_Show_Comments_in_Graph_View" xml:space="preserve">
    <value>Show Comments in Graph View</value>
  </data>
  <data name="Menu_Show_Full_FSM_Path" xml:space="preserve">
    <value>Show Full FSM Path</value>
  </data>
  <data name="Label_State_Label_Scale" xml:space="preserve">
    <value>Label Scale</value>
  </data>
  <data name="Hint_Network_Sync_Variables" xml:space="preserve">
    <value>Check NetworkSync in the Variables Tab to automatically sync a variable across the network.
NOTE: The GameObject requires a Network View that Observes the PlayMakerFSM (drag the PlayMakerFSM component into the Observed field).</value>
  </data>
  <data name="Hint_System_Events_cannot_be_renamed" xml:space="preserve">
    <value>NOTE: System Events cannot be renamed!</value>
  </data>
  <data name="Dialog_Edit_Variable_Type_Are_you_sure" xml:space="preserve">
    <value>This variable is used! Are you sure you want to change its type?</value>
  </data>
  <data name="Menu_Step_To_Next_State_Change_in_this_FSM" xml:space="preserve">
    <value>Step To Next State Change in this FSM</value>
  </data>
  <data name="Menu_Move_Action_To_Bottom" xml:space="preserve">
    <value>Move Action To Bottom</value>
  </data>
  <data name="Label_None_In_Table" xml:space="preserve">
    <value>[none]</value>
  </data>
  <data name="Label_Enable_Tool_Windows_When_Playing" xml:space="preserve">
    <value>Enable Tool Windows When Playing</value>
  </data>
  <data name="Hint_Use_Action_Browser_To_Add_Actions" xml:space="preserve">
    <value>Use the Action Browser to Add Actions to this State.</value>
  </data>
  <data name="Dialog_Rename_Event" xml:space="preserve">
    <value>Rename Event</value>
  </data>
  <data name="Menu_Add_After_Selected_Action" xml:space="preserve">
    <value>Add After Selected Action</value>
  </data>
  <data name="Tooltip_Particle_Collision" xml:space="preserve">
    <value>Sent when a particle hits a Collider. See unity docs for more info.</value>
  </data>
  <data name="Tooltip_Trigger_Exit_2D" xml:space="preserve">
    <value>Sent when the GameObject exits a 2D Trigger.</value>
  </data>
  <data name="Label_Globals" xml:space="preserve">
    <value>Globals</value>
  </data>
  <data name="Command_Edit_Event_Global_Setting" xml:space="preserve">
    <value>Edit Event Global Setting</value>
  </data>
  <data name="Menu_NewGlobalEvent" xml:space="preserve">
    <value>New Global Event...</value>
  </data>
  <data name="Label_Variable_Type" xml:space="preserve">
    <value>Variable Type</value>
  </data>
  <data name="ErrorSelector_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="Hint_Right_Click_to_Add_FSM_to_" xml:space="preserve">
    <value>Right-Click to Add FSM to </value>
  </data>
  <data name="ActionEditor_EditFsmEventTarget_FSM_Name" xml:space="preserve">
    <value>FSM Name</value>
  </data>
  <data name="Dialog_Use_Saved_Template_in_this_FSM" xml:space="preserve">
    <value>Use Saved Template in this FSM?</value>
  </data>
  <data name="Menu_Add_FSM_to__" xml:space="preserve">
    <value>Add FSM to {0}</value>
  </data>
  <data name="BugReportWindow_MissingDescription" xml:space="preserve">
    <value>Please Enter a Description
</value>
  </data>
  <data name="FsmEditorSettings_Category_Labels" xml:space="preserve">
    <value>Labels</value>
  </data>
  <data name="Menu_Hide_Prefabs_When_Playing" xml:space="preserve">
    <value>Hide Prefabs When Playing</value>
  </data>
  <data name="Label_Tween_To_Color" xml:space="preserve">
    <value>Tween To Color</value>
  </data>
  <data name="Tooltip_UI_Float_Value_Changed" xml:space="preserve">
    <value>Sent when a Slider or Scrollbar value changes. GameObject needs a Slider or Scrollbar component. Use Get Event Float Data to get the value.</value>
  </data>
  <data name="BugReportWindow_Where_does_it_happen" xml:space="preserve">
    <value>Where does it happen</value>
  </data>
  <data name="Dialog_Add_FSM_Template" xml:space="preserve">
    <value>Add FSM Template</value>
  </data>
  <data name="Themes_Default" xml:space="preserve">
    <value>Default</value>
  </data>
  <data name="FsmEditorSettings_Minimap_Size_Tooltip" xml:space="preserve">
    <value>Minimap size in pixels.</value>
  </data>
  <data name="Dialog_Editing_FSM_while_game_is_running" xml:space="preserve">
    <value>You are editing the FSM while the game is playing.
Changes will be lost when you stop playing!</value>
  </data>
  <data name="Menu_Move_Action_To_Top" xml:space="preserve">
    <value>Move Action To Top</value>
  </data>
  <data name="Command_Paste_FSM" xml:space="preserve">
    <value>Paste FSM</value>
  </data>
  <data name="FsmEditorSettings_Category_Selection" xml:space="preserve">
    <value>Selection</value>
  </data>
  <data name="FsmEditorSettings_Cetegory_Paths" xml:space="preserve">
    <value>Paths</value>
  </data>
  <data name="Menu_Show_State_Loop_Counts" xml:space="preserve">
    <value>Show State Loop Counts</value>
  </data>
  <data name="FsmEditorSettings_Add_Prefab_Labels_Tooltip" xml:space="preserve">
    <value>Add (Prefab) to prefab labels.</value>
  </data>
  <data name="Label_Help_Url" xml:space="preserve">
    <value>Help Url</value>
  </data>
  <data name="Label_Loading_Watermark_Textures___" xml:space="preserve">
    <value>Loading Watermark Textures...</value>
  </data>
  <data name="Menu_GraphView_Link_Direction_Right" xml:space="preserve">
    <value>Link Direction/Lock To Right</value>
  </data>
  <data name="Tooltip_Draw_Active_State_Labels" xml:space="preserve">
    <value>Draw the currently active state over GameObjects in the Game View. You can enable/disable for each FSM in the PlayMakerFSM Inspector.</value>
  </data>
  <data name="Menu_GraphView_CustomEvents" xml:space="preserve">
    <value>Custom Events/</value>
  </data>
  <data name="FsmEditorSettings_Preferences_Reset" xml:space="preserve">
    <value>PlayMaker: Preferences Reset!</value>
  </data>
  <data name="Tooltip_Network_Sync_Not_Supported" xml:space="preserve">
    <value>Network Sync is not available for this variable type.</value>
  </data>
  <data name="ActionEditor_Send_To_Children" xml:space="preserve">
    <value>Send To Children</value>
  </data>
  <data name="FsmEditorSettings_Show_Comments_in_Graph_View_Tooltip" xml:space="preserve">
    <value>Add descriptions to FSMs and States and see them in the Graph View</value>
  </data>
  <data name="Menu_No_FSMs_Use_This_Event" xml:space="preserve">
    <value>No FSMs use this event...</value>
  </data>
  <data name="Toggle" xml:space="preserve">
    <value>Toggle</value>
  </data>
  <data name="ActionSelector_Count_Postfix" xml:space="preserve">
    <value>[{0}]</value>
  </data>
  <data name="Command_Save_Selection_as_Template" xml:space="preserve">
    <value>Save Selection as Template</value>
  </data>
  <data name="Label_Category___" xml:space="preserve">
    <value>Category...</value>
  </data>
  <data name="Command_New_Global_Event" xml:space="preserve">
    <value>New Global Event</value>
  </data>
  <data name="Error_Name_is_too_long" xml:space="preserve">
    <value>Name is too long!</value>
  </data>
  <data name="Error_Event_is_not_marked_Global" xml:space="preserve">
    <value>Event is not marked Global!</value>
  </data>
  <data name="Label_Prefab_Instance_disconnected" xml:space="preserve">
    <value>Prefab Instance (disconnected)</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Network_Setup_Errors" xml:space="preserve">
    <value>Check for Network Setup Errors</value>
  </data>
  <data name="Title_Screenshots" xml:space="preserve">
    <value>Screenshots</value>
  </data>
  <data name="Label_Enum_Type" xml:space="preserve">
    <value>Enum Type</value>
  </data>
  <data name="Menu_GraphView_Add_Transition" xml:space="preserve">
    <value>Add Transition/</value>
  </data>
  <data name="Tooltip_Tooltip" xml:space="preserve">
    <value>Tooltip used in Inspector and Action Editor. Not supported for Vector3.</value>
  </data>
  <data name="Dialog_Delete_Variable" xml:space="preserve">
    <value>Delete Variable</value>
  </data>
  <data name="Menu_Play_Sound" xml:space="preserve">
    <value>Play Sound</value>
  </data>
  <data name="Menu_Edit_Tool_Window" xml:space="preserve">
    <value>Edit Tool Window</value>
  </data>
  <data name="Menu_Enable_Logging" xml:space="preserve">
    <value>Enable Logging</value>
  </data>
  <data name="FsmEditorSettings_Ping_Open_Editor_Windows_Tooltip" xml:space="preserve">
    <value>Ping Editor Windows if already open when you try to open them.</value>
  </data>
  <data name="Dialog_Apply_Are_You_Sure" xml:space="preserve">
    <value>Are you sure you want to Apply all changes to the Prefab FSM?</value>
  </data>
  <data name="Dialog_UnlockFSM_Wrong_Password" xml:space="preserve">
    <value>Could not unlock FSM.
Please check the password.</value>
  </data>
  <data name="AboutPlaymaker_Hutong_Games_Link" xml:space="preserve">
    <value>Hutong Games</value>
  </data>
  <data name="FsmEditorSettings_Color_Scheme" xml:space="preserve">
    <value>Color Scheme</value>
  </data>
  <data name="Label_GUIText_State_Labels" xml:space="preserve">
    <value>GUIText State Labels</value>
  </data>
  <data name="Hint_Graph_View_Settings" xml:space="preserve">
    <value>These settings control the appearance and behaviour of the main Graph View where you build state machines.</value>
  </data>
  <data name="EventMenu_Global_Events" xml:space="preserve">
    <value>Global Events</value>
  </data>
  <data name="Menu_Inherited_Submenu" xml:space="preserve">
    <value>Inherited/{0}</value>
  </data>
  <data name="Label_No_unused_variables" xml:space="preserve">
    <value>No unused variables...</value>
  </data>
  <data name="ErrorSelector_Total_Errors" xml:space="preserve">
    <value>Total: {0}</value>
  </data>
  <data name="Tooltip_UI_Drop" xml:space="preserve">
    <value>Sent when an object accepts a drop.</value>
  </data>
  <data name="Tooltip_UI_Drag" xml:space="preserve">
    <value>Sent every time the pointer is moved during dragging.</value>
  </data>
  <data name="Menu_GraphView_Add_FSM_Component_New_FSM" xml:space="preserve">
    <value>Add FSM Component/New FSM</value>
  </data>
  <data name="FsmEditorSettings_Disconnect_All_Modified_Prefab_Instances_in_Scene_Tooltip" xml:space="preserve">
    <value>Checks loaded scene for Prefab Instance FSMs that should be disconnected from their Prefab Parent. Disconnected FSMs are listed in the Unity Console.</value>
  </data>
  <data name="Menu_GameObject_Actions" xml:space="preserve">
    <value>GameObject Actions</value>
  </data>
  <data name="Command_Rename_Action" xml:space="preserve">
    <value>Rename Action</value>
  </data>
  <data name="Error_Could_Not_Find_Action_Field" xml:space="preserve">
    <value>Could not find action field: {0}
NOTE: field must be public.</value>
  </data>
  <data name="Label_Modified_postfix" xml:space="preserve">
    <value> (Modified)</value>
  </data>
  <data name="Label_Logging_is_disabled_in_Preferences" xml:space="preserve">
    <value>Logging is disabled in Preferences...</value>
  </data>
  <data name="Tooltip_Events_Used" xml:space="preserve">
    <value>Number of FSMs that respond to the Event.</value>
  </data>
  <data name="Label_General" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="CustomActionWizard_Find_File" xml:space="preserve">
    <value>Find File</value>
  </data>
  <data name="Menu_State_Browser" xml:space="preserve">
    <value>State Browser</value>
  </data>
  <data name="Error_Could_Not_Set_Action_Field_Value" xml:space="preserve">
    <value>Could not set action field: {0}
Value type is incompatible.</value>
  </data>
  <data name="Command_Add_Event" xml:space="preserve">
    <value>Add Event</value>
  </data>
  <data name="Command_Add_State" xml:space="preserve">
    <value>Add State</value>
  </data>
  <data name="Dialog_Make_Global_Variable" xml:space="preserve">
    <value>Make Global Variable</value>
  </data>
  <data name="FsmEditorSettings_Edge_Scroll_Speed" xml:space="preserve">
    <value>Edge Scroll Speed</value>
  </data>
  <data name="Label_Templates" xml:space="preserve">
    <value>Templates</value>
  </data>
  <data name="Dialogs_BreakPrefabInstance" xml:space="preserve">
    <value>Break Prefab Instance</value>
  </data>
  <data name="Menu_NewGlobalVariable" xml:space="preserve">
    <value>New Global Variable...</value>
  </data>
  <data name="Hint_GraphView_Shortcut_Description" xml:space="preserve">
    <value>Shortcut Hints:
Add State:
Add FINISHED Event:
Add Transition State:
Quick Delete:
Snap To Grid:
Constrain Drag:
Select Start State:
Follow Transition:
Lock Link Direction:
Move Selected Transition:</value>
  </data>
  <data name="Tooltip_Select_Next_FSM" xml:space="preserve">
    <value>Select Next FSM</value>
  </data>
  <data name="FsmEditorSettings_Show_Selected_FSM_Label" xml:space="preserve">
    <value>Show Selected FSM Label</value>
  </data>
  <data name="Label_Click_to_Make_Global" xml:space="preserve">
    <value>[Click to Make Global]</value>
  </data>
  <data name="StateSelector_Title" xml:space="preserve">
    <value>State Browser</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="FsmEditorSettings_Category_Components_and_Gizmos" xml:space="preserve">
    <value>Components and Gizmos</value>
  </data>
  <data name="Hint_Export_Globals_Notes" xml:space="preserve">
    <value>Exporting and Importing Globals:

To copy Globals to a new project that doesn't already have Globals, simply include this asset in the exported unitypackage.

If the new project has Globals that you don't want to overwrite, use Export Globals in this project, include the exported asset in the unitypackage, and Use Import Globals in the new project.</value>
  </data>
  <data name="FsmEditorSettings_Select_GameObject_When_FSM_Selected" xml:space="preserve">
    <value>Select GameObject When FSM Selected</value>
  </data>
  <data name="Menu_No_FSMs_use_this_variable" xml:space="preserve">
    <value>No FSMs use this variable...</value>
  </data>
  <data name="Menu_Remove_Unused_Events" xml:space="preserve">
    <value>Remove Unused Events</value>
  </data>
  <data name="CustomActionWizard_Label_Action_Name" xml:space="preserve">
    <value>Action Name</value>
  </data>
  <data name="VariableManager_MoveToGlobals_Warning" xml:space="preserve">
    <value>A global variable with the same name but different type already exists!</value>
  </data>
  <data name="FsmLogger_Title" xml:space="preserve">
    <value>FSM Log</value>
  </data>
  <data name="Hint_Paste_Template" xml:space="preserve">
    <value>Select a template in the Templates window to enable PasteTemplate...</value>
  </data>
  <data name="Label_Global_Event" xml:space="preserve">
    <value>Global Event</value>
  </data>
  <data name="Hint_Variable_Panel" xml:space="preserve">
    <value>This panel shows the Variables used by the FSM.
Right-click Variable to select States that use the Variable.</value>
  </data>
  <data name="Hint_Variable_Usage" xml:space="preserve">
    <value>Make Variables to use in Actions. Hint: In the State panel, rollover an Action Parameter to see the type of variable required.</value>
  </data>
  <data name="Menu_Templates_Browser" xml:space="preserve">
    <value>Templates Browser</value>
  </data>
  <data name="FsmEditorSettings_Zooming" xml:space="preserve">
    <value>Zooming</value>
  </data>
  <data name="FsmEditorSettings_Disable_Undo_Redo_Tooltip" xml:space="preserve">
    <value>If editor performance is slow with large projects on slower machines you can turn off undo/redo support for better performance.</value>
  </data>
  <data name="Command_Delete_Event_From_All_FSMs" xml:space="preserve">
    <value>Delete Event From All FSMs</value>
  </data>
  <data name="Menu_Paste_FSM" xml:space="preserve">
    <value>Paste FSM</value>
  </data>
  <data name="FsmEditorSettings_Ping_Open_Editor_Windows" xml:space="preserve">
    <value>Ping Open Editor Windows</value>
  </data>
  <data name="Menu_Rename_Category" xml:space="preserve">
    <value>Rename Category</value>
  </data>
  <data name="FsmEditorSettings_Disable_Undo_Redo" xml:space="preserve">
    <value>Disable Undo/Redo</value>
  </data>
  <data name="ActionEditor_Cannot_Add_Component_Type_XXX" xml:space="preserve">
    <value>Cannot Add Component Type: {0}</value>
  </data>
  <data name="Label_FINISHED" xml:space="preserve">
    <value>[FINISHED]</value>
  </data>
  <data name="Tooltip_Select_Previous_FSM" xml:space="preserve">
    <value>Select Previous FSM</value>
  </data>
  <data name="Menu_With_Global_Transition" xml:space="preserve">
    <value>With Global Transition</value>
  </data>
  <data name="Command_Set_Start_State" xml:space="preserve">
    <value>Set Start State</value>
  </data>
  <data name="DebugToolbar_Button_Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="DebugToolbar_Button_Prev" xml:space="preserve">
    <value>Prev</value>
  </data>
  <data name="Dialog_Delete_Unused_Global_Variables" xml:space="preserve">
    <value>Delete Unused Global Variables</value>
  </data>
  <data name="FsmEditorSettings_Edge_Scroll_Speed_Tooltip" xml:space="preserve">
    <value>Sets how fast the view scrolls when dragging states/transitions near the edge of the Graph View. Default is 10, smaller is slower.</value>
  </data>
  <data name="FsmLog_Label_Sent_By" xml:space="preserve">
    <value>FROM: </value>
  </data>
  <data name="Error_Cannot_Rename_State" xml:space="preserve">
    <value>Cannot Rename State!</value>
  </data>
  <data name="Menu_Event_Browser" xml:space="preserve">
    <value>Event Browser</value>
  </data>
  <data name="FsmEditorSettings_Auto_Frame_Selected_State" xml:space="preserve">
    <value>Auto Frame Selected State</value>
  </data>
  <data name="Tooltip_Editor_Windows" xml:space="preserve">
    <value>Editor Windows</value>
  </data>
  <data name="Command_Set_Link_Style" xml:space="preserve">
    <value>Set Link Style</value>
  </data>
  <data name="Menu_GraphView_Link_Direction_Auto" xml:space="preserve">
    <value>Link Direction/Auto Direction</value>
  </data>
  <data name="Menu_GraphView_Link_Direction_Left" xml:space="preserve">
    <value>Link Direction/Lock To Left</value>
  </data>
  <data name="Error_Invalid_Event_Name" xml:space="preserve">
    <value>Invalid Event Name!</value>
  </data>
  <data name="FsmEditorSettings_Draw_Playmaker_Gizmos_in_Scene_View_Tooltip" xml:space="preserve">
    <value>Draw the Playmaker Gizmo on game objects with FSMs in the scene view.</value>
  </data>
  <data name="ActionEditor_Store_Texture" xml:space="preserve">
    <value>Store Texture</value>
  </data>
  <data name="Tooltip_Variables_Debug" xml:space="preserve">
    <value>Show the current value of variables used in Action parameters.</value>
  </data>
  <data name="VersionInfo_Trial_Version_Notes" xml:space="preserve">
    <value>Trial Version: Fully functional trial version. Builds display a PlayMaker watermark. NOTE: The watermark uses Unity GUI, which can adversely effect performance on some devices.</value>
  </data>
  <data name="Menu_Copy_Value" xml:space="preserve">
    <value>Copy Value</value>
  </data>
  <data name="CustomActionWizard_Copy_Code_Tooltip" xml:space="preserve">
    <value>Copy generated code to clipboard.</value>
  </data>
  <data name="FsmEditorSettings_New_State_Name" xml:space="preserve">
    <value>New State Name</value>
  </data>
  <data name="Label_Variable_Output_Tooltip" xml:space="preserve">
    <value>Expose this variable as an Output. Can be assigned to a variable in Template controls.</value>
  </data>
  <data name="Label_Debugging" xml:space="preserve">
    <value>Debugging</value>
  </data>
  <data name="Tooltip_Trigger_Exit" xml:space="preserve">
    <value>Sent when the GameObject exits a trigger volume.</value>
  </data>
  <data name="Tooltip_Trigger_Stay" xml:space="preserve">
    <value>Sent while the GameObject stays inside a trigger volume.</value>
  </data>
  <data name="Command_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Label_New_Variable" xml:space="preserve">
    <value>New Variable</value>
  </data>
  <data name="Command_Paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="Error_Name_contains_illegal_character" xml:space="preserve">
    <value>Name contains illegal character: \</value>
  </data>
  <data name="Hint_Action_Shortcuts" xml:space="preserve">
    <value>Double click action title to edit action name.
Ctrl-click foldout to expand/collapse all actions.
Ctrl-click enable checkbox to enable/disable all actions.</value>
  </data>
  <data name="Dialog_Delete_Event" xml:space="preserve">
    <value>Delete Event</value>
  </data>
  <data name="EventsWindow_Enable_When_Playing" xml:space="preserve">
    <value>Enable Event Browser When Playing.</value>
  </data>
  <data name="Label_Disconnected_FSM_Instance" xml:space="preserve">
    <value>Disconnected FSM Instance</value>
  </data>
  <data name="Label_None_Action" xml:space="preserve">
    <value>None (Action)</value>
  </data>
  <data name="Menu_Show_Action_Parameters" xml:space="preserve">
    <value>Show Action Parameters</value>
  </data>
  <data name="FsmErrorChecker_InvalidEventError" xml:space="preserve">
    <value>Event not used by this state or any global transition!</value>
  </data>
  <data name="Error_Could_not_find_property_" xml:space="preserve">
    <value>Could not find property: </value>
  </data>
  <data name="Dialog_Delete_Event_Are_you_sure" xml:space="preserve">
    <value>Are you sure?{0}</value>
  </data>
  <data name="Label_Global_Events" xml:space="preserve">
    <value>Global Events</value>
  </data>
  <data name="ActionSelector_Add_Action_To_State" xml:space="preserve">
    <value>Add Action To State</value>
  </data>
  <data name="Command_Adjust_iTween_Path" xml:space="preserve">
    <value>Adjust iTween Path</value>
  </data>
  <data name="FsmEditorSettings_Disable_PlayMaker_Editor_When_Game_Is_Playing" xml:space="preserve">
    <value>Disable PlayMaker Editor When Game Is Playing</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Obsolete_Actions_Tooltip" xml:space="preserve">
    <value>Find all actions that have been marked obsolete.</value>
  </data>
  <data name="FsmErrorChecker_StateHasMissingActionError" xml:space="preserve">
    <value>State has a Missing Action.</value>
  </data>
  <data name="Tooltip_Reset_On_Disable" xml:space="preserve">
    <value>Should the FSM reset or keep its current state on Enable/Disable. Uncheck this if you want to pause an FSM by enabling/disabling the PlayMakerFSM component.</value>
  </data>
  <data name="ActionEditor_Parameter" xml:space="preserve">
    <value>Parameter</value>
  </data>
  <data name="CustomActionWizard_Error_InvalidPath" xml:space="preserve">
    <value>Invalid path: {0}</value>
  </data>
  <data name="Action_Sequence" xml:space="preserve">
    <value>Action Sequence</value>
  </data>
  <data name="Command_Set_Transition_Color" xml:space="preserve">
    <value>Set Transition Color</value>
  </data>
  <data name="Menu_GraphView_Copy_FSM" xml:space="preserve">
    <value>Copy FSM</value>
  </data>
  <data name="FilterMenu_Recently_Selected" xml:space="preserve">
    <value>Recently Selected</value>
  </data>
  <data name="Command_Set_Transition_Event" xml:space="preserve">
    <value>Set Transition Event</value>
  </data>
  <data name="EditFsmArray_Unknown_FsmArray_Type" xml:space="preserve">
    <value>Unknown FsmArray Type: Use ArrayEditorAttribute to specify a VariableType.</value>
  </data>
  <data name="EventsWindow_Disabled_When_Playing" xml:space="preserve">
    <value>Disabled when playing. See Event Browser Settings Menu.</value>
  </data>
  <data name="Menu_GraphView_Add_FSM_Component_Use_Template" xml:space="preserve">
    <value>Add FSM Component/Use Template/{0}/{1}</value>
  </data>
  <data name="Label_Quaternion" xml:space="preserve">
    <value>Quaternion</value>
  </data>
  <data name="Command_DeleteTemplate" xml:space="preserve">
    <value>Delete Template?
NOTE: You cannot undo this action!</value>
  </data>
  <data name="Command_Open_Screenshot" xml:space="preserve">
    <value>Open Screenshot</value>
  </data>
  <data name="Menu_FsmLog_Show_Sent_By" xml:space="preserve">
    <value>Show Sent By</value>
  </data>
  <data name="Label_Property" xml:space="preserve">
    <value>Property</value>
  </data>
  <data name="ActionEditor_EditFsmEventTarget_FSM_Component_Tooltip" xml:space="preserve">
    <value>Send event directly to a PlayMakerFSM component.</value>
  </data>
  <data name="Tooltip_Browse_Templates" xml:space="preserve">
    <value>Select a Template.
Templates let you share the same FSM across GameObjects.</value>
  </data>
  <data name="TemplateSelector_Title" xml:space="preserve">
    <value>Templates</value>
  </data>
  <data name="Command_Event_Browser" xml:space="preserve">
    <value>Event Browser</value>
  </data>
  <data name="Hint_GraphView_Shortcuts_OSX" xml:space="preserve">
    <value>F1 Show/Hide
Cmd Click Canvas
Cmd Click State
Cmd Drag Transition
Cmd Shift Click
Cmd Drag States
Shift Drag States
Home
Alt Click Transition
Cmd Left/Right
Cmd Down
Cmd Up</value>
  </data>
  <data name="FsmEditorSettings_Disable_Tool_Windows_When_Game_Is_Playing_Tooltip" xml:space="preserve">
    <value>If you find performance too slow with the editor running, you can try disabling the tool windows. Note, some tool windows can be enabled/disabled individually in their Settings Menu.</value>
  </data>
  <data name="DebugToolbar_Button_Open_Log" xml:space="preserve">
    <value>Open Log</value>
  </data>
  <data name="Dialog_Template_Missing_Start_State" xml:space="preserve">
    <value>Template didn't define a start state...
Setting Start State to first state.</value>
  </data>
  <data name="Tooltip_UI_Click" xml:space="preserve">
    <value>Sent when a Button is pressed. GameObject needs a Button component.</value>
  </data>
  <data name="Hint_Events_Window" xml:space="preserve">
    <value>This window shows Events used in your project.
Right-click an Event to select FSMs that use the Event.
Use the Global checkbox to send events between FSMs.</value>
  </data>
  <data name="Hint_Context_Tools" xml:space="preserve">
    <value>Context sensitive editing tools that depend on the current selection. Some users prefer this to the right-click context menu way of editing.</value>
  </data>
  <data name="Title_NewEvent" xml:space="preserve">
    <value>New Event</value>
  </data>
  <data name="Dialog_Error_Variable_with_same_name_already_exists" xml:space="preserve">
    <value>Variable with the same name already exists!</value>
  </data>
  <data name="Menu_Select_All_Actions" xml:space="preserve">
    <value>Select All Actions</value>
  </data>
  <data name="CustomActionWizard_Title" xml:space="preserve">
    <value>PlayMaker</value>
  </data>
  <data name="Menu_Add_To_Favorites" xml:space="preserve">
    <value>Add To Favorites</value>
  </data>
  <data name="Menu_Global_Variables" xml:space="preserve">
    <value>Global Variables</value>
  </data>
  <data name="ActionEditor_Store_GameObject" xml:space="preserve">
    <value>Store GameObject</value>
  </data>
  <data name="ActionEditor_Edit_Template" xml:space="preserve">
    <value>Edit Template</value>
  </data>
  <data name="BugReportWindow_Progress" xml:space="preserve">
    <value>Submitting bug report...</value>
  </data>
  <data name="ActionEditor_Event_Target" xml:space="preserve">
    <value>Event Target</value>
  </data>
  <data name="Menu_Select_Prefab" xml:space="preserve">
    <value>Select Prefab</value>
  </data>
  <data name="FsmEditorSettings_Show_Editing_While_Running_Warning_Tooltip" xml:space="preserve">
    <value>Shows a non-modal warning the first time you edit an FSM after running the game. A gentle reminder!</value>
  </data>
  <data name="Label_Control_Mouse_Cursor" xml:space="preserve">
    <value>Control Mouse Cursor</value>
  </data>
  <data name="Label_Draw_Active_State_Labels" xml:space="preserve">
    <value>Draw Active State Labels</value>
  </data>
  <data name="Dialog_Loading_FSM_Prefabs" xml:space="preserve">
    <value>Loading Prefabs With PlayMakerFSMs...</value>
  </data>
  <data name="Error_Failed_to_load_Custom_Action_Editor" xml:space="preserve">
    <value>Failed to load Custom Action Editor inspected type.</value>
  </data>
  <data name="InspectorPanel_FSM_Uses_Template" xml:space="preserve">
    <value>This FSM uses a Template.
Click in the Graph View to select and edit the Template.
NOTE: This Template might be shared by other FSMs!</value>
  </data>
  <data name="Menu_Select_Script" xml:space="preserve">
    <value>Select Script</value>
  </data>
  <data name="FsmEditorSettings_Animate_UI" xml:space="preserve">
    <value>UI Animation FX</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Transitions_Missing_Targets" xml:space="preserve">
    <value>Check for Transitions Missing Targets</value>
  </data>
  <data name="Menu_Select_GameObject" xml:space="preserve">
    <value>Select GameObject</value>
  </data>
  <data name="Command_Clear_Breakpoints" xml:space="preserve">
    <value>Clear Breakpoints</value>
  </data>
  <data name="Event_Tooltip_Warning" xml:space="preserve">
    <value>NOTE: You cannot rename globals since they might be used in other scenes. For now you have to make a new event and reference that. We will add Rename in a future update.</value>
  </data>
  <data name="Log_No_Transition_To_State" xml:space="preserve">
    <value>No Transition to State: {0}</value>
  </data>
  <data name="FsmEditorSettings_Snap_To_Grid" xml:space="preserve">
    <value>Snap To Grid</value>
  </data>
  <data name="Menu_Add_to_Top_of_Action_List" xml:space="preserve">
    <value>Add to Top of Action List</value>
  </data>
  <data name="Tooltip_Collision_Enter_2D" xml:space="preserve">
    <value>Sent when the GameObject first collides with a 2D Collider.</value>
  </data>
  <data name="FsmEditorSettings_Show_Send_Events_Icons_Tooltip" xml:space="preserve">
    <value>Show events icon on states that send events. Click on the icon for more info.</value>
  </data>
  <data name="Command_Set_Selected_States_Color" xml:space="preserve">
    <value>Set Selected States Color</value>
  </data>
  <data name="Menu_Disabled_Add_FSM_to_selected_object" xml:space="preserve">
    <value>Add FSM to selected object...</value>
  </data>
  <data name="Label_Global_postfix" xml:space="preserve">
    <value> (global)</value>
  </data>
  <data name="Command_Add_Variable" xml:space="preserve">
    <value>Add Variable</value>
  </data>
  <data name="FsmEditorSettings_Show_Selected_GameObject_Label" xml:space="preserve">
    <value>Show Selected GameObject Label</value>
  </data>
  <data name="FsmErrorChecker_TransitionMissingEventError" xml:space="preserve">
    <value>Transition missing event!</value>
  </data>
  <data name="Error_Invalid_path__" xml:space="preserve">
    <value>Invalid path: {0}</value>
  </data>
  <data name="Command_Import_Globals" xml:space="preserve">
    <value>Import Globals</value>
  </data>
  <data name="FsmEditorSettings_Enable_Logging" xml:space="preserve">
    <value>Enable Logging</value>
  </data>
  <data name="ActionReportWindow_Description" xml:space="preserve">
    <value>This log helps you track down potential problems caused by Actions changing since the project was saved. Click the FSM/State/Action to select it in the main editor window and quickly jump to the change.</value>
  </data>
  <data name="Hint_Watermarks" xml:space="preserve">
    <value>Watermarks can help you quickly identify FSMs.
Put custom watermark images in: PlayMaker/Editor/Watermarks.
Use the existing watermarks as a guide.
NOTE: Watermarks can be turned off in Preferences.</value>
  </data>
  <data name="Error_Unrecognized_variable_type__" xml:space="preserve">
    <value>Unrecognized variable type: {0}</value>
  </data>
  <data name="Menu_Copy_Variables" xml:space="preserve">
    <value>Copy Variables</value>
  </data>
  <data name="Tooltip_Browse_FSMs_on_GameObject" xml:space="preserve">
    <value>Browse FSMs on GameObject</value>
  </data>
  <data name="Label_Enabled" xml:space="preserve">
    <value>Enabled</value>
  </data>
  <data name="FsmEditorSettings_Show_Experimental_Settings_in_FSM_Inspector" xml:space="preserve">
    <value>Show Experimental Settings in FSM Inspector</value>
  </data>
  <data name="Menu_No_Drag_and_Drop_Actions_Found_For_This_Asset_Type" xml:space="preserve">
    <value>No Drag and Drop Actions Found For This Asset Type...</value>
  </data>
  <data name="LOG_Auto_Added_Playmaker_GUI" xml:space="preserve">
    <value>Auto Added PlayMakerGUI to scene. Note, you need a PlayMakerGUI in the scene to see Playmaker Gizmos, OnGUI actions and iTween path editing. You can turn this option off in Preferences.</value>
  </data>
  <data name="MainMenu_PlayMaker_Editor" xml:space="preserve">
    <value>PlayMaker/PlayMaker Editor</value>
  </data>
  <data name="Command_Enable_Watermarks" xml:space="preserve">
    <value>Enable Watermarks</value>
  </data>
  <data name="CustomActionWizard_Custom_Category" xml:space="preserve">
    <value>Custom Category</value>
  </data>
  <data name="Menu_GraphView_Transition_Target" xml:space="preserve">
    <value>Transition Target/</value>
  </data>
  <data name="iTween_Path_Editing_Label_Begin" xml:space="preserve">
    <value>'{0}' Begin</value>
  </data>
  <data name="Label_Confirm_Editing_of_Prefab_Instance" xml:space="preserve">
    <value>Confirm Editing of Prefab Instance. See Preferences.</value>
  </data>
  <data name="WelcomeWindow_Show_at_Startup" xml:space="preserve">
    <value>Show at Startup</value>
  </data>
  <data name="Dialog_ImportGlobals_Remove_extra_PlayMakerGlobals_" xml:space="preserve">
    <value>Remove extra PlayMakerGlobals?</value>
  </data>
  <data name="Label_No_search_results_for__" xml:space="preserve">
    <value>No search results for: </value>
  </data>
  <data name="FsmSelector_Label_Url_to_docs" xml:space="preserve">
    <value>Url to docs</value>
  </data>
  <data name="Label_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Hint_Globals_Refresh" xml:space="preserve">
    <value>Use counts show where the global variables are used in any loaded FSM. NOTE: Variables might be used in other scenes! Use Refresh to manually update the use counts.</value>
  </data>
  <data name="FsmEditorSettings_Add_Prefab_Labels" xml:space="preserve">
    <value>Add Prefab Labels</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Required_Action_Fields_Tooltip" xml:space="preserve">
    <value>Check required action fields. These are field that must be specified for the action to work correctly.</value>
  </data>
  <data name="Menu_Tools_Select_Filtered_Actions_in_Project_View" xml:space="preserve">
    <value>Tools/Select Filtered Actions in Project View</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Missing_Actions_Tooltip" xml:space="preserve">
    <value>Find all Missing Actions.</value>
  </data>
  <data name="ToolWindow_Header_Global_Transitions" xml:space="preserve">
    <value>Global Transitions:</value>
  </data>
  <data name="Tooltip_Lock_Selected_FSM" xml:space="preserve">
    <value>Keep this FSM selected</value>
  </data>
  <data name="Error_Duplicate_Event_Found__" xml:space="preserve">
    <value>Duplicate Event Found: {0}</value>
  </data>
  <data name="Tooltip_EventManager_Add_Event" xml:space="preserve">
    <value>Add an Event to this FSM. Use the Browse button or Event Browser to quickly add existing events.</value>
  </data>
  <data name="AboutPlaymaker_Title" xml:space="preserve">
    <value>About</value>
  </data>
  <data name="Tooltip_Select_FSM" xml:space="preserve">
    <value>Select FSM</value>
  </data>
  <data name="WelcomeWindow_Forums_More" xml:space="preserve">
    <value>Join the PlayMaker community!
</value>
  </data>
  <data name="Tooltip_Show_State_Label" xml:space="preserve">
    <value>Show active state label in game view.
NOTE: Requires PlayMakerGUI in scene</value>
  </data>
  <data name="Menu_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Hint_Right_Click_to_Add_FSM" xml:space="preserve">
    <value>Right-Click to Add FSM</value>
  </data>
  <data name="CustomActionWizard_Full_Title" xml:space="preserve">
    <value>Custom Action Wizard</value>
  </data>
  <data name="Log_Saving_FSM_Screenshot__" xml:space="preserve">
    <value>Saving FSM Screenshot: </value>
  </data>
  <data name="CustomActionWizard_Action_Name_contains_invalid_characters" xml:space="preserve">
    <value>Action Name contains invalid characters.</value>
  </data>
  <data name="CustomActionWizard_Log_Creating_custom_action__" xml:space="preserve">
    <value>Creating custom action: </value>
  </data>
  <data name="FsmEditorSettings_Selected_language_not_yet_supported_in_menus" xml:space="preserve">
    <value>Selected language is not yet supported in Unity menus...</value>
  </data>
  <data name="FsmLog_Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="Tooltip_Edit_Variable" xml:space="preserve">
    <value>Edit the selected Variable.</value>
  </data>
  <data name="Command_Browse_Screenshots" xml:space="preserve">
    <value>Browse</value>
  </data>
  <data name="Command_Rename_State" xml:space="preserve">
    <value>Rename State</value>
  </data>
  <data name="Menu_Check_for_Setup_Errors_With_Collision_Events" xml:space="preserve">
    <value>Check for Setup Errors With Collision Events</value>
  </data>
  <data name="Menu_Find_Script" xml:space="preserve">
    <value>Find Script</value>
  </data>
  <data name="Menu_Make_Animation_States" xml:space="preserve">
    <value>Make Animation States</value>
  </data>
  <data name="Dialogs_BreakPrefabInstance_Message" xml:space="preserve">
    <value>This action will break the connection to the Prefab. 

Are you sure you want to continue?

Note: Use Apply or Revert to reconnect to the Prefab.</value>
  </data>
  <data name="VariableManager_MoveToGlobals_Confirm" xml:space="preserve">
    <value>Global variable already exists.
Use global variable and delete local variable?</value>
  </data>
  <data name="Dialog_Saved_FSM_Screenshot" xml:space="preserve">
    <value>Saved FSM Screenshot:
{0}</value>
  </data>
  <data name="Menu_GraphView_Add_FSM_Component" xml:space="preserve">
    <value>Add FSM Component</value>
  </data>
  <data name="Menu_No_Context_Actions_Found" xml:space="preserve">
    <value>No Context Actions Found!</value>
  </data>
  <data name="Command_Rename_Event" xml:space="preserve">
    <value>Rename Event</value>
  </data>
  <data name="FsmErrorChecker_ControllerCollisionEventsNeedController" xml:space="preserve">
    <value>Owner needs a Character Controller to respond to Controller Collision events!</value>
  </data>
  <data name="Label_System_Event_Tooltip" xml:space="preserve">
    <value>Built-in Unity Event</value>
  </data>
  <data name="Error_Name_is_empty" xml:space="preserve">
    <value>Name is empty!</value>
  </data>
  <data name="FsmEditorSettings_Draw_Frame_Around_FSM" xml:space="preserve">
    <value>Draw Frame Around FSM</value>
  </data>
  <data name="Menu_GraphView_Add_To_Selected" xml:space="preserve">
    <value>Add To Selected GameObject</value>
  </data>
  <data name="FsmEditorSettings_Auto_Add_PlayMakerGUI_to_Scene" xml:space="preserve">
    <value>Auto Add PlayMakerGUI to Scene</value>
  </data>
  <data name="Error_Name_already_used_in_this_FSM" xml:space="preserve">
    <value>Name already used in this FSM!</value>
  </data>
  <data name="Hint_Inspector_disabled_when_playing" xml:space="preserve">
    <value>Inspector disabled when playing. See Preferences.</value>
  </data>
  <data name="VersionInfo_Preview_Version_Notes" xml:space="preserve">
    <value>Preview Version: Editing is disabled, but you can view and run FSMs authored in other versions. This is convenient for team members who don't need a full version but need to run FSMs. Or if you want to share a project with someone who doesn't own PlayMaker.</value>
  </data>
  <data name="Label_PlayMaker_Supported_Languages" xml:space="preserve">
    <value>PlayMaker Supported Languages:</value>
  </data>
  <data name="Error_Missing_Action__Set_Property" xml:space="preserve">
    <value>PlayMaker Missing Action: Set Property</value>
  </data>
  <data name="Error_Variable_name_already_exists_and_is_of_different_type" xml:space="preserve">
    <value>Variable name already exists and is of different type: {0}</value>
  </data>
  <data name="Dialog_Export_Globals" xml:space="preserve">
    <value>Export Globals</value>
  </data>
  <data name="ActionEditor_Undo_Resize_Array" xml:space="preserve">
    <value>Resize Array</value>
  </data>
  <data name="Command_Edit_Action" xml:space="preserve">
    <value>Edit Action : {0}</value>
  </data>
  <data name="Command_Remove_FSM_Component" xml:space="preserve">
    <value>Remove FSM Component</value>
  </data>
  <data name="FsmEditorSettings_Select_GameObject_When_FSM_Selected_Tooltip" xml:space="preserve">
    <value>Should the GameObject that owns an FSM be automatically selected when selecting the FSM.</value>
  </data>
  <data name="CustomActionWizard_Action_Folder" xml:space="preserve">
    <value>Action Folder</value>
  </data>
  <data name="FsmEditorSettings_Color_Links_With_State_Color_Tooltip" xml:space="preserve">
    <value>Use the state color to color transition links from that state.</value>
  </data>
  <data name="Tooltip_Filter_State_Labels_With_Distance" xml:space="preserve">
    <value>This is useful if you only want to see nearby state labels as you move in the Game View.</value>
  </data>
  <data name="Hint_FsmSelector" xml:space="preserve">
    <value>The FSM Browser lets you quickly see all loaded FSMs and their current state. Click an FSM to select it in the main editor.
Hint: Use the filter list above, e.g., to show recent FSMs.</value>
  </data>
  <data name="Tooltip_Collision_Exit" xml:space="preserve">
    <value>Sent when the GameObject stops colliding with another object.</value>
  </data>
  <data name="Tooltip_Collision_Stay" xml:space="preserve">
    <value>Sent while the GameObject is colliding with another object.</value>
  </data>
  <data name="Label_Fix_Network_Observe_This_FSM" xml:space="preserve">
    <value>GameObject NetworkView component must Observe this PlayMakerFSM. Drag the PlayMakerFSM into the Observed field, or click this box to fix.</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>PlayMaker</value>
  </data>
  <data name="Dialog_Saved_Screenshot" xml:space="preserve">
    <value>Saved Screenshot</value>
  </data>
  <data name="FsmEditorSettings_Creating_PlayMakerPrefs_Asset" xml:space="preserve">
    <value>Creating PlayMakerPrefs asset: </value>
  </data>
  <data name="Tooltip_Global_Event_Flag" xml:space="preserve">
    <value>Global flag. Global events can be sent between FSMs.</value>
  </data>
  <data name="ActionEditor_Add_Component_MeshRenderer" xml:space="preserve">
    <value>Add Component: MeshRenderer</value>
  </data>
  <data name="Label_Description___" xml:space="preserve">
    <value>Description...</value>
  </data>
  <data name="Command_Edit_Global_Variable" xml:space="preserve">
    <value>Edit Global Variable</value>
  </data>
  <data name="Command_Delete_Unused_Variables" xml:space="preserve">
    <value>Delete Unused Variables</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Required_Components_Tooltip" xml:space="preserve">
    <value>Check for required components. Can be useful to disable if you add components at runtime.</value>
  </data>
  <data name="Tooltip_Global_Variables_Type" xml:space="preserve">
    <value>View/Edit the variable's type.
Click to sort by type.</value>
  </data>
  <data name="BugReportWindow_FrequencyChoices_Sometimes__but_not_always" xml:space="preserve">
    <value>Sometimes, but not always</value>
  </data>
  <data name="Menu_GraphView_Add_FSM_Component_Paste_FSM" xml:space="preserve">
    <value>Add FSM Component/Paste FSM</value>
  </data>
  <data name="Tooltip_Controls" xml:space="preserve">
    <value>Events and Variables exposed in the Inspector.</value>
  </data>
  <data name="Label_Added_Missing_Event" xml:space="preserve">
    <value>Added missing event: </value>
  </data>
  <data name="Menu_GraphView_Add_FSM_Component_Add_Template" xml:space="preserve">
    <value>Add FSM Component/Add Template</value>
  </data>
  <data name="Path_Resources_Folder" xml:space="preserve">
    <value>Resources</value>
  </data>
  <data name="Hint_Send_Event_to_GameObject" xml:space="preserve">
    <value>Send Event to all FSMs on a GameObject.
NOTE: Event must be marked as global in the Event Browser or Events Inspector.</value>
  </data>
  <data name="FsmEditorSettings_Maximize_File_Compatibility_Tooltip" xml:space="preserve">
    <value>Save FSMs in a format that can be loaded in previous versions of Playmaker.
Results in a larger file size.</value>
  </data>
  <data name="Menu_Enable_Real_Time_Error_Checker" xml:space="preserve">
    <value>Enable Real-Time Error Checker</value>
  </data>
  <data name="Label_Templates_Used" xml:space="preserve">
    <value>Templates Used</value>
  </data>
  <data name="Category_Tooltip" xml:space="preserve">
    <value>Use categories to organize variables into groups.</value>
  </data>
  <data name="Menu_Show_Preview" xml:space="preserve">
    <value>Show Preview</value>
  </data>
  <data name="FsmEditorSettings_Auto_Frame_Selected_State_Tooltip" xml:space="preserve">
    <value>Should the graph view auto frame selected states.</value>
  </data>
  <data name="ActionReportWindow_Effected_States_Title" xml:space="preserve">
    <value>	Effected States:</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Label_Load_All_Templates" xml:space="preserve">
    <value>Load All Templates</value>
  </data>
  <data name="Label_Edit_Prefab" xml:space="preserve">
    <value>Edit Prefab</value>
  </data>
  <data name="FsmEditorSettings_Minimap_Size" xml:space="preserve">
    <value>Minimap Size</value>
  </data>
  <data name="ActionEditor_Store_Color" xml:space="preserve">
    <value>Store Color</value>
  </data>
  <data name="Label_Reset_Variables_Tooltip" xml:space="preserve">
    <value>Reset Variable Values on Disable/Enable.</value>
  </data>
  <data name="ActionEditor_Store_Float" xml:space="preserve">
    <value>Store Float</value>
  </data>
  <data name="BugReportWindow_FrequencyChoices_This_is_the_first_time" xml:space="preserve">
    <value>This is the first time</value>
  </data>
  <data name="Hint_Exporting_Templates" xml:space="preserve">
    <value>Exporting Templates:

Export the Template asset in a unitypackage, and import into the new project. Make sure to check 'Include Dependencies.'

If the template uses Globals, please also see Exporting and Importing Globals in the PlayMakerGlobals Inspector.</value>
  </data>
  <data name="MainToolbar_Recent" xml:space="preserve">
    <value>Recent</value>
  </data>
  <data name="FsmEditorSettings_Infinite_Loop_Threshold" xml:space="preserve">
    <value>Infinite Loop Threshold</value>
  </data>
  <data name="Path_PlayMakerGlobals_Asset" xml:space="preserve">
    <value>/Resources/PlayMakerGlobals.asset</value>
  </data>
  <data name="GlobalVariablesWindow_Note_Asset_Location" xml:space="preserve">
    <value>NOTE: Globals are stored in ThirdPlugins/PlayMaker/Resources/PlayMakerGlobals.asset</value>
  </data>
  <data name="Menu_Make_Local_Variable" xml:space="preserve">
    <value>Make Local Variable</value>
  </data>
  <data name="FsmEditorSettings_Custom_Colors" xml:space="preserve">
    <value>Custom Colors</value>
  </data>
  <data name="Tooltip_Browse_variables_in_FSM" xml:space="preserve">
    <value>Browse variables in FSM</value>
  </data>
  <data name="ActionEditor_EditFsmProperty_Object_Type" xml:space="preserve">
    <value>Object Type</value>
  </data>
  <data name="FsmEditorSettings_FSM_Screenshots_Directory" xml:space="preserve">
    <value>FSM Screenshots Directory</value>
  </data>
  <data name="DebugToolbar_Label_Errors" xml:space="preserve">
    <value>errors</value>
  </data>
  <data name="Tooltip_Add_Global_Variable" xml:space="preserve">
    <value>Add a Variable to this FSM. Variables are used by Actions to store information</value>
  </data>
  <data name="Help_Experimental_Warning" xml:space="preserve">
    <value>NOTE: Experimental features could be removed or modified in future versions!</value>
  </data>
  <data name="Menu_Revert_To_Prefab" xml:space="preserve">
    <value>Revert To Prefab</value>
  </data>
  <data name="FsmErrorChecker_CollisionEventsNeedCollider" xml:space="preserve">
    <value>Owner needs a Collider/RigidBody to respond to collision/trigger events!</value>
  </data>
  <data name="Label_Rename_Variable_Category" xml:space="preserve">
    <value>Rename Variable Category</value>
  </data>
  <data name="CustomActionWizard_Custom_Error_Checker" xml:space="preserve">
    <value>Custom Error Checker</value>
  </data>
  <data name="Menu_Check_for_Required_Action_Fields" xml:space="preserve">
    <value>Check for Required Action Fields</value>
  </data>
  <data name="Tooltip_Doc_Button" xml:space="preserve">
    <value>Doc Link</value>
  </data>
  <data name="ActionEditor_Set_Event_Target" xml:space="preserve">
    <value>Set Event Target</value>
  </data>
  <data name="FsmErrorChecker_CollisionEventsNeedCollider2D" xml:space="preserve">
    <value>Owner needs a 2D Collider/RigidBody to respond to 2D collision/trigger events!</value>
  </data>
  <data name="FsmEditorSettings_Category_Prefabs" xml:space="preserve">
    <value>Prefabs</value>
  </data>
  <data name="Tooltip_Preview_GUI_Actions_While_Editing" xml:space="preserve">
    <value>This lets you preview GUI actions as you edit them. NOTE: This is an experimental feature, so you might run into some bugs!</value>
  </data>
  <data name="FsmEditorSettings_Auto_Load_Prefabs_in_Scene_Tooltip" xml:space="preserve">
    <value>Automatically load any prefabs used in a scene.</value>
  </data>
  <data name="FsmEditorSettings_Auto_Load_Prefabs_in_Scene" xml:space="preserve">
    <value>Auto Load Prefabs in Scene</value>
  </data>
  <data name="Label_Importing_Globals_" xml:space="preserve">
    <value>Importing Globals...</value>
  </data>
  <data name="BugReportWindow_Your_E_mail_Tooltip" xml:space="preserve">
    <value>So you can get updates on your bug report.</value>
  </data>
  <data name="FsmErrorChecker_TargetFsmMissingEventError" xml:space="preserve">
    <value>Event not used by target FSM!</value>
  </data>
  <data name="Dialog_Delete_Multiple_States" xml:space="preserve">
    <value>Delete Multiple States</value>
  </data>
  <data name="ActionEditor_Undo_Resize_Compound_Array" xml:space="preserve">
    <value>Resize Compound Array</value>
  </data>
  <data name="Label_Fix_Missing_Network_Component" xml:space="preserve">
    <value>GameObject requires a NetworkView component.
Click to fix.</value>
  </data>
  <data name="Command_Delete_States" xml:space="preserve">
    <value>Delete States</value>
  </data>
  <data name="Label_Create_Variable" xml:space="preserve">
    <value>Create Variable</value>
  </data>
  <data name="CustomActionWizard_Group_Name_and_Description" xml:space="preserve">
    <value>Name and Description</value>
  </data>
  <data name="FsmErrorChecker_RequiredFieldError" xml:space="preserve">
    <value>Required field...</value>
  </data>
  <data name="FsmEditorSettings_Disable_Error_Checker_When_Game_Is_Playing_Tooltip" xml:space="preserve">
    <value>Disable the real time error checking to improve playback performance in the editor.</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Events_Not_Used_by_Target_FSM" xml:space="preserve">
    <value>Check for Events Not Used by Target FSM</value>
  </data>
  <data name="Hint_Debugger_Settings" xml:space="preserve">
    <value>These settings control the behaviour of the runtime debugger.</value>
  </data>
  <data name="Tooltip_Trigger_Enter" xml:space="preserve">
    <value>Sent when the GameObject enters a trigger volume.</value>
  </data>
  <data name="WelcomeWindow_Samples_More" xml:space="preserve">
    <value>Download sample scenes to play with working examples.</value>
  </data>
  <data name="Hint_Template_Selector" xml:space="preserve">
    <value>The Template Browser shows all saved Templates in your project. Save an FSM as a Template by right-clicking on the canvas in the Graph View.</value>
  </data>
  <data name="ToolWindow_Header_State_Tools" xml:space="preserve">
    <value>State Tools:</value>
  </data>
  <data name="FSM" xml:space="preserve">
    <value>FSM</value>
  </data>
  <data name="Tab" xml:space="preserve">
    <value>	</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Tooltip_Choose_Watermark" xml:space="preserve">
    <value>Choose a watermark image...</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>Event</value>
  </data>
  <data name="Command_Add_FSM_Component" xml:space="preserve">
    <value>Add FSM Component</value>
  </data>
  <data name="Menu_Select_a_State_to_add_Actions" xml:space="preserve">
    <value>Select a State to add Actions...</value>
  </data>
  <data name="Label" xml:space="preserve">
    <value>Label</value>
  </data>
  <data name="ActionSelector_Add_Action_To_State_Tooltip" xml:space="preserve">
    <value>Add the selected Action to the selected State. NOTE: You can also drag and drop the actions.</value>
  </data>
  <data name="ActionEditor_Click_to_Add_Required_Component" xml:space="preserve">
    <value>[Click to Add Required Component]</value>
  </data>
  <data name="ActionEditor_Error_Unsupported_Type_XXX" xml:space="preserve">
    <value>Unsupported Type: {0}</value>
  </data>
  <data name="Menu_GraphView_GlobalEvents" xml:space="preserve">
    <value>Global Events/</value>
  </data>
  <data name="FsmEditorSettings_Max_State_Width" xml:space="preserve">
    <value>Max State Width</value>
  </data>
  <data name="CustomActionWizard_File_Path_Prefix" xml:space="preserve">
    <value>File: Assets/</value>
  </data>
  <data name="Menu_Remove_From_Favorrites" xml:space="preserve">
    <value>Remove From Favorites</value>
  </data>
  <data name="Menu_Check_for_Transitions_Missing_Targets" xml:space="preserve">
    <value>Check for Transitions Missing Targets</value>
  </data>
  <data name="Menu_Remove_Action" xml:space="preserve">
    <value>Remove Action</value>
  </data>
  <data name="Menu_Dim_Unused_Parameters" xml:space="preserve">
    <value>Dim Unused Parameters</value>
  </data>
  <data name="DebugToolbar_No_errors" xml:space="preserve">
    <value>No errors</value>
  </data>
  <data name="DebugToolbar_Label_Debug" xml:space="preserve">
    <value>Debug</value>
  </data>
  <data name="Label_Prefab_Instance" xml:space="preserve">
    <value>Prefab Instance</value>
  </data>
  <data name="DebugToolbar_Label_Error" xml:space="preserve">
    <value>error</value>
  </data>
  <data name="Error_You_cannot_reference_Scene_Objects_in_Project_Assets" xml:space="preserve">
    <value>You cannot reference Scene Objects in Project Assets.</value>
  </data>
  <data name="Menu_FSM_Editor_Log" xml:space="preserve">
    <value>Editor Log</value>
  </data>
  <data name="Label_Add_Event" xml:space="preserve">
    <value>Add Event</value>
  </data>
  <data name="FsmEditorSettings_Show_Editing_While_Running_Warning" xml:space="preserve">
    <value>Show Editing While Running Warning</value>
  </data>
  <data name="EventsWindow_Title" xml:space="preserve">
    <value>Events</value>
  </data>
  <data name="FsmEditorSettings_Fade_Links_Not_Connected_to_Selected_States" xml:space="preserve">
    <value>Fade Links Not Connected to Selected States</value>
  </data>
  <data name="Error_Unknown_variable_type" xml:space="preserve">
    <value>Unknown variable type!</value>
  </data>
  <data name="ActionReportWindow_Title" xml:space="preserve">
    <value>Editor Log</value>
  </data>
  <data name="Tooltip_UI_Int_Value_Changed" xml:space="preserve">
    <value>Sent when a Dropdown selection changes. GameObject needs a Dropdown component. Use Get Event Int Data to get the value.</value>
  </data>
  <data name="Command_Rename" xml:space="preserve">
    <value>Rename</value>
  </data>
  <data name="Label_Theme" xml:space="preserve">
    <value>Theme</value>
  </data>
  <data name="Label_Filter_State_Labels_With_Distance" xml:space="preserve">
    <value>Filter State Labels With Distance</value>
  </data>
  <data name="Label_Show_State_Label" xml:space="preserve">
    <value>Show State Label</value>
  </data>
  <data name="EventMenu_Custom_Events" xml:space="preserve">
    <value>Custom Events</value>
  </data>
  <data name="Hint_Action_Shortcuts_OSX" xml:space="preserve">
    <value>Double click action title to edit action Name.
Cmd-click foldout to expand/collapse all actions.
Cmd-click enable checkbox to enable/disable all actions.</value>
  </data>
  <data name="BugReportWindow_Your_E_mail" xml:space="preserve">
    <value>Your E-mail</value>
  </data>
  <data name="Label_Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="Tootlip_Action_Renamed" xml:space="preserve">
    <value>Title: {0}
Action: {1}
{2}</value>
  </data>
  <data name="Menu_GraphView_Set_Color" xml:space="preserve">
    <value>Set Color/</value>
  </data>
  <data name="Label_State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="Menu_Paste_Actions_Before" xml:space="preserve">
    <value>Paste Actions Before</value>
  </data>
  <data name="Label_Input" xml:space="preserve">
    <value>Input</value>
  </data>
  <data name="Label_NOTES" xml:space="preserve">
    <value>NOTES</value>
  </data>
  <data name="Menu_Select_Globals_Asset" xml:space="preserve">
    <value>Select Globals Asset</value>
  </data>
  <data name="Error_Missing_PlayMaker_dll" xml:space="preserve">
    <value>Couldn't find PlayMaker.dll...</value>
  </data>
  <data name="Label_Event" xml:space="preserve">
    <value>Event</value>
  </data>
  <data name="Label_Red" xml:space="preserve">
    <value>Red</value>
  </data>
  <data name="Label_Int" xml:space="preserve">
    <value>Int</value>
  </data>
  <data name="Label_Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Label_Debug" xml:space="preserve">
    <value>Debug</value>
  </data>
  <data name="Label_Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="Label_Green" xml:space="preserve">
    <value>Green</value>
  </data>
  <data name="Tooltip_Browse_Animator_Parameters" xml:space="preserve">
    <value>Browse {0} Parameters on GameObject</value>
  </data>
  <data name="Label_Float" xml:space="preserve">
    <value>Float</value>
  </data>
  <data name="Label_Color" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="Tooltip_Control_Mouse_Cursor" xml:space="preserve">
    <value>Disable this if you have scripts that need to control the mouse cursor.</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Network_Setup_Errors_Tooltip" xml:space="preserve">
    <value>Disable this if using another network library like Photon.</value>
  </data>
  <data name="FsmBuilder_Postfix_Copy" xml:space="preserve">
    <value> - Copy</value>
  </data>
  <data name="ProductCopyright" xml:space="preserve">
    <value>© Hutong Games LLC. All Rights Reserved.</value>
  </data>
  <data name="Label_Max_Loop_Override" xml:space="preserve">
    <value>Max Loop Override</value>
  </data>
  <data name="FilterMenu_All_FSMs" xml:space="preserve">
    <value>All FSMs</value>
  </data>
  <data name="CustomActionWizard_Group_Category" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="Label_Enable_GUILayout" xml:space="preserve">
    <value>Enable GUILayout</value>
  </data>
  <data name="Menu_GraphView_Link_Style_Default" xml:space="preserve">
    <value>Link Style/Default</value>
  </data>
  <data name="Label_Tween_From_Color" xml:space="preserve">
    <value>Tween From Color</value>
  </data>
  <data name="Label_Inspector" xml:space="preserve">
    <value>Inspector</value>
  </data>
  <data name="BugReportWindow_How_can_we_reproduce_it" xml:space="preserve">
    <value>2) How can we reproduce it</value>
  </data>
  <data name="PasteVariables_Warning_Some_variables_already_exist__overwrite_values" xml:space="preserve">
    <value>Some variables already exist, overwrite values?</value>
  </data>
  <data name="Menu_Find_Action" xml:space="preserve">
    <value>Find...</value>
  </data>
  <data name="Fixed_Graph_View_Bounds" xml:space="preserve">
    <value>PlayMaker: Fixed Graph View bounds.</value>
  </data>
  <data name="ActionEditor_Required_Field_Warning" xml:space="preserve">
    <value>*</value>
  </data>
  <data name="FsmErrorChecker_MouseEventsNeedCollider" xml:space="preserve">
    <value>Owner needs a Collider or GUI Element to respond to mouse events!</value>
  </data>
  <data name="FsmTimeline_Title" xml:space="preserve">
    <value>FSM Timeline</value>
  </data>
  <data name="Hint_State_Inspector_Workflow" xml:space="preserve">
    <value>Hit Tab after selecting a State to quickly edit the Name.
Use the Settings Menu next to the name, or right click below for more options.</value>
  </data>
  <data name="Menu_Make_Instance" xml:space="preserve">
    <value>Make Instance</value>
  </data>
  <data name="Tooltip_Global_Variables_Used_Count" xml:space="preserve">
    <value>The number of FSMs that use the global variable.</value>
  </data>
  <data name="Command_Reset_Action_Name" xml:space="preserve">
    <value>Reset Action Name</value>
  </data>
  <data name="Menu_GraphView_Link_Style_Circuit" xml:space="preserve">
    <value>Link Style/Circuit</value>
  </data>
  <data name="Label_Use_Template" xml:space="preserve">
    <value>Use Template</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Prefab_Restrictions" xml:space="preserve">
    <value>Check for Prefab Restrictions</value>
  </data>
  <data name="ActionUtility_Add_Animation_Clip_to_GameObject" xml:space="preserve">
    <value>Add Animation Clip to GameObject?</value>
  </data>
  <data name="Label_Create_Event" xml:space="preserve">
    <value>Create Event</value>
  </data>
  <data name="ErrorSelector_Setup_Errors" xml:space="preserve">
    <value>Setup Errors: {0}</value>
  </data>
  <data name="Tooltip_Enable_Breakpoints" xml:space="preserve">
    <value>Enable/disable breakpoints in this FSM. NOTE: Use Preferences &gt; Breakpoints Enabled to enable/disable globally.</value>
  </data>
  <data name="Command_Add_Global_Variable" xml:space="preserve">
    <value>Add Global Variable</value>
  </data>
  <data name="Tooltip_Camera_Distance" xml:space="preserve">
    <value>Distance is measured from the main camera</value>
  </data>
  <data name="Tooltip_Show_State_Labels_in_Standalone_Builds" xml:space="preserve">
    <value>Show State Labels in Standalone Builds</value>
  </data>
  <data name="Tooltip_Joint_Break_2D" xml:space="preserve">
    <value>Sent when a Joint2D attached to the same GameObject breaks.</value>
  </data>
  <data name="FsmEditorSettings_Disable_Tool_Windows_When_Game_Is_Playing" xml:space="preserve">
    <value>Disable Tool Windows When Game Is Playing</value>
  </data>
  <data name="WelcomeWindow_Docs_More" xml:space="preserve">
    <value>Browse the comprehensive online docs.
</value>
  </data>
  <data name="Hint_Required_Fields" xml:space="preserve">
    <value>* Required Fields</value>
  </data>
  <data name="Tooltip_Edit_Variable_Type" xml:space="preserve">
    <value>Select the type of information stored. Rollover Action parameters to see the type of each parameter.</value>
  </data>
  <data name="Log_Loading_all_FSMs" xml:space="preserve">
    <value>Loading all GameObjects with a PlayMakerFSM component...</value>
  </data>
  <data name="FsmEditorSettings_Draw_Links_Behind_States" xml:space="preserve">
    <value>Draw Links Behind States</value>
  </data>
  <data name="FsmEditorSettings_Zoom_Speed" xml:space="preserve">
    <value>Zoom Speed</value>
  </data>
  <data name="FsmEditorSettings_Zoom_Range" xml:space="preserve">
    <value>Zoom Range</value>
  </data>
  <data name="Menu_NewVariable" xml:space="preserve">
    <value>New Variable...</value>
  </data>
  <data name="FsmEditorSettings_Custom_Colors_Name_Hint" xml:space="preserve">
    <value>Name a custom color to display it in menus.</value>
  </data>
  <data name="FsmEditorSettings_Show_FSM_Password_Controls_Tooltip" xml:space="preserve">
    <value>Allows you to lock FSMs using a password. 
WARNING: There is no way to recover a forgotten password!</value>
  </data>
  <data name="FsmEditorSettings_Enable_Real_Time_Error_Checker" xml:space="preserve">
    <value>Enable Real-Time Error Checker</value>
  </data>
  <data name="Menu_Hide_Obsolete_Actions" xml:space="preserve">
    <value>Hide Obsolete Actions</value>
  </data>
  <data name="Dialog_Delete_Unused_Events_Are_you_sure" xml:space="preserve">
    <value>Are you sure you want to delete {0} unused events?</value>
  </data>
  <data name="ActionEditor_Click_to_Add_Transition_to_State" xml:space="preserve">
    <value>[Click to Add Transition to State]</value>
  </data>
  <data name="Error_Bad_screenshot_texture" xml:space="preserve">
    <value>Bad screenshot texture!</value>
  </data>
  <data name="Label_Tool_Windows_disabled_when_playing" xml:space="preserve">
    <value>Tool Windows disabled when playing. See Preferences.</value>
  </data>
  <data name="FsmEditorSettings_DoDebuggingSettings_Infinite_Loop_Threshold_Tooltip" xml:space="preserve">
    <value>The maximum number of times a state is allowed to immediately re-enter itself before being considered an infinite loop that should be broken out of.</value>
  </data>
  <data name="Warning_Some_actions_have_changed_since_FSMs_were_saved" xml:space="preserve">
    <value>Some actions have changed since FSMs were saved! All parameters have been updated successfully. Check the PlayMaker Console for details. NOTE: You must resave these FSMs before making a build!</value>
  </data>
  <data name="Label_Prefab_Instance_Modified" xml:space="preserve">
    <value>Prefab Instance (modified)</value>
  </data>
  <data name="Menu_Use_Template" xml:space="preserve">
    <value>Use Template/{0}/{1}</value>
  </data>
  <data name="FsmEditorSettings_Draw_Playmaker_Gizmos_in_Hierarchy" xml:space="preserve">
    <value>Draw Playmaker Gizmos in Hierarchy</value>
  </data>
  <data name="Label_Network_Sync" xml:space="preserve">
    <value>Network Sync</value>
  </data>
  <data name="ActionEditor_EditFsmProperty_Target_Object" xml:space="preserve">
    <value>Target Object</value>
  </data>
  <data name="Menu_Show_State_Labels_in_Game_View" xml:space="preserve">
    <value>Show State Labels in Game View</value>
  </data>
  <data name="Dialog_Delete_Unused_Global_Variables_Are_you_sure" xml:space="preserve">
    <value>Are you sure you want to delete {0} unused global variables?</value>
  </data>
  <data name="Tooltip_Trigger_Enter_2D" xml:space="preserve">
    <value>Sent when the GameObject enters a 2D Trigger.</value>
  </data>
  <data name="Command_Finished" xml:space="preserve">
    <value>Finished</value>
  </data>
  <data name="ActionEditor_Label_Input" xml:space="preserve">
    <value>Input</value>
  </data>
  <data name="Hint_Error_Checker_Settings" xml:space="preserve">
    <value>These settings controls the behaviour of the edit time Error Checker. The Error Checker looks for common setup errors. Click the error count in the bottom left of the Graph View to open the Error Check window.</value>
  </data>
  <data name="Menu_FsmLog_Show_State_Exit" xml:space="preserve">
    <value>Show State Exit</value>
  </data>
  <data name="Tooltip_Action" xml:space="preserve">
    <value>Action: {0}
{1}</value>
  </data>
  <data name="Command_Global_Variables" xml:space="preserve">
    <value>Global Variables</value>
  </data>
  <data name="Label_Object_Type" xml:space="preserve">
    <value>Object Type</value>
  </data>
  <data name="Label_Set_Value" xml:space="preserve">
    <value>Set Value</value>
  </data>
  <data name="Tooltip_Application_Quit" xml:space="preserve">
    <value>Sent right before the Application quits.</value>
  </data>
  <data name="Label_GUITexture_State_Labels" xml:space="preserve">
    <value>GUITexture State Labels</value>
  </data>
  <data name="Menu_Paste_Actions" xml:space="preserve">
    <value>Paste Actions</value>
  </data>
  <data name="FsmEditorSettings_Select_Game_Objects_With_FSMs_in_Game_View" xml:space="preserve">
    <value>Select Game Objects With FSMs in Game View</value>
  </data>
  <data name="Menu_Add_FSM" xml:space="preserve">
    <value>Add FSM</value>
  </data>
  <data name="ActionReportWindow_No_warnings_or_errors___" xml:space="preserve">
    <value>No warnings or errors...</value>
  </data>
  <data name="Menu_Prefab_Actions" xml:space="preserve">
    <value>Prefab Actions</value>
  </data>
  <data name="Menu_Edit_Name" xml:space="preserve">
    <value>Edit Name</value>
  </data>
  <data name="FsmEditorSettings_Mouse_Wheel_Scrolls_Graph_View" xml:space="preserve">
    <value>Mouse Wheel Scrolls Graph View</value>
  </data>
  <data name="Tooltip_Collision_Exit_2D" xml:space="preserve">
    <value>Sent when the GameObject stops colliding with a 2D Collider.</value>
  </data>
  <data name="EditPrefab_Appy_or_Revert" xml:space="preserve">
    <value>Use Apply or Revert to re-connect this FSM to the Prefab.</value>
  </data>
  <data name="Tooltip_Event_Browser_Button_in_Events_Tab" xml:space="preserve">
    <value>Use the Event Browser to manage Events across your Project.</value>
  </data>
  <data name="Menu_Find_Action_In_Browser" xml:space="preserve">
    <value>Find Action In Browser</value>
  </data>
  <data name="Menu_Reconnect_to_Prefab" xml:space="preserve">
    <value>Reconnect to Prefab</value>
  </data>
  <data name="FsmErrorChecker_RequiresComponentError" xml:space="preserve">
    <value>GameObject requires </value>
  </data>
  <data name="ErrorSelector_Title" xml:space="preserve">
    <value>Error Check</value>
  </data>
  <data name="ActionEditor_Store_Object" xml:space="preserve">
    <value>Store Object</value>
  </data>
  <data name="Menu_Auto_Refresh_Action_Usage" xml:space="preserve">
    <value>Auto Refresh Action Usage</value>
  </data>
  <data name="Menu_Paste_Value" xml:space="preserve">
    <value>Paste Value</value>
  </data>
  <data name="Hint_Select_a_Game_Object___" xml:space="preserve">
    <value>Select a Game Object...</value>
  </data>
  <data name="FsmEditorSettings_Show_Scrollbars_All_The_Time" xml:space="preserve">
    <value>Show Scrollbars All The Time</value>
  </data>
  <data name="FsmEditorSettings_Draw_Playmaker_Gizmos_in_Hierarchy_Tooltip" xml:space="preserve">
    <value>Draw the Playmaker Gizmo on game objects with FSMs in the hierarchy view.</value>
  </data>
  <data name="Command_Delete_Action" xml:space="preserve">
    <value>Delete Action</value>
  </data>
  <data name="CustomActionWizard_Code_Preview" xml:space="preserve">
    <value>Code Preview</value>
  </data>
  <data name="Label_Set_Variable_Category" xml:space="preserve">
    <value>Set Variable Category</value>
  </data>
  <data name="AboutPlaymaker_Version_Info" xml:space="preserve">
    <value>Version {0}</value>
  </data>
  <data name="Label_Fsm_Info_Tooltip" xml:space="preserve">
    <value>Useful info about the FSM.</value>
  </data>
  <data name="Menu_Transition_Event" xml:space="preserve">
    <value>Transition Event</value>
  </data>
  <data name="ActionSelector_Disabled_when_playing" xml:space="preserve">
    <value>Disabled when playing. See Action Browser Settings Menu.</value>
  </data>
  <data name="Tooltip_Inspector" xml:space="preserve">
    <value>Show this variable in the PlayMakerFSM Inspector</value>
  </data>
  <data name="Menu_NewEvent" xml:space="preserve">
    <value>New Event...</value>
  </data>
  <data name="VersionInfo_PREVIEW_VERSION" xml:space="preserve">
    <value>PREVIEW VERSION</value>
  </data>
  <data name="Error_Failed_to_load_texture__" xml:space="preserve">
    <value>Failed to load texture: </value>
  </data>
  <data name="ErrorSelector_Runtime_Errors" xml:space="preserve">
    <value>Runtime Errors: {0}</value>
  </data>
  <data name="FsmEditorSettings_Enable_Real_Time_Error_Checker_Tooltip" xml:space="preserve">
    <value>If larger projects start to slow down in the editor you can disable the real time error checking and use the manual Refresh button in the Error Check window instead.</value>
  </data>
  <data name="Command_Delete_Variables_Are_you_sure" xml:space="preserve">
    <value>Are you sure you want to delete {0} unused variables?</value>
  </data>
  <data name="Label_Updating_Usages" xml:space="preserve">
    <value>Updating usages...</value>
  </data>
  <data name="Command_Add_Global_Transition" xml:space="preserve">
    <value>Add Global Transition</value>
  </data>
  <data name="Label_Reset_Variables" xml:space="preserve">
    <value>Reset Variables</value>
  </data>
  <data name="Menu_Move_Transition_Up" xml:space="preserve">
    <value>Move Transition Up</value>
  </data>
  <data name="Label_Events_Count" xml:space="preserve">
    <value>Events [{0}]</value>
  </data>
  <data name="Command_Change_Global_Variable_Type" xml:space="preserve">
    <value>Change Global Variable Type</value>
  </data>
  <data name="Error_Invalid_Name" xml:space="preserve">
    <value>Invalid Name!</value>
  </data>
  <data name="Tooltip_Mouse_Up_As_Button" xml:space="preserve">
    <value>Sent when the mouse button is released over the same GUI Element or Collider it was pressed on.</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Transitions_Missing_Targets_Tooltip" xml:space="preserve">
    <value>Check for transitions that are missing target states.</value>
  </data>
  <data name="Tooltip_GUITexture_State_Labels" xml:space="preserve">
    <value>Draw active state labels on GUITextures.</value>
  </data>
  <data name="Dialog_Delete_Unused_Events" xml:space="preserve">
    <value>Delete Unused Events</value>
  </data>
  <data name="Command_Add_Action" xml:space="preserve">
    <value>Add Action</value>
  </data>
  <data name="Label_Tween_From_Color_Tooltip" xml:space="preserve">
    <value>Color used to preview from targets in scene view.</value>
  </data>
  <data name="CustomActionWizard_Label_Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="AboutPlaymaker_Special_Thanks_People" xml:space="preserve">
    <value>Erin Ko, Kemal Amarasingham, Bruce Blumberg, Steve Gargolinski, Lee Hepler, Bart Simon, Lucas Meijer, Joachim Ante, Jaydee Alley, James Murchison, XiaoHang Zheng, Andrzej Łukasik, Vanessa Wesley, Marek Ledvina, Bob Berkebile, Jean Fabre, MaDDoX, and the PlayMaker community!
</value>
  </data>
  <data name="Menu_Show_FSMs_in_Prefabs" xml:space="preserve">
    <value>Show FSMs in Prefabs</value>
  </data>
  <data name="Menu_Delete_Unused_Variables" xml:space="preserve">
    <value>Delete Unused Variables</value>
  </data>
  <data name="Label_Enable_Editor_When_Playing" xml:space="preserve">
    <value>Enable Editor When Playing</value>
  </data>
  <data name="GlobalVariablesWindow_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="Command_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="WelcomeWindow_Tutorials_More" xml:space="preserve">
    <value>Subscribe to our YouTube channel for updates.</value>
  </data>
  <data name="Menu_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ActionSelector_Add_Action_to_New_FSM_Tooltip" xml:space="preserve">
    <value>Add selected action to a new FSM on the selected GameObject.</value>
  </data>
  <data name="FsmEditorSettings_Show_Minimap" xml:space="preserve">
    <value>Show Minimap</value>
  </data>
  <data name="Error_Editing_Action" xml:space="preserve">
    <value>Error Editing Action</value>
  </data>
  <data name="FsmEditorSettings_Restore_Default_Settings" xml:space="preserve">
    <value>Restore Default Settings</value>
  </data>
  <data name="FsmEditorSettings_Reset_Default_Colors" xml:space="preserve">
    <value>Reset Default Colors</value>
  </data>
  <data name="Menu_Show_Used_Actions_Only" xml:space="preserve">
    <value>Show Used Actions Only</value>
  </data>
  <data name="Menu_Template_Not_Used" xml:space="preserve">
    <value>Template not used in any loaded FSMs</value>
  </data>
  <data name="ActionEditor_Show_FSM" xml:space="preserve">
    <value>Show FSM</value>
  </data>
  <data name="Label_New_Template" xml:space="preserve">
    <value>New Template</value>
  </data>
  <data name="Command_Browse" xml:space="preserve">
    <value>Browse</value>
  </data>
  <data name="Command_Set_Link_Constraint" xml:space="preserve">
    <value>Set Link Constraint</value>
  </data>
  <data name="Menu_Add_Template" xml:space="preserve">
    <value>Add Template/{0}/{1}</value>
  </data>
  <data name="Label_Yellow" xml:space="preserve">
    <value>Yellow</value>
  </data>
  <data name="Tooltip_UI_Pointer_Up" xml:space="preserve">
    <value>Sent when a mouse click is released.</value>
  </data>
  <data name="Hint_Expose_Events_and_Variables" xml:space="preserve">
    <value>Expose Events and Variables as controls in the Events and Variables Tabs by checking the Inspector option.
While the game is running, you can use the controls to test and tweak the FSM.</value>
  </data>
  <data name="Command_Console" xml:space="preserve">
    <value>Console</value>
  </data>
  <data name="Label_Editing_of_Prefab_Instance_is_disabled" xml:space="preserve">
    <value>Editing of Prefab Instance is disabled. See Preferences.</value>
  </data>
  <data name="Tooltip_Mouse_Up" xml:space="preserve">
    <value>Sent when the mouse button is released over the GameObject.</value>
  </data>
  <data name="Label_Load_All_Templates_on_Startup_Tooltip" xml:space="preserve">
    <value>Load all FSM Templates when starting the PlayMaker editor.</value>
  </data>
  <data name="AboutPlaymaker_Release_Notes" xml:space="preserve">
    <value>Release Notes</value>
  </data>
  <data name="Menu_Move_Transition_Down" xml:space="preserve">
    <value>Move Transition Down</value>
  </data>
  <data name="FsmEditorSettings_Forward_Playmaker_Log_to_Unity_Log" xml:space="preserve">
    <value>Forward Playmaker Log to Unity Log</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Transitions_Missing_Events_Tooltip" xml:space="preserve">
    <value>Check for transitions that are missing events.</value>
  </data>
  <data name="Menu_GraphView_Toggle_Breakpoint" xml:space="preserve">
    <value>Toggle Breakpoint</value>
  </data>
  <data name="Command_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Label_Refresh_Template" xml:space="preserve">
    <value>Refresh Template</value>
  </data>
  <data name="Label_Variable" xml:space="preserve">
    <value>Variable</value>
  </data>
  <data name="Menu_Clear_Transition_Event" xml:space="preserve">
    <value>Clear Transition Event</value>
  </data>
  <data name="Label_FSM_Uses_Template" xml:space="preserve">
    <value>Uses Template...</value>
  </data>
  <data name="Label_Enable_State_Labels_in_Builds" xml:space="preserve">
    <value>Enable State Labels in Builds</value>
  </data>
  <data name="FilterMenu_On_Selected_Objects" xml:space="preserve">
    <value>On Selected Objects</value>
  </data>
  <data name="Dialog_Option_Select_Template" xml:space="preserve">
    <value>Select Template</value>
  </data>
  <data name="FsmEditorSettings_Inspector_Wide_Mode_Tooltip" xml:space="preserve">
    <value>Use single line editors for Vector2, Vector3 etc. Uncheck this to use the old-style 2 line editors.</value>
  </data>
  <data name="Dialog_Edit_Variable_Type" xml:space="preserve">
    <value>Edit Variable Type</value>
  </data>
  <data name="Tooltip_UI_Pointer_Enter" xml:space="preserve">
    <value>Sent when the mouse begins to hover over the GameObject.</value>
  </data>
  <data name="Tooltip_UI_Pointer_Click" xml:space="preserve">
    <value>Sent when a click is detected.</value>
  </data>
  <data name="Label_Refresh_Action_Usage_Tooltip" xml:space="preserve">
    <value>Refresh Action Usage Counts</value>
  </data>
  <data name="Command_Show" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="Command_Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="Command_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Command_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="Tooltip_Variables_Set_To_Network_Sync" xml:space="preserve">
    <value>Variables set to NetworkSync in the Variables tab.</value>
  </data>
  <data name="FsmEditorSettings_Snap_Grid_Size_Tooltip" xml:space="preserve">
    <value>Size of the grid used when dragging states with Ctrl held down.</value>
  </data>
  <data name="Menu_Make_Animation_State" xml:space="preserve">
    <value>Make Animation State</value>
  </data>
  <data name="Label_Edit_Template_Category" xml:space="preserve">
    <value>Edit Template Category</value>
  </data>
  <data name="ActionEditor_Store_Quaternion" xml:space="preserve">
    <value>Store Quaternion</value>
  </data>
  <data name="FsmEditorSettings_Forward_Playmaker_Log_to_Unity_Log_Tooltip" xml:space="preserve">
    <value>The Playmaker Log is easier to filter by Fsm, but the Unity Log lets you see all log events in context.</value>
  </data>
  <data name="Label_PREVIEW_VERSION" xml:space="preserve">
    <value>PREVIEW VERSION - EDITING DISABLED</value>
  </data>
  <data name="Label_Unlock" xml:space="preserve">
    <value>Unlock</value>
  </data>
  <data name="Menu_Select_Template" xml:space="preserve">
    <value>Select Template</value>
  </data>
  <data name="Tooltip_Network_Sync" xml:space="preserve">
    <value>Sync this variable over the Network. NOTE: Requires a NetworkView component on the GameObject that Observes this PlayMakerFSM.</value>
  </data>
  <data name="Hint_Select_Game_Object" xml:space="preserve">
    <value>Select Game Object</value>
  </data>
  <data name="Command_New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="Label_Outputs" xml:space="preserve">
    <value>Outputs</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Setup_Errors_With_Mouse_Events_Tooltip" xml:space="preserve">
    <value>Check the owner setup for mouse events.</value>
  </data>
  <data name="Error_Could_not_save_action_category" xml:space="preserve">
    <value>Could not save action category: </value>
  </data>
  <data name="Label_Vector3" xml:space="preserve">
    <value>Vector3</value>
  </data>
  <data name="Label_Vector2" xml:space="preserve">
    <value>Vector2</value>
  </data>
  <data name="Dialog_Paste_Variables" xml:space="preserve">
    <value>Paste Variables</value>
  </data>
  <data name="Error_Failed_to_draw_inspector" xml:space="preserve">
    <value>Failed to draw inspector!</value>
  </data>
  <data name="Menu_Debug_Variables" xml:space="preserve">
    <value>Debug Variable Values</value>
  </data>
  <data name="Menu_GraphView_Set_Watermark" xml:space="preserve">
    <value>Set Watermark</value>
  </data>
  <data name="Menu_GraphView_Link_Style_Bezier" xml:space="preserve">
    <value>Link Style/Bezier</value>
  </data>
  <data name="Tooltip_Uses_scene_gizmos" xml:space="preserve">
    <value>Uses scene gizmos to edit/preview.</value>
  </data>
  <data name="Menu_GraphView_Sent_By" xml:space="preserve">
    <value>Event Sent By.../</value>
  </data>
  <data name="ActionEditor_Store_Material" xml:space="preserve">
    <value>Store Material</value>
  </data>
  <data name="ActionEditor_Store_String" xml:space="preserve">
    <value>Store String</value>
  </data>
  <data name="Hint_FsmLog" xml:space="preserve">
    <value>Use the FSM Log to examine the behaviour of FSMs at runtime.
With Debug Flow enabled, you can pause the game and step through state changes and watch variable values!</value>
  </data>
  <data name="Error_Missing_Watermark_Texture" xml:space="preserve">
    <value>Could not find any Watermark textures! Check the PlayMaker/Editor/Watermarks directory...</value>
  </data>
  <data name="Menu_FsmLog_Show_TimeCode" xml:space="preserve">
    <value>Show TimeCode</value>
  </data>
  <data name="ActionEditor_Tooltip_Parameter_XXX" xml:space="preserve">
    <value>Parameter: {0}</value>
  </data>
  <data name="Dialog_Globals_Created" xml:space="preserve">
    <value>PlayMakerGlobals asset created:

{0}/Resources/PlayMakerGlobals.asset

NOTE: Copy this file, or Use Tools &gt; Export Globals to transfer globals between projects.</value>
  </data>
  <data name="WelcomeWindow_Samples" xml:space="preserve">
    <value>Sample Scenes</value>
  </data>
  <data name="FsmErrorChecker_MissingActionError" xml:space="preserve">
    <value>Bad Actions Array! Missing Action!</value>
  </data>
  <data name="FsmEditorSettings_Disable_Error_Checker_When_Game_Is_Playing" xml:space="preserve">
    <value>Disable Error Checker When Game Is Playing</value>
  </data>
  <data name="Label_Material" xml:space="preserve">
    <value>Material</value>
  </data>
  <data name="VersionInfo_NACL_VERSION" xml:space="preserve">
    <value>NACL VERSION</value>
  </data>
  <data name="BugReportWindow_Bug_Description_Label" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="GlobalsWindow_Title" xml:space="preserve">
    <value>Globals</value>
  </data>
  <data name="ActionEditor_Tooltip_Send_Event_to_GameObject" xml:space="preserve">
    <value>Send event to all FSMs on this game object.</value>
  </data>
  <data name="Label_DISABLED" xml:space="preserve">
    <value>[DISABLED]</value>
  </data>
  <data name="Menu_GraphView_Copy_States" xml:space="preserve">
    <value>Copy States</value>
  </data>
  <data name="Labels_Use_Import_Globals_" xml:space="preserve">
    <value>Use Import Globals to import the exported globals into another project.</value>
  </data>
  <data name="Tooltip_Hide_Unused" xml:space="preserve">
    <value>Hide unused optional Action parameters (per state).</value>
  </data>
  <data name="Menu_GraphView_Delete_States" xml:space="preserve">
    <value>Delete States</value>
  </data>
  <data name="Tooltip_Lock_and_password_protect_FSM" xml:space="preserve">
    <value>Lock and password protect FSM</value>
  </data>
  <data name="Label_Show_State_Labels" xml:space="preserve">
    <value>Show State Labels</value>
  </data>
  <data name="Tooltip_Enable_GUILayout" xml:space="preserve">
    <value>Disabling GUILayout can improve the performance of GUI actions, especially on mobile devices. NOTE: You cannot use GUILayout actions with GUILayout disabled.</value>
  </data>
  <data name="Dialog_CreateTemplate_Errors" xml:space="preserve">
    <value>The created template has some errors!
Please select the template to review the error log.
NOTE: Templates cannot reference scene objects!</value>
  </data>
  <data name="Error_Missing_Animation_Component" xml:space="preserve">
    <value>Missing Animation Component</value>
  </data>
  <data name="Label_Model_Prefab_Instance" xml:space="preserve">
    <value>Model Prefab Instance</value>
  </data>
  <data name="Tooltip_Events_Used_By_States" xml:space="preserve">
    <value>Number of States that use the Event.</value>
  </data>
  <data name="Label_Favorites" xml:space="preserve">
    <value>Favorites</value>
  </data>
  <data name="Command_Add_Template_to_Selected" xml:space="preserve">
    <value>Add Template to Selected</value>
  </data>
  <data name="FsmEditorSettings_Dim_Finished_Actions_Tooltip" xml:space="preserve">
    <value>Actions that have FINISHED will be dimmed in the State tab. NOTE: The UI is not disabled, you can still click on the controls.</value>
  </data>
  <data name="Menu_Delete_Transition" xml:space="preserve">
    <value>Delete Transition</value>
  </data>
  <data name="Option_Use_Variable" xml:space="preserve">
    <value>Use Variable</value>
  </data>
  <data name="Menu_Type_Actions" xml:space="preserve">
    <value>{0} Actions</value>
  </data>
  <data name="Error_Could_not_create_directory__" xml:space="preserve">
    <value>Could not create directory: {0}</value>
  </data>
  <data name="Tooltip_Mouse_Exit" xml:space="preserve">
    <value>Sent when the mouse rolls off the GameObject.</value>
  </data>
  <data name="Tooltip_Mouse_Drag" xml:space="preserve">
    <value>Sent while the mouse button is down and over the GameObject.</value>
  </data>
  <data name="Tooltip_Mouse_Down" xml:space="preserve">
    <value>Sent when the mouse clicks on the GameObject.</value>
  </data>
  <data name="Tooltip_Mouse_Over" xml:space="preserve">
    <value>Sent while the mouse is over the GameObject.</value>
  </data>
  <data name="Dialog_No_Globals_to_import" xml:space="preserve">
    <value>No Globals to import!</value>
  </data>
  <data name="Command_Paste_States" xml:space="preserve">
    <value>Paste States</value>
  </data>
  <data name="FsmEditorSettings_Minimap" xml:space="preserve">
    <value>Minimap</value>
  </data>
  <data name="Command_Set_As_Start_State" xml:space="preserve">
    <value>Set As Start State</value>
  </data>
  <data name="FsmEditorSettings_Animate_UI_Tooltip" xml:space="preserve">
    <value>Enable animation in UI panels, e.g., expanding/collapsing actions. Disable if you notice performance issues.</value>
  </data>
  <data name="WelcomeWindow_Title" xml:space="preserve">
    <value>Welcome To PlayMaker</value>
  </data>
  <data name="EditPrefab_Disconnect_Info" xml:space="preserve">
    <value>Editing the FSM can disconnect it from the Prefab FSM. You can edit Variable Values without losing the connection.</value>
  </data>
  <data name="Tooltip_Joint_Break" xml:space="preserve">
    <value>Sent when a Joint attached to the same GameObject breaks.</value>
  </data>
  <data name="Label_You_cannot_undo_this_action" xml:space="preserve">
    <value>You cannot undo this action!</value>
  </data>
  <data name="FsmSelector_Tootlip_State" xml:space="preserve">
    <value>Start/Current State</value>
  </data>
  <data name="Label_Default" xml:space="preserve">
    <value>Default</value>
  </data>
  <data name="Menu_Check_for_Setup_Errors_With_Mouse_Events" xml:space="preserve">
    <value>Check for Setup Errors With Mouse Events</value>
  </data>
  <data name="Label_Purple" xml:space="preserve">
    <value>Purple</value>
  </data>
  <data name="Menu_Show_Disabled_FSMs" xml:space="preserve">
    <value>Show Disabled FSMs</value>
  </data>
  <data name="ActionEditor_EditFsmEventTarget_Exclude_Self" xml:space="preserve">
    <value>Exclude Self</value>
  </data>
  <data name="FsmEditorSettings_Category_Graph_Styles" xml:space="preserve">
    <value>Graph Styles</value>
  </data>
  <data name="Label_Prefab" xml:space="preserve">
    <value>Prefab</value>
  </data>
  <data name="BugReportWindow_Copy_Tooltip" xml:space="preserve">
    <value>Copy report to clipboard.</value>
  </data>
  <data name="FsmEditorSettings_Snap_Grid_Size" xml:space="preserve">
    <value>Snap Grid Size</value>
  </data>
  <data name="Tooltip_UI_End_Edit" xml:space="preserve">
    <value>Sent when when editing of an InputField has ended. Owner needs an InputField component.</value>
  </data>
  <data name="Tooltip_UI_End_Drag" xml:space="preserve">
    <value>Sent when a drag is ended.</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Setup_Errors_With_Collision_Events_Tooltip" xml:space="preserve">
    <value>Check the owner setup for collision events.</value>
  </data>
  <data name="ActionEditor_Send_To_Children_Tooltip" xml:space="preserve">
    <value>Send events to all children too.</value>
  </data>
  <data name="Tooltip_EventManager_Inspector_Checkbox" xml:space="preserve">
    <value>Check to expose this event in the Inspector as an event button.</value>
  </data>
  <data name="Dialog_Revert_Are_You_Sure" xml:space="preserve">
    <value>Are you sure you want to Revert to the Prefab FSM?

All changes will be lost!</value>
  </data>
  <data name="File_Failed_To_Create_Directory" xml:space="preserve">
    <value>Could not create directory: {0}</value>
  </data>
  <data name="CustomActionWizard_FileExists_Error" xml:space="preserve">
    <value>Action file already exists!</value>
  </data>
  <data name="Menu_GraphView_Link_Color" xml:space="preserve">
    <value>Link Color/</value>
  </data>
  <data name="Label_Load_All_Templates_on_Startup" xml:space="preserve">
    <value>Load All Templates on Startup</value>
  </data>
  <data name="Label_Selected_Template" xml:space="preserve">
    <value>Template: {0}</value>
  </data>
  <data name="Command_Move_State" xml:space="preserve">
    <value>Move State</value>
  </data>
  <data name="Command_Add_New_State" xml:space="preserve">
    <value>Add New State</value>
  </data>
  <data name="Menu_GraphView_Link_Style_Direct" xml:space="preserve">
    <value>Link Style/Direct</value>
  </data>
  <data name="Menu_Check_for_Duplicate_Transition_Events" xml:space="preserve">
    <value>Check for Duplicate Transition Events</value>
  </data>
  <data name="Label_Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="Error_Globals_Cannot_Be_Scene_Objects" xml:space="preserve">
    <value>Globals cannot be scene objects!
Use prefabs or find the scene object at runtime...</value>
  </data>
  <data name="ActionEditor_Create_New_Template" xml:space="preserve">
    <value>Create New Template</value>
  </data>
  <data name="Label_Used_By" xml:space="preserve">
    <value>Used By</value>
  </data>
  <data name="CustomActionWizard_Copy_Code" xml:space="preserve">
    <value>Copy Code</value>
  </data>
  <data name="Menu_Hide_Unused_Events" xml:space="preserve">
    <value>Hide Unused Events</value>
  </data>
  <data name="BugReportWindow_FrequencyChoices_Always" xml:space="preserve">
    <value>Always</value>
  </data>
  <data name="VersionInfol_STUDENT_VERSION" xml:space="preserve">
    <value>STUDENT VERSION</value>
  </data>
  <data name="Command_State_Browser" xml:space="preserve">
    <value>State Browser</value>
  </data>
  <data name="FsmEditorSettings_Enable_Transition_Effects_Tooltip" xml:space="preserve">
    <value>Enables a fading effect on states and transitions so it's easier to see state history. Try turning this off if the Playmaker Editor is slowing down playback.</value>
  </data>
  <data name="ActionReportWindow_Sort_By_Action" xml:space="preserve">
    <value>Sort By Action</value>
  </data>
  <data name="Label_Postfix_FSM" xml:space="preserve">
    <value> FSM</value>
  </data>
  <data name="FsmEditorSettings_Dim_Finished_Actions" xml:space="preserve">
    <value>Dim Finished Actions</value>
  </data>
  <data name="Dialog_Replace_Start_State_Description" xml:space="preserve">
    <value>The template defines a start state. Do you want to replace the current start state?</value>
  </data>
  <data name="Dialog_SaveGlobals_Created" xml:space="preserve">
    <value>PlayMakerGlobals Created:
{0}

Use PlayMaker &gt; Tools &gt; Export and Import Globals to copy globals between projects.</value>
  </data>
  <data name="Label_Find_Template_Usages_Tooltip" xml:space="preserve">
    <value>Find Template Usages...</value>
  </data>
  <data name="FsmEditorSettings_Select_State_On_Activated_Tooltip" xml:space="preserve">
    <value>Should activated states be auto-selected. Selected states are visible in the inspector.</value>
  </data>
  <data name="Dialog_Templates_can_only_be_saved_in_the_Project_s_Assets_folder" xml:space="preserve">
    <value>Templates can only be saved in the Project's Assets folder!</value>
  </data>
  <data name="Menu_None_State" xml:space="preserve">
    <value>None (State)</value>
  </data>
  <data name="ToolWindow_Header_Transitions" xml:space="preserve">
    <value>Transitions:</value>
  </data>
  <data name="Menu_None_Event" xml:space="preserve">
    <value>None (Event)</value>
  </data>
  <data name="Tooltip_KeepDelayedEvents" xml:space="preserve">
    <value>Normally delayed events are removed when the active state is exited. Check this if you want to keep delayed events queued up.</value>
  </data>
  <data name="Menu_Select_Next_FSM" xml:space="preserve">
    <value>Select Next FSM</value>
  </data>
  <data name="Command_Delete_State" xml:space="preserve">
    <value>Delete State</value>
  </data>
  <data name="Dialog_Import_Globals_From" xml:space="preserve">
    <value>Import Globals From:</value>
  </data>
  <data name="VersionInfo_TRIAL_VERSION" xml:space="preserve">
    <value>TRIAL VERSION</value>
  </data>
  <data name="Hint_Select_a_GameObject" xml:space="preserve">
    <value>Select a GameObject</value>
  </data>
  <data name="Label_Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="Menu_Check_for_Obsolete_Actions" xml:space="preserve">
    <value>Check for Obsolete Actions</value>
  </data>
  <data name="Command_Delete_Event" xml:space="preserve">
    <value>Delete Event</value>
  </data>
  <data name="Label_String" xml:space="preserve">
    <value>String</value>
  </data>
  <data name="FsmEditorSettings_Disable_the_Inspector_Panel_When_Game_Is_Playing" xml:space="preserve">
    <value>Disable the Inspector Panel When Game Is Playing </value>
  </data>
  <data name="Lable_States" xml:space="preserve">
    <value>States</value>
  </data>
  <data name="Command_Add_Transition" xml:space="preserve">
    <value>Add Transition</value>
  </data>
  <data name="FsmEditorSettings_Experimental" xml:space="preserve">
    <value>Experimental</value>
  </data>
  <data name="FsmEditorSettings_Disconnect_Modified_Prefab_Instances" xml:space="preserve">
    <value>Disconnect Modified Prefab Instances</value>
  </data>
  <data name="Label_Revert" xml:space="preserve">
    <value>Revert</value>
  </data>
  <data name="Command_Paste_Template" xml:space="preserve">
    <value>Paste Template</value>
  </data>
  <data name="PlayMaker_installation_has_moved" xml:space="preserve">
    <value>PlayMaker installation has moved. Finding new location...</value>
  </data>
  <data name="Title_NewGlobalVariable" xml:space="preserve">
    <value>New Global Variable</value>
  </data>
  <data name="Error_Missing_Script" xml:space="preserve">
    <value>Couldn't find Action Script: {0}</value>
  </data>
  <data name="Could_not_find_PlayMaker_dll_" xml:space="preserve">
    <value>Could not find PlayMaker dll!</value>
  </data>
  <data name="FsmEditorSettings_Auto_Summarize_Folded_Actions" xml:space="preserve">
    <value>Auto Summarize Folded Actions</value>
  </data>
  <data name="ActionEditor_Store_Int" xml:space="preserve">
    <value>Store Int</value>
  </data>
  <data name="Label_Model_Prefab" xml:space="preserve">
    <value>Model Prefab</value>
  </data>
  <data name="Found_Prefab_with_FSM" xml:space="preserve">
    <value>Found Prefab with FSM: </value>
  </data>
  <data name="Command_Move_Action_Up" xml:space="preserve">
    <value>Move Action Up</value>
  </data>
  <data name="Label_Property_Tooltip" xml:space="preserve">
    <value>The name of the Property.
HINT: Use Unity Script Reference for more info about the property.</value>
  </data>
  <data name="Label_Enable_Breakpoints" xml:space="preserve">
    <value>Enable Breakpoints</value>
  </data>
  <data name="Tooltip_Collision_Enter" xml:space="preserve">
    <value>Sent when the GameObject first collides with another object.</value>
  </data>
  <data name="Menu_GraphView_Paste_States" xml:space="preserve">
    <value>Paste States</value>
  </data>
  <data name="CustomActionWizard_Root_Folder" xml:space="preserve">
    <value>Root Folder</value>
  </data>
  <data name="Label_Inputs" xml:space="preserve">
    <value>Inputs</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Duplicate_Transition_Events_Tooltip" xml:space="preserve">
    <value>Check for state transitions with the same event.</value>
  </data>
  <data name="ToolWindow_Header_FSM_Tools" xml:space="preserve">
    <value>FSM Tools: </value>
  </data>
  <data name="Error_Action_is_null_" xml:space="preserve">
    <value>Action is null!</value>
  </data>
  <data name="Action_Enable_Logging" xml:space="preserve">
    <value>Enable Logging</value>
  </data>
  <data name="Title_NewVariable" xml:space="preserve">
    <value>New Variable</value>
  </data>
  <data name="FsmEditorSettings_Default_Colors" xml:space="preserve">
    <value>Default Colors</value>
  </data>
  <data name="Label_Hide_Unused" xml:space="preserve">
    <value>Hide Unused</value>
  </data>
  <data name="Dialog_ImportGlobals_Error" xml:space="preserve">
    <value>Errors importing globals:</value>
  </data>
  <data name="Menu_Unused_Event" xml:space="preserve">
    <value>No states use this event...</value>
  </data>
  <data name="Command_Delete_Actions" xml:space="preserve">
    <value>Delete Actions</value>
  </data>
  <data name="Menu_Template_not_used_in_FSM" xml:space="preserve">
    <value>Template not used in FSM</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Obsolete_Actions" xml:space="preserve">
    <value>Check for Obsolete Actions</value>
  </data>
  <data name="Label_Variables_Count" xml:space="preserve">
    <value>Variables [{0}]</value>
  </data>
  <data name="Command_Toggle_Hints" xml:space="preserve">
    <value>Hints [F1]</value>
  </data>
  <data name="FsmEditorSettings_Enable_Logging_Tooltip" xml:space="preserve">
    <value>Enable logging of events, state changes etc. Can improve performance when disabled.</value>
  </data>
  <data name="Label_Orange" xml:space="preserve">
    <value>Orange</value>
  </data>
  <data name="GlobalVariablesWindow_Refresh_Used_Count_In_This_Scene" xml:space="preserve">
    <value>Refresh Used Count In This Scene</value>
  </data>
  <data name="FsmLog_Label_Target" xml:space="preserve">
    <value>TO: </value>
  </data>
  <data name="Label_Rename_Variable" xml:space="preserve">
    <value>Rename Variable</value>
  </data>
  <data name="Could_not_load_resource" xml:space="preserve">
    <value>Could not load resource: </value>
  </data>
  <data name="Menu_Check_for_Events_Not_Used_by_Target_FSM" xml:space="preserve">
    <value>Check for Events Not Used by Target FSM</value>
  </data>
  <data name="Command_Move_Action_Down" xml:space="preserve">
    <value>Move Action Down</value>
  </data>
  <data name="Label_Output" xml:space="preserve">
    <value>Output</value>
  </data>
  <data name="Label_Object" xml:space="preserve">
    <value>Object</value>
  </data>
  <data name="Command_Paste_FSM_to_Selected" xml:space="preserve">
    <value>Paste FSM to Selected</value>
  </data>
  <data name="Label_Tween_To_Color_Tooltip" xml:space="preserve">
    <value>Color used to preview to targets in scene view.</value>
  </data>
  <data name="Label_Edit_Instance" xml:space="preserve">
    <value>Edit Instance</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Transitions_Missing_Events" xml:space="preserve">
    <value>Check for Transitions Missing Events</value>
  </data>
  <data name="Label_No_Animations_On_Object" xml:space="preserve">
    <value>No Animations On Object</value>
  </data>
  <data name="Label_Edit_Variable" xml:space="preserve">
    <value>Edit Variable</value>
  </data>
  <data name="Dialogs_Missing_Action" xml:space="preserve">
    <value>Missing Action</value>
  </data>
  <data name="Menu_Sent_By" xml:space="preserve">
    <value>Event Sent By...</value>
  </data>
  <data name="Dialog_Rename_Event_Are_you_sure" xml:space="preserve">
    <value>This is a Global Event, it will be renamed globally...</value>
  </data>
  <data name="Label_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="Menu_With_Global_Transitions" xml:space="preserve">
    <value>With Global Transitions</value>
  </data>
  <data name="Hint_Send_Event_To_Host" xml:space="preserve">
    <value>Sends an event to the FSM that launched this FSM (see Run FSM action). NOTE: Events must be marked as Global.</value>
  </data>
  <data name="Tooltip_Variable_Used_Count" xml:space="preserve">
    <value>The number of times the Variable is used in the FSM.</value>
  </data>
  <data name="Menu_Add_Before_Selected_Action" xml:space="preserve">
    <value>Add Before Selected Action</value>
  </data>
  <data name="Menu_GraphView_Paste_Template_None" xml:space="preserve">
    <value>Paste Template</value>
  </data>
  <data name="Command_Add_Selected_Event_To_FSM" xml:space="preserve">
    <value>Add Selected Event To FSM</value>
  </data>
  <data name="Label_Check_Edit_Global_Variable" xml:space="preserve">
    <value>Global variables could be used in other scenes!
Are you sure you want to edit this variable?</value>
  </data>
  <data name="Label_Events" xml:space="preserve">
    <value>Events</value>
  </data>
  <data name="Dialog_Paste_FSM_to_multiple_objects" xml:space="preserve">
    <value>Paste FSM to multiple objects?</value>
  </data>
  <data name="Command_Delete_Global_Transition" xml:space="preserve">
    <value>Delete Global Transition</value>
  </data>
  <data name="Tooltip_Browse_Animations_on_GameObject" xml:space="preserve">
    <value>Browse Animations on GameObject</value>
  </data>
  <data name="Hint_Send_Event_To_SubFSMs" xml:space="preserve">
    <value>Sends an event to the sub FSMs launched by this FSM (see Run FSM action). NOTE: Events must be marked as Global.</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Setup_Errors_With_Collision_Events" xml:space="preserve">
    <value>Check for Setup Errors With Collision Events</value>
  </data>
  <data name="Tooltip_Favorites" xml:space="preserve">
    <value>Shows actions marked as Favorites.</value>
  </data>
  <data name="SubMenu_More_" xml:space="preserve">
    <value>/More...</value>
  </data>
  <data name="Label_Used" xml:space="preserve">
    <value>Used</value>
  </data>
  <data name="Label_Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Label_Misc" xml:space="preserve">
    <value>Misc</value>
  </data>
  <data name="Label_Lock" xml:space="preserve">
    <value>Lock</value>
  </data>
  <data name="Label_Size" xml:space="preserve">
    <value>Size</value>
  </data>
  <data name="Label_Rect" xml:space="preserve">
    <value>Rect</value>
  </data>
  <data name="Label_Info" xml:space="preserve">
    <value>Info</value>
  </data>
  <data name="Label_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Label_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Label_Enum" xml:space="preserve">
    <value>Enum</value>
  </data>
  <data name="Label_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Label_Cyan" xml:space="preserve">
    <value>Cyan</value>
  </data>
  <data name="Label_Blue" xml:space="preserve">
    <value>Blue</value>
  </data>
  <data name="Label_Bool" xml:space="preserve">
    <value>Bool</value>
  </data>
  <data name="Hint_Global_Variables" xml:space="preserve">
    <value>Global Variables can be seen by any FSM.
Right click a variable to see the FSMs that use it.</value>
  </data>
  <data name="FsmEditorSettings_Enable_DebugFlowInFSM" xml:space="preserve">
    <value>Enable the recording of variable values on this FSM when you play in the Editor. When you step through states using Debug Flow you can inspect the value of variables when the state changed.

See Preferences &gt; Debugging to enable/disable the Debug Flow system.</value>
  </data>
  <data name="Dialog_Add_FSM" xml:space="preserve">
    <value>Add FSM</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Required_Action_Fields" xml:space="preserve">
    <value>Check for Required Action Fields</value>
  </data>
  <data name="Label_Action_Browser" xml:space="preserve">
    <value>Action Browser</value>
  </data>
  <data name="Tooltip_Toggle_Minimap" xml:space="preserve">
    <value>Toggle Minimap</value>
  </data>
  <data name="Command_Ping_Template_Asset" xml:space="preserve">
    <value>Ping Template Asset</value>
  </data>
  <data name="Hint_GlobalsInspector_Shows_DEFAULT_Values" xml:space="preserve">
    <value>This inspector shows the DEFAULT values of Globals. To see current values when playing, use the PlayMaker Editor (Global Variables Window, Debug fields, etc.)</value>
  </data>
  <data name="Tooltip_Disable_editing_of_prefab_intances" xml:space="preserve">
    <value>Disable editing of prefab intances unless the user explicitly confirms it.</value>
  </data>
  <data name="Menu_Set_State_Color" xml:space="preserve">
    <value>Set State Color/{0}</value>
  </data>
  <data name="CustomActionWizard_Save_Button" xml:space="preserve">
    <value>Save Custom Action</value>
  </data>
  <data name="BugReportWindow_Bug_Title_Label" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="Menu_Paste_Template" xml:space="preserve">
    <value>Paste Template/{0}/{1}</value>
  </data>
  <data name="Tooltip_Documentation_Url" xml:space="preserve">
    <value>Documentation Url...</value>
  </data>
  <data name="Hint_Right_Click_to_Add_FSM_to_Selected_Objects_" xml:space="preserve">
    <value>Right-Click to Add FSM to Selected Objects.</value>
  </data>
  <data name="Menu_Select_Previous_FSM" xml:space="preserve">
    <value>Select Previous FSM</value>
  </data>
  <data name="Menu_Edit_Script" xml:space="preserve">
    <value>Edit Script...</value>
  </data>
  <data name="Dialog_Delete_Event_Used_By" xml:space="preserve">
    <value>The event is used by </value>
  </data>
  <data name="Hint_EventManager" xml:space="preserve">
    <value>This panel shows the Events used by the selected FSM.
Right-click an Event to select States that use the Event.</value>
  </data>
  <data name="Menu_Clear_Transition_Target" xml:space="preserve">
    <value>Clear Transition Target</value>
  </data>
  <data name="Command_Enable_All_Actions" xml:space="preserve">
    <value>Enable All Actions</value>
  </data>
  <data name="Tooltip_GUIText_State_Labels" xml:space="preserve">
    <value>Draw active state labels on GUITexts.</value>
  </data>
  <data name="BugReportWindow_Error" xml:space="preserve">
    <value>Error Submitting Report:
{0}</value>
  </data>
  <data name="Command_Move_Up" xml:space="preserve">
    <value>Move Up</value>
  </data>
  <data name="BugReportWindow_Title" xml:space="preserve">
    <value>Submit Bug Report</value>
  </data>
  <data name="FsmEditorSettings_Disable_the_Inspector_Panel_When_Game_Is_Playing_Tooltip" xml:space="preserve">
    <value>Useful if you want to see the Graph View, but the inspector panel slows the game down too much.</value>
  </data>
  <data name="BugReportWindow_Success" xml:space="preserve">
    <value>Thanks for the bug report!
You should receive a confirmation email soon...</value>
  </data>
  <data name="Menu_Action_Browser" xml:space="preserve">
    <value>Action Browser</value>
  </data>
  <data name="Tooltip_Global_Variables" xml:space="preserve">
    <value>Use the Global Variables window to manage global variables that can be seen by any FSM.</value>
  </data>
  <data name="Error_Name_already_exists" xml:space="preserve">
    <value>Name already exists!</value>
  </data>
  <data name="Label_Minimap_Short" xml:space="preserve">
    <value>Map</value>
  </data>
  <data name="Command_Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Setup_Errors_With_Mouse_Events" xml:space="preserve">
    <value>Check for Setup Errors With Mouse Events</value>
  </data>
  <data name="Menu_Tools_Custom_Action_Wizard" xml:space="preserve">
    <value>Tools/Custom Action Wizard</value>
  </data>
  <data name="Hint_PlayMakerGUI_Notes" xml:space="preserve">
    <value>- A scene should only have one PlayMakerGUI component.
- PlayMaker will auto-add this component.
- Disable auto-add in Preferences.</value>
  </data>
  <data name="FsmEditorSettings_Jump_to_Breakpoint_Error_Tooltip" xml:space="preserve">
    <value>Should the editor automatically jump to a breakpoint/error. Note: This can often mean switching to a different Fsm.</value>
  </data>
  <data name="Error_Failed_to_load_Property_Drawer" xml:space="preserve">
    <value>Failed to load Property Drawer inspected type.</value>
  </data>
  <data name="Menu_GraphView_Save_Screenshot" xml:space="preserve">
    <value>Save Screenshot</value>
  </data>
  <data name="Label_Variables_and_Events_with_Inspector_checked_" xml:space="preserve">
    <value>[Variables and Events with Inspector checked]</value>
  </data>
  <data name="WelcomeWindow_Docs" xml:space="preserve">
    <value>Online Docs</value>
  </data>
  <data name="Dialog_Cannot_Delete_Start_State" xml:space="preserve">
    <value>Cannot Delete Start State!</value>
  </data>
  <data name="Hint_Sequence_States" xml:space="preserve">
    <value>Action Sequences executes each action in turn. When one action finishes the next action starts.</value>
  </data>
  <data name="FsmEditorSettings_Enable_Watermarks_Tooltip" xml:space="preserve">
    <value>Enable the large title text and graphic watermarks drawn in the background. NOTE: Try disabling this on slower computers if you experience editor slowdown.</value>
  </data>
  <data name="Dialogs_BreakPrefabInstance_Title" xml:space="preserve">
    <value>Break Prefab Instance</value>
  </data>
  <data name="FsmEditorSettings_Check_for_Events_Not_Used_by_Target_FSM_Tooltip" xml:space="preserve">
    <value>Check for events not used by target FSM. E.g., check events sent by actions.</value>
  </data>
  <data name="ActionEditor_EditFsmEventTarget_FSM_Component" xml:space="preserve">
    <value>FSM Component</value>
  </data>
  <data name="Label_Collapse_this_inspector_for_better_editor_performance__" xml:space="preserve">
    <value>NOTE: Collapse this inspector for better editor performance with large FSMs.</value>
  </data>
  <data name="iTween_Path_Editing_Label_End" xml:space="preserve">
    <value>'{0}' End</value>
  </data>
  <data name="Hint_GraphView_Getting_Started" xml:space="preserve">
    <value>1. Select a GameObject in any view.
2. Right click in this view to add an FSM.
3. Build an FSM to control the GameObject.</value>
  </data>
  <data name="WelcomeWindow_Addons_More" xml:space="preserve">
    <value>Expand PlayMaker with these powerful Add-Ons.</value>
  </data>
  <data name="FsmEditorSettings_Show_State_Labels_in_Game_View" xml:space="preserve">
    <value>Show State Labels in Game View</value>
  </data>
  <data name="Menu_Play_Animation" xml:space="preserve">
    <value>Play Animation</value>
  </data>
  <data name="FsmEditorHint_General_Settings" xml:space="preserve">
    <value>General PlayMaker Settings. Use the DropDown list above to select other categories.</value>
  </data>
  <data name="Label_Enum_Value" xml:space="preserve">
    <value>Enum Value</value>
  </data>
  <data name="Label_Global" xml:space="preserve">
    <value>Global</value>
  </data>
  <data name="Menu_Open_Log_Window" xml:space="preserve">
    <value>Open Log Window</value>
  </data>
  <data name="Hint_GraphView_Shortcuts" xml:space="preserve">
    <value>F1 Show/Hide
Ctrl Click Canvas
Ctrl Click State
Ctrl Drag Transition
Ctrl Shift Click
Ctrl Drag States
Shift Drag States
Home
Alt Click Transition
Ctrl Left/Right
Ctrl Up/Down</value>
  </data>
  <data name="Label_No_Description" xml:space="preserve">
    <value>[No Description - Edit in FSM Inspector]</value>
  </data>
  <data name="FsmEditorSettings_FSM_Screenshots_Directory_Tooltip" xml:space="preserve">
    <value>Where to save FSM screenshots. The path is relative to the Unity project directory. NOTE: It is not recommended to use a directory under Assets, since Unity will add it to the project's assets.</value>
  </data>
  <data name="Label_Select_A_Watermark" xml:space="preserve">
    <value>Select A Watermark:</value>
  </data>
  <data name="Command_Insert_Action" xml:space="preserve">
    <value>Insert Action</value>
  </data>
  <data name="Tooltip_UI_Begin_Drag" xml:space="preserve">
    <value>Sent before a drag is started.</value>
  </data>
  <data name="CustomActionWizard_Same_as_Category" xml:space="preserve">
    <value>Same as Category</value>
  </data>
  <data name="Menu_Show_State_Description" xml:space="preserve">
    <value>Show State Description</value>
  </data>
  <data name="ActionSelector_Enable_Action_Browser_When_Playing" xml:space="preserve">
    <value>Enable Action Browser When Playing</value>
  </data>
  <data name="Label_Missing_Dll_resource__" xml:space="preserve">
    <value>Missing Dll resource: {0}</value>
  </data>
  <data name="Menu_GraphView_Save_Template" xml:space="preserve">
    <value>Save Template</value>
  </data>
  <data name="Command_Preferences" xml:space="preserve">
    <value>Preferences</value>
  </data>
  <data name="Menu_Clear_Breakpoints" xml:space="preserve">
    <value>Clear Breakpoints</value>
  </data>
  <data name="FilterMenu_FSMs_With_Errors" xml:space="preserve">
    <value>FSMs With Errors</value>
  </data>
  <data name="Hint_State_Selector" xml:space="preserve">
    <value>The State Browser lets you quickly select any state in the selected FSM.</value>
  </data>
  <data name="BugReportWindow_What_happened" xml:space="preserve">
    <value>1) What happened</value>
  </data>
  <data name="Command_Clear_Watermark" xml:space="preserve">
    <value>Clear Watermark</value>
  </data>
  <data name="Menu_GraphView_Remove_FSM_Component" xml:space="preserve">
    <value>Remove FSM Component</value>
  </data>
  <data name="FsmErrorChecker_TransitionMissingTargetError" xml:space="preserve">
    <value>Transition missing target state!</value>
  </data>
  <data name="Tooltip_Event_GUI" xml:space="preserve">
    <value>Select or double-click event to add to the selected FSM. Right-click event to navigate to FSMs that use the event.</value>
  </data>
  <data name="FsmEditorSettings_Category_When_Game_Is_Playing" xml:space="preserve">
    <value>When Game Is Playing</value>
  </data>
  <data name="Menu_Log_Window" xml:space="preserve">
    <value>Log Window</value>
  </data>
  <data name="Label_Tooltip" xml:space="preserve">
    <value>Tooltip</value>
  </data>
  <data name="Tooltip_Edit_in_the_PlayMaker_Editor" xml:space="preserve">
    <value>Edit in the PlayMaker Editor</value>
  </data>
  <data name="Menu_Add_to_End_of_Action_List" xml:space="preserve">
    <value>Add to End of Action List</value>
  </data>
  <data name="Label_Variables" xml:space="preserve">
    <value>Variables</value>
  </data>
  <data name="FsmEditorSettings_Enable_DebugFlow" xml:space="preserve">
    <value>Enable DebugFlow</value>
  </data>
  <data name="Tooltip_Mouse_Enter" xml:space="preserve">
    <value>Sent when the mouse rolls over the GameObject.</value>
  </data>
  <data name="Error_Variable_Name_Taken" xml:space="preserve">
    <value>Variable name already used!</value>
  </data>
  <data name="Dialog_Delete_Template" xml:space="preserve">
    <value>Delete Template</value>
  </data>
  <data name="FsmEditorSettings_Maximize_File_Compatibility" xml:space="preserve">
    <value>Maximize File Compatibility</value>
  </data>
  <data name="FsmEditorSettings_DoDebuggingSettings_Show_State_Labels_Tooltip" xml:space="preserve">
    <value>Turns all state labels off/on in the Game View</value>
  </data>
  <data name="Tooltip_Collision_Stay_2D" xml:space="preserve">
    <value>Sent while the GameObject is colliding with a 2D Collider.</value>
  </data>
  <data name="Menu_Delete_All_Actions" xml:space="preserve">
    <value>Delete All Actions</value>
  </data>
  <data name="Menu_Step_To_Next_State_Change_in_any_FSM" xml:space="preserve">
    <value>Step To Next State Change in any FSM</value>
  </data>
  <data name="ActionEditor_EditArray_Element_XXX" xml:space="preserve">
    <value>  Element {0}</value>
  </data>
  <data name="Dialog_No_unused_events" xml:space="preserve">
    <value>No unused events...</value>
  </data>
  <data name="Dialog_Add_FSM_Template_to_multiple_objects_" xml:space="preserve">
    <value>Add FSM Template to multiple objects?</value>
  </data>
  <data name="Label_Timeline" xml:space="preserve">
    <value>Timeline</value>
  </data>
  <data name="Menu_Find_in_Template_Browser" xml:space="preserve">
    <value>Find in Template Browser</value>
  </data>
  <data name="Error_Could_not_load_watermarks" xml:space="preserve">
    <value>Could not load watermarks!</value>
  </data>
  <data name="WelcomeWindow_Photon_Cloud_More" xml:space="preserve">
    <value>Build scalable MMOGs, FPS or any other multiplayer game
and application for PC, Mac, Browser, Mobile or Console.</value>
  </data>
  <data name="ActionSelector_Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="Dialogs_PreviewVersion_Note" xml:space="preserve">
    <value>NOTE: Editing is disabled in this version of PlayMaker!
Please visit HutongGames.com for more info on PlayMaker.</value>
  </data>
  <data name="Label_Browse" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="Label_Texture" xml:space="preserve">
    <value>Texture</value>
  </data>
  <data name="FilterMenu_FSMs_in_Scene" xml:space="preserve">
    <value>FSMs in Scene</value>
  </data>
  <data name="Tab2" xml:space="preserve">
    <value>		</value>
  </data>
  <data name="Tab3" xml:space="preserve">
    <value>			</value>
  </data>
  <data name="Menu_GraphView_Add_State" xml:space="preserve">
    <value>Add State</value>
  </data>
  <data name="Label_Scene_GUI_Colors" xml:space="preserve">
    <value>Scene GUI Colors</value>
  </data>
  <data name="FsmEditorSettings_Auto_Add_PlayMakerGUI_to_Scene_Tooltip" xml:space="preserve">
    <value>Automatically adds a PlayMakerGUI component to the scene (if necessary) when you add or select an FSM. Note, you need a PlayMakerGUI in the scene to see Playmaker Gizmos, OnGUI actions and iTween path editing.</value>
  </data>
  <data name="Tooltip_Fsm_Docs" xml:space="preserve">
    <value>Fsm Docs</value>
  </data>
  <data name="Hint_DebugFlow_Disabled" xml:space="preserve">
    <value>Debug Flow Mode: Recording of variables and other state information is disabled. Enable this setting globally in Preferences and individually on each FSM that you want to track.</value>
  </data>
  <data name="Tooltip_Refresh_Template" xml:space="preserve">
    <value>Use this if you've updated the template but don't see the changes here.</value>
  </data>
  <data name="FsmEditorSettings_Category_Editing" xml:space="preserve">
    <value>Editing</value>
  </data>
  <data name="Menu_GraphView_Add_Global_Transition" xml:space="preserve">
    <value>Add Global Transition/</value>
  </data>
  <data name="Menu_Paste_Variable_Values" xml:space="preserve">
    <value>Paste Variable Values</value>
  </data>
  <data name="Label_Sorting_Layers" xml:space="preserve">
    <value>Sorting Layers</value>
  </data>
</root>