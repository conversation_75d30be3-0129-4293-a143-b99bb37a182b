// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.CommandEvents
// Assembly: PlayMakerEditorResources, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: AC0A2A01-A9BA-4780-A9C4-FA6F159AF803
// Assembly location: F:\UnityProjects\PlayMakerTest\Assets\PlayMaker\Core\PlayMakerEditorResources.dll

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace HutongGames.PlayMakerEditor
{
  [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
  [DebuggerNonUserCode]
  [CompilerGenerated]
  public class CommandEvents
  {
    private static ResourceManagerEx resourceMan;
    private static CultureInfo resourceCulture;

    internal CommandEvents()
    {
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    public static ResourceManagerEx ResourceManager
    {
      get
      {
        if (CommandEvents.resourceMan == null)
          CommandEvents.resourceMan = new ResourceManagerEx("HutongGames.PlayMakerEditor.CommandEvents");//, typeof (CommandEvents).Assembly);
        return CommandEvents.resourceMan;
      }
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    public static CultureInfo Culture
    {
      get => CommandEvents.resourceCulture;
      set => CommandEvents.resourceCulture = value;
    }

    public static string AddFsmComponent => CommandEvents.ResourceManager.GetString(nameof (AddFsmComponent), CommandEvents.resourceCulture);

    public static string Command_Make_Prefab_Instance => CommandEvents.ResourceManager.GetString(nameof (Command_Make_Prefab_Instance), CommandEvents.resourceCulture);

    public static string OpenAboutWindow => CommandEvents.ResourceManager.GetString(nameof (OpenAboutWindow), CommandEvents.resourceCulture);

    public static string OpenActionWindow => CommandEvents.ResourceManager.GetString(nameof (OpenActionWindow), CommandEvents.resourceCulture);

    public static string OpenErrorWindow => CommandEvents.ResourceManager.GetString(nameof (OpenErrorWindow), CommandEvents.resourceCulture);

    public static string OpenFsmLogWindow => CommandEvents.ResourceManager.GetString(nameof (OpenFsmLogWindow), CommandEvents.resourceCulture);

    public static string OpenFsmSelectorWindow => CommandEvents.ResourceManager.GetString(nameof (OpenFsmSelectorWindow), CommandEvents.resourceCulture);

    public static string OpenFsmTemplateWindow => CommandEvents.ResourceManager.GetString(nameof (OpenFsmTemplateWindow), CommandEvents.resourceCulture);

    public static string OpenGlobalEventsWindow => CommandEvents.ResourceManager.GetString(nameof (OpenGlobalEventsWindow), CommandEvents.resourceCulture);

    public static string OpenGlobalVariablesWindow => CommandEvents.ResourceManager.GetString(nameof (OpenGlobalVariablesWindow), CommandEvents.resourceCulture);

    public static string OpenReportWindow => CommandEvents.ResourceManager.GetString(nameof (OpenReportWindow), CommandEvents.resourceCulture);

    public static string OpenStateSelectorWindow => CommandEvents.ResourceManager.GetString(nameof (OpenStateSelectorWindow), CommandEvents.resourceCulture);

    public static string OpenToolWindow => CommandEvents.ResourceManager.GetString(nameof (OpenToolWindow), CommandEvents.resourceCulture);

    public static string OpenWelcomeWindow => CommandEvents.ResourceManager.GetString(nameof (OpenWelcomeWindow), CommandEvents.resourceCulture);
  }
}
