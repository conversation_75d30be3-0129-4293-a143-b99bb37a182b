// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.EditorPrefStrings
// Assembly: PlayMakerEditorResources, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: AC0A2A01-A9BA-4780-A9C4-FA6F159AF803
// Assembly location: F:\UnityProjects\PlayMakerTest\Assets\PlayMaker\Core\PlayMakerEditorResources.dll

using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Runtime.CompilerServices;
using System.Xml;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
  [DebuggerNonUserCode]
  [CompilerGenerated]
  public class EditorPrefStrings
  {
    private static ResourceManagerEx resourceMan;
    private static CultureInfo resourceCulture;

    internal EditorPrefStrings()
    {
    }

    // [EditorBrowsable(EditorBrowsableState.Advanced)]
    public static ResourceManagerEx ResourceManager
    {
      get
      {
        if (EditorPrefStrings.resourceMan == null)
          EditorPrefStrings.resourceMan = new ResourceManagerEx("HutongGames.PlayMakerEditor.EditorPrefStrings");//, typeof (EditorPrefStrings).Assembly);
        return EditorPrefStrings.resourceMan;
      }
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    public static CultureInfo Culture
    {
      get => EditorPrefStrings.resourceCulture;
      set => EditorPrefStrings.resourceCulture = value;
    }

    public static string BugReportWindow_UserEmail => EditorPrefStrings.ResourceManager.GetString(nameof (BugReportWindow_UserEmail), EditorPrefStrings.resourceCulture);

    public static string DrawPlaymakerGizmoInHierarchy => EditorPrefStrings.ResourceManager.GetString(nameof (DrawPlaymakerGizmoInHierarchy), EditorPrefStrings.resourceCulture);

    public static string FsmGraphView_LoadSettings_GraphView_ScrollX => EditorPrefStrings.ResourceManager.GetString(nameof (FsmGraphView_LoadSettings_GraphView_ScrollX), EditorPrefStrings.resourceCulture);

    public static string FsmGraphView_LoadView_GraphViewSelectedID => EditorPrefStrings.ResourceManager.GetString(nameof (FsmGraphView_LoadView_GraphViewSelectedID), EditorPrefStrings.resourceCulture);

    public static string FsmGraphView_SaveSettings_GraphView_ScrollY => EditorPrefStrings.ResourceManager.GetString(nameof (FsmGraphView_SaveSettings_GraphView_ScrollY), EditorPrefStrings.resourceCulture);

    public static string GraphViewScale => EditorPrefStrings.ResourceManager.GetString(nameof (GraphViewScale), EditorPrefStrings.resourceCulture);

    public static string InspectorMode => EditorPrefStrings.ResourceManager.GetString(nameof (InspectorMode), EditorPrefStrings.resourceCulture);

    public static string PlayMaker_SelectedState => EditorPrefStrings.ResourceManager.GetString(nameof (PlayMaker_SelectedState), EditorPrefStrings.resourceCulture);

    public static string PlaymakerVersion => EditorPrefStrings.ResourceManager.GetString(nameof (PlaymakerVersion), EditorPrefStrings.resourceCulture);

    public static string ShowStateLabelsInBuild => EditorPrefStrings.ResourceManager.GetString(nameof (ShowStateLabelsInBuild), EditorPrefStrings.resourceCulture);

    public static string ShowStateLabelsInGameView => EditorPrefStrings.ResourceManager.GetString(nameof (ShowStateLabelsInGameView), EditorPrefStrings.resourceCulture);

    public static string ShowUpgradeGuide => EditorPrefStrings.ResourceManager.GetString(nameof (ShowUpgradeGuide), EditorPrefStrings.resourceCulture);

    public static string ShowWelcomeScreen => EditorPrefStrings.ResourceManager.GetString(nameof (ShowWelcomeScreen), EditorPrefStrings.resourceCulture);

    public static string UserEmail => EditorPrefStrings.ResourceManager.GetString(nameof (UserEmail), EditorPrefStrings.resourceCulture);
  }
}
