// Decompiled with JetBrains decompiler
// Type: StringEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using HutongGames.PlayMakerEditor;
using System.Collections.Generic;
using System.Reflection;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;

public static class StringEditor
{
  private static FsmString editingVariable;
  private static object editingObject;
  private static FieldInfo editingField;

  private static void SetStringValue(object userdata)
  {
    Keyboard.ResetFocus();
    FsmEditor.RecordUndo("Edit Action");
    if (StringEditor.editingVariable != null)
    {
      StringEditor.editingVariable.Value = userdata as string;
    }
    else
    {
      if (StringEditor.editingObject == null || StringEditor.editingField == null)
        return;
      StringEditor.editingField.SetValue(StringEditor.editingObject, (object) (userdata as string));
    }
  }

  public static void AnimatorFloatPopup(
    GameObject go,
    FsmString variable,
    object obj = null,
    FieldInfo field = null)
  {
    StringEditor.DoAnimatorParameterPopup(go, UnityEngine.AnimatorControllerParameterType.Float, variable, obj, field);
  }

  public static void AnimatorIntPopup(
    GameObject go,
    FsmString variable,
    object obj = null,
    FieldInfo field = null)
  {
    StringEditor.DoAnimatorParameterPopup(go, UnityEngine.AnimatorControllerParameterType.Int, variable, obj, field);
  }

  public static void AnimatorBoolPopup(
    GameObject go,
    FsmString variable,
    object obj = null,
    FieldInfo field = null)
  {
    StringEditor.DoAnimatorParameterPopup(go, UnityEngine.AnimatorControllerParameterType.Bool, variable, obj, field);
  }

  public static void AnimatorTriggerPopup(
    GameObject go,
    FsmString variable,
    object obj = null,
    FieldInfo field = null)
  {
    StringEditor.DoAnimatorParameterPopup(go, UnityEngine.AnimatorControllerParameterType.Trigger, variable, obj, field);
  }

  private static void DoAnimatorParameterPopup(
    GameObject go,
    UnityEngine.AnimatorControllerParameterType parameterType,
    FsmString variable,
    object obj = null,
    FieldInfo field = null)
  {
    if (!FsmEditorGUILayout.BrowseButton((Object) go != (Object) null, string.Format(Strings.Tooltip_Browse_Animator_Parameters, (object) parameterType)))
      return;
    StringEditor.editingVariable = variable;
    StringEditor.editingObject = obj;
    StringEditor.editingField = field;
    StringEditor.DoAnimatorParameterMenu(go, parameterType);
  }

  private static void DoAnimatorParameterMenu(
    GameObject go,
    UnityEngine.AnimatorControllerParameterType parameterType)
  {
    GenericMenu genericMenu = new GenericMenu();
    foreach (string animatorParameterName in StringEditor.GetAnimatorParameterNames(go, parameterType))
      genericMenu.AddItem(new GUIContent(animatorParameterName), false, new GenericMenu.MenuFunction2(StringEditor.SetStringValue), (object) animatorParameterName);
    if (genericMenu.GetItemCount() == 0)
      genericMenu.AddDisabledItem(new GUIContent(string.Format(Strings.Menu_No_Animator_Parameters, (object) parameterType)));
    genericMenu.ShowAsContext();
  }

  public static IEnumerable<string> GetAnimatorParameterNames(
    GameObject go,
    UnityEngine.AnimatorControllerParameterType ofType)
  {
    List<string> stringList = new List<string>();
    Animator component = go.GetComponent<Animator>();
    if ((Object) component == (Object) null)
      return (IEnumerable<string>) stringList;
    UnityEditor.Animations.AnimatorController animatorController = (UnityEditor.Animations.AnimatorController) AssetDatabase.LoadAssetAtPath(AssetDatabase.GetAssetPath((Object) component.runtimeAnimatorController), typeof (UnityEditor.Animations.AnimatorController));
    if ((Object) animatorController == (Object) null)
      return (IEnumerable<string>) stringList;
    foreach (UnityEngine.AnimatorControllerParameter parameter in animatorController.parameters)
    {
      if (parameter.type == ofType)
        stringList.Add(parameter.name);
    }
    return (IEnumerable<string>) stringList;
  }

  public static void AnimationNamePopup(
    GameObject go,
    FsmString variable,
    object obj = null,
    FieldInfo field = null)
  {
    if (!FsmEditorGUILayout.BrowseButton((Object) go != (Object) null, Strings.Tooltip_Browse_Animations_on_GameObject))
      return;
    StringEditor.editingVariable = variable;
    StringEditor.editingObject = obj;
    StringEditor.editingField = field;
    StringEditor.DoAnimationNameMenu(go);
  }

  private static void DoAnimationNameMenu(GameObject go)
  {
    GenericMenu genericMenu = new GenericMenu();
    foreach (AnimationClip animationClip in AnimationUtility.GetAnimationClips(go))
    {
      if ((Object) animationClip != (Object) null)
        genericMenu.AddItem(new GUIContent(animationClip.name), false, new GenericMenu.MenuFunction2(StringEditor.SetStringValue), (object) animationClip.name);
    }
    if (genericMenu.GetItemCount() == 0)
      genericMenu.AddDisabledItem(new GUIContent(Strings.Label_No_Animations_On_Object));
    genericMenu.ShowAsContext();
  }

  public static void SortingLayerNamePopup(
    GUIContent label,
    FsmString variable,
    object obj = null,
    FieldInfo field = null)
  {
    EditorGUILayout.PrefixLabel(label, EditorStyles.popup);
    if (!GUILayout.Button(variable.Value, EditorStyles.popup))
      return;
    StringEditor.editingVariable = variable;
    StringEditor.editingObject = obj;
    StringEditor.editingField = field;
    StringEditor.DoSortingLayerMenu();
  }

  public static void SortingLayerNameBrowseButton(FsmString variable, object obj = null, FieldInfo field = null)
  {
    if (!FsmEditorGUILayout.BrowseButton(true, Strings.Label_Sorting_Layers))
      return;
    StringEditor.editingVariable = variable;
    StringEditor.editingObject = obj;
    StringEditor.editingField = field;
    StringEditor.DoSortingLayerMenu();
  }

  private static void DoSortingLayerMenu()
  {
    GenericMenu genericMenu = new GenericMenu();
    foreach (string sortingLayerName in EditorHacks.UpdateSortingLayerNames())
      genericMenu.AddItem(new GUIContent(sortingLayerName), false, new GenericMenu.MenuFunction2(StringEditor.SetStringValue), (object) sortingLayerName);
    genericMenu.ShowAsContext();
  }

  public static void TagPopup(GUIContent label, FsmString variable, object obj = null, FieldInfo field = null)
  {
    FsmEditorGUILayout.PrefixLabel(label, EditorStyles.popup);
    if (!GUILayout.Button(variable.Value, EditorStyles.popup))
      return;
    StringEditor.editingVariable = variable;
    StringEditor.editingObject = obj;
    StringEditor.editingField = field;
    StringEditor.DoTagMenu();
  }

  private static void DoTagMenu()
  {
    GenericMenu genericMenu = new GenericMenu();
    string str = StringEditor.editingVariable.Value;
    List<string> stringList = new List<string>();
    stringList.Add("<Any Tag>");
    stringList.AddRange((IEnumerable<string>) InternalEditorUtility.tags);
    foreach (string text in stringList)
      genericMenu.AddItem(new GUIContent(text), text == str || text == "<Any Tag>" && str == "", new GenericMenu.MenuFunction2(StringEditor.SetStringValue), text != "<Any Tag>" ? (object) text : (object) "");
    genericMenu.ShowAsContext();
  }

  public static void LayerNamePopup(
    GUIContent label,
    FsmString variable,
    object obj = null,
    FieldInfo field = null)
  {
    EditorGUILayout.PrefixLabel(label, EditorStyles.popup);
    if (!GUILayout.Button(variable.Value, EditorStyles.popup))
      return;
    StringEditor.editingVariable = variable;
    StringEditor.editingObject = obj;
    StringEditor.editingField = field;
    StringEditor.DoLayerMenu();
  }

  private static void DoLayerMenu()
  {
    GenericMenu genericMenu = new GenericMenu();
    for (int layer = 0; layer < 32; ++layer)
    {
      string name = LayerMask.LayerToName(layer);
      if (!string.IsNullOrEmpty(name))
        genericMenu.AddItem(new GUIContent(name), false, new GenericMenu.MenuFunction2(StringEditor.SetStringValue), (object) name);
    }
    genericMenu.ShowAsContext();
  }

  public static void FsmNamePopup(GameObject go, FsmString variable, object obj = null, FieldInfo field = null)
  {
    if (!FsmEditorGUILayout.BrowseButton((Object) go != (Object) null, Strings.Tooltip_Browse_FSMs_on_GameObject))
      return;
    StringEditor.editingVariable = variable;
    StringEditor.editingObject = obj;
    StringEditor.editingField = field;
    StringEditor.DoFsmNameMenu(go);
  }

  private static void DoFsmNameMenu(GameObject go)
  {
    GenericMenu genericMenu = new GenericMenu();
    foreach (Fsm fsm in FsmEditor.FsmList)
    {
      if ((Object) fsm.GameObject == (Object) go)
        genericMenu.AddItem(new GUIContent(fsm.Name), false, new GenericMenu.MenuFunction2(StringEditor.SetStringValue), (object) fsm.Name);
    }
    if (genericMenu.GetItemCount() == 0)
      genericMenu.AddDisabledItem(new GUIContent(Strings.Label_None));
    genericMenu.ShowAsContext();
  }

  public static void VariablesPopup(
    GameObject go,
    string fsmName,
    UIHint hint,
    FsmString variable)
  {
    if (!FsmEditorGUILayout.BrowseButton((Object) go != (Object) null, Strings.Tooltip_Browse_variables_in_FSM))
      return;
    StringEditor.editingVariable = variable;
    StringEditor.DoVariablesMenu(go, fsmName, hint);
  }

  private static void DoVariablesMenu(GameObject go, string fsmName, UIHint hint)
  {
    GenericMenu genericMenu = new GenericMenu();
    foreach (Fsm fsm in FsmEditor.FsmList)
    {
      if ((Object) fsm.GameObject == (Object) go && fsm.Name == fsmName)
      {
        foreach (NamedVariable namedVariable in fsm.Variables.GetNamedVariables(StringEditor.GetVariableType(hint)))
          genericMenu.AddItem(Menus.FormatItem(namedVariable.Name), false, new GenericMenu.MenuFunction2(StringEditor.SetStringValue), (object) namedVariable.Name);
      }
    }
    if (genericMenu.GetItemCount() == 0)
      genericMenu.AddDisabledItem(new GUIContent(Strings.Label_None));
    genericMenu.ShowAsContext();
  }

  public static VariableType GetVariableType(UIHint hint)
  {
    switch (hint)
    {
      case UIHint.FsmFloat:
        return VariableType.Float;
      case UIHint.FsmInt:
        return VariableType.Int;
      case UIHint.FsmBool:
        return VariableType.Bool;
      case UIHint.FsmString:
        return VariableType.String;
      case UIHint.FsmVector3:
        return VariableType.Vector3;
      case UIHint.FsmGameObject:
        return VariableType.GameObject;
      case UIHint.FsmColor:
        return VariableType.Color;
      case UIHint.FsmRect:
        return VariableType.Rect;
      case UIHint.FsmMaterial:
        return VariableType.Material;
      case UIHint.FsmTexture:
        return VariableType.Texture;
      case UIHint.FsmQuaternion:
        return VariableType.Quaternion;
      case UIHint.FsmObject:
        return VariableType.Object;
      case UIHint.FsmVector2:
        return VariableType.Vector2;
      case UIHint.FsmEnum:
        return VariableType.Enum;
      case UIHint.FsmArray:
        return VariableType.Array;
      default:
        Debug.LogError((object) string.Format(Strings.Error_Unrecognized_variable_type__, (object) hint));
        return VariableType.Unknown;
    }
  }
}
