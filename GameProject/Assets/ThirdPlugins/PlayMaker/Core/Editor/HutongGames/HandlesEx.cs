// Decompiled with JetBrains decompiler
// Type: HutongGames.HandlesEx
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using UnityEditor;
using UnityEngine;

namespace HutongGames
{
  public static class HandlesEx
  {
    private static readonly Vector3[] framePoints = new Vector3[5];

    public static void DrawFrame(this Rect rect, Color color)
    {
      HandlesEx.framePoints[0] = new Vector3(rect.x, rect.y);
      HandlesEx.framePoints[1] = new Vector3(rect.xMax, rect.y);
      HandlesEx.framePoints[2] = new Vector3(rect.xMax, rect.yMax);
      HandlesEx.framePoints[3] = new Vector3(rect.x, rect.yMax);
      HandlesEx.framePoints[4] = new Vector3(rect.x, rect.y);
      Color color1 = Handles.color;
      Handles.color = color;
      Handles.DrawPolyLine(HandlesEx.framePoints);
      Handles.color = color1;
    }

    public static void DrawFrameWithLabel(this Rect rect, Color color, string label)
    {
      HandlesEx.framePoints[0] = new Vector3(rect.x, rect.y);
      HandlesEx.framePoints[1] = new Vector3(rect.xMax, rect.y);
      HandlesEx.framePoints[2] = new Vector3(rect.xMax, rect.yMax);
      HandlesEx.framePoints[3] = new Vector3(rect.x, rect.yMax);
      HandlesEx.framePoints[4] = new Vector3(rect.x, rect.y);
      Color color1 = Handles.color;
      Handles.color = color;
      Handles.DrawPolyLine(HandlesEx.framePoints);
      Handles.Label(HandlesEx.framePoints[0], label);
      Handles.color = color1;
    }
  }
}
