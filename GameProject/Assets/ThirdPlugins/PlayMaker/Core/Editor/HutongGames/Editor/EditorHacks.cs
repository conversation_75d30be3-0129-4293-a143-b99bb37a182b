// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.EditorHacks
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.ComponentModel;
using System.Reflection;
using UnityEditor;

using UnityEditorInternal;
using UnityEngine;

namespace HutongGames.Editor
{
  [Localizable(false)]
  public class EditorHacks
  {
    private static readonly FieldInfo LastControlIdField = typeof (EditorGUIUtility).GetField("s_LastControlID", BindingFlags.Static | BindingFlags.NonPublic);
    private static readonly Action<string> focusTextInControl;
    private static readonly MethodInfo getScriptObjectFromClass;
    private static readonly MethodInfo getIconForObject;
    private static readonly MethodInfo getIconForAction = typeof (EditorGUIUtility).GetMethod("GetIconForObject", BindingFlags.Static | BindingFlags.NonPublic);
    private static MethodInfo isDockedMethod;
    private static MethodInfo openPrefabMethod;
    private static System.Type inspectorWindowType;
    private static EditorWindow inspectorWindow;

    static EditorHacks()
    {
      EditorHacks.getScriptObjectFromClass = typeof (EditorGUIUtility).GetMethod("GetScript", BindingFlags.Static | BindingFlags.NonPublic);
      EditorHacks.getIconForObject = typeof (EditorGUIUtility).GetMethod("GetIconForObject", BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic);
      MethodInfo method = typeof (EditorGUI).GetMethod("FocusTextInControl", BindingFlags.Static | BindingFlags.Public);
      if (method == null)
        return;
      EditorHacks.focusTextInControl = (Action<string>) Delegate.CreateDelegate(typeof (Action<string>), method);
    }

    public static int GetLastControlId()
    {
      if (EditorHacks.LastControlIdField != null)
        return (int) EditorHacks.LastControlIdField.GetValue((object) null);
      Debug.LogError((object) "Compatibility with Unity broke: can't find lastControlId field in EditorGUI");
      return 0;
    }

    public static T WithoutSelectAll<T>(Func<T> guiCall)
    {
      bool flag = Event.current.type == UnityEngine.EventType.MouseDown;
      Color cursorColor = GUI.skin.settings.cursorColor;
      if (flag)
        GUI.skin.settings.cursorColor = new Color(0.0f, 0.0f, 0.0f, 0.0f);
      T obj = guiCall();
      if (!flag)
        return obj;
      GUI.skin.settings.cursorColor = cursorColor;
      return obj;
    }

    public static void FocusTextInControl(string controlName)
    {
      if (EditorHacks.focusTextInControl != null)
        EditorHacks.focusTextInControl(controlName);
      else
        GUI.FocusControl(controlName);
    }

    public static Texture2D GetScriptTypeIcon(string scriptName)
    {
      UnityEngine.Object @object = (UnityEngine.Object) EditorHacks.getScriptObjectFromClass.Invoke((object) null, new object[1]
      {
        (object) scriptName
      });
      if (!(@object != (UnityEngine.Object) null))
        return (Texture2D) null;
      return (Texture2D) EditorHacks.getIconForObject.Invoke((object) null, new object[1]
      {
        (object) @object
      });
    }

    public static Texture GetIconForObject(GameObject go)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null)
        return (Texture) null;
      return EditorHacks.getIconForAction.Invoke((object) null, new object[1]
      {
        (object) go
      }) as Texture;
    }

    public static string[] SortingLayerNames { get; private set; }

    public static string[] UpdateSortingLayerNames() => EditorHacks.SortingLayerNames = (string[]) typeof (InternalEditorUtility).GetProperty("sortingLayerNames", BindingFlags.Static | BindingFlags.NonPublic).GetValue((object) null, new object[0]);

    public static bool IsDocked(EditorWindow editorWindow)
    {
      if ((UnityEngine.Object) editorWindow == (UnityEngine.Object) null)
        return false;
      if (EditorHacks.isDockedMethod == null)
      {
        PropertyInfo property = typeof (EditorWindow).GetProperty("docked", BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic);
        if (property != null)
          EditorHacks.isDockedMethod = property.GetGetMethod(true);
      }
      return EditorHacks.isDockedMethod != null && (bool) EditorHacks.isDockedMethod.Invoke((object) editorWindow, (object[]) null);
    }

    public static bool OpenPrefab(string assetPath, GameObject instanceRoot)
    {
      if (string.IsNullOrEmpty(assetPath) || (UnityEngine.Object) instanceRoot == (UnityEngine.Object) null)
        return false;
      if (EditorHacks.openPrefabMethod == null)
        EditorHacks.openPrefabMethod = typeof (UnityEditor.SceneManagement.PrefabStageUtility).GetMethod(nameof (OpenPrefab), BindingFlags.Static | BindingFlags.NonPublic, (Binder) null, new System.Type[2]
        {
          typeof (string),
          typeof (GameObject)
        }, (ParameterModifier[]) null);
      if (EditorHacks.openPrefabMethod != null)
      {
        object[] parameters = new object[2]
        {
          (object) assetPath,
          (object) instanceRoot
        };
        EditorHacks.openPrefabMethod.Invoke((object) null, parameters);
        return true;
      }
      AssetDatabase.OpenAsset(AssetDatabase.LoadMainAssetAtPath(assetPath));
      return false;
    }

    public static System.Type InspectorWindowType => EditorHacks.inspectorWindowType ?? (EditorHacks.inspectorWindowType = EditorHacks.GetInternalWindowType("UnityEditor.InspectorWindow"));

    public static EditorWindow GetUnityInspectorWindow()
    {
      if ((UnityEngine.Object) EditorHacks.inspectorWindow == (UnityEngine.Object) null)
      {
        EditorWindow focusedWindow = EditorWindow.focusedWindow;
        EditorHacks.inspectorWindow = EditorWindow.GetWindow(EditorHacks.InspectorWindowType);
        if ((UnityEngine.Object) focusedWindow != (UnityEngine.Object) null)
          focusedWindow.Focus();
      }
      return EditorHacks.inspectorWindow;
    }

    public static EditorWindow GetGameView() => EditorHacks.GetInternalWindow("UnityEditor.GameView");

    private static System.Type GetInternalWindowType(string windowTypeName) => typeof (UnityEditor.Editor).Assembly.GetType(windowTypeName);

    private static EditorWindow GetInternalWindow(string windowTypeName) => EditorWindow.GetWindow(typeof (UnityEditor.Editor).Assembly.GetType(windowTypeName));

    public static void SetIsInspectorExpanded(UnityEngine.Object component, bool expand) => InternalEditorUtility.SetIsInspectorExpanded(component, expand);

    public static void RepaintUnityInspector()
    {
      EditorWindow unityInspectorWindow = EditorHacks.GetUnityInspectorWindow();
      if (!((UnityEngine.Object) unityInspectorWindow != (UnityEngine.Object) null))
        return;
      unityInspectorWindow.Repaint();
    }
  }
}
