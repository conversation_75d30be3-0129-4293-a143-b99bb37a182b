// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.UndoUtility
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMakerEditor;
using System;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace HutongGames.Editor
{
  public class UndoUtility
  {
    public static void RecordObject(UnityEngine.Object obj, string name)
    {
      if (obj == (UnityEngine.Object) null)
        return;
      if (FsmEditorSettings.DisableUndoRedo)
      {
        UndoUtility.MarkSceneDirty();
      }
      else
      {
        Undo.RegisterCompleteObjectUndo(obj, name);
        if (!obj.IsSceneBound())
          EditorUtility.SetDirty(obj);
        if (!obj.IsPrefabInstance())
          return;
        EditorApplication.delayCall += (EditorApplication.CallbackFunction) (() => PrefabUtility.RecordPrefabInstancePropertyModifications(obj));
      }
    }

    public static void RegisterCompleteObjectUndo(UnityEngine.Object obj, string name)
    {
      if (obj == (UnityEngine.Object) null)
        return;
      if (FsmEditorSettings.DisableUndoRedo)
      {
        UndoUtility.MarkSceneDirty();
      }
      else
      {
        Undo.RegisterCompleteObjectUndo(obj, "PlayMaker: " + name);
        if (EditorUtility.IsPersistent(obj))
          EditorUtility.SetDirty(obj);
        if (!obj.IsPrefabInstance())
          return;
        EditorApplication.delayCall += (EditorApplication.CallbackFunction) (() => PrefabUtility.RecordPrefabInstancePropertyModifications(obj));
      }
    }

    public static void MarkSceneDirty()
    {
      if (EditorApplication.isPlaying)
        return;
      EditorSceneManager.MarkSceneDirty(SceneManager.GetActiveScene());
      FsmEditor.IgnoreHierarchyChange = true;
    }

    [Obsolete("Use RecordObject instead.")]
    public static void RegisterUndo(UnityEngine.Object objectToUndo, string message)
    {
      if (objectToUndo == (UnityEngine.Object) null)
        Debug.LogWarning((object) "UndoUtility: Object was null!");
      else if (FsmEditorSettings.DisableUndoRedo)
        UndoUtility.MarkSceneDirty();
      else
        Undo.RecordObject(objectToUndo, "PlayMaker: " + message);
    }

    public static void RegisterUndo(UnityEngine.Object[] objectsToUndo, string message)
    {
      if (FsmEditorSettings.DisableUndoRedo)
        UndoUtility.MarkSceneDirty();
      else
        Undo.RecordObjects(objectsToUndo, message);
    }

    [Obsolete("Use UndoUtility.RegisterUndo instead")]
    public static void SetSnapshotTarget(UnityEngine.Object objectsToUndo, string message) => UndoUtility.RegisterUndo(objectsToUndo, message);

    [Obsolete("Use UndoUtility.RegisterUndo instead")]
    public static void CreateSnapshot()
    {
    }

    [Obsolete("Use UndoUtility.RegisterUndo instead")]
    public static void RegisterSnapshot()
    {
    }
  }
}
