// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.PopupWindowManager
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames.Editor
{
  public class PopupWindowManager
  {
    private EditorWindow parentWindow;
    private List<PopupWindow> popupWindows;
    private static FieldInfo focusedWindowField;

    public static int focusedWindow
    {
      get
      {
        if (PopupWindowManager.focusedWindowField == null)
          PopupWindowManager.focusedWindowField = typeof (GUI).GetField(nameof (focusedWindow), BindingFlags.Static | BindingFlags.NonPublic);
        return (int) PopupWindowManager.focusedWindowField.GetValue((object) null);
      }
    }

    public PopupWindowManager(EditorWindow window)
    {
      this.parentWindow = window;
      this.popupWindows = new List<PopupWindow>();
    }

    public void OnGUI()
    {
      if (this.popupWindows.Count == 0)
        return;
      this.parentWindow.BeginWindows();
      foreach (PopupWindow popupWindow in this.popupWindows)
        popupWindow.DoGUI();
      this.parentWindow.EndWindows();
    }

    public bool HitTestPopups(Vector2 position)
    {
      foreach (PopupWindow popupWindow in this.popupWindows)
      {
        if (popupWindow.Position.Contains(position))
          return true;
      }
      return false;
    }

    public int GetNextPopupID() => this.popupWindows.Count;

    public PopupWindow AddWindow(System.Type popupType, Rect position)
    {
      PopupWindow instance = (PopupWindow) Activator.CreateInstance(popupType, (object) this.GetNextPopupID());
      instance.Position = position;
      return this.AddWindow(instance);
    }

    public PopupWindow AddWindow(PopupWindow popup)
    {
      this.popupWindows.Add(popup);
      this.parentWindow.Repaint();
      return popup;
    }

    public void RemoveWindow(PopupWindow popup)
    {
      this.popupWindows.Remove(popup);
      this.parentWindow.Repaint();
    }
  }
}
