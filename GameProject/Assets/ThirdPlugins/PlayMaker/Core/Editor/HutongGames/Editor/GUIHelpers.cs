// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.GUIHelpers
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Collections.Generic;
using System.ComponentModel;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;

namespace HutongGames.Editor
{
  [Localizable(false)]
  public static class GUIHelpers
  {
    private static readonly Dictionary<UnityEngine.Object, SerializedObject> serializedObjectCache = new Dictionary<UnityEngine.Object, SerializedObject>();
    private static readonly Stack<Color> guiColors = new Stack<Color>();
    private static readonly Color faded = new Color(1f, 1f, 1f, 0.5f);
    private static readonly Stack<Color> guiBackgroundColors = new Stack<Color>();
    private static readonly Stack<Color> guiContentColors = new Stack<Color>();

    public static void DebugLastRect()
    {
      if (Event.current.type != UnityEngine.EventType.Repaint)
        return;
      GUILayoutUtility.GetLastRect().DrawFrame(Color.red);
    }

    public static bool IsHidpi => (double) EditorGUIUtility.pixelsPerPoint > 1.0;

    public static SerializedObject GetSerializedObject(UnityEngine.Object targetObject) => targetObject == (UnityEngine.Object) null ? (SerializedObject) null : new SerializedObject(targetObject);

    public static SerializedObject GetSharedSerializedObject(UnityEngine.Object targetObject)
    {
      if (targetObject == (UnityEngine.Object) null)
        return (SerializedObject) null;
      SerializedObject serializedObject;
      if (!GUIHelpers.serializedObjectCache.TryGetValue(targetObject, out serializedObject))
      {
        serializedObject = new SerializedObject(targetObject);
        GUIHelpers.serializedObjectCache.Add(targetObject, serializedObject);
      }
      return serializedObject;
    }

    public static string GetWindowControlId(EditorWindow window, string controlName) => ((UnityEngine.Object) window != (UnityEngine.Object) null ? window.GetInstanceID().ToString() + "." : "") + controlName;

    public static void SafeExitGUI()
    {
      if (Event.current == null)
        return;
      GUIUtility.ExitGUI();
    }

    public static void SafeRepaint()
    {
      if (Event.current == null)
        return;
      HandleUtility.Repaint();
    }

    public static void DrawTexture(
      Rect position,
      Texture texture,
      Color tint,
      ScaleMode scaleMode = ScaleMode.StretchToFill)
    {
      Color color = GUI.color;
      GUI.color = tint;
      GUI.DrawTexture(position, texture, scaleMode);
      GUI.color = color;
    }

    public static void ArrayGUI(SerializedProperty property)
    {
      SerializedProperty propertyRelative = property.FindPropertyRelative("Array.size");
      EditorGUILayout.PropertyField(propertyRelative);
      ++EditorGUI.indentLevel;
      for (int index = 0; index < propertyRelative.intValue; ++index)
        EditorGUILayout.PropertyField(property.GetArrayElementAtIndex(index));
      --EditorGUI.indentLevel;
    }

    public static void ObjectPropertyField(
      SerializedProperty prop,
      GUIContent label,
      UnityEngine.Object value,
      System.Type type,
      bool allowSceneObjects)
    {
      if (prop == null)
        GUILayout.Label("SerializedProperty is null!");
      else if (type == null)
      {
        GUILayout.Label("Error null type: " + prop.propertyPath);
      }
      else
      {
        Rect rect = GUILayoutUtility.GetRect(0.0f, EditorGUIUtility.singleLineHeight, EditorStyles.textField);
        EditorGUI.BeginProperty(rect, label, prop);
        EditorGUI.BeginChangeCheck();
        value = EditorGUI.ObjectField(rect, label, value, type, allowSceneObjects);
        if (EditorGUI.EndChangeCheck())
          prop.objectReferenceValue = value;
        EditorGUI.EndProperty();
      }
    }

    public static void EnumFromIntPropertyField(
      SerializedProperty prop,
      GUIContent label,
      System.Type type)
    {
      if (type == null)
        GUILayout.Label("Error null type: " + prop.propertyPath);
      else if (!type.IsEnum)
        GUILayout.Label("Error !type.IsEnum: " + (object) type);
      else if (prop == null)
      {
        GUILayout.Label("SerializedProperty is null!");
      }
      else
      {
        Enum selected = (Enum) Enum.Parse(type, prop.intValue.ToString());
        Rect rect = GUILayoutUtility.GetRect(0.0f, EditorGUIUtility.singleLineHeight, EditorStyles.textField);
        EditorGUI.BeginProperty(rect, label, prop);
        EditorGUI.BeginChangeCheck();
        Enum @enum = EditorGUI.EnumPopup(rect, label, selected);
        if (EditorGUI.EndChangeCheck())
          prop.intValue = Convert.ToInt32((object) @enum);
        EditorGUI.EndProperty();
      }
    }

    public static void QuaternionEulerAnglesPropertyField(SerializedProperty prop, GUIContent label)
    {
      if (prop == null)
      {
        GUILayout.Label("SerializedProperty is null!");
      }
      else
      {
        Vector3 eulerAngles = prop.quaternionValue.eulerAngles;
        Rect vectorRect = GUIHelpers.GetVectorRect();
        EditorGUI.BeginProperty(vectorRect, label, prop);
        EditorGUI.BeginChangeCheck();
        Vector3 euler = EditorGUI.Vector3Field(vectorRect, label, eulerAngles);
        if (EditorGUI.EndChangeCheck())
          prop.quaternionValue = Quaternion.Euler(euler);
        EditorGUI.EndProperty();
      }
    }

    public static Rect GetVectorRect() => GUILayoutUtility.GetRect(0.0f, EditorGUIUtility.singleLineHeight * (EditorGUIUtility.wideMode ? 1f : 2f), EditorStyles.textField);

    public static Rect GetVector4Rect() => GUILayoutUtility.GetRect(0.0f, EditorGUIUtility.singleLineHeight * (EditorGUIUtility.wideMode ? 2f : 3f), EditorStyles.textField);

    public static Rect GetSingleLineRect() => GUILayoutUtility.GetRect(0.0f, EditorGUIUtility.singleLineHeight, EditorStyles.textField);

    public static bool ClickableEmptySpace()
    {
      GUILayout.BeginVertical();
      if (!GUILayout.Button("", GUIStyle.none))
      {
        if (!GUILayout.Button("", GUIStyle.none, GUILayout.ExpandHeight(true)))
        {
          GUILayout.EndVertical();
          return false;
        }
      }
      GUILayout.EndVertical();
      return true;
    }

    public static Texture2D FlipTexture(Texture2D original, bool upSideDown = true)
    {
      original = GUIHelpers.DuplicateTexture(original);
      Texture2D texture2D = new Texture2D(original.width, original.height);
      int width = original.width;
      int height = original.height;
      for (int index1 = 0; index1 < width; ++index1)
      {
        for (int index2 = 0; index2 < height; ++index2)
        {
          if (upSideDown)
            texture2D.SetPixel(index2, width - index1 - 1, original.GetPixel(index2, index1));
          else
            texture2D.SetPixel(width - index1 - 1, index2, original.GetPixel(index1, index2));
        }
      }
      texture2D.Apply();
      return texture2D;
    }

    public static Texture2D DuplicateTexture(Texture2D source)
    {
      RenderTexture temporary = RenderTexture.GetTemporary(source.width, source.height, 0, RenderTextureFormat.Default, RenderTextureReadWrite.Linear);
      Graphics.Blit((Texture) source, temporary);
      RenderTexture active = RenderTexture.active;
      RenderTexture.active = temporary;
      Texture2D texture2D = new Texture2D(source.width, source.height);
      texture2D.ReadPixels(new Rect(0.0f, 0.0f, (float) temporary.width, (float) temporary.height), 0, 0);
      texture2D.Apply();
      RenderTexture.active = active;
      RenderTexture.ReleaseTemporary(temporary);
      return texture2D;
    }

    public static void DrawRect(Rect rect, Color color)
    {
      Vector3[] vector3Array = new Vector3[5]
      {
        new Vector3(rect.x, rect.y),
        new Vector3(rect.xMax, rect.y),
        new Vector3(rect.xMax, rect.yMax),
        new Vector3(rect.x, rect.yMax),
        new Vector3(rect.x, rect.y)
      };
      Color color1 = Handles.color;
      Handles.color = color;
      Handles.DrawPolyLine(vector3Array);
      Handles.color = color1;
    }

    public static Texture2D NewTexture(Color color)
    {
      Texture2D texture2D = new Texture2D(1, 1);
      texture2D.SetPixel(1, 1, color);
      texture2D.Apply(false);
      return texture2D;
    }

    public static Texture GetBuiltinIcon(string name) => EditorGUIUtility.IconContent(name).image;

    public static void DrawPolyline(
      Color color,
      Texture2D lineTexture,
      float width,
      params Vector3[] points)
    {
      Color color1 = Handles.color;
      Handles.color = color;
      Handles.DrawAAPolyLine(lineTexture, width, points);
      Handles.color = color1;
    }

    public static void DrawPolyline(
      Color color,
      Texture2D lineTexture,
      float width,
      Vector2 offset,
      params Vector3[] points)
    {
      Vector3[] vector3Array = new Vector3[points.Length];
      for (int index = 0; index < points.Length; ++index)
        vector3Array[index] = new Vector3(points[index].x + offset.x, points[index].y + offset.y, 0.0f);
      Color color1 = Handles.color;
      Handles.color = color;
      Handles.DrawAAPolyLine(lineTexture, width, vector3Array);
      Handles.color = color1;
    }

    public static void BeginGuiColor(Color color)
    {
      GUIHelpers.guiColors.Push(GUI.color);
      GUI.color = color;
    }

    public static void BeginGuiColorFaded()
    {
      GUIHelpers.guiColors.Push(GUI.color);
      GUI.color = GUIHelpers.faded;
    }

    public static void BeginGuiColor(Color color, float alpha)
    {
      GUIHelpers.guiColors.Push(GUI.color);
      GUI.color = new Color(color.r, color.g, color.b, alpha);
    }

    public static void BeginGuiColorFaded(float alpha) => GUIHelpers.BeginGuiColor(new Color(GUI.color.r, GUI.color.g, GUI.color.b, GUI.color.a * alpha));

    public static void BeginGuiColorKeepAlpha(Color color) => GUIHelpers.BeginGuiColor(new Color(color.r, color.g, color.b, GUI.color.a));

    public static void BeginGuiColor() => GUIHelpers.guiColors.Push(GUI.color);

    public static void EndGuiColor() => GUI.color = GUIHelpers.guiColors.Pop();

    public static void BeginGuiBackgroundColor(Color color)
    {
      GUIHelpers.guiBackgroundColors.Push(GUI.backgroundColor);
      GUI.backgroundColor = color;
    }

    public static void EndGuiBackgroundColor() => GUI.backgroundColor = GUIHelpers.guiBackgroundColors.Pop();

    public static void BeginGuiContentColor(Color color)
    {
      GUIHelpers.guiContentColors.Push(GUI.contentColor);
      GUI.contentColor = color;
    }

    public static void EndGuiContentColor() => GUI.contentColor = GUIHelpers.guiContentColors.Pop();

    public static bool TryParseHtmlString(string htmlColor, out Color color)
    {
      if (ColorUtility.TryParseHtmlString("#" + htmlColor, out color))
        return true;
      color = Color.white;
      return false;
    }

    public static bool TryLoadHtmlColor(string htmlColor, out Color color)
    {
      if (ColorUtility.TryParseHtmlString("#" + htmlColor, out color))
        return true;
      color = Color.white;
      return false;
    }

    public static string GetHtmlColor(Color color, bool useColor) => !useColor ? "" : ColorUtility.ToHtmlStringRGBA(color);

    public static Color GetAutoTextColor(Color bgColor)
    {
      Debug.Log((object) ("bgColor: " + (object) bgColor));
      return (double) bgColor.r * 0.29899999499321 + (double) bgColor.g * 0.587000012397766 + (double) bgColor.b * (57.0 / 500.0) <= 0.729411780834198 ? Color.white : Color.black;
    }

    public static void UseEvent(string use)
    {
      if (Event.current.type == UnityEngine.EventType.Used)
        return;
      Event.current.Use();
    }

    public static void AddMenuItem(
      GenericMenu menu,
      string action,
      bool selected,
      bool enabled,
      GenericMenu.MenuFunction func)
    {
      if (enabled)
        menu.AddItem(new GUIContent(action), selected, func);
      else
        menu.AddDisabledItem(new GUIContent(action));
    }

    public static int LayerMaskField(GUIContent label, int layermask) => (int) GUIHelpers.FieldToLayerMask(EditorGUILayout.MaskField(label, GUIHelpers.LayerMaskToField((LayerMask) layermask), InternalEditorUtility.layers));

    public static int LayerMaskField(Rect position, GUIContent label, int layermask) => (int) GUIHelpers.FieldToLayerMask(EditorGUI.MaskField(position, label, GUIHelpers.LayerMaskToField((LayerMask) layermask), InternalEditorUtility.layers));

    public static LayerMask FieldToLayerMask(int field)
    {
      LayerMask layerMask = (LayerMask) 0;
      string[] layers = InternalEditorUtility.layers;
      for (int index = 0; index < layers.Length; ++index)
      {
        if ((field & 1 << index) != 0)
          layerMask = (LayerMask) ((int) layerMask | 1 << LayerMask.NameToLayer(layers[index]));
      }
      return layerMask;
    }

    public static int LayerMaskToField(LayerMask mask)
    {
      int num = 0;
      string[] layers = InternalEditorUtility.layers;
      for (int index = 0; index < layers.Length; ++index)
      {
        if (((int) mask & 1 << LayerMask.NameToLayer(layers[index])) != 0)
          num |= 1 << index;
      }
      return num;
    }
  }
}
