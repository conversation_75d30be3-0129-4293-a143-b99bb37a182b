// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.HighlighterHelper
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using UnityEditor;
using UnityEditor.AnimatedValues;
using UnityEngine;

namespace HutongGames.Editor
{
  [Localizable(false)]
  public class HighlighterHelper
  {
    public static Color HighlightColor = new Color(0.9921569f, 0.5882353f, 0.01568628f);
    public static float HighlightWidth = 3f;
    private static readonly Dictionary<System.Type, List<HighlighterHelper.Highlight>> highlightLookup = new Dictionary<System.Type, List<HighlighterHelper.Highlight>>();
    public static bool Enabled;
    public static Action<EditorWindow, string> MouseOver;
    public static Action<EditorWindow, string> MouseEnter;
    private static EditorWindow lastOverWindow;
    private static string lastOverId;
    private static EditorWindow currentWindow;
    private static System.Type currentWindowType;
    private static AnimFloat pingAnim;
    public static HighlighterHelper.Highlight ActiveHighlight;
    private static System.Type activateWindow;
    private static string activateId;
    private static bool inArea;
    private static Vector2 scrollPosition;
    private static Rect tempRect = new Rect();

    public static bool Disabled
    {
      get => !HighlighterHelper.Enabled;
      set => HighlighterHelper.Enabled = !value;
    }

    public static bool MouseEvents { get; set; }

    public static void Init(EditorWindow editorWindow)
    {
      if ((UnityEngine.Object) editorWindow == (UnityEngine.Object) null)
        UnityEngine.Debug.LogWarning((object) "HighligherHelper: Null window!");
      HighlighterHelper.currentWindow = editorWindow;
      HighlighterHelper.currentWindowType = (UnityEngine.Object) HighlighterHelper.currentWindow != (UnityEngine.Object) null ? HighlighterHelper.currentWindow.GetType() : (System.Type) null;
    }

    public static void SetActiveHighlight(System.Type window, string id)
    {
      HighlighterHelper.ClearActiveHighlight();
      HighlighterHelper.Highlight highlight = HighlighterHelper.GetHighlight(window, id);
      if (highlight != null)
      {
        HighlighterHelper.ActiveHighlight = highlight;
        EditorApplication.update += new EditorApplication.CallbackFunction(HighlighterHelper.UpdateActiveHighlight);
      }
      else
      {
        EditorApplication.update += new EditorApplication.CallbackFunction(HighlighterHelper.SetActiveHighlightDelayed);
        HighlighterHelper.activateWindow = window;
        HighlighterHelper.activateId = id;
      }
    }

    public static void PingHighlight(System.Type window, string id)
    {
      HighlighterHelper.SetActiveHighlight(window, id);
      AnimFloat animFloat = new AnimFloat(1f);
      animFloat.target = 0.0f;
      animFloat.speed = 0.2f;
      HighlighterHelper.pingAnim = animFloat;
    }

    public static void PingInspectorComponent(string id) => HighlighterHelper.PingHighlight(EditorHacks.InspectorWindowType, id);

    private static void SetActiveHighlightDelayed()
    {
      HighlighterHelper.Highlight highlight = HighlighterHelper.GetHighlight(HighlighterHelper.activateWindow, HighlighterHelper.activateId);
      if (highlight != null)
      {
        HighlighterHelper.ActiveHighlight = highlight;
        EditorApplication.update += new EditorApplication.CallbackFunction(HighlighterHelper.UpdateActiveHighlight);
      }
      EditorApplication.update -= new EditorApplication.CallbackFunction(HighlighterHelper.SetActiveHighlightDelayed);
    }

    public static void ClearActiveHighlight()
    {
      if (HighlighterHelper.ActiveHighlight != null && (UnityEngine.Object) HighlighterHelper.ActiveHighlight.inWindow != (UnityEngine.Object) null)
        HighlighterHelper.ActiveHighlight.inWindow.Repaint();
      HighlighterHelper.pingAnim = (AnimFloat) null;
      HighlighterHelper.ActiveHighlight = (HighlighterHelper.Highlight) null;
      EditorApplication.update -= new EditorApplication.CallbackFunction(HighlighterHelper.UpdateActiveHighlight);
    }

    public static void ValidateActiveHighlight()
    {
      if (HighlighterHelper.ActiveHighlight == null || HighlighterHelper.GetHighlight(HighlighterHelper.ActiveHighlight.window, HighlighterHelper.ActiveHighlight.id) == HighlighterHelper.ActiveHighlight)
        return;
      HighlighterHelper.ClearActiveHighlight();
    }

    public static void UpdateActiveHighlight()
    {
      if (HighlighterHelper.ActiveHighlight != null && (UnityEngine.Object) HighlighterHelper.ActiveHighlight.inWindow != (UnityEngine.Object) null)
      {
        HighlighterHelper.ActiveHighlight.inWindow.Repaint();
        if (HighlighterHelper.pingAnim != null)
        {
          if ((double) HighlighterHelper.pingAnim.value >= 0.1)
            return;
          HighlighterHelper.ClearActiveHighlight();
        }
        else
          EditorApplication.update -= new EditorApplication.CallbackFunction(HighlighterHelper.UpdateActiveHighlight);
      }
      else
        EditorApplication.update -= new EditorApplication.CallbackFunction(HighlighterHelper.UpdateActiveHighlight);
    }

    public static void OnGUI()
    {
      if (HighlighterHelper.ActiveHighlight == null || HighlighterHelper.ActiveHighlight.window != HighlighterHelper.currentWindowType)
        return;
      Color highlightColor = HighlighterHelper.HighlightColor;
      if (HighlighterHelper.pingAnim != null && HighlighterHelper.pingAnim.isAnimating)
        highlightColor.a = Mathf.Clamp01(HighlighterHelper.pingAnim.value * 4f);
      HighlighterHelper.ActiveHighlight.DrawRect(highlightColor, HighlighterHelper.HighlightWidth);
    }

    public static void Reset() => HighlighterHelper.highlightLookup.Clear();

    public static List<HighlighterHelper.Highlight> GetHighlights(System.Type window)
    {
      List<HighlighterHelper.Highlight> highlightList;
      return HighlighterHelper.highlightLookup.TryGetValue(window, out highlightList) ? highlightList : new List<HighlighterHelper.Highlight>();
    }

    public static HighlighterHelper.Highlight GetHighlight(System.Type window, string id)
    {
      foreach (HighlighterHelper.Highlight highlight in HighlighterHelper.GetHighlights(window))
      {
        if (highlight.id == id)
          return highlight;
      }
      return (HighlighterHelper.Highlight) null;
    }

    public static void Reset(System.Type window) => HighlighterHelper.GetHighlights(window).Clear();

    public static bool IsDefined(System.Type windowType, string id) => HighlighterHelper.GetHighlights(windowType).Any<HighlighterHelper.Highlight>((Func<HighlighterHelper.Highlight, bool>) (highlight => highlight.id == id));

    public static void AddHighlight(Rect rect, string id, int priority = -1)
    {
      if ((UnityEngine.Object) HighlighterHelper.currentWindow == (UnityEngine.Object) null || string.IsNullOrEmpty(id))
        return;
      rect = HighlighterHelper.ClampToWindow(rect, HighlighterHelper.HighlightWidth);
      if (HighlighterHelper.highlightLookup.ContainsKey(HighlighterHelper.currentWindowType))
      {
        HighlighterHelper.Highlight highlight1 = HighlighterHelper.GetHighlight(HighlighterHelper.currentWindowType, id);
        if (highlight1 == null)
        {
          HighlighterHelper.Highlight highlight2 = new HighlighterHelper.Highlight(HighlighterHelper.currentWindow, id, rect);
          List<HighlighterHelper.Highlight> highlights = HighlighterHelper.GetHighlights(HighlighterHelper.currentWindowType);
          if (priority < 0 || priority > highlights.Count)
            highlights.Add(highlight2);
          else
            highlights.Insert(priority, highlight2);
        }
        else
          highlight1.rect.Set(rect.x, rect.y, rect.width, rect.height);
      }
      else
      {
        HighlighterHelper.Highlight highlight = new HighlighterHelper.Highlight(HighlighterHelper.currentWindow, id, rect);
        HighlighterHelper.highlightLookup.Add(HighlighterHelper.currentWindowType, new List<HighlighterHelper.Highlight>()
        {
          highlight
        });
      }
    }

    public static void RemoveHighlight(string id)
    {
      if ((UnityEngine.Object) HighlighterHelper.currentWindow == (UnityEngine.Object) null)
        return;
      HighlighterHelper.GetHighlights(HighlighterHelper.currentWindowType).RemoveAll((Predicate<HighlighterHelper.Highlight>) (h => h.id == id));
    }

    public static Rect areaRect { get; private set; }

    public static void BeginArea(Rect area)
    {
      GUILayout.BeginArea(area);
      HighlighterHelper.areaRect = HighlighterHelper.inArea ? new Rect(HighlighterHelper.areaRect.x + area.x, HighlighterHelper.areaRect.y + area.y, area.width, area.height) : area;
      HighlighterHelper.inArea = true;
    }

    public static void StartArea(Rect area)
    {
      Rect rect;
      if (!HighlighterHelper.inArea)
      {
        rect = area;
      }
      else
      {
        Rect areaRect = HighlighterHelper.areaRect;
        double num1 = (double) areaRect.x + (double) area.x;
        areaRect = HighlighterHelper.areaRect;
        double num2 = (double) areaRect.y + (double) area.y;
        double width = (double) area.width;
        double height = (double) area.height;
        rect = new Rect((float) num1, (float) num2, (float) width, (float) height);
      }
      HighlighterHelper.areaRect = rect;
      HighlighterHelper.inArea = true;
    }

    public static void SetScrollPosition(Vector2 scroll) => HighlighterHelper.scrollPosition = scroll;

    public static void EndArea()
    {
      GUILayout.EndArea();
      HighlighterHelper.areaRect = new Rect();
      HighlighterHelper.inArea = false;
    }

    public static void FromGUILayout(string id, int priority = -1)
    {
      if (HighlighterHelper.Disabled || string.IsNullOrEmpty(id) || Event.current.type != UnityEngine.EventType.Repaint)
        return;
      if (HighlighterHelper.inArea)
      {
        HighlighterHelper.FromGUILayoutInArea(HighlighterHelper.areaRect, id, priority);
      }
      else
      {
        Rect lastRect = GUILayoutUtility.GetLastRect();
        HighlighterHelper.AddHighlight(lastRect, id, priority);
        if (!HighlighterHelper.MouseEvents)
          return;
        HighlighterHelper.UpdateMouseEvents(lastRect, id);
      }
    }

    public static void FromGUILayoutInArea(Rect area, string id, int priority = -1)
    {
      if (HighlighterHelper.Disabled || string.IsNullOrEmpty(id) || Event.current.type != UnityEngine.EventType.Repaint)
        return;
      Rect lastRect = GUILayoutUtility.GetLastRect();
      lastRect.x += area.x;
      lastRect.y += area.y - HighlighterHelper.scrollPosition.y;
      if ((double) lastRect.y < (double) area.y)
      {
        lastRect.height = Mathf.Max(0.0f, lastRect.height - (area.y - lastRect.y));
        lastRect.y = area.y;
      }
      if ((double) lastRect.y > (double) area.yMax)
      {
        lastRect.y = area.yMax;
        lastRect.height = 0.0f;
      }
      else if ((double) lastRect.yMax > (double) area.yMax)
        lastRect.height = area.yMax - lastRect.y;
      if ((double) lastRect.xMax > (double) area.xMax)
        lastRect.width = Mathf.Max(0.0f, area.xMax - lastRect.x);
      HighlighterHelper.AddHighlight(lastRect, id, priority);
      if (!HighlighterHelper.MouseEvents)
        return;
      HighlighterHelper.UpdateMouseEvents(lastRect, id);
    }

    public static void FromRectInArea(Rect area, Rect rect, string id, int priority = -1)
    {
      if (HighlighterHelper.Disabled || string.IsNullOrEmpty(id) || Event.current.type != UnityEngine.EventType.Repaint)
        return;
      rect.Set(Mathf.Min(rect.x + area.x, area.x + area.width), Mathf.Min(rect.y + area.y, area.y + area.height), rect.width, rect.height);
      if ((double) rect.xMax > (double) area.xMax)
        rect.width = Mathf.Max(0.0f, area.xMax - rect.x);
      HighlighterHelper.AddHighlight(rect, id, priority);
      if (!HighlighterHelper.MouseEvents)
        return;
      HighlighterHelper.UpdateMouseEvents(rect, id);
    }

    public static void FromWindowArea(string id, int priority = -1)
    {
      if (HighlighterHelper.Disabled || string.IsNullOrEmpty(id) || (UnityEngine.Object) HighlighterHelper.currentWindow == (UnityEngine.Object) null)
        return;
      HighlighterHelper.tempRect.Set(0.0f, -0.0f, HighlighterHelper.currentWindow.position.width - 0.0f, (float) ((double) HighlighterHelper.currentWindow.position.height + 0.0 - 0.0));
      HighlighterHelper.AddHighlight(HighlighterHelper.tempRect, id);
      if (!HighlighterHelper.MouseEvents)
        return;
      HighlighterHelper.UpdateMouseEvents(HighlighterHelper.tempRect, id);
    }

    public static void FromPosition(
      float x,
      float y,
      float width,
      float height,
      string id,
      int priority = -1)
    {
      if (HighlighterHelper.Disabled || string.IsNullOrEmpty(id))
        return;
      HighlighterHelper.tempRect.Set(x, y, width, height);
      HighlighterHelper.AddHighlight(HighlighterHelper.tempRect, id, priority);
      if (!HighlighterHelper.MouseEvents)
        return;
      HighlighterHelper.UpdateMouseEvents(HighlighterHelper.tempRect, id);
    }

    public static void FromRect(Rect rect, string id, int priority = -1)
    {
      if (HighlighterHelper.Disabled || string.IsNullOrEmpty(id))
        return;
      HighlighterHelper.AddHighlight(rect, id, priority);
      if (!HighlighterHelper.MouseEvents)
        return;
      HighlighterHelper.UpdateMouseEvents(HighlighterHelper.tempRect, id);
    }

    private static void UpdateMouseEvents(Rect highlightRect, string highlightId)
    {
      if (!highlightRect.Contains(Event.current.mousePosition))
        return;
      if (HighlighterHelper.MouseOver != null)
        HighlighterHelper.MouseOver(HighlighterHelper.currentWindow, highlightId);
      if ((HighlighterHelper.lastOverId != highlightId || (UnityEngine.Object) HighlighterHelper.lastOverWindow != (UnityEngine.Object) HighlighterHelper.currentWindow) && HighlighterHelper.MouseEnter != null)
        HighlighterHelper.MouseEnter(HighlighterHelper.currentWindow, highlightId);
      HighlighterHelper.lastOverWindow = HighlighterHelper.currentWindow;
      HighlighterHelper.lastOverId = highlightId;
    }

    public static void DebugPrint()
    {
      foreach (KeyValuePair<System.Type, List<HighlighterHelper.Highlight>> keyValuePair in HighlighterHelper.highlightLookup)
      {
        string text = keyValuePair.Key.Name;
        foreach (HighlighterHelper.Highlight highlight in keyValuePair.Value)
          text = text + " " + (object) highlight;
        GUILayout.Label(text);
      }
    }

    public static void EndVertical(string highlightIdentifier, int priority = -1)
    {
      GUILayout.EndVertical();
      HighlighterHelper.FromGUILayout(highlightIdentifier, priority);
    }

    public static void EndHorizontal(string highlightIdentifier, int priority = -1)
    {
      GUILayout.EndHorizontal();
      HighlighterHelper.FromGUILayout(highlightIdentifier, priority);
    }

    public static void EndScrollView(string highlightIdentifier, int priority = -1)
    {
      EditorGUILayout.EndScrollView();
      HighlighterHelper.FromGUILayout(highlightIdentifier, priority);
    }

    public static void EndDisabledGroup(string id, int priority = -1)
    {
      EditorGUI.EndDisabledGroup();
      HighlighterHelper.FromGUILayout(id, priority);
    }

    private static Rect ClampToWindow(Rect rect, float pad)
    {
      if ((UnityEngine.Object) HighlighterHelper.currentWindow == (UnityEngine.Object) null)
        return rect;
      float x = Mathf.Max(rect.x - pad, 0.0f);
      float y = Mathf.Max(rect.y - pad, 0.0f);
      float width = Mathf.Min(rect.xMax - pad, HighlighterHelper.currentWindow.position.width) - x;
      float height = Mathf.Min(rect.yMax - pad, HighlighterHelper.currentWindow.position.height) - y;
      rect.Set(x, y, width, height);
      return rect;
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }

    public class Highlight
    {
      public EditorWindow inWindow;
      public System.Type window;
      public string id;
      public Rect rect;
      private static Rect left;
      private static Rect right;
      private static Rect top;
      private static Rect bottom;

      public Highlight(EditorWindow inWindow, string id, Rect rect)
      {
        this.inWindow = inWindow;
        this.window = inWindow.GetType();
        this.rect = new Rect();
        this.rect.Set(Mathf.Max(rect.x, 0.0f), Mathf.Max(rect.y, 0.0f), rect.width, rect.height);
        this.id = id;
      }

      public void DrawInnerRect(Color color, float width)
      {
        HighlighterHelper.Highlight.left.Set(this.rect.x, this.rect.y, width, this.rect.height);
        HighlighterHelper.Highlight.right.Set(this.rect.xMax - width, this.rect.y, width, this.rect.height);
        HighlighterHelper.Highlight.top.Set(this.rect.x, this.rect.y, this.rect.width, width);
        HighlighterHelper.Highlight.bottom.Set(this.rect.x, this.rect.yMax - width, this.rect.width, width);
        this.DrawRect(color, width);
      }

      public void DrawOuterRect(Color color, float width)
      {
        HighlighterHelper.Highlight.left.Set(this.rect.x - width, this.rect.y - width, width, this.rect.height + width);
        HighlighterHelper.Highlight.right.Set(this.rect.xMax, this.rect.y - width, width, this.rect.height + width);
        HighlighterHelper.Highlight.top.Set(this.rect.x, this.rect.y - width, this.rect.width + width, width);
        HighlighterHelper.Highlight.bottom.Set(this.rect.x, this.rect.yMax, this.rect.width, width);
        this.DrawRect(color, width);
      }

      public void DrawRect(Color color, float width)
      {
        HighlighterHelper.Highlight.left.Set(this.rect.x, this.rect.y, width, this.rect.height);
        HighlighterHelper.Highlight.right.Set(this.rect.xMax, this.rect.y, width, this.rect.height);
        HighlighterHelper.Highlight.top.Set(this.rect.x, this.rect.y, this.rect.width, width);
        HighlighterHelper.Highlight.bottom.Set(this.rect.x, this.rect.yMax, this.rect.width + width, width);
        EditorGUI.DrawRect(HighlighterHelper.Highlight.left, color);
        EditorGUI.DrawRect(HighlighterHelper.Highlight.top, color);
        EditorGUI.DrawRect(HighlighterHelper.Highlight.bottom, color);
        EditorGUI.DrawRect(HighlighterHelper.Highlight.right, color);
      }
    }
  }
}
