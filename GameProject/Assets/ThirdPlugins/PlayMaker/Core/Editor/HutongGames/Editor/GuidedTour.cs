// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.GuidedTour
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace HutongGames.Editor
{
  [Localizable(false)]
  public class GuidedTour
  {
    private readonly List<System.Type> windows = new List<System.Type>();
    private readonly Dictionary<System.Type, List<GuidedTour.Topic>> windowTopics = new Dictionary<System.Type, List<GuidedTour.Topic>>();
    public Action OnSelectionChanged;
    private System.Type highlightedWindow;
    private string highlightedId;

    public GuidedTour.Topic SelectedTopic { get; private set; }

    public void Reset()
    {
      this.windows.Clear();
      this.windowTopics.Clear();
      this.ClearHighlight();
    }

    public List<System.Type> GetWindows() => this.windows;

    public List<GuidedTour.Topic> GetTopics(System.Type window)
    {
      List<GuidedTour.Topic> topicList;
      return !this.windowTopics.TryGetValue(window, out topicList) ? (List<GuidedTour.Topic>) null : topicList;
    }

    public GuidedTour.Topic AddWindow(
      System.Type window,
      string label,
      string tooltip = "",
      string helpText = "",
      string highlightID = "",
      string helpUrl = "")
    {
      if (this.windows.Contains(window))
      {
        Debug.LogError((object) ("Guided Tour already contains window: " + window.Name));
        return (GuidedTour.Topic) null;
      }
      this.windows.Add(window);
      GuidedTour.Topic topic = new GuidedTour.Topic()
      {
        Window = window,
        Label = new GUIContent(label, tooltip),
        HelpText = HtmlText.ParseHtml(helpText),
        HighlightID = highlightID,
        HelpUrl = helpUrl,
        Indent = 0
      };
      return this.AddTopic(window, topic);
    }

    public GuidedTour.Topic AddTopic(
      GuidedTour.Topic parent,
      string label,
      string tooltip = "",
      string helpText = "",
      string highlightID = "",
      string helpUrl = "")
    {
      return this.AddTopic(parent.Window, new GuidedTour.Topic()
      {
        Parent = parent,
        Window = parent.Window,
        Indent = parent.Indent + 1,
        Label = new GUIContent(label, tooltip),
        HelpText = HtmlText.ParseHtml(helpText),
        HighlightID = highlightID,
        HelpUrl = helpUrl
      });
    }

    private GuidedTour.Topic AddTopic(System.Type window, GuidedTour.Topic topic)
    {
      if (string.IsNullOrEmpty(topic.HighlightID))
        topic.HighlightID = topic.Label.text;
      List<GuidedTour.Topic> topics = this.GetTopics(window);
      if (topics == null)
        this.windowTopics.Add(window, new List<GuidedTour.Topic>()
        {
          topic
        });
      else
        topics.Add(topic);
      return topic;
    }

    public bool IsSelected(System.Type window) => this.highlightedWindow == window;

    public bool IsHighlighted(GuidedTour.Topic topic) => this.highlightedId == topic.HighlightID && this.highlightedWindow == topic.Window;

    public void UpdateTopicTree()
    {
      foreach (GuidedTour.Topic topic in this.windowTopics.SelectMany<KeyValuePair<System.Type, List<GuidedTour.Topic>>, GuidedTour.Topic>((Func<KeyValuePair<System.Type, List<GuidedTour.Topic>>, IEnumerable<GuidedTour.Topic>>) (x => (IEnumerable<GuidedTour.Topic>) x.Value)))
        topic.IsOpen = false;
      for (GuidedTour.Topic topic = this.SelectedTopic; topic != null; topic = topic.Parent)
      {
        if (topic.Validate != null)
        {
          topic.IsOpen = topic.Validate();
          if (!topic.IsOpen)
          {
            this.ClearHighlight();
            this.SelectedTopic = topic.Parent;
            if (this.OnSelectionChanged != null)
              this.OnSelectionChanged();
          }
        }
        else
          topic.IsOpen = true;
      }
    }

    public bool IsVisible(GuidedTour.Topic topic)
    {
      if (topic.Indent == 0)
        return true;
      return (HighlighterHelper.IsDefined(topic.Window, topic.HighlightID) || topic.OnClick != null) && topic.Parent.IsOpen;
    }

    public bool IsDescendant(GuidedTour.Topic parent, GuidedTour.Topic topic)
    {
      for (; topic.Parent != null; topic = topic.Parent)
      {
        if (topic.Parent == parent)
          return true;
      }
      return false;
    }

    public void HighlightTopic(GuidedTour.Topic topic)
    {
      EditorWindow window = EditorWindow.GetWindow(topic.Window);
      if (topic.OnClick != null)
        topic.OnClick();
      HighlighterHelper.SetActiveHighlight(window.GetType(), topic.HighlightID);
      this.highlightedWindow = topic.Window;
      this.highlightedId = topic.HighlightID;
      this.SelectedTopic = topic;
      window.Repaint();
    }

    public void ValidateHighlight()
    {
      if (this.SelectedTopic != null && this.SelectedTopic.Validate != null && !this.SelectedTopic.Validate())
        this.ClearHighlight();
      HighlighterHelper.ValidateActiveHighlight();
      if (HighlighterHelper.ActiveHighlight != null)
        return;
      this.ClearHighlight();
    }

    public void ClearHighlight()
    {
      HighlighterHelper.ClearActiveHighlight();
      this.highlightedWindow = (System.Type) null;
      this.highlightedId = string.Empty;
      this.SelectedTopic = (GuidedTour.Topic) null;
    }

    public delegate bool Validate();

    public class Topic
    {
      public GuidedTour.Topic Parent;
      public System.Type Window;
      public bool IsOpen;
      public int Indent;
      public GUIContent Label;
      public HtmlText HelpText;
      public string HighlightID;
      public string HelpUrl;
      public Action OnClick;
      public GuidedTour.Validate Validate;
      public int version;

      public bool IsNew(int newVersion) => this.version >= newVersion;
    }
  }
}
