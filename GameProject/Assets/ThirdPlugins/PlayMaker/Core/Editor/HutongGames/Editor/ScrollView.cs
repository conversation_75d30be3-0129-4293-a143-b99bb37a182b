// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.ScrollView
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using UnityEditor;
using UnityEditor.AnimatedValues;
using UnityEngine;
using UnityEngine.Events;

namespace HutongGames.Editor
{
  public class ScrollView
  {
    public Vector2 ScrollPosition;
    public bool Animate = true;
    private readonly EditorWindow window;
    private Rect autoScrollTarget;
    private bool autoScrolling;
    private bool waitingForAutoScrollTarget;
    private readonly AnimFloat autoScrollAnim;

    public Rect Area { get; private set; }

    public Rect ContentArea { get; private set; }

    public bool VerticalScrollbarVisible { get; private set; }

    public ScrollView(EditorWindow window)
    {
      this.window = window;
      AnimFloat animFloat = new AnimFloat(0.0f, new UnityAction(this.UpdateAutoScroll));
      animFloat.speed = 4f;
      this.autoScrollAnim = animFloat;
    }

    public void Begin()
    {
      this.ScrollPosition = GUILayout.BeginScrollView(this.ScrollPosition);
      GUILayout.BeginVertical();
    }

    public void Begin(bool enabled)
    {
      bool changed = GUI.changed;
      bool enabled1 = GUI.enabled;
      GUI.enabled = enabled;
      this.ScrollPosition = GUILayout.BeginScrollView(this.ScrollPosition);
      GUI.enabled = enabled1;
      GUI.changed = changed;
      GUILayout.BeginVertical();
    }

    public void AutoScroll()
    {
      this.waitingForAutoScrollTarget = true;
      this.autoScrolling = true;
    }

    public void SetAutoScrollTarget(Rect target)
    {
      this.autoScrollTarget = target;
      this.waitingForAutoScrollTarget = false;
    }

    public void CancelAutoScroll() => this.autoScrolling = false;

    public void ScrollToTop() => this.ScrollPosition.y = 0.0f;

    public void ScrollToBottom() => this.ScrollPosition.y = float.MaxValue;

    public void End()
    {
      int num = Event.current.type == UnityEngine.EventType.Repaint ? 1 : 0;
      GUILayout.EndVertical();
      if (num != 0)
        this.ContentArea = GUILayoutUtility.GetLastRect();
      GUILayout.EndScrollView();
      if (num == 0)
        return;
      this.Area = GUILayoutUtility.GetLastRect();
      this.VerticalScrollbarVisible = (double) this.ContentArea.height > (double) this.Area.height;
      if (!this.autoScrolling || this.waitingForAutoScrollTarget)
        return;
      this.DoAutoScroll();
    }

    private void DoAutoScroll()
    {
      this.autoScrolling = false;
      this.waitingForAutoScrollTarget = true;
      float num1 = this.ScrollPosition.y + this.Area.height;
      if ((double) this.autoScrollTarget.y < (double) this.ScrollPosition.y)
      {
        if (this.Animate)
        {
          this.autoScrollAnim.value = this.ScrollPosition.y;
          this.autoScrollAnim.target = this.autoScrollTarget.y;
        }
        else
          this.ScrollPosition.y = this.autoScrollTarget.y;
      }
      else if ((double) this.autoScrollTarget.yMax > (double) num1)
      {
        float num2 = this.ScrollPosition.y + this.autoScrollTarget.yMax - num1;
        if ((double) this.ScrollPosition.y > (double) this.autoScrollTarget.y)
          this.ScrollPosition.y = this.autoScrollTarget.y;
        if (this.Animate)
        {
          this.autoScrollAnim.value = this.ScrollPosition.y;
          this.autoScrollAnim.target = num2;
        }
        else
          this.ScrollPosition.y = num2;
      }
      this.Repaint();
    }

    private void UpdateAutoScroll()
    {
      this.ScrollPosition.y = this.autoScrollAnim.value;
      this.Repaint();
    }

    private void Repaint()
    {
      if ((Object) this.window != (Object) null)
      {
        this.window.Repaint();
      }
      else
      {
        if (Event.current == null)
          return;
        HandleUtility.Repaint();
      }
    }
  }
}
