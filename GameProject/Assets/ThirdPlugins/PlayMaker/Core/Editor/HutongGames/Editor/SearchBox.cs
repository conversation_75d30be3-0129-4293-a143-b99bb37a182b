// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.SearchBox
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMakerEditor;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.Editor
{
  [Localizable(false)]
  public class SearchBox
  {
    public EditorApplication.CallbackFunction SearchChanged;
    private EditorWindow window;
    private string saveKey;
    private bool focusSearchBox;
    private string searchString = "";
    private int searchMode;
    private bool searchChanged;
    private GUIContent[] searchModesMenuItems;
    private string[] searchModes = new string[1]{ "" };
    private Rect searchModePopupButton;
    private GUIStyle searchFieldStyle;
    private GUIStyle searchFieldCancelButtonStyle;
    private GUIStyle searchFieldCancelButtonEmptyStyle;

    public string SearchString => this.searchString;

    public bool HasPopupSearchModes { get; set; }

    public string[] SearchModes
    {
      get => this.searchModes;
      set => this.searchModes = value;
    }

    public int SearchMode
    {
      get => this.searchMode;
      set => this.SelectSearchMode((object) null, (string[]) null, value);
    }

    public SearchBox(EditorWindow window)
    {
      this.window = window;
      this.saveKey = (Object) window != (Object) null ? window.GetType().ToString() : nameof (SearchBox);
      this.SetSearchFilter(EditorPrefs.GetString(this.saveKey + ".SearchString", ""));
      this.SearchMode = EditorPrefs.GetInt(this.saveKey + ".SearchMode", 0);
    }

    public void Clear()
    {
      this.SetSearchFilter("");
      Keyboard.ResetFocus(true);
    }

    public void Focus() => this.focusSearchBox = true;

    public bool HasFocus { get; private set; }

    public void OnGUI()
    {
      this.InitStyles();
      this.DoSearchBox(GUILayoutUtility.GetRect(1f, 200f, 16f, 16f, this.searchFieldStyle));
      HighlighterHelper.FromGUILayout("Search");
      if (this.searchModes.Length != 0)
      {
        Rect lastRect = GUILayoutUtility.GetLastRect();
        HighlighterHelper.FromPosition(lastRect.x, lastRect.y, 16f, lastRect.height, "Search Mode");
      }
      if (!this.searchChanged || Event.current.type != UnityEngine.EventType.Layout)
        return;
      if (this.SearchChanged != null)
        this.SearchChanged();
      this.searchChanged = false;
    }

    private void DoSearchBox(Rect position)
    {
      if (Event.current.type == UnityEngine.EventType.MouseDown && position.Contains(Event.current.mousePosition))
        this.focusSearchBox = true;
      if (Event.current.type == UnityEngine.EventType.ValidateCommand && Event.current.commandName == "Find")
      {
        Event.current.Use();
      }
      else
      {
        if (Event.current.type == UnityEngine.EventType.ExecuteCommand && Event.current.commandName == "Find")
          this.focusSearchBox = true;
        GUI.SetNextControlName("SearchField");
        if (this.focusSearchBox)
        {
          EditorHacks.FocusTextInControl("SearchField");
          if (Event.current.type == UnityEngine.EventType.Repaint)
            this.focusSearchBox = false;
        }
        this.HasFocus = GUI.GetNameOfFocusedControl() == "SearchField";
        if (Event.current.type == UnityEngine.EventType.KeyDown && Event.current.keyCode == KeyCode.Escape)
        {
          if (this.HasFocus)
          {
            this.SetSearchFilter(string.Empty);
            GUIUtility.keyboardControl = 0;
            Event.current.Use();
          }
          else
          {
            if (string.IsNullOrEmpty(this.searchString))
              this.searchMode = 0;
            this.SetSearchFilter(string.Empty);
          }
        }
        string searchFilter = this.ToolbarSearchField(position, this.searchString);
        if (!(this.searchString != searchFilter))
          return;
        this.SetSearchFilter(searchFilter);
        this.window.Repaint();
      }
    }

    private string ToolbarSearchField(Rect position, string text)
    {
      if (this.HasPopupSearchModes && this.DoSearchModePopup(position))
        this.DoSearchModeMenu(position);
      Rect position1 = position;
      position1.x += position.width - 14f;
      position1.width = 14f;
      if (!string.IsNullOrEmpty(text))
        EditorGUIUtility.AddCursorRect(position1, MouseCursor.Arrow);
      if (Event.current.type == UnityEngine.EventType.MouseUp && position1.Contains(Event.current.mousePosition))
      {
        text = "";
        GUIUtility.keyboardControl = 0;
        GUI.changed = true;
      }
      Rect position2 = position;
      if (!FsmEditorStyles.UsingFlatStyle)
        position2.width -= 14f;
      text = EditorGUI.TextField(position2, text, this.searchFieldStyle);
      if (text == string.Empty && (!this.HasFocus || (Object) EditorWindow.focusedWindow != (Object) this.window || this.searchMode != 0) && Event.current.type == UnityEngine.EventType.Repaint)
      {
        EditorGUI.BeginDisabledGroup(true);
        Color backgroundColor = GUI.backgroundColor;
        GUI.backgroundColor = Color.clear;
        if (this.HasFocus && !((Object) EditorWindow.focusedWindow != (Object) this.window) && this.searchMode > 0)
        {
          this.searchFieldStyle.alignment = TextAnchor.UpperRight;
          this.searchFieldStyle.Draw(position2, this.searchModes[this.searchMode] + " ", false, false, false, false);
          this.searchFieldStyle.alignment = TextAnchor.UpperLeft;
        }
        EditorGUI.EndDisabledGroup();
        GUI.backgroundColor = backgroundColor;
      }
      GUI.Button(position1, GUIContent.none, text != "" ? this.searchFieldCancelButtonStyle : this.searchFieldCancelButtonEmptyStyle);
      return text;
    }

    private void InitStyles()
    {
      if (this.searchFieldStyle != null && this.searchFieldCancelButtonStyle != null && this.searchFieldCancelButtonEmptyStyle != null)
        return;
      this.searchFieldStyle = (GUIStyle) (this.HasPopupSearchModes ? "ToolbarSeachTextFieldPopup" : "ToolbarSeachTextField");
      this.searchFieldCancelButtonStyle = (GUIStyle) "ToolbarSeachCancelButton";
      this.searchFieldCancelButtonEmptyStyle = (GUIStyle) "ToolbarSeachCancelButtonEmpty";
    }

    private bool DoSearchModePopup(Rect position)
    {
      this.searchModePopupButton.Set(position.xMin, position.yMin, 20f, position.height);
      return this.searchModes.Length != 0 && Event.current.type == UnityEngine.EventType.MouseDown && this.searchModePopupButton.Contains(Event.current.mousePosition);
    }

    private void DoSearchModeMenu(Rect position)
    {
      if (this.searchModesMenuItems == null)
      {
        this.searchModesMenuItems = new GUIContent[this.searchModes.Length];
        for (int index = 0; index < this.searchModes.Length; ++index)
          this.searchModesMenuItems[index] = new GUIContent(this.searchModes[index]);
      }
      EditorUtility.DisplayCustomMenu(position, this.searchModesMenuItems, this.searchMode, new EditorUtility.SelectMenuItemFunction(this.SelectSearchMode), (object) null);
      Event.current.Use();
    }

    private void SelectSearchMode(object userData, string[] options, int selected)
    {
      if (this.searchMode == selected)
        return;
      this.searchMode = selected;
      this.searchChanged = true;
      this.window.Repaint();
      EditorPrefs.SetInt(this.saveKey + ".SearchMode", this.searchMode);
    }

    private void SetSearchFilter(string searchFilter)
    {
      if (searchFilter == this.searchString)
        return;
      this.searchString = searchFilter;
      this.HasFocus = false;
      this.searchChanged = true;
      this.window.Repaint();
      EditorPrefs.SetString(this.saveKey + ".SearchString", this.searchString);
    }
  }
}
