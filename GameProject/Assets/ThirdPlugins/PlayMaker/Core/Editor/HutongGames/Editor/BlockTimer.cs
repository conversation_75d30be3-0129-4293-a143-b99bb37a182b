// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.BlockTimer
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using UnityEngine.Profiling;

namespace HutongGames.Editor
{
  [Localizable(false)]
  public class BlockTimer
  {
    private static readonly Stack<BlockTimer> stack = new Stack<BlockTimer>();
    private readonly string name;
    private readonly Stopwatch stopwatch;
    private readonly float warningThreshold;
    private readonly float errorThreshold;

    public static void Start(string name, float warning = 10f, float error = 20f)
    {
      BlockTimer.stack.Push(new BlockTimer(name, warning, error));
      Profiler.BeginSample(name);
    }

    public static string End()
    {
      BlockTimer blockTimer = BlockTimer.stack.Pop();
      if (blockTimer != null)
      {
        Profiler.EndSample();
        return blockTimer.GetReport();
      }
      UnityEngine.Debug.LogError((object) "BlockTimer: Bad stack!");
      return "<color=red><b>BlockTimer: Bad stack!</b></color>";
    }

    private BlockTimer(string name, float warning, float error)
    {
      this.name = name;
      this.warningThreshold = warning;
      this.errorThreshold = error;
      this.stopwatch = Stopwatch.StartNew();
    }

    private string GetReport()
    {
      long elapsedMilliseconds = this.stopwatch.ElapsedMilliseconds;
      return string.Format("<color={0}>{1}: {2}ms</color>", (object) this.GetColor((float) elapsedMilliseconds), (object) this.name, (object) elapsedMilliseconds);
    }

    private string GetColor(float time)
    {
      if ((double) time > (double) this.errorThreshold)
        return "red";
      return (double) time > (double) this.warningThreshold ? "yellow" : "green";
    }
  }
}
