// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.HtmlTextEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using UnityEditor;
using UnityEngine;

namespace HutongGames.Editor
{
  public class HtmlTextEditor
  {
    public Action OnCommit;
    private HtmlText htmlText;
    private bool startEditing;
    private string originalText;
    private bool stopEditing;
    private bool cancelEditing;
    private Rect editArea;
    private static HtmlTextEditor.Styles styles;

    public bool locked { get; set; }

    public bool commitOnEnter { get; set; }

    public bool isEditing { get; private set; }

    public string OnGUI(string text, float width = -1f)
    {
      if (HtmlTextEditor.styles == null)
        HtmlTextEditor.styles = new HtmlTextEditor.Styles();
      if ((double) width < 0.0)
        width = EditorGUIUtility.currentViewWidth;
      width -= 30f;
      Event current = Event.current;
      UnityEngine.EventType type = current.type;
      bool flag = type == UnityEngine.EventType.Repaint;
      string name = "HtmlTextEditor_" + (object) GUIUtility.GetControlID(FocusType.Keyboard);
      if (this.isEditing)
      {
        GUI.SetNextControlName(name);
        if (current.isKey)
        {
          switch (Event.current.keyCode)
          {
            case KeyCode.Return:
            case KeyCode.KeypadEnter:
              if (this.commitOnEnter)
              {
                this.stopEditing = true;
                current.Use();
                break;
              }
              break;
            case KeyCode.Escape:
              this.cancelEditing = true;
              break;
          }
        }
        text = EditorGUILayout.TextArea(text, HtmlTextEditor.styles.TextAreaWithWordWrap);
        if (flag)
          this.editArea = GUILayoutUtility.GetLastRect();
        if (this.startEditing)
        {
          GUI.FocusControl(name);
          this.startEditing = false;
        }
        if (GUI.GetNameOfFocusedControl() != name)
          this.stopEditing = true;
        if (type == UnityEngine.EventType.MouseDown && !this.editArea.Contains(current.mousePosition))
          this.stopEditing = true;
        if (flag)
        {
          if (this.stopEditing)
          {
            this.isEditing = false;
            this.stopEditing = false;
            text = text.Trim();
            HandleUtility.Repaint();
            if (this.OnCommit != null)
              this.OnCommit();
          }
          if (this.cancelEditing)
          {
            text = this.originalText;
            this.isEditing = false;
            this.cancelEditing = false;
            HandleUtility.Repaint();
          }
        }
        return text;
      }
      if (this.htmlText == null || this.htmlText.rawText != text)
        this.htmlText = HtmlText.ParseHtml(text);
      GUILayout.BeginVertical(HtmlTextEditor.styles.Margins);
      this.htmlText.DoGUILayout(width);
      GUILayout.EndVertical();
      if (!this.locked && type == UnityEngine.EventType.MouseDown && current.button == 0 && GUILayoutUtility.GetLastRect().Contains(Event.current.mousePosition))
      {
        if (current.clickCount == 2)
        {
          this.startEditing = true;
          HandleUtility.Repaint();
        }
        GUI.FocusControl("");
        current.Use();
      }
      if (flag && this.startEditing)
      {
        this.isEditing = true;
        this.originalText = text;
        HandleUtility.Repaint();
      }
      return text;
    }

    private class Styles
    {
      public readonly GUIStyle Margins;
      public readonly GUIStyle TextAreaWithWordWrap;

      public Styles()
      {
        this.Margins = new GUIStyle()
        {
          padding = new RectOffset(2, 4, 0, 0)
        };
        this.TextAreaWithWordWrap = new GUIStyle(EditorStyles.textArea)
        {
          wordWrap = true
        };
        this.TextAreaWithWordWrap.margin = new RectOffset();
      }
    }
  }
}
