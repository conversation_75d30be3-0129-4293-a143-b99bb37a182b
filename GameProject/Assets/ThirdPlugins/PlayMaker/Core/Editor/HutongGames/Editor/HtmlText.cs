// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.HtmlText
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using HutongGames.PlayMakerEditor;
using HutongGames.PlayMakerEditorData;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;

namespace HutongGames.Editor
{
  [Localizable(false)]
  public class HtmlText
  {
    private const int MAX_LINKS = 1000;
    public static Color linkColor;
    private const string linkStart = "<a href=\"";
    private const string linkEnd = "</a>";
    private static GUIStyle wordStyle;
    private static GUIStyle wordStyleBold;
    private static GUIStyle wordStyleItalic;
    private static GUIStyle wordStyleBoldItalic;
    private static GUIStyle urlStyleBold;
    private static GUIStyle urlStyleItalic;
    private static GUIStyle urlStyleBoldItalic;
    private readonly List<HtmlText.Word> words = new List<HtmlText.Word>();
    private readonly List<HtmlText.Line> lines = new List<HtmlText.Line>();
    private readonly List<HtmlText.Link> links = new List<HtmlText.Link>();
    private string text;
    private float lineWidth;
    private float lastLineWidth;
    private readonly char[] wordBreakChars = new char[5]
    {
      ' ',
      '\n',
      '\r',
      ':',
      '-'
    };

    private static void HRefNotFoundError(object message) => UnityEngine.Debug.LogError((object) ("Link Error: " + message));

    public string rawText { get; private set; }

    public string parseError { get; private set; }

    public int parseErrorAtIndex { get; private set; }

    public static string StripTags(string textWithTags) => Regex.Replace(textWithTags, "<.*?>", string.Empty);

    public static HtmlText ParseHtml(string _rawText)
    {
      HtmlText htmlText = new HtmlText()
      {
        rawText = _rawText
      };
      string str1 = "";
      int num1 = -1;
      int num2 = 0;
      for (int index = _rawText.IndexOf("<a href=\"", StringComparison.Ordinal); index != -1 && num2 < 1000; ++num2)
      {
        int num3 = _rawText.IndexOf("</a>", index, StringComparison.Ordinal);
        int startIndex = _rawText.IndexOf(">", index, StringComparison.Ordinal) + 1;
        if (startIndex == -1 || num3 != -1 && startIndex > num3)
        {
          str1 = "Could not parse <a href=\"";
          num1 = index;
          break;
        }
        if (num3 == -1)
        {
          str1 = "Could not find </a>";
          num1 = startIndex;
          break;
        }
        string str2 = _rawText.Substring(index + "<a href=\"".Length, startIndex - (index + "<a href=\"".Length) - 2);
        string str3 = _rawText.Substring(startIndex, num3 - startIndex);
        HtmlText.Link link = new HtmlText.Link()
        {
          Url = str2,
          Label = str3,
          StartIndex = index,
          EndIndex = index + str3.Length
        };
        htmlText.links.Add(link);
        _rawText = _rawText.Substring(0, index) + str3 + _rawText.Substring(num3 + "</a>".Length);
        index = _rawText.IndexOf("<a href=\"", StringComparison.Ordinal);
      }
      htmlText.parseError = str1;
      htmlText.parseErrorAtIndex = num1;
      htmlText.text = _rawText;
      return htmlText;
    }

    public void DoGUILayout()
    {
      float width = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.label, GUILayout.Height(0.0f)).width;
      if ((double) width > 1.0)
        this.lineWidth = width;
      this.DoGUILayout(this.lineWidth);
    }

    public void DoGUILayout(float width)
    {
      if ((double) width <= 1.0)
        return;
      try
      {
        this.InitGUI(width);
        foreach (HtmlText.Line line in this.lines)
          line.DoGUILayout();
      }
      catch (Exception ex)
      {
        EditorGUILayout.HelpBox(ex.Message, MessageType.Error);
      }
      if (this.parseErrorAtIndex == -1)
        return;
      EditorGUILayout.HelpBox(this.parseError + " near:\n" + this.text.Substring(this.parseErrorAtIndex).Split('\n')[0], MessageType.Error);
    }

    public static GUIStyle urlStyle { get; private set; }

    private void InitGUI(float width)
    {
      HtmlText.InitStyles();
      if ((double) Math.Abs(this.lastLineWidth - width) <= 1.0 || Event.current.type != UnityEngine.EventType.Layout)
        return;
      this.lastLineWidth = width;
      this.BuildWords();
      this.BuildLines(width);
    }

    private static void InitStyles()
    {
      if (HtmlText.wordStyle == null)
      {
        HtmlText.wordStyle = new GUIStyle(EditorStyles.label)
        {
          padding = new RectOffset(),
          margin = new RectOffset()
        };
        HtmlText.wordStyleBold = new GUIStyle(HtmlText.wordStyle)
        {
          fontStyle = FontStyle.Bold
        };
        HtmlText.wordStyleItalic = new GUIStyle(HtmlText.wordStyle)
        {
          fontStyle = FontStyle.Italic
        };
        HtmlText.wordStyleBoldItalic = new GUIStyle(HtmlText.wordStyle)
        {
          fontStyle = FontStyle.BoldAndItalic
        };
        HtmlText.urlStyle = new GUIStyle(HtmlText.wordStyle)
        {
          normal = {
            textColor = Color.white
          }
        };
        HtmlText.urlStyleBold = new GUIStyle(HtmlText.urlStyle)
        {
          fontStyle = FontStyle.Bold
        };
        HtmlText.urlStyleItalic = new GUIStyle(HtmlText.urlStyle)
        {
          fontStyle = FontStyle.Italic
        };
        HtmlText.urlStyleBoldItalic = new GUIStyle(HtmlText.urlStyle)
        {
          fontStyle = FontStyle.BoldAndItalic
        };
      }
      HtmlText.linkColor = new Color(0.7f, 0.7f, 1f);
      if (EditorGUIUtility.isProSkin)
        return;
      HtmlText.linkColor = new Color(0.1f, 0.1f, 1f);
    }

    private HtmlText.Link GetLink(int charIndex)
    {
      foreach (HtmlText.Link link in this.links)
      {
        if (charIndex >= link.StartIndex && charIndex <= link.EndIndex)
          return link;
      }
      return (HtmlText.Link) null;
    }

    private void BuildLines(float width)
    {
      this.lines.Clear();
      if (this.words.Count == 0)
        return;
      HtmlText.Line line = new HtmlText.Line();
      HtmlText.Word word1 = this.words[0];
      float width1 = word1.Width;
      for (int index = 1; index < this.words.Count; ++index)
      {
        HtmlText.Word word2 = this.words[index];
        if (word1.EndsWith("\n"))
        {
          word1.TrimEnd('\n');
          line.AddWord(word1);
          this.lines.Add(line);
          line = new HtmlText.Line();
          word1 = word2;
          width1 = word1.Width;
        }
        else if ((double) width1 + (double) word2.Width > (double) width)
        {
          line.AddWord(word1);
          this.lines.Add(line);
          line = new HtmlText.Line();
          word1 = word2;
          width1 = word2.Width;
        }
        else
        {
          if (word2.CanCombine(word1))
          {
            word1.Append(word2);
          }
          else
          {
            line.AddWord(word1);
            word1 = word2;
          }
          width1 += word2.Width;
        }
      }
      line.AddWord(word1);
      this.lines.Add(line);
    }

    private void BuildWords()
    {
      this.words.Clear();
      int num = 0;
      int startIndex = 0;
      for (HtmlText.Word word = this.GetWord(this.text, startIndex); word != null && num++ < 1000; word = this.GetWord(this.text, startIndex))
      {
        this.words.Add(word);
        startIndex += word.Text.Length;
      }
      this.ParseWordsForStyleTags();
    }

    private void ParseWordsForStyleTags()
    {
      bool flag1 = false;
      bool flag2 = false;
      foreach (HtmlText.Word word in this.words)
      {
        bool flag3 = !string.IsNullOrEmpty(word.Url);
        word.IsUrl = flag3;
        if (word.ParseTag("<b>"))
          flag1 = true;
        if (word.ParseTag("<i>"))
          flag2 = true;
        word.IsBold = flag1;
        word.IsItalic = flag2;
        if (flag3)
        {
          if (flag1)
            word.SetStyle(flag2 ? HtmlText.urlStyleBoldItalic : HtmlText.urlStyleBold);
          else if (flag2)
            word.SetStyle(HtmlText.urlStyleItalic);
          else
            word.SetStyle(HtmlText.urlStyle);
        }
        else if (flag1)
          word.SetStyle(flag2 ? HtmlText.wordStyleBoldItalic : HtmlText.wordStyleBold);
        else if (flag2)
          word.SetStyle(HtmlText.wordStyleItalic);
        else
          word.SetStyle(HtmlText.wordStyle);
        if (word.ParseTag("</b>"))
          flag1 = false;
        if (word.ParseTag("</i>"))
          flag2 = false;
      }
    }

    private HtmlText.Word GetWord(string sourceText, int startIndex = 0)
    {
      if (string.IsNullOrEmpty(sourceText) || startIndex >= sourceText.Length - 1)
        return (HtmlText.Word) null;
      int num1 = sourceText.IndexOfAny(this.wordBreakChars, startIndex);
      int num2 = num1 != -1 ? num1 + 1 : sourceText.Length;
      string str1 = sourceText.Substring(startIndex, num2 - startIndex);
      HtmlText.Link link = this.GetLink(startIndex);
      string str2 = link != null ? link.Url : string.Empty;
      return new HtmlText.Word()
      {
        Text = str1.Replace(' ', ' '),
        Url = str2
      };
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("HtmlText: " + message));

    private class Link
    {
      public string Url { get; set; }

      public string Label { get; set; }

      public int StartIndex { get; set; }

      public int EndIndex { get; set; }

      public override string ToString() => this.Url + ": " + this.Label + " [" + (object) this.StartIndex + "," + (object) this.EndIndex + "]";
    }

    private class Line
    {
      private readonly List<HtmlText.Word> words = new List<HtmlText.Word>();

      public void AddWord(HtmlText.Word word) => this.words.Add(word);

      public void DoGUILayout()
      {
        GUILayout.BeginHorizontal();
        foreach (HtmlText.Word word in this.words)
          word.DoGUILayout();
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
      }
    }

    private class Word
    {
      public bool IsUrl;
      public bool IsBold;
      public bool IsItalic;
      private readonly GUIContent content = new GUIContent();
      private string text = "";
      private string Tooltip = "";
      private string url = "";
      private bool urlHasError;
      private GUIStyle style;
      private Vector2 size;
      private Rect drawRect;
      private Texture icon;

      public string Text
      {
        get => this.text;
        set
        {
          this.text = value;
          this.content.text = this.text;
        }
      }

      public string Url
      {
        get => this.url;
        set
        {
          this.url = value;
          this.UpdateTooltip();
        }
      }

      public float Width => this.size.x;

      internal bool CanCombine(HtmlText.Word otherWord) => this.IsBold == otherWord.IsBold && this.IsItalic == otherWord.IsItalic && this.Url == otherWord.Url;

      internal void Append(HtmlText.Word otherWord)
      {
        this.Text += otherWord.Text;
        this.size = new Vector2(this.size.x + otherWord.size.x, Mathf.Max(this.size.y, otherWord.size.y));
      }

      internal void TrimEnd(char trimChar) => this.Text = this.Text.TrimEnd(trimChar);

      internal bool EndsWith(string value) => this.Text.EndsWith(value);

      internal bool ParseTag(string tag)
      {
        if (!this.Text.Contains(tag))
          return false;
        this.Text = this.Text.Replace(tag, "");
        return true;
      }

      public void SetStyle(GUIStyle _style)
      {
        this.style = _style;
        this.size = this.style.CalcSize(new GUIContent(this.Text));
      }

      public void DoGUILayout()
      {
        UnityEngine.EventType type = Event.current.type;
        if (!this.IsUrl)
        {
          GUILayout.Label(this.Text, this.style);
        }
        else
        {
          Color color = GUI.color;
          GUI.color = this.urlHasError ? Color.red : HtmlText.linkColor;
          GUILayout.Label(this.content, this.style);
          GUI.color = color;
          if (type == UnityEngine.EventType.Repaint)
          {
            this.drawRect = GUILayoutUtility.GetLastRect();
            EditorGUIUtility.AddCursorRect(this.drawRect, MouseCursor.Link);
          }
        }
        if (!this.IsUrl || type != UnityEngine.EventType.MouseUp || !this.drawRect.Contains(Event.current.mousePosition))
          return;
        this.HandleClick();
      }

      private void UpdateTooltip()
      {
        this.icon = (Texture) null;
        this.Tooltip = "";
        this.urlHasError = false;
        if (this.url.StartsWith("http"))
          this.Tooltip = this.url;
        else if (this.url.StartsWith("W"))
        {
          int result;
          this.Tooltip = !int.TryParse(this.url.Substring(2), out result) ? "Go to Online Wiki Page (" + this.Url + ")" : "Go to Online Wiki Page: " + (object) (WikiPages) result;
        }
        else if (this.url.StartsWith("Scene:"))
          this.Tooltip = "Scene:\n" + this.url.Substring(6);
        else if (this.url.StartsWith("Action:"))
        {
          string actionName = this.url.Substring(7);
          System.Type byName = Actions.FindByName(actionName);
          this.Tooltip = byName != null ? CustomAttributeHelpers.GetTooltip(byName) : "Couldn't find action: " + actionName;
        }
        else if (this.url.StartsWith("Assets/"))
        {
          this.Tooltip = "Asset: " + this.url;
          if (AssetDatabase.LoadAssetAtPath(this.Url, typeof (UnityEngine.Object)) == (UnityEngine.Object) null)
          {
            this.urlHasError = true;
            this.Tooltip += " (Missing)";
          }
        }
        else if (this.url.StartsWith("State:"))
        {
          string stateName = this.url.Substring(6);
          this.Tooltip = "State: " + stateName;
          if (FsmEditor.SelectedFsm != null)
          {
            FsmState state = FsmEditor.SelectedFsm.GetState(stateName);
            if (state != null)
            {
              this.Tooltip = this.Tooltip + "\n" + state.Description;
            }
            else
            {
              this.urlHasError = true;
              this.Tooltip += " (missing)";
            }
          }
        }
        else if (this.url.StartsWith("Var:"))
        {
          string name = this.url.Substring(4);
          this.Tooltip = "Variable: " + name;
          if (FsmEditor.SelectedFsm != null)
          {
            NamedVariable variable = FsmEditor.SelectedFsm.Variables.GetVariable(name);
            if (variable != null)
            {
              this.Tooltip = this.Tooltip + "\nType: " + (object) variable.VariableType;
              this.Tooltip = this.Tooltip + "\n" + variable.Tooltip;
            }
            else
            {
              this.urlHasError = true;
              this.Tooltip += " (missing)";
            }
          }
        }
        else if (this.url.StartsWith("Event:"))
        {
          string eventName = this.Url.Substring(6);
          this.Tooltip = "Event: " + eventName;
          FsmEvent fsmEvent = FsmEvent.FindEvent(eventName);
          if (fsmEvent != null)
          {
            string eventTooltip = EventEditorData.GetEventTooltip(fsmEvent);
            if (!string.IsNullOrEmpty(eventTooltip))
              this.Tooltip = this.Tooltip + "\n" + eventTooltip;
          }
          else
          {
            this.urlHasError = true;
            this.Tooltip += " (missing)";
          }
        }
        this.content.image = this.icon;
        this.content.text = this.Text;
        this.content.tooltip = this.Tooltip;
      }

      private void HandleClick()
      {
        if (this.Url.StartsWith("http"))
          Application.OpenURL(this.Url);
        else if (this.Url.StartsWith("W"))
          Application.OpenURL("https://hutonggames.fogbugz.com/default.asp?" + this.Url);
        else if (this.Url.StartsWith("Assets/"))
        {
          UnityEngine.Object @object = AssetDatabase.LoadAssetAtPath(this.Url, typeof (UnityEngine.Object));
          if (!(@object != (UnityEngine.Object) null))
            return;
          EditorGUIUtility.PingObject(@object);
        }
        else if (this.url.StartsWith("Scene:"))
          EditorApplication.delayCall += (EditorApplication.CallbackFunction) (() => EditorSceneManager.OpenScene(this.url.Substring(6)));
        else if (this.url.StartsWith("Action:"))
        {
          System.Type byName = Actions.FindByName(this.url.Substring(7));
          if (byName == null)
            return;
          ActionSelector.FindAction(byName);
        }
        else if (this.Url.StartsWith("State:"))
        {
          FsmEditor.SelectStateByName(this.Url.Substring(6));
          GUIUtility.ExitGUI();
        }
        else if (this.Url.StartsWith("Var:"))
        {
          string name = this.Url.Substring(4);
          NamedVariable variable = FsmEditor.SelectedFsm.Variables.GetVariable(name);
          if (variable != null)
          {
            FsmEditor.Inspector.SetMode(HutongGames.PlayMakerEditor.InspectorMode.VariableManager);
            FsmEditor.VariablesManager.SelectVariable(variable);
          }
          else
            HtmlText.HRefNotFoundError((object) ("Could not find variable: " + name));
        }
        else
        {
          if (!this.Url.StartsWith("Event:"))
            return;
          string eventName = this.Url.Substring(6);
          FsmEvent fsmEvent = FsmEvent.FindEvent(eventName);
          if (fsmEvent != null)
          {
            if (FsmEditor.SelectedFsm.HasEvent(eventName))
            {
              FsmEditor.Inspector.SetMode(HutongGames.PlayMakerEditor.InspectorMode.EventManager);
              EditorApplication.delayCall += (EditorApplication.CallbackFunction) (() => FsmEditor.EventsManager.SelectEvent(fsmEvent));
            }
            else
            {
              FsmEditor.OpenGlobalEventsWindow();
              EditorApplication.delayCall += (EditorApplication.CallbackFunction) (() => GlobalEventsWindow.SyncSelection(fsmEvent));
            }
          }
          else
            HtmlText.HRefNotFoundError((object) ("Could not find event: " + eventName));
        }
      }
    }
  }
}
