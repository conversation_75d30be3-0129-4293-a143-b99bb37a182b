// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.EditorZoomArea
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Extensions;
using UnityEngine;

namespace HutongGames.Editor
{
  public class EditorZoomArea
  {
    private const float kEditorWindowTabHeight = 21f;
    private static Matrix4x4 _prevGuiMatrix;

    public static Rect Begin(float zoomScale, Rect screenCoordsArea)
    {
      GUI.EndGroup();
      Rect rect = screenCoordsArea.ScaleSizeBy(1f / zoomScale, screenCoordsArea.TopLeft());
      if (Mathf.Approximately(zoomScale, 1f))
        rect = screenCoordsArea;
      rect.y += 21f;
      GUI.BeginGroup(rect);
      EditorZoomArea._prevGuiMatrix = GUI.matrix;
      if ((double) zoomScale < 1.0)
      {
        Matrix4x4 matrix4x4_1 = Matrix4x4.TRS((Vector3) rect.TopLeft(), Quaternion.identity, Vector3.one);
        Matrix4x4 matrix4x4_2 = Matrix4x4.Scale(new Vector3(zoomScale, zoomScale, 1f));
        GUI.matrix = matrix4x4_1 * matrix4x4_2 * matrix4x4_1.inverse * GUI.matrix;
      }
      else
        GUI.matrix = Matrix4x4.identity;
      return rect;
    }

    public static void End()
    {
      GUI.matrix = EditorZoomArea._prevGuiMatrix;
      GUI.EndGroup();
      GUI.BeginGroup(new Rect(0.0f, 21f, (float) Screen.width, (float) Screen.height));
    }
  }
}
