// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.BaseControl
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.Editor
{
  [Localizable(false)]
  public abstract class BaseControl
  {
    public Action OnRepaint;
    private static int nextControlID;
    protected readonly EditorWindow window;
    protected string controlName;
    private bool focus;

    public string ControlName
    {
      get => this.controlName;
      set => this.controlName = value;
    }

    public bool HasFocus { get; private set; }

    public GUIStyle Style { get; set; }

    public int ID { get; set; }

    protected BaseControl(EditorWindow window)
    {
      this.window = window;
      this.controlName = this.GetType().Name + "_" + (object) BaseControl.nextControlID++;
    }

    public virtual void OnGUI(params GUILayoutOption[] options) => GUI.SetNextControlName(this.controlName);

    public virtual void OnGUI(Rect rect) => GUI.SetNextControlName(this.controlName);

    public void Focus() => this.focus = true;

    public void UpdateFocus()
    {
      if (this.focus)
      {
        GUI.FocusControl(this.controlName);
        EditorGUI.FocusTextInControl(this.controlName);
        this.focus = false;
      }
      this.HasFocus = GUI.GetNameOfFocusedControl() == this.controlName;
    }

    public void Repaint()
    {
      if ((UnityEngine.Object) this.window != (UnityEngine.Object) null)
        this.window.Repaint();
      else if (Event.current != null)
        HandleUtility.Repaint();
      if (this.OnRepaint == null)
        return;
      this.OnRepaint();
    }
  }
}
