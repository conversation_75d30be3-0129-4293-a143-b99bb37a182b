// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.StringHelper
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.ComponentModel;
using System.Globalization;
using System.Text.RegularExpressions;

namespace HutongGames.Editor
{
  [Localizable(false)]
  public static class StringHelper
  {
    public static string EnforceNamingConvention(
      string s,
      StringHelper.NamingConvention namingConvention)
    {
      switch (namingConvention)
      {
        case StringHelper.NamingConvention.None:
          return s;
        case StringHelper.NamingConvention.CamelCase:
          return s.ToCamelCase();
        case StringHelper.NamingConvention.TitleCase:
          return s.ToTitleCase();
        default:
          throw new ArgumentOutOfRangeException(nameof (namingConvention), (object) namingConvention, (string) null);
      }
    }

    public static string ToCamelCase(this string s)
    {
      string titleCase = s.ToTitleCase();
      return titleCase.Length == 0 ? "" : char.ToLower(titleCase[0]).ToString() + titleCase.Substring(1);
    }

    public static string ToTitleCase(this string s) => new CultureInfo("en-US", false).TextInfo.ToTitleCase(s.Trim()).Replace(" ", "").Replace("_", "");

    public static string TrimAndLower(this string input) => input.Trim().ToLower();

    public static string ToUrl(string str) => str.Replace(" ", "-").ToLower();

    public static string ToHyphenCase(string str) => Regex.Replace(str, "([a-z])([A-Z])", "$1-$2").ToLower();

    public enum NamingConvention
    {
      None,
      CamelCase,
      TitleCase,
    }
  }
}
