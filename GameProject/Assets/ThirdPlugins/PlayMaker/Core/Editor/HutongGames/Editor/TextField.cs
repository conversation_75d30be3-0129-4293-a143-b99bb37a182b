// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.TextField
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMakerEditor;
using System.ComponentModel;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;

namespace HutongGames.Editor
{
  [Localizable(false)]
  public class TextField : BaseControl
  {
    private GUIStyle editStyle;
    public bool useEditStyleMargins;
    private GUIStyle hintTextStyle;
    private GUIContent label = GUIContent.none;
    private string text;
    private string[] autoCompleteStrings;
    private bool showBrowseButton;
    public TextField.EditCommitedCallback EditCommited;
    public TextField.LostFocusCallback FocusLost;
    public TextField.EditingCancelledCallback EditCanceled;
    public TextField.ValidateCallback ValidateText;
    private bool hadFocus;
    private string originalText;
    private Rect browseButtonRect;

    public GUIStyle EditStyle
    {
      get => this.editStyle ?? (this.editStyle = EditorStyles.textField);
      set => this.editStyle = value;
    }

    public GUIStyle HintTextStyle
    {
      get
      {
        GUIStyle hintTextStyle = this.hintTextStyle;
        if (hintTextStyle != null)
          return hintTextStyle;
        GUIStyle guiStyle1 = new GUIStyle(GUI.skin.textField);
        guiStyle1.normal.textColor = new Color(0.5f, 0.5f, 0.5f, 0.75f);
        GUIStyle guiStyle2 = guiStyle1;
        this.hintTextStyle = guiStyle1;
        return guiStyle2;
      }
      set => this.hintTextStyle = value;
    }

    public GUIContent Label
    {
      get => this.label;
      set => this.label = value;
    }

    public string Text
    {
      get => this.text;
      set
      {
        if (this.text == value)
          return;
        this.text = value;
        this.DoValidateText();
      }
    }

    public string HintText { get; set; }

    public string[] AutoCompleteStrings
    {
      get => this.autoCompleteStrings;
      set
      {
        this.autoCompleteStrings = value;
        this.showBrowseButton = this.autoCompleteStrings != null && (uint) this.autoCompleteStrings.Length > 0U;
      }
    }

    public bool IsValid { get; private set; }

    public TextField(string label, string text = "")
      : base((EditorWindow) null)
    {
      this.Label = new GUIContent(label);
      this.Text = text;
    }

    public TextField(GUIContent label, string text = "")
      : base((EditorWindow) null)
    {
      this.Label = label;
      this.Text = text;
    }

    public TextField(EditorWindow window, string label, string text = "")
      : base(window)
    {
      this.Label = new GUIContent(label);
      this.Text = text;
    }

    public TextField(EditorWindow window, GUIContent label, string text = "")
      : base(window)
    {
      this.Label = label;
      this.Text = text;
    }

    public override void OnGUI(params GUILayoutOption[] options)
    {
      if (this.useEditStyleMargins)
        GUILayoutUtility.GetRect(0.0f, 0.0f, (float) this.editStyle.margin.top, (float) this.editStyle.margin.top);
      this.OnGUI(GUILayoutUtility.GetRect(GUIContent.none, this.EditStyle, options));
      if (!this.useEditStyleMargins)
        return;
      GUILayoutUtility.GetRect(0.0f, 0.0f, (float) this.editStyle.margin.bottom, (float) this.editStyle.margin.bottom);
    }

    public override void OnGUI(Rect rect)
    {
      base.OnGUI(rect);
      bool changed = GUI.changed;
      EditorGUI.BeginChangeCheck();
      if (this.showBrowseButton)
      {
        this.browseButtonRect.Set(rect.xMax - 20f, rect.y, 20f, rect.height);
        rect.width -= 22f;
      }
      if (this.HasFocus)
      {
        if (!this.hadFocus)
          this.originalText = this.Text;
        if (Event.current.isKey)
        {
          if (Keyboard.CommitKeyPressed())
          {
            this.CommitEdit();
            Event.current.Use();
            GUIHelpers.SafeExitGUI();
          }
          else if (Event.current.keyCode == KeyCode.Escape)
          {
            this.CancelEdit();
            Event.current.Use();
            GUIHelpers.SafeExitGUI();
          }
        }
        this.hadFocus = true;
      }
      else if (this.hadFocus)
      {
        this.hadFocus = false;
        this.LostFocus();
      }
      if (!this.HasFocus && string.IsNullOrEmpty(this.Text) && !string.IsNullOrEmpty(this.HintText))
        EditorGUI.TextField(rect, this.Label, this.HintText, this.HintTextStyle);
      else
        this.Text = EditorGUI.TextField(rect, this.Label, this.Text, this.EditStyle);
      if (this.showBrowseButton)
      {
        int index = EditorGUI.Popup(this.browseButtonRect, -1, this.AutoCompleteStrings);
        if (index != -1)
        {
          this.Text = this.AutoCompleteStrings[index];
          this.CommitEdit();
        }
      }
      if (EditorGUI.EndChangeCheck())
        this.DoValidateText();
      if (this.NeedsCommit())
        GUI.changed = changed;
      this.UpdateFocus();
    }

    private void DoBrowseButton()
    {
      int index = EditorGUILayout.Popup(-1, this.AutoCompleteStrings, GUILayout.Width(20f));
      if (index == -1)
        return;
      this.Text = this.AutoCompleteStrings[index];
      this.CommitEdit();
    }

    public void CancelTextEdit() => this.Text = this.originalText;

    public void Validate() => this.DoValidateText();

    private void DoValidateText() => this.IsValid = this.ValidateText == null || this.ValidateText(this.Text);

    public void CommitEdit()
    {
      this.Validate();
      if (this.IsValid)
        this.DoCommitEdit();
      GUI.changed = true;
      TextField.UseEvent();
    }

    private void CancelEdit()
    {
      if (this.EditCanceled != null)
        this.EditCanceled(this);
      this.CancelTextEdit();
      GUIUtility.keyboardControl = 0;
      this.hadFocus = false;
      TextField.UseEvent();
      GUIHelpers.SafeExitGUI();
    }

    private static void UseEvent()
    {
      if (Event.current == null)
        return;
      Event.current.Use();
    }

    private void LostFocus() => this.DoLostFocus();

    private void DoCommitEdit()
    {
      if (this.EditCommited != null)
        this.EditCommited(this);
      this.hadFocus = false;
    }

    private bool NeedsCommit() => this.EditCommited != null;

    private void DoLostFocus()
    {
      if (this.FocusLost == null)
        return;
      this.FocusLost(this);
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("TextField: " + message));

    public delegate void EditCommitedCallback(TextField textField);

    public delegate void LostFocusCallback(TextField textField);

    public delegate void EditingCancelledCallback(TextField textField);

    public delegate bool ValidateCallback(string text);
  }
}
