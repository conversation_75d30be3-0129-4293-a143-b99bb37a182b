// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.EditorCoroutine
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System.Collections;
using UnityEditor;

namespace HutongGames.Editor
{
  public class EditorCoroutine
  {
    private readonly IEnumerator routine;

    public static EditorCoroutine Start(IEnumerator routine)
    {
      EditorCoroutine editorCoroutine = new EditorCoroutine(routine);
      editorCoroutine.Start();
      return editorCoroutine;
    }

    private EditorCoroutine(IEnumerator routine) => this.routine = routine;

    private void Start() => EditorApplication.update += new EditorApplication.CallbackFunction(this.Update);

    public void Stop() => EditorApplication.update -= new EditorApplication.CallbackFunction(this.Update);

    private void Update()
    {
      if (this.routine.MoveNext())
        return;
      this.Stop();
    }
  }
}
