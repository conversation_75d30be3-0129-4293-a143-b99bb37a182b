// Decompiled with JetBrains decompiler
// Type: HutongGames.Editor.EditableLabel
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;

namespace HutongGames.Editor
{
  public class EditableLabel : BaseControl
  {
    private bool isEditing;
    private string originalText;
    public EditableLabel.EditCommitedCallback EditCommited;
    public Action OnCommit;
    public EditableLabel.ContextClickCallback ContextClick;
    public EditableLabel.ClickCallback Click;
    private bool startEditing;
    private bool stopEditing;

    public TextField editableTextField { get; private set; }

    public string Text { get; set; }

    public GUIStyle EditStyle
    {
      set
      {
        this.editableTextField.EditStyle = value;
        this.editableTextField.useEditStyleMargins = true;
      }
    }

    public EditableLabel(EditorWindow window, string text = "")
      : base(window)
    {
      this.Style = EditorStyles.label;
      this.Text = text;
      this.editableTextField = new TextField(window, GUIContent.none, text)
      {
        EditCommited = new TextField.EditCommitedCallback(this.CommitEdit),
        EditCanceled = new TextField.EditingCancelledCallback(this.CancelEditing),
        FocusLost = new TextField.LostFocusCallback(this.CommitEdit)
      };
      this.controlName = this.editableTextField.ControlName;
    }

    public override void OnGUI(params GUILayoutOption[] options)
    {
      GUILayout.Space(2f);
      this.OnGUI(GUILayoutUtility.GetRect(GUIContent.none, this.Style));
      GUILayout.Space(2f);
    }

    public override void OnGUI(Rect rect)
    {
      base.OnGUI(rect);
      if (this.isEditing)
      {
        this.editableTextField.OnGUI(rect);
      }
      else
      {
        GUI.Label(rect, this.Text, this.Style ?? EditorStyles.label);
        if (rect.Contains(Event.current.mousePosition))
        {
          if (Event.current.clickCount >= 2)
            this.startEditing = true;
          else if (Event.current.type == UnityEngine.EventType.MouseUp)
          {
            if (this.Click != null)
              this.Click(this);
          }
          else if (Event.current.type == UnityEngine.EventType.ContextClick)
          {
            if (this.ContextClick != null)
              this.ContextClick(this);
            Event.current.Use();
          }
        }
      }
      if (Event.current.type != UnityEngine.EventType.Repaint)
        return;
      if (this.startEditing)
        this.StartEditing();
      if (!this.stopEditing)
        return;
      this.StopEditing();
    }

    public void StartEditing()
    {
      this.startEditing = false;
      this.isEditing = true;
      this.originalText = this.Text;
      this.editableTextField.Focus();
      this.Repaint();
    }

    public void StopEditing()
    {
      this.stopEditing = false;
      this.isEditing = false;
      GUIUtility.keyboardControl = 0;
      this.Repaint();
    }

    public void CancelEditing(TextField textField)
    {
      this.Text = this.originalText;
      this.stopEditing = true;
    }

    private void CommitEdit(TextField textField)
    {
      this.Text = textField.Text;
      if (this.EditCommited != null)
        this.EditCommited(this);
      if (this.OnCommit != null)
        this.OnCommit();
      this.stopEditing = true;
      GUI.changed = true;
    }

    [Obsolete("Not used.")]
    public void Update()
    {
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("EditableLabel: " + message));

    public delegate void EditCommitedCallback(EditableLabel editableLabel);

    public delegate void ContextClickCallback(EditableLabel editableLabel);

    public delegate void ClickCallback(EditableLabel editableLabel);
  }
}
