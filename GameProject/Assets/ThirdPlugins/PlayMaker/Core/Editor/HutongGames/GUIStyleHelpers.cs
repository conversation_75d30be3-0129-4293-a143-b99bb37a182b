// Decompiled with JetBrains decompiler
// Type: HutongGames.GUIStyleHelpers
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using UnityEngine;

namespace HutongGames
{
  public static class GUIStyleHelpers
  {
    public static Vector2 GetBackgroundSize(this GUIStyle guiStyle)
    {
      Texture2D background = guiStyle.normal.background;
      return !((Object) background == (Object) null) ? new Vector2((float) background.width, (float) background.height) : Vector2.zero;
    }

    public static void Draw(this GUIStyle guiStyle, Rect rect) => guiStyle.Draw(rect, false, false, false, false);

    public static void Draw(this GUIStyle guiStyle, Rect rect, string text) => guiStyle.Draw(rect, text, false, false, false, false);

    public static void Draw(this GUIStyle guiStyle, Rect rect, Texture texture) => guiStyle.Draw(rect, texture, false, false, false, false);

    public static void Draw(this GUIStyle guiStyle, Rect rect, Color color)
    {
      if (guiStyle == null)
        return;
      Color color1 = GUI.color;
      GUI.color = color;
      guiStyle.Draw(rect, false, false, false, false);
      GUI.color = color1;
    }

    public static void Draw(
      this GUIStyle guiStyle,
      Rect rect,
      GUIContent content,
      Color backgroundColor,
      Color contentColor)
    {
      if (guiStyle == null)
        return;
      Color backgroundColor1 = GUI.backgroundColor;
      Color contentColor1 = GUI.contentColor;
      GUI.backgroundColor = backgroundColor;
      GUI.contentColor = contentColor;
      guiStyle.Draw(rect, content, false, false, false, false);
      GUI.backgroundColor = backgroundColor1;
      GUI.contentColor = contentColor1;
    }

    public static void Draw(this GUIStyle guiStyle, Rect rect, string text, Color color)
    {
      if (guiStyle == null)
        return;
      Color color1 = GUI.color;
      GUI.color = color;
      guiStyle.Draw(rect, text, false, false, false, false);
      GUI.color = color1;
    }

    public static void Draw(
      this GUIStyle guiStyle,
      Rect rect,
      string text,
      Color backgroundColor,
      Color contentColor)
    {
      if (guiStyle == null)
        return;
      Color backgroundColor1 = GUI.backgroundColor;
      Color contentColor1 = GUI.contentColor;
      GUI.backgroundColor = backgroundColor;
      GUI.contentColor = contentColor;
      guiStyle.Draw(rect, text, false, false, false, false);
      GUI.backgroundColor = backgroundColor1;
      GUI.contentColor = contentColor1;
    }

    public static void Draw(this GUIStyle guiStyle, Rect rect, Texture texture, Color color)
    {
      if (guiStyle == null)
        return;
      Color color1 = GUI.color;
      GUI.color = color;
      guiStyle.Draw(rect, texture, false, false, false, false);
      GUI.color = color1;
    }

    public static void Draw(
      this GUIStyle guiStyle,
      Rect rect,
      Color color,
      float rotation,
      Vector2 pivotPoint)
    {
      Matrix4x4 matrix = GUI.matrix;
      GUIUtility.RotateAroundPivot(rotation, pivotPoint);
      guiStyle.Draw(rect, color);
      GUI.matrix = matrix;
    }
  }
}
