// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditorData.EventEditorData
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using HutongGames.PlayMakerEditor;
using System.Collections.Generic;
using System.Diagnostics;

namespace HutongGames.PlayMakerEditorData
{
  public class EventEditorData
  {
    private static bool systemEventPropertiesInitialized;
    private static readonly Dictionary<string, string> systemEventTooltips = new Dictionary<string, string>();

    public static string GetEventTooltip(FsmEvent fsmEvent)
    {
      if (fsmEvent == null)
        return (string) null;
      EventEditorData.InitSystemEventProperties();
      string str;
      EventEditorData.systemEventTooltips.TryGetValue(fsmEvent.Name, out str);
      return str;
    }

    private static void InitSystemEventProperties()
    {
      if (EventEditorData.systemEventPropertiesInitialized || FsmEvent.CollisionEnter == null)
        return;
      EventEditorData.systemEventPropertiesInitialized = true;
      EventEditorData.systemEventTooltips.Clear();
      EventEditorData.AddLookup(FsmEvent.Disable, "Sent when the PlayMakerFSM component is disabled. Also sent when the GameObject is destroyed.");
      EventEditorData.AddLookup(FsmEvent.BecameVisible, "Sent when the renderer became visible by any camera. NOTE: This includes the Scene View camera!");
      EventEditorData.AddLookup(FsmEvent.BecameInvisible, "Sent when the renderer is no longer visible by any camera. NOTE: This includes the Scene View camera!");
      EventEditorData.AddLookup(FsmEvent.CollisionEnter, Strings.Tooltip_Collision_Enter);
      EventEditorData.AddLookup(FsmEvent.CollisionExit, Strings.Tooltip_Collision_Exit);
      EventEditorData.AddLookup(FsmEvent.CollisionStay, Strings.Tooltip_Collision_Stay);
      EventEditorData.AddLookup(FsmEvent.TriggerEnter, Strings.Tooltip_Trigger_Enter);
      EventEditorData.AddLookup(FsmEvent.TriggerExit, Strings.Tooltip_Trigger_Exit);
      EventEditorData.AddLookup(FsmEvent.TriggerStay, Strings.Tooltip_Trigger_Stay);
      EventEditorData.AddLookup(FsmEvent.ControllerColliderHit, Strings.Tooltip_Controller_Collider_Hit);
      EventEditorData.AddLookup(FsmEvent.CollisionEnter2D, Strings.Tooltip_Collision_Enter_2D);
      EventEditorData.AddLookup(FsmEvent.CollisionExit2D, Strings.Tooltip_Collision_Exit_2D);
      EventEditorData.AddLookup(FsmEvent.CollisionStay2D, Strings.Tooltip_Collision_Stay_2D);
      EventEditorData.AddLookup(FsmEvent.TriggerEnter2D, Strings.Tooltip_Trigger_Enter_2D);
      EventEditorData.AddLookup(FsmEvent.TriggerExit2D, Strings.Tooltip_Trigger_Exit_2D);
      EventEditorData.AddLookup(FsmEvent.TriggerStay2D, Strings.Tooltip_Trigger_Stay_2D);
      EventEditorData.AddLookup(FsmEvent.ParticleCollision, Strings.Tooltip_Particle_Collision);
      EventEditorData.AddLookup(FsmEvent.JointBreak, Strings.Tooltip_Joint_Break);
      EventEditorData.AddLookup(FsmEvent.JointBreak2D, Strings.Tooltip_Joint_Break_2D);
      EventEditorData.AddLookup(FsmEvent.MouseDown, Strings.Tooltip_Mouse_Down);
      EventEditorData.AddLookup(FsmEvent.MouseDrag, Strings.Tooltip_Mouse_Drag);
      EventEditorData.AddLookup(FsmEvent.MouseEnter, Strings.Tooltip_Mouse_Enter);
      EventEditorData.AddLookup(FsmEvent.MouseExit, Strings.Tooltip_Mouse_Exit);
      EventEditorData.AddLookup(FsmEvent.MouseOver, Strings.Tooltip_Mouse_Over);
      EventEditorData.AddLookup(FsmEvent.MouseUp, Strings.Tooltip_Mouse_Up);
      EventEditorData.AddLookup(FsmEvent.MouseUpAsButton, Strings.Tooltip_Mouse_Up_As_Button);
      EventEditorData.AddLookup(FsmEvent.ApplicationFocus, Strings.Tooltip_Application_Focus);
      EventEditorData.AddLookup(FsmEvent.ApplicationPause, Strings.Tooltip_Application_Pause);
      EventEditorData.AddLookup(FsmEvent.ApplicationQuit, Strings.Tooltip_Application_Quit);
      EventEditorData.AddLookup(FsmEvent.UiClick, Strings.Tooltip_UI_Click);
      EventEditorData.AddLookup(FsmEvent.UiBeginDrag, Strings.Tooltip_UI_Begin_Drag);
      EventEditorData.AddLookup(FsmEvent.UiDrag, Strings.Tooltip_UI_Drag);
      EventEditorData.AddLookup(FsmEvent.UiEndDrag, Strings.Tooltip_UI_End_Drag);
      EventEditorData.AddLookup(FsmEvent.UiDrop, Strings.Tooltip_UI_Drop);
      EventEditorData.AddLookup(FsmEvent.UiPointerUp, Strings.Tooltip_UI_Pointer_Up);
      EventEditorData.AddLookup(FsmEvent.UiPointerClick, Strings.Tooltip_UI_Pointer_Click);
      EventEditorData.AddLookup(FsmEvent.UiPointerDown, Strings.Tooltip_UI_Pointer_Down);
      EventEditorData.AddLookup(FsmEvent.UiPointerEnter, Strings.Tooltip_UI_Pointer_Enter);
      EventEditorData.AddLookup(FsmEvent.UiPointerExit, Strings.Tooltip_UI_Pointer_Exit);
      EventEditorData.AddLookup(FsmEvent.UiBoolValueChanged, Strings.Tooltip_UI_Bool_Value_Changed);
      EventEditorData.AddLookup(FsmEvent.UiFloatValueChanged, Strings.Tooltip_UI_Float_Value_Changed);
      EventEditorData.AddLookup(FsmEvent.UiIntValueChanged, Strings.Tooltip_UI_Int_Value_Changed);
      EventEditorData.AddLookup(FsmEvent.UiVector2ValueChanged, Strings.Tooltip_UI_Vector2_Value_Changed);
      EventEditorData.AddLookup(FsmEvent.UiEndEdit, Strings.Tooltip_UI_End_Edit);
    }

    private static void AddLookup(FsmEvent fsmEvent, string tooltip)
    {
      if (fsmEvent == null)
        return;
      EventEditorData.systemEventTooltips.Add(fsmEvent.Name, tooltip);
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("EventEditorData: " + message));
  }
}
