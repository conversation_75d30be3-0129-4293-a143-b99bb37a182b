// Decompiled with JetBrains decompiler
// Type: HutongGames.EnumData
// Assembly: PlayMakerEditor, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;

namespace HutongGames
{
  internal struct EnumData
  {
    public Enum[] values;
    public int[] flagValues;
    public string[] displayNames;
    public string[] tooltip;
    public bool flags;
    public Type underlyingType;
    public bool unsigned;
    public bool serializable;
  }
}
