// Decompiled with JetBrains decompiler
// Type: HutongGames.Extensions.ListExtensions
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System.Collections.Generic;
using System.Linq;

namespace HutongGames.Extensions
{
  public static class ListExtensions
  {
    public static void Resize<T>(this List<T> list, int size, T element = default)
    {
      int count = list.Count;
      if (size < count)
      {
        list.RemoveRange(size, count - size);
      }
      else
      {
        if (size <= count)
          return;
        if (size > list.Capacity)
          list.Capacity = size;
        list.AddRange(Enumerable.Repeat<T>(element, size - count));
      }
    }
  }
}
