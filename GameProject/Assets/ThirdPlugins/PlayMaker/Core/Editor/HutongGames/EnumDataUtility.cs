// Decompiled with JetBrains decompiler
// Type: HutongGames.EnumDataUtility
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames
{
  internal static class EnumDataUtility
  {
    private static readonly Dictionary<System.Type, EnumData> s_NonObsoleteEnumData = new Dictionary<System.Type, EnumData>();
    private static readonly Dictionary<System.Type, EnumData> s_EnumData = new Dictionary<System.Type, EnumData>();

    internal static EnumData GetCachedEnumData(System.Type enumType, bool excludeObsolete = true)
    {
      EnumData enumData;
      if (excludeObsolete && EnumDataUtility.s_NonObsoleteEnumData.TryGetValue(enumType, out enumData) || !excludeObsolete && EnumDataUtility.s_EnumData.TryGetValue(enumType, out enumData))
        return enumData;
      enumData = new EnumData()
      {
        underlyingType = Enum.GetUnderlyingType(enumType)
      };
      enumData.unsigned = enumData.underlyingType == typeof (byte) || enumData.underlyingType == typeof (ushort) || enumData.underlyingType == typeof (uint) || enumData.underlyingType == typeof (ulong);
      FieldInfo[] fields = enumType.GetFields(BindingFlags.Static | BindingFlags.Public);
      List<FieldInfo> source = new List<FieldInfo>();
      int length1 = fields.Length;
      for (int index = 0; index < length1; ++index)
      {
        if (EnumDataUtility.CheckObsoleteAddition(fields[index], excludeObsolete))
          source.Add(fields[index]);
      }
      if (!source.Any<FieldInfo>())
      {
        string[] strArray = new string[1]{ "" };
        Enum[] enumArray = new Enum[0];
        int[] numArray = new int[1];
        enumData.values = enumArray;
        enumData.flagValues = numArray;
        enumData.displayNames = strArray;
        enumData.tooltip = strArray;
        enumData.flags = true;
        enumData.serializable = true;
        return enumData;
      }
      source.OrderBy<FieldInfo, int>((Func<FieldInfo, int>) (f => f.MetadataToken));
      enumData.displayNames = source.Select<FieldInfo, string>((Func<FieldInfo, string>) (f => EnumDataUtility.EnumNameFromEnumField(f))).ToArray<string>();
      if (((IEnumerable<string>) enumData.displayNames).Distinct<string>().Count<string>() != enumData.displayNames.Length)
        Debug.LogWarning((object) string.Format("Enum {0} has multiple entries with the same display name, this prevents selection in EnumPopup.", (object) enumType.Name));
      enumData.tooltip = source.Select<FieldInfo, string>((Func<FieldInfo, string>) (f => EnumDataUtility.EnumTooltipFromEnumField(f))).ToArray<string>();
      enumData.values = source.Select<FieldInfo, Enum>((Func<FieldInfo, Enum>) (f => (Enum) f.GetValue((object) null))).ToArray<Enum>();
      enumData.flagValues = enumData.unsigned ? ((IEnumerable<Enum>) enumData.values).Select<Enum, int>((Func<Enum, int>) (v => (int) Convert.ToUInt64((object) v))).ToArray<int>() : ((IEnumerable<Enum>) enumData.values).Select<Enum, int>((Func<Enum, int>) (v => (int) Convert.ToInt64((object) v))).ToArray<int>();
      if (enumData.underlyingType == typeof (ushort))
      {
        int index = 0;
        for (int length2 = enumData.flagValues.Length; index < length2; ++index)
        {
          if (enumData.flagValues[index] == (int) ushort.MaxValue)
            enumData.flagValues[index] = -1;
        }
      }
      else if (enumData.underlyingType == typeof (byte))
      {
        int index = 0;
        for (int length2 = enumData.flagValues.Length; index < length2; ++index)
        {
          if (enumData.flagValues[index] == (int) byte.MaxValue)
            enumData.flagValues[index] = -1;
        }
      }
      enumData.flags = enumType.IsDefined(typeof (FlagsAttribute), false);
      enumData.serializable = enumData.underlyingType != typeof (long) && enumData.underlyingType != typeof (ulong);
      if (excludeObsolete)
        EnumDataUtility.s_NonObsoleteEnumData[enumType] = enumData;
      else
        EnumDataUtility.s_EnumData[enumType] = enumData;
      return enumData;
    }

    internal static int EnumFlagsToInt(EnumData enumData, Enum enumValue)
    {
      if (!enumData.unsigned)
        return Convert.ToInt32((object) enumValue);
      if (enumData.underlyingType == typeof (uint))
        return (int) Convert.ToUInt32((object) enumValue);
      if (enumData.underlyingType == typeof (ushort))
      {
        ushort uint16 = Convert.ToUInt16((object) enumValue);
        return uint16 != ushort.MaxValue ? (int) uint16 : -1;
      }
      byte num = Convert.ToByte((object) enumValue);
      return num != byte.MaxValue ? (int) num : -1;
    }

    internal static Enum IntToEnumFlags(System.Type enumType, int value)
    {
      EnumData cachedEnumData = EnumDataUtility.GetCachedEnumData(enumType);
      if (!cachedEnumData.unsigned)
        return Enum.Parse(enumType, value.ToString()) as Enum;
      if (cachedEnumData.underlyingType == typeof (uint))
      {
        uint num = (uint) value;
        return Enum.Parse(enumType, num.ToString()) as Enum;
      }
      if (cachedEnumData.underlyingType == typeof (ushort))
      {
        ushort num = (ushort) value;
        return Enum.Parse(enumType, num.ToString()) as Enum;
      }
      byte num1 = (byte) value;
      return Enum.Parse(enumType, num1.ToString()) as Enum;
    }

    private static bool CheckObsoleteAddition(FieldInfo field, bool excludeObsolete)
    {
      object[] customAttributes = field.GetCustomAttributes(typeof (ObsoleteAttribute), false);
      if (customAttributes.Length == 0)
        return true;
      return !excludeObsolete && !((ObsoleteAttribute) ((IEnumerable<object>) customAttributes).First<object>()).IsError;
    }

    private static string EnumTooltipFromEnumField(FieldInfo field)
    {
      object[] customAttributes = field.GetCustomAttributes(typeof (TooltipAttribute), false);
      return customAttributes.Length != 0 ? ((TooltipAttribute) ((IEnumerable<object>) customAttributes).First<object>()).tooltip : string.Empty;
    }

    private static string EnumNameFromEnumField(FieldInfo field)
    {
      object[] customAttributes = field.GetCustomAttributes(typeof (InspectorNameAttribute), false);
      if (customAttributes.Length != 0)
        return ((InspectorNameAttribute) ((IEnumerable<object>) customAttributes).First<object>()).displayName;
      return field.IsDefined(typeof (ObsoleteAttribute), false) ? string.Format("{0} (Obsolete)", (object) ObjectNames.NicifyVariableName(field.Name)) : ObjectNames.NicifyVariableName(field.Name);
    }
  }
}
