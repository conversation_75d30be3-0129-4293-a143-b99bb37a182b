// Decompiled with JetBrains decompiler
// Type: HutongGames.SearchToolbar
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMakerEditor;
using System;
using UnityEditor;

namespace HutongGames
{
  public class SearchToolbar
  {
    public Action SearchChanged;
    public Action SettingsButtonClicked;
    public bool ShowSettingsButton = true;
    private readonly EditorWindow window;
    private SearchBox searchBox;

    public SearchBox SearchBox => this.searchBox;

    public string SearchString => this.searchBox.SearchString;

    public SearchToolbar(EditorWindow _window)
    {
      this.window = _window;
      this.InitControls();
    }

    public void SetSearchModes(string[] searchModes) => this.searchBox.SearchModes = searchModes;

    private void InitControls()
    {
      if (this.searchBox != null)
        return;
      this.searchBox = new SearchBox(this.window);
      this.searchBox.SearchChanged += (EditorApplication.CallbackFunction) (() =>
      {
        if (this.SearchChanged == null)
          return;
        this.SearchChanged();
      });
      this.searchBox.Focus();
    }

    public void OnGUI()
    {
      this.InitControls();
      EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
      this.searchBox.OnGUI();
      if (this.ShowSettingsButton && FsmEditorGUILayout.ToolbarSettingsButton() && this.SettingsButtonClicked != null)
        this.SettingsButtonClicked();
      EditorGUILayout.EndHorizontal();
    }
  }
}
