// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Menus
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;

using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class Menus
  {
    private static EditorWindow tempWindow;
    private static Rect tempRect;
    private static FsmTransition targetTransition;

    public static GUIContent FormatItem(string itemPath)
    {
      itemPath = itemPath.Replace(" /", "/");
      itemPath = itemPath.TrimStart('/');
      return new GUIContent(itemPath);
    }

    public static GenericMenu AddColorMenu(
      GenericMenu menu,
      string root,
      int selectedColorIndex,
      GenericMenu.MenuFunction2 function)
    {
      root = Menus.MakeMenuRoot(root);
      for (int index = 0; index < 8; ++index)
      {
        string colorName = PlayMakerPrefs.ColorNames[index];
        menu.AddItem(new GUIContent(root + colorName), selectedColorIndex == index, function, (object) index);
      }
      menu.AddSeparator(root);
      for (int index = 8; index < 24; ++index)
      {
        string colorName = PlayMakerPrefs.ColorNames[index];
        if (!string.IsNullOrEmpty(colorName))
          menu.AddItem(new GUIContent(root + colorName), selectedColorIndex == index, function, (object) index);
      }
      return menu;
    }

    public static string MakeMenuRoot(string menuItem)
    {
      if (string.IsNullOrEmpty(menuItem))
        return string.Empty;
      if (menuItem[menuItem.Length - 1] != '/')
        menuItem += "/";
      return menuItem;
    }

    public static void AddCategorizedEventMenuItem(
      ref GenericMenu menu,
      string category,
      FsmEvent fsmEvent)
    {
      if (category != "")
        category += "/";
      menu.AddItem(new GUIContent(category + fsmEvent.Name), FsmEditor.SelectedTransition.FsmEvent == fsmEvent, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectGlobalEvent), (object) fsmEvent);
    }

    public static void ShowStateSelectionContextMenu() => FsmEditorGUILayout.GenerateStateSelectionMenu(FsmEditor.SelectedFsm).ShowAsContext();

    public static void ShowStateSelectionMenu(Rect position) => FsmEditorGUILayout.GenerateStateSelectionMenu(FsmEditor.SelectedFsm).DropDown(position);

    public static void ShowTransitionSelectionMenu() => Menus.ShowTransitionSelectionMenu(FsmEditor.SelectedState);

    public static void ShowTransitionSelectionMenu(FsmState state)
    {
      if (state == null)
        return;
      GenericMenu genericMenu = new GenericMenu();
      foreach (FsmTransition transition in state.Transitions)
        genericMenu.AddItem(new GUIContent(transition.EventName), FsmEditor.SelectedTransition == transition, new GenericMenu.MenuFunction2(EditorCommands.SelectTransition), (object) transition);
      genericMenu.ShowAsContext();
    }

    public static void ShowTransitionEventSelectionMenu(
      EditorWindow window,
      Fsm fsm,
      FsmTransition transition,
      Rect position)
    {
      Menus.tempWindow = window;
      GenericMenu menu = new GenericMenu();
      Menus.AddTransitionEventSelectionMenuItems(menu, "", fsm, transition, position);
      menu.DropDown(position);
    }

    public static void AddTransitionEventSelectionMenuItems(
      GenericMenu menu,
      string subMenu,
      Fsm fsm,
      FsmTransition transition,
      Rect position)
    {
      if (menu == null || fsm == null || transition == null)
        return;
      subMenu = Menus.MakeMenuRoot(subMenu);
      Menus.tempRect = position;
      Menus.targetTransition = transition;
      menu.AddItem(new GUIContent(Strings.Label_None), transition.FsmEvent == null, new GenericMenu.MenuFunction(Menus.DoClearTransitionEvent));
      Menus.AddFsmEventsMenu(menu, subMenu, fsm, transition.FsmEvent, new GenericMenu.MenuFunction2(Menus.DoSetTransitionEvent));
      menu.AddSeparator(subMenu);
      Menus.AddCustomEventsMenu(menu, subMenu, transition.FsmEvent, new GenericMenu.MenuFunction2(Menus.DoSetTransitionEvent));
      Menus.AddSystemEventsMenu(menu, subMenu, transition.FsmEvent, new GenericMenu.MenuFunction2(Menus.DoSetTransitionEvent));
      menu.AddSeparator(subMenu);
      menu.AddItem(new GUIContent(subMenu + Strings.Menu_New_Event), false, new GenericMenu.MenuFunction2(Menus.OpenNewEventWindow), (object) transition);
    }

    public static GenericMenu AddEventListMenu(
      GenericMenu menu,
      string subMenu,
      IEnumerable<FsmEvent> eventList,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      subMenu = Menus.MakeMenuRoot(subMenu);
      string str = selectedEvent != null ? selectedEvent.Name : "";
      if (eventList != null)
      {
        foreach (FsmEvent fsmEvent in eventList)
          menu.AddItem(Menus.FormatItem(subMenu + fsmEvent.Name), str == fsmEvent.Name, menuFunction, (object) fsmEvent);
      }
      return menu;
    }

    public static GenericMenu AddFsmEventsMenu(
      GenericMenu menu,
      string subMenu,
      Fsm fsm,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      subMenu = Menus.MakeMenuRoot(subMenu);
      menu = Menus.AddEventListMenu(menu, subMenu, (IEnumerable<FsmEvent>) new List<FsmEvent>(fsm != null ? (IEnumerable<FsmEvent>) fsm.Events : (IEnumerable<FsmEvent>) new FsmEvent[0]), selectedEvent, menuFunction);
      return menu;
    }

    public static GenericMenu AddCustomEventsMenu(
      GenericMenu menu,
      string subMenu,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      subMenu = Menus.MakeMenuRoot(subMenu);
      string str1 = selectedEvent != null ? selectedEvent.Name : "";
      string str2 = Menus.MakeMenuRoot(subMenu + Strings.Menu_GraphView_CustomEvents);
      foreach (FsmEvent fsmEvent in FsmEvent.EventList.OrderBy<FsmEvent, string>((Func<FsmEvent, string>) (o => o.Name)).ToList<FsmEvent>())
      {
        if (!fsmEvent.IsSystemEvent)
          menu.AddItem(Menus.FormatItem(str2 + fsmEvent.Name), str1 == fsmEvent.Name, menuFunction, (object) fsmEvent);
      }
      return menu;
    }

    public static GenericMenu AddSystemEventsMenu(
      GenericMenu menu,
      string subMenu,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      subMenu = Menus.MakeMenuRoot(subMenu);
      string str = selectedEvent != null ? selectedEvent.Name : "";
      foreach (FsmEvent fsmEvent in FsmEvent.EventList.OrderBy<FsmEvent, string>((Func<FsmEvent, string>) (o => o.Name)).ToList<FsmEvent>())
      {
        if (fsmEvent.IsSystemEvent && fsmEvent != FsmEvent.Finished && (FsmEditorSettings.UseLegacyNetworking || !fsmEvent.IsLegacyNetworkEvent))
          menu.AddItem(new GUIContent(subMenu + fsmEvent.Path + fsmEvent.Name), str == fsmEvent.Name, menuFunction, (object) fsmEvent);
      }
      return menu;
    }

    private static void OpenNewEventWindow(object userdata)
    {
      if (!(userdata is FsmTransition))
        return;
      Menus.tempRect.x += Menus.tempWindow.position.x;
      Menus.tempRect.y += Menus.tempWindow.position.y;
      NewEventWindow.CreateDropdown(Strings.Menu_New_Event, Menus.tempRect, "").EditCommited += new TextField.EditCommitedCallback(Menus.DoSetTransitionNewEvent);
    }

    private static void DoClearTransitionEvent() => EditorCommands.SetTransitionEvent(Menus.targetTransition, (FsmEvent) null);

    private static void DoSetTransitionEvent(object userdata)
    {
      FsmEvent fsmEvent = userdata as FsmEvent;
      EditorCommands.SetTransitionEvent(Menus.targetTransition, fsmEvent);
    }

    private static void DoSetTransitionNewEvent(TextField textField)
    {
      if (string.IsNullOrEmpty(textField.Text))
        return;
      EditorCommands.SetTransitionNewEvent(Menus.targetTransition, textField.Text);
    }

    public static GenericMenu AddVariableUsedByItems(
      GenericMenu menu,
      NamedVariable variable,
      string rootItem = "")
    {
      List<FsmInfo> variableUsageList = FsmSearch.GetVariableUsageList(FsmEditor.SelectedFsm, variable);
      if (variableUsageList.Count == 0)
      {
        menu.AddDisabledItem(new GUIContent(Strings.Menu_Unused_Variable));
      }
      else
      {
        foreach (FsmInfo fsmInfo in variableUsageList)
        {
          if (fsmInfo.IsValid())
            menu.AddItem(new GUIContent(rootItem + fsmInfo.AsMenuItemShort()), FsmEditor.SelectedState == fsmInfo.state, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
        }
      }
      return menu;
    }

    public static GenericMenu TemplateUsageInFsmMenu(
      Fsm fsm,
      FsmTemplate template,
      GenericMenu appendToMenu = null)
    {
      GenericMenu genericMenu = appendToMenu ?? new GenericMenu();
      if (fsm == null || (UnityEngine.Object) template == (UnityEngine.Object) null)
        return genericMenu;
      List<FsmInfo> templateUsageList = FsmSearch.GetSearch(fsm).GetTemplateUsageList(template);
      if (templateUsageList.Count > 0)
      {
        foreach (FsmInfo fsmInfo in templateUsageList)
        {
          if (!string.IsNullOrEmpty(fsmInfo.stateName))
            genericMenu.AddItem(new GUIContent("Find.../" + fsmInfo.AsMenuItemShort()), false, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
        }
      }
      else
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Template_not_used_in_FSM));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Select_Template), false, new GenericMenu.MenuFunction2(Menus.SelectFsm), (object) template.fsm);
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Find_in_Template_Browser), false, new GenericMenu.MenuFunction2(Menus.FindTemplateInBrowser), (object) template);
      return genericMenu;
    }

    public static void ShowAddFsmContextMenu()
    {
      GenericMenu menu = new GenericMenu();
      Menus.AddNewFsmMenuItems(menu);
      menu.ShowAsContext();
    }

    public static void ShowFsmOwnerSelectionMenu() => Menus.GetFsmOwnerSelectionMenu().ShowAsContext();

    public static void ShowFsmOwnerSelectionDropdownMenu(Rect buttonArea) => Menus.GetFsmOwnerSelectionMenu().DropDown(buttonArea);

    private static GenericMenu GetFsmOwnerSelectionMenu()
    {
      FsmEditor.RebuildFsmList();
      List<GameObject> gameObjectList = new List<GameObject>();
      List<Fsm> sortedFsmList = FsmEditor.SortedFsmList;
      foreach (Fsm fsm in sortedFsmList)
      {
        if (!gameObjectList.Contains(fsm.GameObject))
          gameObjectList.Add(fsm.GameObject);
      }
      UnityEditor.SceneManagement.PrefabStage currentPrefabStage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
      bool flag = currentPrefabStage != null;
      GenericMenu menu = new GenericMenu();
      if (gameObjectList.Count > 500)
      {
        menu.AddDisabledItem(new GUIContent(Strings.Hint_Select_a_GameObject));
      }
      else
      {
        Dictionary<GameObject, string> source = new Dictionary<GameObject, string>();
        List<string> labels = new List<string>();
        string seperator = sortedFsmList.Count > 50 ? "/" : "> ";
        foreach (GameObject gameObject in gameObjectList)
        {
          if (!((UnityEngine.Object) gameObject == (UnityEngine.Object) null) && (!flag || currentPrefabStage.IsPartOfPrefabContents(gameObject)))
          {
            string str = Labels.GenerateUniqueLabel(labels, FsmUtility.GetFullPath(gameObject, seperator));
            if (FsmPrefabs.IsPrefab((UnityEngine.Object) gameObject))
              str = "ΩPrefabs/" + str;
            labels.Add(str);
            source.Add(gameObject, str);
          }
        }
        foreach (KeyValuePair<GameObject, string> keyValuePair in (IEnumerable<KeyValuePair<GameObject, string>>) source.OrderBy<KeyValuePair<GameObject, string>, string>((Func<KeyValuePair<GameObject, string>, string>) (pair => pair.Value)))
        {
          GameObject key = keyValuePair.Key;
          string text = keyValuePair.Value.Replace("Ω", "");
          menu.AddItem(new GUIContent(text), (UnityEngine.Object) FsmEditor.Selection.ActiveFsmGameObject == (UnityEngine.Object) key, new GenericMenu.MenuFunction2(Menus.DoSelectGameObject), (object) key);
        }
      }
      Menus.AddTemplateItems(menu, "Templates", new GenericMenu.MenuFunction2(Menus.DoSelectTemplate));
      return menu;
    }

    private static void DoSelectGameObject(object userdata)
    {
      GameObject go = (GameObject) userdata;
      int num = FsmEditorSettings.LockGraphView ? 1 : 0;
      FsmEditorSettings.LockGraphView = false;
      if (EditorUtility.IsPersistent((UnityEngine.Object) go))
        FsmEditor.SelectPrefab(go);
      else
        Selection.activeGameObject = go;
      FsmEditor.Instance.OnSelectionChange();
      FsmEditorSettings.LockGraphView = num != 0;
    }

    private static void DoSelectTemplate(object userdata)
    {
      FsmTemplate fsmTemplate = userdata as FsmTemplate;
      if ((UnityEngine.Object) fsmTemplate == (UnityEngine.Object) null)
        return;
      FsmEditor.SelectFsm(fsmTemplate.fsm);
    }

    public static void ShowGameObjectFsmSelectionMenu() => Menus.GetGameObjectFsmSelectionMenu().ShowAsContext();

    public static void ShowGameObjectFsmSelectionDropdownMenu(Rect buttonArea) => Menus.GetGameObjectFsmSelectionMenu().DropDown(buttonArea);

    public static GenericMenu GetGameObjectFsmSelectionMenu()
    {
      GenericMenu menu = new GenericMenu();
      if ((UnityEngine.Object) FsmEditor.Selection.ActiveFsmGameObject != (UnityEngine.Object) null)
      {
        List<Fsm> fsmList = new List<Fsm>();
        foreach (Fsm sortedFsm in FsmEditor.SortedFsmList)
        {
          if ((UnityEngine.Object) sortedFsm.GameObject == (UnityEngine.Object) FsmEditor.Selection.ActiveFsmGameObject)
            fsmList.Add(sortedFsm);
        }
        if (fsmList.Count == 0)
        {
          menu.AddDisabledItem(new GUIContent(Strings.Label_None));
        }
        else
        {
          foreach (Fsm fsm in fsmList)
          {
            string fsmLabel = Labels.GetFsmLabel(fsm);
            menu.AddItem(new GUIContent(fsmLabel), FsmEditor.SelectedFsm == fsm, new GenericMenu.MenuFunction2(FsmEditor.SelectFsm), (object) fsm);
          }
        }
        menu.AddSeparator(string.Empty);
        if (FsmEditor.SelectedFsm != null)
        {
          List<FsmTemplate> usedTemplates = FsmSearch.GetSearch(FsmEditor.SelectedFsm).GetUsedTemplates();
          if (usedTemplates.Count > 0)
          {
            string str = "Templates Used in Selected FSM/";
            foreach (FsmTemplate fsmTemplate in usedTemplates)
              menu.AddItem(Menus.FormatItem(str + fsmTemplate.Category + "/" + fsmTemplate.name), false, new GenericMenu.MenuFunction2(FsmEditor.SelectFsm), (object) fsmTemplate.fsm);
            menu.AddSeparator(string.Empty);
          }
        }
      }
      if ((UnityEngine.Object) FsmEditor.SelectedTemplate == (UnityEngine.Object) null)
      {
        if ((UnityEngine.Object) Selection.activeGameObject != (UnityEngine.Object) null)
          Menus.AddNewFsmMenuItems(menu, "Add FSM/");
        else
          menu.AddDisabledItem(new GUIContent("Add FSM"));
      }
      else
        menu = Menus.TemplateUsageMenu(FsmEditor.SelectedTemplate);
      return menu;
    }

    public static void AddNewFsmMenuItems(GenericMenu menu, string subMenu = "")
    {
      if ((UnityEngine.Object) Selection.activeGameObject == (UnityEngine.Object) null || (UnityEngine.Object) FsmEditor.SelectedTemplate != (UnityEngine.Object) null)
        return;
      subMenu = Menus.MakeMenuRoot(subMenu);
      menu.AddItem(new GUIContent(subMenu + "New FSM"), false, new GenericMenu.MenuFunction(FsmBuilder.AddFsmToSelected));
      if (FsmEditor.Clipboard.CanPaste())
        menu.AddItem(new GUIContent(subMenu + "Paste FSM"), false, new GenericMenu.MenuFunction(EditorCommands.PasteFsm));
      else
        menu.AddDisabledItem(new GUIContent(subMenu + "Paste FSM"));
      Menus.AddTemplateItems(menu, subMenu + Strings.Command_Paste_Template + "/", new GenericMenu.MenuFunction2(EditorCommands.AddTemplateToSelected));
      Menus.AddTemplateItems(menu, subMenu + Strings.Label_Use_Template + "/", new GenericMenu.MenuFunction2(EditorCommands.AddFsmAndUseTemplateWithSelected));
    }

    private static void SelectFsm(object userdata)
    {
      if (!(userdata is Fsm fsm))
        return;
      if (!FsmEditor.IsOpen)
        FsmEditor.Open(fsm);
      else
        FsmEditor.SelectFsm(fsm);
    }

    public static GenericMenu TemplateUsageMenu(
      FsmTemplate template,
      bool findInBrowser = true,
      GenericMenu appendToMenu = null)
    {
      GenericMenu genericMenu = appendToMenu ?? new GenericMenu();
      List<FsmInfo> templateAllUsageList = FsmSearch.GetTemplateAllUsageList(template);
      if (templateAllUsageList.Count > 0)
      {
        foreach (FsmInfo fsmInfo in templateAllUsageList)
        {
          string str = "Used In Scene/";
          if ((bool) (UnityEngine.Object) fsmInfo.fsm.UsedInTemplate)
            str = "Used In Templates/";
          else if (FsmPrefabs.IsPrefab(fsmInfo.fsm))
            str = "Used In Prefabs/";
          genericMenu.AddItem(new GUIContent(str + fsmInfo.AsMenuItem(true, false)), false, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
        }
      }
      else
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Template_Not_Used));
      if (findInBrowser)
      {
        genericMenu.AddSeparator("");
        genericMenu.AddItem(new GUIContent(Strings.Menu_Find_in_Template_Browser), false, new GenericMenu.MenuFunction2(Menus.FindTemplateInBrowser), (object) template);
      }
      return genericMenu;
    }

    public static GenericMenu AddTemplateItems(
      GenericMenu menu,
      string subMenu,
      GenericMenu.MenuFunction2 function)
    {
      subMenu = Menus.MakeMenuRoot(subMenu);
      subMenu += "{0}/{1}";
      Templates.InitList();
      foreach (string category in Templates.Categories)
      {
        foreach (FsmTemplate fsmTemplate in Templates.GetTemplatesInCategory(category))
          menu.AddItem(new GUIContent(string.Format(subMenu, (object) category, (object) fsmTemplate.name)), false, function, (object) fsmTemplate);
      }
      if (Templates.List.Count == 0)
        menu.AddDisabledItem(new GUIContent(string.Format(subMenu, (object) Strings.Menu_None, (object) Strings.Menu_None)));
      return menu;
    }

    private static void FindTemplateInBrowser(object userdata)
    {
      FsmTemplate template = userdata as FsmTemplate;
      if (!((UnityEngine.Object) template != (UnityEngine.Object) null))
        return;
      FsmEditor.OpenFsmTemplateWindow();
      FsmTemplateSelector.FindTemplateInBrowser(template);
    }

    public static void SelectFsmInfo(object userdata)
    {
      if (!(userdata is FsmInfo fsmInfo))
        return;
      fsmInfo.Select();
    }

    public static GenericMenu GenerateEventSelectionMenu(
      GenericMenu.MenuFunction2 onSelect)
    {
      GenericMenu genericMenu = new GenericMenu();
      foreach (FsmEvent fsmEvent in FsmEvent.EventList)
        genericMenu.AddItem(new GUIContent(fsmEvent.Name), false, onSelect, (object) fsmEvent);
      return genericMenu;
    }

    public static void DoEventSelectionContextMenu(GenericMenu.MenuFunction2 onSelect) => Menus.GenerateEventSelectionMenu(onSelect).ShowAsContext();

    public static void DoEventUsageContextMenu(Fsm fsm, FsmEvent fsmEvent) => Menus.GenerateEventUsageContextMenu(fsm, fsmEvent).ShowAsContext();

    public static GenericMenu GenerateEventUsageContextMenu(
      Fsm fsmOwner,
      FsmEvent fsmEvent)
    {
      FsmSearch.RefreshAll();
      GenericMenu genericMenu = new GenericMenu();
      if (fsmOwner == null || fsmEvent == null)
        return genericMenu;
      if (FsmSearch.GetEventUseCount(fsmOwner, fsmEvent.Name) == 0)
      {
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Unused_Event));
        return genericMenu;
      }
      List<FsmInfo> eventUsageList = FsmSearch.GetEventUsageList(fsmOwner, fsmEvent.Name);
      string str1 = Menus.MakeMenuRoot(Strings.Menu_Received_By);
      foreach (FsmInfo fsmInfo in eventUsageList)
      {
        FsmState state = fsmOwner.GetState(fsmInfo.stateName);
        if (state != null)
          genericMenu.AddItem(new GUIContent(str1 + state.Name), FsmEditor.SelectedState == state, new GenericMenu.MenuFunction2(EditorCommands.SelectState), (object) state);
      }
      if (eventUsageList.Count == 0)
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Received_By));
      List<FsmInfo> fsmInfoList = new List<FsmInfo>();
      foreach (Fsm fsm in FsmEditor.FsmList)
      {
        foreach (FsmInfo eventSentBy in FsmSearch.GetSearch(fsm).GetEventSentByList(fsmEvent))
        {
          if (fsm == FsmEditor.SelectedFsm)
            fsmInfoList.Add(eventSentBy);
          else if (eventSentBy.eventTarget != null && eventSentBy.eventTarget.target != FsmEventTarget.EventTarget.Self)
            fsmInfoList.Add(eventSentBy);
        }
      }
      if (fsmInfoList.Count == 0)
      {
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Sent_By));
      }
      else
      {
        string str2 = Menus.MakeMenuRoot(Strings.Menu_GraphView_Sent_By);
        foreach (FsmInfo fsmInfo in fsmInfoList)
        {
          if (fsmInfo.fsm == FsmEditor.SelectedFsm)
          {
            Labels.UpdateAutoName(fsmInfo.action);
            genericMenu.AddItem(new GUIContent(str2 + fsmInfo.state.Name + " : " + Labels.GetActionLabel(fsmInfo.action)), false, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
          }
        }
        List<string> labels = new List<string>();
        foreach (FsmInfo fsmInfo in fsmInfoList)
        {
          if (fsmInfo.fsm != FsmEditor.SelectedFsm)
          {
            string label = str2 + Labels.GetFullStateLabel(fsmInfo.state) + " : " + Labels.GetActionLabel(fsmInfo.action);
            string uniqueLabel = Labels.GenerateUniqueLabel(labels, label);
            labels.Add(uniqueLabel);
          }
          foreach (string text in labels)
            genericMenu.AddItem(new GUIContent(text), false, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
        }
      }
      return genericMenu;
    }
  }
}
