// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.EditNamedVariable
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class EditNamedVariable
  {
    public static NamedVariable Copy(this INamedVariable variable)
    {
      System.Type type = variable.GetType();
      if (type == typeof (FsmMaterial))
        return (NamedVariable) new FsmMaterial((FsmObject) variable);
      if (type == typeof (FsmTexture))
        return (NamedVariable) new FsmTexture((FsmObject) variable);
      if (type == typeof (FsmFloat))
        return (NamedVariable) new FsmFloat((FsmFloat) variable);
      if (type == typeof (FsmInt))
        return (NamedVariable) new FsmInt((FsmInt) variable);
      if (type == typeof (FsmBool))
        return (NamedVariable) new FsmBool((FsmBool) variable);
      if (type == typeof (FsmString))
        return (NamedVariable) new FsmString((FsmString) variable);
      if (type == typeof (FsmGameObject))
        return (NamedVariable) new FsmGameObject((FsmGameObject) variable);
      if (type == typeof (FsmVector2))
        return (NamedVariable) new FsmVector2((FsmVector2) variable);
      if (type == typeof (FsmVector3))
        return (NamedVariable) new FsmVector3((FsmVector3) variable);
      if (type == typeof (FsmRect))
        return (NamedVariable) new FsmRect((FsmRect) variable);
      if (type == typeof (FsmQuaternion))
        return (NamedVariable) new FsmQuaternion((FsmQuaternion) variable);
      if (type == typeof (FsmColor))
        return (NamedVariable) new FsmColor((FsmColor) variable);
      if (type == typeof (FsmArray))
        return (NamedVariable) new FsmArray((FsmArray) variable);
      if (type == typeof (FsmEnum))
        return (NamedVariable) new FsmEnum((FsmEnum) variable);
      if (type == typeof (FsmObject))
        return (NamedVariable) new FsmObject((FsmObject) variable);
      Debug.LogError((object) Strings.Error_Unknown_variable_type);
      return (NamedVariable) null;
    }

    public static NamedVariable ResetVariableReference(this INamedVariable variable)
    {
      bool useVariable = variable.UseVariable;
      NamedVariable variableOfSameType = variable.GetNewVariableOfSameType();
      variableOfSameType.UseVariable = useVariable;
      return variableOfSameType;
    }

    public static NamedVariable GetNewVariableOfSameType(this INamedVariable variable)
    {
      System.Type type = variable.GetType();
      if (type == typeof (FsmMaterial))
        return (NamedVariable) new FsmMaterial();
      if (type == typeof (FsmTexture))
        return (NamedVariable) new FsmTexture();
      if (type == typeof (FsmFloat))
        return (NamedVariable) new FsmFloat();
      if (type == typeof (FsmInt))
        return (NamedVariable) new FsmInt();
      if (type == typeof (FsmBool))
        return (NamedVariable) new FsmBool();
      if (type == typeof (FsmString))
        return (NamedVariable) new FsmString();
      if (type == typeof (FsmGameObject))
        return (NamedVariable) new FsmGameObject();
      if (type == typeof (FsmVector2))
        return (NamedVariable) new FsmVector2();
      if (type == typeof (FsmVector3))
        return (NamedVariable) new FsmVector3();
      if (type == typeof (FsmRect))
        return (NamedVariable) new FsmRect();
      if (type == typeof (FsmQuaternion))
        return (NamedVariable) new FsmQuaternion();
      if (type == typeof (FsmColor))
        return (NamedVariable) new FsmColor();
      if (type == typeof (FsmArray))
      {
        FsmArray fsmArray = new FsmArray((FsmArray) variable);
        fsmArray.UseVariable = false;
        fsmArray.SetName((string) null);
        fsmArray.Resize(0);
        return (NamedVariable) fsmArray;
      }
      if (type == typeof (FsmEnum))
      {
        FsmEnum source = (FsmEnum) variable;
        FsmEnum fsmEnum = new FsmEnum(source);
        fsmEnum.UseVariable = false;
        fsmEnum.SetName((string) null);
        fsmEnum.EnumName = source.EnumName;
        return (NamedVariable) fsmEnum;
      }
      if (type == typeof (FsmObject))
      {
        FsmObject fsmObject = new FsmObject((FsmObject) variable);
        fsmObject.UseVariable = false;
        fsmObject.SetName((string) null);
        fsmObject.Value = (UnityEngine.Object) null;
        return (NamedVariable) fsmObject;
      }
      Debug.LogError((object) Strings.Error_Unknown_variable_type);
      return (NamedVariable) null;
    }

    public static VariableType GetVariableType(this NamedVariable variable)
    {
      System.Type type = variable.GetType();
      if (type == typeof (FsmMaterial))
        return VariableType.Material;
      if (type == typeof (FsmTexture))
        return VariableType.Texture;
      if (type == typeof (FsmFloat))
        return VariableType.Float;
      if (type == typeof (FsmInt))
        return VariableType.Int;
      if (type == typeof (FsmBool))
        return VariableType.Bool;
      if (type == typeof (FsmString))
        return VariableType.String;
      if (type == typeof (FsmGameObject))
        return VariableType.GameObject;
      if (type == typeof (FsmVector2))
        return VariableType.Vector2;
      if (type == typeof (FsmVector3))
        return VariableType.Vector3;
      if (type == typeof (FsmRect))
        return VariableType.Rect;
      if (type == typeof (FsmQuaternion))
        return VariableType.Quaternion;
      if (type == typeof (FsmColor))
        return VariableType.Color;
      if (type == typeof (FsmObject))
        return VariableType.Object;
      if (type == typeof (FsmEnum))
        return VariableType.Enum;
      if (type == typeof (FsmArray))
        return VariableType.Array;
      Debug.LogError((object) Strings.Error_Unknown_variable_type);
      return VariableType.Unknown;
    }

    public static bool CanNetworkSync(this NamedVariable variable)
    {
      System.Type type = variable.GetType();
      return type == typeof (FsmFloat) || type == typeof (FsmInt) || (type == typeof (FsmBool) || type == typeof (FsmVector2)) || (type == typeof (FsmVector3) || type == typeof (FsmQuaternion) || type == typeof (FsmString)) || type == typeof (FsmColor);
    }

    public static System.Type GetVariableType(UIHint hint)
    {
      switch (hint)
      {
        case UIHint.FsmFloat:
          return typeof (FsmFloat);
        case UIHint.FsmInt:
          return typeof (FsmInt);
        case UIHint.FsmBool:
          return typeof (FsmBool);
        case UIHint.FsmString:
          return typeof (FsmString);
        case UIHint.FsmVector3:
          return typeof (FsmVector3);
        case UIHint.FsmGameObject:
          return typeof (FsmGameObject);
        case UIHint.FsmColor:
          return typeof (FsmColor);
        case UIHint.FsmRect:
          return typeof (FsmRect);
        case UIHint.FsmMaterial:
          return typeof (FsmMaterial);
        case UIHint.FsmTexture:
          return typeof (FsmTexture);
        case UIHint.FsmQuaternion:
          return typeof (FsmQuaternion);
        case UIHint.FsmObject:
          return typeof (FsmObject);
        case UIHint.FsmVector2:
          return typeof (FsmVector2);
        case UIHint.FsmEnum:
          return typeof (FsmEnum);
        case UIHint.FsmArray:
          return typeof (FsmArray);
        default:
          Debug.LogError((object) string.Format(Strings.Error_Unrecognized_variable_type__, (object) hint));
          return (System.Type) null;
      }
    }
  }
}
