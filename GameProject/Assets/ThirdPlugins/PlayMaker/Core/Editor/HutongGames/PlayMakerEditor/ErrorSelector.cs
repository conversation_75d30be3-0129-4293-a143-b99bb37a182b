// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.ErrorSelector
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using JetBrains.Annotations;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class ErrorSelector : BaseEditorWindow
  {
    [SerializeField]
    private bool filterByFsm;
    private List<FsmError> allErrors;
    private Vector2 scrollPosition;
    private FsmError selectedError;

    public override void Initialize()
    {
      this.isToolWindow = true;
      this.minSize = new Vector2(200f, 100f);
      this.Refresh();
      FsmErrorChecker.OnChanged += new Action(this.Refresh);
    }

    [UsedImplicitly]
    private void OnDisable() => FsmErrorChecker.OnChanged -= new Action(this.Refresh);

    public override void InitWindowTitle() => this.SetTitle(Strings.ErrorSelector_Title);

    public void Refresh()
    {
      this.allErrors = new List<FsmError>((IEnumerable<FsmError>) FsmErrorChecker.GetErrors());
      this.allErrors.Sort();
    }

    public void OnHierarchyChange() => this.Refresh();

    public override void DoGUI()
    {
      this.DoToolbar();
      this.DoErrorHierarchyGUI();
      ErrorSelector.DoBottomPanel();
    }

    private void DoToolbar()
    {
      EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
      if (GUILayout.Button(Strings.ErrorSelector_Refresh, EditorStyles.toolbarButton))
        FsmErrorChecker.Refresh();
      GUILayout.FlexibleSpace();
      this.filterByFsm = GUILayout.Toggle(this.filterByFsm, new GUIContent(Strings.ErrorSelector_Filter_Selected_FSM_Only, Strings.ErrorSelector_Filter_Selected_FSM), EditorStyles.toolbarButton);
      GUILayout.Space(5f);
      if (FsmEditorGUILayout.ToolbarSettingsButton())
        ErrorSelector.GenerateSettingsMenu().ShowAsContext();
      EditorGUILayout.EndHorizontal();
    }

    private void DoErrorHierarchyGUI()
    {
      if (this.allErrors == null)
      {
        GUILayout.FlexibleSpace();
      }
      else
      {
        this.scrollPosition = GUILayout.BeginScrollView(this.scrollPosition);
        GUILayout.BeginVertical();
        foreach (FsmError allError in this.allErrors)
        {
          if (allError != null && allError.Fsm != null && !(allError.Fsm.OwnerObject == (UnityEngine.Object) null) && (!this.filterByFsm || allError.Fsm == FsmEditor.SelectedFsm))
          {
            GUIStyle style1 = FsmEditorStyles.ErrorTitle;
            GUIStyle style2 = FsmEditorStyles.ErrorLine;
            if (this.selectedError == allError)
            {
              style1 = FsmEditorStyles.ErrorTitleSelected;
              style2 = FsmEditorStyles.ErrorLineSelected;
            }
            FsmEditorGUILayout.LightDivider();
            if (GUILayout.Button(allError.GetPath(), style1) || GUILayout.Button(allError.ErrorString, style2))
            {
              this.selectedError = allError;
              ErrorSelector.GotoError(this.selectedError);
            }
          }
        }
        GUILayout.EndVertical();
        EditorGUIUtility.AddCursorRect(GUILayoutUtility.GetLastRect(), MouseCursor.Link);
        GUILayout.EndScrollView();
      }
    }

    private static void DoBottomPanel()
    {
      GUILayout.BeginHorizontal(FsmEditorStyles.ToolbarBottom);
      string text = string.Format(Strings.ErrorSelector_Setup_Errors, (object) FsmErrorChecker.CountSetupErrors());
      if (FsmErrorChecker.CheckingForErrors)
        EditorGUI.ProgressBar(GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.label, GUILayout.Width(150f)), FsmErrorChecker.CheckingForErrorsProgress, text);
      else
        GUILayout.Label(text, GUILayout.Width(150f));
      GUILayout.Space(20f);
      GUILayout.Label(string.Format(Strings.ErrorSelector_Runtime_Errors, (object) FsmErrorChecker.CountRuntimeErrors()));
      GUILayout.FlexibleSpace();
      GUILayout.Label(string.Format(Strings.ErrorSelector_Total_Errors, (object) FsmErrorChecker.CountAllErrors()));
      GUILayout.EndHorizontal();
    }

    private static void GotoError(FsmError error)
    {
      if (error == null || error.Fsm == null)
        return;
      FsmEditor.SelectFsm(error.Fsm);
      if (error.State == null)
        return;
      FsmEditor.SelectState(error.State, true);
      if (error.Action == null)
        return;
      FsmEditor.SelectAction(error.Action);
      FsmEditor.Repaint(true);
    }

    private static GenericMenu GenerateSettingsMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent(Strings.Menu_Enable_Real_Time_Error_Checker), FsmEditorSettings.EnableRealtimeErrorChecker, new GenericMenu.MenuFunction(ErrorSelector.ToggleEnableRealtimeErrorChecker));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Disable_Error_Checker_When_Game_Is_Playing), FsmEditorSettings.DisableErrorCheckerWhenPlaying, new GenericMenu.MenuFunction(ErrorSelector.ToggleDisableErrorCheckerWhenPlaying));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Check_for_Required_Components), FsmEditorSettings.CheckForRequiredComponent, new GenericMenu.MenuFunction(ErrorSelector.ToggleCheckForRequiredComponent));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Check_for_Required_Action_Fields), FsmEditorSettings.CheckForRequiredField, new GenericMenu.MenuFunction(ErrorSelector.ToggleCheckForRequiredField));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Check_for_Events_Not_Used_by_Target_FSM), FsmEditorSettings.CheckForEventNotUsed, new GenericMenu.MenuFunction(ErrorSelector.ToggleCheckForEventNotUsed));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Check_for_Transitions_Missing_Events), FsmEditorSettings.CheckForTransitionMissingEvent, new GenericMenu.MenuFunction(ErrorSelector.ToggleCheckForTransitionMissingEvent));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Check_for_Transitions_Missing_Targets), FsmEditorSettings.CheckForTransitionMissingTarget, new GenericMenu.MenuFunction(ErrorSelector.ToggleCheckForTransitionMissingTarget));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Check_for_Duplicate_Transition_Events), FsmEditorSettings.CheckForDuplicateTransitionEvent, new GenericMenu.MenuFunction(ErrorSelector.ToggleCheckForDuplicateTransitionEvent));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Check_for_Setup_Errors_With_Mouse_Events), FsmEditorSettings.CheckForMouseEventErrors, new GenericMenu.MenuFunction(ErrorSelector.ToggleCheckForMouseEventErrors));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Check_for_Setup_Errors_With_Collision_Events), FsmEditorSettings.CheckForCollisionEventErrors, new GenericMenu.MenuFunction(ErrorSelector.ToggleCheckForCollisionEventErrors));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Check_for_Prefab_Restrictions), FsmEditorSettings.CheckForPrefabRestrictions, new GenericMenu.MenuFunction(ErrorSelector.ToggleCheckForPrefabRestrictions));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Check_for_Obsolete_Actions), FsmEditorSettings.CheckForObsoleteActions, new GenericMenu.MenuFunction(ErrorSelector.ToggleCheckForObsoleteActions));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Check_for_Missing_Actions), FsmEditorSettings.CheckForMissingActions, new GenericMenu.MenuFunction(ErrorSelector.ToggleCheckForMissingActions));
      return genericMenu;
    }

    private static void ToggleEnableRealtimeErrorChecker()
    {
      FsmEditorSettings.EnableRealtimeErrorChecker = !FsmEditorSettings.EnableRealtimeErrorChecker;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleDisableErrorCheckerWhenPlaying()
    {
      FsmEditorSettings.DisableErrorCheckerWhenPlaying = !FsmEditorSettings.DisableErrorCheckerWhenPlaying;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleCheckForCollisionEventErrors()
    {
      FsmEditorSettings.CheckForCollisionEventErrors = !FsmEditorSettings.CheckForCollisionEventErrors;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleCheckForDuplicateTransitionEvent()
    {
      FsmEditorSettings.CheckForDuplicateTransitionEvent = !FsmEditorSettings.CheckForDuplicateTransitionEvent;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleCheckForEventNotUsed()
    {
      FsmEditorSettings.CheckForEventNotUsed = !FsmEditorSettings.CheckForEventNotUsed;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleCheckForMouseEventErrors()
    {
      FsmEditorSettings.CheckForMouseEventErrors = !FsmEditorSettings.CheckForMouseEventErrors;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleCheckForPrefabRestrictions()
    {
      FsmEditorSettings.CheckForPrefabRestrictions = !FsmEditorSettings.CheckForPrefabRestrictions;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleCheckForRequiredComponent()
    {
      FsmEditorSettings.CheckForRequiredComponent = !FsmEditorSettings.CheckForRequiredComponent;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleCheckForRequiredField()
    {
      FsmEditorSettings.CheckForRequiredField = !FsmEditorSettings.CheckForRequiredField;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleCheckForTransitionMissingEvent()
    {
      FsmEditorSettings.CheckForTransitionMissingEvent = !FsmEditorSettings.CheckForTransitionMissingEvent;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleCheckForTransitionMissingTarget()
    {
      FsmEditorSettings.CheckForTransitionMissingTarget = !FsmEditorSettings.CheckForTransitionMissingTarget;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleCheckForObsoleteActions()
    {
      FsmEditorSettings.CheckForObsoleteActions = !FsmEditorSettings.CheckForObsoleteActions;
      ErrorSelector.SaveSettings();
    }

    private static void ToggleCheckForMissingActions()
    {
      FsmEditorSettings.CheckForMissingActions = !FsmEditorSettings.CheckForMissingActions;
      ErrorSelector.SaveSettings();
    }

    private static void SaveSettings()
    {
      FsmEditorSettings.SaveSettings();
      FsmErrorChecker.Refresh();
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }
  }
}
