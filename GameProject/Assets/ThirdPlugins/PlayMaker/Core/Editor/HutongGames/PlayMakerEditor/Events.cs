// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Events
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System.Collections.Generic;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class Events
  {
    public static Fsm GetFsmTarget(Fsm fsm, FsmEventTarget fsmEventTarget)
    {
      if (fsmEventTarget == null)
        return fsm;
      switch (fsmEventTarget.target)
      {
        case FsmEventTarget.EventTarget.Self:
          return fsm;
        case FsmEventTarget.EventTarget.GameObject:
          return (Fsm) null;
        case FsmEventTarget.EventTarget.GameObjectFSM:
          return (Fsm) null;
        case FsmEventTarget.EventTarget.FSMComponent:
          return !((Object) fsmEventTarget.fsmComponent != (Object) null) ? (Fsm) null : fsmEventTarget.fsmComponent.Fsm;
        case FsmEventTarget.EventTarget.BroadcastAll:
          return (Fsm) null;
        default:
          return (Fsm) null;
      }
    }

    public static bool FsmStateRespondsToEvent(FsmState state, FsmEvent fsmEvent)
    {
      if (FsmEvent.IsNullOrEmpty(fsmEvent))
        return false;
      foreach (FsmTransition globalTransition in state.Fsm.GlobalTransitions)
      {
        if (globalTransition.EventName == fsmEvent.Name)
          return true;
      }
      foreach (FsmTransition transition in state.Transitions)
      {
        if (transition.EventName == fsmEvent.Name)
          return true;
      }
      return false;
    }

    public static bool FsmRespondsToEvent(Fsm fsm, FsmEvent fsmEvent) => fsm != null && !FsmEvent.IsNullOrEmpty(fsmEvent) && Events.FsmRespondsToEvent(fsm, fsmEvent.Name);

    public static bool FsmRespondsToEvent(Fsm fsm, string fsmEventName)
    {
      if (fsm == null || string.IsNullOrEmpty(fsmEventName))
        return false;
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (globalTransition.EventName == fsmEventName)
          return true;
      }
      foreach (FsmState state in fsm.States)
      {
        foreach (FsmTransition transition in state.Transitions)
        {
          if (transition.EventName == fsmEventName)
            return true;
        }
      }
      return false;
    }

    public static List<FsmEvent> GetGlobalEventList()
    {
      List<FsmEvent> fsmEventList = new List<FsmEvent>();
      foreach (FsmEvent fsmEvent in FsmEvent.EventList)
      {
        if (fsmEvent.IsGlobal)
          fsmEventList.Add(fsmEvent);
      }
      return fsmEventList;
    }

    public static List<FsmEvent> GetGlobalEventList(Fsm fsm)
    {
      if (fsm == null)
        return Events.GetGlobalEventList();
      List<FsmEvent> fsmEventList = new List<FsmEvent>();
      foreach (FsmEvent fsmEvent in fsm.Events)
      {
        if (fsmEvent.IsGlobal)
          fsmEventList.Add(fsmEvent);
      }
      return fsmEventList;
    }

    public static List<FsmEvent> GetEventList(Fsm fsm) => fsm != null ? Events.GetGlobalEventList(fsm) : Events.GetGlobalEventList();

    public static List<FsmEvent> GetEventList(PlayMakerFSM fsmComponent) => !((Object) fsmComponent == (Object) null) ? Events.GetGlobalEventList(fsmComponent.Fsm) : Events.GetGlobalEventList();

    public static List<FsmEvent> GetEventList(GameObject go) => Events.GetGlobalEventList();

    public static GUIContent[] GetEventNamesFromList(List<FsmEvent> eventList)
    {
      List<GUIContent> guiContentList = new List<GUIContent>()
      {
        new GUIContent(Strings.Label_None)
      };
      foreach (FsmEvent fsmEvent in eventList)
        guiContentList.Add(new GUIContent(fsmEvent.Name));
      return guiContentList.ToArray();
    }

    public static bool EventListContainsEventName(List<FsmEvent> eventList, string fsmEventName)
    {
      foreach (FsmEvent fsmEvent in eventList)
      {
        if (fsmEvent.Name == fsmEventName)
          return true;
      }
      return false;
    }
  }
}
