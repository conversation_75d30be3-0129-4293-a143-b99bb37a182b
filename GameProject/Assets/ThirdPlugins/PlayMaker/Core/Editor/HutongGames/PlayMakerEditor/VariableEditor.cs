// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.VariableEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.ComponentModel;
using System.Globalization;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class VariableEditor
  {
    private static object copiedValue;
    private static FsmEnum editingFsmEnumType;

    public static bool DebugVariables { get; set; }

    private static void RecordUndo(string edit = "Edit Action") => FsmEditor.RecordUndo(edit);

    private static bool VariableToggle(bool useVariable)
    {
      GUILayout.Space(-2f);
      GUI.backgroundColor = Color.white;
      FsmEditorContent.VariableButton.tooltip = !DragAndDropManager.IsDragging ? Strings.Tooltip_Use_Variable : (string) null;
      return GUILayout.Toggle(useVariable, FsmEditorContent.VariableButton, FsmEditorStyles.MiniToggle);
    }

    private static NamedVariable VariableToggle(NamedVariable variable, string label)
    {
      bool flag = VariableEditor.VariableToggle(variable.UseVariable);
      if (flag != variable.UseVariable)
      {
        Keyboard.ResetFocus();
        variable = variable.GetNewVariableOfSameType();
        if (flag)
        {
          variable.UseVariable = true;
          if (EditorGUI.actionKey && FsmEditor.DisconnectCheck())
          {
            string name = Labels.EnforceNamingConvention(label.Trim('*'));
            return EditorCommands.AddVariable(variable.GetVariableType(), name);
          }
        }
      }
      return variable;
    }

    public static void DebugVariableValue(INamedVariable variable)
    {
      if (variable == null)
        return;
      Rect rect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.textField);
      if (FsmEditorStyles.UsingFlatStyle)
        ++rect.y;
      switch (variable.VariableType)
      {
        case VariableType.Unknown:
          break;
        case VariableType.Float:
        case VariableType.Int:
        case VariableType.Bool:
        case VariableType.String:
        case VariableType.Vector2:
        case VariableType.Vector3:
        case VariableType.Rect:
        case VariableType.Quaternion:
        case VariableType.Array:
        case VariableType.Enum:
          FsmEditorGUI.ReadonlyTextField(rect, variable.DebugString());
          break;
        case VariableType.GameObject:
          FsmEditorGUI.ReadonlyObjectField(rect, (UnityEngine.Object) ((FsmGameObject) variable).Value, typeof (GameObject));
          break;
        case VariableType.Color:
          FsmEditorGUI.ReadonlyTextField(rect, variable.ToString());
          if (Event.current.type != UnityEngine.EventType.Repaint || variable.RawValue == null)
            break;
          Color rawValue = (Color) variable.RawValue;
          rect.x = rect.width - 37f;
          rect.width = 40f;
          rect.height = EditorGUIUtility.singleLineHeight;
          Color color = GUI.color;
          GUI.color = rawValue;
          FsmEditorStyles.ColorSwatch.Draw(rect, GUIContent.none, 0);
          GUI.color = color;
          break;
        case VariableType.Material:
          FsmEditorGUI.ReadonlyObjectField(rect, (UnityEngine.Object) ((FsmMaterial) variable).Value, typeof (Material));
          break;
        case VariableType.Texture:
          FsmEditorGUI.ReadonlyObjectField(rect, (UnityEngine.Object) ((FsmTexture) variable).Value, typeof (Texture));
          break;
        case VariableType.Object:
          FsmEditorGUI.ReadonlyObjectField(rect, ((FsmObject) variable).Value, variable.ObjectType);
          break;
        default:
          throw new ArgumentOutOfRangeException();
      }
    }

    private static void EndVariableEditor(INamedVariable variable)
    {
      EditorGUILayout.EndHorizontal();
      if (variable == null || !VariableEditor.DebugVariables || (!variable.UseVariable || string.IsNullOrEmpty(variable.Name)))
        return;
      VariableEditor.DebugVariableValue(variable);
    }

    [Localizable(false)]
    private static void DoVariableContextMenu(INamedVariable variable)
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent(Strings.Menu_Copy_Value), false, new GenericMenu.MenuFunction2(VariableEditor.CopyVariableValue), (object) variable);
      genericMenu.AddItem(new GUIContent(Strings.Menu_Paste_Value), false, new GenericMenu.MenuFunction2(VariableEditor.PasteVariableValue), (object) variable);
      genericMenu.ShowAsContext();
    }

    private static void CopyVariableValue(object userdata) => VariableEditor.copiedValue = ((NamedVariable) userdata).RawValue;

    private static void PasteVariableValue(object userdata) => ((NamedVariable) userdata).RawValue = VariableEditor.copiedValue;

    public static bool IsReallyAssignableFrom(this System.Type t, System.Type other)
    {
      if (t.IsAssignableFrom(other))
        return true;
      return t.GetMethod("op_Implicit", new System.Type[1]{ other }) != null;
    }

    private static FsmVar DoFsmVarPopup(
      GUIContent label,
      Fsm fsm,
      FsmVar fsmVar,
      VariableType typeConstraint,
      System.Type objectConstraint)
    {
      ActionEditor.DoVariableSelector(label, fsm, fsmVar.Type, fsmVar.NamedVar, typeConstraint, objectConstraint);
      fsmVar.useVariable = true;
      return fsmVar;
    }

    public static FsmVar FsmVarPopup(
      GUIContent label,
      Fsm fsm,
      FsmVar fsmVar,
      VariableType variableType = VariableType.Unknown,
      System.Type objectConstraint = null)
    {
      EditorGUILayout.BeginHorizontal();
      fsmVar = VariableEditor.DoFsmVarPopup(label, fsm, fsmVar, variableType, objectConstraint);
      VariableEditor.EndVariableEditor((INamedVariable) fsmVar.NamedVar);
      return fsmVar;
    }

    private static FsmFloat DoFsmFloatPopup(GUIContent label, Fsm fsm, FsmFloat fsmFloat)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Float, (NamedVariable) fsmFloat);
      fsmFloat.UseVariable = true;
      return fsmFloat;
    }

    public static FsmFloat FsmFloatPopup(GUIContent label, Fsm fsm, FsmFloat fsmFloat)
    {
      EditorGUILayout.BeginHorizontal();
      fsmFloat = VariableEditor.DoFsmFloatPopup(label, fsm, fsmFloat);
      VariableEditor.EndVariableEditor((INamedVariable) fsmFloat);
      return fsmFloat;
    }

    public static FsmFloat FsmFloatField(GUIContent label, Fsm fsm, FsmFloat fsmFloat)
    {
      EditorGUILayout.BeginHorizontal();
      if (fsmFloat.UseVariable)
      {
        fsmFloat = VariableEditor.DoFsmFloatPopup(label, fsm, fsmFloat);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        float num = EditorGUILayout.FloatField(label, fsmFloat.Value);
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmFloat.Value = num;
        }
      }
      fsmFloat = (FsmFloat) VariableEditor.VariableToggle((NamedVariable) fsmFloat, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmFloat);
      return fsmFloat;
    }

    public static FsmFloat FsmFloatSlider(
      GUIContent label,
      Fsm fsm,
      FsmFloat fsmFloat,
      float minSliderValue,
      float maxSliderValue)
    {
      EditorGUILayout.BeginHorizontal();
      if (fsmFloat.UseVariable)
      {
        fsmFloat = VariableEditor.DoFsmFloatPopup(label, fsm, fsmFloat);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        float num = FsmEditorGUILayout.FloatSlider(label, fsmFloat.Value, minSliderValue, maxSliderValue);
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmFloat.Value = num;
        }
      }
      fsmFloat = (FsmFloat) VariableEditor.VariableToggle((NamedVariable) fsmFloat, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmFloat);
      return fsmFloat;
    }

    public static FsmInt FsmIntSlider(
      GUIContent label,
      Fsm fsm,
      FsmInt fsmInt,
      int minSliderValue,
      int maxSliderValue)
    {
      EditorGUILayout.BeginHorizontal();
      if (fsmInt.UseVariable)
      {
        fsmInt = VariableEditor.DoFsmIntPopup(label, fsm, fsmInt);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        int num = FsmEditorGUILayout.IntSlider(label, fsmInt.Value, minSliderValue, maxSliderValue);
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmInt.Value = num;
        }
      }
      fsmInt = (FsmInt) VariableEditor.VariableToggle((NamedVariable) fsmInt, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmInt);
      return fsmInt;
    }

    private static FsmInt DoFsmIntPopup(GUIContent label, Fsm fsm, FsmInt fsmInt)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Int, (NamedVariable) fsmInt);
      fsmInt.UseVariable = true;
      return fsmInt;
    }

    public static FsmInt FsmIntPopup(GUIContent label, Fsm fsm, FsmInt fsmInt)
    {
      EditorGUILayout.BeginHorizontal();
      fsmInt = VariableEditor.DoFsmIntPopup(label, fsm, fsmInt);
      VariableEditor.EndVariableEditor((INamedVariable) fsmInt);
      return fsmInt;
    }

    public static FsmInt FsmIntField(
      GUIContent label,
      Fsm fsm,
      FsmInt fsmInt,
      object[] attributes)
    {
      EditorGUILayout.BeginHorizontal();
      if (fsmInt.UseVariable)
      {
        fsmInt = VariableEditor.DoFsmIntPopup(label, fsm, fsmInt);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        int num;
        switch (CustomAttributeHelpers.GetUIHint(attributes))
        {
          case UIHint.Layer:
            num = EditorGUILayout.LayerField(label, Convert.ToInt32((object) fsmInt.Value, (IFormatProvider) CultureInfo.CurrentCulture));
            break;
          case UIHint.LayerMask:
            num = GUIHelpers.LayerMaskField(label, Convert.ToInt32((object) fsmInt.Value, (IFormatProvider) CultureInfo.CurrentCulture));
            break;
          default:
            num = EditorGUILayout.IntField(label, fsmInt.Value);
            break;
        }
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmInt.Value = num;
        }
      }
      fsmInt = (FsmInt) VariableEditor.VariableToggle((NamedVariable) fsmInt, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmInt);
      return fsmInt;
    }

    private static FsmBool DoFsmBoolPopup(GUIContent label, Fsm fsm, FsmBool fsmBool)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Bool, (NamedVariable) fsmBool);
      fsmBool.UseVariable = true;
      return fsmBool;
    }

    public static FsmBool FsmBoolPopup(GUIContent label, Fsm fsm, FsmBool fsmBool)
    {
      EditorGUILayout.BeginHorizontal();
      fsmBool = VariableEditor.DoFsmBoolPopup(label, fsm, fsmBool);
      VariableEditor.EndVariableEditor((INamedVariable) fsmBool);
      return fsmBool;
    }

    public static FsmBool FsmBoolField(GUIContent label, Fsm fsm, FsmBool fsmBool)
    {
      EditorGUILayout.BeginHorizontal();
      if (fsmBool.UseVariable)
      {
        fsmBool = VariableEditor.DoFsmBoolPopup(label, fsm, fsmBool);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        bool flag = EditorGUILayout.Toggle(label, fsmBool.Value);
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmBool.Value = flag;
        }
      }
      fsmBool = (FsmBool) VariableEditor.VariableToggle((NamedVariable) fsmBool, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmBool);
      return fsmBool;
    }

    private static FsmString DoFsmStringPopup(
      GUIContent label,
      Fsm fsm,
      FsmString fsmString)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.String, (NamedVariable) fsmString);
      fsmString.UseVariable = true;
      return fsmString;
    }

    public static FsmString FsmStringPopup(GUIContent label, Fsm fsm, FsmString fsmString)
    {
      EditorGUILayout.BeginHorizontal();
      fsmString = VariableEditor.DoFsmStringPopup(label, fsm, fsmString);
      VariableEditor.EndVariableEditor((INamedVariable) fsmString);
      return fsmString;
    }

    public static FsmString FsmStringField(
      GUIContent label,
      Fsm fsm,
      FsmString fsmString,
      object[] attributes)
    {
      if (fsmString.UseVariable)
      {
        EditorGUILayout.BeginHorizontal();
        fsmString = VariableEditor.DoFsmStringPopup(label, fsm, fsmString);
      }
      else
      {
        if (fsmString.Value == null)
          fsmString.Value = string.Empty;
        EditorGUILayout.BeginHorizontal();
        EditorGUI.BeginChangeCheck();
        string str = fsmString.Value;
        UIHint uiHint = CustomAttributeHelpers.GetUIHint(attributes);
        switch (uiHint)
        {
          case UIHint.TextArea:
            GUILayout.BeginVertical();
            GUILayout.Label(label);
            Rect rect = GUILayoutUtility.GetRect(FsmEditorContent.TempContent(fsmString.Value), FsmEditorStyles.TextArea, GUILayout.MinHeight(44f));
            rect.width = FsmEditor.InspectorPanelWidth - 10f;
            str = EditorGUI.TextArea(rect, fsmString.Value);
            GUILayout.EndVertical();
            break;
          case UIHint.Behaviour:
            str = ActionEditor.EditComponentName(label, fsmString.Value, typeof (Behaviour));
            break;
          case UIHint.Script:
            str = ActionEditor.EditComponentName(label, fsmString.Value, typeof (MonoBehaviour));
            ActionEditor.TrySetBehaviourContext(fsmString.Value);
            break;
          case UIHint.Method:
            str = ActionEditor.EditMethodName(label, fsmString.Value, false);
            break;
          case UIHint.Animation:
            str = EditorGUILayout.TextField(label, fsmString.Value);
            ActionEditor.AnimationNamePopup(fsmString);
            break;
          case UIHint.Tag:
            str = EditorGUILayout.TagField(label, fsmString.Value);
            break;
          case UIHint.Layer:
            StringEditor.LayerNamePopup(label, fsmString);
            break;
          case UIHint.ScriptComponent:
            str = ActionEditor.EditScriptName(label, fsmString.Value);
            break;
          case UIHint.FsmName:
            str = EditorGUILayout.TextField(label, fsmString.Value);
            ActionEditor.FsmNamePopup(fsmString);
            break;
          case UIHint.FsmEvent:
            str = ActionEditor.EditFsmEvent(label, fsmString.Value);
            break;
          case UIHint.FsmFloat:
          case UIHint.FsmInt:
          case UIHint.FsmBool:
          case UIHint.FsmString:
          case UIHint.FsmVector3:
          case UIHint.FsmGameObject:
          case UIHint.FsmColor:
          case UIHint.FsmRect:
          case UIHint.FsmMaterial:
          case UIHint.FsmTexture:
          case UIHint.FsmQuaternion:
          case UIHint.FsmObject:
          case UIHint.FsmVector2:
          case UIHint.FsmEnum:
          case UIHint.FsmArray:
            str = EditorGUILayout.TextField(label, fsmString.Value);
            ActionEditor.VariablePopup(fsmString, uiHint);
            break;
          case UIHint.AnimatorFloat:
            str = EditorGUILayout.TextField(label, fsmString.Value);
            ActionEditor.AnimatorFloatPopup(fsmString);
            break;
          case UIHint.AnimatorBool:
            str = EditorGUILayout.TextField(label, fsmString.Value);
            ActionEditor.AnimatorBoolPopup(fsmString);
            break;
          case UIHint.AnimatorInt:
            str = EditorGUILayout.TextField(label, fsmString.Value);
            ActionEditor.AnimatorIntPopup(fsmString);
            break;
          case UIHint.AnimatorTrigger:
            str = EditorGUILayout.TextField(label, fsmString.Value);
            ActionEditor.AnimatorTriggerPopup(fsmString);
            break;
          case UIHint.SortingLayer:
            StringEditor.SortingLayerNamePopup(label, fsmString);
            break;
          case UIHint.TagMenu:
            StringEditor.TagPopup(label, fsmString);
            break;
          default:
            str = EditorGUILayout.TextField(label, fsmString.Value);
            break;
        }
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmString.Value = str;
        }
      }
      fsmString = (FsmString) VariableEditor.VariableToggle((NamedVariable) fsmString, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmString);
      return fsmString;
    }

    private static FsmRect DoFsmRectPopup(GUIContent label, Fsm fsm, FsmRect fsmRect)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Rect, (NamedVariable) fsmRect);
      fsmRect.UseVariable = true;
      return fsmRect;
    }

    public static FsmRect FsmRectPopup(GUIContent label, Fsm fsm, FsmRect fsmRect)
    {
      EditorGUILayout.BeginHorizontal();
      fsmRect = VariableEditor.DoFsmRectPopup(label, fsm, fsmRect);
      VariableEditor.EndVariableEditor((INamedVariable) fsmRect);
      return fsmRect;
    }

    public static FsmRect FsmRectField(GUIContent label, Fsm fsm, FsmRect fsmRect)
    {
      EditorGUILayout.BeginHorizontal();
      if (fsmRect.UseVariable)
      {
        fsmRect = VariableEditor.DoFsmRectPopup(label, fsm, fsmRect);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        Rect rect = EditorGUILayout.RectField(label, fsmRect.Value);
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmRect.Value = rect;
        }
      }
      fsmRect = (FsmRect) VariableEditor.VariableToggle((NamedVariable) fsmRect, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmRect);
      return fsmRect;
    }

    private static FsmQuaternion DoFsmQuaternionPopup(
      GUIContent label,
      Fsm fsm,
      FsmQuaternion fsmQauternion)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Quaternion, (NamedVariable) fsmQauternion);
      fsmQauternion.UseVariable = true;
      return fsmQauternion;
    }

    public static FsmQuaternion FsmQuaternionPopup(
      GUIContent label,
      Fsm fsm,
      FsmQuaternion fsmQauternion)
    {
      EditorGUILayout.BeginHorizontal();
      fsmQauternion = VariableEditor.DoFsmQuaternionPopup(label, fsm, fsmQauternion);
      VariableEditor.EndVariableEditor((INamedVariable) fsmQauternion);
      return fsmQauternion;
    }

    public static FsmQuaternion FsmQuaternionField(
      GUIContent label,
      Fsm fsm,
      FsmQuaternion fsmQauternion)
    {
      EditorGUILayout.BeginHorizontal();
      if (fsmQauternion.UseVariable)
      {
        fsmQauternion = VariableEditor.DoFsmQuaternionPopup(label, fsm, fsmQauternion);
      }
      else
      {
        Vector3 euler = EditorGUILayout.Vector3Field(label, fsmQauternion.Value.eulerAngles);
        if (euler != fsmQauternion.Value.eulerAngles)
        {
          VariableEditor.RecordUndo();
          fsmQauternion.Value = Quaternion.Euler(euler);
        }
      }
      fsmQauternion = (FsmQuaternion) VariableEditor.VariableToggle((NamedVariable) fsmQauternion, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmQauternion);
      return fsmQauternion;
    }

    private static FsmVector2 DoFsmVector2Popup(
      GUIContent label,
      Fsm fsm,
      FsmVector2 fsmVector2)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Vector2, (NamedVariable) fsmVector2);
      fsmVector2.UseVariable = true;
      return fsmVector2;
    }

    public static FsmVector2 FsmVector2Popup(
      GUIContent label,
      Fsm fsm,
      FsmVector2 fsmVector2)
    {
      EditorGUILayout.BeginHorizontal();
      fsmVector2 = VariableEditor.DoFsmVector2Popup(label, fsm, fsmVector2);
      VariableEditor.EndVariableEditor((INamedVariable) fsmVector2);
      return fsmVector2;
    }

    public static FsmVector2 FsmVector2Field(
      GUIContent label,
      Fsm fsm,
      FsmVector2 fsmVector2)
    {
      EditorGUILayout.BeginHorizontal();
      if (fsmVector2.UseVariable)
      {
        fsmVector2 = VariableEditor.DoFsmVector2Popup(label, fsm, fsmVector2);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        Vector2 vector2 = EditorGUILayout.Vector2Field(label, fsmVector2.Value);
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmVector2.Value = vector2;
        }
      }
      fsmVector2 = (FsmVector2) VariableEditor.VariableToggle((NamedVariable) fsmVector2, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmVector2);
      return fsmVector2;
    }

    private static FsmVector3 DoFsmVector3Popup(
      GUIContent label,
      Fsm fsm,
      FsmVector3 fsmVector3)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Vector3, (NamedVariable) fsmVector3);
      fsmVector3.UseVariable = true;
      return fsmVector3;
    }

    public static FsmVector3 FsmVector3Popup(
      GUIContent label,
      Fsm fsm,
      FsmVector3 fsmVector3)
    {
      EditorGUILayout.BeginHorizontal();
      fsmVector3 = VariableEditor.DoFsmVector3Popup(label, fsm, fsmVector3);
      VariableEditor.EndVariableEditor((INamedVariable) fsmVector3);
      return fsmVector3;
    }

    public static FsmVector3 FsmVector3Field(
      GUIContent label,
      Fsm fsm,
      FsmVector3 fsmVector3)
    {
      EditorGUILayout.BeginHorizontal();
      if (fsmVector3.UseVariable)
      {
        fsmVector3 = VariableEditor.DoFsmVector3Popup(label, fsm, fsmVector3);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        Vector3 vector3 = EditorGUILayout.Vector3Field(label, fsmVector3.Value);
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmVector3.Value = vector3;
        }
      }
      fsmVector3 = (FsmVector3) VariableEditor.VariableToggle((NamedVariable) fsmVector3, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmVector3);
      return fsmVector3;
    }

    private static FsmColor DoFsmColorPopup(GUIContent label, Fsm fsm, FsmColor fsmColor)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Color, (NamedVariable) fsmColor);
      fsmColor.UseVariable = true;
      return fsmColor;
    }

    public static FsmColor FsmColorPopup(GUIContent label, Fsm fsm, FsmColor fsmColor)
    {
      EditorGUILayout.BeginHorizontal();
      fsmColor = VariableEditor.DoFsmColorPopup(label, fsm, fsmColor);
      VariableEditor.EndVariableEditor((INamedVariable) fsmColor);
      return fsmColor;
    }

    public static FsmColor FsmColorField(GUIContent label, Fsm fsm, FsmColor fsmColor)
    {
      EditorGUILayout.BeginHorizontal();
      if (fsmColor.UseVariable)
      {
        fsmColor = VariableEditor.DoFsmColorPopup(label, fsm, fsmColor);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        Color color = EditorGUILayout.ColorField(label, fsmColor.Value);
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmColor.Value = color;
        }
      }
      fsmColor = (FsmColor) VariableEditor.VariableToggle((NamedVariable) fsmColor, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmColor);
      return fsmColor;
    }

    private static FsmGameObject DoFsmGameObjectPopup(
      GUIContent label,
      Fsm fsm,
      FsmGameObject fsmGameObject)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.GameObject, (NamedVariable) fsmGameObject);
      fsmGameObject.UseVariable = true;
      return fsmGameObject;
    }

    public static FsmGameObject FsmGameObjectPopup(
      GUIContent label,
      Fsm fsm,
      FsmGameObject fsmGameObject)
    {
      EditorGUILayout.BeginHorizontal();
      fsmGameObject = VariableEditor.DoFsmGameObjectPopup(label, fsm, fsmGameObject);
      VariableEditor.EndVariableEditor((INamedVariable) fsmGameObject);
      return fsmGameObject;
    }

    public static FsmGameObject FsmGameObjectField(
      GUIContent label,
      Fsm fsm,
      FsmGameObject fsmGameObject)
    {
      if (label == null)
        label = GUIContent.none;
      EditorGUILayout.BeginHorizontal();
      if (fsmGameObject.UseVariable)
      {
        fsmGameObject = VariableEditor.DoFsmGameObjectPopup(label, fsm, fsmGameObject);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        GameObject gameObject = (GameObject) EditorGUILayout.ObjectField(label, (UnityEngine.Object) fsmGameObject.Value, typeof (GameObject), !FsmEditor.SelectedFsmIsPersistent());
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmGameObject.Value = gameObject;
        }
      }
      fsmGameObject = (FsmGameObject) VariableEditor.VariableToggle((NamedVariable) fsmGameObject, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmGameObject);
      return fsmGameObject;
    }

    private static FsmObject DoFsmObjectPopup(
      GUIContent label,
      Fsm fsm,
      FsmObject fsmObject,
      System.Type objectType)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Object, (NamedVariable) fsmObject, VariableType.Object, objectType);
      fsmObject.UseVariable = true;
      return fsmObject;
    }

    public static FsmObject FsmObjectPopup(
      GUIContent label,
      Fsm fsm,
      FsmObject fsmObject,
      System.Type objectType)
    {
      EditorGUILayout.BeginHorizontal();
      fsmObject = VariableEditor.DoFsmObjectPopup(label, fsm, fsmObject, objectType);
      VariableEditor.EndVariableEditor((INamedVariable) fsmObject);
      return fsmObject;
    }

    public static FsmObject FsmObjectField(
      GUIContent label,
      Fsm fsm,
      FsmObject fsmObject,
      System.Type objectTypeConstraint,
      object[] attributes)
    {
      if (label == null)
        label = GUIContent.none;
      if (fsmObject == null)
      {
        FsmObject fsmObject1 = new FsmObject();
        fsmObject1.ObjectType = objectTypeConstraint;
        fsmObject = fsmObject1;
      }
      EditorGUILayout.BeginHorizontal();
      if (!objectTypeConstraint.IsAssignableFrom(fsmObject.ObjectType))
        fsmObject.ObjectType = objectTypeConstraint;
      if (fsmObject.UseVariable)
      {
        fsmObject = VariableEditor.DoFsmObjectPopup(label, fsm, fsmObject, objectTypeConstraint);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        ObjectPropertyDrawer objectPropertyDrawer = ObjectPropertyDrawers.GetObjectPropertyDrawer(fsmObject.ObjectType);
        UnityEngine.Object @object = objectPropertyDrawer != null ? objectPropertyDrawer.OnGUI(label, fsmObject.Value, !FsmEditor.SelectedFsmIsPersistent(), attributes) : EditorGUILayout.ObjectField(label, fsmObject.Value, fsmObject.ObjectType, !FsmEditor.SelectedFsmIsPersistent());
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmObject.Value = @object;
        }
      }
      fsmObject = (FsmObject) VariableEditor.VariableToggle((NamedVariable) fsmObject, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmObject);
      return fsmObject;
    }

    private static FsmMaterial DoFsmMaterialPopup(
      GUIContent label,
      Fsm fsm,
      FsmMaterial fsmObject)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Material, (NamedVariable) fsmObject);
      fsmObject.UseVariable = true;
      return fsmObject;
    }

    public static FsmMaterial FsmMaterialPopup(
      GUIContent label,
      Fsm fsm,
      FsmMaterial fsmObject)
    {
      EditorGUILayout.BeginHorizontal();
      fsmObject = VariableEditor.DoFsmMaterialPopup(label, fsm, fsmObject);
      VariableEditor.EndVariableEditor((INamedVariable) fsmObject);
      return fsmObject;
    }

    public static FsmMaterial FsmMaterialField(
      GUIContent label,
      Fsm fsm,
      FsmMaterial fsmObject)
    {
      if (label == null)
        label = GUIContent.none;
      EditorGUILayout.BeginHorizontal();
      if (fsmObject.UseVariable)
      {
        fsmObject = VariableEditor.DoFsmMaterialPopup(label, fsm, fsmObject);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        Material material = (Material) EditorGUILayout.ObjectField(label, (UnityEngine.Object) fsmObject.Value, typeof (Material), !FsmEditor.SelectedFsmIsPersistent());
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmObject.Value = material;
        }
      }
      fsmObject = (FsmMaterial) VariableEditor.VariableToggle((NamedVariable) fsmObject, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmObject);
      return fsmObject;
    }

    private static FsmTexture DoFsmTexturePopup(
      GUIContent label,
      Fsm fsm,
      FsmTexture fsmObject)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Texture, (NamedVariable) fsmObject);
      fsmObject.UseVariable = true;
      return fsmObject;
    }

    public static FsmTexture FsmTexturePopup(
      GUIContent label,
      Fsm fsm,
      FsmTexture fsmObject)
    {
      EditorGUILayout.BeginHorizontal();
      fsmObject = VariableEditor.DoFsmTexturePopup(label, fsm, fsmObject);
      VariableEditor.EndVariableEditor((INamedVariable) fsmObject);
      return fsmObject;
    }

    public static FsmTexture FsmTextureField(
      GUIContent label,
      Fsm fsm,
      FsmTexture fsmObject)
    {
      if (label == null)
        label = GUIContent.none;
      EditorGUILayout.BeginHorizontal();
      if (fsmObject.UseVariable)
      {
        fsmObject = VariableEditor.DoFsmTexturePopup(label, fsm, fsmObject);
      }
      else
      {
        EditorGUILayout.PrefixLabel(label);
        EditorGUI.BeginChangeCheck();
        Texture texture = (Texture) EditorGUILayout.ObjectField((UnityEngine.Object) fsmObject.Value, typeof (Texture), (!FsmEditor.SelectedFsmIsPersistent() ? 1 : 0) != 0, GUILayout.Width(80f), GUILayout.Height(80f));
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmObject.Value = texture;
        }
        GUILayout.FlexibleSpace();
      }
      fsmObject = (FsmTexture) VariableEditor.VariableToggle((NamedVariable) fsmObject, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmObject);
      return fsmObject;
    }

    private static FsmArray DoFsmArrayPopup(
      GUIContent label,
      Fsm fsm,
      FsmArray fsmArray,
      VariableType typeConstraint)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Array, (NamedVariable) fsmArray, typeConstraint, fsmArray.ObjectType);
      fsmArray.UseVariable = true;
      return fsmArray;
    }

    public static FsmArray FsmArrayPopup(
      GUIContent label,
      Fsm fsm,
      FsmArray fsmArray,
      VariableType typeConstraint)
    {
      EditorGUILayout.BeginHorizontal();
      fsmArray = VariableEditor.DoFsmArrayPopup(label, fsm, fsmArray, typeConstraint);
      VariableEditor.EndVariableEditor((INamedVariable) fsmArray);
      return fsmArray;
    }

    public static FsmArray FsmArrayField(
      GUIContent label,
      Fsm fsm,
      FsmArray fsmArray,
      VariableType typeConstraint)
    {
      EditorGUILayout.BeginHorizontal();
      if (fsmArray.UseVariable)
      {
        fsmArray = VariableEditor.DoFsmArrayPopup(label, fsm, fsmArray, typeConstraint);
      }
      else
      {
        fsmArray.SetType(typeConstraint);
        GUILayout.Label(label);
      }
      fsmArray = (FsmArray) VariableEditor.VariableToggle((NamedVariable) fsmArray, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmArray);
      return fsmArray;
    }

    public static void EnumTypeSelector(FsmEnum fsmEnum)
    {
      GUILayout.BeginHorizontal();
      FsmEditorGUILayout.PrefixLabel(Strings.Label_Enum_Type);
      Rect rect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.popup);
      if (GUI.Button(rect, FsmEditorContent.TempContent(Labels.GetShortTypeName(fsmEnum.EnumType), fsmEnum.EnumName), EditorStyles.popup))
      {
        if (FsmEditorSettings.UseAdvancedDropdowns)
          VariableEditor.DoFsmEnumTypeDropdown(rect, fsmEnum);
        else
          VariableEditor.DoFsmEnumTypeMenu(fsmEnum);
      }
      GUILayout.EndHorizontal();
    }

    public static GenericMenu DoFsmEnumTypeMenu(FsmEnum fsmEnum)
    {
      VariableEditor.editingFsmEnumType = fsmEnum;
      GenericMenu genericMenu = new GenericMenu();
      foreach (System.Type enumType in TypeHelpers.EnumTypeList)
      {
        string fullName = enumType.FullName;
        string text = fullName.Replace('.', '/');
        genericMenu.AddItem(new GUIContent(text), fullName == fsmEnum.EnumName, new GenericMenu.MenuFunction2(VariableEditor.SetFsmEnumType), (object) fullName);
      }
      genericMenu.ShowAsContext();
      return genericMenu;
    }

    public static void DoFsmEnumTypeDropdown(Rect rect, FsmEnum fsmEnum)
    {
      VariableEditor.editingFsmEnumType = fsmEnum;
      VariableTypeDropdown.ShowEnumsTypeDropdown(rect, new GenericMenu.MenuFunction2(VariableEditor.SetFsmEnumTypeDropdown));
    }

    private static void SetFsmEnumTypeDropdown(object userdata)
    {
      if (VariableEditor.editingFsmEnumType == null)
        return;
      System.Type type = userdata as System.Type;
      VariableEditor.editingFsmEnumType.EnumType = type;
      FsmEditor.SaveActions();
    }

    private static void SetFsmEnumType(object userdata)
    {
      if (VariableEditor.editingFsmEnumType == null)
        return;
      string typeName = userdata as string;
      VariableEditor.editingFsmEnumType.EnumType = ReflectionUtils.GetGlobalType(typeName);
    }

    private static FsmEnum DoFsmEnumPopup(
      GUIContent label,
      Fsm fsm,
      FsmEnum fsmEnum,
      System.Type objectType)
    {
      ActionEditor.DoVariableSelector(label, fsm, VariableType.Enum, (NamedVariable) fsmEnum, VariableType.Enum, objectType);
      fsmEnum.UseVariable = true;
      return fsmEnum;
    }

    public static FsmEnum FsmEnumPopup(
      GUIContent label,
      Fsm fsm,
      FsmEnum fsmEnum,
      System.Type objectType)
    {
      EditorGUILayout.BeginHorizontal();
      fsmEnum = VariableEditor.DoFsmEnumPopup(label, fsm, fsmEnum, objectType);
      VariableEditor.EndVariableEditor((INamedVariable) fsmEnum);
      return fsmEnum;
    }

    public static FsmEnum FsmEnumField(
      GUIContent label,
      Fsm fsm,
      FsmEnum fsmEnum,
      System.Type objectType)
    {
      fsmEnum.EnumType = objectType;
      EditorGUILayout.BeginHorizontal();
      if (fsmEnum.UseVariable)
      {
        fsmEnum = VariableEditor.DoFsmEnumPopup(label, fsm, fsmEnum, objectType);
      }
      else
      {
        EditorGUI.BeginChangeCheck();
        Enum @enum = VariableEditor.EditFsmEnum(label, fsmEnum);
        if (EditorGUI.EndChangeCheck())
        {
          VariableEditor.RecordUndo();
          fsmEnum.Value = @enum;
        }
      }
      fsmEnum = (FsmEnum) VariableEditor.VariableToggle((NamedVariable) fsmEnum, label.text);
      VariableEditor.EndVariableEditor((INamedVariable) fsmEnum);
      return fsmEnum;
    }

    public static Enum EditFsmEnum(GUIContent label, FsmEnum fsmEnum)
    {
      if (!FsmEditorSettings.UseAdvancedDropdowns)
        return EditorGUILayout.EnumPopup(label, fsmEnum.Value);
      HutongGames.EnumData enumData = HutongGames.EnumDataUtility.GetCachedEnumData(fsmEnum.EnumType);
      if (enumData.displayNames.Length < 6)
        return EditorGUILayout.EnumPopup(label, fsmEnum.Value);
      GUILayout.BeginHorizontal();
      FsmEditorGUILayout.PrefixLabel(label);
      Rect rect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.popup);
      int index = Array.IndexOf<Enum>(enumData.values, fsmEnum.Value);
      if (GUI.Button(rect, enumData.displayNames[index], EditorStyles.popup))
      {
        Action<int> onSelectionMade = (Action<int>) (i =>
        {
          fsmEnum.Value = enumData.values[i];
          FsmEditor.SaveActions();
        });
        EnumDropdown.ShowEnumDropdown(rect, fsmEnum.EnumType, onSelectionMade);
      }
      GUILayout.EndHorizontal();
      return fsmEnum.Value;
    }
  }
}
