// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Graph.NodeStyle
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using UnityEngine;

namespace HutongGames.PlayMakerEditor.Graph
{
  public class NodeStyle
  {
    public GUIStyle shadow;
    public GUIStyle box;
    public GUIStyle[] outlines;

    protected NodeStyle()
    {
    }

    public NodeStyle(string basePath, RectOffset border, int shadowOffset = 5)
    {
      this.box = GraphStyles.NewStyle(basePath);
      this.box.border = border;
      this.box.normal.textColor = Color.white;
      this.shadow = GraphStyles.NewStyle(basePath + "Shadow");
      if ((Object) this.shadow.normal.background != (Object) null)
      {
        Texture2D background1 = this.shadow.normal.background;
        Texture2D background2 = this.box.normal.background;
        int num1 = (background1.width - background2.width) / 2;
        int num2 = (background1.height - background2.height) / 2;
        this.shadow.border = new RectOffset(num1 + 2, num1 + 2, num2 - shadowOffset + 2, num2 + shadowOffset + 2);
        this.shadow.overflow = new RectOffset(num1, num1, num2 - shadowOffset, num2 + shadowOffset);
      }
      this.outlines = new GUIStyle[4];
      for (int index = 0; index < 4; ++index)
      {
        int num = index + 1;
        this.outlines[index] = GraphStyles.NewStyle(string.Format("{0}Outline{1}px", (object) basePath, (object) num));
        this.outlines[index].border = new RectOffset(border.left + index, border.right + index, border.top + index, border.bottom + index);
        this.outlines[index].overflow = new RectOffset(num, num, num, num);
      }
    }

    public void Draw(Rect pos, GUIContent content, Color color)
    {
      Color backgroundColor = GUI.backgroundColor;
      GUI.backgroundColor = color;
      this.Draw(pos, content);
      GUI.backgroundColor = backgroundColor;
    }

    public void Draw(Rect pos, GUIContent content, Color bgColor, Color contentColor)
    {
      Color backgroundColor = GUI.backgroundColor;
      Color contentColor1 = GUI.contentColor;
      GUI.backgroundColor = bgColor;
      GUI.contentColor = contentColor;
      this.Draw(pos, content);
      GUI.backgroundColor = backgroundColor;
      GUI.contentColor = contentColor1;
    }

    public void DrawShadow(Rect pos)
    {
      if (this.shadow == null)
        return;
      this.shadow.Draw(pos, false, false, false, false);
    }

    public void Draw(Rect pos, GUIContent content) => this.box.Draw(pos, content, false, false, false, false);

    public void DrawOutline(Rect pos, int width, Color color)
    {
      Color backgroundColor = GUI.backgroundColor;
      GUI.backgroundColor = color;
      this.DrawOutline(pos, width);
      GUI.backgroundColor = backgroundColor;
    }

    public void DrawOutline(Rect pos, int width) => this.outlines[width - 1].Draw(pos, false, false, false, false);

    public void SetOverflow(RectOffset overflow)
    {
      this.box.overflow = overflow;
      this.shadow.overflow = new RectOffset(this.shadow.overflow.left + overflow.left, this.shadow.overflow.right + overflow.right, this.shadow.overflow.top + overflow.top, this.shadow.overflow.bottom + overflow.bottom);
      for (int index = 0; index < 4; ++index)
      {
        int num = index + 1;
        RectOffset rectOffset = new RectOffset(overflow.left + num, overflow.right + num, overflow.top + num, overflow.bottom + num);
        this.outlines[index].overflow = rectOffset;
      }
    }
  }
}
