// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.PropertyDropdown
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class PropertyDropdown : AdvancedDropdown
  {
    private readonly FsmProperty fsmProperty;
    private readonly Dictionary<int, string> itemLookup = new Dictionary<int, string>();
    private int currentId;

    public static void ShowPropertyDropdown(Rect rect, FsmProperty fsmProperty)
    {
      rect.width += 100f;
      rect.x -= 100f;
      new PropertyDropdown(new AdvancedDropdownState(), fsmProperty).Show(rect);
    }

    private PropertyDropdown(AdvancedDropdownState state, FsmProperty property)
      : base(state)
      => this.fsmProperty = property;

    protected override AdvancedDropdownItem BuildRoot()
    {
      AdvancedDropdownItem root = new AdvancedDropdownItem("Property");
      if (this.fsmProperty == null || string.IsNullOrEmpty(this.fsmProperty.TargetTypeName))
        return root;
      System.Type globalType = ReflectionUtils.GetGlobalType(this.fsmProperty.TargetTypeName);
      if (globalType == null)
        return root;
      this.AddPropertyMenuItems(root, globalType, "", 0, this.fsmProperty.setProperty);
      return root;
    }

    private void AddPropertyMenuItems(
      AdvancedDropdownItem root,
      System.Type type,
      string path,
      int depth,
      bool setProperty)
    {
      if (type == null || type.IsEnum || depth >= 3)
        return;
      List<MemberInfo> fieldsAndProperties = ReflectionUtils.GetFieldsAndProperties(type, BindingFlags.Instance | BindingFlags.Public);
      fieldsAndProperties.Sort((Comparison<MemberInfo>) ((x, y) => string.CompareOrdinal(x.Name, y.Name)));
      foreach (MemberInfo memberInfo in fieldsAndProperties)
      {
        if (!TypeHelpers.FilterMember(memberInfo))
        {
          AdvancedDropdownItem root1 = root;
          string name1 = memberInfo.Name;
          System.Type memberUnderlyingType = ReflectionUtils.GetMemberUnderlyingType(memberInfo);
          bool flag = memberUnderlyingType.IsClass || (setProperty ? TypeHelpers.CanSetProperty(memberInfo) : TypeHelpers.CanGetProperty(memberInfo));
          if (flag)
          {
            if (TypeHelpers.HasProperties(memberUnderlyingType))
            {
              AdvancedDropdownItem child = new AdvancedDropdownItem(memberInfo.Name);
              root.AddChild(child);
              root1 = child;
            }
            string name2 = (path + "." + name1).Trim('.');
            root1.AddChild(new AdvancedDropdownItem(name2)
            {
              id = this.currentId
            });
            this.itemLookup.Add(this.currentId, (path + "." + name1).Trim('.'));
            ++this.currentId;
          }
          if (flag || memberUnderlyingType.IsSubclassOf(typeof (Component)))
            this.AddPropertyMenuItems(root1, memberUnderlyingType, path != "" ? path + "." + name1 : name1, depth + 1, setProperty);
        }
      }
    }

    private AdvancedDropdownItem FindChildItem(
      AdvancedDropdownItem root,
      string findName)
    {
      if (root == null)
        return (AdvancedDropdownItem) null;
      foreach (AdvancedDropdownItem child in root.children)
      {
        if (child.name == findName)
          return child;
      }
      return (AdvancedDropdownItem) null;
    }

    protected override void ItemSelected(AdvancedDropdownItem item)
    {
      string propertyName = this.itemLookup[item.id];
      Debug.Log((object) propertyName);
      if (this.fsmProperty == null)
        return;
      this.fsmProperty.SetPropertyName(propertyName);
      FsmEditor.SetFsmDirty(true);
      FsmEditor.SaveActions();
    }
  }
}
