// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.EditFsmState
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class EditFsmState
  {
    public static void LoadActionData(this FsmState state) => state.ActionData.InitEditorData(state);

    public static bool IsStartState(this FsmState state)
    {
      if (state.Fsm != null)
        return state.Fsm.StartState == state.Name;
      Debug.LogError((object) "State.Fsm == null!");
      return false;
    }

    public static FsmTransition AddTransition(this FsmState state) => state.AddTransition(string.Empty, (FsmEvent) null);

    public static FsmTransition AddTransition(
      this FsmState state,
      string toState,
      FsmEvent fsmEvent)
    {
      FsmTransition fsmTransition = new FsmTransition()
      {
        ToState = toState,
        FsmEvent = fsmEvent
      };
      state.Transitions = ArrayUtility.Add<FsmTransition>(state.Transitions, fsmTransition);
      return fsmTransition;
    }

    public static FsmTransition AddGlobalTransition(
      this FsmState state,
      FsmEvent fsmEvent)
    {
      if (state.Fsm != null)
        return state.Fsm.AddGlobalTransitionToState(state, fsmEvent);
      Debug.LogError((object) "State.Fsm == null!");
      return (FsmTransition) null;
    }

    public static void MoveTransitionUp(this FsmState state, FsmTransition transition)
    {
      int transitionIndex = EditFsmState.GetTransitionIndex(state, transition);
      if (transitionIndex <= 0)
        return;
      state.Transitions = ArrayUtility.MoveItem<FsmTransition>(state.Transitions, transitionIndex, transitionIndex - 1);
    }

    public static void MoveTransitionDown(this FsmState state, FsmTransition transition)
    {
      int transitionIndex = EditFsmState.GetTransitionIndex(state, transition);
      if (transitionIndex >= state.Transitions.Length - 1)
        return;
      state.Transitions = ArrayUtility.MoveItem<FsmTransition>(state.Transitions, transitionIndex, transitionIndex + 1);
    }

    public static int GetTransitionIndex(this FsmState state, FsmTransition transition)
    {
      int num = 0;
      foreach (FsmTransition transition1 in state.Transitions)
      {
        if (transition1 == transition)
          return num;
        ++num;
      }
      return -1;
    }

    public static void DeleteTransition(this FsmState state, FsmTransition transition) => state.Transitions = ArrayUtility.Remove<FsmTransition>(state.Transitions, transition);

    public static T AddAction<T>(this FsmState state) where T : FsmStateAction
    {
      FsmEditor.RecordUndo(Strings.Command_Add_Action);
      T instance = Activator.CreateInstance<T>();
      state.Actions = ArrayUtility.Add<FsmStateAction>(state.Actions, (FsmStateAction) instance);
      instance.Init(state);
      instance.Reset();
      FsmEditor.SaveActions(instance.State);
      return instance;
    }

    public static FsmStateAction AddAction(this FsmState state, System.Type actionType)
    {
      FsmEditor.RecordUndo(Strings.Command_Add_Action);
      FsmStateAction instance = (FsmStateAction) Activator.CreateInstance(actionType);
      state.Actions = ArrayUtility.Add<FsmStateAction>(state.Actions, instance);
      instance.Init(state);
      instance.Reset();
      FsmEditor.SaveActions(instance.State);
      return instance;
    }

    public static FsmStateAction InsertAction(
      this FsmState state,
      System.Type actionType,
      FsmStateAction beforeAction)
    {
      if (state == null)
        return (FsmStateAction) null;
      if (beforeAction == null)
        return state.AddAction(actionType);
      FsmEditor.RecordUndo(Strings.Command_Insert_Action);
      FsmStateAction instance = (FsmStateAction) Activator.CreateInstance(actionType);
      int actionIndex = Actions.GetActionIndex(state, beforeAction);
      if (actionIndex == -1)
        return state.AddAction(actionType);
      List<FsmStateAction> fsmStateActionList = new List<FsmStateAction>((IEnumerable<FsmStateAction>) state.Actions);
      fsmStateActionList.Insert(actionIndex, instance);
      state.Actions = fsmStateActionList.ToArray();
      instance.Init(state);
      instance.Reset();
      FsmEditor.SaveActions(instance.State);
      FsmEditor.UpdateActionUsage();
      return instance;
    }

    public static void DeleteAction(this FsmState state, FsmStateAction action)
    {
      if (state == null || action == null)
        return;
      FsmEditor.RecordUndo(Strings.Command_Delete_Action);
      state.Actions = ArrayUtility.Remove<FsmStateAction>(state.Actions, action);
      Keyboard.ResetFocus();
      FsmEditor.SaveActions(state);
      FsmEditor.UpdateActionUsage();
      FsmEditor.UpdateFsmInfo();
    }

    public static void DeleteActions(
      this FsmState state,
      IEnumerable<FsmStateAction> actions,
      bool undo = true)
    {
      if (state == null || actions == null)
        return;
      if (undo)
        FsmEditor.RecordUndo(Strings.Command_Delete_Actions);
      foreach (FsmStateAction action in actions)
        state.Actions = ArrayUtility.Remove<FsmStateAction>(state.Actions, action);
      Keyboard.ResetFocus();
      FsmEditor.SaveActions(state);
      FsmEditor.UpdateActionUsage();
      FsmEditor.UpdateFsmInfo();
    }

    public static void DeleteEvent(this FsmState state, FsmStateAction action, string fsmEventName)
    {
      if (state == null || action == null || string.IsNullOrEmpty(fsmEventName))
        return;
      EditFsmState.DeleteEvent((object) action, fsmEventName);
    }

    public static void DeleteEvent(object obj, string fsmEventName)
    {
      if (obj == null || string.IsNullOrEmpty(fsmEventName))
        return;
      foreach (FieldInfo field in ActionData.GetFields(obj.GetType()))
      {
        System.Type fieldType = field.FieldType;
        object obj1 = field.GetValue(obj);
        if (obj1 != null && fieldType == typeof (FsmEvent) && ((FsmEvent) obj1).Name == fsmEventName)
          field.SetValue(obj, (object) null);
      }
    }

    public static void SetColorIndex(this FsmState state, int colorIndex)
    {
      colorIndex = Mathf.Clamp(colorIndex, 0, PlayMakerPrefs.Colors.Length - 1);
      state.ColorIndex = colorIndex;
    }

    private static void RecordUndo(FsmState state, string name)
    {
      if (state == null || state.Fsm == null)
        return;
      FsmEditor.RecordUndo(state.Fsm, name);
    }
  }
}
