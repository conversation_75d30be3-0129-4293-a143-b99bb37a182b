// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmPrefabs
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using UnityEditor;

using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public static class FsmPrefabs
  {
    private static readonly Dictionary<string, bool> assetHasPlayMakerFSMLookup = new Dictionary<string, bool>();

    public static void LoadUsedPrefabs()
    {
      foreach (Fsm fsm in FsmEditor.FsmList)
      {
        if (!((UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null) && PrefabUtility.GetPrefabAssetType((UnityEngine.Object) fsm.Owner) != PrefabAssetType.NotAPrefab)
          PrefabUtility.GetCorrespondingObjectFromSource<MonoBehaviour>(fsm.Owner);
      }
    }

    public static PrefabType GetPrefabType(Fsm fsm)
    {
      if (fsm == null)
        return PrefabType.None;
      PrefabType prefabType = PrefabUtility.GetPrefabType((UnityEngine.Object) fsm.Owner);
      if (!EditorApplication.isPlaying)
        return prefabType;
      switch (prefabType)
      {
        case PrefabType.None:
        case PrefabType.Prefab:
        case PrefabType.ModelPrefab:
        case PrefabType.PrefabInstance:
        case PrefabType.ModelPrefabInstance:
        case PrefabType.MissingPrefabInstance:
          return prefabType;
        case PrefabType.DisconnectedPrefabInstance:
          return PrefabType.PrefabInstance;
        case PrefabType.DisconnectedModelPrefabInstance:
          return PrefabType.ModelPrefabInstance;
        default:
          return PrefabType.None;
      }
    }

    [Localizable(false)]
    public static bool IsModifiedPrefabInstance(Fsm fsm) => fsm != null && !((UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null) && PrefabUtility.IsPartOfPrefabInstance((UnityEngine.Object) fsm.Owner) && GUIHelpers.GetSerializedObject((UnityEngine.Object) fsm.Owner).FindProperty(nameof (fsm)).prefabOverride;

    public static void UpdateIsModifiedPrefabInstance(Fsm fsm)
    {
      if (fsm == null || (UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null)
        return;
      fsm.IsModifiedPrefabInstance = FsmPrefabs.IsModifiedPrefabInstance(fsm);
    }

    public static bool ShouldModify(Fsm fsm)
    {
      if (fsm == null || (UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null)
        return false;
      return !PrefabUtility.IsPartOfAnyPrefab((UnityEngine.Object) fsm.Owner) || FsmPrefabs.IsModifiedPrefabInstance(fsm);
    }

    public static bool IsPersistent(UnityEngine.Object obj) => obj != (UnityEngine.Object) null && EditorUtility.IsPersistent(obj);

    public static bool IsPartOfAnyPrefab(Fsm fsm) => fsm != null && !((UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null) && PrefabUtility.IsPartOfAnyPrefab((UnityEngine.Object) fsm.Owner);

    public static bool IsPrefab(Fsm fsm) => fsm != null && FsmPrefabs.IsPrefab((UnityEngine.Object) fsm.Owner);

    public static bool IsPrefabOrPreview(UnityEngine.Object obj)
    {
      if (FsmPrefabs.IsPrefab(obj))
        return true;
      PlayMakerFSM playMakerFsm = obj as PlayMakerFSM;
      return !((UnityEngine.Object) playMakerFsm == (UnityEngine.Object) null) && FsmPrefabs.IsPrefabPreview(playMakerFsm.Fsm);
    }

    public static bool IsPrefab(UnityEngine.Object obj) => !(obj == (UnityEngine.Object) null) && !((UnityEngine.Object) (obj as FsmTemplate) != (UnityEngine.Object) null) && PrefabUtility.IsPartOfPrefabAsset(obj);

    public static bool IsAddedComponentOverride(Fsm fsm) => fsm != null && !((UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null) && PrefabUtility.IsAddedComponentOverride((UnityEngine.Object) fsm.Owner);

    public static bool IsPersistent(Fsm fsm)
    {
      if (fsm == null)
        return false;
      return (bool) (UnityEngine.Object) fsm.UsedInTemplate || FsmPrefabs.IsPrefab(fsm);
    }

    public static bool IsNonAssetPrefabInstance(Fsm fsm) => fsm != null && !((UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null) && PrefabUtility.IsPartOfNonAssetPrefabInstance((UnityEngine.Object) fsm.Owner);

    public static bool IsPrefabInstance(Fsm fsm) => fsm != null && !((UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null) && PrefabUtility.IsPartOfPrefabInstance((UnityEngine.Object) fsm.Owner);

    public static bool IsPrefabPreview(Fsm fsm)
    {
      if (fsm == null || (UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null)
        return false;
      UnityEditor.SceneManagement.PrefabStage currentPrefabStage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
      return currentPrefabStage != null && currentPrefabStage.IsPartOfPrefabContents(fsm.GameObject);
    }

    public static bool IsFsmInstanceOfPrefab(Fsm fsm, Fsm prefab)
    {
      Debug.LogWarning((object) "Obsolete!");
      return false;
    }

    public static void BuildAssetsWithPlayMakerFSMsList() => FsmPrefabs.assetHasPlayMakerFSMLookup.Clear();

    public static bool AssetHasPlayMakerFSM(string guid)
    {
      bool flag;
      if (FsmPrefabs.assetHasPlayMakerFSMLookup.TryGetValue(guid, out flag))
        return flag;
      string assetPath = AssetDatabase.GUIDToAssetPath(guid);
      if (string.IsNullOrEmpty(assetPath))
        return false;
      foreach (Fsm fsm in FsmEditor.FsmList)
      {
        if (string.Compare(AssetDatabase.GetAssetPath(fsm.OwnerObject), assetPath, StringComparison.OrdinalIgnoreCase) == 0)
        {
          flag = true;
          break;
        }
      }
      FsmPrefabs.assetHasPlayMakerFSMLookup.Add(guid, flag);
      return flag;
    }

    public static bool StateExistsInPrefabParent(FsmState state)
    {
      Debug.LogWarning((object) "Obsolete!");
      return false;
    }

    public static bool ShouldDisconnectPrefabInstance(Fsm fsm)
    {
      if (!FsmEditorSettings.DisconnectModifiedInstances || fsm == null || ((UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null || !FsmPrefabs.IsNonAssetPrefabInstance(fsm)))
        return false;
      PlayMakerFSM prefabDefinition = fsm.Owner.GetPrefabDefinition() as PlayMakerFSM;
      if ((UnityEngine.Object) prefabDefinition == (UnityEngine.Object) null)
        return false;
      string str = "Disconnected Prefab Instance: " + Labels.GetFullFsmLabel(fsm) + "\n";
      if (prefabDefinition.UsesTemplate != fsm.FsmComponent.UsesTemplate)
      {
        if (prefabDefinition.UsesTemplate)
        {
          Debug.Log((object) (str + "Prefab uses a template and Instance doesn't."));
          return true;
        }
        Debug.Log((object) (str + "Instance uses a template and Prefab doesn't."));
        return true;
      }
      if (prefabDefinition.UsesTemplate && fsm.FsmComponent.UsesTemplate)
      {
        if (!((UnityEngine.Object) prefabDefinition.FsmTemplate != (UnityEngine.Object) fsm.FsmComponent.FsmTemplate))
          return false;
        Debug.Log((object) (str + "Prefab and Instance use different templates."));
        return true;
      }
      if (prefabDefinition.Fsm.States.Length != fsm.States.Length)
      {
        Debug.Log((object) (str + "Prefab and Instance have different number of states."));
        return true;
      }
      if (prefabDefinition.Fsm.Events.Length != fsm.Events.Length)
      {
        Debug.Log((object) (str + "Prefab and Instance have different number of events!"));
        return true;
      }
      if (prefabDefinition.Fsm.GlobalTransitions.Length != fsm.GlobalTransitions.Length)
      {
        Debug.Log((object) (str + "Prefab and Instance have different number of global transitions!"));
        return true;
      }
      for (int index = 0; index < prefabDefinition.Fsm.GlobalTransitions.Length; ++index)
      {
        FsmTransition globalTransition1 = prefabDefinition.Fsm.GlobalTransitions[index];
        FsmTransition globalTransition2 = fsm.GlobalTransitions[index];
        if (globalTransition1.EventName != globalTransition2.EventName || globalTransition1.ToState != globalTransition2.ToState)
        {
          Debug.Log((object) (str + "Prefab and Instance have different global transitions."));
          return true;
        }
      }
      for (int index = 0; index < prefabDefinition.Fsm.States.Length; ++index)
      {
        FsmState state1 = prefabDefinition.Fsm.States[index];
        FsmState state2 = fsm.States[index];
        if (state1.ActionData.ActionCount != state2.ActionData.ActionCount)
        {
          Debug.Log((object) (str + "Prefab and Instance have different actions."));
          return true;
        }
        if (state1.Transitions.Length != state2.Transitions.Length)
        {
          Debug.Log((object) (str + "Prefab and Instance have different transitions."));
          return true;
        }
      }
      for (int index1 = 0; index1 < prefabDefinition.Fsm.States.Length; ++index1)
      {
        FsmState state1 = prefabDefinition.Fsm.States[index1];
        FsmState state2 = fsm.States[index1];
        for (int index2 = 0; index2 < state1.ActionData.ActionCount; ++index2)
        {
          if (state2.ActionData.ActionNames[index2] != state1.ActionData.ActionNames[index2])
          {
            Debug.Log((object) (str + "Prefab and Instance have different action lists."));
            return true;
          }
        }
      }
      prefabDefinition.Fsm.Variables.Init();
      fsm.Variables.Init();
      NamedVariable[] allNamedVariables1 = prefabDefinition.Fsm.Variables.GetAllNamedVariables();
      NamedVariable[] allNamedVariables2 = fsm.Variables.GetAllNamedVariables();
      if (allNamedVariables1.Length != allNamedVariables2.Length)
      {
        Debug.Log((object) (str + "Prefab and Instance have different number of variables."));
        return true;
      }
      for (int index = 0; index < allNamedVariables1.Length; ++index)
      {
        NamedVariable namedVariable1 = allNamedVariables1[index];
        NamedVariable namedVariable2 = allNamedVariables2[index];
        if (namedVariable1.Name != namedVariable2.Name)
        {
          Debug.Log((object) (str + "Prefab and Instance have different variable names."));
          return true;
        }
        if (namedVariable1.GetType() != namedVariable2.GetType())
        {
          Debug.Log((object) (str + "Prefab and Instance have different variable types."));
          return true;
        }
      }
      return false;
    }

    public static void DisconnectPrefabInstance(Fsm fsm)
    {
      if (!FsmPrefabs.IsPrefabInstance(fsm))
        return;
      GameObject prefabInstanceRoot = PrefabUtility.GetOutermostPrefabInstanceRoot((UnityEngine.Object) fsm.GameObject);
      if ((UnityEngine.Object) prefabInstanceRoot != (UnityEngine.Object) null)
        PrefabUtility.UnpackPrefabInstance(prefabInstanceRoot, PrefabUnpackMode.OutermostRoot, InteractionMode.UserAction);
      UndoUtility.MarkSceneDirty();
    }

    public static bool DisconnectIfModifiedInstance(Fsm fsm)
    {
      if (!FsmPrefabs.IsNonAssetPrefabInstance(fsm) || !FsmPrefabs.ShouldDisconnectPrefabInstance(fsm))
        return false;
      FsmPrefabs.DisconnectPrefabInstance(fsm);
      return true;
    }

    public static void Apply() => FsmPrefabs.Apply((MonoBehaviour) FsmEditor.SelectedFsmComponent, true);

    public static void Apply(MonoBehaviour target, bool confirm = false)
    {
      if ((UnityEngine.Object) target == (UnityEngine.Object) null || confirm && !Dialogs.AreYouSure("PlayMaker", Strings.Dialog_Apply_Are_You_Sure))
        return;
      string nearestInstanceRoot = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot((UnityEngine.Object) target);
      if (!string.IsNullOrEmpty(nearestInstanceRoot))
        PrefabUtility.ApplyObjectOverride((UnityEngine.Object) target, nearestInstanceRoot, InteractionMode.UserAction);
      FsmEditor.RepaintAll();
    }

    public static void Revert() => FsmPrefabs.Revert(FsmEditor.SelectedFsmGameObject, FsmEditor.SelectedFsmOwner, true);

    public static void Revert(GameObject go, UnityEngine.Object obj, bool confirm = false)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null || obj == (UnityEngine.Object) null || confirm && !Dialogs.AreYouSure("PlayMaker", Strings.Dialog_Revert_Are_You_Sure))
        return;
      PrefabUtility.RevertObjectOverride(FsmEditor.SelectedFsmOwner, InteractionMode.UserAction);
      FsmEditor.StopEditingPrefabInstance();
      FsmEditor.SelectedFsm.Reload();
      FsmEditor.RepaintAll();
    }

    public static int GetPrefabComponentIndex(UnityEngine.Component component)
    {
      List<UnityEngine.Component> componentList = new List<UnityEngine.Component>((IEnumerable<UnityEngine.Component>) component.gameObject.GetComponents(component.GetType()));
      componentList.RemoveAll(new Predicate<UnityEngine.Component>(PrefabUtility.IsAddedComponentOverride));
      for (int index = 0; index < componentList.Count; ++index)
      {
        if ((UnityEngine.Object) componentList[index] == (UnityEngine.Object) component)
          return index;
      }
      return -1;
    }

    public static void ApplyAddedComponent() => FsmPrefabs.ApplyAddedComponent((UnityEngine.Component) FsmEditor.SelectedFsmComponent, true);

    public static void ApplyAddedComponent(UnityEngine.Component component, bool confirm = false)
    {
      if (confirm && !Dialogs.AreYouSure("PlayMaker", "Are you sure you want to add this FSM Component to the Prefab? \n\nAll Instances of the Prefab will have this new FSM!"))
        return;
      string nearestInstanceRoot = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot((UnityEngine.Object) component.gameObject);
      PrefabUtility.ApplyAddedComponent(component, nearestInstanceRoot, InteractionMode.UserAction);
    }

    public static void RevertAddedComponent() => FsmPrefabs.RevertAddedComponent((UnityEngine.Component) FsmEditor.SelectedFsmComponent, true);

    public static void RevertAddedComponent(UnityEngine.Component component, bool confirm = false)
    {
      if (confirm && !Dialogs.AreYouSure("PlayMaker", "Are you sure you want to revert to the Prefab? \n\nThis FSM Component will be deleted!"))
        return;
      if ((UnityEngine.Object) component == (UnityEngine.Object) FsmEditor.SelectedFsmComponent)
        FsmEditor.SelectNone();
      PrefabUtility.RevertAddedComponent(component, InteractionMode.UserAction);
    }

    public static void DebugPropertyModifications(UnityEngine.Object prefabInstance)
    {
      PropertyModification[] propertyModifications = PrefabUtility.GetPropertyModifications(prefabInstance);
      if (propertyModifications == null)
      {
        Debug.Log((object) "No PropertyModifications.");
      }
      else
      {
        string str = "PropertyModifications: " + (object) propertyModifications.Length + "\n";
        foreach (PropertyModification propertyModification in propertyModifications)
          str = str + propertyModification.propertyPath + "\n";
        Debug.Log((object) str);
      }
    }

    public static List<GameObject> FindAllPrefabInstances(UnityEngine.Object myPrefab)
    {
      List<GameObject> gameObjectList = new List<GameObject>();
      foreach (GameObject gameObject in Resources.FindObjectsOfTypeAll<GameObject>())
      {
        if (PrefabUtility.IsPartOfAnyPrefab((UnityEngine.Object) gameObject))
          PrefabUtility.IsPartOfPrefabInstance((UnityEngine.Object) gameObject);
      }
      return gameObjectList;
    }
  }
}
