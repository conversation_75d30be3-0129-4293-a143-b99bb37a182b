// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmEnumEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class FsmEnumEditor : FsmVariableEditor
  {
    private readonly FsmEnum fsmEnum;

    public FsmEnumEditor(FsmVariable variable)
      : base(variable)
    {
      this.fsmEnum = variable.NamedVar as FsmEnum;
      this.valueProp = this.serializedOwner.FindProperty(variable.PropertyPath + ".intValue");
    }

    public override void DoValueField(GUIContent label, bool isAsset) => GUIHelpers.EnumFromIntPropertyField(this.valueProp, label, this.fsmEnum.EnumType);

    public override void DoTypeSelector()
    {
      if (VariableTypeMenu.IsTypeAliased(this.fsmVariable.NamedVar.ObjectType))
        return;
      GUILayout.BeginHorizontal();
      FsmEditorGUILayout.PrefixLabel("Enum Type");
      Rect rect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.popup);
      if (GUI.Button(rect, FsmEditorContent.TempContent(this.fsmVariable.TypeNameShort, this.fsmVariable.TypeName), EditorStyles.popup))
      {
        if (FsmEditorSettings.UseAdvancedDropdowns)
          VariableTypeDropdown.ShowEnumsTypeDropdown(rect, new GenericMenu.MenuFunction2(this.SelectEnumTypeDropdown));
        else
          VariableTypeMenu.ShowEnumTypesMenu(this.fsmVariable, new GenericMenu.MenuFunction2(this.SelectEnumType));
      }
      GUILayout.EndHorizontal();
    }

    private void SelectEnumTypeDropdown(object userdata)
    {
      EditorCommands.ChangeVariableObjectType(this.fsmVariable, VariableType.Enum, (System.Type) userdata);
      this.fsmEnum.Init();
    }

    private void SelectEnumType(object userdata)
    {
      VariableTypeMenu.MenuItemSelectionData itemSelectionData = (VariableTypeMenu.MenuItemSelectionData) userdata;
      EditorCommands.ChangeVariableObjectType(this.fsmVariable, itemSelectionData.variableType, itemSelectionData.objectType);
      this.fsmEnum.Init();
    }
  }
}
