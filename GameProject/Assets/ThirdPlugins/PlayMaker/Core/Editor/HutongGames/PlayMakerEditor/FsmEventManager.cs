// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmEventManager
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using HutongGames.PlayMakerEditorData;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class FsmEventManager
  {
    private List<FsmEventManager.EventLine> eventList = new List<FsmEventManager.EventLine>();
    private ScrollView _scrollView;
    private FsmEvent selectedEvent;
    private string newEventName = "";
    private bool sortByUsageCount;
    private Rect globalCheckboxRect;
    private Rect eventNameRect;
    private Rect usedCountRect;
    private Rect deleteButtonRect;
    private string savedSelection;

    private ScrollView scrollView => this._scrollView ?? (this._scrollView = new ScrollView(FsmEditor.Window));

    public void Init()
    {
      FsmEvent.EventList.Sort();
      this.UpdateEventList();
    }

    public void Reset()
    {
      this.DeselectAll();
      this.UpdateEventList();
      FsmEditor.Repaint(true);
      FsmEditor.RepaintAll();
    }

    public void DeselectAll()
    {
      Keyboard.ResetFocus();
      this.selectedEvent = (FsmEvent) null;
      this.newEventName = "";
      FsmEditor.Repaint(true);
    }

    private void UpdateEventList()
    {
      foreach (Fsm fsm in FsmEditor.FsmList)
        FsmEventManager.SanityCheckEventList(fsm);
      if (FsmEditor.SelectedFsm != null)
      {
        FsmSearch.Update(FsmEditor.SelectedFsm);
        FsmEditor.SelectedFsm.InitEvents();
        FsmEventManager.SanityCheckEventList(FsmEditor.SelectedFsm);
        this.eventList = new List<FsmEventManager.EventLine>();
        foreach (FsmEvent fsmEvent in FsmEditor.SelectedFsm.Events)
          this.eventList.Add(new FsmEventManager.EventLine(fsmEvent));
        this.SortEvents();
      }
      else
        this.eventList = new List<FsmEventManager.EventLine>();
    }

    private GenericMenu GenerateEventManagerMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent("Sort By/Event Name"), !this.sortByUsageCount, (GenericMenu.MenuFunction) (() => this.SortByUsedCount(false)));
      genericMenu.AddItem(new GUIContent("Sort By/Used Count"), this.sortByUsageCount, (GenericMenu.MenuFunction) (() => this.SortByUsedCount(true)));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Remove_Unused_Events), false, new GenericMenu.MenuFunction(this.DeleteUnusedEvents));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Event_Browser), false, new GenericMenu.MenuFunction(FsmEditor.OpenGlobalEventsWindow));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Online_Help), false, new GenericMenu.MenuFunction(FsmEventManager.OpenOnlineHelp));
      return genericMenu;
    }

    private void SortByUsedCount(bool state)
    {
      this.sortByUsageCount = state;
      this.UpdateEventList();
    }

    private static void OpenOnlineHelp() => EditorCommands.OpenWikiPage(WikiPages.EventManager);

    private void DeleteUnusedEvents()
    {
      FsmSearch.Update(FsmEditor.SelectedFsm);
      List<string> unusedEvents = FsmSearch.GetUnusedEvents(FsmEditor.SelectedFsm);
      int count = unusedEvents.Count;
      if (count == 0)
      {
        EditorUtility.DisplayDialog(Strings.Dialog_Delete_Unused_Events, Strings.Dialog_No_unused_events, Strings.OK);
      }
      else
      {
        if (Dialogs.YesNoDialog(Strings.Dialog_Delete_Unused_Events, string.Format(Strings.Dialog_Delete_Unused_Events_Are_you_sure, (object) count)))
        {
          FsmEditor.RecordUndo(Strings.Dialog_Delete_Unused_Events);
          foreach (string str in unusedEvents)
          {
            if (!FsmEvent.IsEventGlobal(str))
              FsmEditor.SelectedFsm.DeleteEvent(str);
          }
          this.Reset();
        }
        FsmEditor.SetFsmDirty(true);
      }
    }

    private void SortEvents()
    {
      if (this.sortByUsageCount)
        this.eventList.Sort(new Comparison<FsmEventManager.EventLine>(FsmEventManager.CompareEventsByUseCount));
      else
        this.eventList.Sort(new Comparison<FsmEventManager.EventLine>(FsmEventManager.CompareEventsByName));
    }

    private static int CompareEventsByName(
      FsmEventManager.EventLine event1,
      FsmEventManager.EventLine event2)
    {
      return string.Compare(event1.fsmEvent.Name, event2.fsmEvent.Name, StringComparison.Ordinal);
    }

    private static int CompareEventsByUseCount(
      FsmEventManager.EventLine event1,
      FsmEventManager.EventLine event2)
    {
      if (event1 == null)
        return event2 == null ? 0 : -1;
      if (event2 == null)
        return 1;
      int num = event2.count.CompareTo(event1.count);
      return num == 0 ? string.Compare(event1.label.text, event2.label.text, StringComparison.CurrentCulture) : num;
    }

    public void OnGUI()
    {
      if (FsmEditor.SelectedFsm == null)
        GUILayout.FlexibleSpace();
      else if (InspectorPanel.DoUsesTemplateUI())
      {
        GUILayout.FlexibleSpace();
      }
      else
      {
        this.DoTableHeaders();
        this.DoEventTable();
        if (Event.current.type == UnityEngine.EventType.ContextClick)
          this.GenerateEventManagerMenu().ShowAsContext();
        GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG);
        this.DoEventEditor();
        GUILayout.Space(2f);
        if (GUILayout.Button(FsmEditorContent.EventBrowserButtonLabel))
        {
          FsmEditor.OpenGlobalEventsWindow();
          GUIUtility.ExitGUI();
        }
        if (FsmEditorSettings.ShowHints)
          GUILayout.Box(Strings.Tooltip_Event_Browser_Button_in_Events_Tab, FsmEditorStyles.HintBox);
        GUILayout.EndVertical();
      }
    }

    private void DoTableHeaders()
    {
      GUILayout.BeginVertical(FsmEditorStyles.TopBarBG);
      EditorGUILayout.BeginHorizontal();
      EditorGUI.BeginChangeCheck();
      GUILayout.BeginHorizontal(FsmEditorStyles.TableRowBox);
      GUILayout.Space(-5f);
      GUILayout.Toggle(false, FsmEditorContent.EventBroadcastIcon, FsmEditorStyles.TableRowHeader, GUILayout.Width(18f), GUILayout.Height(16f));
      GUILayout.Space(3f);
      if (GUILayout.Button(FsmEditorContent.EventHeaderLabel, FsmEditorStyles.TableRowHeader, GUILayout.Width(FsmEditor.InspectorPanelWidth - 85f)))
        this.sortByUsageCount = false;
      if (GUILayout.Button(FsmEditorContent.EventUsedHeaderLabel, FsmEditorStyles.TableRowHeader))
        this.sortByUsageCount = true;
      if (FsmEditorGUILayout.SettingsButton())
        this.GenerateEventManagerMenu().ShowAsContext();
      GUILayout.EndHorizontal();
      if (EditorGUI.EndChangeCheck())
        this.SortEvents();
      EditorGUILayout.EndHorizontal();
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box(Strings.Hint_EventManager, FsmEditorStyles.HintBox);
      GUILayout.EndVertical();
    }

    private void DoEventTable()
    {
      this.scrollView.Begin(true);
      if (this.eventList.Count == 0)
      {
        GUILayout.BeginHorizontal(FsmEditorStyles.TableRowBox);
        GUILayout.Label(Strings.Label_None_In_Table);
        GUILayout.EndHorizontal();
      }
      List<FsmEventManager.EventLine> eventLineList = new List<FsmEventManager.EventLine>();
      for (int index = 0; index < this.eventList.Count; ++index)
      {
        FsmEventManager.EventLine eventLine = this.eventList[index];
        if (!eventLine.isOutput)
          this.DoEventLine(eventLine);
        else
          eventLineList.Add(eventLine);
      }
      if (eventLineList.Count > 0)
      {
        GUILayout.Space(5f);
        GUILayout.Box(FsmEditorContent.OutputsSection, FsmEditorStyles.SectionTitle);
        FsmEditorGUILayout.LightDivider();
        foreach (FsmEventManager.EventLine eventLine in eventLineList)
          this.DoEventLine(eventLine);
      }
      if (GUIHelpers.ClickableEmptySpace())
        this.DeselectAll();
      this.scrollView.End();
    }

    public static bool IsOutputEvent(string eventName) => FsmEditor.SelectedFsm != null && FsmEditor.SelectedFsm.OutputEvents.Find((Predicate<FsmEvent>) (x => x.Name == eventName)) != null;

    public static bool IsOutputEvent(FsmEvent fsmEvent) => FsmEditor.SelectedFsm != null && FsmEditor.SelectedFsm.OutputEvents.Find((Predicate<FsmEvent>) (x => x.Name == fsmEvent.Name)) != null;

    private void DoEventLine(FsmEventManager.EventLine eventLine)
    {
      int num = Event.current.type == UnityEngine.EventType.Repaint ? 1 : 0;
      Rect rect = GUILayoutUtility.GetRect(GUIContent.none, FsmEditorStyles.TableRowBox);
      bool flag = this.selectedEvent != null && eventLine.label.text == this.selectedEvent.Name;
      if (num != 0)
      {
        if (flag)
          FsmEditorStyles.SelectedEventBox.Draw(rect);
        else
          FsmEditorStyles.TableRowBoxNoDivider.Draw(rect);
      }
      GUIStyle style1;
      GUIStyle style2;
      if (flag)
      {
        style1 = FsmEditorStyles.TableRowTextSelected;
        style2 = FsmEditorStyles.TableRowTextSelectedAlignRight;
      }
      else
      {
        style1 = FsmEditorStyles.TableRowText;
        style2 = FsmEditorStyles.TableRowTextAlignRight;
      }
      this.globalCheckboxRect.Set(3f, rect.y + 2f, 15f, rect.height - 2f);
      this.eventNameRect.Set(this.globalCheckboxRect.xMax + (float) style1.margin.left, rect.y, rect.width - 75f - (float) style1.margin.horizontal, rect.height);
      this.usedCountRect.Set(this.eventNameRect.xMax, rect.y, 30f, rect.height);
      this.deleteButtonRect.Set(rect.xMax - 20f, rect.y + 3f, 20f, rect.height);
      EditorGUI.BeginDisabledGroup(eventLine.isSystemEvent);
      bool isGlobal = GUI.Toggle(this.globalCheckboxRect, eventLine.isGlobal, FsmEditorContent.GlobalEventTooltipLabel, FsmEditorStyles.TableRowCheckBox);
      if (isGlobal != eventLine.isGlobal)
      {
        EditPlayMakerGlobals.SetEventIsGlobal(eventLine.fsmEvent, isGlobal);
        GlobalEventsWindow.ResetView();
      }
      EditorGUI.EndDisabledGroup();
      if (GUI.Button(this.eventNameRect, eventLine.label, style1))
      {
        this.SelectEvent(eventLine.fsmEvent);
        GUIUtility.keyboardControl = 0;
        if (Event.current.button == 1 || EditorGUI.actionKey)
          Menus.DoEventUsageContextMenu(FsmEditor.SelectedFsm, this.selectedEvent);
      }
      GUI.Label(this.usedCountRect, eventLine.countLabel, style2);
      if (FsmEditorGUI.DeleteButton(this.deleteButtonRect))
      {
        EditorCommands.DeleteEvent(eventLine.fsmEvent);
        this.Reset();
      }
      if (!flag)
        return;
      this.scrollView.SetAutoScrollTarget(rect);
    }

    private void DoEventEditor()
    {
      if ((UnityEngine.Object) EditorWindow.focusedWindow != (UnityEngine.Object) FsmEditor.Window)
        return;
      EditorGUIUtility.labelWidth = 86f;
      bool editingEvent = !FsmEvent.IsNullOrEmpty(this.selectedEvent);
      if (editingEvent && FsmEditorSettings.ShowHints)
        GUILayout.Box(this.selectedEvent.IsSystemEvent ? Strings.Hint_System_Events_cannot_be_renamed : Strings.Hint_Use_Event_Browser_to_rename_globally, FsmEditorStyles.HintBox);
      if (editingEvent && this.selectedEvent.IsGlobal)
        FsmEditorGUILayout.ReadonlyTextField(FsmEditorContent.GlobalEventName, this.newEventName);
      else if (!editingEvent || !this.selectedEvent.IsSystemEvent)
      {
        EditorGUILayout.BeginHorizontal();
        this.newEventName = EditorGUILayout.TextField(editingEvent ? FsmEditorContent.EditEventNameLabel : FsmEditorContent.AddEventLabel, this.newEventName);
        string eventName = FsmEditorGUILayout.FsmEventListPopup();
        if (eventName != "")
        {
          this.AddEvent(eventName);
          return;
        }
        EditorGUILayout.EndHorizontal();
        if (!editingEvent && FsmEditorSettings.ShowHints)
          GUILayout.Box(Strings.Tooltip_EventManager_Add_Event, FsmEditorStyles.HintBox);
      }
      if (editingEvent)
      {
        bool flag1 = false;
        foreach (FsmEvent exposedEvent in FsmEditor.SelectedFsm.ExposedEvents)
        {
          if (exposedEvent.Name == this.selectedEvent.Name)
          {
            flag1 = true;
            break;
          }
        }
        bool flag2 = EditorGUILayout.Toggle(FsmEditorContent.EventInspectorLabel, flag1);
        if (flag1 != flag2)
        {
          if (!flag2)
            EditorCommands.RemoveExposedEvent(FsmEditor.SelectedFsm, this.selectedEvent);
          else
            EditorCommands.AddExposedEvent(FsmEditor.SelectedFsm, this.selectedEvent);
          FsmEditor.OnControlsChanged();
        }
        bool flag3 = FsmEventManager.IsOutputEvent(FsmEditor.SelectedFsm, this.selectedEvent);
        bool flag4 = EditorGUILayout.Toggle(FsmEditorContent.IsOutputEventLabel, flag3);
        if (flag3 != flag4)
        {
          if (!flag4)
            EditorCommands.RemoveOutputEvent(FsmEditor.SelectedFsm, this.selectedEvent);
          else
            EditorCommands.AddOutputEvent(FsmEditor.SelectedFsm, this.selectedEvent);
          this.UpdateEventList();
          FsmEditor.OnControlsChanged();
          GUIHelpers.SafeExitGUI();
        }
        if (FsmEditorSettings.ShowHints)
          GUILayout.Box(Strings.Hint_EventManager_Expose_Events, FsmEditorStyles.HintBox);
      }
      string text = this.ValidateEventName(editingEvent);
      bool flag = string.IsNullOrEmpty(text);
      if (!flag)
        GUILayout.Box(text, FsmEditorStyles.ErrorBox);
      if (!Event.current.isKey)
        return;
      if (flag && Keyboard.EnterKeyPressed())
      {
        if (!editingEvent)
          this.AddEvent(this.newEventName);
        else
          this.RenameEvent(this.selectedEvent, this.newEventName);
        Event.current.Use();
        GUIUtility.ExitGUI();
      }
      else
      {
        if (Event.current.keyCode != KeyCode.Escape)
          return;
        this.Reset();
      }
    }

    private string ValidateEventName(bool editingEvent)
    {
      if (editingEvent && this.selectedEvent.IsSystemEvent)
        return (string) null;
      return !editingEvent ? FsmEditor.SelectedFsm.ValidateAddEvent(this.newEventName) : FsmEventManager.ValidateRenameEvent(this.selectedEvent, this.newEventName);
    }

    public void SelectEvent(FsmEvent fsmEvent, bool syncSelection = true)
    {
      if (syncSelection)
        GlobalEventsWindow.SyncSelection(fsmEvent);
      this.selectedEvent = fsmEvent;
      this.savedSelection = fsmEvent != null ? fsmEvent.Name : "";
      if (FsmEvent.IsNullOrEmpty(fsmEvent))
      {
        this.newEventName = "";
      }
      else
      {
        this.newEventName = !fsmEvent.IsSystemEvent ? fsmEvent.Name : "";
        this.scrollView.AutoScroll();
        FsmEditor.Repaint(true);
      }
    }

    [Localizable(false)]
    private void AddEvent(string eventName)
    {
      if (eventName.Replace(" ", "") == "")
      {
        EditorApplication.Beep();
        EditorUtility.DisplayDialog(Strings.Label_Add_Event, Strings.Error_Invalid_Name, Strings.OK);
      }
      else
      {
        EditorCommands.AddEvent(eventName);
        this.Reset();
      }
    }

    private void RenameEvent(FsmEvent fsmEvent, string newName)
    {
      Fsm selectedFsm = FsmEditor.SelectedFsm;
      if (selectedFsm == null || fsmEvent == null)
        return;
      if (!FsmEditor.DisconnectCheck())
      {
        this.SelectEvent(fsmEvent);
      }
      else
      {
        int num = FsmEventManager.IsOutputEvent(selectedFsm, fsmEvent) ? 1 : 0;
        if (num != 0)
          selectedFsm.OutputEvents.RemoveAll((Predicate<FsmEvent>) (e => e.Name == fsmEvent.Name));
        if (fsmEvent.IsGlobal)
        {
          Dialogs.OkDialog("Global Events cannot be renamed!");
        }
        else
        {
          FsmEditor.RecordUndo(Strings.Command_Rename_Event);
          string name = fsmEvent.Name;
          selectedFsm.RenameEvent(fsmEvent.Name, newName);
          GlobalEventsWindow.UpdateView(name);
          GlobalEventsWindow.UpdateView(newName);
        }
        if (num != 0)
          selectedFsm.OutputEvents.Add(fsmEvent);
        selectedFsm.Events = ArrayUtility.Sort<FsmEvent>(selectedFsm.Events);
        this.Reset();
        this.SelectEvent(FsmEditor.SelectedFsm.FindEvent(newName));
      }
    }

    public static bool IsOutputEvent(Fsm fsm, FsmEvent fsmEvent)
    {
      if (FsmEvent.IsNullOrEmpty(fsmEvent))
        return false;
      foreach (FsmEvent outputEvent in fsm.OutputEvents)
      {
        if (outputEvent != null && outputEvent.Name == fsmEvent.Name)
          return true;
      }
      return false;
    }

    public static void SanityCheckEventList(Fsm fsm)
    {
      if (!fsm.Initialized || FsmPrefabs.IsPrefabInstance(fsm))
        return;
      bool flag = false;
      List<FsmEvent> source = new List<FsmEvent>((IEnumerable<FsmEvent>) fsm.Events);
      List<string> list = source.Select<FsmEvent, string>((Func<FsmEvent, string>) (x => x.Name)).ToList<string>();
      foreach (FsmState state in fsm.States)
      {
        foreach (FsmTransition transition in state.Transitions)
        {
          if (!string.IsNullOrEmpty(transition.EventName) && !list.Contains(transition.EventName))
          {
            source.Add(FsmEvent.GetFsmEvent(transition.EventName));
            list.Add(transition.EventName);
          }
        }
      }
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (!string.IsNullOrEmpty(globalTransition.EventName) && !list.Contains(globalTransition.EventName))
        {
          source.Add(FsmEvent.GetFsmEvent(globalTransition.EventName));
          list.Add(globalTransition.EventName);
        }
      }
      fsm.Events = source.ToArray();
      List<FsmEvent> fsmEventList1 = new List<FsmEvent>();
      foreach (FsmEvent fsmEvent in fsm.Events)
      {
        if (!FsmEvent.EventListContainsEvent(fsmEventList1, fsmEvent.Name))
        {
          fsmEventList1.Add(fsmEvent);
        }
        else
        {
          flag = true;
          UnityEngine.Debug.LogError((object) string.Format(Strings.Error_Duplicate_Event_Found__, (object) fsmEvent.Name));
          UnityEngine.Debug.LogError((object) FsmUtility.GetFullFsmLabel(fsm));
        }
      }
      List<FsmEvent> fsmEventList2 = new List<FsmEvent>();
      foreach (FsmEvent outputEvent in fsm.OutputEvents)
      {
        if (outputEvent != null)
        {
          if (fsm.HasEvent(outputEvent.Name))
            fsmEventList2.Add(outputEvent);
          else
            flag = true;
        }
      }
      fsm.OutputEvents = fsmEventList2;
      if (!flag)
        return;
      fsm.Events = fsmEventList1.ToArray();
      FsmEditor.SetFsmDirty(fsm, false);
    }

    public static string ValidateRenameEvent(FsmEvent fsmEvent, string newEventName) => fsmEvent == null || newEventName == null || !(fsmEvent.Name == newEventName) && newEventName.Replace(" ", "") == "" ? Strings.Error_Invalid_Name : "";

    public void AfterUndo()
    {
      if (FsmEditor.SelectedFsm == null)
        return;
      this.Reset();
      FsmEvent fsmEvent = FsmEditor.SelectedFsm.FindEvent(this.savedSelection);
      if (fsmEvent == null)
        return;
      this.SelectEvent(fsmEvent);
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("FsmEventManager: " + message));

    private class EventLine
    {
      public readonly FsmEvent fsmEvent;
      public readonly GUIContent label;
      public readonly int count;
      public readonly string countLabel;
      public readonly bool isOutput;

      public bool isSystemEvent => this.fsmEvent.IsSystemEvent;

      public bool isGlobal => this.fsmEvent.IsGlobal;

      public EventLine(FsmEvent fsmEvent)
      {
        this.fsmEvent = fsmEvent;
        this.label = new GUIContent(fsmEvent.Name, EventEditorData.GetEventTooltip(fsmEvent));
        this.count = FsmSearch.GetEventUseCount(FsmEditor.SelectedFsm, fsmEvent.Name);
        this.countLabel = this.count >= 0 ? this.count.ToString((IFormatProvider) CultureInfo.CurrentCulture) : "-";
        this.isOutput = FsmEventManager.IsOutputEvent(fsmEvent);
      }
    }
  }
}
