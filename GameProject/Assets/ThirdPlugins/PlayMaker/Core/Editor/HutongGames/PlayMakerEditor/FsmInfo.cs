// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmInfo
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class FsmInfo
  {
    public const int MAX_SEARCH_DEPTH = 7;
    public object targetObject;
    public GUIContent guiContent;
    private static FsmInfo currentInfo;
    private static FsmEventTarget currentEventTarget;
    private static int currentArrayIndex;
    private const string div = " : ";

    public PlayMakerFSM fsmComponent { get; set; }

    public Fsm fsm { get; set; }

    public FsmState state { get; set; }

    public FsmTransition transition { get; set; }

    public FsmStateAction action { get; set; }

    public int actionIndex { get; set; }

    public object fieldInObject { get; set; }

    public FieldInfo field { get; set; }

    [Obsolete("Use variableName instead.")]
    public NamedVariable variable { get; set; }

    public string variableName { get; set; }

    public string eventName { get; set; }

    public FsmEventTarget eventTarget { get; set; }

    public Array fieldInArray { get; set; }

    public int arrayIndex { get; set; }

    public string stateName => this.state == null ? "" : this.state.Name;

    public FsmInfo()
    {
    }

    public FsmInfo(FsmInfo source)
    {
      this.fsmComponent = source.fsmComponent;
      this.fsm = source.fsm;
      this.state = source.state;
      this.transition = source.transition;
      this.action = source.action;
      this.actionIndex = source.actionIndex;
      this.variableName = source.variableName;
      this.field = source.field;
      this.fieldInObject = source.fieldInObject;
      this.eventName = source.eventName;
      this.eventTarget = source.eventTarget;
      this.targetObject = source.targetObject;
      this.fieldInArray = source.fieldInArray;
      this.arrayIndex = source.arrayIndex;
    }

    public bool IsValid() => this.fsm != null && this.fsm.OwnerObject != (UnityEngine.Object) null;

    public string AsMenuItemShort(bool includeField = false)
    {
      if (this.fsm == null && !string.IsNullOrEmpty(this.stateName))
        return "";
      Labels.UpdateAutoName(this.action);
      string str = this.stateName;
      if (this.action != null)
      {
        str = str + " : " + Labels.GetActionLabel(this.action);
        if (includeField && this.field != null)
          str = str + " : " + this.field.Name;
      }
      return str;
    }

    public string AsMenuItem(bool flat = false, bool includeField = true)
    {
      if (this.fsm == null)
        return "";
      Labels.UpdateAutoName(this.action);
      string str = FsmInfo.GetFsmMenuRoot(this.fsm, flat);
      if (!string.IsNullOrEmpty(this.stateName))
      {
        if (str != "")
          str += " : ";
        str += this.stateName;
        if (this.action != null)
        {
          str = str + " : " + Labels.GetActionLabel(this.action);
          if (includeField && this.field != null)
            str = str + " : " + this.field.Name;
        }
      }
      return str;
    }

    public string AsMenuItemActionSelector()
    {
      Labels.UpdateAutoName(this.action);
      return Labels.GetFullStateLabel(this.state) + " : " + Labels.GetActionLabel(this.action);
    }

    private static string GetFsmMenuRoot(Fsm fsm, bool flat = false)
    {
      if (fsm == null)
        return "";
      string str = "/";
      if (flat)
        str = " : ";
      return (UnityEngine.Object) fsm.UsedInTemplate != (UnityEngine.Object) null ? fsm.UsedInTemplate.name + str + fsm.Name : fsm.OwnerName + str + fsm.Name;
    }

    public void DelayedSelect()
    {
      EditorApplication.update -= new EditorApplication.CallbackFunction(this.DelayedSelect);
      this.Select();
    }

    public void Select()
    {
      if (this.fsm == null)
        return;
      if (FsmEditor.Instance == null)
      {
        FsmEditor.Open(this);
      }
      else
      {
        FsmEditor.SelectFsm(this.fsm);
        if (this.state == null)
          return;
        FsmEditor.SelectState(this.state, true);
        if (this.action == null || this.actionIndex < 0 || this.actionIndex >= this.state.Actions.Length)
          return;
        FsmEditor.SelectAction(this.state.Actions[this.actionIndex]);
        FsmEditor.InspectorHasFocus = true;
      }
    }

    public static void SelectFsmInfo(object obj)
    {
      if (!(obj is FsmInfo fsmInfo))
        return;
      fsmInfo.Select();
    }

    public static List<FsmInfo> FindTransitionsUsingEvent(string eventName) => FsmInfo.FindTransitionsUsingEvent(FsmEditor.FsmList, eventName);

    public static List<FsmInfo> FindTransitionsUsingEvent(
      List<Fsm> fsmSelection,
      string eventName)
    {
      List<FsmInfo> fsmInfoList = new List<FsmInfo>();
      foreach (Fsm fsm in fsmSelection)
        fsmInfoList.AddRange((IEnumerable<FsmInfo>) FsmInfo.FindTransitionsUsingEvent(fsm, eventName));
      return fsmInfoList;
    }

    public static List<FsmInfo> FindTransitionsUsingEvent(Fsm fsm, string eventName)
    {
      List<FsmInfo> fsmInfoList = new List<FsmInfo>();
      foreach (FsmState state in fsm.States)
      {
        foreach (FsmTransition transition in state.Transitions)
        {
          if (transition.EventName == eventName)
            fsmInfoList.Add(new FsmInfo()
            {
              fsm = fsm,
              state = state,
              transition = transition
            });
        }
      }
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (globalTransition.EventName == eventName)
          fsmInfoList.Add(new FsmInfo()
          {
            fsm = fsm,
            state = fsm.GetState(globalTransition.ToState),
            transition = globalTransition
          });
      }
      return fsmInfoList;
    }

    public static bool DoesActionEventTargetLocalTransition(FsmInfo actionEvent, FsmState state)
    {
      if (actionEvent.fsm == state.Fsm && actionEvent.state != state)
        return false;
      FsmEventTarget fsmEventTarget = actionEvent.eventTarget ?? FsmEventTarget.TargetSelf;
      switch (fsmEventTarget.target)
      {
        case FsmEventTarget.EventTarget.Self:
          return actionEvent.fsm == state.Fsm && actionEvent.state == state;
        case FsmEventTarget.EventTarget.GameObject:
          return (UnityEngine.Object) actionEvent.fsm.GetOwnerDefaultTarget(fsmEventTarget.gameObject) == (UnityEngine.Object) state.Fsm.GameObject;
        case FsmEventTarget.EventTarget.GameObjectFSM:
          string str = actionEvent.eventTarget.fsmName.Value;
          if (!((UnityEngine.Object) actionEvent.fsm.GetOwnerDefaultTarget(fsmEventTarget.gameObject) == (UnityEngine.Object) state.Fsm.GameObject))
            return false;
          return string.IsNullOrEmpty(str) || str == state.Fsm.Name;
        case FsmEventTarget.EventTarget.FSMComponent:
          return (UnityEngine.Object) fsmEventTarget.fsmComponent == (UnityEngine.Object) state.Fsm.Owner;
        case FsmEventTarget.EventTarget.BroadcastAll:
          return !fsmEventTarget.excludeSelf.Value || actionEvent.fsm != state.Fsm;
        default:
          return false;
      }
    }

    public static bool DoesActionEventTargetGlobalTransition(FsmInfo actionEvent, Fsm fsm)
    {
      FsmEventTarget fsmEventTarget = actionEvent.eventTarget ?? FsmEventTarget.TargetSelf;
      switch (fsmEventTarget.target)
      {
        case FsmEventTarget.EventTarget.Self:
          return actionEvent.fsm == fsm;
        case FsmEventTarget.EventTarget.GameObject:
          return (UnityEngine.Object) actionEvent.fsm.GetOwnerDefaultTarget(fsmEventTarget.gameObject) == (UnityEngine.Object) fsm.GameObject;
        case FsmEventTarget.EventTarget.GameObjectFSM:
          string str = actionEvent.eventTarget.fsmName.Value;
          if (!((UnityEngine.Object) actionEvent.fsm.GetOwnerDefaultTarget(fsmEventTarget.gameObject) == (UnityEngine.Object) fsm.GameObject))
            return false;
          return string.IsNullOrEmpty(str) || str == fsm.Name;
        case FsmEventTarget.EventTarget.FSMComponent:
          return (UnityEngine.Object) fsmEventTarget.fsmComponent == (UnityEngine.Object) fsm.Owner;
        case FsmEventTarget.EventTarget.BroadcastAll:
          return !fsmEventTarget.excludeSelf.Value || actionEvent.fsm != fsm;
        default:
          return false;
      }
    }

    [Obsolete("Use FsmSearch instead.")]
    private static IEnumerable<FsmInfo> FindEventUsage(
      object obj,
      string eventName,
      int depth = 0)
    {
      List<FsmInfo> fsmInfoList = new List<FsmInfo>();
      if (obj != null && depth < 7)
      {
        foreach (FieldInfo field in obj.GetType().GetFields(BindingFlags.Instance | BindingFlags.Public))
        {
          if (!CustomAttributeHelpers.HasAttribute<EventNotSentAttribute>(field))
          {
            System.Type fieldType = field.FieldType;
            object fieldValue = field.GetValue(obj);
            if (fieldType == typeof (FsmEventTarget))
              FsmInfo.currentEventTarget = fieldValue as FsmEventTarget;
            if (FsmInfo.IsEventUsedInField(fieldType, fieldValue, eventName))
              fsmInfoList.Add(new FsmInfo(FsmInfo.currentInfo)
              {
                field = field,
                eventTarget = FsmInfo.currentEventTarget,
                arrayIndex = FsmInfo.currentArrayIndex
              });
            else if (fieldType.IsClass)
              fsmInfoList.AddRange(FsmInfo.FindEventUsage(fieldValue, eventName, depth + 1));
          }
        }
      }
      return (IEnumerable<FsmInfo>) fsmInfoList;
    }

    [Obsolete("Use FsmSearch instead.")]
    private static bool IsEventUsedInField(System.Type fieldType, object fieldValue, string eventName)
    {
      FsmInfo.currentArrayIndex = 0;
      if (fieldValue == null)
        return false;
      if (fieldType == typeof (FsmEvent))
      {
        FsmEvent fsmEvent = (FsmEvent) fieldValue;
        return eventName == fsmEvent.Name;
      }
      if (fieldType == typeof (FsmTemplateControl))
      {
        foreach (FsmEventMapping outputEvent in ((FsmTemplateControl) fieldValue).outputEvents)
        {
          if (outputEvent == null)
            return false;
          if (outputEvent.fromEvent != null && outputEvent.fromEvent.Name == eventName || outputEvent.toEvent != null && outputEvent.toEvent.Name == eventName)
            return true;
        }
      }
      if (fieldType.IsArray)
      {
        Array array = (Array) fieldValue;
        System.Type elementType = fieldType.GetElementType();
        if (elementType == typeof (FsmEvent))
        {
          for (int index = 0; index < array.Length; ++index)
          {
            if (FsmInfo.IsEventUsedInField(elementType, array.GetValue(index), eventName))
            {
              FsmInfo.currentArrayIndex = index;
              return true;
            }
          }
        }
      }
      return false;
    }

    [Obsolete("Use FsmSearch instead.")]
    public static List<FsmInfo> FindActionUsage(System.Type actionType)
    {
      List<FsmInfo> fsmInfoList = new List<FsmInfo>();
      if (FsmEditor.FsmList != null)
      {
        foreach (Fsm fsm in FsmEditor.FsmList)
          fsmInfoList.AddRange((IEnumerable<FsmInfo>) FsmInfo.FindStatesUsingAction(fsm, actionType));
      }
      return fsmInfoList;
    }

    [Obsolete("Use FsmSearch instead.")]
    public static List<FsmInfo> FindStatesUsingAction(Fsm fsm, System.Type actionType)
    {
      List<FsmInfo> fsmInfoList = new List<FsmInfo>();
      foreach (FsmState state in fsm.States)
      {
        if (state.ActionsLoaded)
        {
          int num = 0;
          foreach (FsmStateAction action in state.Actions)
          {
            if (action.GetType() == actionType)
              fsmInfoList.Add(new FsmInfo()
              {
                fsm = fsm,
                state = state,
                action = action,
                actionIndex = num
              });
            ++num;
          }
        }
      }
      return fsmInfoList;
    }

    public static List<Fsm> GetFsmList(List<FsmInfo> fsmInfoList)
    {
      List<Fsm> fsmList = new List<Fsm>();
      foreach (FsmInfo fsmInfo in fsmInfoList)
      {
        if (fsmInfo.fsm != null && !fsmList.Contains(fsmInfo.fsm))
          fsmList.Add(fsmInfo.fsm);
      }
      return fsmList;
    }

    public static List<FsmState> GetStateList(List<FsmInfo> fsmInfoList)
    {
      List<FsmState> fsmStateList = new List<FsmState>();
      foreach (FsmInfo fsmInfo in fsmInfoList)
      {
        if (fsmInfo.state != null && !fsmStateList.Contains(fsmInfo.state))
          fsmStateList.Add(fsmInfo.state);
      }
      return fsmStateList;
    }

    public static List<NamedVariable> GetVariableList(List<FsmInfo> fsmInfoList) => new List<NamedVariable>();

    public void DoLinkButton(GUIContent label, GUIStyle style)
    {
      Event current = Event.current;
      Color color = GUI.color;
      GUI.color = HtmlText.linkColor;
      GUILayout.Box(label, style);
      if (current.type == UnityEngine.EventType.MouseDown && current.button == 0 && GUILayoutUtility.GetLastRect().Contains(current.mousePosition))
      {
        this.Select();
        current.Use();
      }
      GUI.color = color;
      if (Event.current.type != UnityEngine.EventType.Repaint)
        return;
      EditorGUIUtility.AddCursorRect(GUILayoutUtility.GetLastRect(), MouseCursor.Link);
    }

    public override string ToString() => Labels.GetFullFsmLabel(this.fsm) + " State: " + this.stateName + " Action: " + (this.action != null ? this.action.Name : "none") + " Field: " + (this.field != null ? this.field.Name : "none");

    [Obsolete("Use FsmSearch")]
    public static List<FsmState> FindStatesUsingEvent(Fsm fsm, string eventName)
    {
      List<FsmInfo> fsmInfoList = new List<FsmInfo>();
      fsmInfoList.AddRange((IEnumerable<FsmInfo>) FsmInfo.FindTransitionsUsingEvent(fsm, eventName));
      fsmInfoList.AddRange((IEnumerable<FsmInfo>) FsmInfo.FindActionsUsingEvent(fsm, eventName));
      return FsmInfo.GetStateList(fsmInfoList);
    }

    [Obsolete("Use FsmSearch instead.")]
    public static List<FsmInfo> FindActionsTargetingTransition(
      FsmState state,
      string eventName)
    {
      return new List<FsmInfo>();
    }

    [Obsolete("Use FsmSearch instead.")]
    public static List<FsmInfo> FindActionsTargetingGlobalTransition(
      Fsm fsm,
      string eventName)
    {
      return new List<FsmInfo>();
    }

    [Obsolete("Use FsmSearch")]
    public static List<FsmInfo> FindActionsUsingEvent(string eventName) => FsmInfo.FindActionsUsingEvent(FsmEditor.FsmList, eventName);

    [Obsolete("Use FsmSearch")]
    public static List<FsmInfo> FindActionsUsingEvent(
      List<Fsm> fsmSelection,
      string eventName)
    {
      List<FsmInfo> fsmInfoList = new List<FsmInfo>();
      foreach (Fsm fsm in fsmSelection)
        fsmInfoList.AddRange((IEnumerable<FsmInfo>) FsmInfo.FindActionsUsingEvent(fsm, eventName));
      return fsmInfoList;
    }

    [Obsolete("Use FsmSearch")]
    public static List<FsmInfo> FindActionsUsingEvent(Fsm fsm, string eventName)
    {
      List<FsmInfo> fsmInfoList = new List<FsmInfo>();
      foreach (FsmState state in fsm.States)
      {
        state.Fsm = fsm;
        fsmInfoList.AddRange((IEnumerable<FsmInfo>) FsmInfo.FindActionsUsingEvent(state, eventName));
      }
      return fsmInfoList;
    }

    [Obsolete("Use FsmSearch")]
    public static List<FsmInfo> FindActionsUsingEvent(FsmState state, string eventName)
    {
      List<FsmInfo> fsmInfoList = new List<FsmInfo>();
      int num = 0;
      foreach (FsmStateAction action in state.Actions)
      {
        FsmInfo.currentInfo = new FsmInfo()
        {
          fsm = state.Fsm,
          state = state,
          action = action,
          actionIndex = num
        };
        fsmInfoList.AddRange(FsmInfo.FindEventUsage((object) action, eventName));
        ++num;
      }
      return fsmInfoList;
    }
  }
}
