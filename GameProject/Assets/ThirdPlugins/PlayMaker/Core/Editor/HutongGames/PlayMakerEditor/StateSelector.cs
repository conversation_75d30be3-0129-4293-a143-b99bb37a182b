// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.StateSelector
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class StateSelector : BaseEditorWindow
  {
    private static Vector2 scrollPosition;
    private static float scrollViewHeight;
    private static Rect selectedRect;
    private static bool autoScroll;
    private List<StateSelector.StateRow> stateList;
    private Fsm targetFsm;
    private FsmState lastSelectedState;

    public override void Initialize()
    {
      this.isToolWindow = true;
      this.minSize = new Vector2(200f, 100f);
      this.InitGUI();
    }

    public override void InitWindowTitle() => this.SetTitle(Strings.Lable_States);

    private void InitGUI()
    {
      if (FsmEditor.SelectedFsm == null)
        return;
      this.stateList = new List<StateSelector.StateRow>();
      foreach (FsmState state in FsmEditor.SelectedFsm.States)
        this.stateList.Add(new StateSelector.StateRow()
        {
          state = state,
          label = new GUIContent(state.Name, state.Name + (!string.IsNullOrEmpty(state.Description) ? "\n" + state.Description : ""))
        });
      this.stateList.Sort((Comparison<StateSelector.StateRow>) ((a, b) => string.Compare(a.label.text, b.label.text, StringComparison.Ordinal)));
    }

    public override void DoGUI()
    {
      if (this.targetFsm != FsmEditor.SelectedFsm)
      {
        this.targetFsm = FsmEditor.SelectedFsm;
        this.InitGUI();
      }
      if (Event.current.type == UnityEngine.EventType.MouseDown && Event.current.button == 0 && GUIUtility.hotControl == 0)
        GUIUtility.keyboardControl = 0;
      this.HandleKeyboardInput();
      StateSelector.DoToolbar();
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box(Strings.Hint_State_Selector, FsmEditorStyles.HintBox);
      StateSelector.scrollPosition = GUILayout.BeginScrollView(StateSelector.scrollPosition);
      if (FsmEditor.SelectedFsm != null)
      {
        foreach (StateSelector.StateRow state in this.stateList)
          StateSelector.DoStateRow(state);
      }
      HighlighterHelper.EndScrollView("State List");
      this.DoAutoScroll();
      FsmEditorGUILayout.PanelDivider();
      GUILayout.BeginHorizontal();
      GUILayout.FlexibleSpace();
      GUILayout.EndHorizontal();
      EditorGUILayout.Space();
    }

    private void Update()
    {
      if (FsmEditorSettings.DisableEditorWhenPlaying || this.lastSelectedState == FsmEditor.SelectedState)
        return;
      this.lastSelectedState = FsmEditor.SelectedState;
      StateSelector.autoScroll = true;
      this.Repaint();
    }

    public override void OnFsmChanged(Fsm fsm) => this.InitGUI();

    private void DoAutoScroll()
    {
      if (FsmEditor.SelectedState == null || Event.current.type != UnityEngine.EventType.Repaint || !StateSelector.autoScroll)
        return;
      StateSelector.scrollViewHeight = GUILayoutUtility.GetLastRect().height;
      if ((double) StateSelector.selectedRect.y < 0.0)
      {
        StateSelector.scrollPosition.y += StateSelector.selectedRect.y;
        this.Repaint();
      }
      else if ((double) StateSelector.selectedRect.y + (double) StateSelector.selectedRect.height > (double) StateSelector.scrollViewHeight)
      {
        StateSelector.scrollPosition.y += StateSelector.selectedRect.y + StateSelector.selectedRect.height - StateSelector.scrollViewHeight;
        this.Repaint();
      }
      StateSelector.autoScroll = false;
    }

    private static void DoStateRow(StateSelector.StateRow row)
    {
      if (FsmEditorGUILayout.TableRow(new GUIContent[1]
      {
        row.label
      }, new float[1]{ 1f }, FsmEditor.SelectedStateName == row.state.Name, row.state.HasErrors) >= 0)
      {
        StateSelector.SelectState(row.state);
        GUIUtility.ExitGUI();
      }
      if (row.state != FsmEditor.SelectedState || Event.current.type != UnityEngine.EventType.Repaint)
        return;
      StateSelector.selectedRect = GUILayoutUtility.GetLastRect();
      StateSelector.selectedRect.y -= StateSelector.scrollPosition.y;
    }

    private static void DoToolbar()
    {
      GUILayout.BeginHorizontal(EditorStyles.toolbar);
      GUILayout.Space(4f);
      if (GUILayout.Button(Labels.GetFullFsmLabel(FsmEditor.SelectedFsm), EditorStyles.toolbarDropDown))
        Menus.ShowFsmOwnerSelectionMenu();
      GUILayout.Space(4f);
      HighlighterHelper.EndHorizontal("FSM Selection");
    }

    private void HandleKeyboardInput()
    {
      if (!Keyboard.IsGuiEventKeyboardShortcut())
        return;
      switch (Event.current.keyCode)
      {
        case KeyCode.UpArrow:
          this.SelectPrevious();
          break;
        case KeyCode.DownArrow:
          this.SelectNext();
          break;
        default:
          return;
      }
      Event.current.Use();
      GUIUtility.ExitGUI();
    }

    private static void SelectState(FsmState state)
    {
      FsmEditor.SelectState(state, true);
      FsmEditor.RepaintAll();
    }

    private void SelectPrevious()
    {
      if (FsmEditor.SelectedState == null)
        return;
      int index = this.stateList.FindIndex((Predicate<StateSelector.StateRow>) (x => x.state == FsmEditor.SelectedState));
      if (index > 0)
        StateSelector.SelectState(this.stateList[index - 1].state);
      StateSelector.autoScroll = true;
    }

    private void SelectNext()
    {
      if (FsmEditor.SelectedState == null)
        return;
      int index = this.stateList.FindIndex((Predicate<StateSelector.StateRow>) (x => x.state == FsmEditor.SelectedState));
      if (index < FsmEditor.SelectedFsm.States.Length - 1)
        StateSelector.SelectState(this.stateList[index + 1].state);
      StateSelector.autoScroll = true;
    }

    private struct StateRow
    {
      public FsmState state;
      public GUIContent label;
    }
  }
}
