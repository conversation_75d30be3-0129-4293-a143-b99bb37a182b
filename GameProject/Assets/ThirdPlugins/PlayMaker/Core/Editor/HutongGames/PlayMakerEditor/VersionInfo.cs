// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.VersionInfo
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Reflection;

namespace HutongGames.PlayMakerEditor
{
  public class VersionInfo
  {
    public static string VersionOverride;

    [Localizable(false)]
    public static string AssemblyVersion => EditorApp.IsSourceCodeVersion ? "Source Code Version" : VersionInfo.GetAssemblyInformationalVersion();

    public static string PlayMakerVersionLabel
    {
      get
      {
        string versionLabel = PlayMakerFSM.VersionLabel;
        return !string.IsNullOrEmpty(versionLabel) ? versionLabel : "";
      }
    }

    public static string PlayMakerVersionInfo
    {
      get
      {
        string versionNotes = PlayMakerFSM.VersionNotes;
        return !string.IsNullOrEmpty(versionNotes) ? versionNotes : "";
      }
    }

    [Localizable(false)]
    public static string GetAssemblyInformationalVersion()
    {
      string str = VersionInfo.VersionOverride;
      try
      {
        if (string.IsNullOrEmpty(str))
          str = VersionInfo.GetInformationalVersion(Assembly.GetExecutingAssembly());
      }
      catch (Exception ex)
      {
        UnityEngine.Debug.LogWarning((object) "Couldn't get Playmaker Version Info!");
        str = "unknown";
      }
      return str + (VersionInfo.PlayMakerVersionLabel != "" ? " - " + VersionInfo.PlayMakerVersionLabel : "");
    }

    public static string GetInformationalVersion(Assembly assembly) => FileVersionInfo.GetVersionInfo(assembly.Location).ProductVersion;
  }
}
