// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.PlayMakerPaths
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class PlayMakerPaths
  {
    public static string ProjectPath { get; private set; }

    public static string RuntimePath { get; private set; }

    public static string EditorPath { get; private set; }

    public static string EditorResourcesPath { get; private set; }

    public static string WatermarksPath { get; private set; }

    public static string ResourcesPath { get; private set; }

    public static string TemplatesPath { get; private set; }

    public static string RuntimeFullPath { get; private set; }

    public static string EditorFullPath { get; private set; }

    public static string WatermarksFullPath { get; private set; }

    public static string ResourcesFullPath { get; private set; }

    public static string TemplatesFullPath { get; private set; }

    static PlayMakerPaths()
    {
      PlayMakerPaths.LoadPaths();
      PlayMakerPaths.ValidatePaths();
    }

    private static void LoadPaths()
    {
      PlayMakerPaths.RuntimePath = PlayMakerPaths.FixPath(EditorPrefs.GetString("PlayMakerPaths.RuntimePath", "Assets/ThirdPlugins/PlayMaker/"));
      PlayMakerPaths.EditorPath = PlayMakerPaths.FixPath(EditorPrefs.GetString("PlayMakerPaths.EditorPath", "Assets/ThirdPlugins/PlayMaker/Editor/"));
      PlayMakerPaths.EditorResourcesPath = PlayMakerPaths.FixPath(Path.Combine(PlayMakerPaths.EditorPath, "Resources"));
      PlayMakerPaths.WatermarksPath = PlayMakerPaths.FixPath(Path.Combine(PlayMakerPaths.EditorPath, "Watermarks"));
      string path1 = PlayMakerPaths.EditorPath.Substring(0, PlayMakerPaths.EditorPath.Length - 7);
      PlayMakerPaths.ResourcesPath = "Assets/ThirdPlugins/PlayMaker/Resources";//PlayMakerPaths.FixPath(Path.Combine(path1, "Resources"));
      PlayMakerPaths.TemplatesPath = PlayMakerPaths.FixPath(Path.Combine(path1, "Templates"));
      PlayMakerPaths.ProjectPath = Path.Combine(Application.dataPath, "..\\");
      PlayMakerPaths.ProjectPath = PlayMakerPaths.FixPath(Path.GetFullPath(PlayMakerPaths.ProjectPath));
      PlayMakerPaths.RuntimeFullPath = PlayMakerPaths.FixPath(Path.GetFullPath(Path.Combine(PlayMakerPaths.ProjectPath, PlayMakerPaths.RuntimePath)));
      PlayMakerPaths.EditorFullPath = PlayMakerPaths.FixPath(Path.GetFullPath(Path.Combine(PlayMakerPaths.ProjectPath, PlayMakerPaths.EditorPath)));
      PlayMakerPaths.WatermarksFullPath = PlayMakerPaths.FixPath(Path.GetFullPath(Path.Combine(PlayMakerPaths.ProjectPath, PlayMakerPaths.WatermarksPath)));
      PlayMakerPaths.TemplatesFullPath = PlayMakerPaths.FixPath(Path.GetFullPath(Path.Combine(PlayMakerPaths.ProjectPath, PlayMakerPaths.TemplatesPath)));
      PlayMakerPaths.ResourcesFullPath = PlayMakerPaths.FixPath(Path.GetFullPath(Path.Combine(PlayMakerPaths.ProjectPath, PlayMakerPaths.ResourcesPath)));
    }

    private static void SavePaths()
    {
      EditorPrefs.SetString("PlayMakerPaths.RuntimePath", PlayMakerPaths.RuntimePath);
      EditorPrefs.SetString("PlayMakerPaths.EditorPath", PlayMakerPaths.EditorPath);
    }

    private static string FixPath(string path) => path.Replace("\\", "/");

    private static void ValidatePaths()
    {
      if (EditorApp.IsSourceCodeVersion)
      {
        PlayMakerPaths.ResetPaths();
      }
      else
      {
        // if (!File.Exists(Path.Combine(PlayMakerPaths.RuntimeFullPath, "PlayMaker.dll")))
        // {
        //   PlayMakerPaths.RuntimeFullPath = PlayMakerPaths.FindPath("PlayMaker.dll");
        //   PlayMakerPaths.RuntimePath = new Uri(Application.dataPath).MakeRelativeUri(new Uri(PlayMakerPaths.RuntimeFullPath)).ToString();
        // }
        // if (!File.Exists(Path.Combine(PlayMakerPaths.EditorFullPath, "PlayMakerEditor.dll")))
        // {
        //   PlayMakerPaths.EditorFullPath = PlayMakerPaths.FindPath("PlayMakerEditor.dll");
        //   PlayMakerPaths.EditorPath = new Uri(Application.dataPath).MakeRelativeUri(new Uri(PlayMakerPaths.EditorFullPath)).ToString();
        // }
        PlayMakerPaths.SavePaths();
      }
    }

    public static string GetTemplatesDirectory() => !Directory.Exists(PlayMakerPaths.TemplatesFullPath) ? Application.dataPath : PlayMakerPaths.TemplatesFullPath;

    private static string FindPath(string filename)
    {
      string str = string.Empty;
      IEnumerable<FileInfo> source = (IEnumerable<FileInfo>) ((IEnumerable<FileInfo>) new DirectoryInfo(Application.dataPath).GetFiles("*.*", SearchOption.AllDirectories)).Where<FileInfo>((Func<FileInfo, bool>) (file => file.Name == filename)).OrderBy<FileInfo, string>((Func<FileInfo, string>) (file => file.Name));
      if (source.Any<FileInfo>())
        str = Path.GetDirectoryName(source.First<FileInfo>().FullName);
      if (string.IsNullOrEmpty(str))
        Debug.LogError((object) ("PlayMakerPaths: Could not find: " + filename));
      return str;
    }

    public static bool IsValidTemplateSavePath(string path) => PlayMakerPaths.PathIsInProject(path);

    public static bool PathIsInProject(string path)
    {
      path = Path.GetDirectoryName(path);
      return !string.IsNullOrEmpty(path) && Directory.Exists(path) && path.ToLowerInvariant().Replace("\\", "/").Contains(Application.dataPath.ToLowerInvariant().Replace("\\", "/"));
    }

    private static void ResetPaths()
    {
      PlayMakerPaths.RuntimePath = "Assets/ThirdPlugins/PlayMaker/";
      PlayMakerPaths.EditorPath = "Assets/ThirdPlugins/PlayMaker/Editor/";
      PlayMakerPaths.EditorResourcesPath = "Assets/ThirdPlugins/PlayMaker/Editor/Resources/";
    }

    private static void DebugPaths() => Debug.Log((object) ("PlayMakerPaths:\nRuntimePath: " + PlayMakerPaths.RuntimePath + "\nRuntimeFullPath: " + PlayMakerPaths.RuntimeFullPath + "\nEditorPath: " + PlayMakerPaths.EditorPath + "\nEditorFullPath: " + PlayMakerPaths.EditorFullPath + "\nEditorResourcesPath: " + PlayMakerPaths.EditorResourcesPath + "\nWatermarksPath: " + PlayMakerPaths.WatermarksPath + "\nWatermarksFullPath: " + PlayMakerPaths.WatermarksFullPath + "\nResourcesPath: " + PlayMakerPaths.ResourcesPath + "\nTemplatesPath: " + PlayMakerPaths.TemplatesPath + "\nResourcesFullPath: " + PlayMakerPaths.ResourcesFullPath + "\nTemplatesFullPath: " + PlayMakerPaths.GetTemplatesDirectory()));
  }
}
