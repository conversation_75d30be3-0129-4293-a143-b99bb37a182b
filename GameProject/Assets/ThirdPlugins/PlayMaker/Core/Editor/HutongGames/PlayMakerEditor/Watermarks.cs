// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Watermarks
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class Watermarks
  {
    private static readonly Dictionary<Fsm, Texture> watermarkTextureLookup = new Dictionary<Fsm, Texture>();
    private static Fsm lastWatermarkFsm;
    private static Texture lastWatermark;

    public static string GetLabel(PlayMakerFSM fsmComponent, string defaultLabel = "No Watermark") => (Object) fsmComponent == (Object) null || string.IsNullOrEmpty(fsmComponent.Fsm.Watermark) ? defaultLabel : fsmComponent.Fsm.Watermark;

    public static Texture Set(Fsm fsm, string textureName)
    {
      fsm.Watermark = textureName;
      Watermarks.watermarkTextureLookup.Remove(fsm);
      Watermarks.lastWatermarkFsm = (Fsm) null;
      return Watermarks.Get(fsm);
    }

    public static Texture Get(Fsm fsm)
    {
      if (Watermarks.lastWatermarkFsm == fsm)
        return Watermarks.lastWatermark;
      if (fsm == null || string.IsNullOrEmpty(fsm.Watermark))
        return (Texture) null;
      Texture texture;
      Watermarks.watermarkTextureLookup.TryGetValue(fsm, out texture);
      if ((Object) texture != (Object) null)
      {
        Watermarks.lastWatermarkFsm = fsm;
        Watermarks.lastWatermark = texture;
        return texture;
      }
      texture = Watermarks.Load(fsm.Watermark);
      Watermarks.lastWatermarkFsm = fsm;
      Watermarks.lastWatermark = texture;
      Watermarks.watermarkTextureLookup.Remove(fsm);
      Watermarks.watermarkTextureLookup.Add(fsm, texture);
      return texture;
    }

    public static Texture Load(string name)
    {
      string assetPath = Path.Combine(PlayMakerPaths.WatermarksPath, name);
      Texture texture = (Texture) AssetDatabase.LoadMainAssetAtPath(assetPath);
      if (!((Object) texture == (Object) null))
        return texture;
      Debug.LogError((object) (Strings.Error_Failed_to_load_texture__ + assetPath));
      return texture;
    }

    public static string[] GetNames()
    {
      string watermarksFullPath = PlayMakerPaths.WatermarksFullPath;
      if (!Directory.Exists(watermarksFullPath))
        return new string[0];
      string[] files = Files.GetFiles(watermarksFullPath, "*.png|*.jpg|*.jpeg");
      for (int index = 0; index < files.Length; ++index)
        files[index] = Path.GetFileName(files[index]);
      return files;
    }

    [Localizable(false)]
    public static Texture[] GetTextures(bool showProgress = true)
    {
      List<Texture> textureList = new List<Texture>();
      if (!FsmEditorSettings.EnableWatermarks)
        return textureList.ToArray();
      string watermarksPath = PlayMakerPaths.WatermarksPath;
      if (!Directory.Exists(watermarksPath))
      {
        Debug.LogError((object) "Could not find Watermarks directory!\nDisabling watermarks...");
        FsmEditorSettings.EnableWatermarks = false;
        return textureList.ToArray();
      }
      string[] files = Files.GetFiles(watermarksPath, "*.png|*.jpg|*.jpeg");
      if (files == null || files.Length == 0)
      {
        Debug.LogError((object) "Could not find Watermarks!\nDisabling watermarks...");
        FsmEditorSettings.EnableWatermarks = false;
        return textureList.ToArray();
      }
      int num = 0;
      float length = (float) files.Length;
      foreach (string str in files)
      {
        if (showProgress)
          EditorUtility.DisplayProgressBar(Strings.ProductName, Strings.Label_Loading_Watermark_Textures___, (float) num++ / length);
        string dataPath = Application.dataPath;
        Texture texture = (Texture) AssetDatabase.LoadMainAssetAtPath(str.Replace(dataPath, "Assets"));
        textureList.Add(texture);
      }
      if (showProgress)
        EditorUtility.ClearProgressBar();
      return textureList.ToArray();
    }
  }
}
