// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Keyboard
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class Keyboard
  {
    private static bool resetFocus;

    public static bool IsGuiEventKeyboardShortcut() => GUIUtility.keyboardControl == 0 && Event.current.GetTypeForControl(GUIUtility.GetControlID(FocusType.Keyboard)) == UnityEngine.EventType.KeyDown;

    public static void ResetFocus(bool immediate = false)
    {
      Keyboard.resetFocus = true;
      if (!immediate)
        return;
      Keyboard.Update();
    }

    public static void Update()
    {
      if (!Keyboard.resetFocus)
        return;
      GUIUtility.keyboardControl = 0;
      Keyboard.resetFocus = false;
    }

    public static bool AltAction() => Keyboard.Action() && Keyboard.Alt();

    public static bool Alt() => (Event.current.modifiers & EventModifiers.Alt) == EventModifiers.Alt;

    public static bool Control() => (Event.current.modifiers & EventModifiers.Control) == EventModifiers.Control;

    public static bool Action() => EditorGUI.actionKey;

    public static bool EnterKeyPressed() => Event.current.keyCode == KeyCode.Return || Event.current.keyCode == KeyCode.KeypadEnter;

    public static bool CommitKeyPressed()
    {
      Event current = Event.current;
      return current.type == UnityEngine.EventType.KeyDown && current.keyCode == KeyCode.Return || current.keyCode == KeyCode.KeypadEnter;
    }
  }
}
