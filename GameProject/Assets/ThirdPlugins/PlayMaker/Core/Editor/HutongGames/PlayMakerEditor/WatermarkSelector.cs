// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.WatermarkSelector
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  internal class WatermarkSelector
  {
    private static Vector2 scrollViewPosition;
    private static GUIContent[] watermarkThumbs;
    private static float gridWidth;
    private static float gridHeight;
    private const int numColumns = 4;
    private static int selectedWatermarkIndex;

    public static void Init()
    {
      Texture[] textures = Watermarks.GetTextures();
      string[] names = Watermarks.GetNames();
      WatermarkSelector.watermarkThumbs = new GUIContent[textures.Length];
      for (int index = 0; index < textures.Length; ++index)
        WatermarkSelector.watermarkThumbs[index] = new GUIContent(textures[index], names[index]);
      WatermarkSelector.gridWidth = FsmEditor.InspectorPanelWidth - 24f;
      WatermarkSelector.gridHeight = (float) ((double) (textures.Length / 4) * (double) WatermarkSelector.gridWidth / 4.0);
    }

    public static void ResetSelection()
    {
      if (WatermarkSelector.watermarkThumbs == null)
        WatermarkSelector.Init();
      if (WatermarkSelector.watermarkThumbs == null)
      {
        Debug.LogError((object) Strings.Error_Could_not_load_watermarks);
      }
      else
      {
        Texture texture = Watermarks.Get(FsmEditor.SelectedFsm);
        WatermarkSelector.selectedWatermarkIndex = -1;
        for (int index = 0; index < WatermarkSelector.watermarkThumbs.Length; ++index)
        {
          if ((Object) texture == (Object) WatermarkSelector.watermarkThumbs[index].image)
            WatermarkSelector.selectedWatermarkIndex = index;
        }
      }
    }

    public static void OnGUI()
    {
      if (!FsmEditorSettings.EnableWatermarks)
      {
        GUILayout.Label(Strings.Label_Watermarks_Are_Disabled);
        if (GUILayout.Button(Strings.Command_Enable_Watermarks))
          FsmEditorSettings.EnableWatermarks = true;
        if (GUILayout.Button(Strings.Command_Finished))
          WatermarkSelector.Cancel();
        GUILayout.FlexibleSpace();
      }
      else
      {
        GUILayout.Label(Strings.Label_Select_A_Watermark);
        FsmEditorGUILayout.PanelDivider();
        if (WatermarkSelector.watermarkThumbs == null || WatermarkSelector.watermarkThumbs.Length == 0)
        {
          EditorGUILayout.HelpBox(Strings.Error_Missing_Watermark_Texture, MessageType.Warning);
          GUILayout.FlexibleSpace();
        }
        else
        {
          WatermarkSelector.scrollViewPosition = GUILayout.BeginScrollView(WatermarkSelector.scrollViewPosition);
          GUIHelpers.BeginGuiContentColor(FsmEditorStyles.WatermarkTintSolid);
          int index = GUILayout.SelectionGrid(WatermarkSelector.selectedWatermarkIndex, WatermarkSelector.watermarkThumbs, 4, GUILayout.Width(WatermarkSelector.gridWidth), GUILayout.Height(WatermarkSelector.gridHeight));
          if (index != WatermarkSelector.selectedWatermarkIndex)
            WatermarkSelector.SelectWatermark(index);
          GUIHelpers.EndGuiContentColor();
          GUILayout.EndScrollView();
        }
        FsmEditorGUILayout.PanelDivider();
        if (GUILayout.Button(Strings.Command_Clear_Watermark))
        {
          Watermarks.Set(FsmEditor.SelectedFsm, "");
          WatermarkSelector.Cancel();
        }
        if (GUILayout.Button(Strings.Command_Finished))
          WatermarkSelector.Cancel();
        if (FsmEditorSettings.ShowHints)
          GUILayout.Box(Strings.Hint_Watermarks, FsmEditorStyles.HintBox);
        EditorGUILayout.Space();
      }
    }

    private static void SelectWatermark(int index)
    {
      Watermarks.Set(FsmEditor.SelectedFsm, Watermarks.GetNames()[index]);
      FsmEditor.SetFsmDirty(false);
      WatermarkSelector.selectedWatermarkIndex = index;
    }

    private static void Cancel() => FsmEditor.Inspector.SetMode(InspectorMode.FsmInspector);
  }
}
