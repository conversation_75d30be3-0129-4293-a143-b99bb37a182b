// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmGraphView
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.Extensions;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class FsmGraphView
  {
    private const float MaxPossibleCanvasSize = 200000f;
    private const float ExpandGraphAmount = 100000f;
    private const float GraphSizePadding = 100f;
    private const int LeftMouseButton = 0;
    private const int RightMouseButton = 1;
    private static bool editingDisabled;
    private static string editingDisabledReason;
    private static bool updateVisibility;
    private static readonly List<FsmState> visibleStates = new List<FsmState>();
    private static readonly List<Link> visibleLinks = new List<Link>();
    private static readonly List<FsmEvent> outputEvents = new List<FsmEvent>();
    private static readonly List<FsmTransition> highlightedTransitions = new List<FsmTransition>();
    private static readonly List<FsmState> highlightedStates = new List<FsmState>();
    private readonly CanvasView canvasView;
    private Vector2 graphSize;
    private Rect view;
    private float zoom = 1f;
    private string graphLabel;
    private Event currentEvent;
    private UnityEngine.EventType eventType;
    private bool isRepaint;
    private Vector2 currentMousePos;
    private Vector2 contextMenuPos;
    internal bool mouseOverGraphView;
    private bool mouseOverMinimap;
    private FsmState mouseOverState;
    private FsmTransition mouseOverTransition;
    private FsmTransition mouseOverGlobalTransition;
    private bool mouseOverStartBox;
    private Rect startBox;
    private static FsmTransition selectedGlobalTransition;
    private double mouseOverStateStartTime;
    private const double autoSelectTime = 0.5;
    private FsmGraphView.DraggingMode draggingMode;
    private readonly FsmState dummyDraggingState;
    private Vector2 dragStartPos;
    private List<FsmState> dragStates;
    private List<Vector2> dragStateStartPositions;
    private List<Vector2> allStateStartPositions;
    private const float DragThreshold = 3f;
    private bool dragStarted;
    private bool frameState;
    private bool graphExpanded;
    private FsmGraphView.DragConstraint dragConstraint;
    private FsmState firstStateLastUpdate;
    private GenericMenu delayedContextMenu;
    private bool forceContextMenu;
    private static Rect tempRect = new Rect();
    private Rect minimapRect;
    private Rect minimapLocalRect;
    private float minimapScale;
    private float scaleToMinimap;
    private float minimapOffset;

    public static bool EditingDisable => FsmGraphView.editingDisabled;

    public bool IsDragging => this.draggingMode != FsmGraphView.DraggingMode.None || this.canvasView.MouseDown;

    public FsmGraphView()
    {
      this.dummyDraggingState = new FsmState((Fsm) null);
      this.canvasView = new CanvasView()
      {
        ContentMargin = 300f
      };
    }

    public void Init()
    {
      this.canvasView.Init(FsmEditor.Window);
      this.ApplySettings();
      this.RefreshView();
    }

    public void RefreshView()
    {
      this.UpdateGraphSize();
      this.InitGraphLabel();
      this.zoom = this.canvasView.SetZoom(FsmEditor.Selection.Zoom);
      this.InitScale(this.zoom);
      this.canvasView.SetContentScrollPosition(FsmEditor.Selection.ScrollPosition);
      this.UpdateVisibility();
      FsmEditor.Repaint(true);
    }

    public void FsmSelected()
    {
      this.UpdateGraphSize();
      this.SanityCheckGraphBounds();
      this.UpdateVisibility();
    }

    public void UpdateGraphLabel() => this.InitGraphLabel();

    private void InitGraphLabel()
    {
      this.graphLabel = "";
      if (!FsmEditorSettings.ShowSelectedGameObjectLabel && !FsmEditorSettings.ShowSelectedFsmLabel)
        return;
      if ((UnityEngine.Object) FsmEditor.SelectedTemplate != (UnityEngine.Object) null)
      {
        if (!FsmEditorSettings.ShowSelectedFsmLabel)
          return;
        this.graphLabel = string.Format(Strings.Label_Selected_Template, (object) FsmEditor.SelectedTemplate.name);
      }
      else
      {
        if (FsmEditorSettings.ShowSelectedGameObjectLabel)
        {
          this.graphLabel = FsmEditor.SelectedFsmGameObjectName;
          if (FsmEditorSettings.ShowSelectedFsmLabel)
            this.graphLabel += " : ";
        }
        if (!FsmEditorSettings.ShowSelectedFsmLabel)
          return;
        this.graphLabel += Labels.GetFsmLabel(FsmEditor.SelectedFsm);
      }
    }

    [Localizable(false)]
    public void TakeScreenshot() => this.canvasView.TakeScreenshot("../" + FsmEditorSettings.ScreenshotsPath + "/" + Files.GetFsmSavePath(FsmEditor.SelectedFsm));

    public bool IsStateOffscreen(FsmState state)
    {
      if (state == null || (double) this.view.width == 0.0 || (double) this.view.height == 0.0)
        return false;
      Rect position = state.Position;
      if ((double) position.x < (double) FsmEditor.Selection.ScrollPosition.x + 0.0)
        return true;
      position = state.Position;
      if ((double) position.y < (double) FsmEditor.Selection.ScrollPosition.y + 0.0)
        return true;
      position = state.Position;
      double x = (double) position.x;
      position = state.Position;
      double width = (double) position.width;
      if (x + width + 0.0 > (double) FsmEditor.Selection.ScrollPosition.x + (double) this.view.width)
        return true;
      position = state.Position;
      double y = (double) position.y;
      position = state.Position;
      double height = (double) position.height;
      return y + height + 0.0 > (double) FsmEditor.Selection.ScrollPosition.y + (double) this.view.height;
    }

    public void SanityCheckGraphBounds()
    {
      bool flag = false;
      if (this.graphExpanded || (double) this.graphSize.x > 100000.0 || (double) this.graphSize.x < 1.0)
        flag = true;
      else if (FsmEditor.SelectedFsm != null)
      {
        foreach (FsmState state in FsmEditor.SelectedFsm.States)
        {
          if ((double) state.Position.x < 0.0)
          {
            flag = true;
            break;
          }
        }
      }
      if (!flag)
        return;
      UnityEngine.Debug.Log((object) Strings.Fixed_Graph_View_Bounds);
      this.UpdateGraphBounds();
    }

    public void UpdateGraphBounds(bool keepViewPosition = false)
    {
      Vector2 origin = FsmGraphView.MoveAllStatesToOrigin(FsmEditor.SelectedFsm);
      if (keepViewPosition)
        this.canvasView.Pan(-origin);
      this.UpdateGraphSize();
      FsmEditor.Repaint(true);
    }

    public void UpdateGraphSize()
    {
      if (FsmEditor.SelectedFsm == null || this.graphExpanded)
        return;
      this.graphSize = new Vector2();
      foreach (FsmState state in FsmEditor.SelectedFsm.States)
      {
        this.graphSize.x = Mathf.Max(this.graphSize.x, state.Position.xMax);
        this.graphSize.y = Mathf.Max(this.graphSize.y, state.Position.yMax);
      }
      this.graphSize.x = Mathf.Min(this.graphSize.x + 100f, 200000f);
      this.graphSize.y = Mathf.Min(this.graphSize.y + 200f, 200000f);
      this.canvasView.ContentSize = this.graphSize;
      this.UpdateVisibility();
    }

    private void UpdateGraphBounds(Vector2 viewPos)
    {
      if (FsmEditor.SelectedFsm == null)
        return;
      Vector2 viewCoordinates = this.canvasView.ContentToViewCoordinates(viewPos);
      Vector2 worldCoordinates = this.canvasView.LocalToWorldCoordinates(viewPos + this.canvasView.ContentOrigin);
      FsmState state = FsmEditor.SelectedFsm.GetState(FsmEditor.SelectedFsm.StartState);
      Vector2 localPos = new Vector2(state.Position.x, state.Position.y) * this.zoom;
      Vector2 vector2 = worldCoordinates - this.canvasView.LocalToWorldCoordinates(localPos);
      this.UpdateGraphBounds();
      this.canvasView.SetScrollPosition(this.canvasView.WorldToLocalCoordinates(this.canvasView.LocalToWorldCoordinates(new Vector2(state.Position.x, state.Position.y) * this.zoom) + vector2 - viewCoordinates / this.zoom));
      this.graphExpanded = false;
      this.UpdateGraphSize();
    }

    private void ExpandGraphBounds()
    {
      if (this.graphExpanded)
        return;
      Vector2 delta = new Vector2(100000f, 100000f);
      this.graphSize += delta * 2f;
      this.canvasView.ContentSize = this.graphSize;
      this.canvasView.SetScrollPosition(this.canvasView.ScrollPosition + delta * this.zoom);
      FsmGraphView.MoveAllStates(FsmEditor.SelectedFsm, delta);
      this.currentMousePos += delta * this.zoom;
      this.dragStartPos += delta * this.zoom;
      for (int index = 0; index < this.dragStateStartPositions.Count; ++index)
        this.dragStateStartPositions[index] += delta;
      this.graphExpanded = true;
    }

    [Localizable(false)]
    public void OnGUI(Rect area)
    {
      if (FsmEditor.SelectedFsmIsLocked)
        GUILayout.Label("LOCKED");
      EditorGUI.BeginDisabledGroup(FsmEditor.SelectedFsmIsLocked);
      this.view = area;
      this.currentEvent = Event.current;
      this.eventType = this.currentEvent.type;
      this.isRepaint = this.eventType == UnityEngine.EventType.Repaint;
      this.DoBackground();
      this.DoCanvasView();
      this.DoGameStateIcon();
      if (this.isRepaint)
      {
        this.DrawMinimap();
        this.DrawFrame();
        this.DrawHintBox();
      }
      EditorGUI.EndDisabledGroup();
    }

    private void DoCanvasView()
    {
      if (FsmGraphView.updateVisibility && this.eventType == UnityEngine.EventType.Layout)
        this.DoUpdateVisibility();
      this.mouseOverGraphView = this.canvasView.ActiveView.Contains(this.currentEvent.mousePosition);
      this.HandleMinimapInput();
      FsmEditor.Selection.ScrollPosition = this.canvasView.BeginGUILayout(this.view, FsmEditorSettings.ShowScrollBars);
      this.HandleMouseEvents();
      this.HandleKeyEvents();
      this.DoFsmGraph(this.canvasView.ContentArea);
      this.HandleDragAndDropObjects();
      this.DoContextMenu();
      this.canvasView.EndGUILayout();
      this.SetScale(this.canvasView.Scale);
      if (!this.canvasView.ViewChanged)
        return;
      this.UpdateVisibility();
      this.canvasView.ViewChanged = false;
    }

    [Localizable(false)]
    private void DoDebugText() => GUI.Label(new Rect(0.0f, 60f, 300f, 30f), "draggingMode: " + (object) this.draggingMode);

    public void Update()
    {
      this.canvasView.Update();
      if (FsmEditor.SelectedFsm != null && FsmEditor.SelectedFsm.States[0] != this.firstStateLastUpdate)
      {
        FsmGraphView.updateVisibility = true;
        this.firstStateLastUpdate = FsmEditor.SelectedFsm.States[0];
        FsmEditor.SanityCheckSelection();
      }
      if (!FsmGraphView.updateVisibility)
        return;
      this.DoUpdateVisibility();
    }

    public void FrameState(FsmState state) => this.FrameState(state, true);

    public void FrameState(FsmState state, bool smooth)
    {
      if (state == null)
        return;
      Vector2 pos = new Vector2()
      {
        x = state.Position.x + state.Position.width * 0.5f,
        y = state.Position.y + state.Position.height
      };
      if (smooth)
      {
        if (this.draggingMode != FsmGraphView.DraggingMode.None)
          return;
        this.canvasView.StartPanToPosition(pos);
      }
      else
      {
        this.canvasView.CancelAutoPan();
        this.canvasView.SetContentScrollPosition(pos);
      }
    }

    private bool IsMouseEvent() => this.currentEvent.isMouse || this.canvasView.IsEdgeScrolling || this.eventType == UnityEngine.EventType.DragUpdated;

    private void HandleMouseEvents()
    {
      if (FsmEditor.MouseUp)
      {
        this.HandleMouseUp();
      }
      else
      {
        if (!this.mouseOverGraphView)
          return;
        if (this.IsMouseEvent())
          this.currentMousePos = this.currentEvent.mousePosition - this.canvasView.ContentOrigin;
        this.UpdateMouseOverInfo(this.currentMousePos);
        if (FsmEditor.SelectedFsm != null)
        {
          switch (this.currentEvent.type)
          {
            case UnityEngine.EventType.MouseDown:
              this.HandleMouseDown();
              break;
            case UnityEngine.EventType.MouseDrag:
              this.HandleMouseDrag();
              break;
          }
          if (!this.dragStarted || this.draggingMode != FsmGraphView.DraggingMode.State && this.draggingMode != FsmGraphView.DraggingMode.Transition)
            return;
          this.HandleMouseDrag();
        }
        else
        {
          if (this.eventType != UnityEngine.EventType.MouseDown || !EditorGUI.actionKey)
            return;
          FsmBuilder.AddFsmToSelected();
          GUIUtility.ExitGUI();
        }
      }
    }

    private void HandleKeyEvents()
    {
      if (FsmEditor.InspectorHasFocus)
        return;
      if (this.eventType == UnityEngine.EventType.KeyUp && this.currentEvent.keyCode == KeyCode.BackQuote && GUIUtility.keyboardControl == 0 || this.currentEvent.keyCode == KeyCode.BackQuote && Keyboard.Control())
      {
        FsmEditor.OpenActionWindow();
      }
      else
      {
        if (this.eventType != UnityEngine.EventType.KeyDown)
          return;
        KeyCode keyCode = this.currentEvent.keyCode;
        if (keyCode == KeyCode.F1)
          EditorCommands.ToggleShowHints();
        if (keyCode == KeyCode.F11 && FsmEditor.SelectedFsm != null)
          this.TakeScreenshot();
        if (GUIUtility.hotControl != 0)
          return;
        switch (keyCode)
        {
          case KeyCode.Return:
            if (FsmEditor.SelectedState != null)
            {
              if (FsmEditor.SelectedTransition == null)
              {
                this.SelectTransition(0);
                break;
              }
              FsmState toFsmState = FsmEditor.SelectedTransition.ToFsmState;
              if (toFsmState != null)
              {
                FsmEditor.SelectState(toFsmState, true);
                break;
              }
              break;
            }
            break;
          case KeyCode.Escape:
            if (FsmEditor.SelectedTransition != null)
            {
              this.SelectTransition((FsmTransition) null);
              break;
            }
            break;
          case KeyCode.F:
            if (Application.isPlaying && FsmEditor.SelectedFsm != null && FsmEditor.SelectedFsm.ActiveState != null)
            {
              FsmEditor.SelectState(FsmEditor.SelectedFsm.ActiveState, true);
              break;
            }
            this.FrameState(FsmEditor.SelectedState);
            break;
          case KeyCode.UpArrow:
            this.SelectUp();
            break;
          case KeyCode.DownArrow:
            this.SelectDown();
            break;
          case KeyCode.RightArrow:
            this.SelectRight();
            break;
          case KeyCode.LeftArrow:
            this.SelectLeft();
            break;
          case KeyCode.Home:
            if (FsmEditor.SelectedFsm != null)
            {
              this.FrameState(FsmEditor.SelectStateByName(FsmEditor.SelectedFsm.StartState));
              break;
            }
            break;
        }
        if (!EditorGUI.actionKey)
          return;
        if (Event.current.alt)
        {
          if (keyCode != KeyCode.UpArrow)
          {
            if (keyCode != KeyCode.DownArrow)
              return;
            FsmGraphView.SetLinkConstraint(FsmEditor.SelectedTransition, FsmTransition.CustomLinkConstraint.None);
          }
          else
            FsmGraphView.ToggleLinkStyle(FsmEditor.SelectedTransition);
        }
        else
        {
          switch (keyCode)
          {
            case KeyCode.UpArrow:
              EditorCommands.MoveTransitionUp((object) FsmEditor.SelectedTransition);
              break;
            case KeyCode.DownArrow:
              EditorCommands.MoveTransitionDown((object) FsmEditor.SelectedTransition);
              break;
            case KeyCode.RightArrow:
              FsmGraphView.SetLinkConstraint(FsmEditor.SelectedTransition, FsmTransition.CustomLinkConstraint.LockRight);
              break;
            case KeyCode.LeftArrow:
              FsmGraphView.SetLinkConstraint(FsmEditor.SelectedTransition, FsmTransition.CustomLinkConstraint.LockLeft);
              break;
          }
        }
      }
    }

    private void SelectTransition(int index)
    {
      if (FsmEditor.SelectedState == null || index <= -1 || index >= FsmEditor.SelectedState.Transitions.Length)
        return;
      this.SelectTransition(FsmEditor.SelectedState.Transitions[index]);
    }

    private void SelectTransition(FsmTransition transition) => EditorCommands.SelectTransition((object) transition);

    private void SelectUp()
    {
      if (EditorGUI.actionKey)
        return;
      if (FsmEditor.SelectedTransition != null)
      {
        int selectedTransitionIndex = FsmEditor.SelectedTransitionIndex;
        if (selectedTransitionIndex <= 0)
          return;
        this.SelectTransition(selectedTransitionIndex - 1);
      }
      else
      {
        if (FsmEditor.SelectedState == null)
          return;
        FsmState above = FsmGraphView.FindAbove(FsmEditor.SelectedFsm, FsmEditor.SelectedState);
        if (above == null)
          return;
        FsmEditor.SelectState(above, true);
      }
    }

    private void SelectDown()
    {
      if (EditorGUI.actionKey)
        return;
      if (FsmEditor.SelectedTransition != null)
      {
        int selectedTransitionIndex = FsmEditor.SelectedTransitionIndex;
        if (selectedTransitionIndex >= FsmEditor.SelectedState.Transitions.Length - 1)
          return;
        this.SelectTransition(selectedTransitionIndex + 1);
      }
      else
      {
        if (FsmEditor.SelectedState == null)
          return;
        FsmState below = FsmGraphView.FindBelow(FsmEditor.SelectedFsm, FsmEditor.SelectedState);
        if (below == null)
          return;
        FsmEditor.SelectState(below, true);
      }
    }

    private void SelectLeft()
    {
      if (EditorGUI.actionKey)
        return;
      if (FsmEditor.SelectedTransition != null)
      {
        if (string.IsNullOrEmpty(FsmEditor.SelectedTransition.ToState))
          return;
        FsmEditor.SelectStateByName(FsmEditor.SelectedTransition.ToState);
      }
      else
      {
        if (FsmEditor.SelectedState == null)
          return;
        FsmState left = FsmGraphView.FindLeft(FsmEditor.SelectedFsm, FsmEditor.SelectedState);
        if (left == null)
          return;
        FsmEditor.SelectState(left, true);
      }
    }

    private void SelectRight()
    {
      if (EditorGUI.actionKey)
        return;
      if (FsmEditor.SelectedTransition != null)
      {
        if (string.IsNullOrEmpty(FsmEditor.SelectedTransition.ToState))
          return;
        FsmEditor.SelectStateByName(FsmEditor.SelectedTransition.ToState);
      }
      else
      {
        if (FsmEditor.SelectedState == null)
          return;
        FsmState right = FsmGraphView.FindRight(FsmEditor.SelectedFsm, FsmEditor.SelectedState);
        if (right == null)
          return;
        FsmEditor.SelectState(right, true);
      }
    }

    private static FsmState FindAbove(Fsm fsm, FsmState state)
    {
      Vector2 vector2_1 = new Vector2();
      ref Vector2 local1 = ref vector2_1;
      Rect position1 = state.Position;
      double x1 = (double) position1.x;
      position1 = state.Position;
      double num1 = (double) position1.width / 2.0;
      double num2 = x1 + num1;
      position1 = state.Position;
      double y1 = (double) position1.y;
      local1 = new Vector2((float) num2, (float) y1);
      float num3 = float.MaxValue;
      FsmState fsmState = (FsmState) null;
      foreach (FsmState state1 in fsm.States)
      {
        if (state1 != state)
        {
          Rect position2 = state1.Position;
          if ((double) position2.y <= (double) vector2_1.y)
          {
            Vector2 vector2_2 = new Vector2();
            ref Vector2 local2 = ref vector2_2;
            position2 = state1.Position;
            double x2 = (double) position2.x;
            position2 = state1.Position;
            double num4 = (double) position2.width / 2.0;
            double num5 = x2 + num4;
            position2 = state1.Position;
            double y2 = (double) position2.y;
            local2 = new Vector2((float) num5, (float) y2);
            float num6 = Math.Abs(vector2_1.x - vector2_2.x) * 2f + vector2_1.y - vector2_2.y;
            if ((double) num6 < (double) num3)
            {
              num3 = num6;
              fsmState = state1;
            }
          }
        }
      }
      return fsmState;
    }

    private static FsmState FindBelow(Fsm fsm, FsmState state)
    {
      Vector2 vector2_1 = new Vector2();
      ref Vector2 local1 = ref vector2_1;
      Rect position1 = state.Position;
      double x1 = (double) position1.x;
      position1 = state.Position;
      double num1 = (double) position1.width / 2.0;
      double num2 = x1 + num1;
      position1 = state.Position;
      double yMax = (double) position1.yMax;
      local1 = new Vector2((float) num2, (float) yMax);
      float num3 = float.MaxValue;
      FsmState fsmState = (FsmState) null;
      foreach (FsmState state1 in fsm.States)
      {
        if (state1 != state)
        {
          Rect position2 = state1.Position;
          if ((double) position2.y >= (double) vector2_1.y)
          {
            Vector2 vector2_2 = new Vector2();
            ref Vector2 local2 = ref vector2_2;
            position2 = state1.Position;
            double x2 = (double) position2.x;
            position2 = state1.Position;
            double num4 = (double) position2.width / 2.0;
            double num5 = x2 + num4;
            position2 = state1.Position;
            double y = (double) position2.y;
            local2 = new Vector2((float) num5, (float) y);
            float num6 = Mathf.Abs(vector2_1.x - vector2_2.x) * 2f + vector2_2.y - vector2_1.y;
            if ((double) num6 < (double) num3)
            {
              num3 = num6;
              fsmState = state1;
            }
          }
        }
      }
      return fsmState;
    }

    private static FsmState FindLeft(Fsm fsm, FsmState state)
    {
      Vector2 vector2_1 = new Vector2();
      ref Vector2 local1 = ref vector2_1;
      Rect position1 = state.Position;
      double x = (double) position1.x;
      position1 = state.Position;
      double y1 = (double) position1.y;
      position1 = state.Position;
      double num1 = (double) position1.height / 2.0;
      double num2 = y1 + num1;
      local1 = new Vector2((float) x, (float) num2);
      float num3 = float.MaxValue;
      FsmState fsmState = (FsmState) null;
      foreach (FsmState state1 in fsm.States)
      {
        if (state1 != state)
        {
          Rect position2 = state1.Position;
          if ((double) position2.xMax <= (double) vector2_1.x)
          {
            Vector2 vector2_2 = new Vector2();
            ref Vector2 local2 = ref vector2_2;
            position2 = state1.Position;
            double xMax = (double) position2.xMax;
            position2 = state1.Position;
            double y2 = (double) position2.y;
            position2 = state1.Position;
            double num4 = (double) position2.height / 2.0;
            double num5 = y2 + num4;
            local2 = new Vector2((float) xMax, (float) num5);
            float num6 = (float) ((double) vector2_1.x - (double) vector2_2.x + (double) Mathf.Abs(vector2_1.y - vector2_2.y) * 2.0);
            if ((double) num6 < (double) num3)
            {
              num3 = num6;
              fsmState = state1;
            }
          }
        }
      }
      return fsmState;
    }

    private static FsmState FindRight(Fsm fsm, FsmState state)
    {
      Vector2 center = state.Position.center;
      float num1 = float.MaxValue;
      FsmState fsmState = (FsmState) null;
      foreach (FsmState state1 in fsm.States)
      {
        if (state1 != state)
        {
          Rect position = state1.Position;
          if ((double) position.x >= (double) center.x)
          {
            Vector2 vector2 = new Vector2();
            ref Vector2 local = ref vector2;
            position = state1.Position;
            double x = (double) position.x;
            position = state1.Position;
            double y = (double) position.y;
            position = state1.Position;
            double num2 = (double) position.height / 2.0;
            double num3 = y + num2;
            local = new Vector2((float) x, (float) num3);
            float num4 = (float) ((double) vector2.x - (double) center.x + (double) Mathf.Abs(center.y - vector2.y) * 2.0);
            if ((double) num4 < (double) num1)
            {
              num1 = num4;
              fsmState = state1;
            }
          }
        }
      }
      return fsmState;
    }

    private void UpdateMouseOverInfo(Vector2 mousePos)
    {
      FsmState mouseOverState = this.mouseOverState;
      this.mouseOverState = (FsmState) null;
      this.mouseOverTransition = (FsmTransition) null;
      this.mouseOverGlobalTransition = (FsmTransition) null;
      this.mouseOverStartBox = false;
      if (FsmEditor.SelectedFsm == null)
        return;
      this.mouseOverStartBox = this.startBox.Contains(mousePos);
      foreach (FsmState visibleState in FsmGraphView.visibleStates)
      {
        Rect source = this.ScaleRect(visibleState.Position);
        if (source.Contains(mousePos))
        {
          this.mouseOverState = visibleState;
          if (this.mouseOverState != mouseOverState)
            this.mouseOverStateStartTime = EditorApplication.timeSinceStartup;
          int num = (int) Mathf.Floor((mousePos.y - source.y) / FsmEditorStyles.StateRowHeight);
          if (num > 0 && num <= visibleState.Transitions.Length)
            this.mouseOverTransition = visibleState.Transitions[num - 1];
        }
        else
        {
          List<FsmTransition> transitionsToState = FsmEditor.SelectedFsm.GetGlobalTransitionsToState(visibleState);
          if (transitionsToState.Count != 0)
          {
            Rect rect = new Rect(source)
            {
              height = (float) transitionsToState.Count * FsmEditorStyles.StateRowHeight
            };
            rect.y -= rect.height + 32f * this.zoom;
            if (rect.Contains(mousePos))
            {
              int index = (int) Mathf.Floor((mousePos.y - rect.y) / FsmEditorStyles.StateRowHeight);
              this.mouseOverGlobalTransition = transitionsToState[index];
            }
          }
        }
      }
    }

    private void HandleMouseDown()
    {
      switch (this.currentEvent.button)
      {
        case 0:
        case 1:
          if (this.mouseOverStartBox)
          {
            FsmEditor.Selection.SelectState(FsmEditor.SelectedFsm.GetState(FsmEditor.SelectedFsm.StartState), this.currentEvent.shift);
            break;
          }
          if (this.mouseOverGlobalTransition != null)
          {
            if (this.currentEvent.clickCount > 1)
              FsmEditor.Inspector.SetMode(InspectorMode.EventManager);
            FsmEditor.EventsManager.SelectEvent(this.mouseOverGlobalTransition.FsmEvent);
            FsmEditor.Selection.SelectState(FsmEditor.SelectedFsm.GetState(this.mouseOverGlobalTransition.ToState), this.currentEvent.shift);
            if (this.currentEvent.alt && Application.isPlaying)
            {
              FsmEditor.SelectedFsm.Event(this.mouseOverGlobalTransition.FsmEvent);
              break;
            }
            break;
          }
          if (this.mouseOverTransition != null)
          {
            if (this.currentEvent.alt)
            {
              FsmEditor.SelectState(FsmEditor.SelectStateByName(this.mouseOverTransition.ToState), true);
              FsmEditor.Inspector.SetMode(InspectorMode.StateInspector);
            }
            else
              FsmEditor.Selection.SelectState(this.mouseOverState, this.currentEvent.shift);
            if (FsmEditor.SelectedState == this.mouseOverState)
            {
              FsmEditor.Selection.SelectTransition(this.mouseOverTransition);
              if (this.currentEvent.clickCount > 1)
                FsmEditor.Inspector.SetMode(InspectorMode.EventManager);
              FsmEditor.EventsManager.SelectEvent(this.mouseOverTransition.FsmEvent);
            }
            if (this.currentEvent.button == 0)
            {
              if (this.draggingMode != FsmGraphView.DraggingMode.Transition)
                this.MouseDragStart();
              this.draggingMode = FsmGraphView.DraggingMode.Transition;
              if (this.currentEvent.alt && Application.isPlaying)
              {
                FsmEditor.SelectedFsm.Event(this.mouseOverTransition.FsmEvent);
                break;
              }
              break;
            }
            break;
          }
          if (this.mouseOverState != null)
          {
            FsmEditor.Selection.SelectState(this.mouseOverState, this.currentEvent.shift, this.currentEvent.alt);
            FsmEditor.Inspector.SetMode(InspectorMode.StateInspector);
            this.frameState = true;
            if (this.currentEvent.button == 0)
            {
              if (this.draggingMode != FsmGraphView.DraggingMode.State)
                this.MouseDragStart();
              this.draggingMode = FsmGraphView.DraggingMode.State;
              if (this.currentEvent.alt && Application.isPlaying)
              {
                FsmTransition stateFromActiveState = FsmEditor.SelectedFsm.FindTransitionToStateFromActiveState(this.mouseOverState);
                if (stateFromActiveState != null)
                {
                  FsmEditor.SelectedFsm.Event(stateFromActiveState.FsmEvent);
                  FsmEditor.Selection.SelectTransition((FsmTransition) null);
                  break;
                }
                if (EditorGUI.actionKey)
                {
                  FsmEditor.SelectedFsm.SetState(this.mouseOverState.Name);
                  break;
                }
                UnityEngine.Debug.Log((object) string.Format(Strings.Log_No_Transition_To_State, (object) this.mouseOverState.Name));
                EditorApplication.Beep();
                break;
              }
              break;
            }
            break;
          }
          FsmEditor.Selection.SelectState((FsmState) null, this.currentEvent.shift, this.currentEvent.alt);
          if (this.currentEvent.button == 0 && !Keyboard.Alt())
          {
            if (this.draggingMode != FsmGraphView.DraggingMode.Selection)
              this.MouseDragStart();
            this.draggingMode = FsmGraphView.DraggingMode.Selection;
            Event.current.Use();
            break;
          }
          break;
      }
      this.UpdateVisibility();
      FsmEditor.Repaint();
      ToolWindow.UpdateView();
    }

    private void MouseDragStart()
    {
      this.dragStartPos = this.currentMousePos;
      this.dragStarted = false;
      this.dragStates = new List<FsmState>((IEnumerable<FsmState>) FsmEditor.Selection.States);
      this.dragStateStartPositions = new List<Vector2>();
      foreach (FsmState dragState in this.dragStates)
        this.dragStateStartPositions.Add(new Vector2(dragState.Position.x, dragState.Position.y));
      this.allStateStartPositions = new List<Vector2>();
      foreach (FsmState state in FsmEditor.SelectedFsm.States)
      {
        List<Vector2> stateStartPositions = this.allStateStartPositions;
        Rect position = state.Position;
        double x = (double) position.x;
        position = state.Position;
        double y = (double) position.y;
        Vector2 vector2 = new Vector2((float) x, (float) y);
        stateStartPositions.Add(vector2);
      }
    }

    private bool OverDragThreshold() => (double) (this.currentMousePos - this.dragStartPos).magnitude > 3.0;

    private void HandleMouseDrag()
    {
      FsmEditor.Repaint();
      switch (this.draggingMode)
      {
        case FsmGraphView.DraggingMode.State:
          if (!this.dragStarted)
          {
            this.dragStarted = this.OverDragThreshold();
            if (FsmGraphView.editingDisabled || !this.dragStarted)
              break;
            this.ExpandGraphBounds();
            break;
          }
          if (FsmGraphView.editingDisabled)
            break;
          this.DoDragStates();
          break;
        case FsmGraphView.DraggingMode.Transition:
          if (this.dragStarted)
          {
            this.canvasView.DoEdgeScroll();
            break;
          }
          if (this.mouseOverState == FsmEditor.SelectedState)
            break;
          this.dragStarted = true;
          this.ExpandGraphBounds();
          break;
        case FsmGraphView.DraggingMode.Selection:
          if (this.dragStarted)
            break;
          this.dragStarted = this.OverDragThreshold();
          break;
      }
    }

    private void DoDragStates()
    {
      Vector2 vector2_1 = this.canvasView.DoEdgeScroll();
      int index = 0;
      Vector2 vector2_2 = this.currentMousePos - this.dragStartPos + vector2_1;
      foreach (FsmState dragState in this.dragStates)
      {
        FsmGraphView.UpdateStatePosition(dragState, this.dragStateStartPositions[index] + vector2_2 / this.zoom);
        if (this.currentEvent.shift)
          this.ConstrainDragToAxis();
        else
          this.dragConstraint = FsmGraphView.DragConstraint.None;
        ++index;
      }
      if ((FsmEditorSettings.SnapToGrid || !EditorGUI.actionKey) && (!FsmEditorSettings.SnapToGrid || EditorGUI.actionKey))
        return;
      this.SnapDraggedStatesToGrid();
    }

    private void ConstrainDragToAxis()
    {
      if (this.dragConstraint == FsmGraphView.DragConstraint.None)
      {
        float f1 = this.currentMousePos.x - this.dragStartPos.x;
        float f2 = this.currentMousePos.y - this.dragStartPos.y;
        if ((double) Math.Abs(f1) < 3.0 && (double) Math.Abs(f1) < 3.0)
          return;
        this.dragConstraint = (double) Mathf.Abs(f1) > (double) Mathf.Abs(f2) ? FsmGraphView.DragConstraint.X : FsmGraphView.DragConstraint.Y;
      }
      else if (this.dragConstraint == FsmGraphView.DragConstraint.X)
      {
        int index = 0;
        foreach (FsmState selectedState in FsmEditor.SelectedStates)
        {
          FsmState fsmState = selectedState;
          Rect position = selectedState.Position;
          double x = (double) position.x;
          double y = (double) this.dragStateStartPositions[index].y;
          position = selectedState.Position;
          double width = (double) position.width;
          position = selectedState.Position;
          double height = (double) position.height;
          Rect rect = new Rect((float) x, (float) y, (float) width, (float) height);
          fsmState.Position = rect;
          ++index;
        }
      }
      else
      {
        int index = 0;
        foreach (FsmState selectedState in FsmEditor.SelectedStates)
        {
          FsmState fsmState = selectedState;
          double x = (double) this.dragStateStartPositions[index].x;
          Rect position = selectedState.Position;
          double y = (double) position.y;
          position = selectedState.Position;
          double width = (double) position.width;
          position = selectedState.Position;
          double height = (double) position.height;
          Rect rect = new Rect((float) x, (float) y, (float) width, (float) height);
          fsmState.Position = rect;
          ++index;
        }
      }
    }

    private void SnapStateToGrid(FsmState state)
    {
      if (state == null)
        return;
      Vector2 vector2 = new Vector2(state.Position.x, state.Position.y);
      int snapGridSize = FsmEditorSettings.SnapGridSize;
      Vector2 offset = new Vector2(FsmGraphView.SnapFloatToGrid(vector2.x, (float) snapGridSize), FsmGraphView.SnapFloatToGrid(vector2.y, (float) snapGridSize)) - vector2;
      FsmGraphView.TranslateState(state, offset);
    }

    private void SnapDraggedStatesToGrid()
    {
      if (FsmEditor.SelectedState == null)
        return;
      Vector2 vector2 = new Vector2();
      if (FsmEditor.SelectedState != null)
      {
        ref Vector2 local = ref vector2;
        Rect position = FsmEditor.SelectedState.Position;
        double x = (double) position.x;
        position = FsmEditor.SelectedState.Position;
        double y = (double) position.y;
        local = new Vector2((float) x, (float) y);
      }
      int snapGridSize = FsmEditorSettings.SnapGridSize;
      Vector2 offset = new Vector2(FsmGraphView.SnapFloatToGrid(vector2.x, (float) snapGridSize), FsmGraphView.SnapFloatToGrid(vector2.y, (float) snapGridSize)) - vector2;
      foreach (FsmState dragState in this.dragStates)
        FsmGraphView.TranslateState(dragState, offset);
    }

    private static float SnapFloatToGrid(float x, float gridSize) => Mathf.Floor(x / gridSize) * gridSize;

    public void CancelDraggingStates()
    {
      if (this.draggingMode != FsmGraphView.DraggingMode.State)
        return;
      this.draggingMode = FsmGraphView.DraggingMode.None;
      this.UpdateGraphBounds(this.currentMousePos);
    }

    private void HandleMouseUp()
    {
      FsmEditor.MouseUpHandled();
      FsmEditor.Repaint();
      switch (this.draggingMode)
      {
        case FsmGraphView.DraggingMode.State:
          if (FsmGraphView.editingDisabled)
          {
            this.DisplayEditingDisabledNotification();
            break;
          }
          if (this.dragStarted)
          {
            this.UpdateGraphBounds(this.currentMousePos);
            List<Vector2> vector2List = new List<Vector2>();
            for (int index = 0; index < FsmEditor.SelectedFsm.States.Length; ++index)
            {
              FsmState state = FsmEditor.SelectedFsm.States[index];
              vector2List.Add(new Vector2(state.Position.x, state.Position.y));
              FsmGraphView.UpdateStatePosition(state, this.allStateStartPositions[index]);
            }
            FsmEditor.RecordUndo(Strings.Command_Move_State);
            for (int index = 0; index < FsmEditor.SelectedFsm.States.Length; ++index)
              FsmGraphView.UpdateStatePosition(FsmEditor.SelectedFsm.States[index], vector2List[index]);
            FsmEditor.SetFsmDirty(false);
            break;
          }
          if (FsmEditor.SelectedState != null && EditorGUI.actionKey && !this.currentEvent.alt)
          {
            if (this.currentEvent.shift)
            {
              EditorCommands.DeleteSelectedState();
              break;
            }
            if (EditorCommands.AddTransitionToSelectedState())
            {
              if (!FsmEditor.SelectedState.HasFinishedTransition())
                FsmGraphView.ContextMenuSelectEvent((object) FsmEvent.Finished);
              else
                FsmGraphView.GenerateEventSelectionsMenu().ShowAsContext();
              this.UpdateGraphBounds(this.currentMousePos);
              break;
            }
            break;
          }
          break;
        case FsmGraphView.DraggingMode.Transition:
          if (FsmGraphView.editingDisabled)
          {
            this.DisplayEditingDisabledNotification();
            break;
          }
          if (this.dragStarted)
          {
            this.UpdateGraphBounds(this.currentMousePos);
            if (FsmEditor.SelectedTransition != null)
            {
              if (this.mouseOverState != null)
              {
                FsmGraphView.SetSelectedTransitionTarget(this.mouseOverState);
                break;
              }
              if (EditorGUI.actionKey)
              {
                EditorCommands.SetTransitionTarget(FsmEditor.SelectedTransition, this.AddState(new Vector2(this.dummyDraggingState.Position.x - 100000f, this.dummyDraggingState.Position.y - 100000f)).Name);
                break;
              }
              break;
            }
            break;
          }
          if (EditorGUI.actionKey && this.currentEvent.shift)
          {
            EditorCommands.DeleteSelectedTransition();
            break;
          }
          break;
        case FsmGraphView.DraggingMode.Selection:
          if (this.dragStarted)
          {
            FsmEditor.Repaint(true);
            float width = Mathf.Abs(this.currentMousePos.x - this.dragStartPos.x);
            float height = Mathf.Abs(this.currentMousePos.y - this.dragStartPos.y);
            Rect rect1 = new Rect(Mathf.Min(this.dragStartPos.x, this.currentMousePos.x), Mathf.Min(this.dragStartPos.y, this.currentMousePos.y), width, height);
            if (FsmEditor.SelectedFsm != null)
            {
              bool flag = (double) this.dragStartPos.x > (double) this.currentMousePos.x;
              List<FsmState> stateSelection = new List<FsmState>();
              foreach (FsmState state in FsmEditor.SelectedFsm.States)
              {
                Rect rect2 = this.ScaleRect(state.Position);
                if (flag)
                {
                  if (rect1.IntersectsWith(rect2))
                    stateSelection.Add(state);
                }
                else if (rect1.Contains(rect2))
                  stateSelection.Add(state);
              }
              FsmEditor.Selection.SelectStates(stateSelection, this.currentEvent.shift, this.currentEvent.alt);
              break;
            }
            break;
          }
          if (EditorGUI.actionKey && !this.currentEvent.shift)
          {
            this.AddState(this.currentMousePos / this.zoom);
            break;
          }
          break;
      }
      this.draggingMode = FsmGraphView.DraggingMode.None;
      if (this.frameState && this.currentEvent.button == 0 && (FsmEditorSettings.FrameSelectedState && !this.dragStarted) && FsmEditor.GraphView.IsStateOffscreen(FsmEditor.SelectedState))
      {
        FsmEditor.GraphView.FrameState(FsmEditor.SelectedState);
        this.frameState = false;
      }
      FsmEditor.Repaint();
    }

    public void EnableEditing() => FsmGraphView.editingDisabled = false;

    public void DisableEditing(string reason)
    {
      FsmGraphView.editingDisabled = true;
      FsmGraphView.editingDisabledReason = reason;
    }

    private void DisplayEditingDisabledNotification() => FsmEditor.Window.ShowNotification(new GUIContent(FsmGraphView.editingDisabledReason));

    private void DoContextMenu()
    {
      if (this.eventType == UnityEngine.EventType.Repaint && this.delayedContextMenu != null)
      {
        if (this.forceContextMenu)
        {
          this.forceContextMenu = false;
          this.delayedContextMenu = (GenericMenu) null;
          FsmEditor.Window.SendEvent(new Event()
          {
            type = UnityEngine.EventType.MouseUp
          });
          GUIHelpers.SafeExitGUI();
        }
        else
        {
          this.delayedContextMenu.ShowAsContext();
          FsmEditor.Repaint(true);
          this.forceContextMenu = true;
        }
      }
      if (this.eventType != UnityEngine.EventType.ContextClick || !this.mouseOverGraphView)
        return;
      this.contextMenuPos = this.currentMousePos;
      if (FsmGraphView.editingDisabled)
      {
        this.DisplayEditingDisabledNotification();
      }
      else
      {
        this.contextMenuPos = this.currentMousePos;
        if (FsmEditor.SelectedFsm == null)
        {
          if ((UnityEngine.Object) Selection.activeGameObject != (UnityEngine.Object) null && !EditorUtility.IsPersistent((UnityEngine.Object) Selection.activeGameObject))
            FsmGraphView.GenerateAddFsmContextMenu().ShowAsContext();
        }
        else
        {
          this.delayedContextMenu = this.GenerateContextMenu();
          FsmEditor.Repaint(true);
        }
      }
      this.currentEvent.Use();
    }

    private static GenericMenu GenerateAddFsmContextMenu()
    {
      GenericMenu menu = new GenericMenu();
      menu.AddItem(new GUIContent(Strings.Menu_Add_FSM), false, new GenericMenu.MenuFunction(FsmBuilder.AddFsmToSelected));
      if (FsmEditor.Clipboard.CanPaste())
        menu.AddItem(new GUIContent(Strings.Menu_Paste_FSM), false, new GenericMenu.MenuFunction(EditorCommands.PasteFsm));
      else
        menu.AddDisabledItem(new GUIContent(Strings.Menu_Paste_FSM));
      Menus.AddTemplateItems(menu, Strings.Command_Paste_Template, new GenericMenu.MenuFunction2(EditorCommands.AddTemplateToSelected));
      Menus.AddTemplateItems(menu, Strings.Label_Use_Template, new GenericMenu.MenuFunction2(EditorCommands.AddFsmAndUseTemplateWithSelected));
      return menu;
    }

    [Localizable(false)]
    private GenericMenu GenerateContextMenu()
    {
      GenericMenu menu1 = new GenericMenu();
      string menuRoot1 = Menus.MakeMenuRoot(Strings.Menu_Transition_Event);
      GenericMenu genericMenu;
      if (this.mouseOverGlobalTransition != null)
      {
        FsmEditor.SelectStateByName(this.mouseOverGlobalTransition.ToState, false);
        FsmGraphView.selectedGlobalTransition = this.mouseOverGlobalTransition;
        List<FsmInfo> fsmInfoList = new List<FsmInfo>();
        foreach (FsmInfo eventSentBy in FsmSearch.GetSearch(FsmEditor.SelectedFsm).GetEventSentByList(FsmGraphView.selectedGlobalTransition.EventName))
        {
          if (FsmInfo.DoesActionEventTargetGlobalTransition(eventSentBy, FsmEditor.SelectedFsm))
            fsmInfoList.Add(eventSentBy);
        }
        if (fsmInfoList.Count == 0)
        {
          menu1.AddDisabledItem(new GUIContent(Strings.Menu_Sent_By));
        }
        else
        {
          foreach (FsmInfo fsmInfo in fsmInfoList)
          {
            if (fsmInfo.fsm == FsmEditor.SelectedFsm)
              menu1.AddItem(new GUIContent(string.Format("{0}/{1} : {2}", (object) Strings.Menu_Sent_By, (object) fsmInfo.state.Name, (object) Labels.GetActionLabel(fsmInfo.action))), false, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
          }
          foreach (FsmInfo fsmInfo in fsmInfoList)
          {
            if (fsmInfo.fsm != FsmEditor.SelectedFsm)
            {
              string fullStateLabel = Labels.GetFullStateLabel(fsmInfo.state);
              menu1.AddItem(new GUIContent(string.Format("{0}/{1} : {2}", (object) Strings.Menu_Sent_By, (object) fullStateLabel, (object) Labels.GetActionLabel(fsmInfo.action))), false, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
            }
          }
        }
        menu1.AddSeparator("");
        foreach (FsmEvent fsmEvent in FsmEditor.SelectedFsm.Events)
          menu1.AddItem(Menus.FormatItem(menuRoot1 + fsmEvent.Name), fsmEvent == FsmGraphView.selectedGlobalTransition.FsmEvent, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectGlobalEvent), (object) fsmEvent);
        FsmEvent fsmEvent1 = FsmEvent.GetFsmEvent("FINISHED");
        menu1.AddItem(new GUIContent(menuRoot1 + "FINISHED"), FsmGraphView.selectedGlobalTransition.FsmEvent == fsmEvent1, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectGlobalEvent), (object) fsmEvent1);
        genericMenu = ActionEditor.AddCommonEventMenus(menu1, menuRoot1, FsmGraphView.selectedGlobalTransition.FsmEvent, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectGlobalEvent));
        genericMenu.AddSeparator("");
        genericMenu.AddItem(new GUIContent(Strings.Menu_Clear_Transition_Event), false, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectGlobalEvent), (object) null);
        genericMenu.AddItem(new GUIContent(Strings.Menu_Delete_Transition), false, new GenericMenu.MenuFunction2(this.DeleteGlobalTransition), (object) this.mouseOverGlobalTransition);
      }
      else if (FsmEditor.SelectedTransition != null)
      {
        List<FsmInfo> fsmInfoList = new List<FsmInfo>();
        foreach (FsmInfo eventSentBy in FsmSearch.GetSearch(FsmEditor.SelectedFsm).GetEventSentByList(FsmEditor.SelectedTransition.EventName))
        {
          if (FsmInfo.DoesActionEventTargetLocalTransition(eventSentBy, FsmEditor.SelectedState))
            fsmInfoList.Add(eventSentBy);
        }
        if (fsmInfoList.Count == 0)
        {
          menu1.AddDisabledItem(new GUIContent(Strings.Menu_Sent_By));
        }
        else
        {
          string str = Menus.MakeMenuRoot(Strings.Menu_GraphView_Sent_By);
          foreach (FsmInfo fsmInfo in fsmInfoList)
          {
            if (fsmInfo.fsm == FsmEditor.SelectedFsm)
              menu1.AddItem(new GUIContent(str + fsmInfo.state.Name + " : " + Labels.GetActionLabel(fsmInfo.action)), false, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
          }
          foreach (FsmInfo fsmInfo in fsmInfoList)
          {
            if (fsmInfo.fsm != FsmEditor.SelectedFsm)
            {
              string fullStateLabel = Labels.GetFullStateLabel(fsmInfo.state);
              menu1.AddItem(new GUIContent(str + fullStateLabel + " : " + Labels.GetActionLabel(fsmInfo.action)), false, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
            }
          }
        }
        menu1.AddSeparator("");
        FsmEvent fsmEvent1 = FsmEditor.SelectedTransition.FsmEvent;
        foreach (FsmEvent fsmEvent2 in FsmEditor.SelectedFsm.Events)
          menu1.AddItem(Menus.FormatItem(menuRoot1 + fsmEvent2.Name), fsmEvent2 == fsmEvent1, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectEvent), (object) fsmEvent2);
        FsmEvent fsmEvent3 = FsmEvent.GetFsmEvent("FINISHED");
        menu1.AddItem(new GUIContent(menuRoot1 + "FINISHED"), fsmEvent1 == fsmEvent3, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectEvent), (object) fsmEvent3);
        GenericMenu menu2 = ActionEditor.AddCommonEventMenus(menu1, menuRoot1, FsmEditor.SelectedTransition.FsmEvent, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectEvent));
        string toState = FsmEditor.SelectedTransition.ToState;
        string str1 = Menus.MakeMenuRoot(Strings.Menu_GraphView_Transition_Target);
        foreach (FsmState state in FsmEditor.SelectedFsm.States)
          menu2.AddItem(new GUIContent(str1 + state.Name), toState == state.Name, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectTransitionTarget), (object) state.Name);
        menu2.AddSeparator("");
        menu2.AddItem(new GUIContent(Strings.Menu_GraphView_Link_Style_Default), FsmEditor.SelectedTransition.LinkStyle == FsmTransition.CustomLinkStyle.Default, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionLinkStyle), (object) FsmTransition.CustomLinkStyle.Default);
        menu2.AddItem(new GUIContent(Strings.Menu_GraphView_Link_Style_Bezier), FsmEditor.SelectedTransition.LinkStyle == FsmTransition.CustomLinkStyle.Bezier, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionLinkStyle), (object) FsmTransition.CustomLinkStyle.Bezier);
        menu2.AddItem(new GUIContent(Strings.Menu_GraphView_Link_Style_Circuit), FsmEditor.SelectedTransition.LinkStyle == FsmTransition.CustomLinkStyle.Circuit, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionLinkStyle), (object) FsmTransition.CustomLinkStyle.Circuit);
        menu2.AddItem(new GUIContent(Strings.Menu_GraphView_Link_Style_Direct), FsmEditor.SelectedTransition.LinkStyle == FsmTransition.CustomLinkStyle.Direct, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionLinkStyle), (object) FsmTransition.CustomLinkStyle.Direct);
        menu2.AddItem(new GUIContent("Link Start/Auto"), FsmEditor.SelectedTransition.LinkConstraint == FsmTransition.CustomLinkConstraint.None, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionLinkConstraint), (object) FsmTransition.CustomLinkConstraint.None);
        menu2.AddItem(new GUIContent("Link Start/Lock To Left"), FsmEditor.SelectedTransition.LinkConstraint == FsmTransition.CustomLinkConstraint.LockLeft, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionLinkConstraint), (object) FsmTransition.CustomLinkConstraint.LockLeft);
        menu2.AddItem(new GUIContent("Link Start/Lock To Right"), FsmEditor.SelectedTransition.LinkConstraint == FsmTransition.CustomLinkConstraint.LockRight, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionLinkConstraint), (object) FsmTransition.CustomLinkConstraint.LockRight);
        menu2.AddItem(new GUIContent("Link End/Auto"), FsmEditor.SelectedTransition.LinkTarget == FsmTransition.CustomLinkTarget.None, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionLinkTarget), (object) FsmTransition.CustomLinkTarget.None);
        menu2.AddItem(new GUIContent("Link End/Lock To Left"), FsmEditor.SelectedTransition.LinkTarget == FsmTransition.CustomLinkTarget.LockLeft, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionLinkTarget), (object) FsmTransition.CustomLinkTarget.LockLeft);
        menu2.AddItem(new GUIContent("Link End/Lock To Right"), FsmEditor.SelectedTransition.LinkTarget == FsmTransition.CustomLinkTarget.LockRight, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionLinkTarget), (object) FsmTransition.CustomLinkTarget.LockRight);
        genericMenu = Menus.AddColorMenu(menu2, Strings.Menu_GraphView_Link_Color, FsmEditor.SelectedTransition.ColorIndex, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionColorIndex));
        genericMenu.AddSeparator("");
        genericMenu.AddItem(new GUIContent(Strings.Menu_Move_Transition_Up), false, new GenericMenu.MenuFunction2(EditorCommands.MoveTransitionUp), (object) FsmEditor.SelectedTransition);
        genericMenu.AddItem(new GUIContent(Strings.Menu_Move_Transition_Down), false, new GenericMenu.MenuFunction2(EditorCommands.MoveTransitionDown), (object) FsmEditor.SelectedTransition);
        genericMenu.AddSeparator("");
        genericMenu.AddItem(new GUIContent(Strings.Menu_Clear_Transition_Event), false, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectEvent), (object) null);
        genericMenu.AddItem(new GUIContent(Strings.Menu_Clear_Transition_Target), false, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectTransitionTarget), (object) string.Empty);
        genericMenu.AddItem(new GUIContent(Strings.Menu_Delete_Transition), false, new GenericMenu.MenuFunction(EditorCommands.DeleteSelectedTransition));
      }
      else if (FsmEditor.SelectedState != null)
      {
        string menuRoot2 = Menus.MakeMenuRoot(Strings.Menu_GraphView_Add_Transition);
        foreach (FsmEvent fsmEvent in FsmEditor.SelectedFsm.Events)
        {
          if (fsmEvent.Name != "PREPARE" && fsmEvent.Name != "CLEANUP")
            menu1.AddItem(Menus.FormatItem(menuRoot2 + fsmEvent.Name), false, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuAddTransition), (object) fsmEvent);
        }
        FsmEvent fsmEvent1 = FsmEvent.GetFsmEvent("FINISHED");
        menu1.AddItem(new GUIContent(menuRoot2 + "FINISHED"), false, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuAddTransition), (object) fsmEvent1);
        // GenericMenu menu2 = ActionEditor.AddCommonEventMenus(menu1, menuRoot2, (FsmEvent) null, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuAddTransition));
        // string menuRoot3 = Menus.MakeMenuRoot(Strings.Menu_GraphView_Add_Global_Transition);
        // foreach (FsmEvent fsmEvent2 in FsmEditor.SelectedFsm.Events)
        //   menu2.AddItem(Menus.FormatItem(menuRoot3 + fsmEvent2.Name), false, new GenericMenu.MenuFunction2(this.ContextMenuAddGlobalTransition), (object) fsmEvent2);
        // menu2.AddItem(new GUIContent(menuRoot3 + "FINISHED"), false, new GenericMenu.MenuFunction2(this.ContextMenuAddGlobalTransition), (object) fsmEvent1);
        // GenericMenu menu3 = ActionEditor.AddCommonEventMenus(menu2, menuRoot3, (FsmEvent) null, new GenericMenu.MenuFunction2(this.ContextMenuAddGlobalTransition));
        GenericMenu menu3 = menu1;
        menu3.AddSeparator("");
        bool flag11 = FsmEditor.SelectedState.IsStartState() || FsmEditor.SelectedState.IsPrepareState() ||
                      FsmEditor.SelectedState.IsCleanupState();
        if (flag11)
          menu3.AddDisabledItem(new GUIContent(Strings.Menu_GraphView_Set_Start_State));
        else
          menu3.AddItem(new GUIContent(Strings.Menu_GraphView_Set_Start_State), false, new GenericMenu.MenuFunction(EditorCommands.SetSelectedStateAsStartState));
        if (flag11) 
          menu3.AddDisabledItem(new GUIContent("Set as Prepare State"));
        else
          menu3.AddItem(new GUIContent("Set as Prepare State"), false, new GenericMenu.MenuFunction(EditorCommands.SetSelectedStateAsPrepareState));
        if (flag11)
          menu3.AddDisabledItem(new GUIContent("Set as Cleanup State"));
        else
          menu3.AddItem(new GUIContent("Set as Cleanup State"), false, new GenericMenu.MenuFunction(EditorCommands.SetSelectedStateAsCleanupState));
        genericMenu = Menus.AddColorMenu(menu3, Strings.Menu_GraphView_Set_Color, FsmEditor.SelectedState.ColorIndex, new GenericMenu.MenuFunction2(EditorCommands.SetSelectedStatesColorIndex));
        genericMenu.AddSeparator("");
        if (FsmEditor.SelectedStates.Count > 0)
          genericMenu.AddItem(FsmEditorContent.MenuGraphViewCopyStates, false, new GenericMenu.MenuFunction(EditorCommands.CopyStateSelection));
        else
          genericMenu.AddDisabledItem(FsmEditorContent.MenuGraphViewCopyStates);
        genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Save_Template), false, new GenericMenu.MenuFunction(EditorCommands.SaveSelectionAsTemplate));
        genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Delete_States), false, new GenericMenu.MenuFunction(EditorCommands.DeleteMultiSelection));
        genericMenu.AddSeparator("");
        genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Toggle_Breakpoint), false, new GenericMenu.MenuFunction(EditorCommands.ToggleBreakpointOnSelectedState));
      }
      else
      {
        menu1.AddItem(new GUIContent(Strings.Menu_GraphView_Add_State), false, new GenericMenu.MenuFunction(this.AddState));
        if (FsmEditor.Clipboard.CanPaste())
          menu1.AddItem(FsmEditorContent.MenuGraphViewPasteStates, false, new GenericMenu.MenuFunction(this.PasteStates));
        else
          menu1.AddDisabledItem(FsmEditorContent.MenuGraphViewPasteStates);
        Templates.InitList();
        foreach (string category in Templates.Categories)
        {
          foreach (FsmTemplate fsmTemplate in Templates.GetTemplatesInCategory(category))
            menu1.AddItem(new GUIContent(string.Format(Strings.Menu_Paste_Template, (object) category, (object) fsmTemplate.name)), false, new GenericMenu.MenuFunction2(this.PasteTemplate), (object) fsmTemplate);
        }
        if (Templates.List.Count == 0)
          menu1.AddDisabledItem(new GUIContent(Strings.Menu_GraphView_Paste_Template_None));
        // string menuRoot2 = Menus.MakeMenuRoot(Strings.Menu_GraphView_Add_Global_Transition);
        // foreach (FsmEvent fsmEvent in FsmEditor.SelectedFsm.Events)
        //   menu1.AddItem(Menus.FormatItem(menuRoot2 + fsmEvent.Name), false, new GenericMenu.MenuFunction2(this.ContextMenuAddGlobalTransition), (object) fsmEvent);
        // menu1.AddItem(new GUIContent(menuRoot2 + "FINISHED"), false, new GenericMenu.MenuFunction2(this.ContextMenuAddGlobalTransition), (object) FsmEvent.Finished);
        // genericMenu = ActionEditor.AddCommonEventMenus(menu1, menuRoot2, (FsmEvent) null, new GenericMenu.MenuFunction2(this.ContextMenuAddGlobalTransition));
        genericMenu = menu1;
        genericMenu.AddSeparator("");
        // genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Copy_FSM), false, new GenericMenu.MenuFunction(EditorCommands.CopyFsm));
        genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Save_Template), false, new GenericMenu.MenuFunction(EditorCommands.SaveFsmAsTemplate));
        // genericMenu.AddSeparator("");
        // genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Set_Watermark), false, new GenericMenu.MenuFunction(EditorCommands.ChooseWatermark));
        // genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Save_Screenshot), false, new GenericMenu.MenuFunction(this.TakeScreenshot));
        genericMenu.AddSeparator("");
        genericMenu.AddItem(new GUIContent("Export as Lua"), false, new GenericMenu.MenuFunction(EditorCommands.ExportFsmAsLua));
        genericMenu.AddItem(new GUIContent("Export and run"), false, new GenericMenu.MenuFunction(EditorCommands.ExportAndRun));
        genericMenu.AddItem(new GUIContent("Export and login"), false, new GenericMenu.MenuFunction(EditorCommands.ExportAndLogin));
        // if ((UnityEngine.Object) Selection.activeGameObject != (UnityEngine.Object) null)
        // {
        //   if ((UnityEngine.Object) FsmEditor.SelectedTemplate == (UnityEngine.Object) null)
        //   {
        //     genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Add_FSM_Component_New_FSM), false, new GenericMenu.MenuFunction(FsmBuilder.AddFsmToSelected));
        //     if (FsmEditor.Clipboard.CanPaste())
        //       genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Add_FSM_Component_Paste_FSM), false, new GenericMenu.MenuFunction(EditorCommands.PasteFsm));
        //     else
        //       genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_GraphView_Add_FSM_Component_Paste_FSM));
        //     Templates.InitList();
        //     foreach (string category in Templates.Categories)
        //     {
        //       foreach (FsmTemplate fsmTemplate in Templates.GetTemplatesInCategory(category))
        //         genericMenu.AddItem(new GUIContent(string.Format(Strings.Menu_GraphView_Add_FSM_Component_Use_Template, (object) category, (object) fsmTemplate.name)), false, new GenericMenu.MenuFunction2(EditorCommands.AddTemplate), (object) fsmTemplate);
        //     }
        //     if (Templates.List.Count == 0)
        //       genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_GraphView_Add_FSM_Component_Add_Template));
        //   }
        //   else
        //     genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Add_To_Selected), false, new GenericMenu.MenuFunction2(EditorCommands.AddTemplateToSelected), (object) FsmEditor.SelectedTemplate);
        // }
        // else
        //   genericMenu.AddDisabledItem((UnityEngine.Object) FsmEditor.SelectedTemplate == (UnityEngine.Object) null ? new GUIContent(Strings.Menu_GraphView_Add_FSM_Component) : new GUIContent(Strings.Menu_GraphView_Add_To_Selected));
        // if ((UnityEngine.Object) FsmEditor.SelectedTemplate == (UnityEngine.Object) null)
        //   genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Remove_FSM_Component), false, new GenericMenu.MenuFunction(EditorCommands.RemoveFsmComponent));
        // else
        //   genericMenu.AddItem(new GUIContent(Strings.Menu_GraphView_Delete_Template), false, new GenericMenu.MenuFunction(EditorCommands.DeleteTemplate));
        // genericMenu.AddSeparator("");
        // genericMenu.AddItem(new GUIContent("Graph View Preferences"), false, new GenericMenu.MenuFunction2(EditorCommands.OpenPreferences), (object) FsmEditorSettings.Categories.GraphView);
      }
      return genericMenu;
    }

    private static GenericMenu GenerateEventSelectionsMenu()
    {
      GenericMenu menu = new GenericMenu();
      FsmEvent fsmEvent1 = FsmEditor.SelectedTransition.FsmEvent;
      foreach (FsmEvent fsmEvent2 in FsmEditor.SelectedFsm.Events)
        menu.AddItem(Menus.FormatItem(fsmEvent2.Name), fsmEvent2 == fsmEvent1, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectEvent), (object) fsmEvent2);
      return ActionEditor.AddCommonEventMenus(menu, "", FsmEditor.SelectedTransition.FsmEvent, new GenericMenu.MenuFunction2(FsmGraphView.ContextMenuSelectEvent));
    }

    private static void ContextMenuAddTransition(object userdata)
    {
      EditorCommands.AddTransitionToSelectedState();
      FsmGraphView.ContextMenuSelectEvent(userdata);
    }

    private void ContextMenuAddGlobalTransition(object userdata)
    {
      if (FsmEditor.SelectedState == null)
        this.AddState();
      FsmGraphView.selectedGlobalTransition = EditorCommands.AddGlobalTransitionToSelectedState();
      if (FsmGraphView.selectedGlobalTransition == null)
        return;
      FsmGraphView.ContextMenuSelectGlobalEvent(userdata);
    }

    private static void ContextMenuSelectEvent(object userdata)
    {
      FsmEvent fsmEvent = (FsmEvent) userdata;
      FsmEditor.SelectedFsm.AddEvent(fsmEvent);
      EditorCommands.SetTransitionEvent(FsmEditor.SelectedTransition, fsmEvent);
      FsmEditor.EventsManager.Reset();
      FsmEditor.EventsManager.SelectEvent(fsmEvent);
      FsmGraphView.selectedGlobalTransition = (FsmTransition) null;
    }

    public static void ContextMenuSelectGlobalEvent(object userdata)
    {
      FsmEvent fsmEvent = (FsmEvent) userdata;
      FsmEditor.SelectedFsm.AddEvent(fsmEvent);
      EditorCommands.SetTransitionEvent(FsmGraphView.selectedGlobalTransition, fsmEvent);
      FsmEditor.EventsManager.Reset();
      FsmEditor.EventsManager.SelectEvent(fsmEvent);
      FsmGraphView.selectedGlobalTransition = (FsmTransition) null;
    }

    private static void ContextMenuSelectTransitionTarget(object userdata) => EditorCommands.SetTransitionTarget(FsmEditor.SelectedTransition, userdata as string);

    private void HandleDragAndDropObjects()
    {
      if (!this.mouseOverGraphView || this.eventType != UnityEngine.EventType.DragUpdated && this.eventType != UnityEngine.EventType.DragPerform)
        return;
      if (DragAndDropManager.AddAction != null || DragAndDropManager.MoveActions != null)
        this.UpdateDraggingActions();
      else if ((UnityEngine.Object) DragAndDropManager.AddTemplate != (UnityEngine.Object) null)
        this.UpdateDraggingTemplate();
      else
        this.UpdateGenericDrag();
    }

    private void UpdateGenericDrag()
    {
      UnityEngine.Object[] objectReferences = DragAndDrop.objectReferences;
      if (objectReferences == null || objectReferences.Length == 0)
        return;
      UnityEngine.Object @object = objectReferences[0];
      if (@object == (UnityEngine.Object) null || (FsmEditor.SelectedFsm != null || !(@object is GameObject)) && (!(@object is AnimationClip) && !(@object is Animation)))
        return;
      DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
      if (this.eventType != UnityEngine.EventType.DragPerform)
        return;
      if (FsmGraphView.editingDisabled)
        this.DisplayEditingDisabledNotification();
      else
        FsmEditorMacros.DropObjectOnGraphView(FsmEditor.SelectedFsm, @object, this.currentMousePos);
    }

    private void DoAutoSelectStateWhileDragging()
    {
      if (this.eventType != UnityEngine.EventType.DragUpdated || this.mouseOverState == null || EditorApplication.timeSinceStartup - this.mouseOverStateStartTime <= 0.5)
        return;
      FsmEditor.SelectState(this.mouseOverState, false);
    }

    private void UpdateDraggingTemplate()
    {
      DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
      this.DoAutoSelectStateWhileDragging();
      if (this.eventType == UnityEngine.EventType.DragPerform)
      {
        if (FsmGraphView.editingDisabled)
        {
          this.DisplayEditingDisabledNotification();
        }
        else
        {
          this.HandleDropTemplate(DragAndDropManager.AddTemplate);
          DragAndDrop.AcceptDrag();
        }
      }
      this.currentEvent.Use();
    }

    private void HandleDropTemplate(FsmTemplate template)
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      if (FsmEditor.SelectedFsm == null && (UnityEngine.Object) Selection.activeGameObject != (UnityEngine.Object) null)
        this.DoAddTemplateToGameObjectMenu();
      else if (this.mouseOverState != null)
        EditorCommands.AddRunFsmActionToState(this.mouseOverState, template);
      else
        this.DoAddTemplateToFsmMenu();
      FsmEditor.SetFsmDirty(true);
    }

    private void DoAddTemplateToGameObjectMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent("Paste Template"), false, new GenericMenu.MenuFunction2(EditorCommands.AddTemplateToSelected), (object) DragAndDropManager.AddTemplate);
      genericMenu.AddItem(new GUIContent("Use Template"), false, new GenericMenu.MenuFunction2(EditorCommands.AddFsmAndUseTemplateWithSelected), (object) DragAndDropManager.AddTemplate);
      genericMenu.ShowAsContext();
    }

    private void DoAddTemplateToFsmMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent("Paste Template"), false, new GenericMenu.MenuFunction2(this.PasteTemplate), (object) DragAndDropManager.AddTemplate);
      genericMenu.AddItem(new GUIContent("New State"), false, (GenericMenu.MenuFunction) (() => this.AddTemplateAsNewState(DragAndDropManager.AddTemplate)));
      genericMenu.ShowAsContext();
    }

    private void AddTemplateAsNewState(FsmTemplate template) => EditorCommands.AddRunFsmActionToState(this.AddState(this.currentMousePos, ObjectNames.NicifyVariableName(template.name)), template);

    private void UpdateDraggingActions()
    {
      if (Selection.activeObject == (UnityEngine.Object) null)
        return;
      DragAndDrop.visualMode = DragAndDropManager.MoveActions == null || !Event.current.shift ? DragAndDropVisualMode.Move : DragAndDropVisualMode.Copy;
      this.DoAutoSelectStateWhileDragging();
      if (this.eventType == UnityEngine.EventType.DragPerform)
      {
        if (FsmGraphView.editingDisabled)
        {
          this.DisplayEditingDisabledNotification();
        }
        else
        {
          DragAndDrop.AcceptDrag();
          if (DragAndDropManager.AddAction != null)
            this.DropNewAction(DragAndDropManager.AddAction);
          else if (DragAndDropManager.MoveActions != null)
            this.DropActions();
        }
      }
      this.currentEvent.Use();
    }

    private void DropNewAction(System.Type actionType)
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      DragAndDropManager.Reset();
      if (FsmEditor.SelectedFsm == null && (UnityEngine.Object) Selection.activeGameObject != (UnityEngine.Object) null)
      {
        FsmBuilder.AddFsmToSelected();
        if (FsmEditor.SelectedFsm != null)
        {
          FsmEditor.SelectStateByName(FsmEditor.SelectedFsm.StartState);
          EditorCommands.AddAction(FsmEditor.SelectedState, actionType);
          GUIUtility.ExitGUI();
        }
      }
      else if (this.mouseOverState != null)
      {
        EditorCommands.AddAction(this.mouseOverState, actionType);
        FsmEditor.SelectState(this.mouseOverState, false);
      }
      else
        EditorCommands.AddAction(this.AddState(this.currentMousePos, Labels.GetActionLabel(actionType)), actionType);
      FsmEditor.SetFsmDirty(true);
    }

    private void DropActions()
    {
      if (this.mouseOverState != null)
        FsmEditor.SelectState(this.mouseOverState, false);
      else
        FsmEditor.SelectState(this.AddState(this.currentMousePos), false);
      FsmEditor.StateInspector.DropDraggedActions();
      FsmEditor.SetFsmDirty(true);
    }

    private void DoLargeWatermarkText(string text) => FsmEditorStyles.LargeWatermarkText.Draw(new Rect(this.view.x + 5f, this.view.y + 5f, this.view.width - 5f, 100f), FsmEditorContent.TempContent(text), false, false, false, false);

    private void DoSelectionHints() => this.DoLargeWatermarkText((UnityEngine.Object) Selection.activeGameObject == (UnityEngine.Object) null || EditorUtility.IsPersistent((UnityEngine.Object) Selection.activeGameObject) ? Strings.Hint_Select_a_GameObject : Strings.Hint_Right_Click_to_Add_FSM);

    public void UpdateVisibility() => FsmGraphView.updateVisibility = true;

    private void DoUpdateVisibility()
    {
      FsmGraphView.visibleStates.Clear();
      FsmGraphView.visibleLinks.Clear();
      FsmGraphView.outputEvents.Clear();
      if (FsmEditor.SelectedFsm != null)
      {
        foreach (FsmState state in FsmEditor.SelectedFsm.States)
        {
          if (this.canvasView.IsVisible(state.Position))
          {
            FsmGraphView.visibleStates.Add(state);
            bool flag = FsmEditor.SelectedState == state || FsmEditor.SelectedStates.Contains(state);
            for (int transitionIndex = 0; transitionIndex < state.Transitions.Length; ++transitionIndex)
            {
              if (!flag)
                FsmGraphView.visibleLinks.Add(new Link()
                {
                  FromState = state,
                  TransitionIndex = transitionIndex
                });
              FsmEvent transitionEvent = state.GetTransitionEvent(transitionIndex);
              if (FsmEventManager.IsOutputEvent(transitionEvent))
                FsmGraphView.outputEvents.Add(transitionEvent);
            }
          }
        }
        foreach (FsmState state in FsmEditor.SelectedFsm.States)
        {
          if (!FsmGraphView.visibleStates.Contains(state))
          {
            for (int index = 0; index < state.Transitions.Length; ++index)
            {
              FsmTransition transition = state.Transitions[index];
              if (transition != null)
              {
                FsmState toFsmState = transition.ToFsmState;
                if (toFsmState != null && (FsmGraphView.visibleStates.Contains(toFsmState) || state.Position.Union(toFsmState.Position).IntersectsWith(this.canvasView.ViewRectInCanvas)))
                  FsmGraphView.visibleLinks.Add(new Link()
                  {
                    FromState = state,
                    TransitionIndex = index
                  });
              }
            }
          }
        }
      }
      FsmGraphView.updateVisibility = false;
    }

    private bool DrawTransitionEffects() => !DebugFlow.Active && GameStateTracker.CurrentState == GameState.Running && FsmEditorSettings.EnableTransitionEffects;

    private void DoFsmGraph(Rect area)
    {
      if (FsmEditor.SelectedFsm == null || !Application.isPlaying && FsmEditor.SelectedFsmUsesTemplate)
        return;
      GUILayout.BeginArea(area);
      this.DrawCanvas();
      this.DrawStartArrow();
      if (this.DrawTransitionEffects())
        this.DrawStateHistory(FsmTime.RealtimeSinceStartup, 0.5f);
      if (FsmEditorSettings.ShowCommentsInGraphView)
      {
        foreach (FsmState visibleState in FsmGraphView.visibleStates)
          this.DrawStateDescription(visibleState);
      }
      if (!FsmEditorSettings.DrawLinksBehindStates)
      {
        foreach (FsmState visibleState in FsmGraphView.visibleStates)
          this.DoFsmStateNode(visibleState);
      }
      foreach (DrawState drawStateFilter in Enum.GetValues(typeof (DrawState)))
      {
        foreach (Link visibleLink in FsmGraphView.visibleLinks)
        {
          if (visibleLink != null)
            this.DoLinkGUI(visibleLink.FromState, visibleLink.TransitionIndex, drawStateFilter);
        }
        foreach (FsmState selectedState in FsmEditor.SelectedStates)
        {
          if (selectedState != null)
          {
            for (int transitionIndex = 0; transitionIndex < selectedState.Transitions.Length; ++transitionIndex)
              this.DoLinkGUI(selectedState, transitionIndex, drawStateFilter);
          }
        }
      }
      foreach (FsmState visibleState in FsmGraphView.visibleStates)
        this.DoGlobalTransitionGUI(visibleState);
      if (FsmEditorSettings.DrawLinksBehindStates)
      {
        foreach (FsmState visibleState in FsmGraphView.visibleStates)
          this.DoFsmStateNode(visibleState);
      }
      if (GameStateTracker.CurrentState != GameState.Stopped)
      {
        if (DebugFlow.Active || !FsmEditorSettings.EnableTransitionEffects)
          this.DrawLastTransition();
        else
          this.DrawTransitionHistory(FsmTime.RealtimeSinceStartup, 0.5f);
      }
      if (this.draggingMode == FsmGraphView.DraggingMode.Selection && this.isRepaint && this.dragStarted)
        this.DrawSelectionRect();
      this.DrawFsmHelUrlButton(FsmEditor.SelectedFsm);
      GUILayout.EndArea();
    }

    public Rect ScaleRect(Rect rect) => rect.Scale(this.zoom);

    public static Rect ScaleRect(Rect rect, float scale, Vector2 minSize) => rect.Scale(scale).MinSize(minSize);

    private void DoFsmStateNode(FsmState state)
    {
      if (this.isRepaint)
        this.DrawFsmStateNode(state);
      if (state.HasErrors)
        this.DrawStateError(state);
      if (!FsmEditorSettings.ShowSendEventsIconOnStates)
        return;
      this.DoSendsEventGUI(state);
    }

    private void DrawFsmStateNode(FsmState state)
    {
      if (state.Position.width.Equals(0.0f))
        this.UpdateStateSize(state);
      Rect rect1 = this.ScaleRect(state.Position);
      if ((double) this.zoom > 0.5)
        FsmEditorStyles.DropShadowBox.Draw(rect1, new Color(0.0f, 0.0f, 0.0f, 0.5f));
      bool selected = FsmEditor.Selection.Contains(state);
      DrawState drawState = FsmDrawState.GetFsmStateDrawState(FsmEditor.SelectedFsm, state, selected);
      if (DebugFlow.Active)
      {
        if (drawState == DrawState.Paused)
          drawState = DrawState.Normal;
        if (state == DebugFlow.DebugState)
          drawState = DebugFlow.SelectedLogEntry == null || DebugFlow.SelectedLogEntry.LogType != FsmLogType.Break ? DrawState.Paused : DrawState.Breakpoint;
      }
      if (drawState != DrawState.Normal)
      {
        Color highlightColor = FsmEditorStyles.HighlightColors[(int) drawState];
        FsmEditorStyles.SelectionBox.Draw(rect1, highlightColor);
      }
      FsmEditorStyles.StateBox.Draw(rect1, false, false, false, false);
      GUIHelpers.BeginGuiBackgroundColor(PlayMakerPrefs.Colors[state.ColorIndex]);
      FsmEditorContent.StateTitleBox.text = FsmEditorContent.StateTitleBox.tooltip = state.Name;
      FsmEditorStyles.StateTitleBox.Draw(rect1, FsmEditorContent.StateTitleBox, false, false, false, false);
      for (int index = 0; index < state.Transitions.Length; ++index)
      {
        FsmTransition transition = state.Transitions[index];
        Rect rect2 = new Rect(rect1);
        rect2.y += FsmEditorStyles.StateRowHeight * (float) (index + 1);
        if (transition == FsmEditor.SelectedTransition)
          FsmEditorStyles.TransitionBoxSelected.Draw(rect2, Labels.GetEventLabel(transition), Color.white, Color.white);
        else
          FsmEditorStyles.TransitionBox.Draw(rect2, Labels.GetEventLabel(transition), FsmGraphView.GetTransitionBoxColor(state, transition), Color.black);
        if (FsmGraphView.outputEvents.Contains(transition.FsmEvent))
          FsmEditorStyles.OutputEventIndicator.Draw(rect2);
      }
      GUIHelpers.EndGuiBackgroundColor();
      if (state.IsBreakpoint)
        this.DrawBreakpoint(state);
      if (!FsmEditorSettings.ShowStateLoopCounts || !EditorApplication.isPlaying)
        return;
      rect1.Set(rect1.x, rect1.y - 20f, 100f, 20f);
      GUI.Label(rect1, state.maxLoopCount.ToString());
    }

    private void DoLinkGUI(FsmState fromState, int transitionIndex, DrawState drawStateFilter)
    {
      FsmTransition transition = fromState.Transitions[transitionIndex];
      DrawState transitionDrawState = FsmDrawState.GetFsmTransitionDrawState(FsmEditor.SelectedFsm, transition, FsmEditor.SelectedTransition == transition);
      if (transitionDrawState != drawStateFilter)
        return;
      FsmState toState = transition.ToFsmState;
      if (FsmEditor.Selection.Contains(transition) && this.draggingMode == FsmGraphView.DraggingMode.Transition && this.dragStarted)
      {
        toState = this.dummyDraggingState;
        if (this.mouseOverState != null)
        {
          this.dummyDraggingState.Position = this.mouseOverState.Position;
        }
        else
        {
          FsmGraphView.UpdateStatePosition(this.dummyDraggingState, this.currentMousePos / this.zoom);
          Rect rect = new Rect(this.dummyDraggingState.Position);
          rect.y -= FsmEditorStyles.StateRowHeight * 0.5f;
          rect.width = 0.0f;
          this.dummyDraggingState.Position = rect;
        }
      }
      if (!this.isRepaint || toState == null)
        return;
      Color linkColor = FsmGraphView.GetLinkColor(fromState, transition, transitionDrawState);
      float linkWidth = FsmEditorStyles.LinkWidths[(int) transitionDrawState];
      this.DoDrawLink(fromState, toState, transitionIndex, linkColor, linkWidth);
    }

    private static Color GetLinkColor(
      FsmState fromState,
      FsmTransition transition,
      DrawState drawState)
    {
      if (drawState != DrawState.Normal)
        return FsmEditorStyles.LinkColors[(int) drawState];
      bool flag = FsmEditor.SelectedState != null && FsmEditorSettings.FadeLinksNotConnectedToSelectedStates && !FsmEditor.SelectedStates.Contains(fromState) && !FsmEditor.SelectedStates.Contains(transition.ToFsmState);
      int index = transition.ColorIndex;
      if (index == 0)
        index = FsmEditorSettings.ColorLinks ? fromState.ColorIndex : transition.ColorIndex;
      return index == 0 ? (!flag ? FsmEditorStyles.LinkColors[(int) drawState] : FsmEditorStyles.FadedLinkColor) : (!flag ? PlayMakerPrefs.Colors[index] : FsmEditorStyles.FadedColors[index]);
    }

    private static Color GetTransitionBoxColor(FsmState state, FsmTransition transition) => transition.ColorIndex > 0 ? Color.white * 0.5f + PlayMakerPrefs.Colors[transition.ColorIndex] * 0.5f : Color.white * 0.5f + PlayMakerPrefs.Colors[state.ColorIndex] * 0.5f;

    [Localizable(false)]
    private void DrawStartArrow()
    {
      FsmState startState = FsmEditor.SelectedFsm.GetStartState();
      if (startState == null)
        return;
      Rect source = this.ScaleRect(startState.Position);
      List<FsmTransition> transitionsToState = FsmEditor.SelectedFsm.GetGlobalTransitionsToState(startState);
      float stateRowHeight = FsmEditorStyles.StateRowHeight;
      Rect rect = new Rect(source)
      {
        height = stateRowHeight
      };
      rect.y -= (float) ((double) transitionsToState.Count * (double) stateRowHeight + (double) stateRowHeight + 32.0 * (double) this.zoom);
      GUI.backgroundColor = Color.white;
      GUI.Box(rect.RoundToInt(), "START", FsmEditorStyles.GlobalTransitionBox);
      this.startBox = rect;
      if (transitionsToState.Count != 0)
        return;
      this.DrawGlobalArrow(startState, FsmEditorStyles.LinkColors[0]);
    }

    private void DoGlobalTransitionGUI(FsmState state)
    {
      List<FsmTransition> transitionsToState = FsmEditor.SelectedFsm.GetGlobalTransitionsToState(state);
      if (transitionsToState.Count == 0)
        return;
      Rect rect = new Rect(this.ScaleRect(state.Position))
      {
        height = (float) transitionsToState.Count * FsmEditorStyles.StateRowHeight
      };
      rect.y -= rect.height + 32f * this.zoom;
      GUILayout.BeginArea(rect.RoundToInt());
      Color backgroundColor = GUI.backgroundColor;
      GUI.backgroundColor = Color.white;
      foreach (FsmTransition transition in transitionsToState)
        GUILayout.Box(Labels.GetEventLabel(transition), FsmEditorStyles.GlobalTransitionBox);
      GUI.backgroundColor = backgroundColor;
      GUILayout.EndArea();
      this.DrawGlobalArrow(state, FsmEditorStyles.LinkColors[0]);
    }

    private void DoSendsEventGUI(FsmState state)
    {
      if (!FsmEditor.Search.DoesStateSendEvents(state))
        return;
      ref Rect local = ref FsmGraphView.tempRect;
      Rect position = state.Position;
      double x = (double) position.x;
      position = state.Position;
      double width = (double) position.width;
      double num = x + width - 10.0;
      position = state.Position;
      double y = (double) position.y;
      local.Set((float) num, (float) y, 10f, 16f);
      if (this.isRepaint)
        GUIStyle.none.Draw(FsmGraphView.tempRect.Scale(this.zoom), (Texture) FsmEditorStyles.SendsEventsIcon);
      if (this.eventType != UnityEngine.EventType.MouseDown || !FsmGraphView.tempRect.Contains(this.currentMousePos))
        return;
      Event.current.Use();
      this.draggingMode = FsmGraphView.DraggingMode.None;
      GenericMenu genericMenu = new GenericMenu();
      foreach (FsmInfo fsmInfo in FsmSearch.GetSearch(FsmEditor.SelectedFsm).FindEventsSentByState(state))
      {
        string text = (fsmInfo.eventTarget != null ? fsmInfo.eventTarget.target.ToString() : "Self") + " : " + fsmInfo.eventName;
        if (FsmEventManager.IsOutputEvent(fsmInfo.eventName))
          text += " > Output";
        genericMenu.AddItem(new GUIContent(text), false, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
      }
      genericMenu.ShowAsContext();
    }

    private void DrawStateDescription(FsmState state)
    {
      if (string.IsNullOrEmpty(state.Description))
        return;
      Rect rect1 = this.ScaleRect(state.Position);
      FsmEditorContent.StateDescription.text = state.Description;
      Rect rect2 = GUILayoutUtility.GetRect(FsmEditorContent.StateDescription, FsmEditorStyles.CommentBox, GUILayout.MaxWidth(rect1.width));
      rect2.x = rect1.x;
      rect2.y = rect1.yMax + 5f;
      GUI.Box(rect2, state.Description, FsmEditorStyles.CommentBox);
    }

    private void DrawFsmHelUrlButton(Fsm fsm)
    {
      if (string.IsNullOrEmpty(fsm.DocUrl))
        return;
      Rect position = new Rect((float) ((double) this.canvasView.ScrollPosition.x + (double) this.view.width - 32.0), this.canvasView.ScrollPosition.y + 10f, 18f, 18f);
      FsmEditorContent.HelpButton.tooltip = Strings.Tooltip_Fsm_Docs;
      GUIContent helpButton = FsmEditorContent.HelpButton;
      GUIStyle none = GUIStyle.none;
      if (!GUI.Button(position, helpButton, none))
        return;
      Application.OpenURL(fsm.DocUrl);
    }

    private void DoBackground()
    {
      if (this.isRepaint)
      {
        FsmEditorStyles.Background.Draw(this.view, false, false, false, false);
        if (this.canvasView.TakingScreenshot && !this.canvasView.ScreenshotFirstTile)
          return;
        if (FsmEditorSettings.EnableWatermarks)
          this.DrawWatermark();
        if (FsmEditor.SelectedFsm == null)
        {
          this.DoSelectionHints();
        }
        else
        {
          this.DoLargeWatermarkText(this.graphLabel);
          if (FsmEditorSettings.ShowFsmDescriptionInGraphView)
            this.DrawFsmDescription(FsmEditor.SelectedFsm);
        }
      }
      if (Application.isPlaying || !((UnityEngine.Object) FsmEditor.SelectedFsmComponent != (UnityEngine.Object) null) || !((UnityEngine.Object) FsmEditor.SelectedFsmComponent.FsmTemplate != (UnityEngine.Object) null))
        return;
      FsmTemplate fsmTemplate = FsmEditor.SelectedFsmComponent.FsmTemplate;
      if (!GUI.Button(this.view, (!string.IsNullOrEmpty(fsmTemplate.Category) ? fsmTemplate.Category + " : " : "") + fsmTemplate.name + "\n\n" + Strings.FsmGraphView_Click_to_Edit_Template, FsmEditorStyles.CenteredLabel))
        return;
      FsmEditor.SelectFsm(fsmTemplate.fsm);
    }

    private void DrawFsmDescription(Fsm fsm)
    {
      if (this.canvasView.TakingScreenshot && !this.canvasView.ScreenshotFirstTile)
        return;
      Rect rect = new Rect();
      if (string.IsNullOrEmpty(fsm.Description))
        return;
      int num = FsmEditorSettings.ShowSelectedGameObjectLabel || FsmEditorSettings.ShowSelectedFsmLabel ? 45 : 10;
      FsmEditorContent.FsmDescription.text = fsm.Description;
      rect.height = FsmEditorStyles.SmallWatermarkText.CalcHeight(FsmEditorContent.FsmDescription, 200f);
      FsmEditorStyles.SmallWatermarkText.Draw(new Rect(this.view.x + 5f, this.view.y + (float) num, 200f, rect.height), FsmEditorContent.TempContent(fsm.Description), false, false, false, false);
      rect.height += this.view.y + 40f;
    }

    private void DrawWatermark()
    {
      if (this.canvasView.TakingScreenshot && !this.canvasView.ScreenshotFirstTile)
        return;
      Texture defaultWatermark = FsmEditorStyles.DefaultWatermark;
      if (FsmEditor.SelectedFsm != null)
        defaultWatermark = Watermarks.Get(FsmEditor.SelectedFsm);
      if ((UnityEngine.Object) defaultWatermark == (UnityEngine.Object) null || this.canvasView.TakingScreenshot)
        return;
      FsmEditorStyles.Watermark.Draw(new Rect(this.view.x, this.view.y, this.view.width, this.view.height - EditorStyles.toolbar.fixedHeight), defaultWatermark, FsmEditorStyles.WatermarkTint);
    }

    private void DrawStateHistory(float currentTime, float timeWindow)
    {
      if (!EditorApplication.isPlaying)
        return;
      float num = currentTime - timeWindow;
      FsmGraphView.highlightedStates.Clear();
      for (int index = FsmEditor.SelectedFsm.MyLog.Entries.Count - 1; index >= 0; --index)
      {
        FsmLogEntry entry = FsmEditor.SelectedFsm.MyLog.Entries[index];
        if ((double) entry.Time <= (double) currentTime)
        {
          if ((double) entry.Time < (double) num)
            break;
          if (entry.LogType == FsmLogType.ExitState && entry.State != null && !FsmGraphView.highlightedStates.Contains(entry.State))
          {
            FsmEditor.Repaint();
            if (this.isRepaint)
            {
              Color activeHighlightColor = FsmEditorStyles.ActiveHighlightColor;
              activeHighlightColor.a = (entry.Time - num) / timeWindow;
              FsmEditorStyles.SelectionBox.Draw(this.ScaleRect(entry.State.Position), activeHighlightColor);
              FsmGraphView.highlightedStates.Add(entry.State);
            }
          }
        }
      }
    }

    private void DrawTransitionHistory(float currentTime, float timeWindow)
    {
      if (!this.isRepaint || !EditorApplication.isPlaying)
        return;
      float num = currentTime - timeWindow;
      Color color = !EditorApplication.isPaused ? FsmEditorStyles.ActiveHighlightColor : FsmEditorStyles.PausedHighlightColor;
      float linkWidth = FsmEditorStyles.LinkWidths[2];
      FsmGraphView.highlightedTransitions.Clear();
      for (int index = FsmEditor.SelectedFsm.MyLog.Entries.Count - 1; index >= 0; --index)
      {
        FsmLogEntry entry = FsmEditor.SelectedFsm.MyLog.Entries[index];
        if ((double) entry.Time < (double) num)
          break;
        if (entry.LogType == FsmLogType.Transition && (double) entry.Time <= (double) currentTime)
        {
          FsmTransition transition = entry.Transition;
          if (transition != null && !FsmGraphView.highlightedTransitions.Contains(transition))
          {
            FsmState state1 = entry.State;
            FsmState state2 = FsmEditor.SelectedFsm.GetState(transition.ToState);
            color.a = (entry.Time - num) / timeWindow;
            if (state1 != null)
            {
              int transitionIndex = state1.GetTransitionIndex(transition);
              this.DoDrawLink(state1, state2, transitionIndex, color, linkWidth);
            }
            else
              this.DoDrawGlobalArrow(state2, color);
            FsmGraphView.highlightedTransitions.Add(transition);
          }
        }
      }
    }

    private void DrawLastTransition()
    {
      FsmLogEntry lastTransition = DebugFlow.GetLastTransition();
      if (lastTransition == null || lastTransition.Fsm != FsmEditor.SelectedFsm)
        return;
      FsmTransition transition = lastTransition.Transition;
      if (transition == null)
        return;
      FsmState state1 = lastTransition.State;
      FsmState state2 = FsmEditor.SelectedFsm.GetState(transition.ToState);
      if (state2 == null)
        return;
      Color color = DebugFlow.Active ? FsmEditorStyles.PausedHighlightColor : FsmEditorStyles.ActiveHighlightColor;
      float linkWidth = FsmEditorStyles.LinkWidths[2];
      if (state1 != null)
      {
        int transitionIndex = state1.GetTransitionIndex(transition);
        this.DoDrawLink(state1, state2, transitionIndex, color, linkWidth);
      }
      else
        this.DoDrawGlobalArrow(state2, color);
    }

    private void DrawFrame()
    {
      if (FsmEditor.SelectedFsm == null || !EditorApplication.isPlaying)
        return;
      DrawState drawState = FsmDrawState.GetDrawState(FsmEditor.SelectedFsm);
      FsmEditorStyles.InnerGlowBox.Draw(this.view, FsmEditorStyles.HighlightColors[(int) drawState]);
    }

    private void DrawCanvas()
    {
      if (FsmEditor.SelectedFsm == null)
        return;
      if (this.isRepaint && FsmEditorSettings.DrawFrameAroundGraph)
        FsmEditorStyles.SinglePixelFrame.Draw(new Rect(0.0f, 0.0f, this.graphSize.x * this.zoom, this.graphSize.y * this.zoom), FsmEditorStyles.MinimapFrameColor);
      if (!this.canvasView.TakingScreenshot)
        return;
      Texture image = Watermarks.Get(FsmEditor.SelectedFsm);
      if (!((UnityEngine.Object) image != (UnityEngine.Object) null))
        return;
      GUIHelpers.BeginGuiColor(FsmEditorStyles.WatermarkTint);
      Rect contentArea = this.canvasView.ContentArea;
      double width = (double) contentArea.width;
      contentArea = this.canvasView.ContentArea;
      double height = (double) contentArea.height;
      GUI.Box(new Rect(0.0f, 0.0f, (float) width, (float) height), image, FsmEditorStyles.Watermark);
      GUIHelpers.EndGuiColor();
    }

    private void DrawHintBox()
    {
      if (!FsmEditorSettings.ShowHints)
        return;
      if (FsmEditor.SelectedFsm != null)
      {
        Rect rect = new Rect();
        rect.x = this.view.xMax - (FsmEditorContent.HintGraphShortcutsSize.x + 20f);
        rect.y = this.view.yMax - (FsmEditorContent.HintGraphShortcutsSize.y + 20f);
        rect.width = FsmEditorContent.HintGraphShortcutsSize.x;
        rect.height = FsmEditorContent.HintGraphShortcutsSize.y;
        Rect position1 = rect;
        rect = new Rect();
        rect.x = this.view.xMax - (FsmEditorContent.HintGraphCommandsSize.x + 20f);
        rect.y = this.view.yMax - (FsmEditorContent.HintGraphCommandsSize.y + 20f);
        rect.width = FsmEditorContent.HintGraphCommandsSize.x;
        rect.height = FsmEditorContent.HintGraphCommandsSize.y;
        Rect position2 = rect;
        position2.x -= position1.width;
        position2.width += position1.width;
        GUI.Box(position2, FsmEditorContent.HintGraphCommands, FsmEditorStyles.HintBox);
        GUI.Box(position1, FsmEditorContent.HintGraphShortcuts, FsmEditorStyles.HintBoxTextOnly);
      }
      else
        GUI.Box(new Rect()
        {
          x = this.view.xMax - (FsmEditorContent.HintGettingStartedSize.x + 20f),
          y = this.view.yMax - (FsmEditorContent.HintGettingStartedSize.y + 20f),
          width = FsmEditorContent.HintGettingStartedSize.x,
          height = FsmEditorContent.HintGettingStartedSize.y
        }, FsmEditorContent.HintGettingStarted, FsmEditorStyles.HintBox);
    }

    private void DrawSelectionRect() => FsmEditorStyles.SelectionRect.Draw(new Rect(Mathf.Min(this.dragStartPos.x, this.currentMousePos.x), Mathf.Min(this.dragStartPos.y, this.currentMousePos.y), Mathf.Abs(this.currentMousePos.x - this.dragStartPos.x), Mathf.Abs(this.currentMousePos.y - this.dragStartPos.y)));

    private void DoDrawLink(
      FsmState fromState,
      FsmState toState,
      int transitionIndex,
      Color color,
      float width)
    {
      if (toState == null)
        return;
      FsmTransition transition = fromState.Transitions[transitionIndex];
      GraphViewLinkStyle graphViewLinkStyle = FsmEditorSettings.GraphViewLinkStyle;
      switch (transition.LinkStyle)
      {
        case FsmTransition.CustomLinkStyle.Bezier:
          graphViewLinkStyle = GraphViewLinkStyle.BezierLinks;
          break;
        case FsmTransition.CustomLinkStyle.Circuit:
          graphViewLinkStyle = GraphViewLinkStyle.CircuitLinks;
          break;
        case FsmTransition.CustomLinkStyle.Direct:
          graphViewLinkStyle = GraphViewLinkStyle.DirectLinks;
          break;
      }
      switch (graphViewLinkStyle)
      {
        case GraphViewLinkStyle.BezierLinks:
          BezierLink.Instance.Draw(fromState, toState, transitionIndex, color, width, this.zoom);
          break;
        case GraphViewLinkStyle.CircuitLinks:
          CircuitLink.Instance.Draw(fromState, toState, transitionIndex, color, width, this.zoom);
          break;
        case GraphViewLinkStyle.DirectLinks:
          DirectLink.Instance.Draw(fromState, toState, transitionIndex, color, width, this.zoom);
          break;
      }
    }

    private void DrawBreakpoint(FsmState state)
    {
      Rect position = this.ScaleRect(state.Position);
      position.width = (float) FsmEditorStyles.BreakpointOff.normal.background.width;
      position.height = (float) FsmEditorStyles.BreakpointOff.normal.background.height * this.zoom;
      if (FsmEditorSettings.BreakpointsEnabled && FsmEditor.SelectedFsm.EnableBreakpoints)
        FsmEditorStyles.BreakpointOn.Draw(position, false, false, false, false);
      else
        FsmEditorStyles.BreakpointOff.Draw(position, false, false, false, false);
    }

    private void DrawStateError(FsmState state)
    {
      Rect position = this.ScaleRect(state.Position);
      position.y -= 6f;
      position.x -= 5f;
      position.width = position.height = 14f;
      FsmEditorContent.ErrorIcon.tooltip = FsmErrorChecker.GetStateTransitionErrors(state);
      GUI.Box(position, FsmEditorContent.ErrorIcon, GUIStyle.none);
    }

    [Localizable(false)]
    private void DoGameStateIcon()
    {
      if (FsmEditor.SelectedFsm == null || !EditorApplication.isPlaying)
        return;
      float gameStateIconSize = (float) FsmEditorSettings.GameStateIconSize;
      Rect position = new Rect(10f, this.view.height - gameStateIconSize, gameStateIconSize, gameStateIconSize + 20f);
      if (FsmEditor.SelectedFsm.Active && !FsmEditor.SelectedFsm.Finished)
      {
        Texture2D gameStateIcon = FsmEditorStyles.GetGameStateIcons()[(int) GameStateTracker.CurrentState];
        if ((UnityEngine.Object) gameStateIcon != (UnityEngine.Object) null && GUI.Button(position, (Texture) gameStateIcon, GUIStyle.none))
        {
          switch (GameStateTracker.CurrentState)
          {
            case GameState.Running:
              EditorApplication.isPaused = true;
              break;
            case GameState.Break:
            case GameState.Error:
              FsmEditor.GotoBreakpoint();
              break;
            case GameState.Paused:
              EditorApplication.isPaused = false;
              break;
          }
        }
      }
      DrawState drawState = FsmDrawState.GetDrawState(FsmEditor.SelectedFsm);
      Color color = FsmEditorStyles.HighlightColors[(int) drawState];
      position.y -= 3f;
      position.width = this.view.width - position.width;
      string text;
      if (!FsmEditor.SelectedFsm.Active)
      {
        position.x = 5f;
        text = Strings.Label_DISABLED;
        color = FsmEditorStyles.LargeWatermarkText.normal.textColor;
      }
      else if (FsmEditor.SelectedFsm.Finished)
      {
        position.x = 5f;
        text = Strings.Label_FINISHED;
        color = FsmEditorStyles.LargeWatermarkText.normal.textColor;
      }
      else
      {
        position.x = 45f;
        text = !DebugFlow.ActiveAndScrubbing ? FsmEditor.SelectedFsm.ActiveStateName : (DebugFlow.DebugState != null ? DebugFlow.DebugState.Name : " " + Strings.Label_None_In_Table);
      }
      GUIHelpers.BeginGuiColor(color);
      GUI.Box(position, text, FsmEditorStyles.LargeText);
      GUIHelpers.EndGuiColor();
    }

    private void DrawGlobalArrow(FsmState state, Color color)
    {
      color.a = !FsmEditorSettings.FadeLinksNotConnectedToSelectedStates || FsmEditor.SelectedStates.Count <= 0 || FsmEditor.SelectedStates.Contains(state) ? 0.9f : 0.3f;
      this.DoDrawGlobalArrow(state, color);
    }

    private void DoDrawGlobalArrow(FsmState state, Color color)
    {
      if (state == null || !this.isRepaint)
        return;
      Rect source = this.ScaleRect(state.Position);
      Rect rect = new Rect(source)
      {
        height = (float) FsmEditorStyles.GlobalArrow.height * this.zoom,
        width = (float) FsmEditorStyles.GlobalArrow.width * this.zoom
      };
      rect.y -= rect.height;
      rect.x = (float) ((double) rect.x + (double) source.width * 0.5 - (double) rect.width * 0.5);
      FsmEditorStyles.GlobalTransitionArrow.Draw(rect, color);
    }

    private void HandleMinimapInput()
    {
      if (!FsmEditorSettings.GraphViewShowMinimap || FsmEditor.SelectedFsm == null || this.canvasView.TakingScreenshot)
        return;
      float graphViewMinimapSize = FsmEditorSettings.GraphViewMinimapSize;
      this.minimapOffset = this.graphExpanded ? 100000f : 0.0f;
      float num1 = this.graphSize.x - this.minimapOffset * 2f;
      float num2 = this.graphSize.y - this.minimapOffset * 2f;
      this.minimapScale = Mathf.Min(graphViewMinimapSize / num1, graphViewMinimapSize / num2, 0.15f);
      this.scaleToMinimap = this.minimapScale / this.zoom;
      this.minimapRect.Set((float) ((double) this.view.xMax - (double) num1 * (double) this.minimapScale - 20.0), this.view.y + 20f, num1 * this.minimapScale, num2 * this.minimapScale);
      this.minimapLocalRect.Set(0.0f, 0.0f, this.minimapRect.width, this.minimapRect.height);
      this.mouseOverMinimap = this.minimapRect.Contains(Event.current.mousePosition);
      if (this.draggingMode != FsmGraphView.DraggingMode.Minimap && (!this.mouseOverMinimap || this.eventType != UnityEngine.EventType.MouseDown || this.currentEvent.button != 0))
        return;
      this.canvasView.CancelAutoPan();
      this.canvasView.SetScrollPosition(this.canvasView.ContentOrigin + (this.currentEvent.mousePosition - this.minimapRect.min) / this.scaleToMinimap - this.canvasView.ViewCenter);
      this.draggingMode = FsmGraphView.DraggingMode.Minimap;
      this.UpdateVisibility();
      if (this.eventType != UnityEngine.EventType.MouseDown)
        return;
      Event.current.Use();
    }

    private void DrawMinimap()
    {
      if (!FsmEditorSettings.GraphViewShowMinimap || FsmEditor.SelectedFsm == null || this.canvasView.TakingScreenshot)
        return;
      Vector2 vector2 = (this.canvasView.ScrollPosition - this.canvasView.ContentOrigin - new Vector2(this.minimapOffset, this.minimapOffset)) * this.scaleToMinimap;
      EditorGUIUtility.AddCursorRect(this.minimapRect, MouseCursor.MoveArrow);
      GUIHelpers.BeginGuiColor(FsmEditorStyles.MinimapFrameColor);
      GUI.BeginGroup(this.minimapRect, FsmEditorStyles.SinglePixelFrame);
      GUIHelpers.EndGuiColor();
      GUIHelpers.BeginGuiColor(FsmEditorStyles.MinimapViewRectColor);
      GUI.Box(new Rect(vector2.x, vector2.y, this.view.width * this.scaleToMinimap, this.view.height * this.scaleToMinimap), GUIContent.none, FsmEditorStyles.SelectionRect);
      GUIHelpers.EndGuiColor();
      foreach (FsmState state in FsmEditor.SelectedFsm.States)
      {
        if (Application.isPlaying && state == FsmEditor.SelectedFsm.ActiveState)
        {
          DrawState fsmStateDrawState = FsmDrawState.GetFsmStateDrawState(FsmEditor.SelectedFsm, state, false);
          GUIHelpers.BeginGuiColor(FsmEditorStyles.HighlightColors[(int) fsmStateDrawState]);
        }
        else
          GUIHelpers.BeginGuiColor(PlayMakerPrefs.MinimapColors[state.ColorIndex]);
        ref Rect local = ref FsmGraphView.tempRect;
        Rect position = state.Position;
        double num1 = (double) position.x - (double) this.minimapOffset;
        position = state.Position;
        double num2 = (double) position.y - (double) this.minimapOffset;
        position = state.Position;
        double width = (double) position.width;
        position = state.Position;
        double height = (double) position.height;
        local.Set((float) num1, (float) num2, (float) width, (float) height);
        GUI.DrawTexture(FsmGraphView.tempRect.Scale(this.minimapScale).MinSize(new Vector2(10f, 4f)), (Texture) EditorGUIUtility.whiteTexture);
        GUIHelpers.EndGuiColor();
        if (state.HasErrors)
        {
          FsmGraphView.tempRect.Set(FsmGraphView.tempRect.x * this.minimapScale, FsmGraphView.tempRect.y * this.minimapScale, 2f, 2f);
          GUIHelpers.BeginGuiColor(Color.red);
          GUI.DrawTexture(FsmGraphView.tempRect, (Texture) EditorGUIUtility.whiteTexture);
          GUIHelpers.EndGuiColor();
        }
      }
      GUI.EndGroup();
    }

    public void AddState() => this.AddState(this.contextMenuPos / this.zoom);

    public FsmState AddState(Vector2 position) => this.AddState(position, "");

    public FsmState AddState(Vector2 position, string stateName)
    {
      if (!FsmEditor.DisconnectCheck())
        return (FsmState) null;
      FsmEditor.RecordUndo(Strings.Command_Add_State);
      FsmState state = FsmEditor.SelectedFsm.AddState(position / this.zoom, stateName);
      this.UpdateStateSize(state);
      FsmGraphView.UpdateStatePosition(state, position);
      FsmEditor.SelectState(state, false);
      if (FsmEditorSettings.SnapToGrid)
        this.SnapStateToGrid(state);
      this.UpdateGraphBounds(this.contextMenuPos);
      FsmEditor.SetFsmDirty(true);
      return state;
    }

    public void PasteStates()
    {
      EditorCommands.PasteStates(this.contextMenuPos / this.zoom);
      if (FsmEditorSettings.SnapToGrid)
        this.SnapDraggedStatesToGrid();
      this.UpdateGraphBounds(this.contextMenuPos);
    }

    public void PasteTemplate(object userdata)
    {
      EditorCommands.PasteTemplate(userdata as FsmTemplate, this.contextMenuPos / this.zoom);
      if (FsmEditorSettings.SnapToGrid)
        this.SnapDraggedStatesToGrid();
      this.UpdateGraphBounds(this.contextMenuPos);
    }

    private static void SetSelectedTransitionTarget(FsmState toState) => EditorCommands.SetTransitionTarget(FsmEditor.SelectedTransition, toState.Name);

    public void DeleteGlobalTransition(object userdata) => EditorCommands.DeleteGlobalTransition(userdata as FsmTransition);

    public static Vector2 GetViewCenter()
    {
      Rect view = FsmEditor.GraphView.view;
      return new Vector2(FsmEditor.Selection.ScrollPosition.x + view.width * 0.5f, FsmEditor.Selection.ScrollPosition.y + view.height * 0.5f) / FsmEditor.GraphView.zoom;
    }

    public static void TranslateState(FsmState state, Vector2 offset)
    {
      Rect position1 = state.Position;
      double x = (double) position1.x;
      position1 = state.Position;
      double y = (double) position1.y;
      Vector2 position2 = new Vector2((float) x, (float) y) + offset;
      FsmGraphView.UpdateStatePosition(state, position2);
    }

    public static void UpdateStatePosition(FsmState state, Vector2 position) => state.Position = new Rect(state.Position)
    {
      x = position.x,
      y = position.y
    };

    public static Vector2 MoveAllStatesToOrigin(Fsm fsm, float padding = 50f)
    {
      if (fsm == null)
        return Vector2.zero;
      Vector2 vector2 = new Vector2(float.PositiveInfinity, float.PositiveInfinity);
      foreach (FsmState state in fsm.States)
      {
        Rect stateBounds = FsmGraphView.GetStateBounds(state);
        if ((double) stateBounds.x < (double) vector2.x)
          vector2.x = stateBounds.x;
        if ((double) stateBounds.y < (double) vector2.y)
          vector2.y = stateBounds.y;
      }
      vector2.x -= padding;
      vector2.y -= padding;
      vector2.x = Mathf.Floor(vector2.x / (float) FsmEditorSettings.SnapGridSize) * (float) FsmEditorSettings.SnapGridSize;
      vector2.y = Mathf.Floor(vector2.y / (float) FsmEditorSettings.SnapGridSize) * (float) FsmEditorSettings.SnapGridSize;
      FsmGraphView.MoveAllStates(fsm, -vector2);
      return vector2;
    }

    public static void MoveAllStates(Fsm fsm, Vector2 delta)
    {
      foreach (FsmState state in fsm.States)
        state.Position = new Rect(state.Position)
        {
          x = state.Position.x + delta.x,
          y = state.Position.y + delta.y
        };
    }

    public void UpdateStateSize(string stateName)
    {
      if (string.IsNullOrEmpty(stateName) || FsmEditor.SelectedFsm == null)
        return;
      this.UpdateStateSize(FsmEditor.SelectedFsm.GetState(stateName));
    }

    public void UpdateStateSize(FsmState state)
    {
      if (state == null)
        return;
      Rect rect = new Rect(state.Position)
      {
        width = FsmGraphView.CalculateStateWidth(state)
      };
      int num = state.Transitions.Length + 1;
      rect.height = (float) num * 16f;
      state.Position = rect;
      FsmGraphView.UpdateStatePosition(state, new Vector2(state.Position.x, state.Position.y));
      this.UpdateGraphSize();
    }

    private static float CalculateStateWidth(FsmState state)
    {
      float num = FsmEditorStyles.DefaultStateBoxStyle.CalcSize(new GUIContent(state.Name)).x;
      foreach (FsmTransition transition in state.Transitions)
      {
        float x = FsmEditorStyles.DefaultStateBoxStyle.CalcSize(new GUIContent(transition.EventName)).x;
        if ((double) x > (double) num)
          num = x;
      }
      foreach (FsmTransition globalTransition in state.Fsm.GlobalTransitions)
      {
        if (globalTransition.ToState == state.Name)
        {
          float x = FsmEditorStyles.DefaultStateBoxStyle.CalcSize(new GUIContent(globalTransition.EventName)).x;
          if ((double) x > (double) num)
            num = x;
        }
      }
      if (FsmEditorSettings.ShowCommentsInGraphView && !string.IsNullOrEmpty(state.Description))
      {
        float x = FsmEditorStyles.DefaultStateBoxStyle.CalcSize(new GUIContent(state.Description)).x;
        if ((double) x > (double) num)
          num = (float) (((double) num + (double) x) * 0.5);
      }
      return Mathf.Clamp(num + 20f, (float) FsmEditorSettings.StateMinWidth, (float) FsmEditorSettings.StateMaxWidth);
    }

    public void UpdateAllStateSizes()
    {
      foreach (Fsm fsm in FsmEditor.FsmList)
        this.UpdateStateSizes(fsm);
      FsmEditor.Repaint();
    }

    public void UpdateStateSizes(Fsm fsm)
    {
      if (fsm == null)
        return;
      foreach (FsmState state in fsm.States)
      {
        state.Fsm = fsm;
        this.UpdateStateSize(state);
      }
    }

    public static Rect GetStateBounds(FsmState state)
    {
      if (state == null)
        return new Rect();
      Rect rect = new Rect(state.Position);
      if (state.Fsm == null)
        return rect;
      int count = state.GetGlobalTransitions().Count;
      if (state.IsStartState())
        ++count;
      if (count > 0)
      {
        rect.yMin -= (float) FsmEditorStyles.GlobalArrow.height;
        rect.yMin -= (float) count * 16f;
      }
      return rect;
    }

    public static void ToggleLinkStyle(FsmTransition transition)
    {
      if (transition == null)
        return;
      FsmEditor.RecordUndo(Strings.Command_Set_Link_Style);
      switch (transition.LinkStyle)
      {
        case FsmTransition.CustomLinkStyle.Default:
          transition.LinkStyle = FsmEditorSettings.GraphViewLinkStyle == GraphViewLinkStyle.BezierLinks ? FsmTransition.CustomLinkStyle.Circuit : FsmTransition.CustomLinkStyle.Bezier;
          break;
        case FsmTransition.CustomLinkStyle.Bezier:
          transition.LinkStyle = FsmTransition.CustomLinkStyle.Circuit;
          break;
        case FsmTransition.CustomLinkStyle.Circuit:
          transition.LinkStyle = FsmTransition.CustomLinkStyle.Bezier;
          break;
      }
      FsmEditor.SetFsmDirty(false);
    }

    public static void SetLinkStyle(
      FsmTransition transition,
      FsmTransition.CustomLinkStyle linkStyle)
    {
      if (transition == null)
        return;
      FsmEditor.RecordUndo(Strings.Command_Set_Link_Style);
      transition.LinkStyle = linkStyle;
      FsmEditor.SetFsmDirty(false);
    }

    public static void SetLinkConstraint(
      FsmTransition transition,
      FsmTransition.CustomLinkConstraint linkConstraint)
    {
      if (transition == null)
        return;
      FsmEditor.RecordUndo(Strings.Command_Set_Link_Constraint);
      transition.LinkConstraint = linkConstraint;
      FsmEditor.SetFsmDirty(false);
    }

    public static void SetLinkTarget(
      FsmTransition transition,
      FsmTransition.CustomLinkTarget linkTarget)
    {
      if (transition == null)
        return;
      FsmEditor.RecordUndo("Edit Link");
      transition.LinkTarget = linkTarget;
      FsmEditor.SetFsmDirty(false);
    }

    private void InitScale(float scale)
    {
      FsmEditorStyles.InitScale(scale);
      this.zoom = scale;
    }

    private void SetScale(float scale)
    {
      FsmEditor.Selection.Zoom = scale;
      if ((double) Math.Abs(scale - this.zoom) <= (double) Mathf.Epsilon)
        return;
      this.zoom = Mathf.Clamp(scale, 0.3f, 1f);
      FsmEditorStyles.SetScale(this.zoom);
      FsmEditor.Repaint(true);
      this.UpdateVisibility();
    }

    public void ApplySettings()
    {
      this.canvasView.MouseWheelZoomsView = !FsmEditorSettings.MouseWheelScrollsGraphView;
      this.canvasView.MinScale = 0.3f;
      this.canvasView.MaxScale = 1f;
      this.canvasView.ZoomSpeed = FsmEditorSettings.GraphViewZoomSpeed;
      this.canvasView.EdgeScrollSpeed = FsmEditorSettings.EdgeScrollSpeed;
      this.canvasView.EdgeScrollZone = FsmEditorSettings.EdgeScrollZone;
      FsmEditor.Repaint(true);
    }

    [Localizable(false)]
    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("FsmGraphView: " + message));

    public void SetScrollPosition(Vector2 pos) => this.canvasView.SetContentScrollPosition(pos);

    private enum DraggingMode
    {
      None,
      State,
      Transition,
      ResizeNode,
      Selection,
      Minimap,
    }

    private enum DragConstraint
    {
      None,
      X,
      Y,
    }
  }
}
