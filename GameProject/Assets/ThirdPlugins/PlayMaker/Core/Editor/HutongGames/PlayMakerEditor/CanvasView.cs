// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.CanvasView
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Extensions;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class CanvasView
  {
    private static float verticalScrollbarWidth;
    private static float horizontalScrollbarHeight;
    private EditorWindow parentWindow;
    private Rect parentWindowPosition;
    private float scale = 1f;
    private float minScale = 0.25f;
    private float maxScale = 1f;
    private float zoomSpeed = 0.01f;
    private float contentMargin = 0.5f;
    private Rect viewRect;
    private Vector2 scrollPosition;
    private Vector2 mousePosition;
    private bool mouseOverView;
    private Vector2 oldScrollPosition = Vector2.zero;
    private Rect activeView;
    private bool viewRectInitialized;
    private Vector2 setScrollPosition;
    private bool startPanToPosition;
    private bool isPanningToPosition;
    private Vector2 panToPositionEnd;
    private Vector2 panToPositionStart;
    private float panToPositionLerp;
    private string screenshotFilename;
    private Texture2D screenshotTexture;
    private Rect screenshotCaptureRect;
    private Vector2 screenshotTargetPosition;
    private Vector2 screenshotStartScrollPosition;
    private float screenshotInitialMargin;
    private bool showSavedScreenshotDialog;
    private string savedScreenshot;
    private bool graphRepainted;
    private bool isDocked;
    private bool stylesInitialized;
    private GUIStyle hiddenHorizontalScrollbar;
    private GUIStyle hiddenVerticalScrollbar;
    private float edgeScrollRampUp;
    private readonly List<CanvasView.Marker> markers = new List<CanvasView.Marker>();
    private Vector2 tabOffset;
    private Matrix4x4 savedGuiMatrix;

    public Vector2 ContentSize { get; set; }

    public Rect ContentArea => new Rect((float) (int) ((double) this.viewRect.width - (double) this.margin * 0.5), (float) (int) ((double) this.viewRect.height - (double) this.margin * 0.5), (float) (int) ((double) this.ContentSize.x * (double) this.scale), (float) (int) ((double) this.ContentSize.y * (double) this.scale));

    public Rect Canvas => new Rect(0.0f, 0.0f, this.CanvasSize.x, this.CanvasSize.y);

    public Rect ViewRectInCanvas
    {
      get
      {
        double num1 = (double) this.scrollPosition.x - (double) this.ContentOrigin.x;
        double num2 = (double) this.scrollPosition.y - (double) this.ContentOrigin.y;
        Rect view = this.View;
        double width = (double) view.width;
        view = this.View;
        double height = (double) view.height;
        return new Rect((float) num1, (float) num2, (float) width, (float) height);
      }
    }

    public Vector2 ScrollPosition => this.scrollPosition;

    public bool ViewChanged { get; set; }

    public Vector2 MousePosition => this.mousePosition;

    public float ContentMargin
    {
      get => this.contentMargin;
      set => this.contentMargin = value;
    }

    public bool MouseWheelZoomsView { get; set; }

    public float Scale => this.scale;

    public float MinScale
    {
      get => this.minScale;
      set => this.minScale = Mathf.Max(0.01f, value);
    }

    public float MaxScale
    {
      get => this.maxScale;
      set => this.maxScale = value;
    }

    public float ZoomSpeed
    {
      get => this.zoomSpeed;
      set => this.zoomSpeed = value;
    }

    public Vector2 CanvasSize => new Vector2((float) ((double) this.ContentSize.x * (double) this.scale + (double) this.viewRect.width * 2.0) - this.margin, (float) ((double) this.ContentSize.y * (double) this.scale + (double) this.viewRect.height * 2.0) - this.margin);

    public Vector2 CanvasCenter => this.CanvasSize * 0.5f;

    public Vector2 ContentOrigin
    {
      get
      {
        Rect contentArea = this.ContentArea;
        double x = (double) contentArea.x;
        contentArea = this.ContentArea;
        double y = (double) contentArea.y;
        return new Vector2((float) x, (float) y);
      }
    }

    public Rect View => this.viewRect;

    public Rect ActiveView => this.activeView;

    public Vector2 ViewOrigin => new Vector2(this.viewRect.x, this.viewRect.y);

    public Vector2 ViewSize => new Vector2(this.viewRect.width, this.viewRect.height);

    public Vector2 ViewCenter => new Vector2(this.viewRect.width * 0.5f, this.viewRect.height * 0.5f);

    public bool DraggingCanvas { get; private set; }

    public bool TakingScreenshot { get; private set; }

    public bool ScreenshotFirstTile { get; private set; }

    public bool IsEdgeScrolling { get; private set; }

    public float EdgeScrollZone { get; set; }

    public float EdgeScrollSpeed { get; set; }

    public bool MouseDown { get; private set; }

    private Vector2 screenshotSourcePosition => new Vector2(this.scrollPosition.x - this.ContentArea.x, this.scrollPosition.y - this.ContentArea.y);

    private Vector2 viewCenter => new Vector2(this.viewRect.width * 0.5f, this.viewRect.height * 0.5f);

    private float margin => this.contentMargin * this.scale;

    public void Init(EditorWindow window)
    {
      this.parentWindow = window;
      this.viewRectInitialized = false;
    }

    public Vector2 BeginGUILayout(Rect view, bool showScrollBars)
    {
      GUIUtility.GetControlID(FocusType.Passive, view);
      this.oldScrollPosition.Set(this.scrollPosition.x - this.CanvasCenter.x, this.scrollPosition.y - this.CanvasCenter.y);
      this.HandleWindowResize();
      this.viewRect = view;
      this.activeView = view;
      if (showScrollBars)
      {
        this.activeView.width -= CanvasView.verticalScrollbarWidth;
        this.activeView.height -= CanvasView.horizontalScrollbarHeight;
      }
      if (!this.viewRectInitialized)
      {
        this.viewRectInitialized = true;
        this.SetContentScrollPosition(this.setScrollPosition);
        this.isPanningToPosition = false;
      }
      GUILayout.BeginArea(this.viewRect);
      this.InitStyles();
      this.HandleInputEvents();
      if (this.TakingScreenshot)
        showScrollBars = false;
      if (this.DraggingCanvas)
      {
        showScrollBars = true;
        EditorGUIUtility.AddCursorRect(this.Canvas, MouseCursor.Pan);
      }
      this.scrollPosition = showScrollBars ? GUILayout.BeginScrollView(this.scrollPosition) : GUILayout.BeginScrollView(this.scrollPosition, this.hiddenHorizontalScrollbar, this.hiddenVerticalScrollbar);
      GUILayout.Box(GUIContent.none, GUIStyle.none, GUILayout.Width(this.CanvasSize.x), GUILayout.Height(this.CanvasSize.y));
      if (Event.current.type == UnityEngine.EventType.Repaint)
        this.graphRepainted = true;
      return this.scrollPosition - this.ContentOrigin;
    }

    public Rect BeginZoomArea(Rect zoomArea, float zoom)
    {
      Vector2 screenPoint1 = GUIUtility.GUIToScreenPoint(Vector2.zero);
      GUI.EndClip();
      Vector2 screenPoint2 = GUIUtility.GUIToScreenPoint(Vector2.zero);
      this.tabOffset = screenPoint1 - screenPoint2;
      Rect rect = zoomArea;
      rect.size /= zoom;
      rect.position /= zoom;
      rect.position += this.tabOffset / zoom;
      this.savedGuiMatrix = GUI.matrix;
      GUI.matrix = Matrix4x4.Scale(zoom * Vector3.one);
      GUI.BeginClip(new Rect(0.0f, 0.0f, this.CanvasSize.x, this.CanvasSize.y), -this.scrollPosition, Vector2.zero, false);
      return rect;
    }

    public void EndZoomArea()
    {
      GUI.EndClip();
      GUI.matrix = this.savedGuiMatrix;
      GUI.BeginClip(new Rect(this.tabOffset, new Vector2((float) Screen.width, (float) Screen.height)));
    }

    public void MarkWorldPosition(Vector2 pos, Color color) => this.markers.Add(new CanvasView.Marker()
    {
      pos = pos,
      color = color
    });

    public void ClearMarkers() => this.markers.Clear();

    public void DrawMarker(Vector2 pos, Color color)
    {
      Color color1 = GUI.color;
      GUI.color = color;
      GUI.Box(new Rect(pos.x - 5f, pos.y - 5f, 10f, 10f), GUIContent.none);
      GUI.color = color1;
    }

    public void EndGUILayout()
    {
      GUILayout.EndScrollView();
      GUILayout.EndArea();
      if (DragAndDropManager.IsDraggingObject)
        this.scrollPosition = this.oldScrollPosition + this.CanvasCenter;
      if (Event.current.type == UnityEngine.EventType.Repaint && this.TakingScreenshot)
        this.CaptureScreenshotTile();
      if ((double) Math.Abs(this.scrollPosition.x - this.CanvasCenter.x - this.oldScrollPosition.x) <= 1.0 && (double) Math.Abs(this.scrollPosition.y - this.CanvasCenter.y - this.oldScrollPosition.y) <= 1.0)
        return;
      this.ViewChanged = true;
    }

    private void HandleWindowResize()
    {
      if ((UnityEngine.Object) this.parentWindow == (UnityEngine.Object) null)
        return;
      if ((double) this.parentWindowPosition.width == 0.0)
      {
        this.parentWindowPosition = this.parentWindow.position;
      }
      else
      {
        Rect position = this.parentWindow.position;
        float num1 = position.width - this.parentWindowPosition.width;
        position = this.parentWindow.position;
        float num2 = position.height - this.parentWindowPosition.height;
        this.scrollPosition.Set(this.scrollPosition.x + num1, this.scrollPosition.y + num2);
        this.parentWindowPosition = this.parentWindow.position;
        if ((double) num1 == 0.0 && (double) num2 == 0.0)
          return;
        this.ViewChanged = true;
      }
    }

    public void Update()
    {
      if (this.startPanToPosition)
      {
        this.PanToPosition(this.panToPositionEnd);
        this.startPanToPosition = false;
      }
      if (this.TakingScreenshot)
      {
        this.Repaint();
        this.ViewChanged = true;
      }
      if (this.isPanningToPosition)
      {
        this.DoPanToPosition();
        this.Repaint();
        this.ViewChanged = true;
      }
      if (this.IsEdgeScrolling)
      {
        this.edgeScrollRampUp += 0.05f;
        if ((double) this.edgeScrollRampUp > 1.0)
          this.edgeScrollRampUp = 1f;
        this.Repaint();
        this.ViewChanged = true;
      }
      if (!this.showSavedScreenshotDialog || !this.graphRepainted)
        return;
      this.showSavedScreenshotDialog = false;
      switch (EditorUtility.DisplayDialogComplex(Strings.Dialog_Saved_Screenshot, this.savedScreenshot, Strings.OK, Strings.Command_Open, Strings.Command_Browse_Screenshots))
      {
        case 1:
          EditorUtility.OpenWithDefaultApp(this.savedScreenshot);
          break;
        case 2:
          string fileName = EditorUtility.OpenFilePanel(Strings.Title_Screenshots, Path.GetDirectoryName(this.savedScreenshot), "png");
          if (string.IsNullOrEmpty(fileName))
            break;
          EditorUtility.OpenWithDefaultApp(fileName);
          break;
      }
    }

    private void Repaint()
    {
      if (!((UnityEngine.Object) this.parentWindow != (UnityEngine.Object) null))
        return;
      this.parentWindow.Repaint();
    }

    public void SetScrollPosition(Vector2 pos) => this.scrollPosition = pos;

    public void Pan(Vector2 offset) => this.scrollPosition += offset * this.scale;

    public void CancelAutoPan()
    {
      this.startPanToPosition = false;
      this.isPanningToPosition = false;
    }

    public void SetContentScrollPosition(Vector2 pos)
    {
      if (!this.viewRectInitialized)
        this.setScrollPosition = pos;
      else
        this.SetScrollPosition(pos + this.ContentOrigin);
    }

    public float ZoomView(Vector2 center, float delta)
    {
      this.CancelAutoPan();
      Vector2 worldCoordinates = this.ViewToWorldCoordinates(center);
      float b = Mathf.Clamp(this.scale + delta, this.MinScale, this.MaxScale);
      if (Mathf.Approximately(this.scale, b))
        return this.scale;
      this.scale = b;
      this.SetScrollPosition(this.WorldToLocalCoordinates(worldCoordinates - center / this.scale));
      this.Repaint();
      return this.scale;
    }

    public float SetZoom(float zoom)
    {
      this.scale = Mathf.Clamp(zoom, this.MinScale, this.MaxScale);
      return this.scale;
    }

    public bool IsVisible(Rect rect)
    {
      if (!this.viewRectInitialized || (double) this.scale < 1.0 || (double) this.scrollPosition.x < 0.0)
        return true;
      float num1 = this.scrollPosition.x - this.ContentOrigin.x;
      float num2 = num1 + this.View.width / this.scale;
      float num3 = this.scrollPosition.y - this.ContentOrigin.y;
      float num4 = num3 + this.View.height / this.scale;
      return (double) rect.x <= (double) num2 && (double) rect.x + (double) rect.width >= (double) num1 && ((double) rect.y <= (double) num4 && (double) rect.y + (double) rect.height >= (double) num3);
    }

    public void StartPanToPosition(Vector2 pos)
    {
      if (this.DraggingCanvas)
        return;
      this.startPanToPosition = true;
      this.panToPositionEnd = pos;
    }

    private void PanToPosition(Vector2 pos)
    {
      this.panToPositionStart = this.scrollPosition;
      this.panToPositionEnd = this.ContentOrigin + pos * this.scale - this.viewCenter;
      if ((double) (this.panToPositionEnd - this.scrollPosition).magnitude <= 50.0)
        return;
      this.isPanningToPosition = true;
    }

    private void DoPanToPosition()
    {
      this.panToPositionLerp = Mathf.Min(this.panToPositionLerp + 0.06f, 1f);
      this.scrollPosition = Vector2.Lerp(this.panToPositionStart, this.panToPositionEnd, Mathf.SmoothStep(0.0f, 1f, this.panToPositionLerp));
      if (!this.panToPositionLerp.Equals(1f))
        return;
      this.panToPositionLerp = 0.0f;
      this.isPanningToPosition = false;
    }

    public Vector2 DoEdgeScroll()
    {
      Vector2 vector2 = new Vector2();
      if ((double) this.mousePosition.x < (double) this.EdgeScrollZone && (double) this.mousePosition.x > 0.0)
      {
        float f = (float) -((double) this.EdgeScrollZone - (double) this.mousePosition.x) / this.EdgeScrollZone;
        vector2.x = this.EdgeScrollSpeed * Mathf.Pow(f, 3f);
      }
      else if ((double) this.mousePosition.x > (double) this.viewRect.width - (double) this.EdgeScrollZone && (double) this.mousePosition.x < (double) this.viewRect.width)
      {
        float f = (this.mousePosition.x - (this.viewRect.width - this.EdgeScrollZone)) / this.EdgeScrollZone;
        vector2.x = this.EdgeScrollSpeed * Mathf.Pow(f, 3f);
      }
      if ((double) this.mousePosition.y < (double) this.EdgeScrollZone && (double) this.mousePosition.y > 0.0)
      {
        float f = (float) -((double) this.EdgeScrollZone - (double) this.mousePosition.y) / this.EdgeScrollZone;
        vector2.y = this.EdgeScrollSpeed * Mathf.Pow(f, 3f);
      }
      else if ((double) this.mousePosition.y > (double) this.viewRect.height - (double) this.EdgeScrollZone && (double) this.mousePosition.y < (double) this.viewRect.height)
      {
        float f = (this.mousePosition.y - (this.viewRect.height - this.EdgeScrollZone)) / this.EdgeScrollZone;
        vector2.y = this.EdgeScrollSpeed * Mathf.Pow(f, 3f);
      }
      if ((double) vector2.sqrMagnitude > 0.0)
        this.IsEdgeScrolling = true;
      vector2 *= this.edgeScrollRampUp;
      if ((double) vector2.sqrMagnitude > 0.0)
      {
        this.scrollPosition += vector2;
        if ((double) vector2.x <= 0.0)
        {
          double y = (double) vector2.y;
        }
      }
      return vector2;
    }

    public void TakeScreenshot(string filename)
    {
      if ((UnityEngine.Object) this.parentWindow == (UnityEngine.Object) null)
        return;
      this.isDocked = (bool) typeof (EditorWindow).GetProperty("docked", BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic).GetGetMethod(true).Invoke((object) this.parentWindow, (object[]) null);
      this.CancelAutoPan();
      this.screenshotFilename = filename;
      this.TakingScreenshot = true;
      this.ScreenshotFirstTile = true;
      double num1 = (double) this.SetZoom(1f);
      Rect rect = new Rect(this.viewRect);
      rect.x = 0.0f;
      this.screenshotCaptureRect = rect;
      --this.screenshotCaptureRect.width;
      ref Rect local = ref this.screenshotCaptureRect;
      rect = this.parentWindow.position;
      double num2 = (double) rect.height - (double) this.screenshotCaptureRect.height - (double) this.screenshotCaptureRect.y + 1.0;
      local.y = (float) num2;
      --this.screenshotCaptureRect.height;
      if (this.isDocked)
      {
        --this.screenshotCaptureRect.width;
        --this.screenshotCaptureRect.height;
      }
      float pixelsPerPoint = EditorGUIUtility.pixelsPerPoint;
      this.screenshotTexture = new Texture2D((int) ((double) this.ContentSize.x * (double) pixelsPerPoint), (int) ((double) this.ContentSize.y * (double) pixelsPerPoint), TextureFormat.RGB24, false);
      this.screenshotStartScrollPosition = this.scrollPosition;
      this.screenshotInitialMargin = this.contentMargin;
      this.contentMargin = 0.0f;
      this.SetContentScrollPosition(Vector2.zero);
      this.screenshotTargetPosition = new Vector2(0.0f, Mathf.Max(this.ContentSize.y - this.screenshotCaptureRect.height, 0.0f));
      this.Repaint();
      this.ViewChanged = true;
    }

    private void CaptureScreenshotTile()
    {
      Rect rect = new Rect(this.screenshotCaptureRect);
      bool flag1 = false;
      bool flag2 = false;
      if ((double) this.screenshotSourcePosition.x + (double) this.screenshotCaptureRect.width >= (double) this.ContentArea.width)
      {
        rect.width = this.ContentArea.width - this.screenshotSourcePosition.x;
        flag1 = true;
      }
      double num1 = (double) this.screenshotSourcePosition.y + (double) this.screenshotCaptureRect.height;
      Rect contentArea = this.ContentArea;
      double height = (double) contentArea.height;
      if (num1 >= height)
      {
        ref Rect local = ref rect;
        contentArea = this.ContentArea;
        double num2 = (double) contentArea.height - (double) this.screenshotSourcePosition.y;
        local.height = (float) num2;
        rect.y = this.screenshotCaptureRect.y + this.screenshotCaptureRect.height - rect.height;
        if (flag1)
          flag2 = true;
      }
      if (this.isDocked)
      {
        rect.x += 2f;
        rect.y += 2f;
      }
      float pixelsPerPoint = EditorGUIUtility.pixelsPerPoint;
      this.screenshotTexture.ReadPixels(rect.Scale(pixelsPerPoint), (int) ((double) this.screenshotTargetPosition.x * (double) pixelsPerPoint), (int) ((double) this.screenshotTargetPosition.y * (double) pixelsPerPoint));
      this.screenshotTexture.Apply(false);
      this.ScreenshotFirstTile = false;
      if (flag2)
        this.SaveScreenShot();
      else if (flag1)
      {
        ref Vector2 local = ref this.scrollPosition;
        contentArea = this.ContentArea;
        double x = (double) contentArea.x;
        local.x = (float) x;
        this.scrollPosition.y += this.screenshotCaptureRect.height;
        this.screenshotTargetPosition.x = 0.0f;
        this.screenshotTargetPosition.y -= this.screenshotCaptureRect.height;
        if ((double) this.screenshotTargetPosition.y < 0.0)
          this.screenshotTargetPosition.y = 0.0f;
      }
      else
      {
        this.scrollPosition.x += this.screenshotCaptureRect.width;
        this.screenshotTargetPosition.x += this.screenshotCaptureRect.width;
      }
      this.Repaint();
      this.graphRepainted = false;
    }

    private void SaveScreenShot(bool showConfirmDialog = true)
    {
      if ((UnityEngine.Object) this.screenshotTexture == (UnityEngine.Object) null)
      {
        Debug.LogError((object) Strings.Error_Bad_screenshot_texture);
      }
      else
      {
        string fullPath = Path.GetFullPath(Application.dataPath + "/" + this.screenshotFilename + ".png");
        Debug.Log((object) (Strings.Log_Saving_FSM_Screenshot__ + fullPath));
        if (!CanvasView.CreateFilePath(fullPath))
          return;
        byte[] png = this.screenshotTexture.EncodeToPNG();
        UnityEngine.Object.DestroyImmediate((UnityEngine.Object) this.screenshotTexture, true);
        File.WriteAllBytes(fullPath, png);
        this.TakingScreenshot = false;
        this.SetScrollPosition(this.screenshotStartScrollPosition);
        this.contentMargin = this.screenshotInitialMargin;
        this.Repaint();
        this.ViewChanged = true;
        this.graphRepainted = false;
        this.showSavedScreenshotDialog = showConfirmDialog;
        this.savedScreenshot = fullPath;
      }
    }

    public Vector2 EventToViewCoordinates(Vector2 eventPos) => new Vector2(eventPos.x - this.viewRect.x, eventPos.y - this.viewRect.y);

    public Vector2 ContentToViewCoordinates(Vector2 contentPos) => contentPos + this.ContentOrigin - this.ScrollPosition;

    public Vector2 ViewToWorldCoordinates(Vector2 viewPos) => this.LocalToWorldCoordinates(this.scrollPosition + viewPos);

    public Vector2 LocalToWorldCoordinates(Vector2 localPos) => (localPos - this.CanvasCenter) / this.scale;

    public Vector2 WorldToLocalCoordinates(Vector2 worldPos) => worldPos * this.scale + this.CanvasCenter;

    public Vector2 WorldToViewCoordinates(Vector2 worldPos) => this.WorldToLocalCoordinates(worldPos) - this.scrollPosition;

    private static bool CreateFilePath(string fullFileName)
    {
      string directoryName = Path.GetDirectoryName(fullFileName);
      if (string.IsNullOrEmpty(directoryName))
      {
        Debug.LogError((object) string.Format(Strings.File_Invalid_Path, (object) fullFileName));
        return false;
      }
      try
      {
        if (!Directory.Exists(directoryName))
          Directory.CreateDirectory(directoryName);
      }
      catch (Exception ex)
      {
        Debug.LogError((object) string.Format(Strings.File_Failed_To_Create_Directory, (object) directoryName));
        return false;
      }
      return true;
    }

    private void InitStyles()
    {
      if (this.stylesInitialized)
        return;
      CanvasView.horizontalScrollbarHeight = GUI.skin.horizontalScrollbar.fixedHeight;
      CanvasView.verticalScrollbarWidth = GUI.skin.verticalScrollbar.fixedWidth;
      this.hiddenHorizontalScrollbar = new GUIStyle(GUI.skin.horizontalScrollbar)
      {
        normal = {
          background = (Texture2D) null
        },
        fixedHeight = 0.0f
      };
      this.hiddenVerticalScrollbar = new GUIStyle(GUI.skin.verticalScrollbar)
      {
        normal = {
          background = (Texture2D) null
        },
        fixedWidth = 0.0f
      };
      this.stylesInitialized = true;
    }

    private void HandleInputEvents()
    {
      Event current = Event.current;
      UnityEngine.EventType type = current.type;
      this.mousePosition = current.mousePosition;
      this.mouseOverView = (double) this.mousePosition.x < (double) this.viewRect.width && (double) this.mousePosition.y < (double) this.viewRect.height;
      if (this.mouseOverView && type == UnityEngine.EventType.MouseDown)
        this.MouseDown = true;
      if (type == UnityEngine.EventType.MouseMove)
        this.MouseDown = false;
      if (this.mouseOverView && type == UnityEngine.EventType.ScrollWheel)
      {
        if (this.MouseWheelZoomsView)
        {
          if (!EditorGUI.actionKey)
          {
            double num = (double) this.ZoomView(this.mousePosition, -current.delta.y * this.zoomSpeed);
            current.delta = new Vector2(0.0f, 0.0f);
          }
        }
        else if (EditorGUI.actionKey)
        {
          double num = (double) this.ZoomView(this.mousePosition, -current.delta.y * this.zoomSpeed);
          current.delta = new Vector2(0.0f, 0.0f);
        }
        else if (current.shift)
        {
          this.scrollPosition += new Vector2(current.delta.y * 10f, 0.0f);
          current.delta = Vector2.zero;
          this.Repaint();
          this.ViewChanged = true;
        }
      }
      if (this.mouseOverView && type == UnityEngine.EventType.MouseDrag && (current.button == 2 || Keyboard.Alt()))
      {
        this.CancelAutoPan();
        this.DraggingCanvas = true;
        this.scrollPosition -= current.delta;
        this.Repaint();
        this.ViewChanged = true;
      }
      if (type == UnityEngine.EventType.MouseUp)
      {
        this.DraggingCanvas = false;
        this.IsEdgeScrolling = false;
        this.edgeScrollRampUp = 0.0f;
        this.MouseDown = false;
      }
      if (GUIUtility.keyboardControl != 0 || type != UnityEngine.EventType.KeyDown)
        return;
      switch (Event.current.keyCode)
      {
        case KeyCode.Plus:
        case KeyCode.Equals:
        case KeyCode.KeypadPlus:
          double num1 = (double) this.ZoomView(this.mousePosition, 0.1f);
          break;
        case KeyCode.Minus:
        case KeyCode.KeypadMinus:
          double num2 = (double) this.ZoomView(this.mousePosition, -0.1f);
          break;
      }
    }

    public class Marker
    {
      public Vector2 pos;
      public Color color;
    }
  }
}
