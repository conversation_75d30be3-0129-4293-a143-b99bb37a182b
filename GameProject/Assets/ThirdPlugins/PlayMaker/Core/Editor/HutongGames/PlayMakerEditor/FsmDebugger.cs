// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmDebugger
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class FsmDebugger
  {
    private static FsmDebugger instance;

    public static FsmDebugger Instance => FsmDebugger.instance ?? (FsmDebugger.instance = new FsmDebugger());

    public FsmDebugger.FsmStepMode StepMode { get; set; }

    public FsmDebugger()
    {
      Application.logMessageReceived -= new Application.LogCallback(this.HandleLog);
      Application.logMessageReceived += new Application.LogCallback(this.HandleLog);
    }

    ~FsmDebugger()
    {
      Application.logMessageReceived -= new Application.LogCallback(this.HandleLog);
      FsmDebugger.instance = (FsmDebugger) null;
    }

    public void HandleLog(string logEntry, string stackTrace, LogType type)
    {
      if (type != LogType.Error || FsmExecutionStack.ExecutingFsm == null || (GameStateTracker.CurrentState == GameState.Stopped || !stackTrace.Contains("HutongGames.PlayMaker")) || stackTrace.Contains("HutongGames.PlayMaker.Fsm:LogError(String)"))
        return;
      FsmExecutionStack.ExecutingFsm.DoBreakError(logEntry);
      FsmDebugger.DoBreak();
    }

    public void Update()
    {
      if (GameStateTracker.CurrentState == GameState.Stopped)
        return;
      foreach (PlayMakerFSM fsm in PlayMakerFSM.FsmList)
      {
        if ((Object) fsm != (Object) null)
        {
          this.Watch(fsm.Fsm);
          for (int index = 0; index < fsm.Fsm.SubFsmList.Count; ++index)
            this.Watch(fsm.Fsm.SubFsmList[index]);
        }
      }
      if (!Fsm.HitBreakpoint)
        return;
      FsmDebugger.DoBreak();
    }

    public void Watch(Fsm fsm)
    {
      if (!fsm.SwitchedState)
        return;
      FsmEditor.RepaintAll();
      if (FsmEditorSettings.SelectStateOnActivated && FsmEditor.Instance != null && FsmEditor.SelectedFsm == fsm)
        FsmEditor.SelectState(fsm.ActiveState, false);
      fsm.SwitchedState = false;
    }

    private static void DoBreak()
    {
      if (Fsm.IsErrorBreak)
        FsmErrorChecker.AddRuntimeError(Fsm.LastError);
      if (FsmEditorSettings.JumpToBreakpoint && FsmEditor.Instance != null)
        FsmEditor.GotoBreakpoint();
      Fsm.HitBreakpoint = false;
      EditorApplication.isPaused = true;
    }

    public void Step()
    {
      switch (this.StepMode)
      {
        case FsmDebugger.FsmStepMode.StepFrame:
          EditorApplication.isPaused = false;
          EditorApplication.Step();
          Fsm.StepToStateChange = false;
          break;
        case FsmDebugger.FsmStepMode.StepToStateChange:
          EditorApplication.isPaused = false;
          Fsm.StepToStateChange = true;
          Fsm.StepFsm = FsmEditor.SelectedFsm;
          break;
        case FsmDebugger.FsmStepMode.StepToAnyStateChange:
          EditorApplication.isPaused = false;
          Fsm.StepToStateChange = true;
          Fsm.StepFsm = (Fsm) null;
          break;
      }
      DebugFlow.UpdateTime();
    }

    public void ResetStep() => Fsm.StepToStateChange = false;

    public enum FsmStepMode
    {
      StepFrame,
      StepToStateChange,
      StepToAnyStateChange,
    }
  }
}
