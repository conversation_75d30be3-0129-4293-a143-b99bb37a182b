// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.SearchWindow
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class SearchWindow : BaseEditorWindow
  {
    private SearchBox searchBox;
    private string searchString = "";

    public override void InitWindowTitle() => this.SetTitle("FSM Search");

    public override void Initialize()
    {
      this.isToolWindow = true;
      this.minSize = new Vector2(200f, 100f);
      this.InitSearchBox();
      this.Repaint();
    }

    private void InitSearchBox()
    {
      if (this.searchBox != null)
        return;
      this.searchBox = new SearchBox((EditorWindow) this);
      this.searchBox.SearchChanged += new EditorApplication.CallbackFunction(this.UpdateSearchResults);
      this.searchBox.Focus();
    }

    private void UpdateSearchResults()
    {
      Debug.Log((object) "Search!");
      this.searchString = this.searchBox.SearchString;
      string.IsNullOrEmpty(this.searchString);
    }

    public override void DoGUI() => this.DoMainToolbar();

    private void DoMainToolbar()
    {
      EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
      this.searchBox.OnGUI();
      FsmEditorGUILayout.ToolbarSettingsButton();
      EditorGUILayout.EndHorizontal();
    }
  }
}
