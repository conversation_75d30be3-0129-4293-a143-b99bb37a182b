// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.InspectorPanel
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using System;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Serializable]
  public class InspectorPanel
  {
    private PixelCache _pixelCache;
    private static string[] _inspectorModeLabels;
    private const string ExpandPrefabInfoKey = "PlayMaker.ExpandPrefabInfo";
    private static bool expandPrefabInfo = true;
    private Rect errorBox = new Rect(90f, 0.0f, 20f, 20f);
    private static readonly Rect prefabInfoFoldout = new Rect(4f, 24f, 20f, 20f);
    private static readonly Rect prefabInfoFoldoutOpen = new Rect(6f, 21f, 20f, 20f);
    private static readonly float prefabFoldoutSpacer = 18f;
    private int mouseOverTab = -1;
    private double mouseOverStartTime;
    private const double tabSwitchDelay = 0.5;
    private static bool disableEditing;
    private static Vector2 prefabIconSize = new Vector2(64f, 64f);

    private PixelCache pixelCache => this._pixelCache ?? (this._pixelCache = new PixelCache(FsmEditor.Window));

    public Rect View { get; private set; }

    public InspectorMode Mode { get; private set; }

    private static string[] inspectorModeLabels
    {
      get
      {
        if (InspectorPanel._inspectorModeLabels == null)
          InspectorPanel.InitLabels();
        return InspectorPanel._inspectorModeLabels;
      }
    }

    public static void InitLabels() => InspectorPanel._inspectorModeLabels = new string[4]
    {
      Strings.FSM,
      Strings.Label_State,
      Strings.Label_Events,
      Strings.Label_Variables
    };

    public void Init()
    {
      InspectorPanel.InitLabels();
      this.RestoreSettings();
    }

    public void RestoreSettings()
    {
      InspectorMode inspectorMode = (InspectorMode) EditorPrefs.GetInt(EditorPrefStrings.InspectorMode, 0);
      this.SetMode(inspectorMode != InspectorMode.Watermarks ? inspectorMode : InspectorMode.StateInspector);
      InspectorPanel.expandPrefabInfo = EditorPrefs.GetBool("PlayMaker.ExpandPrefabInfo", true);
    }

    public void ResetView()
    {
      if (FsmEditorSettings.UsePixelCaching)
        this.InvalidatePixelCache();
      switch (this.Mode)
      {
        case InspectorMode.FsmInspector:
          FsmEditor.FsmInspector.Reset();
          FsmEditor.UpdateFsmInfo();
          break;
        case InspectorMode.StateInspector:
          FsmEditor.StateInspector.Reset();
          break;
        case InspectorMode.EventManager:
          FsmEditor.EventsManager.Reset();
          break;
        case InspectorMode.VariableManager:
          FsmEditor.VariablesManager.Reset();
          FsmEditor.UpdateFsmInfo();
          break;
        case InspectorMode.Watermarks:
          WatermarkSelector.ResetSelection();
          break;
      }
    }

    public void SetMode(InspectorMode mode)
    {
      if (this.Mode == mode)
        return;
      FsmEditor.DoDirtyFsmPrefab();
      EditorPrefs.SetInt(EditorPrefStrings.InspectorMode, (int) mode);
      Keyboard.ResetFocus();
      this.EndMode(this.Mode);
      this.Mode = mode;
      this.ResetView();
      switch (this.Mode)
      {
        case InspectorMode.StateInspector:
          StateInspector.Init();
          break;
        case InspectorMode.Preferences:
          FsmEditorSettings.LoadCategory();
          break;
        case InspectorMode.Watermarks:
          WatermarkSelector.Init();
          break;
      }
      FsmEditor.InvalidatePixelCache();
      FsmEditor.Repaint(true);
    }

    private void EndMode(InspectorMode mode)
    {
      if (mode != InspectorMode.StateInspector)
        return;
      StateInspector.Close();
    }

    public void OnGUI(Rect area)
    {
      this.View = area;
      if (FsmEditorSettings.UsePixelCaching && this.pixelCache.Draw(area))
        return;
      EditorGUI.indentLevel = 0;
      FsmEditorGUILayout.AutoLabelWidth();
      HighlighterHelper.BeginArea(area);
      if (EditorApplication.isPlaying && FsmEditorSettings.DisableInspectorWhenPlaying)
        InspectorPanel.DoDisabledAtRuntimeGUI();
      else
        this.DoInspectorPanelGUI(area);
      HighlighterHelper.EndArea();
      if (!FsmEditorSettings.UsePixelCaching || Event.current.type != UnityEngine.EventType.Repaint)
        return;
      bool canCapture = !Application.isPlaying && FsmEditor.GraphView.IsDragging || !FsmEditor.MouseOverInspector;
      this.pixelCache.OnRepaint(area, canCapture);
    }

    public static bool DoUsesTemplateUI()
    {
      if (Application.isPlaying || !FsmEditor.SelectedFsmUsesTemplate)
        return false;
      bool enabled = GUI.enabled;
      GUI.enabled = true;
      if (GUILayout.Button("Uses Template. Click to Edit Template.", FsmEditorStyles.HintBox))
        FsmEditor.SelectFsm(FsmEditor.SelectedFsmComponent.FsmTemplate);
      GUI.enabled = enabled;
      return true;
    }

    public void Update()
    {
      if (FsmEditor.Instance == null || EditorApplication.isPlaying && FsmEditorSettings.DisableInspectorWhenPlaying)
        return;
      if (this.Mode == InspectorMode.Watermarks && FsmEditor.SelectedFsm == null)
        this.SetMode(InspectorMode.FsmInspector);
      if (this.Mode != InspectorMode.StateInspector)
        return;
      FsmEditor.StateInspector.Update();
    }

    public void InvalidatePixelCache()
    {
      if (this.pixelCache == null)
        return;
      this.pixelCache.Invalidate();
    }

    private void DoModeSelector()
    {
      this.errorBox.Set(FsmEditor.InspectorPanelWidth * 0.25f, 1f, 20f, 20f);
      if (FsmEditorStyles.UsingFlatStyle)
        GUILayout.BeginHorizontal(FsmEditorStyles.Toolbar);
      InspectorMode mode = (InspectorMode) GUILayout.Toolbar((int) this.Mode, InspectorPanel.inspectorModeLabels, FsmEditorStyles.ToolbarTab);
      if (mode != this.Mode)
        this.SetMode(mode);
      if (FsmErrorChecker.StateHasActionErrors(FsmEditor.SelectedState))
      {
        FsmEditorContent.ErrorIcon.tooltip = FsmErrorChecker.GetStateErrors(FsmEditor.SelectedState);
        GUI.Box(this.errorBox, FsmEditorContent.ErrorIcon, FsmEditorStyles.InlineErrorIcon);
      }
      else
        GUI.Box(this.errorBox, GUIContent.none, GUIStyle.none);
      if (FsmEditorStyles.UsingFlatStyle)
        GUILayout.EndHorizontal();
      GUILayout.Space(1f);
      if (Event.current.type != UnityEngine.EventType.DragUpdated)
        return;
      Vector2 mousePosition = Event.current.mousePosition;
      if ((double) mousePosition.x > 0.0 && (double) mousePosition.y > 0.0 && (double) mousePosition.y < (double) EditorStyles.toolbar.fixedHeight)
      {
        int num = Mathf.FloorToInt(4f * mousePosition.x / FsmEditor.InspectorPanelWidth);
        if (this.mouseOverTab != num)
        {
          this.mouseOverStartTime = EditorApplication.timeSinceStartup;
          this.mouseOverTab = num;
        }
        if (EditorApplication.timeSinceStartup - this.mouseOverStartTime <= 0.5)
          return;
        this.mouseOverStartTime = EditorApplication.timeSinceStartup;
        switch (num)
        {
          case 0:
            this.SetMode(InspectorMode.FsmInspector);
            GUIUtility.ExitGUI();
            break;
          case 1:
            this.SetMode(InspectorMode.StateInspector);
            GUIUtility.ExitGUI();
            break;
          case 2:
            this.SetMode(InspectorMode.EventManager);
            GUIUtility.ExitGUI();
            break;
          case 3:
            this.SetMode(InspectorMode.VariableManager);
            GUIUtility.ExitGUI();
            break;
        }
      }
      else
        this.mouseOverTab = -1;
    }

    private void DoInspectorPanelGUI(Rect area)
    {
      this.DoModeSelector();
      if (this.Mode != InspectorMode.Preferences)
      {
        FsmEditorGUILayout.UnlockFsmGUI(FsmEditor.SelectedFsm);
        EditorGUI.BeginDisabledGroup(FsmEditor.SelectedFsm != null && FsmEditor.SelectedFsm.Locked);
      }
      switch (this.Mode)
      {
        case InspectorMode.FsmInspector:
          InspectorPanel.BeginPrefabInstanceCheck();
          FsmEditor.FsmInspector.OnGUI(FsmEditor.SelectedFsm);
          InspectorPanel.EndPrefabInstanceCheck();
          break;
        case InspectorMode.StateInspector:
          if (!FsmEditor.SelectedFsmIsLocked)
          {
            InspectorPanel.BeginPrefabInstanceCheck();
            FsmEditor.StateInspector.OnGUI();
            InspectorPanel.EndPrefabInstanceCheck();
            break;
          }
          GUILayout.FlexibleSpace();
          break;
        case InspectorMode.EventManager:
          if (!FsmEditor.SelectedFsmIsLocked)
          {
            InspectorPanel.BeginPrefabInstanceCheck();
            FsmEditor.EventsManager.OnGUI();
            InspectorPanel.EndPrefabInstanceCheck();
            break;
          }
          GUILayout.FlexibleSpace();
          break;
        case InspectorMode.VariableManager:
          if (!FsmEditor.SelectedFsmIsLocked)
          {
            InspectorPanel.BeginPrefabInstanceCheck();
            FsmEditor.VariablesManager.OnGUI();
            InspectorPanel.EndPrefabInstanceCheck();
            break;
          }
          GUILayout.FlexibleSpace();
          break;
        case InspectorMode.Preferences:
          FsmEditorSettings.OnGUI(area);
          break;
        case InspectorMode.Watermarks:
          WatermarkSelector.OnGUI();
          break;
      }
      if (this.Mode != InspectorMode.Preferences)
        EditorGUI.EndDisabledGroup();
      this.DoBottomToolbar();
    }

    private static void DoDisabledAtRuntimeGUI()
    {
      GUILayout.BeginHorizontal(EditorStyles.toolbar);
      GUILayout.FlexibleSpace();
      GUILayout.EndHorizontal();
      GUILayout.Label(Strings.Hint_Inspector_disabled_when_playing);
      FsmEditorSettings.DisableInspectorWhenPlaying = !GUILayout.Toggle(!FsmEditorSettings.DisableInspectorWhenPlaying, Strings.Hint_Enable_inspector_when_playing);
      FsmEditorSettings.SaveSettingsIfGuiChanged();
    }

    private void DoBottomToolbar()
    {
      GUILayout.BeginHorizontal(FsmEditorStyles.ToolbarBottom);
      GUILayout.Space(2f);
      bool flag = GUILayout.Toggle((FsmEditorSettings.ShowHints ? 1 : 0) != 0, Strings.Command_Toggle_Hints, EditorStyles.toolbarButton, GUILayout.MaxWidth(100f));
      if (flag != FsmEditorSettings.ShowHints)
      {
        FsmEditorSettings.ShowHints = flag;
        FsmEditorSettings.SaveSettings();
        FsmEditor.RepaintAll();
      }
      if (GUILayout.Button(Strings.Command_Preferences, EditorStyles.toolbarButton, GUILayout.MinWidth(150f)))
        this.SetMode(InspectorMode.Preferences);
      GUILayout.EndHorizontal();
    }

    private static void DoApplyRevertControls()
    {
      InspectorPanel.BeginPrefabControls();
      InspectorPanel.DoPrefabSelectionControls();
      EditorGUI.BeginDisabledGroup(!FsmEditor.SelectedFsmIsModifiedPrefabInstance);
      if (GUILayout.Button("Overrides", EditorStyles.miniPullDown))
      {
        GenericMenu genericMenu = new GenericMenu();
        genericMenu.AddItem(new GUIContent("Revert All"), false, new GenericMenu.MenuFunction(FsmPrefabs.Revert));
        genericMenu.AddItem(new GUIContent("Apply All"), false, new GenericMenu.MenuFunction(FsmPrefabs.Apply));
        genericMenu.ShowAsContext();
      }
      EditorGUI.EndDisabledGroup();
      InspectorPanel.EndPrefabControls();
    }

    private static void DoApplyRevertAddedComponentControls()
    {
      InspectorPanel.BeginPrefabControls();
      GUILayout.Label("Prefab Override", GUILayout.Width(100f));
      if (GUILayout.Button(Strings.Label_Revert, EditorStyles.miniButton))
      {
        FsmPrefabs.RevertAddedComponent();
        GUIUtility.ExitGUI();
      }
      if (GUILayout.Button(Strings.Label_Apply, EditorStyles.miniButton))
        FsmPrefabs.ApplyAddedComponent();
      InspectorPanel.EndPrefabControls();
    }

    private static void BeginPrefabInstanceCheck()
    {
      if (!FsmEditorSettings.ConfirmEditingPrefabInstances || EditorApplication.isPlaying || FsmEditor.SelectedFsm == null)
      {
        EditorGUI.BeginDisabledGroup(false);
      }
      else
      {
        GUILayout.BeginVertical(FsmEditorStyles.TopBarBG);
        if (FsmEditor.SelectedFsmIsDisconnected)
        {
          InspectorPanel.PrefabInfoBox(FsmEditorContent.DisconnectedPrefabLabel, FsmEditorStyles.InfoBox);
          EditorGUI.BeginDisabledGroup(false);
          InspectorPanel.DoApplyRevertControls();
          InspectorPanel.EndPrefabBox();
        }
        else if (FsmEditor.SelectedFsmIsModifiedPrefabInstance)
        {
          InspectorPanel.PrefabInfoBox(FsmEditorContent.ModifiedPrefabLabel, FsmEditorStyles.InfoBox);
          EditorGUI.BeginDisabledGroup(false);
          InspectorPanel.DoApplyRevertControls();
          InspectorPanel.EndPrefabBox();
        }
        else if ((UnityEngine.Object) FsmEditor.SelectedFsmGameObject != (UnityEngine.Object) null && PrefabUtility.IsPrefabAssetMissing((UnityEngine.Object) FsmEditor.SelectedFsmGameObject))
        {
          InspectorPanel.PrefabInfoBox(FsmEditorContent.PrefabWarning, FsmEditorStyles.InfoBox);
          EditorGUI.BeginDisabledGroup(false);
          InspectorPanel.DoPrefabMissingControls();
          InspectorPanel.EndPrefabBox();
        }
        else if (FsmEditor.SelectedFsmIsAddedComponentOverride())
        {
          InspectorPanel.PrefabInfoBox(FsmEditorContent.AddedComponentOverrideLabel, FsmEditorStyles.InfoBox);
          EditorGUI.BeginDisabledGroup(false);
          InspectorPanel.DoApplyRevertAddedComponentControls();
          InspectorPanel.EndPrefabBox();
        }
        else if (FsmEditor.EditingPrefabInstance())
        {
          InspectorPanel.PrefabInfoBox(FsmEditorContent.PrefabWarning, FsmEditorStyles.InfoBox);
          InspectorPanel.DoApplyRevertControls();
          InspectorPanel.EndPrefabBox();
          EditorGUI.BeginDisabledGroup(false);
        }
        else
        {
          InspectorPanel.disableEditing = FsmPrefabs.IsPrefabInstance(FsmEditor.SelectedFsm);
          if (InspectorPanel.disableEditing)
          {
            InspectorPanel.PrefabInfoBox(FsmEditorContent.PrefabWarning, FsmEditorStyles.InfoBox);
            InspectorPanel.BeginPrefabControls();
            InspectorPanel.DoPrefabSelectionControls();
            if (GUILayout.Button(Strings.Label_Edit_Instance, EditorStyles.miniButton))
              FsmEditor.StartEditingPrefabInstance();
            InspectorPanel.EndPrefabControls();
            InspectorPanel.EndPrefabBox();
            FsmEditor.GraphView.DisableEditing(Strings.Label_Editing_of_Prefab_Instance_is_disabled);
            EditorGUI.BeginDisabledGroup(true);
          }
          else
          {
            EditorGUI.BeginDisabledGroup(InspectorPanel.disableEditing);
            GUILayout.EndVertical();
          }
        }
      }
    }

    private static void BeginPrefabControls()
    {
      GUILayout.BeginHorizontal();
      if (InspectorPanel.expandPrefabInfo)
        return;
      GUILayout.Space(InspectorPanel.prefabFoldoutSpacer);
    }

    private static void EndPrefabControls() => GUILayout.EndHorizontal();

    private static void DoPrefabSelectionControls()
    {
      GUILayout.Label(Strings.Label_Prefab, GUILayout.Width(50f));
      if (GUILayout.Button("Open", EditorStyles.miniButton))
        FsmEditor.OpenPrefabParent();
      if (GUILayout.Button("Select", EditorStyles.miniButton))
        FsmEditor.SelectPrefabParent();
      GUILayout.Space(10f);
    }

    private static void DoPrefabMissingControls()
    {
      InspectorPanel.BeginPrefabControls();
      GUILayout.Label("Prefab", GUILayout.Width(50f));
      GUILayout.Label("Missing");
      GUILayout.Space(40f);
      if (GUILayout.Button("Unpack Prefab", EditorStyles.miniButton))
        PrefabUtility.UnpackPrefabInstance(FsmEditor.SelectedFsmGameObject, PrefabUnpackMode.OutermostRoot, InteractionMode.UserAction);
      InspectorPanel.EndPrefabControls();
    }

    private static void PrefabInfoBox(GUIContent content, GUIStyle style, string altText = "")
    {
      Vector2 iconSize = EditorGUIUtility.GetIconSize();
      EditorGUIUtility.SetIconSize(InspectorPanel.prefabIconSize);
      EditorGUI.BeginChangeCheck();
      InspectorPanel.expandPrefabInfo = EditorGUI.Toggle(InspectorPanel.expandPrefabInfo ? InspectorPanel.prefabInfoFoldoutOpen : InspectorPanel.prefabInfoFoldout, InspectorPanel.expandPrefabInfo, EditorStyles.foldout);
      if (EditorGUI.EndChangeCheck())
      {
        EditorPrefs.SetBool("PlayMaker.ExpandPrefabInfo", InspectorPanel.expandPrefabInfo);
        GUIUtility.ExitGUI();
      }
      if (InspectorPanel.expandPrefabInfo)
        GUI.Box(GUILayoutUtility.GetRect(100f, 1000f, 70f, 70f, style), content, style);
      else
        GUILayout.Space(4f);
      if (altText != "")
      {
        GUILayout.BeginHorizontal();
        GUILayout.Space(InspectorPanel.prefabFoldoutSpacer);
        GUILayout.Label(altText);
        GUILayout.EndHorizontal();
      }
      EditorGUIUtility.SetIconSize(iconSize);
    }

    private static void EndPrefabBox()
    {
      GUILayout.Space(3f);
      GUILayout.EndVertical();
      GUILayout.Space(1f);
    }

    private static void EndPrefabInstanceCheck() => EditorGUI.EndDisabledGroup();

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("InspectorPanel: " + message));
  }
}
