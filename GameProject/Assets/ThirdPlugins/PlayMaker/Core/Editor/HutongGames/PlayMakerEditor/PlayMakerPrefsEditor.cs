// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.PlayMakerPrefsEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [CustomEditor(typeof (PlayMakerPrefs))]
  public class PlayMakerPrefsEditor : UnityEditor.Editor
  {
    private SerializedProperty debugLinesDuration;
    private SerializedProperty colorsArray;
    private SerializedProperty colorNamesArray;
    private SerializedProperty tweenFromColor;
    private SerializedProperty tweenToColor;
    private SerializedProperty arrowColor;
    private SerializedProperty logPerfWarnings;
    private SerializedProperty showEventHandlers;
    private SerializedProperty organizePools;
    private SerializedProperty hidePools;
    private SerializedProperty autoNamePools;
    private SerializedProperty oldActionNamesArray;
    private SerializedProperty newActionNamesArray;

    public void OnEnable()
    {
      this.debugLinesDuration = this.serializedObject.FindProperty("debugLinesDuration");
      this.colorsArray = this.serializedObject.FindProperty("colors");
      this.colorNamesArray = this.serializedObject.FindProperty("colorNames");
      this.tweenFromColor = this.serializedObject.FindProperty("tweenFromColor");
      this.tweenToColor = this.serializedObject.FindProperty("tweenToColor");
      this.arrowColor = this.serializedObject.FindProperty("arrowColor");
      this.logPerfWarnings = this.serializedObject.FindProperty("logPerformanceWarnings");
      this.showEventHandlers = this.serializedObject.FindProperty("showEventHandlerComponents");
      this.organizePools = this.serializedObject.FindProperty("organizePoolsInHierarchy");
      this.hidePools = this.serializedObject.FindProperty("hidePoolsInHierarchy");
      this.autoNamePools = this.serializedObject.FindProperty("autoNamePoolInstances");
      this.oldActionNamesArray = this.serializedObject.FindProperty("oldActionNames");
      this.newActionNamesArray = this.serializedObject.FindProperty("newActionNames");
    }

    public override void OnInspectorGUI()
    {
      FsmEditorStyles.Init();
      EditorGUIUtility.labelWidth = GUILayoutUtility.GetRect(GUIContent.none, GUIStyle.none, GUILayout.Height(0.0f)).width * 0.5f;
      this.serializedObject.Update();
      EditorGUI.BeginChangeCheck();
      this.DoDebugGUI();
      this.DoColorsGUI();
      this.DoPoolGUI();
      this.DoRenamedActionsGUI();
      if (!EditorGUI.EndChangeCheck())
        return;
      this.serializedObject.ApplyModifiedProperties();
    }

    private void DoRenamedActionsGUI()
    {
      PlayMakerPrefs target = (PlayMakerPrefs) this.target;
      this.Header("Renamed Actions");
      EditorGUILayout.HelpBox("Use action renaming rules to recover Missing Actions if an action was renamed.", MessageType.None);
      if (this.oldActionNamesArray.arraySize > 0)
        EditorGUILayout.LabelField("Old Name", "New Name");
      for (int index = 0; index < this.oldActionNamesArray.arraySize; ++index)
      {
        SerializedProperty arrayElementAtIndex1 = this.oldActionNamesArray.GetArrayElementAtIndex(index);
        SerializedProperty arrayElementAtIndex2 = this.newActionNamesArray.GetArrayElementAtIndex(index);
        GUILayout.BeginHorizontal();
        EditorGUILayout.PropertyField(arrayElementAtIndex1, GUIContent.none);
        EditorGUILayout.PropertyField(arrayElementAtIndex2, GUIContent.none);
        if (FsmEditorGUILayout.DeleteButton())
        {
          Undo.RecordObject((Object) target, "Delete Action Rename Rule");
          target.DeleteActionRenameRule(index);
        }
        GUILayout.EndHorizontal();
      }
      GUILayout.Space(5f);
      if (!FsmEditorGUILayout.CenteredButton("Add Rename Rule", 200f))
        return;
      Undo.RecordObject((Object) target, "Add Action Rename Rule");
      target.AddActionRenameRule("", "");
    }

    private void DoColorsGUI()
    {
      PlayMakerPrefs target = (PlayMakerPrefs) this.target;
      this.Header("Scene GUI Colors");
      EditorGUILayout.PropertyField(this.tweenFromColor);
      EditorGUILayout.PropertyField(this.tweenToColor);
      EditorGUILayout.PropertyField(this.arrowColor);
      this.Header("Default Colors");
      for (int index = 0; index < 8; ++index)
      {
        SerializedProperty arrayElementAtIndex1 = this.colorsArray.GetArrayElementAtIndex(index);
        SerializedProperty arrayElementAtIndex2 = this.colorNamesArray.GetArrayElementAtIndex(index);
        GUILayout.BeginHorizontal();
        EditorGUILayout.PropertyField(arrayElementAtIndex2, GUIContent.none);
        EditorGUILayout.PropertyField(arrayElementAtIndex1, GUIContent.none);
        GUILayout.EndHorizontal();
      }
      GUILayout.Space(5f);
      if (FsmEditorGUILayout.CenteredButton("Reset Default Colors", 200f))
        target.ResetDefaultColors();
      this.Header("Custom Colors");
      for (int index = 8; index < 24; ++index)
      {
        SerializedProperty arrayElementAtIndex1 = this.colorsArray.GetArrayElementAtIndex(index);
        SerializedProperty arrayElementAtIndex2 = this.colorNamesArray.GetArrayElementAtIndex(index);
        GUILayout.BeginHorizontal();
        EditorGUILayout.PropertyField(arrayElementAtIndex2, GUIContent.none);
        EditorGUILayout.PropertyField(arrayElementAtIndex1, GUIContent.none);
        GUILayout.EndHorizontal();
      }
    }

    private void DoDebugGUI()
    {
      this.Header("Debug");
      EditorGUILayout.PropertyField(this.logPerfWarnings);
      EditorGUILayout.PropertyField(this.showEventHandlers);
      EditorGUILayout.PropertyField(this.debugLinesDuration);
    }

    private void DoPoolGUI()
    {
    }

    private void Header(string title)
    {
      GUILayout.Space(10f);
      GUILayout.Label(title.ToUpper(), EditorStyles.boldLabel);
      FsmEditorGUILayout.LightDivider();
      GUILayout.Space(2f);
    }
  }
}
