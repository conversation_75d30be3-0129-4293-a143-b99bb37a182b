// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmEditorStyles
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using System;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public static class FsmEditorStyles
  {
    private static bool usingProSkin;
    public const float dragAndDropInsertLineSpacer = 2f;
    public const string toolbarSearchTextFieldStyle = "ToolbarSeachTextField";
    public const string toolbarSearchCancelButtonStyle = "ToolbarSeachCancelButton";
    public const string buttonStyle = "Button";
    public const string labelStyle = "Label";
    public const string toggleStyle = "Toggle";
    public const string boxStyle = "Box";
    public const string newline = "\n";
    public const string tab = "\t";
    public const string tab2 = "\t\t";
    public const float linkAlpha = 0.9f;
    public static float linkAlphaFaded = 0.3f;
    public const float globalLinkAlpha = 0.9f;
    public const float globalLinkAlphaFaded = 0.3f;
    [Obsolete("Use FsmEditorSettings.StateMinWidth")]
    public const float StateMinWidth = 100f;
    [Obsolete("Use FsmEditorSettings.StateMaxWidth")]
    public const float StateMaxWidth = 400f;
    public const float DefaultStateRowHeight = 16f;
    public const float StateWidthPadding = 20f;
    public const float DescriptionHeight = 44f;
    public const float MaxFsmDescriptionWidth = 200f;
    public static Vector2 IconSize = new Vector2(16f, 16f);
    public static float StateRowHeight = 16f;
    public static readonly Color ErrorTextColor = new Color(1f, 0.0f, 0.0f);
    public static readonly Color ErrorTextColorIndie = new Color(0.8f, 0.0f, 0.0f);
    public static readonly Color DimmedStateColor = new Color(1f, 1f, 1f, 0.5f);
    public static readonly Color[] LinkColors = new Color[6]
    {
      new Color(0.1f, 0.1f, 0.1f),
      new Color(0.24f, 0.38f, 0.57f),
      new Color(0.06f, 0.44f, 0.06f),
      new Color(1f, 0.0f, 0.0f),
      new Color(1f, 0.0f, 0.0f),
      new Color(0.8f, 0.8f, 0.0f)
    };
    public static Color[] FadedColors;
    public static Color FadedLinkColor;
    public static Color[] HighlightColors = new Color[6];
    public static readonly Color[] ActionColors = new Color[6]
    {
      new Color(1f, 1f, 1f),
      new Color(1f, 1f, 1f),
      new Color(0.06f, 1f, 0.06f),
      new Color(1f, 0.0f, 0.0f),
      new Color(1f, 1f, 1f),
      new Color(0.8f, 0.8f, 0.0f)
    };
    public static readonly float[] LinkWidths = new float[6]
    {
      1.75f,
      3f,
      2f,
      3f,
      3f,
      1.75f
    };
    private static Color graphTextColor;
    private static float initScale;
    private static bool scaleInitialized;
    private static bool initialized;
    private static Texture2D[] gameStateIcons;
    private static GUIStyle[] logTypeStyles;
    public static Color DefaultBackgroundColor = new Color(0.3f, 0.3f, 0.3f);

    public static float Scale { get; private set; }

    public static bool UsingFlatStyle { get; private set; }

    public static int ScrollBarWidth { get; set; }

    public static Texture2D SplitLine { get; set; }

    public static GUIStyle OutputEventIndicator { get; set; }

    public static GUIStyle ReadonlyObjectField { get; set; }

    public static GUIStyle ReadonlyObjectFieldNone { get; set; }

    public static GUIStyle ErrorTitle { get; set; }

    public static GUIStyle ErrorTitleSelected { get; set; }

    public static GUIStyle ErrorLine { get; set; }

    public static GUIStyle ErrorLineSelected { get; set; }

    public static GUIStyle ResizeHandle { get; set; }

    public static GUIStyle InfoButton { get; set; }

    public static GUIStyle InfoButtonDisabled { get; set; }

    public static GUIStyle Toolbar { get; set; }

    public static GUIStyle ToolbarBottom { get; set; }

    public static GUIStyle ToolbarCheckbox { get; set; }

    public static GUIStyle ToolbarImageButton { get; set; }

    public static GUIStyle InspectorHeader { get; set; }

    public static GUIStyle LogLineFoldout { get; set; }

    public static GUIStyle LogLineFoldoutBackground { get; set; }

    public static GUIStyle SectionTitle { get; set; }

    public static GUIStyle SectionHeader { get; set; }

    public static GUIStyle HintText { get; set; }

    public static GUIStyle StandardMargins { get; set; }

    public static GUIStyle LeftArrowHead { get; set; }

    public static GUIStyle RightArrowHead { get; set; }

    public static GUIStyle GlobalTransitionArrow { get; set; }

    public static Texture2D LogIcon { get; set; }

    public static GUIStyle EventButton { get; set; }

    public static GUIStyle EventGlobalButton { get; set; }

    public static GUIStyle ButtonLeftText { get; set; }

    public static Texture2D InfoIcon { get; set; }

    public static Texture2D WarningIcon { get; set; }

    public static Texture2D ErrorIcon { get; set; }

    public static Texture2D HelpIcon { get; set; }

    public static GUIStyle HelpBoxSolid { get; set; }

    public static Color DefaultSetPropertyColor { get; set; }

    public static GUIStyle StandardPadding { get; private set; }

    public static GUIStyle IconButton { get; private set; }

    public static GUIStyle ToolbarIconButton { get; private set; }

    public static Color GraphBackgroundColor { get; private set; }

    public static Color BackgroundColor { get; private set; }

    public static Color LabelTextColor { get; private set; }

    public static Texture2D SelectedBG { get; private set; }

    public static GUIStyle PropertyListRow { get; private set; }

    public static GUIStyle DragHandle { get; private set; }

    public static GUIStyle LockButton { get; private set; }

    public static GUIStyle NewTag { get; private set; }

    public static GUIStyle TopBarBG { get; private set; }

    public static GUIStyle BottomBarBG { get; private set; }

    public static GUIStyle FlatButton { get; private set; }

    public static GUIStyle TimelineLabelLeft { get; private set; }

    public static GUIStyle TimelineLabelRight { get; private set; }

    public static GUIStyle TimelineBarText { get; private set; }

    public static GUIStyle TimelineDebugLine { get; private set; }

    public static GUIStyle TimelineBar { get; private set; }

    public static GUIStyle DarkPreviewBg { get; private set; }

    public static GUIStyle RightAlignedLabel { get; private set; }

    public static GUIStyle ColorSwatch { get; private set; }

    public static GUIStyle CenteredLabel { get; private set; }

    public static GUIStyle LabelWithWordWrap { get; private set; }

    public static GUIStyle BoldLabelWithWordWrap { get; private set; }

    public static GUIStyle ActionPreviewTitle { get; private set; }

    public static GUIStyle ToolbarTab { get; private set; }

    public static GUIStyle TextAreaWithWordWrap { get; private set; }

    public static GUIStyle TextFieldWithWordWrap { get; private set; }

    public static GUIStyle RichTextLabelWithWordWrap { get; private set; }

    public static GUIStyle Background { get; private set; }

    public static GUIStyle InnerShadow { get; private set; }

    public static GUIStyle InnerGlowBox { get; private set; }

    public static GUIStyle SelectionBox { get; private set; }

    public static GUIStyle DropShadowBox { get; private set; }

    public static GUIStyle SinglePixelFrame { get; private set; }

    public static GUIStyle DoublePixelFrame { get; private set; }

    public static GUIStyle SelectionFrame { get; private set; }

    public static GUIStyle SelectionRect { get; private set; }

    public static GUIStyle CommentNode { get; private set; }

    public static GUIStyle GroupBox { get; private set; }

    public static GUIStyle StateBox { get; private set; }

    public static GUIStyle StateTitleBox { get; private set; }

    public static GUIStyle StateTitleLongBox { get; private set; }

    public static GUIStyle TransitionBox { get; private set; }

    public static GUIStyle TransitionBoxSelected { get; private set; }

    public static GUIStyle GlobalTransitionBox { get; private set; }

    public static GUIStyle StartTransitionBox { get; private set; }

    public static GUIStyle BreakpointOff { get; private set; }

    public static GUIStyle BreakpointOn { get; private set; }

    public static GUIStyle FoldoutClosed { get; private set; }

    public static GUIStyle FoldoutOpen { get; private set; }

    public static GUIStyle FoldoutUp { get; private set; }

    public static GUIStyle FoldoutDown { get; private set; }

    public static Texture2D LineTexture { get; private set; }

    public static Texture2D TitleIcon { get; private set; }

    public static Texture2D LeftArrow { get; private set; }

    public static Texture2D RightArrow { get; private set; }

    public static Texture2D StartArrow { get; private set; }

    public static Texture2D GlobalArrow { get; private set; }

    public static Texture2D StateErrorIcon { get; private set; }

    public static Texture2D BroadcastIcon { get; private set; }

    public static Texture2D SendEventIcon { get; private set; }

    public static Texture2D SendsEventsIcon { get; private set; }

    public static Texture2D ColorIcon { get; private set; }

    public static Texture2D ColorWheelIcon { get; private set; }

    public static Texture2D WanMediumIcon { get; private set; }

    public static Texture2D WanTabIcon { get; private set; }

    public static Texture2D AnimateVariableIcon { get; private set; }

    public static Texture2D AnimationIcon { get; private set; }

    public static Texture2D AnimatorIcon { get; private set; }

    public static Texture2D ApplicationIcon { get; private set; }

    public static Texture2D ArrayIcon { get; private set; }

    public static Texture2D AudioIcon { get; private set; }

    public static Texture2D CameraIcon { get; private set; }

    public static Texture2D CanvasIcon { get; private set; }

    public static Texture2D ConvertIcon { get; private set; }

    public static Texture2D ControllerIcon { get; private set; }

    public static Texture2D DeviceIcon { get; private set; }

    public static Texture2D EffectsIcon { get; private set; }

    public static Texture2D EnumIcon { get; private set; }

    public static Texture2D FavoritesIcon { get; private set; }

    public static Texture2D GameObjectIcon { get; private set; }

    public static Texture2D GuiIcon { get; private set; }

    public static Texture2D GuiElementIcon { get; private set; }

    public static Texture2D GuiLayoutIcon { get; private set; }

    public static Texture2D InputIcon { get; private set; }

    public static Texture2D LightIcon { get; private set; }

    public static Texture2D LogicIcon { get; private set; }

    public static Texture2D MathIcon { get; private set; }

    public static Texture2D MaterialIcon { get; private set; }

    public static Texture2D MeshIcon { get; private set; }

    public static Texture2D MovieCamIcon { get; private set; }

    public static Texture2D SphereColliderIcon { get; private set; }

    public static Texture2D CircleColliderIcon { get; private set; }

    public static Texture2D QuaternionIcon { get; private set; }

    public static Texture2D RectIcon { get; private set; }

    public static Texture2D RectTransformIcon { get; private set; }

    public static Texture2D RecentIcon { get; private set; }

    public static Texture2D RenderIcon { get; private set; }

    public static Texture2D SceneIcon { get; private set; }

    public static Texture2D SpriteIcon { get; private set; }

    public static Texture2D StringIcon { get; private set; }

    public static Texture2D StateMachineIcon { get; private set; }

    public static Texture2D SubstanceIcon { get; private set; }

    public static Texture2D TestsIcon { get; private set; }

    public static Texture2D TransformIcon { get; private set; }

    public static Texture2D Vector2Icon { get; private set; }

    public static Texture2D Vector3Icon { get; private set; }

    public static Texture2D TweenIcon { get; private set; }

    public static Texture2D TrigIcon { get; private set; }

    public static Texture2D TimeIcon { get; private set; }

    public static Texture2D UnityObjectIcon { get; private set; }

    public static Texture2D WwwIcon { get; private set; }

    public static Texture2D DebugIcon { get; private set; }

    public static Texture2D SaveIcon { get; private set; }

    public static Texture2D StopwatchIcon { get; private set; }

    public static Texture2D PoolIcon { get; private set; }

    public static Texture2D GamepadIcon { get; private set; }

    public static GUIStyle Divider { get; private set; }

    public static GUIStyle DividerSequence { get; private set; }

    public static GUIStyle ActionFoldout { get; private set; }

    public static GUIStyle ActionToggle { get; private set; }

    public static GUIStyle ActionTitlebar { get; private set; }

    public static GUIStyle HeaderTitlebar { get; private set; }

    public static GUIStyle ActionTitle { get; private set; }

    public static GUIStyle ActionTitleError { get; private set; }

    public static GUIStyle ActionTitleSelected { get; private set; }

    public static GUIStyle ActionHeader { get; private set; }

    public static GUIStyle ActionHeaderSelected { get; private set; }

    public static GUIStyle CategoryHeader { get; private set; }

    public static GUIStyle CategoryHeaderEditing { get; private set; }

    public static GUIStyle CategoryFoldout { get; private set; }

    public static GUIStyle VersionInfo { get; private set; }

    public static GUIStyle ActionErrorBox { get; private set; }

    public static GUIStyle EventBox { get; private set; }

    public static GUIStyle SelectedEventBox { get; private set; }

    public static GUIStyle TableSectionHeader { get; private set; }

    public static GUIStyle TableRowHeader { get; private set; }

    public static GUIStyle TableRowBox { get; private set; }

    public static GUIStyle TableRowBoxNoDivider { get; private set; }

    public static GUIStyle ErrorBox { get; private set; }

    public static GUIStyle InfoBox { get; private set; }

    public static GUIStyle HintBox { get; private set; }

    public static GUIStyle InlineButton { get; private set; }

    public static GUIStyle MiniButton { get; private set; }

    public static GUIStyle MiniButtonPadded { get; private set; }

    public static GUIStyle MiniToggle { get; private set; }

    public static GUIStyle ActionCategory { get; private set; }

    public static GUIStyle ActionCategoryIcon { get; private set; }

    public static GUIStyle ActionCategoryCount { get; private set; }

    public static GUIStyle ActionItem { get; private set; }

    public static GUIStyle ActionItemSelected { get; private set; }

    public static GUIStyle ActionLabel { get; private set; }

    public static GUIStyle ActionLabelSelected { get; private set; }

    public static GUIStyle BoldLabel { get; private set; }

    public static GUIStyle BoldLabelSelected { get; private set; }

    public static GUIStyle LargeLabel { get; private set; }

    public static GUIStyle LargeLabelSelected { get; private set; }

    public static GUIStyle ErrorCount { get; private set; }

    public static Texture2D NoErrors { get; private set; }

    public static Texture2D Errors { get; private set; }

    public static GUIStyle RightAlignedToolbarDropdown { get; private set; }

    public static GUIStyle ToolbarHeading { get; private set; }

    public static GUIStyle TableRow { get; private set; }

    public static GUIStyle TableRowSelected { get; private set; }

    public static GUIStyle TableRowCheckBox { get; private set; }

    public static GUIStyle LogoLarge { get; private set; }

    public static GUIStyle CommentBox { get; private set; }

    public static GUIStyle CommentBoxEditor { get; private set; }

    public static GUIStyle HintBoxTextOnly { get; private set; }

    public static GUIStyle TextArea { get; private set; }

    public static GUIStyle RichTextHelpBox { get; private set; }

    public static GUIStyle RichTextHelpBoxSolid { get; private set; }

    public static GUIStyle BoldFoldout { get; private set; }

    public static GUIStyle TableRowText { get; private set; }

    public static GUIStyle TableRowTextSelected { get; private set; }

    public static GUIStyle TableRowTextAlignRight { get; private set; }

    public static GUIStyle TableRowTextSelectedAlignRight { get; private set; }

    public static GUIStyle SelectedRow { get; private set; }

    public static GUIStyle InsertLine { get; private set; }

    public static GUIStyle LogBackground { get; private set; }

    public static GUIStyle LogLine { get; private set; }

    public static GUIStyle LogLine2 { get; private set; }

    public static GUIStyle LogLineSelected { get; private set; }

    public static GUIStyle LogLineTimeline { get; private set; }

    public static GUIStyle InlineErrorIcon { get; set; }

    public static Color GuiContentErrorColor { get; private set; }

    public static Color GuiBackgroundErrorColor { get; private set; }

    public static Color MinimapFrameColor { get; private set; }

    public static Color MinimapViewRectColor { get; private set; }

    public static Color WatermarkTint { get; private set; }

    public static Color WatermarkTintSolid { get; private set; }

    public static Texture DefaultWatermark { get; private set; }

    public static GUIStyle Watermark { get; set; }

    public static GUIStyle LargeWatermarkText { get; private set; }

    public static GUIStyle LargeText { get; private set; }

    public static GUIStyle SmallWatermarkText { get; private set; }

    public static GUIStyle LargeTitleText { get; private set; }

    public static GUIStyle LargeTitleWithLogo { get; private set; }

    public static GUIStyle PlaymakerHeader { get; private set; }

    public static GUIStyle MediumTitleText { get; private set; }

    public static GUIStyle WelcomeLink { get; private set; }

    public static Texture BasicsIcon { get; private set; }

    public static Texture DocsIcon { get; private set; }

    public static Texture VideoIcon { get; private set; }

    public static Texture ForumIcon { get; private set; }

    public static Texture SamplesIcon { get; private set; }

    public static Texture PhotonIcon { get; private set; }

    public static Texture AddonsIcon { get; private set; }

    public static Texture BlackBerryAddonIcon { get; set; }

    public static Texture WP8AddonIcon { get; set; }

    public static Texture MetroAddonIcon { get; set; }

    public static Texture BackButton { get; private set; }

    [Obsolete("Use ConvertIcon")]
    public static Texture2D Converticon => FsmEditorStyles.ConvertIcon;

    [Obsolete("Use EnumIcon")]
    public static Texture2D Enumicon => FsmEditorStyles.EnumIcon;

    public static GUIStyle DefaultStateBoxStyle { get; private set; }

    public static Color ActiveHighlightColor => FsmEditorStyles.HighlightColors[2];

    public static Color PausedHighlightColor => FsmEditorStyles.HighlightColors[5];

    public static Color BreakpointHighlightColor => FsmEditorStyles.HighlightColors[4];

    public static GUIStyle LogInfo { get; private set; }

    static FsmEditorStyles() => EditorApplication.playModeStateChanged += (Action<PlayModeStateChange>) (change => FsmEditorStyles.Reinitialize());

    [Localizable(false)]
    public static bool UsingProSkin() => EditorGUIUtility.isProSkin;

    public static bool IsInitialized() => FsmEditorStyles.initialized && FsmEditorStyles.scaleInitialized && (FsmEditorStyles.usingProSkin == FsmEditorStyles.UsingProSkin() && !((UnityEngine.Object) FsmEditorStyles.SectionHeader.normal.background == (UnityEngine.Object) null)) && (UnityEngine.Object) FsmEditorStyles.LeftArrow != (UnityEngine.Object) null;

    public static void Reinitialize() => FsmEditorStyles.initialized = false;

    public static void Init()
    {
      if (FsmEditorStyles.IsInitialized() || Event.current == null)
        return;
      FsmEditorStyles.InitCommon();
      FsmEditorStyles.usingProSkin = FsmEditorStyles.UsingProSkin();
      if (FsmEditorStyles.usingProSkin)
        FsmEditorStyles.InitProSkin();
      else
        FsmEditorStyles.InitIndieSkin();
      FsmEditorStyles.InspectorHeader = new GUIStyle(FsmEditorStyles.ActionCategory)
      {
        padding = new RectOffset(5, 0, 0, 0)
      };
      FsmEditorContent.Init(FsmEditorStyles.usingProSkin);
      FsmEditorStyles.InitColorScheme(FsmEditorSettings.ColorScheme);
      FsmEditorStyles.InitFadedColors();
      FsmEditorStyles.SetScale(FsmEditorStyles.initScale);
      FsmEditorStyles.initialized = true;
    }

    public static Texture2D GetMessageIcon(MessageIcons icon)
    {
      switch (icon)
      {
        case MessageIcons.None:
        case MessageIcons.Custom:
          return (Texture2D) null;
        case MessageIcons.Help:
          return FsmEditorStyles.HelpIcon;
        case MessageIcons.Info:
          return FsmEditorStyles.InfoIcon;
        case MessageIcons.Warning:
          return FsmEditorStyles.WarningIcon;
        case MessageIcons.Error:
          return FsmEditorStyles.ErrorIcon;
        case MessageIcons.PlayMaker:
          return FsmEditorStyles.WanMediumIcon;
        default:
          UnityEngine.Debug.LogError((object) ("Missing Message Icon: " + (object) icon));
          return (Texture2D) null;
      }
    }

    public static Texture2D TextureFromColor(float grey, float alpha = 0.0f) => FsmEditorStyles.TextureFromColor(new Color(grey, grey, grey), alpha);

    public static Texture2D TextureFromColor(Color color, float alpha = 0.0f)
    {
      Texture2D texture2D = new Texture2D(2, 2, TextureFormat.ARGB32, false);
      if ((double) alpha > 0.0)
        color.a = alpha;
      texture2D.SetPixel(0, 0, color);
      texture2D.SetPixel(1, 0, color);
      texture2D.SetPixel(0, 1, color);
      texture2D.SetPixel(1, 1, color);
      texture2D.Apply();
      return texture2D;
    }

    private static void SaveBuiltinIcon(Texture2D tex)
    {
      Texture2D tex1 = new Texture2D(tex.width, tex.height, TextureFormat.ARGB32, false);
      tex1.LoadRawTextureData(tex.GetRawTextureData());
      tex1.name = tex.name;
      FsmEditorStyles.SaveTexture(tex1);
    }

    private static void SaveTexture(Texture2D tex)
    {
      byte[] png = tex.EncodeToPNG();
      File.WriteAllBytes(Application.dataPath + "/../Playmaker/Icons/" + tex.name + "_" + GUI.skin.name + ".png", png);
    }

    private static void LoadStyleBackground(
      GUIStyle style,
      string resourceName,
      int width,
      int height,
      UnityEngine.FilterMode filterMode = UnityEngine.FilterMode.Bilinear)
    {
      style.normal.background = Files.LoadTextureFromDll(resourceName, width, height, filterMode);
      Texture2D texture2D = Files.LoadTextureFromDll(resourceName + "@2x", width * 2, height * 2, filterMode);
      if (!((UnityEngine.Object) texture2D != (UnityEngine.Object) null))
        return;
      style.normal.scaledBackgrounds = new Texture2D[1]
      {
        texture2D
      };
    }

    private static void Load2xBackground(GUIStyle style, string resourceName)
    {
      if (style == null)
        return;
      Texture2D background = style.normal.background;
      if ((UnityEngine.Object) background == (UnityEngine.Object) null)
        return;
      Texture2D texture2D = Files.LoadTextureFromDll(resourceName + "@2x", background.width * 2, background.height * 2, background.filterMode);
      if (!((UnityEngine.Object) texture2D != (UnityEngine.Object) null))
        return;
      style.normal.scaledBackgrounds = new Texture2D[1]
      {
        texture2D
      };
    }

    private static void InitCommon()
    {
      FsmEditorStyles.UsingFlatStyle = (double) EditorGUIUtility.singleLineHeight > 16.0;
      FsmEditorStyles.ScrollBarWidth = (int) GUI.skin.verticalScrollbar.fixedWidth;
      FsmEditorStyles.linkAlphaFaded = 0.3f;
      if (FsmEditorSettings.ColorScheme == FsmEditorStyles.ColorScheme.LightBackground || !FsmEditorStyles.usingProSkin)
        FsmEditorStyles.linkAlphaFaded = 0.5f;
      FsmEditorStyles.ResizeHandle = new GUIStyle();
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.ResizeHandle, "resizeHandle", 16, 16);
      FsmEditorStyles.InfoButton = new GUIStyle(EditorStyles.label);
      FsmEditorStyles.InfoButtonDisabled = new GUIStyle(FsmEditorStyles.InfoButton);
      FsmEditorStyles.Toolbar = new GUIStyle(EditorStyles.toolbar);
      FsmEditorStyles.ToolbarBottom = FsmEditorStyles.UsingFlatStyle ? new GUIStyle((GUIStyle) "ToolbarBottom") : EditorStyles.toolbar;
      FsmEditorStyles.ToolbarCheckbox = new GUIStyle(EditorStyles.toggle);
      if (FsmEditorStyles.UsingFlatStyle)
      {
        ++FsmEditorStyles.ToolbarCheckbox.margin.top;
        FsmEditorStyles.ToolbarCheckbox.padding.bottom += 2;
      }
      else
      {
        FsmEditorStyles.ToolbarCheckbox.margin = new RectOffset();
        FsmEditorStyles.ToolbarCheckbox.padding = new RectOffset(15, 3, 2, 2);
      }
      FsmEditorStyles.ToolbarImageButton = new GUIStyle(EditorStyles.toolbarButton)
      {
        padding = new RectOffset(6, 6, 0, 0)
      };
      FsmEditorStyles.ReadonlyObjectField = new GUIStyle((GUIStyle) "TextField")
      {
        fixedHeight = EditorGUIUtility.singleLineHeight,
        imagePosition = ImagePosition.ImageLeft
      };
      FsmEditorStyles.ReadonlyObjectFieldNone = new GUIStyle((GUIStyle) "TextField")
      {
        fixedHeight = EditorGUIUtility.singleLineHeight,
        imagePosition = ImagePosition.TextOnly
      };
      FsmEditorStyles.SectionTitle = new GUIStyle(EditorStyles.largeLabel)
      {
        fontSize = 14,
        margin = {
          top = 10
        },
        normal = {
          textColor = EditorGUIUtility.isProSkin ? new Color(0.7f, 0.7f, 0.7f, 1f) : new Color(0.2f, 0.2f, 0.2f, 1f)
        }
      };
      FsmEditorStyles.SectionHeader = new GUIStyle(EditorStyles.label)
      {
        alignment = TextAnchor.MiddleLeft,
        normal = {
          background = FsmEditorStyles.TextureFromColor(0.7137255f)
        },
        margin = new RectOffset(0, 0, 5, 0),
        padding = new RectOffset(5, 0, 0, 0),
        fixedHeight = 21f
      };
      FsmEditorStyles.LogLineFoldout = new GUIStyle(EditorStyles.foldout)
      {
        fontStyle = FontStyle.Bold,
        normal = {
          textColor = Color.white
        },
        active = {
          textColor = Color.white
        },
        onActive = {
          textColor = Color.white
        },
        onNormal = {
          textColor = Color.white
        },
        onFocused = {
          textColor = Color.white
        }
      };
      FsmEditorStyles.LogLineFoldoutBackground = new GUIStyle(FsmEditorStyles.SectionHeader)
      {
        normal = {
          background = Files.LoadTextureFromDll("logEntryBox", 16, 16),
          textColor = Color.white
        }
      };
      FsmEditorStyles.HintText = new GUIStyle(EditorStyles.label)
      {
        alignment = TextAnchor.UpperLeft,
        padding = EditorStyles.textField.padding
      };
      FsmEditorStyles.StandardMargins = new GUIStyle()
      {
        padding = new RectOffset(5, 4, 0, 0)
      };
      FsmEditorStyles.LeftArrowHead = new GUIStyle();
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.LeftArrowHead, "leftArrow", 17, 11);
      FsmEditorStyles.RightArrowHead = new GUIStyle();
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.RightArrowHead, "rightArrow", 17, 11);
      FsmEditorStyles.GlobalTransitionArrow = new GUIStyle();
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.GlobalTransitionArrow, "globalArrow", 16, 32);
      FsmEditorStyles.DefaultSetPropertyColor = new Color(0.2509804f, 0.5176471f, 0.5568628f);
      FsmEditorStyles.graphTextColor = new Color(1f, 1f, 1f, 0.7f);
      FsmEditorStyles.LabelTextColor = EditorStyles.label.normal.textColor;
      FsmEditorStyles.BackgroundColor = (Color) (EditorGUIUtility.isProSkin ? new Color32((byte) 63, (byte) 63, (byte) 63, byte.MaxValue) : new Color32((byte) 151, (byte) 151, (byte) 151, byte.MaxValue));
      FsmEditorStyles.GraphBackgroundColor = FsmEditorStyles.BackgroundColor;
      switch (FsmEditorSettings.ColorScheme)
      {
        case FsmEditorStyles.ColorScheme.DarkBackground:
          FsmEditorStyles.GraphBackgroundColor = (Color) new Color32((byte) 63, (byte) 63, (byte) 63, byte.MaxValue);
          break;
        case FsmEditorStyles.ColorScheme.LightBackground:
          FsmEditorStyles.GraphBackgroundColor = (Color) new Color32((byte) 151, (byte) 151, (byte) 151, byte.MaxValue);
          break;
      }
      FsmEditorStyles.InfoIcon = (Texture2D) GUIHelpers.GetBuiltinIcon("console.infoicon");
      FsmEditorStyles.WarningIcon = (Texture2D) GUIHelpers.GetBuiltinIcon("console.warnicon");
      FsmEditorStyles.ErrorIcon = (Texture2D) GUIHelpers.GetBuiltinIcon("console.erroricon");
      FsmEditorStyles.HelpIcon = (Texture2D) GUIHelpers.GetBuiltinIcon("_Help");
      FsmEditorStyles.LogIcon = (Texture2D) GUIHelpers.GetBuiltinIcon("d_UnityEditor.ConsoleWindow");
      FsmEditorStyles.ButtonLeftText = new GUIStyle((GUIStyle) "Button")
      {
        alignment = TextAnchor.MiddleLeft
      };
      FsmEditorStyles.EventButton = FsmEditorStyles.ButtonLeftText;
      FsmEditorStyles.EventButton.padding.left = 20;
      FsmEditorStyles.EventGlobalButton = new GUIStyle(FsmEditorStyles.EventButton);
      FsmEditorStyles.EventGlobalButton.padding.left = 26;
      FsmEditorStyles.HelpBoxSolid = new GUIStyle(EditorStyles.helpBox)
      {
        normal = {
          background = Files.LoadTextureFromDll("noteBackground", 11, 8)
        }
      };
      FsmEditorStyles.RichTextHelpBox = new GUIStyle(EditorStyles.helpBox)
      {
        richText = true
      };
      FsmEditorStyles.RichTextHelpBoxSolid = new GUIStyle(FsmEditorStyles.HelpBoxSolid)
      {
        richText = true
      };
      FsmEditorStyles.IconButton = new GUIStyle();
      FsmEditorStyles.ToolbarIconButton = new GUIStyle(FsmEditorStyles.IconButton)
      {
        padding = new RectOffset(4, 4, 2, 2)
      };
      FsmEditorStyles.BottomBarBG = new GUIStyle((GUIStyle) "ProjectBrowserBottomBarBg");
      FsmEditorStyles.BottomBarBG.stretchHeight = false;
      FsmEditorStyles.BottomBarBG.padding.top += 4;
      FsmEditorStyles.BottomBarBG.padding.bottom += 6;
      FsmEditorStyles.TopBarBG = new GUIStyle((GUIStyle) "ProjectBrowserTopBarBg");
      FsmEditorStyles.TopBarBG.fixedHeight = 0.0f;
      FsmEditorStyles.TopBarBG.border = new RectOffset(0, 0, 0, 1);
      FsmEditorStyles.TopBarBG.padding = new RectOffset(2, 0, 0, 0);
      FsmEditorStyles.FoldoutClosed = new GUIStyle((GUIStyle) "IN foldout");
      FsmEditorStyles.FoldoutClosed.padding = new RectOffset(16, 0, 0, 0);
      FsmEditorStyles.FoldoutClosed.margin = new RectOffset(0, 0, 1, 0);
      FsmEditorStyles.FoldoutOpen = new GUIStyle(FsmEditorStyles.FoldoutClosed);
      FsmEditorStyles.FoldoutOpen.normal.background = FsmEditorStyles.FoldoutOpen.onNormal.background;
      FsmEditorStyles.FoldoutDown = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("GroupFoldoutDown", 27, 16)
        }
      };
      FsmEditorStyles.FoldoutUp = new GUIStyle(FsmEditorStyles.FoldoutDown)
      {
        normal = {
          background = Files.LoadTextureFromDll("GroupFoldoutUp", 27, 16)
        }
      };
      FsmEditorStyles.LockButton = new GUIStyle((GUIStyle) "IN LockButton");
      FsmEditorStyles.DragHandle = new GUIStyle((GUIStyle) "RL DragHandle");
      FsmEditorStyles.DragHandle.alignment = TextAnchor.MiddleCenter;
      FsmEditorStyles.DragHandle.margin = new RectOffset(5, 5, 5, 5);
      FsmEditorStyles.DragHandle.padding = new RectOffset(5, 5, 5, 5);
      FsmEditorStyles.PropertyListRow = new GUIStyle()
      {
        padding = new RectOffset(5, 5, 0, 0)
      };
      FsmEditorStyles.FlatButton = new GUIStyle()
      {
        normal = {
          textColor = FsmEditorStyles.BackgroundColor,
          background = FsmEditorStyles.TextureFromColor(FsmEditorStyles.LabelTextColor, 0.5f)
        },
        fontSize = 12,
        border = new RectOffset(5, 0, 2, 2),
        padding = new RectOffset(3, 3, 3, 3),
        margin = new RectOffset(3, 3, 1, 1),
        alignment = TextAnchor.MiddleLeft,
        fixedHeight = 22f
      };
      FsmEditorStyles.TimelineBarText = new GUIStyle()
      {
        normal = {
          textColor = Color.white
        },
        alignment = TextAnchor.MiddleLeft,
        padding = new RectOffset(2, 0, 0, 1)
      };
      FsmEditorStyles.TimelineLabelLeft = new GUIStyle(FsmEditorStyles.TimelineBarText)
      {
        normal = {
          background = Files.LoadTextureFromDll("smallLeftArrow", 6, 20, UnityEngine.FilterMode.Point)
        },
        border = new RectOffset(5, 0, 0, 0),
        padding = new RectOffset(6, 0, 0, 1)
      };
      FsmEditorStyles.TimelineLabelRight = new GUIStyle(FsmEditorStyles.TimelineBarText)
      {
        normal = {
          background = Files.LoadTextureFromDll("smallRightArrow", 6, 20, UnityEngine.FilterMode.Point)
        },
        border = new RectOffset(0, 5, 0, 0),
        padding = new RectOffset(0, 6, 0, 1)
      };
      FsmEditorStyles.TimelineDebugLine = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("whiteVertical", 5, 2),
          textColor = Color.white
        },
        fixedWidth = 5f
      };
      FsmEditorStyles.TimelineBar = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("timelineBar", 4, 16, UnityEngine.FilterMode.Point)
        },
        border = new RectOffset(1, 1, 1, 1)
      };
      FsmEditorStyles.DarkPreviewBg = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("darkPreviewBg", 32, 32)
        },
        border = new RectOffset(13, 13, 13, 13)
      };
      FsmEditorStyles.ColorSwatch = new GUIStyle((GUIStyle) "Box")
      {
        border = new RectOffset(2, 2, 2, 2),
        margin = FsmEditorStyles.UsingFlatStyle ? new RectOffset(0, 4, 4, 1) : new RectOffset(0, 4, 2, 2)
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.ColorSwatch, "swatchBox", 16, 16);
      FsmEditorStyles.RightAlignedLabel = new GUIStyle(EditorStyles.label)
      {
        alignment = TextAnchor.MiddleRight
      };
      FsmEditorStyles.CenteredLabel = new GUIStyle(EditorStyles.label)
      {
        alignment = TextAnchor.MiddleCenter
      };
      FsmEditorStyles.TextAreaWithWordWrap = new GUIStyle(EditorStyles.textField)
      {
        wordWrap = true
      };
      FsmEditorStyles.TextFieldWithWordWrap = new GUIStyle(EditorStyles.textField)
      {
        wordWrap = true
      };
      FsmEditorStyles.LabelWithWordWrap = new GUIStyle(EditorStyles.label)
      {
        wordWrap = true
      };
      FsmEditorStyles.RichTextLabelWithWordWrap = new GUIStyle(FsmEditorStyles.LabelWithWordWrap)
      {
        richText = true
      };
      FsmEditorStyles.BoldLabelWithWordWrap = new GUIStyle(EditorStyles.boldLabel)
      {
        wordWrap = true
      };
      FsmEditorStyles.TextArea = new GUIStyle(EditorStyles.textField)
      {
        wordWrap = true
      };
      FsmEditorStyles.ToolbarTab = new GUIStyle(EditorStyles.toolbarButton);
      FsmEditorStyles.ActionPreviewTitle = new GUIStyle(FsmEditorStyles.BoldLabelWithWordWrap)
      {
        padding = new RectOffset(2, 2, -3, 5)
      };
      FsmEditorStyles.LogoLarge = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("playMakerLogo", 256, 67)
        },
        margin = new RectOffset(4, 4, 4, 8),
        fixedWidth = 256f,
        fixedHeight = 67f
      };
      FsmEditorStyles.RightAlignedToolbarDropdown = new GUIStyle(EditorStyles.toolbarDropDown)
      {
        alignment = TextAnchor.MiddleRight
      };
      FsmEditorStyles.ToolbarHeading = new GUIStyle(EditorStyles.toolbarButton)
      {
        alignment = TextAnchor.MiddleLeft
      };
      FsmEditorStyles.ErrorCount = new GUIStyle(EditorStyles.toolbarButton)
      {
        padding = new RectOffset(21, 5, 2, 2),
        alignment = TextAnchor.MiddleLeft
      };
      FsmEditorStyles.Errors = Files.LoadTexture("errorCount", 14, 14);
      FsmEditorStyles.NoErrors = Files.LoadTexture("noErrors", 14, 14);
      FsmEditorStyles.Background = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("graphBackground", 32, 32)
        },
        border = new RectOffset(16, 16, 20, 10)
      };
      FsmEditorStyles.InnerShadow = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("innerGlowBox", 32, 32)
        },
        border = new RectOffset(14, 14, 14, 14)
      };
      FsmEditorStyles.InnerGlowBox = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll(FsmEditorStyles.UsingFlatStyle ? "innerGlowBox_flat" : "innerGlowBox", 32, 32)
        },
        border = new RectOffset(14, 14, 14, 14)
      };
      FsmEditorStyles.SelectionBox = new GUIStyle()
      {
        border = new RectOffset(11, 11, 11, 11),
        margin = new RectOffset(3, 3, 3, 3),
        overflow = new RectOffset(10, 10, 10, 10)
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.SelectionBox, "outerGlow", 32, 32);
      FsmEditorStyles.SelectionRect = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("selectionRect", 8, 8)
        },
        border = new RectOffset(3, 3, 3, 3)
      };
      FsmEditorStyles.DropShadowBox = new GUIStyle()
      {
        border = new RectOffset(31, 31, 16, 16),
        margin = new RectOffset(3, 3, 3, 3),
        overflow = new RectOffset(15, 15, 15, 15)
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.DropShadowBox, "dropShadowBox", 64, 64);
      FsmEditorStyles.StateBox = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("stateBox", 16, 16)
        },
        border = new RectOffset(2, 2, 2, 2),
        overflow = new RectOffset(1, 1, 2, 1)
      };
      FsmEditorStyles.StateTitleBox = new GUIStyle()
      {
        normal = {
          textColor = FsmEditorStyles.graphTextColor
        },
        border = new RectOffset(1, 1, 1, 1),
        alignment = TextAnchor.MiddleCenter,
        fontStyle = FontStyle.Bold,
        fontSize = 12,
        contentOffset = new Vector2(0.0f, -1f),
        fixedHeight = FsmEditorStyles.StateRowHeight,
        padding = new RectOffset(5, 5, 0, 0),
        overflow = new RectOffset(0, 0, 1, 0)
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.StateTitleBox, "stateTitleBox", 16, 16);
      FsmEditorStyles.DefaultStateBoxStyle = new GUIStyle(FsmEditorStyles.StateTitleBox);
      FsmEditorStyles.StateTitleLongBox = new GUIStyle(FsmEditorStyles.StateTitleBox)
      {
        alignment = TextAnchor.MiddleLeft
      };
      FsmEditorStyles.CommentNode = new GUIStyle()
      {
        normal = {
          background = Texture2D.whiteTexture
        },
        padding = new RectOffset(5, 5, 5, 5)
      };
      FsmEditorStyles.GroupBox = new GUIStyle(FsmEditorStyles.StateTitleBox)
      {
        border = new RectOffset(2, 2, 17, 2),
        fixedHeight = 0.0f,
        alignment = TextAnchor.UpperLeft,
        overflow = new RectOffset(0, 0, 16, 0),
        padding = {
          top = -15
        }
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.GroupBox, "groupBox", 16, 20);
      FsmEditorStyles.TransitionBox = new GUIStyle()
      {
        normal = {
          textColor = Color.white
        },
        border = new RectOffset(4, 4, 2, 2),
        fixedHeight = FsmEditorStyles.StateRowHeight,
        alignment = TextAnchor.MiddleCenter
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.TransitionBox, "transitionBox", 16, 16);
      FsmEditorStyles.TransitionBoxSelected = new GUIStyle(FsmEditorStyles.TransitionBox)
      {
        normal = {
          textColor = FsmEditorStyles.graphTextColor
        }
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.TransitionBoxSelected, "transitionBoxSelected", 16, 16);
      FsmEditorStyles.OutputEventIndicator = new GUIStyle()
      {
        border = new RectOffset(8, 8, 0, 0),
        fixedHeight = FsmEditorStyles.StateRowHeight,
        overflow = new RectOffset(12, 12, 0, 0)
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.OutputEventIndicator, "outputEvent", 20, 16);
      FsmEditorStyles.GlobalTransitionBox = new GUIStyle(FsmEditorStyles.TransitionBox)
      {
        normal = {
          textColor = new Color(0.8f, 0.8f, 0.8f)
        },
        fontStyle = FontStyle.Bold,
        contentOffset = new Vector2(0.0f, -1f)
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.GlobalTransitionBox, "globalTransitionBox", 16, 16);
      FsmEditorStyles.StartTransitionBox = new GUIStyle(FsmEditorStyles.GlobalTransitionBox)
      {
        normal = {
          background = Files.LoadTextureFromDll("startTransitionBox", 16, 16)
        },
        fontStyle = FontStyle.Bold
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.StartTransitionBox, "startTransitionBox", 16, 16);
      FsmEditorStyles.SinglePixelFrame = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("singlePixelFrame", 16, 16)
        },
        border = new RectOffset(8, 8, 8, 8),
        padding = new RectOffset(0, 0, -10, 0)
      };
      FsmEditorStyles.DoublePixelFrame = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("doublePixelFrame", 16, 16)
        },
        border = new RectOffset(8, 8, 8, 8),
        padding = new RectOffset(0, 0, -10, 0)
      };
      FsmEditorStyles.SelectionFrame = new GUIStyle(FsmEditorStyles.DoublePixelFrame)
      {
        overflow = new RectOffset(0, 0, 2, 0)
      };
      FsmEditorStyles.BreakpointOff = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("breakpointOff", 5, 16)
        },
        overflow = new RectOffset(0, 0, 1, 0)
      };
      FsmEditorStyles.BreakpointOn = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("breakpointOn", 5, 16)
        },
        overflow = new RectOffset(0, 0, 1, 0)
      };
      FsmEditorStyles.TitleIcon = Files.LoadTextureFromDll("wanIcon", 20, 20);
      FsmEditorStyles.LineTexture = Files.LoadTextureFromDll("line", 2, 4);
      FsmEditorStyles.LeftArrow = Files.LoadTexture("leftArrow", 17, 12);
      FsmEditorStyles.RightArrow = Files.LoadTextureFromDll("rightArrow", 17, 12);
      FsmEditorStyles.StartArrow = Files.LoadTextureFromDll("startArrow", 28, 64);
      FsmEditorStyles.GlobalArrow = Files.LoadTextureFromDll("globalArrow", 16, 32);
      FsmEditorStyles.StateErrorIcon = Files.LoadTexture("errorCount", 14, 14);
      FsmEditorStyles.BroadcastIcon = Files.LoadTexture("broadcastIcon", 16, 16);
      FsmEditorStyles.SendEventIcon = Files.LoadTexture("sendEventIcon", 16, 16);
      FsmEditorStyles.SendsEventsIcon = Files.LoadTexture("SendsEventsIcon", 10, 16);
      FsmEditorStyles.ColorIcon = Files.LoadTextureFromDll("colorIcon", 64, 64);
      FsmEditorStyles.ColorWheelIcon = Files.LoadTextureFromDll("colorWheelIcon", 32, 32);
      FsmEditorStyles.WanMediumIcon = Files.LoadTextureFromDll("wanMedium", 32, 32);
      FsmEditorStyles.WanTabIcon = Files.LoadTextureFromDll("wanTabIcon", 64, 64);
      FsmEditorStyles.AnimateVariableIcon = Files.LoadTextureFromDll("animateVariableIcon", 64, 64);
      FsmEditorStyles.AnimationIcon = Files.LoadTextureFromDll("animationIcon", 64, 64);
      FsmEditorStyles.AnimatorIcon = Files.LoadTextureFromDll("animatorIcon", 64, 64);
      FsmEditorStyles.ApplicationIcon = Files.LoadTextureFromDll("applicationIcon", 64, 64);
      FsmEditorStyles.ArrayIcon = Files.LoadTextureFromDll("arrayIcon", 64, 64);
      FsmEditorStyles.AudioIcon = Files.LoadTextureFromDll("audioIcon", 64, 64);
      FsmEditorStyles.CameraIcon = Files.LoadTextureFromDll("cameraIcon", 64, 64);
      FsmEditorStyles.CanvasIcon = Files.LoadTextureFromDll("canvasIcon", 64, 64);
      FsmEditorStyles.ConvertIcon = Files.LoadTextureFromDll("convertIcon", 64, 64);
      FsmEditorStyles.TestsIcon = Files.LoadTextureFromDll("testsIcon", 64, 64);
      FsmEditorStyles.ControllerIcon = Files.LoadTextureFromDll("controllerIcon", 64, 64);
      FsmEditorStyles.DeviceIcon = Files.LoadTextureFromDll("deviceIcon", 64, 64);
      FsmEditorStyles.EffectsIcon = Files.LoadTextureFromDll("effectsIcon", 64, 64);
      FsmEditorStyles.EnumIcon = Files.LoadTextureFromDll("enumIcon", 64, 64);
      FsmEditorStyles.FavoritesIcon = Files.LoadTextureFromDll("favoritesIcon", 64, 64);
      FsmEditorStyles.GameObjectIcon = Files.LoadTextureFromDll("gameobjectIcon", 64, 64);
      FsmEditorStyles.GuiIcon = Files.LoadTextureFromDll("guiIcon", 64, 64);
      FsmEditorStyles.GuiElementIcon = Files.LoadTextureFromDll("guiElementIcon", 64, 64);
      FsmEditorStyles.GuiLayoutIcon = Files.LoadTextureFromDll("guiLayoutIcon", 64, 64);
      FsmEditorStyles.InputIcon = Files.LoadTextureFromDll("inputIcon", 64, 64);
      FsmEditorStyles.LightIcon = Files.LoadTextureFromDll("lightIcon", 64, 64);
      FsmEditorStyles.LogicIcon = Files.LoadTextureFromDll("logicIcon", 64, 64);
      FsmEditorStyles.MathIcon = Files.LoadTextureFromDll("mathIcon", 64, 64);
      FsmEditorStyles.MaterialIcon = Files.LoadTextureFromDll("materialIcon", 64, 64);
      FsmEditorStyles.MeshIcon = Files.LoadTextureFromDll("meshIcon", 64, 64);
      FsmEditorStyles.RectIcon = Files.LoadTextureFromDll("rectIcon", 64, 64);
      FsmEditorStyles.RectTransformIcon = Files.LoadTextureFromDll("rectTransformIcon", 64, 64);
      FsmEditorStyles.RenderIcon = Files.LoadTextureFromDll("renderIcon", 64, 64);
      FsmEditorStyles.RecentIcon = Files.LoadTextureFromDll("recentIcon", 64, 64);
      FsmEditorStyles.SceneIcon = Files.LoadTextureFromDll("sceneIcon", 64, 64);
      FsmEditorStyles.SaveIcon = Files.LoadTextureFromDll("saveIcon", 64, 64);
      FsmEditorStyles.SphereColliderIcon = Files.LoadTextureFromDll("sphereColliderIcon", 64, 64);
      FsmEditorStyles.CircleColliderIcon = Files.LoadTextureFromDll("circleColliderIcon", 64, 64);
      FsmEditorStyles.StopwatchIcon = Files.LoadTextureFromDll("stopwatchIcon", 64, 64);
      FsmEditorStyles.StateMachineIcon = Files.LoadTextureFromDll("playmakerIcon", 64, 64);
      FsmEditorStyles.StringIcon = Files.LoadTextureFromDll("stringIcon", 64, 64);
      FsmEditorStyles.SpriteIcon = Files.LoadTextureFromDll("spriteIcon", 64, 64);
      FsmEditorStyles.SubstanceIcon = Files.LoadTextureFromDll("substanceIcon", 64, 64);
      FsmEditorStyles.TimeIcon = Files.LoadTextureFromDll("timeIcon", 64, 64);
      FsmEditorStyles.TransformIcon = Files.LoadTextureFromDll("transformIcon", 64, 64);
      FsmEditorStyles.QuaternionIcon = Files.LoadTextureFromDll("quaternionIcon", 64, 64);
      FsmEditorStyles.Vector2Icon = Files.LoadTextureFromDll("vector2Icon", 64, 64);
      FsmEditorStyles.Vector3Icon = Files.LoadTextureFromDll("vector3Icon", 64, 64);
      FsmEditorStyles.MovieCamIcon = Files.LoadTextureFromDll("videoIcon", 64, 64);
      FsmEditorStyles.UnityObjectIcon = Files.LoadTextureFromDll("unityObjectIcon", 64, 64);
      FsmEditorStyles.DebugIcon = Files.LoadTextureFromDll("debugIcon", 64, 64);
      FsmEditorStyles.WwwIcon = Files.LoadTextureFromDll("wwwIcon", 64, 64);
      FsmEditorStyles.TweenIcon = Files.LoadTextureFromDll("tweenIcon", 64, 64);
      FsmEditorStyles.TrigIcon = Files.LoadTextureFromDll("trigIcon", 64, 64);
      FsmEditorStyles.PoolIcon = Files.LoadTextureFromDll("poolIcon", 64, 64);
      FsmEditorStyles.GamepadIcon = Files.LoadTextureFromDll("gamepadIcon", 64, 64);
      FsmEditorStyles.gameStateIcons = new Texture2D[5]
      {
        null,
        Files.LoadTexture("playIcon", 64, 64),
        Files.LoadTexture("breakIcon", 64, 64),
        Files.LoadTexture("pauseIcon", 64, 64),
        Files.LoadTexture("errorIcon", 64, 64)
      };
      FsmEditorStyles.NewTag = new GUIStyle((GUIStyle) "Label")
      {
        alignment = TextAnchor.MiddleRight,
        padding = new RectOffset(0, 4, 0, 0),
        richText = true
      };
      FsmEditorStyles.CommentBox = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("infoBox", 16, 16),
          textColor = FsmEditorStyles.graphTextColor
        },
        border = new RectOffset(2, 2, 2, 2),
        padding = new RectOffset(5, 5, 3, 3),
        margin = new RectOffset(3, 3, 3, 3),
        alignment = TextAnchor.UpperLeft,
        wordWrap = true,
        font = EditorStyles.standardFont,
        clipping = TextClipping.Clip
      };
      FsmEditorStyles.Divider = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("divider_flat", 32, 2, UnityEngine.FilterMode.Point)
        },
        border = new RectOffset(1, 1, 2, 0),
        fixedHeight = 2f
      };
      FsmEditorStyles.DividerSequence = new GUIStyle()
      {
        border = new RectOffset(37, 0, 0, 0),
        fixedHeight = 12f
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.DividerSequence, "dividerSequence", 42, 12);
      FsmEditorStyles.InlineButton = new GUIStyle((GUIStyle) "Button")
      {
        margin = new RectOffset(0, 4, 2, 0),
        fixedHeight = EditorGUIUtility.singleLineHeight
      };
      FsmEditorStyles.MiniToggle = new GUIStyle(FsmEditorStyles.InlineButton)
      {
        padding = new RectOffset(0, 0, 0, 0),
        margin = new RectOffset(0, 3, 2, 0),
        fixedWidth = 18f,
        stretchWidth = false
      };
      FsmEditorStyles.MiniButton = new GUIStyle(EditorStyles.miniButton)
      {
        overflow = new RectOffset(0, 0, 0, 2),
        padding = new RectOffset(0, 0, 0, 0),
        margin = new RectOffset(0, 0, 3, 2),
        stretchWidth = false,
        stretchHeight = false
      };
      FsmEditorStyles.MiniButtonPadded = new GUIStyle(EditorStyles.miniButton)
      {
        overflow = new RectOffset(0, 0, 0, 2),
        padding = new RectOffset(0, 0, 0, 0),
        stretchWidth = false,
        stretchHeight = false
      };
      if (FsmEditorStyles.UsingFlatStyle)
      {
        ++FsmEditorStyles.MiniButtonPadded.margin.right;
        FsmEditorStyles.MiniButtonPadded.margin.left = 0;
        FsmEditorStyles.MiniButtonPadded.overflow = new RectOffset();
      }
      FsmEditorStyles.ActionFoldout = new GUIStyle(EditorStyles.foldout)
      {
        fixedWidth = 15f,
        margin = new RectOffset(2, 0, 0, 0)
      };
      FsmEditorStyles.ActionToggle = new GUIStyle(EditorStyles.toggle)
      {
        fixedWidth = 17f,
        margin = new RectOffset(0, 0, 0, 0)
      };
      if (!FsmEditorStyles.UsingFlatStyle)
        FsmEditorStyles.ActionToggle.overflow = new RectOffset(0, 0, -1, 0);
      FsmEditorStyles.ActionTitlebar = new GUIStyle((GUIStyle) "IN Title")
      {
        padding = new RectOffset(2, 0, 4, 0),
        margin = new RectOffset(0, 0, 0, 0),
        fixedHeight = 22f
      };
      FsmEditorStyles.HeaderTitlebar = new GUIStyle(FsmEditorStyles.ActionTitlebar)
      {
        normal = {
          background = (Texture2D) null
        },
        onNormal = {
          background = (Texture2D) null
        },
        active = {
          background = (Texture2D) null
        },
        onActive = {
          background = (Texture2D) null
        }
      };
      FsmEditorStyles.ActionTitle = new GUIStyle(EditorStyles.boldLabel)
      {
        alignment = TextAnchor.UpperLeft,
        padding = new RectOffset(2, 0, 0, 0),
        margin = new RectOffset(0, 0, 0, 0),
        fixedHeight = 20f
      };
      FsmEditorStyles.ActionTitleError = new GUIStyle(FsmEditorStyles.ActionTitle)
      {
        normal = {
          textColor = new Color(0.7f, 0.7f, 0.7f)
        }
      };
      FsmEditorStyles.ActionTitleSelected = new GUIStyle(FsmEditorStyles.ActionTitle)
      {
        normal = {
          textColor = Color.white
        },
        onHover = {
          textColor = Color.white
        },
        hover = {
          textColor = Color.white
        },
        onActive = {
          textColor = Color.white
        },
        active = {
          textColor = Color.white
        },
        onFocused = {
          textColor = Color.white
        },
        focused = {
          textColor = Color.white
        }
      };
      FsmEditorStyles.ActionHeader = new GUIStyle(EditorStyles.largeLabel)
      {
        padding = new RectOffset(0, 0, 0, 0),
        fontSize = 16,
        alignment = TextAnchor.UpperLeft,
        contentOffset = new Vector2(-1f, -3f)
      };
      FsmEditorStyles.ActionHeaderSelected = new GUIStyle(FsmEditorStyles.ActionHeader)
      {
        normal = {
          textColor = Color.white
        }
      };
      FsmEditorStyles.CategoryHeader = new GUIStyle(FsmEditorStyles.ActionHeader)
      {
        padding = new RectOffset(4, 0, 8, 0)
      };
      FsmEditorStyles.CategoryHeaderEditing = new GUIStyle(EditorStyles.textField)
      {
        alignment = TextAnchor.MiddleLeft,
        padding = new RectOffset(3, 0, 0, 0),
        margin = new RectOffset(4, 4, 8, 8),
        fontSize = FsmEditorStyles.CategoryHeader.fontSize,
        fixedHeight = (float) (FsmEditorStyles.CategoryHeader.fontSize + 8)
      };
      FsmEditorStyles.StandardPadding = new GUIStyle()
      {
        padding = new RectOffset(2, 0, 3, 0)
      };
      FsmEditorStyles.SelectedBG = Files.LoadTextureFromDll("selectedColor", 2, 2);
      FsmEditorStyles.SelectedRow = new GUIStyle()
      {
        normal = {
          background = FsmEditorStyles.SelectedBG,
          textColor = Color.white
        },
        padding = new RectOffset(2, 0, 4, 0),
        margin = new RectOffset(0, 0, 0, 0),
        fixedHeight = FsmEditorStyles.ActionTitlebar.fixedHeight
      };
      FsmEditorStyles.CategoryFoldout = new GUIStyle(EditorStyles.foldout);
      FsmEditorStyles.InfoBox = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("infoBox", 16, 16),
          textColor = FsmEditorStyles.LabelTextColor
        },
        border = new RectOffset(2, 2, 2, 2),
        padding = new RectOffset(5, 5, 3, 3),
        margin = new RectOffset(5, 5, 3, 3),
        alignment = TextAnchor.LowerLeft,
        wordWrap = true
      };
      FsmEditorStyles.HintBox = new GUIStyle(FsmEditorStyles.InfoBox)
      {
        normal = {
          background = Files.LoadTextureFromDll("hintBox", 16, 16),
          textColor = Color.white
        }
      };
      FsmEditorStyles.ErrorBox = new GUIStyle(FsmEditorStyles.InfoBox)
      {
        normal = {
          background = Files.LoadTextureFromDll("errorBox", 16, 16),
          textColor = FsmEditorStyles.ErrorTextColor
        }
      };
      FsmEditorStyles.ActionErrorBox = new GUIStyle(FsmEditorStyles.ErrorBox);
      FsmEditorStyles.EventBox = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("transitionBox", 16, 16)
        },
        border = new RectOffset(5, 5, 5, 5),
        padding = new RectOffset(5, 0, 3, 0),
        alignment = TextAnchor.MiddleLeft,
        fixedHeight = 20f
      };
      FsmEditorStyles.SelectedEventBox = new GUIStyle(FsmEditorStyles.EventBox)
      {
        normal = {
          background = FsmEditorStyles.SelectedBG,
          textColor = Color.white
        },
        fixedHeight = 22f
      };
      FsmEditorStyles.TableRowBox = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("tableRowBox", 16, 16)
        },
        border = new RectOffset(5, 5, 5, 5),
        padding = new RectOffset(5, 0, 3, 0),
        alignment = TextAnchor.MiddleLeft,
        fixedHeight = 22f
      };
      FsmEditorStyles.TableRowBoxNoDivider = new GUIStyle()
      {
        border = new RectOffset(5, 5, 5, 5),
        padding = new RectOffset(5, 0, 3, 0),
        alignment = TextAnchor.MiddleLeft,
        fixedHeight = 22f
      };
      FsmEditorStyles.TableRowHeader = new GUIStyle((GUIStyle) "Label")
      {
        alignment = TextAnchor.MiddleLeft,
        margin = new RectOffset(0, 0, 1, 0),
        padding = new RectOffset()
      };
      FsmEditorStyles.TableSectionHeader = new GUIStyle(FsmEditorStyles.TableRowHeader)
      {
        margin = new RectOffset(2, 0, 0, 0),
        padding = new RectOffset(0, 0, 0, 1)
      };
      FsmEditorStyles.VersionInfo = new GUIStyle()
      {
        padding = new RectOffset(5, 5, 0, 0),
        alignment = TextAnchor.LowerRight
      };
      FsmEditorStyles.LogInfo = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("logInfoIcon", 20, 20),
          textColor = EditorStyles.label.normal.textColor
        },
        border = new RectOffset(20, 0, 0, 0),
        padding = new RectOffset(24, 0, 0, 0),
        margin = new RectOffset(3, 3, 0, 0),
        alignment = TextAnchor.MiddleLeft,
        fixedHeight = 20f
      };
      GUIStyle guiStyle1 = new GUIStyle(FsmEditorStyles.LogInfo);
      GUIStyle other = new GUIStyle(FsmEditorStyles.LogInfo)
      {
        normal = {
          background = (Texture2D) null
        },
        fontStyle = FontStyle.Bold
      };
      GUIStyle guiStyle2 = new GUIStyle(other);
      GUIStyle guiStyle3 = new GUIStyle(FsmEditorStyles.LogInfo)
      {
        normal = {
          background = Files.LoadTextureFromDll("logWarningIcon", 20, 20)
        }
      };
      GUIStyle guiStyle4 = new GUIStyle(FsmEditorStyles.LogInfo)
      {
        normal = {
          background = Files.LoadTextureFromDll("logErrorIcon", 20, 20)
        }
      };
      GUIStyle guiStyle5 = new GUIStyle(FsmEditorStyles.LogInfo)
      {
        normal = {
          background = Files.LoadTextureFromDll("logTransitionIcon", 20, 20)
        }
      };
      FsmEditorStyles.logTypeStyles = new GUIStyle[11]
      {
        FsmEditorStyles.LogInfo,
        guiStyle3,
        guiStyle4,
        FsmEditorStyles.LogInfo,
        guiStyle5,
        FsmEditorStyles.LogInfo,
        guiStyle5,
        FsmEditorStyles.LogInfo,
        guiStyle1,
        other,
        guiStyle2
      };
      FsmEditorStyles.ActionCategory = new GUIStyle(EditorStyles.label)
      {
        alignment = TextAnchor.MiddleLeft,
        normal = {
          background = FsmEditorStyles.TextureFromColor(0.7137255f),
          textColor = new Color(0.3f, 0.3f, 0.3f)
        },
        active = {
          background = FsmEditorStyles.TextureFromColor(0.6666667f)
        },
        margin = new RectOffset(4, 4, 6, 6),
        padding = new RectOffset(28, 0, 0, 0),
        fontStyle = FontStyle.Bold,
        fixedHeight = 22f
      };
      FsmEditorStyles.ActionCategory.active.textColor = Color.black;
      FsmEditorStyles.ActionCategory.onNormal = FsmEditorStyles.ActionCategory.active;
      FsmEditorStyles.ActionCategory.onActive = FsmEditorStyles.ActionCategory.active;
      FsmEditorStyles.ActionCategoryIcon = new GUIStyle()
      {
        alignment = TextAnchor.MiddleLeft,
        padding = new RectOffset(7, 1, 2, 2)
      };
      FsmEditorStyles.ActionCategoryCount = new GUIStyle((GUIStyle) "Label")
      {
        alignment = TextAnchor.MiddleRight,
        padding = new RectOffset(0, 4, 0, 0)
      };
      FsmEditorStyles.ActionItem = new GUIStyle()
      {
        normal = {
          textColor = EditorStyles.label.normal.textColor
        },
        padding = new RectOffset(25, 0, 0, 0),
        margin = new RectOffset(3, 3, 0, 0),
        alignment = TextAnchor.UpperLeft,
        fixedHeight = 16f
      };
      FsmEditorStyles.ActionItemSelected = new GUIStyle(FsmEditorStyles.ActionItem)
      {
        normal = {
          background = FsmEditorStyles.SelectedBG,
          textColor = Color.white
        }
      };
      FsmEditorStyles.ErrorLine = new GUIStyle()
      {
        normal = {
          textColor = EditorStyles.label.normal.textColor
        },
        padding = new RectOffset(5, 0, 0, 10),
        margin = new RectOffset(3, 3, 0, 0)
      };
      FsmEditorStyles.ErrorLineSelected = new GUIStyle(FsmEditorStyles.ErrorLine)
      {
        normal = {
          background = FsmEditorStyles.SelectedBG,
          textColor = Color.white
        }
      };
      FsmEditorStyles.ErrorTitle = new GUIStyle(FsmEditorStyles.ErrorLine)
      {
        fontStyle = FontStyle.Bold,
        fixedHeight = 20f
      };
      FsmEditorStyles.ErrorTitleSelected = new GUIStyle(FsmEditorStyles.ErrorLineSelected)
      {
        fontStyle = FontStyle.Bold,
        fixedHeight = 20f
      };
      FsmEditorStyles.ActionLabel = new GUIStyle(EditorStyles.label)
      {
        margin = new RectOffset(3, 4, 0, 0),
        alignment = TextAnchor.UpperLeft,
        fixedHeight = 16f
      };
      FsmEditorStyles.ActionLabelSelected = new GUIStyle(FsmEditorStyles.ActionLabel)
      {
        normal = {
          textColor = Color.white
        }
      };
      FsmEditorStyles.BoldLabel = new GUIStyle(EditorStyles.label)
      {
        fontStyle = FontStyle.Bold
      };
      FsmEditorStyles.BoldLabelSelected = new GUIStyle(FsmEditorStyles.BoldLabel)
      {
        normal = {
          textColor = Color.white
        }
      };
      FsmEditorStyles.LargeLabel = new GUIStyle(EditorStyles.largeLabel)
      {
        alignment = TextAnchor.UpperLeft,
        fontSize = 14,
        fontStyle = FontStyle.Normal
      };
      FsmEditorStyles.LargeLabelSelected = new GUIStyle(FsmEditorStyles.LargeLabel)
      {
        normal = {
          textColor = Color.white
        }
      };
      FsmEditorStyles.TableRow = new GUIStyle()
      {
        normal = {
          textColor = EditorStyles.label.normal.textColor
        },
        padding = new RectOffset(1, 0, 3, 2),
        fixedHeight = 18f
      };
      FsmEditorStyles.TableRowSelected = new GUIStyle(FsmEditorStyles.TableRow)
      {
        normal = {
          background = FsmEditorStyles.SelectedBG,
          textColor = Color.white
        }
      };
      FsmEditorStyles.TableRowText = new GUIStyle((GUIStyle) "Label")
      {
        alignment = TextAnchor.MiddleLeft
      };
      FsmEditorStyles.TableRowTextSelected = new GUIStyle(FsmEditorStyles.TableRowText)
      {
        normal = {
          textColor = Color.white
        }
      };
      FsmEditorStyles.TableRowTextAlignRight = new GUIStyle(FsmEditorStyles.TableRowText)
      {
        alignment = TextAnchor.MiddleRight
      };
      FsmEditorStyles.TableRowTextSelectedAlignRight = new GUIStyle(FsmEditorStyles.TableRowTextSelected)
      {
        alignment = TextAnchor.MiddleRight
      };
      FsmEditorStyles.TableRowCheckBox = new GUIStyle((GUIStyle) "Toggle")
      {
        padding = new RectOffset(0, 0, 0, 0),
        margin = new RectOffset(4, 0, 1, 0)
      };
      FsmEditorStyles.BoldFoldout = new GUIStyle(EditorStyles.foldout)
      {
        fontStyle = FontStyle.Bold
      };
      FsmEditorStyles.LogBackground = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("logEntryBox", 16, 16),
          textColor = Color.white
        }
      };
      FsmEditorStyles.LogLine = new GUIStyle()
      {
        normal = {
          textColor = Color.white
        },
        padding = new RectOffset(1, 0, 3, 2),
        fixedHeight = 18f
      };
      FsmEditorStyles.LogLine2 = new GUIStyle(FsmEditorStyles.LogLine)
      {
        padding = new RectOffset(27, 0, 3, 2)
      };
      FsmEditorStyles.LogLineSelected = new GUIStyle(FsmEditorStyles.LogLine)
      {
        normal = {
          background = FsmEditorStyles.SelectedBG,
          textColor = Color.white
        }
      };
      FsmEditorStyles.LogLineTimeline = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("yellow", 2, 6),
          textColor = Color.white
        },
        fixedHeight = 6f
      };
      FsmEditorStyles.InsertLine = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("pasteDivider", 2, 2),
          textColor = Color.white
        },
        fixedHeight = 2f
      };
      FsmEditorStyles.DefaultWatermark = (Texture) Files.LoadTextureFromDll("playMakerWatermark", 256, 256);
      FsmEditorStyles.Watermark = new GUIStyle()
      {
        alignment = TextAnchor.LowerRight
      };
      FsmEditorStyles.LargeWatermarkText = new GUIStyle(EditorStyles.label)
      {
        alignment = TextAnchor.UpperLeft,
        normal = {
          textColor = new Color(1f, 1f, 1f, 0.1f)
        },
        fontSize = 32,
        fontStyle = FontStyle.Bold
      };
      FsmEditorStyles.SmallWatermarkText = new GUIStyle()
      {
        normal = {
          textColor = new Color(1f, 1f, 1f, 0.15f)
        },
        padding = new RectOffset(5, 0, 0, 0),
        font = EditorStyles.standardFont,
        fontSize = 14,
        fontStyle = FontStyle.Normal,
        wordWrap = true
      };
      FsmEditorStyles.LargeTitleText = new GUIStyle(EditorStyles.label)
      {
        fontSize = 32,
        fontStyle = FontStyle.Bold
      };
      FsmEditorStyles.MediumTitleText = new GUIStyle(EditorStyles.label)
      {
        fontSize = 18,
        fontStyle = FontStyle.Bold,
        alignment = TextAnchor.UpperLeft
      };
      FsmEditorStyles.LargeText = new GUIStyle()
      {
        normal = {
          textColor = Color.white
        },
        fontSize = 32,
        fontStyle = FontStyle.Bold
      };
      FsmEditorStyles.LargeTitleWithLogo = new GUIStyle()
      {
        normal = {
          background = Files.LoadTextureFromDll("wanLarge", 42, 42),
          textColor = Color.white
        },
        border = new RectOffset(42, 0, 0, 0),
        padding = new RectOffset(42, 0, 0, 0),
        margin = new RectOffset(0, 0, 0, 0),
        contentOffset = new Vector2(0.0f, 0.0f),
        alignment = TextAnchor.MiddleLeft,
        fixedHeight = 42f,
        fontSize = 32,
        fontStyle = FontStyle.Bold
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.LargeTitleWithLogo, "wanLarge", 42, 42);
      FsmEditorStyles.PlaymakerHeader = new GUIStyle()
      {
        normal = {
          textColor = Color.white
        },
        border = new RectOffset(253, 0, 0, 0)
      };
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.PlaymakerHeader, "playMakerHeader", 253, 60);
      FsmEditorStyles.WelcomeLink = new GUIStyle()
      {
        normal = {
          textColor = EditorStyles.label.normal.textColor
        },
        border = new RectOffset(64, 0, 0, 0),
        padding = new RectOffset(66, 0, 0, 0),
        margin = new RectOffset(20, 20, 20, 0),
        alignment = TextAnchor.UpperLeft,
        fixedHeight = 64f
      };
      FsmEditorStyles.DocsIcon = (Texture) Files.LoadTextureFromDll("linkDocs", 48, 48);
      FsmEditorStyles.BasicsIcon = (Texture) Files.LoadTextureFromDll("linkBasics", 64, 64);
      FsmEditorStyles.VideoIcon = (Texture) Files.LoadTextureFromDll("linkVideos", 48, 48);
      FsmEditorStyles.ForumIcon = (Texture) Files.LoadTextureFromDll("linkForums", 48, 48);
      FsmEditorStyles.SamplesIcon = (Texture) Files.LoadTextureFromDll("linkSamples", 48, 48);
      FsmEditorStyles.PhotonIcon = (Texture) Files.LoadTextureFromDll("photonIcon", 48, 48);
      FsmEditorStyles.AddonsIcon = (Texture) Files.LoadTextureFromDll("linkAddons", 48, 48);
      FsmEditorStyles.BackButton = (Texture) Files.LoadTextureFromDll("backButton", 123, 24);
      FsmEditorStyles.InlineErrorIcon = new GUIStyle()
      {
        fixedWidth = 16f,
        fixedHeight = 16f,
        padding = new RectOffset(2, 1, 1, 0)
      };
    }

    private static void InitProSkin()
    {
      FsmEditorStyles.SplitLine = FsmEditorStyles.TextureFromColor(0.1019608f);
      FsmEditorStyles.GuiContentErrorColor = new Color(1f, 0.1f, 0.1f);
      FsmEditorStyles.GuiBackgroundErrorColor = new Color(1f, 0.4f, 0.4f);
      FsmEditorStyles.SectionHeader.normal.background = FsmEditorStyles.TextureFromColor(0.2627451f);
      FsmEditorStyles.ActionCategory.normal = FsmEditorStyles.SectionHeader.normal;
      FsmEditorStyles.ActionCategory.active.background = FsmEditorStyles.TextureFromColor(0.3529412f);
      FsmEditorStyles.ActionCategory.active.textColor = Color.white;
      FsmEditorStyles.ActionCategory.onNormal = FsmEditorStyles.ActionCategory.active;
      FsmEditorStyles.ActionCategory.onActive = FsmEditorStyles.ActionCategory.active;
      FsmEditorStyles.HintBox = new GUIStyle(FsmEditorStyles.InfoBox)
      {
        normal = {
          background = Files.LoadTextureFromDll("hintBox", 16, 16),
          textColor = new Color(0.6f, 0.7f, 0.8f)
        }
      };
      FsmEditorStyles.HintBoxTextOnly = new GUIStyle(FsmEditorStyles.HintBox)
      {
        normal = {
          background = (Texture2D) null
        }
      };
    }

    private static void InitIndieSkin()
    {
      FsmEditorStyles.SplitLine = FsmEditorStyles.TextureFromColor(0.5f);
      FsmEditorStyles.ErrorBox.normal.background = FsmEditorStyles.InfoBox.normal.background;
      FsmEditorStyles.ErrorBox.normal.textColor = FsmEditorStyles.ErrorTextColorIndie;
      FsmEditorStyles.ActionErrorBox.normal.textColor = FsmEditorStyles.ErrorTextColorIndie;
      FsmEditorStyles.LoadStyleBackground(FsmEditorStyles.ColorSwatch, "swatchBox_indie", 16, 16);
      FsmEditorStyles.WanTabIcon = Files.LoadTextureFromDll("wanTabIcon_indie", 64, 64);
      FsmEditorStyles.SelectedBG = Files.LoadTextureFromDll("selectedColor_indie", 2, 2);
      FsmEditorStyles.SelectedRow.normal.background = FsmEditorStyles.SelectedBG;
      FsmEditorStyles.ActionItemSelected.normal.background = FsmEditorStyles.SelectedBG;
      FsmEditorStyles.SelectedEventBox.normal.background = FsmEditorStyles.SelectedBG;
      FsmEditorStyles.TableRowSelected.normal.background = FsmEditorStyles.SelectedBG;
      FsmEditorStyles.GuiContentErrorColor = new Color(1f, 0.0f, 0.0f);
      FsmEditorStyles.GuiBackgroundErrorColor = new Color(1f, 0.3f, 0.3f);
      FsmEditorStyles.HintBox = new GUIStyle(FsmEditorStyles.InfoBox)
      {
        normal = {
          background = Files.LoadTextureFromDll("hintBox", 16, 16),
          textColor = new Color(0.9f, 0.95f, 1f)
        }
      };
      FsmEditorStyles.HintBoxTextOnly = new GUIStyle(FsmEditorStyles.HintBox)
      {
        normal = {
          background = (Texture2D) null
        }
      };
    }

    private static GUIStyle GetStyle(string styleName)
    {
      GUIStyle guiStyle = GUI.skin.FindStyle(styleName) ?? EditorGUIUtility.GetBuiltinSkin(EditorSkin.Inspector).FindStyle(styleName);
      if (guiStyle == null)
      {
        UnityEngine.Debug.LogError((object) ("Missing built-in guistyle " + styleName));
        guiStyle = new GUIStyle();
      }
      return guiStyle;
    }

    private static void InitColorScheme(FsmEditorStyles.ColorScheme colorScheme)
    {
      if (colorScheme == FsmEditorStyles.ColorScheme.Default)
        colorScheme = FsmEditorStyles.usingProSkin ? FsmEditorStyles.ColorScheme.DarkBackground : FsmEditorStyles.ColorScheme.LightBackground;
      if (colorScheme != FsmEditorStyles.ColorScheme.DarkBackground)
      {
        if (colorScheme != FsmEditorStyles.ColorScheme.LightBackground)
          throw new ArgumentOutOfRangeException(nameof (colorScheme));
        FsmEditorStyles.LinkColors[0] = new Color(0.25f, 0.25f, 0.25f);
        FsmEditorStyles.Background.normal.background = Files.LoadTextureFromDll("graphBackground_indie", 32, 32);
        FsmEditorStyles.CommentBox.normal.background = Files.LoadTextureFromDll("infoBox_indie", 16, 16);
        FsmEditorStyles.LargeWatermarkText.normal.textColor = new Color(0.0f, 0.0f, 0.0f, 0.2f);
        FsmEditorStyles.SmallWatermarkText.normal.textColor = new Color(0.0f, 0.0f, 0.0f, 0.3f);
        FsmEditorStyles.MinimapViewRectColor = new Color(1f, 1f, 1f, 0.5f);
        FsmEditorStyles.MinimapFrameColor = new Color(0.0f, 0.0f, 0.0f, 0.1f);
        FsmEditorStyles.WatermarkTint = new Color(0.0f, 0.0f, 0.0f, 0.075f);
        FsmEditorStyles.WatermarkTintSolid = new Color(0.2f, 0.2f, 0.2f);
        FsmEditorStyles.HighlightColors = new Color[6]
        {
          new Color(0.0f, 0.0f, 0.0f),
          new Color(0.24f, 0.5f, 0.875f),
          new Color(0.06f, 0.8f, 0.06f),
          new Color(1f, 0.0f, 0.0f),
          new Color(1f, 0.0f, 0.0f),
          new Color(0.8f, 0.8f, 0.0f)
        };
      }
      else
      {
        FsmEditorStyles.LinkColors[0] = new Color(0.1f, 0.1f, 0.1f);
        FsmEditorStyles.LargeWatermarkText.normal.textColor = new Color(1f, 1f, 1f, 0.15f);
        FsmEditorStyles.SmallWatermarkText.normal.textColor = new Color(1f, 1f, 1f, 0.2f);
        FsmEditorStyles.MinimapViewRectColor = new Color(1f, 1f, 1f, 0.3f);
        FsmEditorStyles.MinimapFrameColor = new Color(1f, 1f, 1f, 0.05f);
        FsmEditorStyles.WatermarkTint = new Color(1f, 1f, 1f, 0.05f);
        FsmEditorStyles.WatermarkTintSolid = new Color(0.8f, 0.8f, 0.8f);
        FsmEditorStyles.HighlightColors = new Color[6]
        {
          new Color(0.0f, 0.0f, 0.0f),
          new Color(0.24f, 0.38f, 0.57f),
          new Color(0.06f, 0.8f, 0.06f),
          new Color(1f, 0.0f, 0.0f),
          new Color(1f, 0.0f, 0.0f),
          new Color(0.8f, 0.8f, 0.0f)
        };
      }
    }

    private static void InitFadedColors()
    {
      FsmEditorStyles.FadedColors = new Color[PlayMakerPrefs.Colors.Length];
      for (int index = 0; index < PlayMakerPrefs.Colors.Length; ++index)
        FsmEditorStyles.FadedColors[index] = FsmEditorStyles.BlendColor(PlayMakerPrefs.Colors[index], FsmEditorStyles.GraphBackgroundColor, FsmEditorStyles.linkAlphaFaded);
      FsmEditorStyles.FadedLinkColor = FsmEditorStyles.BlendColor(FsmEditorStyles.LinkColors[0], FsmEditorStyles.GraphBackgroundColor, FsmEditorStyles.linkAlphaFaded);
    }

    private static Color BlendColor(Color color, Color bgColor, float alpha)
    {
      color.r = (float) ((double) color.r * (double) alpha + (double) bgColor.r * (1.0 - (double) alpha));
      color.g = (float) ((double) color.g * (double) alpha + (double) bgColor.g * (1.0 - (double) alpha));
      color.b = (float) ((double) color.b * (double) alpha + (double) bgColor.b * (1.0 - (double) alpha));
      return color;
    }

    public static Texture2D[] GetGameStateIcons() => FsmEditorStyles.gameStateIcons;

    public static GUIStyle[] GetLogTypeStyles() => FsmEditorStyles.logTypeStyles;

    public static void OnDestroy()
    {
    }

    public static void InitScale(float scale)
    {
      FsmEditorStyles.scaleInitialized = false;
      FsmEditorStyles.initScale = scale;
    }

    public static void SetScale(float scale)
    {
      int num1 = Mathf.CeilToInt(Mathf.Clamp(scale * 12f, 4f, 12f));
      int num2 = num1;
      FsmEditorStyles.StateRowHeight = 16f * scale;
      FsmEditorStyles.StateTitleBox.fontSize = num1;
      FsmEditorStyles.StateTitleBox.fixedHeight = FsmEditorStyles.StateRowHeight;
      FsmEditorStyles.TransitionBox.fontSize = num2;
      FsmEditorStyles.TransitionBox.fixedHeight = FsmEditorStyles.StateRowHeight;
      FsmEditorStyles.TransitionBoxSelected.fontSize = num2;
      FsmEditorStyles.TransitionBoxSelected.fixedHeight = FsmEditorStyles.StateRowHeight;
      FsmEditorStyles.GlobalTransitionBox.fontSize = num2;
      FsmEditorStyles.GlobalTransitionBox.fixedHeight = FsmEditorStyles.StateRowHeight;
      FsmEditorStyles.StartTransitionBox.fontSize = num2;
      FsmEditorStyles.StartTransitionBox.fixedHeight = FsmEditorStyles.StateRowHeight;
      FsmEditorStyles.CommentBox.fontSize = num2;
      FsmEditorStyles.CommentBox.padding = new RectOffset((int) (5.0 * (double) scale), (int) (5.0 * (double) scale), (int) (3.0 * (double) scale), (int) (3.0 * (double) scale));
      FsmEditorStyles.CommentNode.fontSize = num2;
      FsmEditorStyles.CommentNode.padding = new RectOffset((int) (5.0 * (double) scale), (int) (5.0 * (double) scale), (int) (5.0 * (double) scale), (int) (5.0 * (double) scale));
      FsmEditorStyles.Scale = scale;
      FsmEditorStyles.scaleInitialized = true;
    }

    [Localizable(false)]
    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("FsmEditorStyles: " + message));

    public enum ColorScheme
    {
      Default,
      DarkBackground,
      LightBackground,
    }
  }
}
