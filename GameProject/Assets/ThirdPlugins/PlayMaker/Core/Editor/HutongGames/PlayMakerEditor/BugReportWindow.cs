// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.BugReportWindow
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class BugReportWindow : BaseEditorWindow
  {
    private WWW www;
    private Vector2 controlsScrollPosition;
    [SerializeField]
    private BugReportWindow.ScoutArea area;
    public static string[] frequencyChoices = new string[3]
    {
      Strings.BugReportWindow_FrequencyChoices_Always,
      Strings.BugReportWindow_FrequencyChoices_Sometimes__but_not_always,
      Strings.BugReportWindow_FrequencyChoices_This_is_the_first_time
    };
    [SerializeField]
    private int frequencyIndex;
    [SerializeField]
    private string description;
    [SerializeField]
    private string extraInfo;
    [SerializeField]
    private string email;
    private bool isValid;
    private string errorString;

    [Localizable(false)]
    private void Reset()
    {
      string newLine = Environment.NewLine;
      this.area = BugReportWindow.ScoutArea.Editor;
      this.frequencyIndex = 0;
      this.description = "";
      this.extraInfo = Strings.BugReportWindow_What_happened + newLine + newLine + newLine + newLine + Strings.BugReportWindow_How_can_we_reproduce_it + newLine + newLine;
      this.email = EditorPrefs.GetString(EditorPrefStrings.UserEmail, "");
      this.Repaint();
    }

    public override void Initialize()
    {
      this.SetTitle(Strings.ProductName);
      this.SetMinSize(new Vector2(500f, 400f));
      this.wantsMouseMove = true;
      this.Reset();
    }

    public override void InitWindowTitle() => this.SetTitle(Strings.ProductName);

    public override void DoGUI()
    {
      this.LargeHeader(Strings.BugReportWindow_Title);
      FsmEditorGUILayout.AutoLabelWidth();
      EditorGUI.BeginChangeCheck();
      this.controlsScrollPosition = EditorGUILayout.BeginScrollView(this.controlsScrollPosition);
      GUILayout.Label(Strings.BugReportWindow_Bug_Title_Label, EditorStyles.boldLabel);
      string str1 = EditorGUILayout.TextField(this.description);
      GUILayout.Label(Strings.BugReportWindow_Bug_Description_Label, EditorStyles.boldLabel);
      string str2 = EditorGUILayout.TextArea(this.extraInfo, FsmEditorStyles.TextAreaWithWordWrap, GUILayout.ExpandHeight(true));
      BugReportWindow.ScoutArea scoutArea = (BugReportWindow.ScoutArea) EditorGUILayout.EnumPopup(Strings.BugReportWindow_Where_does_it_happen, (Enum) this.area);
      int num = EditorGUILayout.Popup(Strings.BugReportWindow_How_often_does_it_happen, this.frequencyIndex, BugReportWindow.frequencyChoices);
      string str3 = EditorGUILayout.TextField(new GUIContent(Strings.BugReportWindow_Your_E_mail, Strings.BugReportWindow_Your_E_mail_Tooltip), this.email);
      EditorGUILayout.EndScrollView();
      FsmEditorGUILayout.Divider();
      GUILayout.BeginHorizontal();
      GUILayout.Label("PlayMaker: " + VersionInfo.AssemblyVersion);
      GUILayout.Label("Unity: " + Application.unityVersion);
      GUILayout.Label("Build Target: " + (object) EditorUserBuildSettings.activeBuildTarget);
      GUILayout.FlexibleSpace();
      GUILayout.EndHorizontal();
      FsmEditorGUILayout.Divider();
      GUILayout.Space(5f);
      GUILayout.BeginHorizontal();
      if (GUILayout.Button(Strings.BugReportWindow_Submit_Button))
      {
        if (!this.isValid)
          EditorUtility.DisplayDialog(Strings.BugReportWindow_Title, this.errorString, Strings.OK);
        else
          this.SubmitBugReportByMail();
        GUIUtility.ExitGUI();
      }
      else
      {
        if (GUILayout.Button(new GUIContent(Strings.Command_Copy, Strings.BugReportWindow_Copy_Tooltip), GUILayout.MaxWidth(100f)))
          this.CopyReportToClipboard();
        if (GUILayout.Button(new GUIContent(Strings.Command_Reset), GUILayout.MaxWidth(100f)))
        {
          GUIUtility.keyboardControl = 0;
          this.Reset();
          GUI.changed = false;
        }
        GUILayout.EndHorizontal();
        GUILayout.Space(10f);
        if (!EditorGUI.EndChangeCheck())
          return;
        Undo.RecordObject((UnityEngine.Object) this, "Edit Bug Report");
        this.description = str1;
        this.extraInfo = str2;
        this.frequencyIndex = num;
        this.area = scoutArea;
        this.email = str3;
        this.UpdateGUI();
        GUIUtility.ExitGUI();
      }
    }

    private void OnFocus() => this.UpdateGUI();

    private void Update()
    {
      if (this.www == null)
        return;
      if (this.www.isDone)
      {
        EditorUtility.ClearProgressBar();
        EditorUtility.DisplayDialog(Strings.ProductName, string.IsNullOrEmpty(this.www.error) ? Strings.BugReportWindow_Success : string.Format(Strings.BugReportWindow_Error, (object) this.www.error), Strings.OK);
        this.www = (WWW) null;
      }
      else
        EditorUtility.DisplayProgressBar(Strings.ProductName, Strings.BugReportWindow_Progress, this.www.uploadProgress);
    }

    private void UpdateGUI() => this.isValid = this.IsValidSetup();

    private bool IsValidSetup()
    {
      this.errorString = "";
      if (string.IsNullOrEmpty(this.description))
        this.errorString += Strings.BugReportWindow_MissingTitle;
      if (string.IsNullOrEmpty(this.extraInfo))
        this.errorString += Strings.BugReportWindow_MissingDescription;
      if (string.IsNullOrEmpty(this.email))
        this.errorString += Strings.BugReportWindow_MissingEmail;
      return this.errorString == "";
    }

    [Localizable(false)]
    public void SubmitBugzScoutReport()
    {
      WWWForm form = new WWWForm();
      form.AddField("Description", this.description);
      form.AddField("ScoutProject", "playMaker");
      form.AddField("ScoutUsername", "Test User");
      form.AddField("ScoutArea", this.area.ToString());
      form.AddField("Extra", this.BuildReportBody());
      if (!string.IsNullOrEmpty(this.email))
      {
        form.AddField("Email", this.email);
        EditorPrefs.SetString("PlayMaker.UserEmail", this.email);
      }
      this.www = new WWW("http://hutonggames.com/scoutSubmit.php", form);
    }

    [Localizable(false)]
    public void SubmitBugReportByMail()
    {
      WWWForm form = new WWWForm();
      form.AddField("object", this.description);
      form.AddField("text", this.BuildReportBody());
      form.AddField("email", this.email);
      EditorPrefs.SetString(EditorPrefStrings.BugReportWindow_UserEmail, this.email);
      this.www = new WWW("www.hutonggames.com/SubmitBug.php", form);
    }

    [Localizable(false)]
    private string BuildReportBody(bool offline = false)
    {
      string str1 = "";
      string newLine = Environment.NewLine;
      if (offline)
        str1 = str1 + this.description + newLine;
      string str2 = str1 + "Area: " + (object) this.area + newLine + "Frequency: " + BugReportWindow.frequencyChoices[this.frequencyIndex] + newLine + newLine + this.extraInfo + newLine;
      if (offline)
        str2 = str2 + this.email + newLine;
      return str2 + newLine + "Unity Info:" + newLine + "Unity Version: " + Application.unityVersion + newLine + "Playmaker Version: " + VersionInfo.GetAssemblyInformationalVersion() + newLine + "BuildTarget: " + (object) EditorUserBuildSettings.activeBuildTarget + newLine + newLine + "System Info:" + newLine + "OS: " + SystemInfo.operatingSystem + newLine + "Processor: " + SystemInfo.processorType + newLine + "System Memory: " + (object) SystemInfo.systemMemorySize + newLine + "Graphics Device: " + SystemInfo.graphicsDeviceName + newLine;
    }

    private void CopyReportToClipboard() => EditorGUIUtility.systemCopyBuffer = this.BuildReportBody(true);

    private enum ScoutArea
    {
      Editor,
      Runtime,
      Actions,
      API,
      Documentation,
    }
  }
}
