// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.EnumDropdown
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class EnumDropdown : AdvancedDropdown
  {
    private readonly System.Type enumType;
    private readonly Action<int> onSelectionMade;

    public static void ShowEnumDropdown(Rect rect, System.Type enumType, Action<int> onSelectionMade) => new EnumDropdown(new AdvancedDropdownState(), enumType, onSelectionMade).Show(rect);

    private EnumDropdown(AdvancedDropdownState state, System.Type enumType, Action<int> onSelectionMade)
      : base(state)
    {
      this.enumType = enumType;
      this.onSelectionMade = onSelectionMade;
    }

    protected override AdvancedDropdownItem BuildRoot()
    {
      EnumData cachedEnumData = EnumDataUtility.GetCachedEnumData(this.enumType);
      AdvancedDropdownItem advancedDropdownItem = new AdvancedDropdownItem(this.enumType.Name);
      string[] displayNames = cachedEnumData.displayNames;
      for (int index = 0; index < displayNames.Length; ++index)
        advancedDropdownItem.AddChild(new AdvancedDropdownItem(displayNames[index])
        {
          id = index
        });
      return advancedDropdownItem;
    }

    protected override void ItemSelected(AdvancedDropdownItem item)
    {
      this.onSelectionMade(item.id);
      FsmEditor.SaveActions();
      FsmEditor.Repaint(true);
    }
  }
}
