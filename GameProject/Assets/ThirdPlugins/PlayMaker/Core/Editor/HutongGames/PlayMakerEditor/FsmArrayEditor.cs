// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmArrayEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class FsmArrayEditor : FsmVariableEditor
  {
    private readonly FsmArray fsmArray;
    private string sourceArrayPath;
    private SerializedProperty sourceArrayProp;
    private SerializedProperty arraySizeProp;
    private VariableTypeMenu _typeMenu;
    private Vector2 scrollPos;
    private Rect tempRectValue;
    private Quaternion tempQuatValue;

    private VariableTypeMenu typeMenu => this._typeMenu ?? (this._typeMenu = new VariableTypeMenu());

    public FsmArrayEditor(FsmVariable variable)
      : base(variable)
    {
      this.fsmArray = variable.NamedVar as FsmArray;
      this.valueProp = this.serializedOwner.FindProperty(variable.PropertyPath);
      this.Init();
    }

    private void Init()
    {
      this.sourceArrayPath = this.GetSourceArrayPropertyPath() + ".Array";
      this.sourceArrayProp = this.FindProperty(this.sourceArrayPath);
      this.arraySizeProp = this.FindProperty(this.sourceArrayPath + ".size");
      this.RefreshHeight();
    }

    private void RefreshHeight()
    {
      if (this.sourceArrayProp == null)
        return;
      bool isExpanded = this.sourceArrayProp.isExpanded;
      this.sourceArrayProp.isExpanded = true;
      this.sourceArrayProp.isExpanded = isExpanded;
    }

    private string GetSourceArrayPropertyPath()
    {
      switch (this.fsmArray.ElementType)
      {
        case VariableType.Unknown:
          return (string) null;
        case VariableType.Float:
          return "floatValues";
        case VariableType.Int:
        case VariableType.Enum:
          return "intValues";
        case VariableType.Bool:
          return "boolValues";
        case VariableType.GameObject:
        case VariableType.Material:
        case VariableType.Texture:
        case VariableType.Object:
          return "objectReferences";
        case VariableType.String:
          return "stringValues";
        case VariableType.Vector2:
        case VariableType.Vector3:
        case VariableType.Color:
        case VariableType.Rect:
        case VariableType.Quaternion:
          return "vector4Values";
        case VariableType.Array:
          return (string) null;
        default:
          Debug.LogError((object) this.fsmArray.VariableType);
          throw new ArgumentOutOfRangeException();
      }
    }

    public override void DoValueField(GUIContent label, bool isAsset)
    {
      bool flag = label != null;
      if (this.sourceArrayProp == null)
        return;
      this.sourceArrayProp.isExpanded = !flag || EditorGUILayout.Foldout(this.sourceArrayProp.isExpanded, label);
      if (!this.sourceArrayProp.isExpanded)
        return;
      if (flag)
        ++EditorGUI.indentLevel;
      EditorGUI.BeginChangeCheck();
      EditorGUILayout.PropertyField(this.arraySizeProp);
      if (!FsmVariableEditor.UnityInspectorMode)
        this.scrollPos = EditorGUILayout.BeginScrollView(this.scrollPos, GUILayout.ExpandHeight(false));
      this.EditArrayItems(isAsset);
      if (!FsmVariableEditor.UnityInspectorMode)
        EditorGUILayout.EndScrollView();
      if (flag)
        --EditorGUI.indentLevel;
      if (!EditorGUI.EndChangeCheck())
        return;
      this.RefreshHeight();
    }

    private void EditArrayItems(bool isAsset)
    {
      for (int index = 0; index < this.arraySizeProp.intValue; ++index)
      {
        GUIContent label = FsmEditorContent.TempContent("Element " + (object) index);
        SerializedProperty propertyRelative = this.sourceArrayProp.FindPropertyRelative("data[" + (object) index + "]");
        switch (this.fsmArray.ElementType)
        {
          case VariableType.Unknown:
          case VariableType.Array:
            continue;
          case VariableType.Float:
          case VariableType.Int:
          case VariableType.Bool:
          case VariableType.String:
            EditorGUILayout.PropertyField(propertyRelative, label);
            continue;
          case VariableType.GameObject:
          case VariableType.Material:
          case VariableType.Texture:
          case VariableType.Object:
            GUIHelpers.ObjectPropertyField(propertyRelative, label, propertyRelative.objectReferenceValue, this.fsmArray.ObjectType ?? typeof (UnityEngine.Object), !isAsset);
            continue;
          case VariableType.Vector2:
            Rect vectorRect1 = GUIHelpers.GetVectorRect();
            EditorGUI.BeginProperty(vectorRect1, label, propertyRelative);
            propertyRelative.vector4Value = (Vector4) EditorGUI.Vector2Field(vectorRect1, label, (Vector2) propertyRelative.vector4Value);
            EditorGUI.EndProperty();
            continue;
          case VariableType.Vector3:
            Rect vectorRect2 = GUIHelpers.GetVectorRect();
            EditorGUI.BeginProperty(vectorRect2, label, propertyRelative);
            propertyRelative.vector4Value = (Vector4) EditorGUI.Vector3Field(vectorRect2, label, (Vector3) propertyRelative.vector4Value);
            EditorGUI.EndProperty();
            continue;
          case VariableType.Color:
            Rect singleLineRect = GUIHelpers.GetSingleLineRect();
            EditorGUI.BeginProperty(singleLineRect, label, propertyRelative);
            propertyRelative.vector4Value = (Vector4) EditorGUI.ColorField(singleLineRect, label, (Color) propertyRelative.vector4Value);
            EditorGUI.EndProperty();
            continue;
          case VariableType.Rect:
            Rect vector4Rect = GUIHelpers.GetVector4Rect();
            EditorGUI.BeginProperty(vector4Rect, label, propertyRelative);
            this.tempRectValue.Set(propertyRelative.vector4Value.x, propertyRelative.vector4Value.y, propertyRelative.vector4Value.z, propertyRelative.vector4Value.w);
            this.tempRectValue = EditorGUI.RectField(vector4Rect, label, this.tempRectValue);
            propertyRelative.vector4Value = new Vector4(this.tempRectValue.x, this.tempRectValue.y, this.tempRectValue.width, this.tempRectValue.height);
            EditorGUI.EndProperty();
            continue;
          case VariableType.Quaternion:
            Rect vectorRect3 = GUIHelpers.GetVectorRect();
            this.tempQuatValue.Set(propertyRelative.vector4Value.x, propertyRelative.vector4Value.y, propertyRelative.vector4Value.z, propertyRelative.vector4Value.w);
            Vector3 eulerAngles = this.tempQuatValue.eulerAngles;
            EditorGUI.BeginProperty(vectorRect3, label, propertyRelative);
            EditorGUI.BeginChangeCheck();
            Vector3 euler = EditorGUI.Vector3Field(vectorRect3, label, eulerAngles);
            if (EditorGUI.EndChangeCheck())
            {
              this.tempQuatValue = Quaternion.Euler(euler);
              propertyRelative.vector4Value = new Vector4(this.tempQuatValue.x, this.tempQuatValue.y, this.tempQuatValue.z, this.tempQuatValue.w);
            }
            EditorGUI.EndProperty();
            continue;
          case VariableType.Enum:
            GUIHelpers.EnumFromIntPropertyField(propertyRelative, label, this.fsmArray.ObjectType ?? typeof (HutongGames.PlayMaker.None));
            continue;
          default:
            throw new ArgumentOutOfRangeException();
        }
      }
    }

    public override void DoTypeSelector()
    {
      string text = this.fsmArray.ElementType.ToString();
      GUILayout.BeginHorizontal();
      EditorGUILayout.PrefixLabel("Array Type");
      if (GUILayout.Button(text, EditorStyles.popup))
        this.typeMenu.ShowArrayTypeMenu(this.fsmVariable, new GenericMenu.MenuFunction2(this.SelectArrayType));
      GUILayout.EndHorizontal();
      if (this.fsmArray.ElementType == VariableType.Object)
      {
        GUILayout.BeginHorizontal();
        EditorGUILayout.PrefixLabel("Object Type");
        if (GUILayout.Button(FsmEditorContent.TempContent(this.fsmVariable.TypeNameShort, this.fsmVariable.TypeName), EditorStyles.popup))
          VariableTypeMenu.ShowObjectTypesMenu(this.fsmVariable, new GenericMenu.MenuFunction2(this.SelectArrayType));
        GUILayout.EndHorizontal();
      }
      else if (this.fsmArray.ElementType == VariableType.Enum)
      {
        GUILayout.BeginHorizontal();
        GUILayout.Label("Enum Type", GUILayout.Width(96f));
        if (GUILayout.Button(FsmEditorContent.TempContent(this.fsmVariable.TypeNameShort, this.fsmVariable.TypeName), EditorStyles.popup))
          VariableTypeMenu.ShowEnumTypesMenu(this.fsmVariable, new GenericMenu.MenuFunction2(this.SelectArrayType));
        GUILayout.EndHorizontal();
      }
      else
      {
        if (this.fsmArray.ElementType != VariableType.Array)
          return;
        EditorGUILayout.HelpBox("Nested Arrays are not supported yet!", MessageType.Error);
        this.fsmArray.Resize(0);
      }
    }

    private void SelectArrayType(object userdata)
    {
      VariableTypeMenu.MenuItemSelectionData itemSelectionData = (VariableTypeMenu.MenuItemSelectionData) userdata;
      EditorCommands.ChangeArrayType(this.fsmVariable, itemSelectionData.variableType, itemSelectionData.objectType);
      Keyboard.ResetFocus();
      this.Init();
    }
  }
}
