// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmLogger
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class FsmLogger : BaseEditorWindow
  {
    private static FsmLogger instance;
    private Vector2 scrollPosition;
    private float scrollViewHeight;
    private Rect selectedRect;
    private bool autoScroll;
    private FsmState currentState;
    private int numEntriesDrawn;
    private readonly List<float> entryHeights = new List<float>();
    private int firstVisibleEntry;
    private int lastVisibleEntry;
    private float prevWindowHeight;
    private bool updateVisibility;
    private FsmLogEntry prevEntry;
    private FsmLogEntry beforeSelected;
    private FsmLogEntry afterSelected;
    private Rect bgRect;

    private static FsmLog selectedLog => DebugFlow.SelectedLog;

    private FsmLogEntry selectedEntry => DebugFlow.SelectedLogEntry;

    private int selectedEntryIndex => DebugFlow.SelectedLogEntryIndex;

    public static void ResetView()
    {
      if (!((UnityEngine.Object) FsmLogger.instance != (UnityEngine.Object) null))
        return;
      FsmLogger.instance.entryHeights.Clear();
      FsmLogger.instance.firstVisibleEntry = 0;
      FsmLogger.instance.lastVisibleEntry = 0;
      FsmLogger.instance.updateVisibility = true;
      if (!EditorApplication.isPlayingOrWillChangePlaymode)
        return;
      if (EditorApplication.isPaused)
        DebugFlow.Refresh();
      else
        FsmLogger.instance.ScrollToLatest();
    }

    public static void SetDebugFlowTime(float time)
    {
      if (!((UnityEngine.Object) FsmLogger.instance != (UnityEngine.Object) null))
        return;
      FsmLogger.instance.updateVisibility = true;
      FsmLogger.instance.autoScroll = true;
    }

    public override void InitWindowTitle() => this.SetTitle(Strings.FsmLogger_Title);

    public override void Initialize()
    {
      this.isToolWindow = true;
      FsmLogger.instance = this;
      this.minSize = new Vector2(200f, 200f);
      DebugFlow.SyncFsmLog(FsmEditor.SelectedFsm);
    }

    private void OnDisable()
    {
      if (!((UnityEngine.Object) FsmLogger.instance == (UnityEngine.Object) this))
        return;
      FsmLogger.instance = (FsmLogger) null;
    }

    protected override void DoUpdateHighlightIdentifiers()
    {
      Rect position = this.position;
      double num1 = (double) position.width - 10.0;
      position = this.position;
      double num2 = (double) position.height - 40.0;
      HighlighterHelper.FromPosition(5f, 20f, (float) num1, (float) num2, "Log Entries");
    }

    public override void DoGUI()
    {
      this.DoMainToolbar();
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box(Strings.Hint_FsmLog, FsmEditorStyles.HintBox);
      if (!FsmEditorSettings.EnableLogging)
      {
        FsmLogger.DoDisabledGUI();
      }
      else
      {
        GUI.enabled = FsmLogger.selectedLog != null;
        this.HandleKeyboardInput();
        this.DoLogView();
        this.DoBottomToolbar();
      }
    }

    private void Update()
    {
      if (FsmLogger.selectedLog == null || FsmLogger.selectedLog.Entries == null)
        return;
      this.HandleWindowResize();
      this.UpdateLayoutInfo();
      if (this.updateVisibility)
      {
        this.UpdateVisibility();
        this.Repaint();
      }
      this.DoAutoScroll();
    }

    private void HandleWindowResize()
    {
      if ((double) this.prevWindowHeight == 0.0)
      {
        this.prevWindowHeight = this.position.height;
      }
      else
      {
        if ((double) this.prevWindowHeight != (double) this.position.height)
          this.UpdateVisibility();
        this.prevWindowHeight = this.position.height;
      }
    }

    private static void DoDisabledGUI()
    {
      GUILayout.Label(Strings.Label_Logging_is_disabled_in_Preferences);
      if (!GUILayout.Button(Strings.Action_Enable_Logging))
        return;
      FsmEditorSettings.EnableLogging = true;
      FsmEditorSettings.SaveSettings();
    }

    private void DrawBackground()
    {
      if (Event.current.type != UnityEngine.EventType.Repaint)
        return;
      ref Rect local = ref this.bgRect;
      double num1 = (double) EditorGUIUtility.singleLineHeight + 1.0;
      Rect position = this.position;
      double width = (double) position.width;
      position = this.position;
      double num2 = (double) position.height - (double) EditorGUIUtility.singleLineHeight * 2.0;
      local.Set(0.0f, (float) num1, (float) width, (float) num2);
      Color color = GUI.color;
      GUI.color = Color.white;
      FsmEditorStyles.Background.Draw(this.bgRect, GUIContent.none, false, false, false, false);
      GUI.color = color;
    }

    private void DrawInnerGlow()
    {
      if (Event.current.type != UnityEngine.EventType.Repaint)
        return;
      Color color = GUI.color;
      DrawState drawState = FsmDrawState.GetDrawState(FsmEditor.SelectedFsm);
      GUI.color = FsmEditorStyles.HighlightColors[(int) drawState];
      FsmEditorStyles.InnerGlowBox.Draw(this.bgRect, false, false, false, false);
      GUI.color = color;
    }

    private void DoLogView()
    {
      if (FsmLogger.selectedLog == null || FsmLogger.selectedLog.Entries == null || FsmLogger.selectedLog.Entries.Count == 0)
      {
        GUILayout.FlexibleSpace();
      }
      else
      {
        this.currentState = (FsmState) null;
        this.numEntriesDrawn = 0;
        Vector2 vector2 = GUILayout.BeginScrollView(this.scrollPosition, false, true);
        if (vector2 != this.scrollPosition)
        {
          this.scrollPosition = vector2;
          this.updateVisibility = true;
        }
        GUIHelpers.BeginGuiColor(Color.white);
        if (FsmLogger.selectedLog != null && FsmLogger.selectedLog.Entries != null && FsmLogger.selectedLog.Entries.Count > 0)
        {
          GUILayout.Space(this.GetEntryPosition(this.firstVisibleEntry));
          if (this.lastVisibleEntry > FsmLogger.selectedLog.Entries.Count)
            this.lastVisibleEntry = FsmLogger.selectedLog.Entries.Count;
          for (int firstVisibleEntry = this.firstVisibleEntry; firstVisibleEntry < this.lastVisibleEntry; ++firstVisibleEntry)
          {
            FsmLogEntry entry = FsmLogger.selectedLog.Entries[firstVisibleEntry];
            this.DoLogLine(entry, firstVisibleEntry);
            if (this.numEntriesDrawn > 0 && this.eventType == UnityEngine.EventType.Repaint && entry == this.selectedEntry)
            {
              this.selectedRect = GUILayoutUtility.GetLastRect();
              this.selectedRect.y -= this.scrollPosition.y;
            }
          }
          if (this.lastVisibleEntry < this.entryHeights.Count)
            GUILayout.Space(this.GetEntryPosition(this.entryHeights.Count) - this.entryHeights[this.lastVisibleEntry]);
          GUILayout.Space(20f);
        }
        GUIHelpers.EndGuiColor();
        GUILayout.EndScrollView();
        if (this.eventType != UnityEngine.EventType.Repaint)
          return;
        this.scrollViewHeight = GUILayoutUtility.GetLastRect().height;
      }
    }

    private void DoAutoScroll()
    {
      if (this.autoScroll)
      {
        float num = this.position.height / 3f;
        if ((double) this.selectedRect.y - (double) num < 0.0)
        {
          this.scrollPosition.y += this.selectedRect.y - num;
          this.Repaint();
        }
        else if ((double) this.selectedRect.y + (double) this.selectedRect.height + (double) num > (double) this.scrollViewHeight)
        {
          this.scrollPosition.y += this.selectedRect.y + this.selectedRect.height - this.scrollViewHeight + num;
          this.Repaint();
        }
        this.autoScroll = false;
      }
      if (EditorApplication.isPaused || !this.ScrollViewIsAtBottom())
        return;
      this.ScrollToLatest();
    }

    [Localizable(false)]
    private void DoLogLine(FsmLogEntry entry, int index)
    {
      if (!this.EntryIsVisible(entry))
        return;
      if (entry.LogType == FsmLogType.EnterState)
      {
        this.currentState = entry.State;
        FsmEditorGUILayout.Divider();
      }
      GUIHelpers.BeginGuiColor((this.selectedEntry == null ? 0 : (index > this.selectedEntryIndex ? 1 : 0)) != 0 ? new Color(1f, 1f, 1f, 0.3f) : GUI.color);
      if (entry.LogType == FsmLogType.Start || entry.LogType == FsmLogType.Stop)
        GUIHelpers.BeginGuiBackgroundColor(FsmEditorStyles.DefaultBackgroundColor);
      else
        GUIHelpers.BeginGuiBackgroundColor(this.currentState != null ? PlayMakerPrefs.Colors[this.currentState.ColorIndex] : Color.grey);
      GUILayout.BeginVertical(FsmEditorStyles.LogBackground);
      GUIStyle logLine = FsmEditorStyles.LogLine;
      Rect rect1 = GUILayoutUtility.GetRect(GUIContent.none, logLine);
      Rect rect2 = new Rect(rect1) { width = 20f };
      rect1.x += 20f;
      if (Event.current.type == UnityEngine.EventType.Repaint)
        FsmEditorStyles.GetLogTypeStyles()[(int) entry.LogType].Draw(rect2, Color.white);
      if (GUI.Button(rect1, FsmEditorSettings.LogShowTimecode ? entry.TextWithTimecode : entry.Text, logLine))
        this.SelectLogEntry(entry);
      if (this.ShowSentBy(entry))
      {
        if (string.IsNullOrEmpty(entry.Text2))
        {
          entry.Text2 = Strings.FsmLog_Label_Sent_By + Labels.GetFullStateLabel(entry.SentByState);
          if (entry.Action != null)
          {
            FsmLogEntry fsmLogEntry = entry;
            fsmLogEntry.Text2 = fsmLogEntry.Text2 + " : " + Labels.GetActionLabel(entry.Action);
          }
        }
        if (GUILayout.Button(entry.Text2, FsmEditorStyles.LogLine2))
          FsmLogger.OnClickSentBy(entry);
        EditorGUIUtility.AddCursorRect(GUILayoutUtility.GetLastRect(), MouseCursor.Link);
      }
      else if (this.ShowEventTarget(entry))
      {
        if (string.IsNullOrEmpty(entry.Text2))
          entry.Text2 = Strings.FsmLog_Label_Target + FsmLogger.GetEventTargetLabel(entry);
        if (GUILayout.Button(entry.Text2, FsmEditorStyles.LogLine2))
        {
          this.OnClickEventTarget(entry);
          GUIUtility.ExitGUI();
          return;
        }
        EditorGUIUtility.AddCursorRect(GUILayoutUtility.GetLastRect(), MouseCursor.Link);
      }
      else if (this.ShowHitGameObject(entry))
      {
        if (string.IsNullOrEmpty(entry.Text2))
        {
          entry.Text2 = "WITH: " + entry.GameObjectName;
          entry.GameObjectIcon = EditorHacks.GetIconForObject(entry.GameObject);
        }
        if ((UnityEngine.Object) entry.GameObject != (UnityEngine.Object) null)
        {
          if (GUILayout.Button(entry.Text2, FsmEditorStyles.LogLine2))
          {
            Selection.activeGameObject = entry.GameObject;
            GUIUtility.ExitGUI();
            return;
          }
          Rect lastRect = GUILayoutUtility.GetLastRect();
          EditorGUIUtility.AddCursorRect(lastRect, MouseCursor.Link);
          if ((UnityEngine.Object) entry.GameObjectIcon != (UnityEngine.Object) null)
          {
            lastRect.Set(lastRect.xMin, lastRect.yMin + 2f, 27f, lastRect.height - 2f);
            GUI.Label(lastRect, entry.GameObjectIcon);
          }
        }
        else
          GUILayout.Label(entry.Text2 + " (Destroyed)", FsmEditorStyles.LogLine2);
      }
      GUILayout.EndVertical();
      if (entry == this.selectedEntry)
      {
        this.beforeSelected = this.prevEntry;
        GUI.backgroundColor = Color.white;
        GUILayout.Box(GUIContent.none, FsmEditorStyles.LogLineTimeline);
      }
      if (this.prevEntry == this.selectedEntry)
        this.afterSelected = entry;
      this.prevEntry = entry;
      ++this.numEntriesDrawn;
      GUIHelpers.EndGuiBackgroundColor();
      GUIHelpers.EndGuiColor();
    }

    private void DoBottomToolbar()
    {
      GUI.enabled = true;
      GUILayout.BeginHorizontal(FsmEditorStyles.ToolbarBottom);
      GUILayout.Space(4f);
      if (GUILayout.Button(Strings.FsmLog_Clear, EditorStyles.toolbarButton, GUILayout.MinWidth(100f)))
        this.ClearLog();
      HighlighterHelper.FromGUILayout("Clear");
      GUILayout.FlexibleSpace();
      GUILayout.EndHorizontal();
    }

    private void HandleKeyboardInput()
    {
      this.prevEntry = (FsmLogEntry) null;
      if (Event.current.GetTypeForControl(GUIUtility.GetControlID(FocusType.Keyboard)) != UnityEngine.EventType.KeyDown)
        return;
      switch (Event.current.keyCode)
      {
        case KeyCode.UpArrow:
          Event.current.Use();
          this.SelectPreviousLogEntry();
          GUIUtility.ExitGUI();
          break;
        case KeyCode.DownArrow:
          Event.current.Use();
          this.SelectNextLogEntry();
          GUIUtility.ExitGUI();
          break;
      }
    }

    private void SelectPreviousLogEntry()
    {
      if (this.selectedEntry == null || this.beforeSelected == null)
        return;
      this.SelectLogEntry(this.beforeSelected);
      if ((double) this.selectedRect.y >= (double) this.position.height / 2.0)
        return;
      this.autoScroll = true;
    }

    private void SelectNextLogEntry()
    {
      if (this.selectedEntry == null || this.afterSelected == null)
        return;
      this.SelectLogEntry(this.afterSelected);
      if ((double) this.selectedRect.y <= (double) this.position.height / 2.0)
        return;
      this.autoScroll = true;
    }

    private void DoMainToolbar()
    {
      GUILayout.BeginHorizontal(FsmEditorStyles.Toolbar);
      GUILayout.Space(4f);
      string fullFsmLabel = Labels.GetFullFsmLabel(FsmEditor.SelectedFsm);
      Rect rect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.toolbarPopup);
      if (GUI.Button(rect, fullFsmLabel, EditorStyles.toolbarPopup))
        FsmEditorGUILayout.GenerateFsmSelectionMenu(false, false).DropDown(rect);
      HighlighterHelper.FromGUILayout("FSM Selection");
      if (FsmEditorGUILayout.ToolbarSettingsButton())
        FsmLogger.GenerateSettingsMenu().ShowAsContext();
      GUILayout.EndHorizontal();
    }

    private static GenericMenu GenerateSettingsMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent(Strings.Menu_FsmLog_Show_TimeCode), FsmEditorSettings.LogShowTimecode, new GenericMenu.MenuFunction(FsmLogger.ToggleShowTimecode));
      genericMenu.AddItem(new GUIContent(Strings.Menu_FsmLog_Show_Sent_By), FsmEditorSettings.LogShowSentBy, new GenericMenu.MenuFunction(FsmLogger.ToggleShowSentBy));
      genericMenu.AddItem(new GUIContent(Strings.Menu_FsmLog_Show_State_Exit), FsmEditorSettings.LogShowExit, new GenericMenu.MenuFunction(FsmLogger.ToggleShowExit));
      return genericMenu;
    }

    private static void ToggleShowTimecode()
    {
      FsmEditorSettings.LogShowTimecode = !FsmEditorSettings.LogShowTimecode;
      FsmEditorSettings.SaveSettings();
    }

    private static void ToggleShowSentBy()
    {
      FsmEditorSettings.LogShowSentBy = !FsmEditorSettings.LogShowSentBy;
      FsmEditorSettings.SaveSettings();
      FsmLogger.instance.ResetLayout();
    }

    private static void ToggleShowExit()
    {
      FsmEditorSettings.LogShowExit = !FsmEditorSettings.LogShowExit;
      FsmEditorSettings.SaveSettings();
      FsmLogger.instance.ResetLayout();
    }

    private static void OnClickSentBy(FsmLogEntry entry)
    {
      if (entry.SentByState == null)
        return;
      FsmEditor.SelectFsm(entry.SentByState.Fsm);
      FsmEditor.SelectState(entry.SentByState, true);
      FsmEditor.SelectAction(entry.Action);
    }

    [Localizable(false)]
    private static string GetEventTargetLabel(FsmLogEntry entry)
    {
      FsmEventTarget eventTarget = entry.EventTarget;
      switch (eventTarget.target)
      {
        case FsmEventTarget.EventTarget.Self:
          return (string) null;
        case FsmEventTarget.EventTarget.GameObject:
          GameObject ownerDefaultTarget1 = FsmEditor.SelectedFsm.GetOwnerDefaultTarget(eventTarget.gameObject);
          return !((UnityEngine.Object) ownerDefaultTarget1 != (UnityEngine.Object) null) ? " GameObject: None" : " GameObject: " + ownerDefaultTarget1.name;
        case FsmEventTarget.EventTarget.GameObjectFSM:
          GameObject ownerDefaultTarget2 = FsmEditor.SelectedFsm.GetOwnerDefaultTarget(eventTarget.gameObject);
          if (!((UnityEngine.Object) ownerDefaultTarget2 != (UnityEngine.Object) null))
            return " GameObjectFSM: None";
          return " GameObjectFSM: " + ownerDefaultTarget2.name + " " + (object) eventTarget.fsmName;
        case FsmEventTarget.EventTarget.FSMComponent:
          return !((UnityEngine.Object) eventTarget.fsmComponent != (UnityEngine.Object) null) ? " FsmComponent: None" : " FsmComponent: " + Labels.GetFullFsmLabel(eventTarget.fsmComponent.Fsm);
        case FsmEventTarget.EventTarget.BroadcastAll:
          return " BroadcastAll";
        case FsmEventTarget.EventTarget.HostFSM:
          return " Host: " + entry.State.Fsm.Host.Name;
        case FsmEventTarget.EventTarget.SubFSMs:
          return " SubFSMs";
        default:
          return (string) null;
      }
    }

    private void OnClickEventTarget(FsmLogEntry entry)
    {
      switch (entry.EventTarget.target)
      {
        case FsmEventTarget.EventTarget.GameObject:
          if (entry.Event == null)
            break;
          GenericMenu genericMenu1 = new GenericMenu();
          foreach (Fsm fsm in FsmInfo.GetFsmList(FsmInfo.FindTransitionsUsingEvent(entry.Event.Name)))
            genericMenu1.AddItem(new GUIContent(Labels.GetFullFsmLabel(fsm)), false, new GenericMenu.MenuFunction2(FsmEditor.SelectFsm), (object) fsm);
          genericMenu1.ShowAsContext();
          break;
        case FsmEventTarget.EventTarget.GameObjectFSM:
          Fsm fsmOnGameObject = FsmSelection.FindFsmOnGameObject(FsmEditor.SelectedFsm.GetOwnerDefaultTarget(entry.EventTarget.gameObject), entry.EventTarget.fsmName.Value);
          if (fsmOnGameObject == null)
            break;
          FsmEditor.SelectFsm(fsmOnGameObject);
          break;
        case FsmEventTarget.EventTarget.FSMComponent:
          if (!((UnityEngine.Object) entry.EventTarget.fsmComponent != (UnityEngine.Object) null))
            break;
          FsmEditor.SelectFsm(entry.EventTarget.fsmComponent.Fsm);
          break;
        case FsmEventTarget.EventTarget.BroadcastAll:
          if (entry.Event == null)
            break;
          GenericMenu genericMenu2 = new GenericMenu();
          foreach (Fsm fsm in FsmInfo.GetFsmList(FsmInfo.FindTransitionsUsingEvent(entry.Event.Name)))
            genericMenu2.AddItem(new GUIContent(Labels.GetFullFsmLabel(fsm)), false, new GenericMenu.MenuFunction2(FsmEditor.SelectFsm), (object) fsm);
          genericMenu2.ShowAsContext();
          break;
        case FsmEventTarget.EventTarget.HostFSM:
          FsmEditor.SelectFsm(entry.State.Fsm.Host);
          break;
      }
    }

    private void SelectLogEntry(FsmLogEntry entry)
    {
      if (entry == null)
        return;
      if (FsmEditorSettings.LogPauseOnSelect && !EditorApplication.isPaused)
        EditorApplication.isPaused = true;
      DebugFlow.SelectLogEntry(entry);
      this.updateVisibility = true;
    }

    private void ClearLog()
    {
      if (FsmLogger.selectedLog == null)
        return;
      FsmLogger.selectedLog.Clear();
      FsmLogger.ResetView();
    }

    private float CalculateEntryHeight(FsmLogEntry entry)
    {
      if (!this.EntryIsVisible(entry))
        return 0.0f;
      float num = 20f;
      if (entry.LogType == FsmLogType.EnterState)
        num += 2f;
      return num + 20f * (float) this.ExtraRows(entry);
    }

    private bool EntryIsVisible(FsmLogEntry entry) => !string.IsNullOrEmpty(entry.Text) && (entry.LogType != FsmLogType.ExitState || FsmEditorSettings.LogShowExit);

    private bool ShowSentBy(FsmLogEntry entry) => entry.LogType == FsmLogType.Event && FsmEditorSettings.LogShowSentBy && (entry.SentByState != null && entry.SentByState != entry.State);

    private bool ShowEventTarget(FsmLogEntry entry) => entry.LogType == FsmLogType.SendEvent && entry.EventTarget.target != FsmEventTarget.EventTarget.Self;

    private bool ShowHitGameObject(FsmLogEntry entry) => (UnityEngine.Object) entry.GameObject != (UnityEngine.Object) null || !string.IsNullOrEmpty(entry.GameObjectName);

    private int ExtraRows(FsmLogEntry entry) => this.ShowSentBy(entry) || this.ShowEventTarget(entry) || this.ShowHitGameObject(entry) ? 1 : 0;

    private void ResetLayout()
    {
      this.entryHeights.Clear();
      this.UpdateLayoutInfo();
    }

    private void UpdateLayoutInfo()
    {
      float entryPosition = this.GetEntryPosition(this.entryHeights.Count);
      while (this.entryHeights.Count < FsmLogger.selectedLog.Entries.Count)
      {
        FsmLogEntry entry = FsmLogger.selectedLog.Entries[this.entryHeights.Count];
        entryPosition += this.CalculateEntryHeight(entry);
        this.entryHeights.Add(entryPosition);
        this.updateVisibility = true;
      }
    }

    private float GetEntryPosition(int entryIndex) => entryIndex <= 0 ? 0.0f : this.entryHeights[entryIndex - 1];

    private bool ScrollViewIsAtBottom() => (double) Math.Abs(this.scrollPosition.y + this.scrollViewHeight - this.GetEntryPosition(this.entryHeights.Count)) < 100.0;

    private void ScrollToLatest() => this.scrollPosition.y = float.PositiveInfinity;

    private void UpdateVisibility()
    {
      if (this.entryHeights.Count == 0)
        return;
      float num = 0.0f;
      if (EditorApplication.isPaused)
        num = 100f;
      this.firstVisibleEntry = 0;
      while (this.firstVisibleEntry < this.entryHeights.Count && (double) this.entryHeights[this.firstVisibleEntry] + (double) num < (double) this.scrollPosition.y)
        ++this.firstVisibleEntry;
      this.lastVisibleEntry = this.firstVisibleEntry;
      while (this.lastVisibleEntry < this.entryHeights.Count && (double) this.entryHeights[this.lastVisibleEntry] - (double) num < (double) this.scrollPosition.y + (double) this.position.height)
        ++this.lastVisibleEntry;
      this.updateVisibility = false;
    }
  }
}
