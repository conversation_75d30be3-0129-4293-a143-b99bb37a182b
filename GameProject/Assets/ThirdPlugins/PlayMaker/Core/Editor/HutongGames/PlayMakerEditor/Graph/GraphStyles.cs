// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Graph.GraphStyles
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor.Graph
{
  public class GraphStyles
  {
    public const float DefaultStateRowHeight = 16f;
    public const float StateMinWidth = 100f;
    public const float StateMaxWidth = 400f;
    public const float StateWidthPadding = 20f;
    public const float DescriptionHeight = 44f;
    public const float MaxFsmDescriptionWidth = 200f;
    public static Color DefaultStateColor = new Color(0.15f, 0.15f, 0.15f);
    public static Color LightTextColor = new Color(1f, 1f, 1f, 0.8f);
    public static Color DarkTextColor = new Color(0.0f, 0.0f, 0.0f, 0.8f);

    public static bool IsInitialized { get; private set; }

    public static Color TitleTextColor { get; private set; }

    public static Color WatermarkTint { get; private set; }

    public static GUIStyle StateTransitionNode { get; private set; }

    public static GUIStyle StateTransitionNodeLast { get; private set; }

    public static GUIStyle TitleNode { get; private set; }

    public static RoundedBoxNode RoundedBox4px { get; private set; }

    public static RoundedBoxNode RoundedBox16px { get; private set; }

    public static RoundedBoxNode PanelNode { get; private set; }

    public static RoundedBoxNode StateNode { get; private set; }

    public static RoundedBoxNode StateNodeClosed { get; private set; }

    public static GUIStyle StateNodeBreakpoint { get; private set; }

    public static GUIStyle StateNodeClosedBreakpoint { get; private set; }

    public static GUIStyle Box { get; private set; }

    public static GUIStyle BoxBottomRadius4px { get; private set; }

    public static GUIStyle BreakpointOn { get; private set; }

    public static GUIStyle BreakpointOff { get; private set; }

    public static void ReInitialize()
    {
      GraphStyles.IsInitialized = false;
      GraphStyles.Initialize();
    }

    public static bool Initialize()
    {
      if (GraphStyles.IsInitialized)
        return false;
      GraphStyles.IsInitialized = true;
      GraphStyles.TitleTextColor = new Color(1f, 1f, 1f, 0.7f);
      GraphStyles.WatermarkTint = new Color(0.0f, 0.0f, 0.0f, 0.075f);
      GraphStyles.Box = GraphStyles.NewStyle("Graph/Box");
      GraphStyles.Box.border = new RectOffset(2, 2, 2, 2);
      GraphStyles.RoundedBox4px = new RoundedBoxNode(4);
      GraphStyles.RoundedBox16px = new RoundedBoxNode(16);
      GraphStyles.BoxBottomRadius4px = GraphStyles.NewStyle("Graph/BoxBottomRadius4px");
      GraphStyles.BoxBottomRadius4px.border = new RectOffset(6, 6, 2, 6);
      GraphStyles.StateTransitionNode = new GUIStyle()
      {
        normal = {
          background = Texture2D.whiteTexture,
          textColor = Color.white
        },
        overflow = new RectOffset(1, 1, 1, 0),
        alignment = TextAnchor.MiddleCenter,
        fontSize = 11,
        contentOffset = new Vector2(0.0f, -2f),
        fixedHeight = 16f
      };
      GraphStyles.StateTransitionNodeLast = new GUIStyle(GraphStyles.BoxBottomRadius4px)
      {
        overflow = new RectOffset(1, 1, 1, 0),
        alignment = TextAnchor.MiddleCenter,
        fontSize = 11,
        contentOffset = new Vector2(0.0f, -1f),
        fixedHeight = 16f,
        normal = {
          textColor = Color.white
        }
      };
      GraphStyles.StateNode = new RoundedBoxNode(4);
      GraphStyles.StateNode.SetOverflow(new RectOffset(1, 1, 4, 0));
      GraphStyles.StateNode.box.alignment = TextAnchor.UpperCenter;
      GraphStyles.StateNode.box.fontStyle = FontStyle.Bold;
      GraphStyles.StateNode.box.fontSize = 12;
      GraphStyles.StateNode.box.contentOffset = new Vector2(0.0f, -2f);
      GraphStyles.StateNode.box.padding = new RectOffset(5, 5, 0, 0);
      GraphStyles.StateNodeClosed = new RoundedBoxNode(4);
      GraphStyles.StateNodeClosed.SetOverflow(new RectOffset(1, 1, 4, 4));
      GraphStyles.StateNodeClosed.box.alignment = TextAnchor.UpperCenter;
      GraphStyles.StateNodeClosed.box.fontStyle = FontStyle.Bold;
      GraphStyles.StateNodeClosed.box.fontSize = 12;
      GraphStyles.StateNodeClosed.box.padding = new RectOffset(5, 5, 0, 0);
      GraphStyles.PanelNode = new RoundedBoxNode(16);
      GraphStyles.PanelNode.box.normal.textColor = GraphStyles.TitleTextColor;
      GraphStyles.PanelNode.box.fontSize = 20;
      GraphStyles.PanelNode.box.fontStyle = FontStyle.Bold;
      GraphStyles.PanelNode.box.alignment = TextAnchor.UpperLeft;
      GraphStyles.PanelNode.box.padding = new RectOffset(10, 10, 5, 5);
      GraphStyles.StateNodeBreakpoint = GraphStyles.NewStyle("Graph/StateNodeBreakpoint");
      GraphStyles.StateNodeBreakpoint.overflow = new RectOffset(0, 0, 3, 0);
      GraphStyles.StateNodeBreakpoint.border = new RectOffset(5, 2, 5, 2);
      GraphStyles.StateNodeBreakpoint.fixedWidth = 6f;
      GraphStyles.StateNodeBreakpoint.fixedHeight = 16f;
      GraphStyles.StateNodeClosedBreakpoint = GraphStyles.NewStyle("Graph/StateNodeClosedBreakpoint");
      GraphStyles.StateNodeClosedBreakpoint.overflow = new RectOffset(0, 0, 3, 3);
      GraphStyles.StateNodeClosedBreakpoint.border = new RectOffset(5, 2, 5, 5);
      GraphStyles.StateNodeClosedBreakpoint.fixedWidth = 6f;
      GraphStyles.StateNodeClosedBreakpoint.fixedHeight = 16f;
      GraphStyles.TitleNode = new GUIStyle(EditorStyles.label)
      {
        fontSize = 20,
        fontStyle = FontStyle.Bold
      };
      GraphStyles.BreakpointOn = GraphStyles.NewStyle("Graph/Breakpoint");
      GraphStyles.BreakpointOff = GraphStyles.NewStyle("Graph/BreakpointOff");
      return true;
    }

    public static void Draw(GUIStyle style, Rect pos, GUIContent content = null) => style.Draw(pos, content, false, false, false, false);

    public static GUIStyle NewStyle(string bgTexture) => GraphStyles.NewStyle((GUIStyle) null, bgTexture);

    public static GUIStyle NewStyle(GUIStyle baseStyle, string bgTexture)
    {
      GUIStyle style = baseStyle != null ? new GUIStyle(baseStyle) : new GUIStyle();
      GraphStyles.LoadBackground(style, bgTexture);
      return style;
    }

    private static void LoadBackground(GUIStyle style, string fileName)
    {
      style.normal.background = Resources.Load<Texture2D>(fileName);
      Texture2D texture2D = Resources.Load<Texture2D>(fileName + "@2x");
      if (!((Object) texture2D != (Object) null))
        return;
      style.normal.scaledBackgrounds = new Texture2D[1]
      {
        texture2D
      };
    }
  }
}
