// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmSelection
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  [Serializable]
  public class FsmSelection
  {
    [SerializeField]
    private int fsmComponentID;
    [NonSerialized]
    private PlayMakerFSM fsmComponent;
    [SerializeField]
    private FsmTemplate fsmTemplate;
    [SerializeField]
    private string stateName;
    [SerializeField]
    private List<string> stateNames;
    [SerializeField]
    private int transitionIndex;
    [SerializeField]
    private Vector2 scrollPosition;
    [SerializeField]
    private float zoom;
    [NonSerialized]
    private Fsm subFsm;
    [NonSerialized]
    private FsmState activeState;
    [NonSerialized]
    private List<FsmState> states;
    [NonSerialized]
    private FsmTransition activeTransition;

    public static FsmSelection None => new FsmSelection((Fsm) null);

    public PlayMakerFSM FsmComponent
    {
      get
      {
        if ((UnityEngine.Object) this.fsmComponent == (UnityEngine.Object) null && this.fsmComponentID != 0)
          this.fsmComponent = EditorUtility.InstanceIDToObject(this.fsmComponentID) as PlayMakerFSM;
        return this.fsmComponent;
      }
    }

    public bool IsOrphaned
    {
      get
      {
        if (this.ActiveFsm == null)
          return true;
        return !FsmEditor.FsmComponentList.Contains(this.ActiveFsmComponent) && !(bool) (UnityEngine.Object) this.ActiveFsm.UsedInTemplate;
      }
    }

    public bool IsGameObjectNull => this.ActiveFsm != null && (UnityEngine.Object) this.ActiveFsmTemplate == (UnityEngine.Object) null && (UnityEngine.Object) this.ActiveFsmGameObject == (UnityEngine.Object) null;

    public PlayMakerFSM ActiveFsmComponent => this.FsmComponent;

    public FsmTemplate ActiveFsmTemplate => this.fsmTemplate;

    public Fsm ActiveFsm
    {
      get
      {
        if (this.subFsm != null)
          return this.subFsm;
        if ((UnityEngine.Object) this.FsmComponent != (UnityEngine.Object) null)
          return this.FsmComponent.Fsm;
        return !((UnityEngine.Object) this.fsmTemplate != (UnityEngine.Object) null) ? (Fsm) null : this.fsmTemplate.fsm;
      }
      private set
      {
        this.subFsm = (Fsm) null;
        this.fsmTemplate = (FsmTemplate) null;
        this.fsmComponent = (PlayMakerFSM) null;
        if (value != null)
        {
          if (value.IsSubFsm)
            this.subFsm = value;
          if ((UnityEngine.Object) value.UsedInTemplate != (UnityEngine.Object) null)
          {
            this.fsmTemplate = value.UsedInTemplate;
          }
          else
          {
            this.fsmComponent = value.Owner as PlayMakerFSM;
            if ((UnityEngine.Object) this.fsmComponent != (UnityEngine.Object) null)
              this.fsmComponentID = this.fsmComponent.GetInstanceID();
          }
        }
        this.activeState = this.ActiveFsm != null ? this.ActiveFsm.GetState(this.stateName) : (FsmState) null;
        this.activeTransition = this.activeState != null ? this.activeState.GetTransition(this.transitionIndex) : (FsmTransition) null;
      }
    }

    public void ClearCaches()
    {
      this.subFsm = (Fsm) null;
      this.activeState = (FsmState) null;
      this.states = (List<FsmState>) null;
      this.activeTransition = (FsmTransition) null;
    }

    public FsmState ActiveState
    {
      get
      {
        if (this.ActiveFsm == null)
          this.activeState = (FsmState) null;
        else if (this.activeState == null && this.stateName != null)
          this.activeState = this.ActiveFsm.GetState(this.stateName);
        return this.activeState;
      }
      private set
      {
        if (this.ActiveFsm == null)
          return;
        this.stateName = value?.Name;
        this.activeState = this.ActiveFsm.GetState(this.stateName);
      }
    }

    public FsmTransition ActiveTransition
    {
      get => this.activeTransition;
      set
      {
        this.activeTransition = value;
        if (this.ActiveState != null)
          this.transitionIndex = this.ActiveState.GetTransitionIndex(value);
        else
          this.transitionIndex = -1;
      }
    }

    public List<string> StateNames => this.stateNames ?? (this.stateNames = new List<string>());

    public List<FsmState> States
    {
      get
      {
        if (this.states == null)
        {
          this.states = new List<FsmState>();
          if (this.ActiveFsm != null)
          {
            foreach (string stateName in this.StateNames)
              this.states.Add(this.ActiveFsm.GetState(stateName));
          }
        }
        return this.states;
      }
    }

    public string ActiveFsmName => this.ActiveFsm != null ? this.ActiveFsm.Name : (string) null;

    public MonoBehaviour ActiveFsmOwner => this.ActiveFsm != null ? this.ActiveFsm.Owner : (MonoBehaviour) null;

    public GameObject ActiveFsmGameObject => this.ActiveFsm != null ? this.ActiveFsm.GameObject : (GameObject) null;

    public string ActiveFsmGameObjectName => !((UnityEngine.Object) this.ActiveFsmGameObject == (UnityEngine.Object) null) ? this.ActiveFsmGameObject.name : "None";

    public PrefabType ActiveFsmPrefabType => !((UnityEngine.Object) this.ActiveFsmGameObject != (UnityEngine.Object) null) ? PrefabType.None : PrefabUtility.GetPrefabType((UnityEngine.Object) this.ActiveFsmGameObject);

    public Vector2 ScrollPosition
    {
      get => this.scrollPosition;
      set => this.scrollPosition = value;
    }

    public float Zoom
    {
      get => this.zoom;
      set => this.zoom = value;
    }

    public FsmSelection(Fsm fsm)
    {
      this.ActiveFsm = fsm;
      this.Zoom = 1f;
      if (fsm == null)
        return;
      FsmState state = fsm.GetState(fsm.StartState);
      this.ActiveState = state;
      this.states = new List<FsmState>()
      {
        this.ActiveState
      };
      this.stateNames = new List<string>()
      {
        fsm.StartState
      };
      this.ScrollPosition = state != null ? new Vector2(state.Position.x - 100f, state.Position.y - 175f) : Vector2.zero;
    }

    public void Update()
    {
      if (this.activeState != null)
        this.stateName = this.activeState.Name;
      if (this.states == null)
        return;
      this.states.RemoveAll((Predicate<FsmState>) (x => x == null));
      this.stateNames = new List<string>();
      foreach (FsmState state in this.states)
        this.stateNames.Add(state.Name);
    }

    public bool IsFor(Fsm testFsm)
    {
      if (testFsm == null)
        return false;
      if (testFsm.IsSubFsm)
        return this.subFsm == testFsm;
      if ((UnityEngine.Object) this.fsmTemplate != (UnityEngine.Object) null && (UnityEngine.Object) testFsm.UsedInTemplate == (UnityEngine.Object) this.fsmTemplate)
        return true;
      return (UnityEngine.Object) this.FsmComponent != (UnityEngine.Object) null && (UnityEngine.Object) testFsm.Owner == (UnityEngine.Object) this.FsmComponent;
    }

    public List<FsmState> SelectStates(
      List<FsmState> stateSelection,
      bool add = false,
      bool subtract = false)
    {
      if (this.ActiveFsm == null)
        return (List<FsmState>) null;
      if (!add && !subtract)
        this.DeselectAll();
      foreach (FsmState fsmState in stateSelection)
      {
        if (fsmState != null)
        {
          FsmState state = this.ActiveFsm.GetState(fsmState.Name);
          if (state != null)
          {
            if (!subtract)
              this.AddState(state);
            else
              this.RemoveState(state);
          }
        }
      }
      this.ActiveTransition = (FsmTransition) null;
      return this.States;
    }

    public List<FsmState> SelectAllStates()
    {
      if (this.ActiveFsm == null)
        return (List<FsmState>) null;
      this.DeselectStates();
      foreach (FsmState state in this.ActiveFsm.States)
        this.AddState(state);
      return this.States;
    }

    public FsmState SelectState(FsmState state, bool add = false, bool subtract = false)
    {
      if (this.ActiveFsm == null)
        return (FsmState) null;
      if (state == null)
      {
        if (!add && !subtract)
          this.DeselectAll();
        return (FsmState) null;
      }
      state = this.ActiveFsm.GetState(state.Name);
      if (state == null)
        return (FsmState) null;
      if (state.Fsm != this.ActiveFsm)
      {
        UnityEngine.Debug.LogError((object) "state.Fsm != ActiveFsm");
        return (FsmState) null;
      }
      if (add)
        this.AddState(state);
      else if (subtract)
      {
        this.RemoveState(state);
      }
      else
      {
        if (!this.States.Contains(state))
          this.DeselectAll();
        this.AddState(state);
      }
      this.ActiveTransition = (FsmTransition) null;
      if (!Application.isPlaying && this.ActiveFsm != null)
        this.ActiveFsm.EditState = state;
      FsmEditor.Repaint();
      return state;
    }

    public void AddState(FsmState state)
    {
      if (state == null)
        return;
      this.ActiveState = state;
      if (!this.States.Contains(state))
      {
        this.States.Add(state);
        this.StateNames.Add(state.Name);
      }
      FsmEditor.StateInspector.Reset();
      FsmEditor.StateInspector.ResetScrollPosition();
    }

    public void RemoveState(FsmState state)
    {
      if (state == null)
        return;
      this.States.Remove(state);
      this.StateNames.Remove(state.Name);
      this.ActiveState = (FsmState) null;
      this.ActiveTransition = (FsmTransition) null;
    }

    public void DeselectAll() => this.DeselectStates();

    public void DeselectStates()
    {
      this.ActiveState = (FsmState) null;
      this.ActiveTransition = (FsmTransition) null;
      this.States.Clear();
      this.StateNames.Clear();
      this.ActiveFsm.EditState = (FsmState) null;
      FsmEditor.InvalidatePixelCache();
    }

    public FsmTransition SelectTransition(FsmTransition transition)
    {
      if (this.ActiveState == null)
        return (FsmTransition) null;
      this.ActiveTransition = transition;
      return transition;
    }

    public void SelectActiveFsmGameObject()
    {
      if ((UnityEngine.Object) this.ActiveFsmGameObject == (UnityEngine.Object) null || (UnityEngine.Object) Selection.activeGameObject == (UnityEngine.Object) this.ActiveFsmGameObject)
        return;
      Selection.activeGameObject = this.ActiveFsmGameObject;
    }

    public bool Contains(FsmState testState) => this.activeState == testState || this.States.Contains(testState);

    public bool Contains(FsmTransition testTransition) => this.ActiveTransition == testTransition;

    public void SanityCheck()
    {
      if ((UnityEngine.Object) this.fsmComponent != (UnityEngine.Object) null && this.subFsm == null)
      {
        foreach (PlayMakerFSM component in this.fsmComponent.gameObject.GetComponents<PlayMakerFSM>())
        {
          if ((UnityEngine.Object) this.fsmComponent == (UnityEngine.Object) component)
          {
            this.ActiveFsm = component.Fsm;
            this.fsmComponent = component;
            break;
          }
        }
      }
      else if ((UnityEngine.Object) this.fsmTemplate != (UnityEngine.Object) null)
        this.ActiveFsm = this.fsmTemplate.fsm;
      if (this.ActiveFsm != null)
      {
        foreach (FsmState state in this.ActiveFsm.States)
          state.Fsm = this.ActiveFsm;
        this.activeState = this.ActiveFsm.GetState(this.stateName);
        if (this.activeState == null)
          this.stateName = (string) null;
        this.states = new List<FsmState>();
        foreach (string stateName in this.StateNames)
        {
          FsmState state = this.ActiveFsm.GetState(stateName);
          if (state != null)
            this.states.Add(state);
        }
      }
      else
      {
        this.activeState = (FsmState) null;
        this.states = new List<FsmState>();
      }
      this.activeTransition = this.activeState != null ? this.activeState.GetTransition(this.transitionIndex) : (FsmTransition) null;
      if (!FsmEditor.FsmContainsState(this.ActiveFsm, this.activeState))
        this.activeState = (FsmState) null;
      if (FsmEditor.StateContainsTransition(this.activeState, this.activeTransition))
        return;
      this.activeTransition = (FsmTransition) null;
    }

    public static Fsm FindFsmOnGameObject(GameObject go)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null)
        return (Fsm) null;
      PlayMakerFSM component = go.GetComponent<PlayMakerFSM>();
      return !((UnityEngine.Object) component == (UnityEngine.Object) null) ? component.Fsm : (Fsm) null;
    }

    public static PlayMakerFSM FindFsmComponentOnGameObject(GameObject go) => (UnityEngine.Object) go == (UnityEngine.Object) null ? (PlayMakerFSM) null : go.GetComponent<PlayMakerFSM>();

    public static Fsm FindFsmOnGameObject(GameObject go, string name)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null)
        return (Fsm) null;
      if (string.IsNullOrEmpty(name))
        name = "FSM";
      foreach (PlayMakerFSM component in go.GetComponents<PlayMakerFSM>())
      {
        if (component.name == name)
          return component.Fsm;
      }
      return (Fsm) null;
    }

    public static bool GameObjectHasFSM(GameObject go)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null)
        return false;
      for (int index = 0; index < FsmEditor.FsmComponentList.Count; ++index)
      {
        PlayMakerFSM fsmComponent = FsmEditor.FsmComponentList[index];
        if ((UnityEngine.Object) fsmComponent != (UnityEngine.Object) null && (UnityEngine.Object) fsmComponent.gameObject == (UnityEngine.Object) go)
          return true;
      }
      return false;
    }

    public static int GetFsmCount(GameObject go)
    {
      int num = 0;
      foreach (PlayMakerFSM fsmComponent in FsmEditor.FsmComponentList)
      {
        if ((UnityEngine.Object) fsmComponent != (UnityEngine.Object) null && (UnityEngine.Object) fsmComponent.gameObject == (UnityEngine.Object) go)
          ++num;
      }
      return num;
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("FsmSelection: " + message));
  }
}
