// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.EditPlayMakerGlobals
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;

namespace HutongGames.PlayMakerEditor
{
  public static class EditPlayMakerGlobals
  {
    public static void SetEventIsGlobal(FsmEvent fsmEvent, bool isGlobal)
    {
      if (fsmEvent == null)
        return;
      FsmEditor.RecordGlobalsUndo(Strings.Command_Edit_Event_Global_Setting);
      fsmEvent.IsGlobal = isGlobal;
      fsmEvent = FsmEvent.GetFsmEvent(fsmEvent.Name);
      fsmEvent.IsGlobal = isGlobal;
      FsmEditor.SaveGlobals();
      foreach (Fsm fsm in FsmEditor.FsmList)
      {
        foreach (FsmEvent fsmEvent1 in fsm.Events)
        {
          if (fsmEvent1.Name == fsmEvent.Name)
          {
            fsmEvent1.IsGlobal = isGlobal;
            FsmEditor.SetFsmDirty(fsm, true);
          }
        }
      }
      FsmEditor.RepaintAll();
    }
  }
}
