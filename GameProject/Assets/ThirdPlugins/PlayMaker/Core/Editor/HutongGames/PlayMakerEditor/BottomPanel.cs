// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.BottomPanel
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using System;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class BottomPanel
  {
    private bool debug;
    public Action DrawPanel;
    public bool HasSplitter;
    public string HighlightId;
    private readonly EditorWindow window;
    private Vector2 scrollPosition;
    private Rect splitterRect;
    private float fixedHeight;
    private bool resetHeight;
    private float minPanelHeight;

    public Rect PanelArea { get; private set; }

    public Rect ContentArea { get; private set; }

    public bool ExpandHeight => (double) this.fixedHeight > 0.0;

    public BottomPanel(EditorWindow _window) => this.window = _window;

    public void ResetHeight()
    {
      if (this.debug)
        Debug.Log((object) nameof (ResetHeight));
      this.resetHeight = true;
      this.fixedHeight = 0.0f;
    }

    public void OnGUI()
    {
      bool flag = Event.current.type == UnityEngine.EventType.Repaint;
      if (this.debug)
        GUILayout.Label("MinHeight: " + (object) this.minPanelHeight + " ContentArea.height: " + (object) this.ContentArea.height + " PanelArea.height: " + (object) this.PanelArea.height + " FixedHeight: " + (object) this.fixedHeight);
      if ((double) this.fixedHeight > 0.0)
        GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG, GUILayout.Height(this.fixedHeight));
      else
        GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG, GUILayout.Height(this.minPanelHeight));
      this.scrollPosition = EditorGUILayout.BeginScrollView(this.scrollPosition, GUILayout.ExpandHeight(false));
      GUILayout.BeginVertical();
      if (this.DrawPanel != null)
      {
        GUI.SetNextControlName("defaultFocus");
        this.DrawPanel();
      }
      GUILayout.EndVertical();
      if (flag)
      {
        this.ContentArea = GUILayoutUtility.GetLastRect();
        if (this.resetHeight)
        {
          this.minPanelHeight = this.ContentArea.height + 10f;
          this.Repaint();
          this.resetHeight = false;
        }
      }
      if (this.debug)
        GUIHelpers.DrawRect(this.ContentArea, Color.red);
      EditorGUILayout.EndScrollView();
      HighlighterHelper.EndVertical(this.HighlightId);
      if (flag)
        this.PanelArea = GUILayoutUtility.GetLastRect();
      if (!this.HasSplitter || !((UnityEngine.Object) this.window != (UnityEngine.Object) null))
        return;
      this.DoSplitter();
    }

    public void Focus() => GUI.FocusControl("defaultFocus");

    private void DoSplitter()
    {
      Event current = Event.current;
      UnityEngine.EventType type = Event.current.type;
      Rect position = this.window.position;
      Rect rect;
      if (DragAndDropManager.IsDraggingSplitter)
      {
        this.splitterRect.Set(0.0f, 0.0f, position.width, position.height);
      }
      else
      {
        ref Rect local = ref this.splitterRect;
        double x = (double) this.PanelArea.x;
        rect = this.PanelArea;
        double y = (double) rect.y;
        rect = this.PanelArea;
        double width = (double) rect.width;
        local.Set((float) x, (float) y, (float) width, 6f);
      }
      EditorGUIUtility.AddCursorRect(this.splitterRect, MouseCursor.ResizeVertical);
      if (this.splitterRect.Contains(current.mousePosition) && type == UnityEngine.EventType.MouseDown)
      {
        DragAndDropManager.DraggingSplitter(true);
        rect = this.PanelArea;
        this.fixedHeight = rect.height;
        current.Use();
      }
      if (!DragAndDropManager.IsDraggingSplitter)
        return;
      if (type == UnityEngine.EventType.MouseDrag)
      {
        rect = this.PanelArea;
        double num1 = (double) rect.yMax - (double) current.mousePosition.y;
        double minPanelHeight = (double) this.minPanelHeight;
        rect = this.window.position;
        double num2 = (double) rect.height - 150.0;
        this.fixedHeight = Mathf.Clamp((float) num1, (float) minPanelHeight, (float) num2);
        current.Use();
      }
      if (current.rawType != UnityEngine.EventType.MouseUp)
        return;
      DragAndDropManager.DraggingSplitter(false);
    }

    private void Repaint()
    {
      if ((UnityEngine.Object) this.window != (UnityEngine.Object) null)
        this.window.Repaint();
      else
        FsmEditor.RepaintAll();
    }
  }
}
