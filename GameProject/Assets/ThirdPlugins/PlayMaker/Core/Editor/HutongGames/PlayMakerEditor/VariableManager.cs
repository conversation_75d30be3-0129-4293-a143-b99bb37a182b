// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.VariableManager
// Assembly: PlayMakerEditor, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class VariableManager
  {
    private static NamedVariable delayedSelect;
    private static FsmClipboard _clipboard;
    public static bool testing;
    private FsmVariablesEditor _fsmVariablesEditor;
    [Obsolete("Use private FsmClipboard instead clipboard")]
    public static FsmTemplate Clipboard;

    public static void SelectAfterOpening(NamedVariable variable) => VariableManager.delayedSelect = variable;

    private static FsmClipboard clipboard => VariableManager._clipboard ?? (VariableManager._clipboard = new FsmClipboard());

    public static string GetClipboardDebugString() => VariableManager._clipboard == null ? "_clipboard = null" : VariableManager._clipboard.GetDebugString();

    public FsmVariablesEditor fsmVariablesEditor
    {
      get
      {
        if (this._fsmVariablesEditor == null)
          this.Reset();
        return this._fsmVariablesEditor;
      }
    }

    public void Reset()
    {
      this._fsmVariablesEditor = new FsmVariablesEditor(FsmEditor.Window, FsmEditor.SelectedFsm);
      this._fsmVariablesEditor.SettingsButtonClicked += new EditorApplication.CallbackFunction(this.DoSettingsMenu);
      this._fsmVariablesEditor.VariableContextClicked += new FsmVariablesEditor.ContextClickVariable(this.DoVariableContextMenu);
      this.UpdateRefreshButton();
      if (VariableManager.delayedSelect == null)
        return;
      this.SelectVariable(VariableManager.delayedSelect);
      VariableManager.delayedSelect = (NamedVariable) null;
    }

    private void UpdateRefreshButton()
    {
      this._fsmVariablesEditor.RefreshButtonClicked = (Action) null;
      if (FsmEditorSettings.AutoRefreshFsmInfo || !FsmSearch.FsmUsesGlobalVariables(FsmEditor.SelectedFsm))
        return;
      this._fsmVariablesEditor.RefreshButtonClicked = (Action) (() => FsmSearch.RefreshAll());
    }

    public void UpdateView() => this.fsmVariablesEditor.UpdateView();

    public void OnGUI()
    {
      if (InspectorPanel.DoUsesTemplateUI())
      {
        GUILayout.FlexibleSpace();
      }
      else
      {
        this.fsmVariablesEditor.SetTarget(FsmEditor.SelectedFsm);
        this.fsmVariablesEditor.OnGUI();
      }
    }

    private void DoSettingsMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent("Group Inputs and Outputs"), FsmEditorSettings.GroupVariableInputOutputs, new GenericMenu.MenuFunction(this.ToggleGroupInputOutput));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Debug_Variables), FsmEditorSettings.DebugVariables, new GenericMenu.MenuFunction(EditorCommands.ToggleDebugVariables));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Global_Variables), false, new GenericMenu.MenuFunction(FsmEditor.OpenGlobalVariablesWindow));
      genericMenu.AddItem(new GUIContent("Auto Refresh Global Used Counts"), FsmEditorSettings.AutoRefreshFsmInfo, (GenericMenu.MenuFunction) (() =>
      {
        EditorCommands.ToggleAutoRefreshFsmInfo();
        this.UpdateRefreshButton();
        if (!FsmEditorSettings.AutoRefreshFsmInfo)
          return;
        FsmSearch.RefreshAll();
      }));
      genericMenu.AddSeparator("");
      if (FsmEditor.SelectedFsm != null)
      {
        genericMenu.AddItem(new GUIContent(Strings.Menu_Copy_Variables), false, new GenericMenu.MenuFunction(this.CopyVariables));
        if (VariableManager.clipboard.HasVariables)
        {
          genericMenu.AddItem(new GUIContent(Strings.Menu_Paste_Variables), false, new GenericMenu.MenuFunction(this.PasteVariables));
          genericMenu.AddItem(new GUIContent(Strings.Menu_Paste_Variable_Values), false, new GenericMenu.MenuFunction(this.PasteVariableValues));
        }
        else
        {
          genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Paste_Variables));
          genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Paste_Variable_Values));
        }
      }
      else
      {
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Copy_Variables));
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Paste_Variables));
      }
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Delete_Unused_Variables), false, new GenericMenu.MenuFunction(this.DeleteUnusedVariables));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Online_Help), false, new GenericMenu.MenuFunction(VariableManager.OpenOnlineHelp));
      genericMenu.ShowAsContext();
    }

    private void ToggleGroupInputOutput()
    {
      FsmEditorSettings.GroupVariableInputOutputs = !FsmEditorSettings.GroupVariableInputOutputs;
      FsmEditorSettings.SaveSettings();
      this.Reset();
    }

    private void CopyVariables() => VariableManager.clipboard.CopyFsm(FsmEditor.SelectedFsm);

    private void PasteVariables()
    {
      if (!FsmEditor.DisconnectCheck() || VariableManager.clipboard.IsEmpty)
        return;
      FsmEditor.RecordUndo(Strings.Menu_Paste_Variables);
      bool overwriteValues = false;
      List<string> variableNameConflicts = VariableManager.clipboard.FindVariableNameConflicts(FsmEditor.SelectedFsm);
      if (variableNameConflicts.Count > 0)
      {
        string str = "\n\n" + string.Join("\n", variableNameConflicts.GetRange(0, variableNameConflicts.Count > 5 ? 5 : variableNameConflicts.Count).ToArray());
        if (variableNameConflicts.Count > 5)
        {
          str = str + "\n\n" + (object) (variableNameConflicts.Count - 5) + " More...\n\nCheck console for full list.";
          Debug.Log((object) ("PlayMaker Variable Name Conflicts:\n" + string.Join("\n", variableNameConflicts.ToArray())));
        }
        switch (Dialogs.YesNoCancelDialog(Strings.Dialog_Paste_Variables, Strings.PasteVariables_Warning_Some_variables_already_exist__overwrite_values + str))
        {
          case 0:
            overwriteValues = true;
            break;
        }
      }
      VariableManager.clipboard.PasteVariables(FsmEditor.SelectedFsm, overwriteValues);
      FsmEditor.SetFsmDirty();
      this.fsmVariablesEditor.Reset();
    }

    private void PasteVariableValues()
    {
      if (VariableManager.clipboard.IsEmpty)
        return;
      FsmEditor.RecordUndo(Strings.Menu_Paste_Variable_Values);
      FsmEditor.SelectedFsm.Variables.ApplyVariableValuesCareful(VariableManager.clipboard.Variables);
      FsmEditor.SetFsmDirty();
      this.fsmVariablesEditor.Reset();
    }

    public void DeleteUnusedVariables()
    {
      FsmSearch.Update(FsmEditor.SelectedFsm);
      List<FsmVariable> unusedVariables = FsmSearch.GetUnusedVariables(FsmEditor.SelectedFsm);
      if (!VariableManager.testing && unusedVariables.Count == 0)
      {
        EditorUtility.DisplayDialog(Strings.Command_Delete_Unused_Variables, Strings.Label_No_unused_variables, Strings.OK);
      }
      else
      {
        if (!VariableManager.testing && !Dialogs.YesNoDialog(Strings.Command_Delete_Unused_Variables, string.Format(Strings.Command_Delete_Variables_Are_you_sure, (object) unusedVariables.Count)))
          return;
        FsmEditor.RecordUndo(Strings.Menu_Delete_Unused_Variables);
        foreach (FsmVariable fsmVariable in new List<FsmVariable>((IEnumerable<FsmVariable>) unusedVariables))
          this.fsmVariablesEditor.DeleteVariable(fsmVariable, false, false);
        this.Reset();
      }
    }

    private static void OpenOnlineHelp() => EditorCommands.OpenWikiPage(WikiPages.VariableManager);

    private void DoVariableContextMenu(FsmVariable variable)
    {
      GenericMenu genericMenu = Menus.AddVariableUsedByItems(new GenericMenu(), variable.NamedVar);
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Move_To_Global_Variables), false, new GenericMenu.MenuFunction2(this.MoveToGlobals), (object) variable);
      genericMenu.ShowAsContext();
    }

    private void MoveToGlobals(object userdata)
    {
      if (!(userdata is FsmVariable variable) || variable.NamedVar == null)
        return;
      if (FsmVariables.GlobalVariables.Contains(variable.Name))
      {
        if (FsmVariables.GlobalVariables.GetVariable(variable.Name).VariableType != variable.NamedVar.VariableType)
        {
          Dialogs.OkDialog(Strings.Dialog_Make_Global_Variable, Strings.VariableManager_MoveToGlobals_Warning);
        }
        else
        {
          if (!Dialogs.YesNoDialog(Strings.Dialog_Make_Global_Variable, Strings.VariableManager_MoveToGlobals_Confirm))
            return;
          this.RemoveLocalVariable(variable);
        }
      }
      else
      {
        if (!Dialogs.AreYouSure(Strings.Dialog_Make_Global_Variable))
          return;
        FsmVariables.GlobalVariables.AddVariable(variable.NamedVar.Clone());
        this.RemoveLocalVariable(variable);
      }
    }

    private void RemoveLocalVariable(FsmVariable variable)
    {
      this.fsmVariablesEditor.DeleteVariable(variable, false, false);
      FsmEditor.SelectedFsm.Reload();
      FsmEditor.SetFsmDirty();
      this.Reset();
      GlobalVariablesWindow.ResetView();
    }

    public static void SortVariables(Fsm fsm)
    {
      if (fsm == null)
        return;
      VariableManager.SortVariables(fsm.Variables);
    }

    public static void SortVariables(FsmVariables fsmVariables)
    {
      if (fsmVariables == null)
        return;
      fsmVariables.BoolVariables = ArrayUtility.Sort<FsmBool>(fsmVariables.BoolVariables);
      fsmVariables.FloatVariables = ArrayUtility.Sort<FsmFloat>(fsmVariables.FloatVariables);
      fsmVariables.IntVariables = ArrayUtility.Sort<FsmInt>(fsmVariables.IntVariables);
      fsmVariables.Vector2Variables = ArrayUtility.Sort<FsmVector2>(fsmVariables.Vector2Variables);
      fsmVariables.Vector3Variables = ArrayUtility.Sort<FsmVector3>(fsmVariables.Vector3Variables);
      fsmVariables.GameObjectVariables = ArrayUtility.Sort<FsmGameObject>(fsmVariables.GameObjectVariables);
      fsmVariables.ObjectVariables = ArrayUtility.Sort<FsmObject>(fsmVariables.ObjectVariables);
      fsmVariables.RectVariables = ArrayUtility.Sort<FsmRect>(fsmVariables.RectVariables);
      fsmVariables.QuaternionVariables = ArrayUtility.Sort<FsmQuaternion>(fsmVariables.QuaternionVariables);
      fsmVariables.MaterialVariables = ArrayUtility.Sort<FsmMaterial>(fsmVariables.MaterialVariables);
      fsmVariables.TextureVariables = ArrayUtility.Sort<FsmTexture>(fsmVariables.TextureVariables);
      fsmVariables.StringVariables = ArrayUtility.Sort<FsmString>(fsmVariables.StringVariables);
      fsmVariables.ColorVariables = ArrayUtility.Sort<FsmColor>(fsmVariables.ColorVariables);
    }

    public void SelectVariable(NamedVariable variable) => this.fsmVariablesEditor.SelectVariable(variable.Name);
  }
}
