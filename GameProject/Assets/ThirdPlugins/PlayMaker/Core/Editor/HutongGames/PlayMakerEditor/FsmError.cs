// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmError
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.ComponentModel;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  internal class FsmError : IComparable
  {
    public Fsm Fsm;
    public FsmState State;
    public FsmStateAction Action;
    public string Parameter;
    public FsmTransition Transition;
    public string ErrorString;
    public GameObject GameObject;
    public System.Type ObjectType;
    public bool RuntimeError;
    public FsmError.ErrorType Type;
    public string info;

    public FsmError()
    {
    }

    public FsmError(FsmState state, FsmStateAction action, string errorString)
    {
      this.Action = action;
      this.State = state;
      this.Fsm = state.Fsm;
      this.ErrorString = errorString;
    }

    public FsmError(FsmState state, FsmStateAction action, string parameter, string errorString)
    {
      this.Action = action;
      this.Parameter = parameter;
      this.State = state;
      this.Fsm = state.Fsm;
      this.ErrorString = errorString;
    }

    public FsmError(FsmState state, FsmTransition transition, string errorString)
    {
      this.State = state;
      this.Fsm = state.Fsm;
      this.Transition = transition;
      this.ErrorString = errorString;
    }

    public bool SameAs(FsmError error) => error != null && this.Fsm == error.Fsm && (this.State == error.State && this.Action == error.Action) && (!(this.Parameter != error.Parameter) && this.Transition == error.Transition && (!(this.ErrorString != error.ErrorString) && this.Type == error.Type)) && this.ObjectType == error.ObjectType;

    [Localizable(false)]
    public string GetPath()
    {
      string str = Labels.GetFullFsmLabel(this.Fsm);
      if (this.State != null)
        str = str + " : " + this.State.Name;
      if (this.Action != null)
        str = str + " : " + Labels.StripNamespace(this.Action.ToString());
      if (this.Parameter != null)
        str = str + " : " + this.Parameter;
      return str;
    }

    [Localizable(false)]
    public override string ToString() => this.GetPath() + " : " + this.ErrorString;

    public int CompareTo(object obj) => string.Compare(this.ToString(), obj.ToString(), StringComparison.Ordinal);

    public enum ErrorType
    {
      general,
      missingRequiredComponent,
      requiredField,
      missingTransitionEvent,
      eventNotGlobal,
      missingVariable,
    }
  }
}
