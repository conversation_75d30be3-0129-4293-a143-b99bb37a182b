// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.DirectLink
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  internal class DirectLink : BaseLink
  {
    private static DirectLink instance;
    private const float FixedTangentLength = 10f;
    private float tangentLength;

    public static DirectLink Instance => DirectLink.instance ?? (DirectLink.instance = new DirectLink());

    public override GraphViewLinkStyle LinkStyle => GraphViewLinkStyle.DirectLinks;

    protected override void LinkRightSideToRightSide()
    {
      this.fromPos.x = this.fromStateRightX;
      this.toPos.x = this.toStateRightX;
      this.fromTangent.x = this.fromPos.x + this.tangentLength;
      this.toTangent.x = this.toPos.x + this.tangentLength + this.arrowWidthUnscaled;
    }

    protected override void LinkRightSideToLeftSide()
    {
      this.fromPos.x = this.fromStateRightX;
      this.toPos.x = this.toStateLeftX;
      this.fromTangent.x = this.fromPos.x + this.tangentLength;
      this.toTangent.x = this.toPos.x - this.tangentLength - this.arrowWidthUnscaled;
    }

    protected override void LinkLeftSideToLeftSide()
    {
      this.fromPos.x = this.fromStateLeftX;
      this.toPos.x = this.toStateLeftX;
      this.fromTangent.x = this.fromPos.x - this.tangentLength;
      this.toTangent.x = this.toPos.x - this.tangentLength - this.arrowWidthUnscaled;
    }

    protected override void LinkLeftSideToRightSide()
    {
      this.fromPos.x = this.fromStateLeftX;
      this.toPos.x = this.toStateRightX;
      this.fromTangent.x = this.fromPos.x - this.tangentLength;
      this.toTangent.x = this.toPos.x + this.tangentLength + this.arrowWidthUnscaled;
    }

    public override void Draw(
      FsmState fromState,
      FsmState toState,
      int transitionIndex,
      Color linkColor,
      float linkWidth,
      float scale)
    {
      this.Init(fromState, toState, transitionIndex, scale);
      this.tangentLength = 10f;
      this.horizontalMinDistance = this.tangentLength;
      this.DoDefaultLinker();
      this.fromTangent.y = this.fromPos.y;
      this.toTangent.y = this.toPos.y;
      this.DrawArrowHead(linkColor);
      linkWidth = Mathf.Min(linkWidth, linkWidth * 2f * scale);
      BaseLink.DrawPolylineCircuit(linkColor, linkWidth, scale, this.fromPos, this.fromTangent, this.toTangent, this.toPos);
    }

    public override bool HitTest(Vector2 point, float hitDistance) => false;
  }
}
