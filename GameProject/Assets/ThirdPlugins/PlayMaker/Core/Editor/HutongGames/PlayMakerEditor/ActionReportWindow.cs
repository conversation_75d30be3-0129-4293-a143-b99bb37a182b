// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.ActionReportWindow
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class ActionReportWindow : BaseEditorWindow
  {
    private const string tab1 = "  ";
    private const string tab2 = "    ";
    private const string tab3 = "      ";
    private Fsm currentFSM;
    private FsmState currentState;
    private FsmStateAction currentAction;
    private readonly string[] sortOptions = new string[2]
    {
      Strings.ActionReportWindow_Sort_By_FSM,
      Strings.ActionReportWindow_Sort_By_Action
    };
    private Vector2 scrollPosition;
    private readonly GUIContent label = new GUIContent();

    public override void Initialize() => this.needsMainEditor = true;

    public override void InitWindowTitle() => this.SetTitle(Strings.ActionReportWindow_Title);

    public override void DoGUI()
    {
      if (EditorApplication.isCompiling)
        GUI.enabled = false;
      FsmEditorStyles.Init();
      this.DoToolbar();
      this.scrollPosition = GUILayout.BeginScrollView(this.scrollPosition);
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box(Strings.ActionReportWindow_Description, FsmEditorStyles.HintBox);
      switch (FsmEditorSettings.ConsoleActionReportSortOptionIndex)
      {
        case 0:
          this.DoSortedByFSM();
          break;
        case 1:
          this.DoSortedByAction();
          break;
      }
      if (ActionReport.ActionReportList.Count == 0)
        GUILayout.Label(Strings.ActionReportWindow_No_warnings_or_errors___);
      this.Spacer();
      this.Spacer();
      FsmEditorGUILayout.LightDivider();
      GUILayout.FlexibleSpace();
      EditorGUILayout.HelpBox("NOTE: This window shows reports after loading a scene. Reload the scene to show the report again. Saving the scene saves any changes made while loading, so, most of the time, you will not see the same report when re-loading after you saved the scene. If anything in the report is unexpected or needs further investigation do not save the scene!", MessageType.Info);
      GUILayout.EndScrollView();
    }

    private void DoToolbar()
    {
      GUILayout.BeginHorizontal(EditorStyles.toolbar);
      EditorGUI.BeginChangeCheck();
      FsmEditorSettings.ConsoleActionReportSortOptionIndex = EditorGUILayout.Popup(FsmEditorSettings.ConsoleActionReportSortOptionIndex, this.sortOptions, EditorStyles.toolbarPopup);
      if (EditorGUI.EndChangeCheck())
        FsmEditorSettings.SaveSettings();
      GUILayout.FlexibleSpace();
      GUILayout.EndHorizontal();
    }

    private void DoSortedByFSM()
    {
      this.currentFSM = (Fsm) null;
      this.currentState = (FsmState) null;
      this.currentAction = (FsmStateAction) null;
      foreach (ActionReport actionReport in ActionReport.ActionReportList)
      {
        if (actionReport.fsm != null && actionReport.state != null && actionReport.action != null)
        {
          if (actionReport.fsm != this.currentFSM)
          {
            this.currentFSM = actionReport.fsm;
            FsmEditorGUILayout.Divider();
            this.label.text = Labels.GetFullFsmLabel(actionReport.fsm);
            this.label.tooltip = "Select: " + this.label.text;
            if (GUILayout.Button(this.label, EditorStyles.boldLabel))
            {
              ActionReportWindow.SelectReport(actionReport);
              break;
            }
            EditorGUIUtility.AddCursorRect(GUILayoutUtility.GetLastRect(), MouseCursor.Link);
          }
          if (actionReport.state != this.currentState)
          {
            this.currentState = actionReport.state;
            this.Spacer();
            this.label.text = "  " + actionReport.state.Name;
            this.label.tooltip = "Select State: " + actionReport.state.Name;
            if (GUILayout.Button(this.label, EditorStyles.boldLabel))
            {
              ActionReportWindow.SelectReport(actionReport);
              break;
            }
            EditorGUIUtility.AddCursorRect(GUILayoutUtility.GetLastRect(), MouseCursor.Link);
          }
          if (actionReport.action != this.currentAction)
          {
            this.currentAction = actionReport.action;
            this.Spacer();
            string actionLabel = Labels.GetActionLabel(actionReport.action);
            this.label.text = "    " + actionLabel;
            this.label.tooltip = "Select Action: " + actionLabel;
            if (GUILayout.Button(this.label, EditorStyles.boldLabel))
            {
              ActionReportWindow.SelectReport(actionReport);
              break;
            }
            EditorGUIUtility.AddCursorRect(GUILayoutUtility.GetLastRect(), MouseCursor.Link);
          }
          ActionReportWindow.DoReportLine("    ", actionReport);
        }
      }
    }

    private void DoSortedByAction()
    {
      List<System.Type> typeList = new List<System.Type>();
      foreach (ActionReport actionReport in ActionReport.ActionReportList)
      {
        System.Type type = actionReport.action.GetType();
        if (!typeList.Contains(type))
          typeList.Add(type);
      }
      this.currentAction = (FsmStateAction) null;
      foreach (System.Type actionType in typeList)
      {
        FsmEditorGUILayout.LightDivider();
        GUILayout.Label(Labels.GetActionLabel(actionType), EditorStyles.boldLabel);
        this.currentFSM = (Fsm) null;
        this.currentState = (FsmState) null;
        List<FsmState> fsmStateList = new List<FsmState>();
        List<ActionReport> actionReportList = new List<ActionReport>();
        List<string> stringList = new List<string>();
        this.Spacer();
        GUILayout.Label("  Action Changes", EditorStyles.boldLabel);
        foreach (ActionReport actionReport in ActionReport.ActionReportList)
        {
          if (actionReport.action.GetType() == actionType)
          {
            if (!fsmStateList.Contains(actionReport.state))
            {
              actionReportList.Add(actionReport);
              fsmStateList.Add(actionReport.state);
            }
            if (!stringList.Contains(actionReport.logText))
            {
              ActionReportWindow.DoReportLine("  ", actionReport);
              stringList.Add(actionReport.logText);
            }
          }
        }
        this.Spacer();
        GUILayout.Label("  Used In States", EditorStyles.boldLabel);
        foreach (ActionReport report in actionReportList)
        {
          if (report.state != null && report.fsm != null)
          {
            if (GUILayout.Button("  " + Labels.GetFullStateLabel(report.state), EditorStyles.label))
            {
              ActionReportWindow.SelectReport(report);
              return;
            }
            EditorGUIUtility.AddCursorRect(GUILayoutUtility.GetLastRect(), MouseCursor.Link);
          }
        }
      }
    }

    private static void DoReportLine(string prefix, ActionReport report)
    {
      if (report.isError)
        GUILayout.Box(prefix + report.logText, FsmEditorStyles.ErrorBox);
      else
        GUILayout.Label(prefix + report.logText);
    }

    private static void SelectReport(ActionReport report)
    {
      FsmEditor.SelectFsm(report.fsm);
      FsmEditor.SelectState(report.state, true);
      FsmEditor.SelectAction(report.state, report.actionIndex);
    }

    private void Spacer() => GUILayout.Space(5f);
  }
}
