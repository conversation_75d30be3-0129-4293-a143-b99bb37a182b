// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.EditFsmTemplate
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class EditFsmTemplate
  {
    private static string hiddenCategory = "~Hidden";

    public static bool IsHidden(this FsmTemplate template) => template.Category == EditFsmTemplate.hiddenCategory;

    public static void Hide(this FsmTemplate template) => template.Category = EditFsmTemplate.hiddenCategory;

    public static FsmTemplate Copy(this FsmTemplate template)
    {
      FsmTemplate template1 = EditFsmTemplate.CreateTemplate(template.fsm);
      template1.Category = template.Category;
      return template1;
    }

    public static void CopyFsm(this FsmTemplate template, Fsm fsm) => template.fsm = new Fsm(fsm)
    {
      UsedInTemplate = template,
      Owner = (MonoBehaviour) null
    };

    public static FsmTemplate CreateTemplate(Fsm fsm)
    {
      FsmTemplate instance = (FsmTemplate) ScriptableObject.CreateInstance(typeof (FsmTemplate));
      instance.fsm = new Fsm(fsm);
      return instance;
    }

    public static void SaveStatesAsNewTemplate(Fsm fromFsm, List<FsmState> states)
    {
      FsmTemplate templateAsset = EditFsmTemplate.CreateTemplateAsset();
      if ((Object) templateAsset == (Object) null)
        return;
      FsmClipboard fsmClipboard = new FsmClipboard();
      fsmClipboard.CopyStates(fromFsm, states);
      templateAsset.CopyFsm(fsmClipboard.Fsm);
      templateAsset.fsm.CheckStartState();
      fsmClipboard.Empty();
      FsmGraphView.MoveAllStatesToOrigin(templateAsset.fsm);
      EditorUtility.SetDirty((Object) templateAsset);
    }

    public static FsmTemplate SaveFsmAsNewTemplate(Fsm fsm, string assetFilename = "")
    {
      FsmTemplate templateAsset = EditFsmTemplate.CreateTemplateAsset(assetFilename);
      if ((Object) templateAsset == (Object) null)
        return (FsmTemplate) null;
      templateAsset.CopyFsm(fsm);
      EditorUtility.SetDirty((Object) templateAsset);
      FsmErrorChecker.CheckFsmForErrors(templateAsset.fsm, true);
      if (FsmErrorChecker.CountFsmErrors(templateAsset.fsm) > 0 && EditorUtility.DisplayDialog(Strings.Dialog_Save_Template, Strings.Dialog_CreateTemplate_Errors, Strings.Dialog_Option_Select_Template, Strings.Dialog_Option_Continue))
        FsmEditor.SelectFsm(templateAsset.fsm);
      return templateAsset;
    }

    public static FsmTemplate CreateTemplateAsset(string assetFilename = "")
    {
      string path = assetFilename;
      if (string.IsNullOrEmpty(path))
        path = EditFsmTemplate.DoSaveTemplateDialog();
      if (!PlayMakerPaths.IsValidTemplateSavePath(path))
      {
        Debug.LogWarning((object) ("Invalid Fsm Template save path: " + path));
        EditorUtility.DisplayDialog(Strings.ProductName, Strings.Dialog_Templates_can_only_be_saved_in_the_Project_s_Assets_folder, Strings.OK);
        return (FsmTemplate) null;
      }
      FsmTemplate instance = (FsmTemplate) ScriptableObject.CreateInstance(typeof (FsmTemplate));
      instance.fsm = new Fsm();
      instance.fsm.Reset((MonoBehaviour) null);
      instance.fsm.UsedInTemplate = instance;
      AssetDatabase.CreateAsset((Object) instance, path.Substring(Application.dataPath.Length - 6));
      return instance;
    }

    public static Fsm AddTemplateToSelected(FsmTemplate fsmTemplate)
    {
      if ((Object) fsmTemplate == (Object) null)
        return (Fsm) null;
      GameObject[] gameObjects = Selection.gameObjects;
      if (gameObjects.Length == 0)
        return (Fsm) null;
      if (gameObjects.Length > 1 && !Dialogs.AreYouSure(Strings.Dialog_Add_FSM_Template, Strings.Dialog_Add_FSM_Template_to_multiple_objects_))
        return (Fsm) null;
      Fsm fsm = (Fsm) null;
      foreach (GameObject gameObject in Selection.gameObjects)
        fsm = EditFsmTemplate.AddTemplateToGameObject(fsmTemplate, gameObject).Fsm;
      return fsm;
    }

    private static PlayMakerFSM AddTemplateToGameObject(
      FsmTemplate fsmTemplate,
      GameObject go,
      bool undo = true)
    {
      PlayMakerFSM gameObject = FsmBuilder.AddFsmToGameObject(go, undo);
      gameObject.Fsm = new Fsm(fsmTemplate.fsm)
      {
        UsedInTemplate = (FsmTemplate) null,
        Owner = (MonoBehaviour) gameObject
      };
      return gameObject;
    }

    private static string DoSaveTemplateDialog()
    {
      string templatesDirectory = PlayMakerPaths.GetTemplatesDirectory();
      string str = EditorPrefs.GetString("PlayMaker.SaveTemplatesDir", templatesDirectory);
      if (!PlayMakerPaths.IsValidTemplateSavePath(str))
        str = templatesDirectory;
      string path = EditorUtility.SaveFilePanel(Strings.Dialog_Save_Template, str, "template", "asset");
      if (!string.IsNullOrEmpty(path))
        EditorPrefs.SetString("PlayMaker.SaveTemplatesDir", Path.GetDirectoryName(path));
      return path;
    }
  }
}
