// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Dialogs
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using UnityEditor;

namespace HutongGames.PlayMakerEditor
{
  internal class Dialogs
  {
    public static void MissingAction(string actionName) => Dialogs.OkDialog(Strings.Dialogs_Missing_Action, actionName);

    public static void OkDialog(string message) => Dialogs.OkDialog(Strings.ProductName, message);

    public static void OkDialog(string title, string message) => EditorUtility.DisplayDialog(title, message, Strings.OK);

    public static bool YesNoDialog(string message) => Dialogs.YesNoDialog(Strings.ProductName, message);

    public static bool YesNoDialog(string title, string message) => EditorUtility.DisplayDialog(title, message, Strings.Yes, Strings.No);

    public static int YesNoCancelDialog(string title, string message) => EditorUtility.DisplayDialogComplex(title, message, Strings.Yes, Strings.No, "Cancel");

    public static bool AreYouSure(string title) => Dialogs.AreYouSure(title, Strings.Dialog_Are_you_sure);

    public static bool AreYouSure(string title, string message) => EditorUtility.DisplayDialog(title, message, Strings.OK, Strings.Command_Cancel);

    public static void PreviewVersion()
    {
      if (!EditorUtility.DisplayDialog(Strings.Dialogs_PREVIEW_VERSION, Strings.Dialogs_PreviewVersion_Note, "HutongGames.com", Strings.Label_OK))
        return;
      EditorCommands.OpenProductWebPage();
    }

    public static bool BreakPrefabInstance() => EditorUtility.DisplayDialog("Edit Prefab Instance", "This edit will break the connection to the Prefab and the Prefab Instance will be unpacked. If this is not what you want, cancel and edit the Prefab instead.", "Unpack Prefab", "Cancel");
  }
}
