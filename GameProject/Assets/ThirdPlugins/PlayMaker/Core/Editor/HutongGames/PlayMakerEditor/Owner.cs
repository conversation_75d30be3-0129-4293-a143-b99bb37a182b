// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Owner
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.ComponentModel;
using System.Diagnostics;
using UnityEditor;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class Owner
  {
    public Action OnChange;
    public Action OnSavedChanges;
    private string editDescription;
    private bool isDirty;
    private bool errorCheck;

    public int Id { get; private set; }

    public UnityEngine.Object Object { get; private set; }

    public PlayMakerFSM PlayMakerFSM { get; private set; }

    public FsmTemplate FsmTemplate { get; private set; }

    public PlayMakerGlobals PlayMakerGlobals { get; private set; }

    public Fsm Fsm { get; private set; }

    public Fsm SubFsm { get; private set; }

    public FsmVariables FsmVariables { get; private set; }

    public SerializedObject SerializedOwner { get; private set; }

    public bool IsPrefab { get; private set; }

    public bool IsAsset { get; private set; }

    public bool IsPlayMakerGlobals => (UnityEngine.Object) this.PlayMakerGlobals != (UnityEngine.Object) null;

    public bool IsPlayMakerFSM => (UnityEngine.Object) this.PlayMakerFSM != (UnityEngine.Object) null;

    public bool IsFsmTemplate => (UnityEngine.Object) this.FsmTemplate != (UnityEngine.Object) null;

    public bool IsOrphaned => this.Object == (UnityEngine.Object) null && this.Fsm == null && this.SubFsm == null;

    public static Owner Create(object target)
    {
      if (target == null)
        return new Owner();
      if (target is Fsm fsm)
        return new Owner(fsm);
      UnityEngine.Object @object = target as UnityEngine.Object;
      return @object != (UnityEngine.Object) null ? new Owner(@object) : throw new ArgumentException("Invalid Owner Type: " + (object) target.GetType());
    }

    private Owner()
    {
    }

    private Owner(UnityEngine.Object obj)
    {
      this.Object = obj;
      this.InitDerivedProperties();
    }

    private Owner(Fsm fsm)
    {
      this.Fsm = fsm;
      this.Object = fsm.OwnerObject;
      if (this.Object == (UnityEngine.Object) null)
        this.SubFsm = this.Fsm;
      this.InitDerivedProperties();
    }

    private void InitDerivedProperties()
    {
      this.PlayMakerFSM = this.Object as PlayMakerFSM;
      this.FsmTemplate = this.Object as FsmTemplate;
      this.PlayMakerGlobals = this.Object as PlayMakerGlobals;
      this.IsPrefab = FsmPrefabs.IsPrefab(this.Object);
      if (this.Fsm == null)
      {
        if ((UnityEngine.Object) this.PlayMakerFSM != (UnityEngine.Object) null)
          this.Fsm = this.PlayMakerFSM.Fsm;
        else if ((UnityEngine.Object) this.FsmTemplate != (UnityEngine.Object) null)
          this.Fsm = this.FsmTemplate.fsm;
      }
      if (this.Fsm != null)
        this.FsmVariables = this.Fsm.Variables;
      else if ((UnityEngine.Object) this.PlayMakerGlobals != (UnityEngine.Object) null)
        this.FsmVariables = this.PlayMakerGlobals.Variables;
      else if ((UnityEngine.Object) this.FsmTemplate != (UnityEngine.Object) null)
        this.FsmVariables = this.FsmTemplate.fsm.Variables;
      if (!(this.Object != (UnityEngine.Object) null))
        return;
      this.IsAsset = EditorUtility.IsPersistent(this.Object);
      this.SerializedOwner = GUIHelpers.GetSharedSerializedObject(this.Object);
      this.Id = this.Object.GetInstanceID();
    }

    public bool IsOwner(object target)
    {
      if (target is Fsm fsm)
        return this.Fsm == fsm;
      UnityEngine.Object @object = target as UnityEngine.Object;
      return @object != (UnityEngine.Object) null && this.Object == @object;
    }

    public SerializedProperty FindProperty(string path)
    {
      SerializedProperty property = this.SerializedOwner.FindProperty(path);
      if (property != null)
        return property;
      UnityEngine.Debug.LogError((object) (Strings.Error_Could_not_find_property_ + path));
      return property;
    }

    public void RecordObject(string action) => UndoUtility.RecordObject(this.Object, action);

    public void BeginEditing(string edit)
    {
      if (this.SerializedOwner != null && this.SerializedOwner.targetObject != (UnityEngine.Object) null)
        this.SerializedOwner.Update();
      this.editDescription = edit;
      this.isDirty = false;
      this.errorCheck = false;
      EditorGUI.BeginChangeCheck();
    }

    public void SetDirty(string edit, bool checkForErrors = false)
    {
      if (edit != null)
        this.editDescription = edit;
      this.isDirty = true;
      this.errorCheck = checkForErrors;
    }

    public bool EndEditing(bool undo = true, string edit = "")
    {
      if (!EditorGUI.EndChangeCheck() && !this.isDirty)
        return false;
      if (undo)
        this.RecordObject(edit == "" ? this.editDescription : edit);
      this.ApplyChanges();
      if (this.OnChange != null)
        this.OnChange();
      this.SaveChanges();
      return true;
    }

    public void ApplyChanges()
    {
      if (this.SerializedOwner == null || !(this.SerializedOwner.targetObject != (UnityEngine.Object) null))
        return;
      this.SerializedOwner.ApplyModifiedProperties();
    }

    public void SaveChanges()
    {
      if (this.Fsm != null)
        FsmEditor.SetFsmDirty(this.Fsm, this.errorCheck);
      else if ((UnityEngine.Object) this.PlayMakerGlobals != (UnityEngine.Object) null)
        FsmEditor.SaveGlobals(this.PlayMakerGlobals);
      if (this.OnSavedChanges == null)
        return;
      this.OnSavedChanges();
    }

    [Localizable(false)]
    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }
  }
}
