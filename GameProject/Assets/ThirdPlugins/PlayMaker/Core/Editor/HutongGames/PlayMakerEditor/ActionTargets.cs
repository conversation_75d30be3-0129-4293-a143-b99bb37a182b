// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.ActionTargets
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class ActionTargets
  {
    private static Dictionary<System.Type, List<ActionTarget>> _lookup;

    private static Dictionary<System.Type, List<ActionTarget>> lookup
    {
      get
      {
        if (ActionTargets._lookup == null)
          ActionTargets.Init();
        return ActionTargets._lookup;
      }
    }

    private static void Init()
    {
      ActionTargets._lookup = new Dictionary<System.Type, List<ActionTarget>>();
      foreach (System.Type type in Actions.List)
      {
        foreach (Attribute attribute in CustomAttributeHelpers.GetAttributes(type, typeof (ActionTarget)))
          ActionTargets.AddActionTarget(type, (ActionTarget) attribute);
      }
      foreach (System.Type actionType in Actions.List)
      {
        if (!ActionTargets.HasNoActionTargetsAttribute(actionType) && !ActionTargets.HasActionTargets(actionType))
          ActionTargets.GenerateActionTargets(actionType);
      }
    }

    public static List<System.Type> GetActions()
    {
      List<System.Type> list = ActionTargets.lookup.Keys.ToList<System.Type>();
      list.Sort((Comparison<System.Type>) ((a, b) => string.Compare(a.Name, b.Name, StringComparison.OrdinalIgnoreCase)));
      return list;
    }

    public static List<System.Type> GetActionsSortedByCategory()
    {
      List<System.Type> list = ActionTargets.lookup.Keys.ToList<System.Type>();
      list.Sort((Comparison<System.Type>) ((a, b) => string.Compare(Actions.GetCategory(a) + a.Name, Actions.GetCategory(b) + b.Name, StringComparison.OrdinalIgnoreCase)));
      return list;
    }

    public static List<ActionTarget> GetActionTargets(System.Type actionType)
    {
      List<ActionTarget> actionTargetList;
      return !ActionTargets.lookup.TryGetValue(actionType, out actionTargetList) ? new List<ActionTarget>() : actionTargetList;
    }

    public static bool HasNoActionTargetsAttribute(System.Type actionType) => CustomAttributeHelpers.HasAttribute<NoActionTargetsAttribute>(actionType);

    public static bool HasActionTargets(System.Type actionType)
    {
      List<ActionTarget> actionTargetList;
      return ActionTargets.lookup.TryGetValue(actionType, out actionTargetList) && actionTargetList.Count > 0;
    }

    public static bool HasActionTargetForType(System.Type actionType, System.Type targetType)
    {
      List<ActionTarget> source;
      return ActionTargets.lookup.TryGetValue(actionType, out source) && source.Any<ActionTarget>((Func<ActionTarget, bool>) (actionTarget => actionTarget.ObjectType == targetType));
    }

    private static void AddActionTarget(System.Type actionType, ActionTarget actionTarget)
    {
      List<ActionTarget> actionTargetList1;
      if (ActionTargets.lookup.TryGetValue(actionType, out actionTargetList1))
      {
        if (ActionTargets.HasActionTargetForType(actionType, actionTarget.ObjectType))
          return;
        actionTargetList1.Add(actionTarget);
      }
      else
      {
        List<ActionTarget> actionTargetList2 = new List<ActionTarget>()
        {
          actionTarget
        };
        ActionTargets.lookup.Add(actionType, actionTargetList2);
      }
    }

    private static void GenerateActionTargets(System.Type actionType)
    {
      foreach (FieldInfo field in actionType.GetFields(BindingFlags.Instance | BindingFlags.Public))
      {
        if (!CustomAttributeHelpers.HasUIHint(field, UIHint.Variable))
        {
          ActionTargets.FindCheckForComponentAttribute(actionType, field);
          ActionTargets.FindObjectTypeAttribute(actionType, field);
          ActionTargets.FindMaterialParameters(actionType, field);
          ActionTargets.FindGameObjectParameters(actionType, field);
          ActionTargets.FindColliderParameters(actionType, field);
          ActionTargets.FindUIHintParameters(actionType, field);
        }
      }
    }

    private static void FindCheckForComponentAttribute(System.Type actionType, FieldInfo field)
    {
      CheckForComponentAttribute attribute = CustomAttributeHelpers.GetAttribute<CheckForComponentAttribute>(field);
      if (attribute == null)
        return;
      if (attribute.Type0 != null)
        ActionTargets.AddActionTarget(actionType, new ActionTarget(attribute.Type0, field.Name));
      if (attribute.Type1 != null)
        ActionTargets.AddActionTarget(actionType, new ActionTarget(attribute.Type1, field.Name));
      if (attribute.Type2 == null)
        return;
      ActionTargets.AddActionTarget(actionType, new ActionTarget(attribute.Type2, field.Name));
    }

    private static void FindObjectTypeAttribute(System.Type actionType, FieldInfo field)
    {
      ObjectTypeAttribute attribute = CustomAttributeHelpers.GetAttribute<ObjectTypeAttribute>(field);
      if (attribute == null || attribute.ObjectType == null)
        return;
      ActionTargets.AddActionTarget(actionType, new ActionTarget(attribute.ObjectType, field.Name));
    }

    private static void FindMaterialParameters(System.Type actionType, FieldInfo field)
    {
      if (field.FieldType == typeof (FsmMaterial) || field.FieldType == typeof (Material))
      {
        ActionTargets.AddActionTarget(actionType, new ActionTarget(typeof (Material), field.Name));
      }
      else
      {
        if (field.FieldType != typeof (FsmTexture) && field.FieldType != typeof (Texture))
          return;
        ActionTargets.AddActionTarget(actionType, new ActionTarget(typeof (Texture), field.Name));
      }
    }

    private static void FindGameObjectParameters(System.Type actionType, FieldInfo field)
    {
      if (field.FieldType != typeof (FsmOwnerDefault) && field.FieldType != typeof (FsmGameObject) && field.FieldType != typeof (GameObject) || CustomAttributeHelpers.HasAttribute<CheckForComponentAttribute>(field))
        return;
      ActionTargets.AddActionTarget(actionType, new ActionTarget(typeof (GameObject), field.Name));
    }

    private static void FindColliderParameters(System.Type actionType, FieldInfo field)
    {
      System.Type fieldType = field.FieldType;
      if (fieldType == typeof (CollisionType) || fieldType == typeof (TriggerType))
      {
        ActionTargets.AddActionTarget(actionType, new ActionTarget(typeof (Collider)));
        ActionTargets.AddActionTarget(actionType, new ActionTarget(typeof (Rigidbody)));
        ActionTargets.AddActionTarget(actionType, new ActionTarget(typeof (GameObject)));
      }
      else
      {
        if (fieldType != typeof (Collision2DType) && fieldType != typeof (Trigger2DType))
          return;
        ActionTargets.AddActionTarget(actionType, new ActionTarget(typeof (Collider2D)));
        ActionTargets.AddActionTarget(actionType, new ActionTarget(typeof (Rigidbody2D)));
        ActionTargets.AddActionTarget(actionType, new ActionTarget(typeof (GameObject)));
      }
    }

    private static void FindUIHintParameters(System.Type actionType, FieldInfo field)
    {
      foreach (UIHintAttribute attribute in CustomAttributeHelpers.GetAttributes<UIHintAttribute>(field))
      {
        if (attribute.Hint == UIHint.Animation)
          ActionTargets.AddActionTarget(actionType, new ActionTarget(typeof (AnimationClip), field.Name));
      }
    }
  }
}
