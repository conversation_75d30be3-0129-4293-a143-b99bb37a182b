// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Graph.DragConstraint
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;

namespace HutongGames.PlayMakerEditor.Graph
{
  [Flags]
  public enum DragConstraint
  {
    None = 0,
    Horizontal = 1,
    Vertical = 2,
    Axis = Vertical | Horizontal, // 0x00000003
    SnapToGrid = 4,
    AlignToNodes = 8,
  }
}
