// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.Experimental.SceneManagement;
using UnityEditor.Presets;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  [Serializable]
  public class FsmEditor
  {
    public static float InspectorPanelWidth = 350f;
    public static bool IgnoreHierarchyChange;
    public static bool IgnoreOnValidate;
    public bool Initialized;
    private EditorWindow window;
    private static Fsm dirtyFsmPrefab;
    private static List<PlayMakerFSM> fsmComponentList;
    private static List<Fsm> fsmList;
    private static string sendWindowCommandEvent;
    [SerializeField]
    private FsmSelection fsmSelection;
    [SerializeField]
    private FsmSelectionHistory selectionHistory;
    public readonly FsmBuilder builder;
    public readonly FsmClipboard clipboard;
    public readonly MainToolbar toolbar;
    public readonly FsmGraphView graphView;
    public readonly InspectorPanel inspector;
    public readonly StateInspector stateInspector;
    public readonly ActionEditor actionEditor;
    public readonly FsmEventManager eventsManager;
    public readonly VariableManager variablesManager;
    public readonly FsmInspector fsmInspector;
    public static Action OnUpdate;
    public static Action OnRepaint;
    private bool openReport;
    private bool repaint;
    private bool repaintAll;
    private bool mouseUp;
    private bool mouseDown;
    private static bool updateIsModifiedPrefabInstance;
    private static bool warnedAboutEditingWhileRunning;
    private Rect toolbarRect;
    private Rect inspectorPanelRect;
    private Rect graphViewRect;
    private Rect debugToolbarRect;
    private Rect splitterRect;
    private bool draggingSplitter;
    private PrefabAssetType prefabTypeLastUpdate;
    private static Fsm editingPrefabInstance;
    public static Action<Fsm> OnFsmChanged;
    public static Action<Fsm> OnFsmControlsChanged;
    private const string undoPrefix = "";
    private static bool breakPrefabAlreadyConfirmedThisUpdate;
    private static bool breakPrefabChoice;
    private static int updateCount;

    public static FsmEditor Instance { get; private set; }

    public static List<PlayMakerFSM> FsmComponentList
    {
      get
      {
        if (FsmEditor.fsmComponentList == null)
          FsmEditor.RebuildFsmList();
        return FsmEditor.fsmComponentList;
      }
    }

    public static List<Fsm> FsmList
    {
      get
      {
        if (FsmEditor.fsmList == null)
          FsmEditor.RebuildFsmList();
        return FsmEditor.fsmList;
      }
    }

    public static List<Fsm> SortedFsmList
    {
      get
      {
        List<Fsm> fsmList = new List<Fsm>((IEnumerable<Fsm>) FsmEditor.FsmList);
        fsmList.Sort();
        return fsmList;
      }
    }

    public static bool NeedRepaint
    {
      get
      {
        if (FsmEditor.Instance == null)
          return false;
        return FsmEditor.Instance.repaint || FsmEditor.Instance.repaintAll;
      }
    }

    public static bool MouseOverInspector { get; private set; }

    public static bool InspectorHasFocus { get; set; }

    public static FsmSearch Search => FsmSearch.GetSearch(FsmEditor.SelectedFsm);

    public static FsmSelectionHistory SelectionHistory => FsmEditor.Instance == null ? (FsmSelectionHistory) null : FsmEditor.Instance.selectionHistory ?? (FsmEditor.Instance.selectionHistory = new FsmSelectionHistory());

    public static UnityEngine.Object SelectedFsmOwner
    {
      get
      {
        if (FsmEditor.Instance == null)
          return (UnityEngine.Object) null;
        if ((UnityEngine.Object) FsmEditor.Instance.fsmSelection.ActiveFsmComponent != (UnityEngine.Object) null)
          return (UnityEngine.Object) FsmEditor.Instance.fsmSelection.ActiveFsmComponent;
        return (UnityEngine.Object) FsmEditor.Instance.fsmSelection.ActiveFsmTemplate != (UnityEngine.Object) null ? (UnityEngine.Object) FsmEditor.Instance.fsmSelection.ActiveFsmTemplate : (UnityEngine.Object) null;
      }
    }

    public static PlayMakerFSM SelectedFsmComponent => FsmEditor.Instance != null ? FsmEditor.Instance.fsmSelection.ActiveFsmComponent : (PlayMakerFSM) null;

    public static bool SelectedFsmUsesTemplate => (UnityEngine.Object) FsmEditor.SelectedFsmComponent != (UnityEngine.Object) null && FsmEditor.SelectedFsmComponent.UsesTemplate;

    public static Fsm SelectedFsm => FsmEditor.Instance != null ? FsmEditor.Instance.fsmSelection.ActiveFsm : (Fsm) null;

    public static bool SelectedFsmIsLocked => FsmEditor.SelectedFsm != null && FsmEditor.SelectedFsm.Locked;

    public static bool SelectedFsmDebugFlowEnabled => FsmEditor.SelectedFsm != null && FsmEditor.SelectedFsm.EnableDebugFlow;

    public static FsmTemplate SelectedTemplate => FsmEditor.Instance != null ? FsmEditor.Selection.ActiveFsmTemplate : (FsmTemplate) null;

    public static FsmState SelectedState => FsmEditor.Instance != null ? FsmEditor.Instance.fsmSelection.ActiveState : (FsmState) null;

    public static string SelectedStateName => FsmEditor.SelectedState != null ? FsmEditor.SelectedState.Name : "";

    public static List<FsmState> SelectedStates => FsmEditor.Instance != null ? FsmEditor.Selection.States : (List<FsmState>) null;

    public static FsmTransition SelectedTransition => FsmEditor.Instance != null ? FsmEditor.Selection.ActiveTransition : (FsmTransition) null;

    public static int SelectedTransitionIndex => FsmEditor.Instance == null || FsmEditor.SelectedState == null || FsmEditor.SelectedTransition == null ? -1 : FsmEditor.SelectedState.GetTransitionIndex(FsmEditor.Selection.ActiveTransition);

    public static List<FsmStateAction> SelectedActions => FsmEditor.Instance != null ? StateInspector.SelectedActions : (List<FsmStateAction>) null;

    public static GameObject SelectedFsmGameObject => FsmEditor.Instance != null ? FsmEditor.Selection.ActiveFsmGameObject : (GameObject) null;

    public static string SelectedFsmGameObjectName => FsmEditor.Instance != null ? FsmEditor.Selection.ActiveFsmGameObjectName : (string) null;

    public static int SelectedFsmInstanceID => !((UnityEngine.Object) FsmEditor.SelectedFsmGameObject != (UnityEngine.Object) null) ? -1 : FsmEditor.SelectedFsmGameObject.GetInstanceID();

    public static EditorWindow Window => FsmEditor.Instance != null ? FsmEditor.Instance.window : (EditorWindow) null;

    public static FsmBuilder Builder => FsmEditor.Instance != null ? FsmEditor.Instance.builder : (FsmBuilder) null;

    public static FsmClipboard Clipboard => FsmEditor.Instance != null ? FsmEditor.Instance.clipboard : (FsmClipboard) null;

    public static bool MouseDown => FsmEditor.Instance.mouseDown;

    public static bool MouseUp => FsmEditor.Instance.mouseUp;

    public static InspectorPanel Inspector => FsmEditor.Instance.inspector;

    public static ActionEditor ActionEditor => FsmEditor.Instance.actionEditor;

    public static FsmSelection Selection => FsmEditor.Instance == null ? (FsmSelection) null : FsmEditor.Instance.fsmSelection ?? new FsmSelection((Fsm) null);

    public static FsmGraphView GraphView => FsmEditor.Instance == null ? (FsmGraphView) null : FsmEditor.Instance.graphView;

    public static StateInspector StateInspector => FsmEditor.Instance.stateInspector;

    public static FsmEventManager EventsManager => FsmEditor.Instance.eventsManager;

    public static VariableManager VariablesManager => FsmEditor.Instance.variablesManager;

    public static FsmInspector FsmInspector => FsmEditor.Instance.fsmInspector;

    [InitializeOnEnterPlayMode]
    private static void OnEnterPlaymodeInEditor(EnterPlayModeOptions options)
    {
      if ((options & EnterPlayModeOptions.DisableDomainReload) != EnterPlayModeOptions.DisableDomainReload)
        return;
      PlayMakerFSM.ApplicationIsQuitting = false;
      PlayMakerFSM.FsmList.Clear();
      Fsm.EventData = new FsmEventData();
      FsmExecutionStack.Reset();
      PlayMakerGlobals.ResetInstance();
    }

    public static bool IsOpen => FsmEditor.Instance != null;

    public static void Open()
    {
      if (FsmEditor.IsOpen)
        return;
      EditorApplication.ExecuteMenuItem("PlayMaker/PlayMaker Editor");
    }

    public static void Open(Fsm fsm)
    {
      FsmEditor.Open();
      FsmEditor.SelectFsm(fsm);
    }

    public static void Open(PlayMakerFSM fsmComponent)
    {
      FsmEditor.Open();
      FsmEditor.SelectFsm(fsmComponent);
    }

    public static void Open(FsmState state)
    {
      FsmEditor.Open();
      FsmEditor.SelectFsm(state.Fsm);
      EditorApplication.delayCall += (EditorApplication.CallbackFunction) (() => FsmEditor.SelectState(state, true));
    }

    public static void Open(FsmInfo selectionInfo)
    {
      FsmEditor.Open();
      EditorApplication.delayCall += new EditorApplication.CallbackFunction(selectionInfo.Select);
    }

    public static void InvalidatePixelCache()
    {
      if (FsmEditor.Instance == null || !FsmEditorSettings.UsePixelCaching)
        return;
      FsmEditor.Instance.inspector.InvalidatePixelCache();
    }

    public FsmEditor()
    {
      if (FsmEditor.Instance == null)
        FsmEditor.Instance = this;
      this.fsmInspector = new FsmInspector();
      this.builder = new FsmBuilder();
      this.clipboard = new FsmClipboard();
      this.actionEditor = new ActionEditor();
      this.stateInspector = new StateInspector();
      this.eventsManager = new FsmEventManager();
      this.variablesManager = new VariableManager();
      this.graphView = new FsmGraphView();
      this.inspector = new InspectorPanel();
      this.toolbar = new MainToolbar();
      this.selectionHistory = new FsmSelectionHistory();
      this.fsmSelection = FsmSelection.None;
      DragAndDropManager.Reset();
    }

    public void InitWindow(EditorWindow fsmEditorWindow)
    {
      this.window = fsmEditorWindow;
      this.window.minSize = new Vector2(800f, 200f);
      this.window.wantsMouseMove = true;
      this.Initialized = true;
      if (FsmEditor.Instance != null)
        return;
      FsmEditor.Instance = this;
    }

    public void OnEnable()
    {
      if (FsmEditor.Instance == null)
        FsmEditor.Instance = this;
      EditorApplication.delayCall += new EditorApplication.CallbackFunction(this.FirstUpdate);
      this.fsmSelection.ClearCaches();
      Fsm lastSelectedFsm = FsmEditor.SelectionHistory.GetLastSelectedFSM();
      if (lastSelectedFsm != null && lastSelectedFsm.OwnerObject == UnityEditor.Selection.activeObject)
      {
        FsmEditor.SelectNone();
        FsmEditor.SelectFsm(lastSelectedFsm);
      }
      this.Init();
      this.AddDelegates();
    }

    private void Init()
    {
      FsmEditor.RebuildFsmList();
      if (!Application.isPlaying && FsmEditor.SelectedFsm != null)
        FsmEditor.SelectedFsm.Reload();
      GameStateTracker.Init();
      CustomActionEditors.Init();
      FsmErrorChecker.Init();
      Actions.Init();
      Templates.Init();
      ObjectPropertyDrawers.Init();
      PropertyDrawers.Init();
      this.graphView.Init();
      this.inspector.Init();
      this.eventsManager.Init();
      if (FsmEditor.SelectedFsm == null)
        this.OnSelectionChange();
      FsmEditor.DoDirtyFsmPrefab();
      FsmEditor.SanityCheckSelection();
      this.AddDelegatesToSelectedFsm();
      FsmEditor.UpdateFsmInfo();
    }

    private void AddDelegates()
    {
      this.RemoveDelegates();
      Undo.undoRedoPerformed += new Undo.UndoRedoCallback(FsmEditor.UndoRedoPerformed);
      EditorSceneManager.sceneOpened += new EditorSceneManager.SceneOpenedCallback(this.OnSceneOpened);
      EditorSceneManager.sceneClosed += new EditorSceneManager.SceneClosedCallback(this.OnSceneClosed);
      EditorApplication.playModeStateChanged += new Action<PlayModeStateChange>(this.OnPlayModeChanged);
      EditorApplication.pauseStateChanged += new Action<PauseState>(this.OnPlayerPauseChanged);
      PrefabUtility.prefabInstanceUpdated += new PrefabUtility.PrefabInstanceUpdated(this.PrefabInstanceUpdated);
      SceneView.duringSceneGui += new Action<SceneView>(this.OnSceneGUICallback);
      ObjectFactory.componentWasAdded += new Action<UnityEngine.Component>(this.ComponentWasAdded);
      PrefabStage.prefabStageOpened += new Action<PrefabStage>(FsmEditor.OnPrefabStageOpened);
    }

    private void RemoveDelegates()
    {
      Undo.undoRedoPerformed -= new Undo.UndoRedoCallback(FsmEditor.UndoRedoPerformed);
      EditorSceneManager.sceneOpened -= new EditorSceneManager.SceneOpenedCallback(this.OnSceneOpened);
      EditorSceneManager.sceneClosed -= new EditorSceneManager.SceneClosedCallback(this.OnSceneClosed);
      EditorApplication.playModeStateChanged -= new Action<PlayModeStateChange>(this.OnPlayModeChanged);
      EditorApplication.pauseStateChanged -= new Action<PauseState>(this.OnPlayerPauseChanged);
      PrefabUtility.prefabInstanceUpdated -= new PrefabUtility.PrefabInstanceUpdated(this.PrefabInstanceUpdated);
      SceneView.duringSceneGui -= new Action<SceneView>(this.OnSceneGUICallback);
      ObjectFactory.componentWasAdded -= new Action<UnityEngine.Component>(this.ComponentWasAdded);
      PrefabStage.prefabStageOpened -= new Action<PrefabStage>(FsmEditor.OnPrefabStageOpened);
    }

    private static void OnPrefabStageOpened(PrefabStage prefabStage) => FsmEditor.RebuildFsmList();

    private void UpdateHighlightIdentifiers()
    {
      float fixedHeight = EditorStyles.toolbar.fixedHeight;
      Rect position = this.window.position;
      float inspectorPanelWidth = FsmEditor.InspectorPanelWidth;
      float num = position.width - inspectorPanelWidth;
      HighlighterHelper.FromPosition(0.0f, fixedHeight, num - 0.0f, position.height - fixedHeight * 2f, "Graph View");
      HighlighterHelper.FromPosition(0.0f, position.height - fixedHeight, num, fixedHeight, "Debug Toolbar");
      HighlighterHelper.FromPosition(position.width - inspectorPanelWidth, position.height - fixedHeight, inspectorPanelWidth, fixedHeight, "Preferences Toolbar");
      HighlighterHelper.FromPosition(position.width - inspectorPanelWidth, position.height - fixedHeight, 120f, fixedHeight, "Hints");
      HighlighterHelper.FromPosition((float) ((double) position.width - (double) inspectorPanelWidth + 120.0), position.height - fixedHeight, inspectorPanelWidth - 120f, fixedHeight, "Open Preferences");
      HighlighterHelper.FromPosition(position.width - inspectorPanelWidth, 0.0f, inspectorPanelWidth, position.height - fixedHeight, "Inspector Panel");
      float width = inspectorPanelWidth * 0.25f;
      HighlighterHelper.FromPosition(num, 0.0f, width, fixedHeight, "FSM Inspector");
      HighlighterHelper.FromPosition(num + width, 0.0f, width, fixedHeight, "State Inspector");
      HighlighterHelper.FromPosition(num + width * 2f, 0.0f, width, fixedHeight, "Event Manager");
      HighlighterHelper.FromPosition(num + width * 3f, 0.0f, width, fixedHeight, "Variable Manager");
    }

    public void OnGUI()
    {
      if ((UnityEngine.Object) this.window == (UnityEngine.Object) null)
      {
        GUILayout.Label("Could not open main editor. Please try again...");
      }
      else
      {
        EditorGUIUtility.wideMode = FsmEditorSettings.InspectorWideMode;
        if (Event.current.type == UnityEngine.EventType.Repaint)
        {
          if (HighlighterHelper.Enabled)
            this.UpdateHighlightIdentifiers();
          if (FsmEditor.OnRepaint != null)
            FsmEditor.OnRepaint();
        }
        FsmEditor.Builder.SetTarget(FsmEditor.SelectedFsm);
        if (FsmEditor.SelectedState != null)
          FsmEditor.SelectedState.Fsm = FsmEditor.SelectedFsm;
        this.graphView.EnableEditing();
        if (FsmEditorGUILayout.DoEditorDisabledGUI())
          return;
        EditorGUI.BeginDisabledGroup(EditorApplication.isCompiling);
        if (this.repaint || FsmStateAction.Repaint)
          this.DoRepaint();
        FsmEditorStyles.Init();
        this.DoSplitter();
        this.HandleMouseInput();
        float num1 = FsmEditorSettings.InspectorOnLeft ? FsmEditor.InspectorPanelWidth : 0.0f;
        Rect position;
        double num2;
        if (!FsmEditorSettings.InspectorOnLeft)
        {
          position = this.window.position;
          num2 = (double) position.width - (double) FsmEditor.InspectorPanelWidth;
        }
        else
          num2 = 0.0;
        float num3 = (float) num2;
        ref Rect local1 = ref this.toolbarRect;
        double num4 = (double) num1;
        position = FsmEditor.Window.position;
        double num5 = (double) position.width - (double) FsmEditor.InspectorPanelWidth;
        double fixedHeight1 = (double) EditorStyles.toolbar.fixedHeight;
        local1.Set((float) num4, 0.0f, (float) num5, (float) fixedHeight1);
        ref Rect local2 = ref this.inspectorPanelRect;
        double num6 = (double) num3;
        double inspectorPanelWidth = (double) FsmEditor.InspectorPanelWidth;
        position = this.window.position;
        double height1 = (double) position.height;
        local2.Set((float) num6, 0.0f, (float) inspectorPanelWidth, (float) height1);
        ref Rect local3 = ref this.graphViewRect;
        double num7 = (double) num1;
        double height2 = (double) this.toolbarRect.height;
        double width = (double) this.toolbarRect.width;
        position = this.window.position;
        double num8 = (double) position.height - (double) this.toolbarRect.height * 2.0;
        local3.Set((float) num7, (float) height2, (float) width, (float) num8);
        ref Rect local4 = ref this.debugToolbarRect;
        double num9 = (double) num1;
        position = this.window.position;
        double num10 = (double) position.height - (double) EditorStyles.toolbar.fixedHeight;
        position = this.window.position;
        double num11 = (double) position.width - (double) FsmEditor.InspectorPanelWidth;
        double fixedHeight2 = (double) EditorStyles.toolbar.fixedHeight;
        local4.Set((float) num9, (float) num10, (float) num11, (float) fixedHeight2);
        this.toolbar.OnGUI(this.toolbarRect);
        this.inspector.OnGUI(this.inspectorPanelRect);
        GUIHelpers.BeginGuiColor(new Color(1f, 1f, 1f, 2f));
        this.graphView.OnGUI(this.graphViewRect);
        GUIHelpers.EndGuiColor();
        DebugToolbar.OnGUI(this.debugToolbarRect);
        FsmEditor.HandleKeyboardInput();
        DragAndDropManager.Update();
        EditorGUI.EndDisabledGroup();
      }
    }

    private void DoSplitter()
    {
      Rect position;
      if (this.draggingSplitter)
      {
        this.splitterRect.Set(0.0f, 0.0f, this.window.position.width, this.window.position.height);
      }
      else
      {
        float num1 = FsmEditorSettings.InspectorOnLeft ? this.inspectorPanelRect.xMax : this.inspectorPanelRect.x;
        ref Rect local = ref this.splitterRect;
        double num2 = (double) num1 - 2.0;
        position = this.window.position;
        double height = (double) position.height;
        local.Set((float) num2, 0.0f, 6f, (float) height);
      }
      EditorGUIUtility.AddCursorRect(this.splitterRect, MouseCursor.ResizeHorizontal);
      if (this.splitterRect.Contains(Event.current.mousePosition) && Event.current.type == UnityEngine.EventType.MouseDown)
      {
        this.draggingSplitter = true;
        Event.current.Use();
      }
      if (!this.draggingSplitter)
        return;
      if (Event.current.type == UnityEngine.EventType.MouseDrag)
      {
        float x = Event.current.mousePosition.x;
        position = this.window.position;
        float width = position.width;
        float num1 = FsmEditorSettings.InspectorOnLeft ? 350f : width - 700f;
        float num2 = FsmEditorSettings.InspectorOnLeft ? 700f : width - 350f;
        int num3 = FsmEditorSettings.InspectorOnLeft ? 1 : -1;
        if ((double) x < (double) num2 && (double) x > (double) num1)
        {
          FsmEditor.InspectorPanelWidth += Event.current.delta.x * (float) num3;
          FsmEditor.InspectorPanelWidth = Mathf.Clamp(FsmEditor.InspectorPanelWidth, 350f, 700f);
        }
        EditorPrefs.SetFloat("PlayMaker.InspectorPanelWidth", FsmEditor.InspectorPanelWidth);
        Event.current.Use();
      }
      if (Event.current.rawType == UnityEngine.EventType.MouseUp)
        this.draggingSplitter = false;
      FsmEditor.Repaint();
    }

    private void DoRepaint()
    {
      this.window.Repaint();
      HandleUtility.Repaint();
      SceneView.RepaintAll();
      this.repaint = false;
      FsmStateAction.Repaint = false;
    }

    public void OnSceneGUICallback(SceneView scnView) => FsmEditor.OnSceneGUI();

    public static void OnSceneGUI()
    {
      if (FsmEditor.SelectedState == null || ActionEditor.PreviewMode || (!FsmEditor.SelectedState.IsInitialized || FsmEditor.Inspector.Mode != InspectorMode.StateInspector))
        return;
      foreach (FsmStateAction action in FsmEditor.SelectedState.Actions)
      {
        if (action.Enabled && action.IsOpen && CustomActionEditors.HasCustomEditor(action.GetType()))
        {
          action.Fsm = FsmEditor.SelectedFsm;
          CustomActionEditor customEditor = CustomActionEditors.GetCustomEditor(action);
          if (customEditor != null)
          {
            customEditor.OnSceneGUI();
            if (GUI.changed)
            {
              FsmEditor.RecordUndo("Edit State");
              FsmEditor.EditingActions();
              FsmEditor.Repaint(true);
            }
          }
        }
      }
    }

    private void HandleMouseInput()
    {
      Event current = Event.current;
      UnityEngine.EventType type = current.type;
      if (current.isMouse || type == UnityEngine.EventType.DragUpdated || type == UnityEngine.EventType.DragPerform)
      {
        Vector2 mousePosition = current.mousePosition;
        FsmEditor.MouseOverInspector = (double) mousePosition.x > (double) this.window.position.width - (double) FsmEditor.InspectorPanelWidth && (double) mousePosition.y > 0.0 && (double) mousePosition.y < (double) this.window.position.height;
      }
      if (current.rawType == UnityEngine.EventType.MouseUp)
        this.mouseUp = true;
      if (type == UnityEngine.EventType.MouseDown)
      {
        this.mouseDown = true;
        FsmEditor.InspectorHasFocus = FsmEditor.MouseOverInspector;
      }
      if (!FsmEditor.MouseOverInspector)
        return;
      FsmEditor.InvalidatePixelCache();
    }

    public static void MouseUpHandled()
    {
      if (FsmEditor.Instance == null)
        return;
      FsmEditor.Instance.mouseUp = false;
    }

    private static void HandleKeyboardInput()
    {
      Keyboard.Update();
      if (!Keyboard.IsGuiEventKeyboardShortcut())
        return;
      switch (Event.current.keyCode)
      {
        case KeyCode.Backspace:
        case KeyCode.Delete:
          if (FsmEditor.InspectorHasFocus && FsmEditor.Inspector.Mode == InspectorMode.StateInspector)
          {
            FsmEditor.StateInspector.DeleteSelectedActions();
            break;
          }
          EditorCommands.DeleteMultiSelection();
          break;
      }
    }

    public void OnInspectorUpdate()
    {
      if (FsmEditor.updateIsModifiedPrefabInstance)
      {
        FsmEditor.updateIsModifiedPrefabInstance = false;
        EditorApplication.update -= new EditorApplication.CallbackFunction(FsmEditor.CheckFsmPrefabInstance);
        EditorApplication.update += new EditorApplication.CallbackFunction(FsmEditor.CheckFsmPrefabInstance);
      }
      if (!Application.isPlaying || (!FsmEditorSettings.DebugActionParameters || FsmEditor.Inspector.Mode != InspectorMode.StateInspector) && (!FsmEditorSettings.DebugVariables || FsmEditor.Inspector.Mode != InspectorMode.VariableManager))
        return;
      FsmEditor.Repaint(true);
    }

    private void FirstUpdate()
    {
      if (Application.isPlaying)
        return;
      if (FsmEditorSettings.DisconnectModifiedInstancesInScene)
        FsmEditor.CheckAllFsmPrefabInstances();
      if (FsmEditorSettings.LoadAllTemplates)
        Templates.LoadAll();
      FsmEditor.RepaintAll();
    }

    private void PrefabInstanceUpdated(GameObject go)
    {
      foreach (PlayMakerFSM componentsInChild in go.GetComponentsInChildren<PlayMakerFSM>())
        componentsInChild.Fsm.Reload();
      if (!((UnityEngine.Object) go == (UnityEngine.Object) FsmEditor.SelectedFsmGameObject))
        return;
      FsmEditor.ReselectFsm();
      FsmEditor.GraphView.UpdateGraphBounds();
    }

    [Localizable(false)]
    public void Update()
    {
      FsmEditor.breakPrefabAlreadyConfirmedThisUpdate = false;
      if (FsmEditor.SelectedFsmOwner != (UnityEngine.Object) null)
      {
        PrefabAssetType prefabAssetType = PrefabUtility.GetPrefabAssetType(FsmEditor.SelectedFsmOwner);
        if (prefabAssetType != this.prefabTypeLastUpdate)
        {
          FsmEditor.ReselectFsm();
          this.prefabTypeLastUpdate = prefabAssetType;
        }
      }
      if ((UnityEngine.Object) this.window == (UnityEngine.Object) null)
        return;
      if (FsmEditor.OnUpdate != null)
        FsmEditor.OnUpdate();
      FsmTime.Update();
      if (Application.isPlaying && FsmEditorSettings.DisableEditorWhenPlaying)
        return;
      if (!string.IsNullOrEmpty(FsmEditor.sendWindowCommandEvent))
      {
        this.window.SendEvent(EditorGUIUtility.CommandEvent(FsmEditor.sendWindowCommandEvent));
        FsmEditor.sendWindowCommandEvent = "";
      }
      if (this.openReport)
        this.DoOpenReport();
      if (FsmEditorSettings.SelectFSMInGameView)
      {
        if ((!FsmEditorSettings.LockGraphView || FsmEditor.SelectedFsm == null) && ((UnityEngine.Object) Fsm.LastClickedObject != (UnityEngine.Object) null && (UnityEngine.Object) FsmEditor.SelectedFsmGameObject != (UnityEngine.Object) Fsm.LastClickedObject))
        {
          UnityEditor.Selection.activeGameObject = Fsm.LastClickedObject;
          EditorGUIUtility.PingObject((UnityEngine.Object) FsmEditor.SelectedFsmGameObject);
        }
        Fsm.LastClickedObject = (GameObject) null;
      }
      if (this.repaintAll)
      {
        this.RepaintAllWindows();
        this.repaintAll = false;
        this.repaint = false;
      }
      this.graphView.Update();
      this.inspector.Update();
      FsmDebugger.Instance.Update();
      this.mouseUp = false;
      this.mouseDown = false;
      FsmErrorChecker.Update();
      DrawState drawState = FsmDrawState.GetDrawState(FsmEditor.SelectedFsm);
      FsmStateAction.ActiveHighlightColor = FsmEditorStyles.ActionColors[(int) drawState];
    }

    public void RepaintAllWindows()
    {
      foreach (EditorWindow editorWindow in Resources.FindObjectsOfTypeAll<BaseEditorWindow>())
        editorWindow.Repaint();
    }

    public void OnSelectionChange()
    {
      FsmEditor.Repaint(true);
      if (FsmEditorSettings.LockGraphView && FsmEditor.SelectedFsm != null)
        return;
      UnityEngine.Object activeObject = UnityEditor.Selection.activeObject;
      GameObject gameObject = activeObject as GameObject;
      if ((UnityEngine.Object) gameObject != (UnityEngine.Object) null)
      {
        if (!PrefabUtility.IsPartOfPrefabAsset((UnityEngine.Object) gameObject))
          FsmEditor.SelectFsm(UnityEditor.Selection.activeGameObject);
        else
          FsmEditor.SelectNone();
      }
      else
      {
        FsmTemplate fsmTemplate = activeObject as FsmTemplate;
        if ((UnityEngine.Object) fsmTemplate != (UnityEngine.Object) null)
        {
          FsmEditor.SelectFsm(fsmTemplate.fsm);
        }
        else
        {
          int num = (UnityEngine.Object) (activeObject as Preset) != (UnityEngine.Object) null ? 1 : 0;
          FsmEditor.SelectNone();
        }
      }
    }

    public void OnHierarchyChange()
    {
      if (FsmEditor.IgnoreHierarchyChange)
      {
        FsmEditor.IgnoreHierarchyChange = false;
      }
      else
      {
        FsmEditor.RebuildFsmList();
        FsmEditor.UpdateLabels(FsmEditor.SelectedFsm);
        if (this.graphView.IsDragging)
          return;
        if ((UnityEngine.Object) FsmEditor.SelectedFsmComponent != (UnityEngine.Object) null && FsmEditor.SelectedFsm != null && !FsmEditor.SelectedFsm.IsSubFsm)
          FsmEditor.SelectFsm(FsmEditor.SelectedFsmComponent.Fsm);
        FsmEditor.SanityCheckSelection();
        this.OnSelectionChange();
        if (!Application.isPlaying)
        {
          if (FsmEditorSettings.EnableRealtimeErrorChecker)
            FsmErrorChecker.CheckForErrors();
          GlobalEventsWindow.ResetView();
        }
        GlobalVariablesWindow.ResetView();
        FsmPrefabs.UpdateIsModifiedPrefabInstance(FsmEditor.SelectedFsm);
      }
    }

    public void OnFocus()
    {
      if (FsmEditor.Selection != null)
        FsmEditor.Selection.SanityCheck();
      if (FsmEditor.GraphView == null)
        return;
      FsmEditor.GraphView.UpdateVisibility();
    }

    public void OnProjectChange() => this.Init();

    public void OnDisable()
    {
      if (FsmEditor.Instance != this)
        return;
      FsmEditor.DoDirtyFsmPrefab();
      DebugFlow.Cleanup();
      CustomActionEditors.ClearCache();
      HighlighterHelper.ClearActiveHighlight();
      FsmEditor.fsmComponentList = (List<PlayMakerFSM>) null;
      FsmEditor.fsmList = (List<Fsm>) null;
      FsmEditor.OnUpdate = (Action) null;
      FsmEditor.OnRepaint = (Action) null;
      this.RemoveDelegates();
      this.Initialized = false;
      FsmEditor.Instance = (FsmEditor) null;
    }

    private void OnSceneOpened(Scene scene, OpenSceneMode mode)
    {
      if (FsmEditorSettings.AutoLoadPrefabs)
        FsmPrefabs.LoadUsedPrefabs();
      FsmEditor.MarkFsmListDirty();
      if (FsmEditorSettings.DisconnectModifiedInstancesInScene)
        FsmEditor.CheckAllFsmPrefabInstances();
      FsmEditor.SanityCheckSelection();
    }

    private void OnSceneClosed(Scene scene)
    {
      if (PlayMakerFSM.ApplicationIsQuitting)
        return;
      FsmEditor.MarkFsmListDirty();
      FsmEditor.SanityCheckSelection();
    }

    private void OnPlayModeChanged(PlayModeStateChange playMode)
    {
      PlayMakerGlobals.InitApplicationFlags();
      GameStateTracker.Update();
      switch (playMode)
      {
        case PlayModeStateChange.EnteredEditMode:
          PlayMakerFSM.ApplicationIsQuitting = false;
          this.Init();
          FsmEditor.Window.RemoveNotification();
          DebugFlow.Stop();
          this.ResetViews();
          break;
        case PlayModeStateChange.ExitingEditMode:
          FsmLog.LoggingEnabled = FsmEditorSettings.EnableLogging;
          FsmLog.MirrorDebugLog = FsmEditorSettings.MirrorDebugLog;
          FsmLog.EnableDebugFlow = FsmEditorSettings.EnableDebugFlow;
          break;
        case PlayModeStateChange.EnteredPlayMode:
          FsmEditor.ReselectFsm();
          break;
      }
    }

    private void OnPlayerPauseChanged(PauseState pauseState)
    {
      GameStateTracker.Update();
      if (FsmEditorSettings.EnableDebugFlow)
      {
        if (EditorApplication.isPaused)
          DebugFlow.Start(FsmEditor.SelectedFsm);
        else
          DebugFlow.Stop();
      }
      FsmEditor.Repaint(true);
      FsmEditor.RepaintAll();
    }

    public static void ToggleLockSelection()
    {
      FsmEditorSettings.LockGraphView = !FsmEditorSettings.LockGraphView;
      FsmEditorSettings.SaveSettings();
      FsmEditor.UpdateLockSelection();
      FsmEditor.RepaintAll();
    }

    public static void UpdateLockSelection()
    {
      if (FsmEditor.Instance == null || FsmEditor.Instance == null || FsmEditorSettings.LockGraphView)
        return;
      FsmEditor.Instance.OnSelectionChange();
    }

    public static void ReselectFsm()
    {
      Fsm selectedFsm = FsmEditor.SelectedFsm;
      FsmEditor.SelectNone();
      FsmEditor.SelectFsm(selectedFsm);
    }

    public static void SelectFsm(GameObject gameObject) => FsmEditor.SelectFsm(FsmEditor.SelectionHistory.GetFsmSelection(gameObject));

    public static void SelectFsm(PlayMakerFSM fsmComponent)
    {
      if ((UnityEngine.Object) fsmComponent != (UnityEngine.Object) null)
        FsmEditor.SelectFsm(fsmComponent.Fsm);
      else
        FsmEditor.SelectNone();
    }

    public static void SelectFsm(FsmTemplate fsmTemplate)
    {
      if ((UnityEngine.Object) fsmTemplate == (UnityEngine.Object) null)
        return;
      FsmEditor.SelectFsm(fsmTemplate.fsm);
    }

    public static void SelectNone()
    {
      FsmEditor.StopEditingPrefabInstance();
      if (FsmEditor.SelectedFsm == null)
        return;
      FsmEditor.DoDirtyFsmPrefab();
      if (FsmEditor.SelectedFsm != null)
        FsmEditor.SelectedFsm.EditState = (FsmState) null;
      if (FsmEditor.Instance != null)
      {
        FsmEditor.Instance.fsmSelection = FsmSelection.None;
        FsmEditor.Instance.ResetViews();
      }
      CustomActionEditors.ClearCache();
      FsmTemplateControlEditor.ClearCache();
    }

    public static void SelectFsm(Fsm fsm)
    {
      if (FsmEditor.Instance == null)
        FsmEditor.SelectFsmDelayed(fsm);
      else if (FsmPrefabs.IsPrefab(fsm))
      {
        FsmEditor.OpenPrefab(fsm);
      }
      else
      {
        if (fsm != null)
        {
          fsm.InitData();
          Labels.Update(fsm);
          FsmEditor.Instance.RemoveDelegatesFromSelectedFsm();
          FsmEditor.Instance.LoadEditorOnlyData(fsm);
        }
        if (fsm == FsmEditor.SelectedFsm)
        {
          FsmEditor.Instance.AddDelegatesToSelectedFsm();
        }
        else
        {
          FsmEditor.SelectNone();
          Keyboard.ResetFocus();
          DragAndDropManager.Reset();
          FsmEditor.Instance.fsmSelection = FsmEditor.Instance.selectionHistory.SelectFsm(fsm);
          PlayMakerGUI.SelectedFSM = FsmEditor.SelectedFsm;
          FsmEditor.Builder.SetTarget(FsmEditor.SelectedFsm);
          FsmEditor.FsmInspector.SetTarget(FsmEditor.SelectedFsm);
          if (FsmEditor.SelectedFsm != null)
          {
            FsmEditor.updateIsModifiedPrefabInstance = true;
            FsmEditor.GraphView.FsmSelected();
            if ((UnityEngine.Object) FsmEditor.SelectedTemplate == (UnityEngine.Object) null)
              FsmEditor.AutoAddPlayMakerGUI();
            if (FsmEditor.SelectedState != null)
              FsmEditor.SelectedFsm.EditState = FsmEditor.SelectedState;
            if (!FsmEditor.SelectedFsm.Initialized)
              FsmEditor.SelectedFsm.Init((MonoBehaviour) FsmEditor.SelectedFsmComponent);
            FsmEditor.SanityCheckFsm(FsmEditor.SelectedFsm);
            FsmErrorChecker.CheckFsmForErrors(FsmEditor.SelectedFsm);
          }
          if (!FsmEditorSettings.LockGraphView && FsmEditorSettings.AutoSelectGameObject)
            FsmEditor.Selection.SelectActiveFsmGameObject();
          if (Application.isPlaying && FsmEditorSettings.SelectStateOnActivated && FsmEditor.SelectedFsm != null)
            FsmEditor.SelectState(FsmEditor.SelectedFsm.ActiveState, true);
          DebugFlow.SyncFsmLog(FsmEditor.SelectedFsm);
          Fsm.StepToStateChange = false;
          FsmEditor.Instance.AddDelegatesToSelectedFsm();
          FsmEditor.Instance.ResetViews();
          FsmEditor.Repaint(true);
          FsmEditor.RepaintAll();
        }
      }
    }

    private void LoadEditorOnlyData(Fsm fsm)
    {
      if (fsm == null)
        return;
      fsm.InitEditorData();
    }

    public static void RefreshInspector()
    {
      if (FsmEditor.Instance == null)
        return;
      FsmEditor.Inspector.ResetView();
    }

    public static void MarkFsmListDirty()
    {
      FsmEditor.fsmComponentList = (List<PlayMakerFSM>) null;
      FsmEditor.fsmList = (List<Fsm>) null;
    }

    public static void RebuildFsmList()
    {
      List<Fsm> first = FsmEditor.fsmComponentList != null ? new List<Fsm>((IEnumerable<Fsm>) FsmEditor.fsmList) : new List<Fsm>();
      FsmEditor.fsmList = new List<Fsm>();
      FsmEditor.fsmComponentList = new List<PlayMakerFSM>();
      FsmEditor.fsmComponentList.AddRange((IEnumerable<PlayMakerFSM>) new List<PlayMakerFSM>((IEnumerable<PlayMakerFSM>) Resources.FindObjectsOfTypeAll(typeof (PlayMakerFSM))));
      foreach (PlayMakerFSM fsmComponent in FsmEditor.fsmComponentList)
      {
        if (fsmComponent.Fsm != null)
          FsmEditor.fsmList.Add(fsmComponent.Fsm);
      }
      if (!Application.isPlaying)
      {
        Templates.InitList();
        foreach (FsmTemplate fsmTemplate in Templates.List)
        {
          if (fsmTemplate.fsm != null && AssetDatabase.Contains((UnityEngine.Object) fsmTemplate))
            FsmEditor.fsmList.Add(fsmTemplate.fsm);
        }
      }
      foreach (Fsm fsm in FsmEditor.fsmList)
      {
        if (!Application.isPlaying)
          fsm.CheckIfDirty();
        foreach (FsmState state in fsm.States)
          state.Fsm = fsm;
      }
      if (!FsmEditor.FsmList.Contains(FsmEditor.SelectedFsm))
        FsmEditor.SelectNone();
      if (first.Count == FsmEditor.fsmList.Count && first.SequenceEqual<Fsm>((IEnumerable<Fsm>) FsmEditor.fsmList))
        return;
      FsmSearch.Invalidate();
      FsmPrefabs.BuildAssetsWithPlayMakerFSMsList();
      EditorApplication.RepaintHierarchyWindow();
      EditorApplication.RepaintProjectWindow();
    }

    public static void AddToFsmList(PlayMakerFSM fsmComponent)
    {
      if ((UnityEngine.Object) fsmComponent == (UnityEngine.Object) null)
        return;
      FsmEditor.FsmComponentList.Add(fsmComponent);
      FsmEditor.FsmList.Add(fsmComponent.Fsm);
      EditorApplication.RepaintHierarchyWindow();
    }

    public static void RemoveFromList(PlayMakerFSM fsmComponent)
    {
      if ((UnityEngine.Object) fsmComponent == (UnityEngine.Object) null)
        return;
      FsmEditor.fsmComponentList.Remove(fsmComponent);
      FsmEditor.fsmList.Remove(fsmComponent.Fsm);
    }

    public static void RemoveFromList(Fsm fsm)
    {
      if (fsm == null)
        return;
      FsmEditor.fsmList.Remove(fsm);
      if (!((UnityEngine.Object) fsm.FsmComponent != (UnityEngine.Object) null))
        return;
      FsmEditor.fsmComponentList.Remove(fsm.FsmComponent);
    }

    public static FsmState SelectState(FsmState state, bool frameState)
    {
      if (FsmEditor.Instance == null || FsmEditor.SelectedFsmIsLocked)
        return (FsmState) null;
      FsmEditor.Instance.graphView.UpdateVisibility();
      if (FsmEditor.SelectedFsm != null)
        FsmEditor.SelectedFsm.EditState = state;
      if (state != null && FsmEditor.SelectedFsm != state.Fsm)
        FsmEditor.SelectFsm(state.Fsm);
      if (state != FsmEditor.SelectedState)
      {
        state = FsmEditor.Selection.SelectState(state);
        FsmEditor.GraphView.CancelDraggingStates();
        FsmEditor.InvalidatePixelCache();
      }
      if (FsmEditorSettings.FrameSelectedState | frameState && FsmEditor.GraphView.IsStateOffscreen(state))
        FsmEditor.GraphView.FrameState(state, true);
      if (!Application.isPlaying)
        FsmEditor.Inspector.SetMode(InspectorMode.StateInspector);
      FsmEditor.Repaint(true);
      FsmEditor.RepaintAll();
      return state;
    }

    public static FsmStateAction SelectAction(FsmStateAction action, bool autoScroll = true)
    {
      if (FsmEditor.Instance == null)
        return (FsmStateAction) null;
      FsmEditor.Instance.stateInspector.SelectAction(action, autoScroll);
      return action;
    }

    public static FsmStateAction SelectAction(
      FsmState state,
      int actionIndex,
      bool autoScroll = true)
    {
      return state == null || actionIndex < 0 || actionIndex >= state.Actions.Length ? (FsmStateAction) null : FsmEditor.SelectAction(state.Actions[actionIndex], autoScroll);
    }

    public static void SelectTemplateDelayed(FsmTemplate template) => EditorApplication.delayCall += (EditorApplication.CallbackFunction) (() => FsmEditor.SelectFsm(template));

    public static void SelectFsmDelayed(Fsm fsm) => EditorApplication.delayCall += (EditorApplication.CallbackFunction) (() => FsmEditor.SelectFsm(fsm));

    public static void AddFsmComponent() => FsmBuilder.AddFsmToSelected();

    public static void GotoBreakpoint()
    {
      if (Fsm.BreakAtFsm == null)
        return;
      FsmEditor.SelectFsm(Fsm.BreakAtFsm);
      FsmEditor.SelectState(Fsm.BreakAtState, true);
    }

    public static void SelectFsm(object userdata) => FsmEditor.SelectFsm(userdata as Fsm);

    public static void AddFsm() => FsmEditor.AddFsmComponent();

    public static void SelectStateFromMenu(object userdata)
    {
      FsmEditor.SelectStateByName(userdata as string);
      FsmEditor.GraphView.FrameState(FsmEditor.Selection.ActiveState);
    }

    public static FsmState SelectStateByName(string stateName, bool frameState = true) => FsmEditor.SelectState(FsmEditor.SelectedFsm.GetState(stateName), frameState);

    public static bool EditingPrefabInstance() => FsmEditor.editingPrefabInstance != null && FsmEditor.editingPrefabInstance == FsmEditor.SelectedFsm;

    public static void StartEditingPrefabInstance() => FsmEditor.editingPrefabInstance = FsmEditor.SelectedFsm;

    public static void StopEditingPrefabInstance() => FsmEditor.editingPrefabInstance = (Fsm) null;

    public static void ApplyPrefabInstanceChanges()
    {
      FsmPrefabs.Apply((MonoBehaviour) FsmEditor.SelectedFsmComponent);
      FsmEditor.StopEditingPrefabInstance();
    }

    public static void RevertPrefabInstanceChanges()
    {
      FsmPrefabs.Revert(FsmEditor.SelectedFsmGameObject, FsmEditor.SelectedFsmOwner);
      FsmEditor.StopEditingPrefabInstance();
    }

    public static bool SelectedFsmIsModifiedPrefabInstance => FsmEditor.SelectedFsm != null && FsmEditor.SelectedFsm.IsModifiedPrefabInstance;

    public static bool SelectedFsmIsDisconnected => FsmEditor.SelectedFsmOwner != (UnityEngine.Object) null && PrefabUtility.IsDisconnectedFromPrefabAsset(FsmEditor.SelectedFsmOwner);

    public static bool SelectedFsmIsPrefab() => FsmPrefabs.IsPrefab(FsmEditor.SelectedFsm);

    public static bool SelectedFsmIsAddedComponentOverride() => FsmPrefabs.IsAddedComponentOverride(FsmEditor.SelectedFsm);

    public static bool SelectedFsmIsPrefabInstance() => FsmPrefabs.IsPrefabInstance(FsmEditor.SelectedFsm);

    public static bool SelectedFsmIsPrefabOrInstance() => FsmPrefabs.IsPrefab(FsmEditor.SelectedFsm) || FsmPrefabs.IsPrefabInstance(FsmEditor.SelectedFsm);

    public static bool SelectedFsmIsPersistent() => (UnityEngine.Object) FsmEditor.SelectedTemplate != (UnityEngine.Object) null || FsmPrefabs.IsPrefab(FsmEditor.SelectedFsm);

    public static void SelectFsmGameObject()
    {
      if ((UnityEngine.Object) FsmEditor.SelectedFsmGameObject == (UnityEngine.Object) null)
        return;
      UnityEditor.Selection.activeGameObject = FsmEditor.SelectedFsmGameObject;
      EditorGUIUtility.PingObject((UnityEngine.Object) FsmEditor.SelectedFsmGameObject);
    }

    public static void InstantiatePrefab()
    {
      if ((UnityEngine.Object) FsmEditor.SelectedFsmGameObject == (UnityEngine.Object) null)
        return;
      GameObject gameObject = (GameObject) PrefabUtility.InstantiatePrefab((UnityEngine.Object) FsmEditor.SelectedFsmGameObject);
      Undo.RegisterCreatedObjectUndo((UnityEngine.Object) gameObject, Strings.Command_Add_Template_to_Selected);
      UnityEditor.Selection.activeGameObject = gameObject;
    }

    public static void OpenPrefabParent() => FsmEditor.OpenPrefabParent(FsmEditor.SelectedFsm);

    public static void OpenPrefabParent(Fsm fsm)
    {
      if (fsm == null || (UnityEngine.Object) fsm.FsmComponent == (UnityEngine.Object) null || !((UnityEngine.Object) PrefabUtility.GetCorrespondingObjectFromSource<PlayMakerFSM>(fsm.FsmComponent) != (UnityEngine.Object) null))
        return;
      string nearestInstanceRoot = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot((UnityEngine.Object) fsm.FsmComponent);
      if (string.IsNullOrEmpty(nearestInstanceRoot))
        return;
      int prefabComponentIndex = FsmPrefabs.GetPrefabComponentIndex((UnityEngine.Component) fsm.FsmComponent);
      if (!EditorHacks.OpenPrefab(nearestInstanceRoot, fsm.FsmComponent.gameObject))
        return;
      PlayMakerFSM[] components = UnityEditor.Selection.activeGameObject.GetComponents<PlayMakerFSM>();
      if (components.Length <= prefabComponentIndex)
        return;
      FsmEditor.SelectFsm(components[prefabComponentIndex]);
    }

    public static void OpenPrefab(Fsm fsm)
    {
      if (fsm == null || (UnityEngine.Object) fsm.FsmComponent == (UnityEngine.Object) null)
        return;
      string nearestInstanceRoot = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot((UnityEngine.Object) fsm.FsmComponent);
      if (string.IsNullOrEmpty(nearestInstanceRoot))
        return;
      AssetDatabase.OpenAsset(AssetDatabase.LoadMainAssetAtPath(nearestInstanceRoot));
      PrefabStage currentPrefabStage = PrefabStageUtility.GetCurrentPrefabStage();
      if (currentPrefabStage == null)
        return;
      string fullPath = FsmUtility.GetFullPath(fsm);
      foreach (PlayMakerFSM componentsInChild in currentPrefabStage.prefabContentsRoot.GetComponentsInChildren<PlayMakerFSM>())
      {
        if (FsmUtility.GetFullPath(componentsInChild.Fsm) == fullPath)
        {
          FsmEditor.SelectFsm(componentsInChild);
          break;
        }
      }
    }

    public static void SelectPrefabParent()
    {
      GameObject prefabInstanceRoot = PrefabUtility.GetOutermostPrefabInstanceRoot((UnityEngine.Object) FsmEditor.SelectedFsmComponent);
      if ((UnityEngine.Object) prefabInstanceRoot == (UnityEngine.Object) null)
        return;
      GameObject objectFromSource = PrefabUtility.GetCorrespondingObjectFromSource<GameObject>(prefabInstanceRoot);
      if ((UnityEngine.Object) objectFromSource == (UnityEngine.Object) null)
        return;
      UnityEditor.Selection.activeGameObject = AssetDatabase.LoadAssetAtPath<GameObject>(PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot((UnityEngine.Object) objectFromSource));
      FsmEditor.SelectNone();
    }

    public static void SelectPrefab(GameObject go)
    {
      Fsm fsmSelection = FsmEditor.SelectionHistory.GetFsmSelection(go);
      if (fsmSelection == null)
        return;
      FsmEditor.SelectPrefab(fsmSelection.FsmComponent);
    }

    public static void SelectPrefab(PlayMakerFSM fsmComponent)
    {
      if ((UnityEngine.Object) fsmComponent == (UnityEngine.Object) null)
        return;
      FsmEditor.OpenPrefab(fsmComponent.Fsm);
    }

    public static void ResetToPrefabState()
    {
      if (!((UnityEngine.Object) FsmEditor.SelectedFsmGameObject != (UnityEngine.Object) null))
        return;
      PrefabUtility.RevertObjectOverride((UnityEngine.Object) FsmEditor.SelectedFsm.Owner, InteractionMode.UserAction);
      FsmEditor.Inspector.ResetView();
      FsmEditor.RepaintAll();
    }

    public static void ReconnectToLastPrefab()
    {
      if (!((UnityEngine.Object) FsmEditor.SelectedFsmGameObject != (UnityEngine.Object) null))
        return;
      PrefabUtility.RevertPrefabInstance(FsmEditor.SelectedFsmGameObject, InteractionMode.UserAction);
    }

    public static void ReloadFSM()
    {
      if (EditorApplication.isPlayingOrWillChangePlaymode || FsmEditor.SelectedFsm == null)
        return;
      FsmEditor.SelectedFsm.Reload();
      FsmEditor.GraphView.UpdateVisibility();
      FsmEditor.CheckFsmForErrors();
    }

    public static void SaveActions(bool undo = true) => FsmEditor.SaveActions(FsmEditor.SelectedFsm, undo);

    public static void SaveActions(Fsm fsm, bool undo = true)
    {
      if (fsm == null)
      {
        UnityEngine.Debug.LogWarning((object) "fsm == null!");
      }
      else
      {
        if (undo)
          UndoUtility.RecordObject(fsm.OwnerObject, "Edit Action");
        foreach (FsmState state in fsm.States)
          state.SaveActions();
        FsmErrorChecker.CheckFsmForErrors(fsm);
      }
    }

    public static void SaveActions(FsmState state, bool errorCheck = true, bool undo = true)
    {
      if (state == null)
        return;
      if (undo && state.Fsm != null)
        UndoUtility.RecordObject(state.Fsm.OwnerObject, "Edit Action");
      if (errorCheck)
        FsmErrorChecker.CheckFsmForErrors(state.Fsm);
      state.SaveActions();
      Undo.FlushUndoRecordObjects();
    }

    public static void EditingActions()
    {
      if (FsmEditor.Instance == null)
        return;
      FsmEditor.StateInspector.EditingActions();
    }

    public static void CheckFsmForErrors() => FsmErrorChecker.CheckFsmForErrors(FsmEditor.SelectedFsm);

    public static void SetFsmDirty() => FsmEditor.SetFsmDirty(false);

    public static void SetFsmDirty(bool errorCheck, bool checkAll = false)
    {
      if (FsmEditor.SelectedFsm == null)
        return;
      FsmEditor.InvalidatePixelCache();
      FsmEditor.SetFsmDirty(FsmEditor.SelectedFsm, errorCheck, checkAll);
    }

    public static void OnChanged(Fsm fsm = null)
    {
      if (fsm == null)
        fsm = FsmEditor.SelectedFsm;
      if (fsm == null || FsmEditor.OnFsmChanged == null)
        return;
      FsmEditor.OnFsmChanged(fsm);
    }

    public static void OnControlsChanged(Fsm fsm = null)
    {
      if (fsm == null)
        fsm = FsmEditor.SelectedFsm;
      if (fsm == null || FsmEditor.OnFsmControlsChanged == null)
        return;
      FsmEditor.OnFsmControlsChanged(fsm);
    }

    public static void SetFsmDirty(Fsm fsm, bool errorCheck, bool checkAll = false, bool modifiedWarning = true)
    {
      if (fsm == null)
        return;
      FsmSearch.Invalidate(fsm);
      FsmEditor.UpdateLabels(fsm);
      FsmEditor.updateIsModifiedPrefabInstance = true;
      if (!EditorApplication.isPlayingOrWillChangePlaymode)
        fsm.ForcePreprocess();
      if ((UnityEngine.Object) fsm.UsedInTemplate != (UnityEngine.Object) null)
        EditorUtility.SetDirty((UnityEngine.Object) fsm.UsedInTemplate);
      else if ((UnityEngine.Object) fsm.Owner != (UnityEngine.Object) null)
      {
        FsmPrefabs.DisconnectIfModifiedInstance(fsm);
        EditorUtility.SetDirty((UnityEngine.Object) fsm.Owner);
      }
      else
        UnityEngine.Debug.LogWarning((object) ("PlayMaker: Unhandled SetDirty: " + Labels.GetFullFsmLabel(fsm)));
      if (FsmEditor.Instance != null)
      {
        if (errorCheck && FsmEditorSettings.EnableRealtimeErrorChecker)
        {
          if (checkAll)
            FsmErrorChecker.CheckForErrors();
          else
            FsmErrorChecker.CheckFsmForErrors(fsm);
        }
        FsmEditor.Instance.graphView.UpdateVisibility();
        FsmEditor.RepaintAll();
        if (Application.isPlaying & modifiedWarning && !FsmEditor.warnedAboutEditingWhileRunning && FsmEditorSettings.ShowEditWhileRunningWarning)
        {
          FsmEditor.Window.ShowNotification(FsmPrefabs.IsPersistent(FsmEditor.SelectedFsm) ? new GUIContent(Strings.Dialog_Editing_Prefab_while_game_is_running) : new GUIContent(Strings.Dialog_Editing_FSM_while_game_is_running));
          FsmEditor.warnedAboutEditingWhileRunning = true;
        }
      }
      FsmEditor.IgnoreHierarchyChange = true;
      FsmEditor.IgnoreOnValidate = true;
      FsmEditor.OnChanged(fsm);
    }

    public static void UpdateViews()
    {
      if (FsmEditor.Instance == null)
        return;
      FsmEditor.Instance.ResetViews();
    }

    public static void UpdateLabels(Fsm fsm = null)
    {
      if (fsm == null)
        fsm = FsmEditor.SelectedFsm;
      if (fsm == null)
        return;
      Labels.Update(fsm);
      if (FsmEditor.GraphView != null)
        FsmEditor.GraphView.UpdateGraphLabel();
      FsmEditor.RepaintAll();
    }

    public static void UpdateEditors(Fsm fsm = null)
    {
      if (fsm == null)
        fsm = FsmEditor.SelectedFsm;
      if (fsm == null)
        return;
      Labels.Update(fsm);
      CustomActionEditors.ClearCache();
      if (FsmEditor.Instance != null)
        FsmEditor.Instance.ResetViews();
      FsmEditor.RepaintAll();
    }

    public static bool CreateAsset(UnityEngine.Object asset, ref string path)
    {
      path = path.Replace("//", "/");
      if (asset == (UnityEngine.Object) null)
      {
        UnityEngine.Debug.LogError((object) "Can't save null asset!");
        return false;
      }
      AssetDatabase.CreateAsset(asset, path);
      AssetDatabase.Refresh();
      return true;
    }

    public static void SaveGlobals() => FsmEditor.SaveGlobals(PlayMakerGlobals.Instance);

    public static void SaveGlobals(PlayMakerGlobals globals)
    {
      if (Application.isPlaying)
        return;
      if (!AssetDatabase.Contains((UnityEngine.Object) globals))
      {
        string path = Path.Combine(PlayMakerPaths.ResourcesPath, "PlayMakerGlobals.asset");
        FsmEditor.CreateAsset((UnityEngine.Object) globals, ref path);
        EditorUtility.DisplayDialog(Strings.ProductName, string.Format(Strings.Dialog_SaveGlobals_Created, (object) path), Strings.OK);
      }
      EditorUtility.SetDirty((UnityEngine.Object) globals);
    }

    public static void Repaint(bool instant = false)
    {
      if (FsmEditor.Instance == null)
        return;
      if (instant)
      {
        if (!((UnityEngine.Object) FsmEditor.Window != (UnityEngine.Object) null))
          return;
        FsmEditor.Window.Repaint();
      }
      else
        FsmEditor.Instance.repaint = true;
    }

    public static void RepaintAll()
    {
      if (FsmEditor.Instance != null)
        FsmEditor.Instance.repaintAll = true;
      SceneView.RepaintAll();
    }

    public static void UpdateActionUsage()
    {
      if (EditorApplication.isPlayingOrWillChangePlaymode || !FsmEditorSettings.AutoRefreshActionUsage)
        return;
      FsmSearch.Update(FsmEditor.SelectedFsm);
      ActionSelector.UpdateActionUsage();
    }

    public static void UpdateFsmInfo()
    {
      if (FsmEditor.SelectedFsm == null)
        return;
      FsmEditor.UpdateFsmInfo(FsmEditor.SelectedFsm);
    }

    public static void UpdateFsmInfo(Fsm fsm) => FsmSearch.Invalidate(fsm);

    [Obsolete("Not Used")]
    public static void UpdateFsmInfoAll()
    {
    }

    public static void Cut()
    {
      if (FsmEditor.InspectorHasFocus && FsmEditor.Inspector.Mode == InspectorMode.StateInspector)
        FsmEditor.StateInspector.CutSelectedActions();
      else
        EditorCommands.CutStateSelection();
    }

    public static void Copy()
    {
      if (FsmEditor.InspectorHasFocus && FsmEditor.Inspector.Mode == InspectorMode.StateInspector)
        StateInspector.CopySelectedActions();
      else
        EditorCommands.CopyStateSelection();
    }

    public static void Paste()
    {
      if (GUIUtility.keyboardControl != 0 || !FsmEditor.Clipboard.CanPaste())
        return;
      if (FsmEditor.InspectorHasFocus && FsmEditor.Inspector.Mode == InspectorMode.StateInspector)
        FsmEditor.StateInspector.PasteActionsAfterSelected(true);
      else
        EditorCommands.PasteStates();
    }

    public static void SelectAll()
    {
      if (FsmEditor.InspectorHasFocus && FsmEditor.Inspector.Mode == InspectorMode.StateInspector)
        StateInspector.SelectAllActions();
      else
        EditorCommands.SelectAllStates();
    }

    public static void SanityCheckSelection()
    {
      FsmEditor.Selection.Update();
      FsmEditor.SelectionHistory.SanityCheck();
      StateInspector.SanityCheckActionSelection();
    }

    public static bool FsmListContainsFsm(Fsm fsm) => fsm != null && FsmEditor.FsmList.Contains(fsm);

    public static bool FsmContainsState(Fsm fsm, FsmState state)
    {
      if (fsm == null || state == null)
        return false;
      foreach (FsmState state1 in fsm.States)
      {
        if (state1 == state)
          return true;
      }
      return false;
    }

    public static bool StateContainsTransition(FsmState state, FsmTransition transition)
    {
      if (state == null || transition == null)
        return false;
      foreach (FsmTransition transition1 in state.Transitions)
      {
        if (transition1 == transition)
          return true;
      }
      return false;
    }

    public static void UndoRedoPerformed()
    {
      if (DragAndDropManager.IsDragging || DragAndDropManager.IsDraggingObject)
        return;
      FsmEditor.RebuildFsmList();
      FsmEditor.SanityCheckSelection();
      foreach (PlayMakerGlobals playMakerGlobals in Resources.FindObjectsOfTypeAll<PlayMakerGlobals>())
        playMakerGlobals.Variables.Init();
      GlobalVariablesWindow.ResetView();
      if (FsmEditor.SelectedFsm != null)
      {
        FsmEditor.VariablesManager.Reset();
        FsmEditor.SelectedFsm.Reload();
      }
      if (FsmEditorSettings.EnableRealtimeErrorChecker)
        FsmErrorChecker.CheckForErrors();
      FsmEditor.Instance.ResetViews();
      Keyboard.ResetFocus(true);
      FsmEditor.SetFsmDirty(true);
      FsmEditor.OnControlsChanged();
      FsmEditor.EventsManager.AfterUndo();
      FsmEditor.UpdateActionUsage();
    }

    internal static void RecordUndo(string name) => UndoUtility.RecordObject(FsmEditor.SelectedFsmOwner, name ?? "");

    internal static void RecordUndo(Fsm fsm, string name)
    {
      if (fsm == null || !(fsm.OwnerObject != (UnityEngine.Object) null))
        return;
      UndoUtility.RecordObject(fsm.OwnerObject, name ?? "");
    }

    internal static void RecordGlobalsUndo(string name) => UndoUtility.RecordObject((UnityEngine.Object) PlayMakerGlobals.Instance, name ?? "");

    [Localizable(false)]
    [Obsolete("Use RecordUndo instead.")]
    internal static void RegisterUndo(string action) => UndoUtility.RegisterUndo(FsmEditor.SelectedFsmOwner, action);

    [Localizable(false)]
    [Obsolete("Use RecordUndo instead.")]
    internal static void RegisterUndo(Fsm fsm, string action) => UndoUtility.RegisterUndo(fsm.OwnerObject, action);

    [Localizable(false)]
    [Obsolete("Use RecordUndo instead.")]
    internal static void RegisterUndo(UnityEngine.Object unityObject, string action) => UndoUtility.RegisterUndo(unityObject, action);

    [Localizable(false)]
    [Obsolete("Use RecordGlobalsUndo instead.")]
    internal static void RegisterGlobalsUndo(string action) => UndoUtility.RegisterUndo((UnityEngine.Object) PlayMakerGlobals.Instance, action);

    public static void ChangeLanguage() => FsmEditor.sendWindowCommandEvent = nameof (ChangeLanguage);

    public static void OpenWelcomeWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenWelcomeWindow;

    public static void OpenFsmSelectorWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenFsmSelectorWindow;

    public static void OpenFsmTemplateWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenFsmTemplateWindow;

    public static void OpenStateSelectorWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenStateSelectorWindow;

    public static void OpenActionWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenActionWindow;

    public static void OpenGlobalVariablesWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenGlobalVariablesWindow;

    public static void OpenGlobalEventsWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenGlobalEventsWindow;

    public static void OpenErrorWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenErrorWindow;

    public static void OpenToolWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenToolWindow;

    public static void OpenFsmControlsWindow() => FsmEditor.sendWindowCommandEvent = nameof (OpenFsmControlsWindow);

    public static void OpenTimelineWindow() => FsmEditor.sendWindowCommandEvent = nameof (OpenTimelineWindow);

    public static void OpenFsmLogWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenFsmLogWindow;

    public static void OpenAboutWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenAboutWindow;

    public static void OpenReportWindow() => FsmEditor.sendWindowCommandEvent = CommandEvents.OpenReportWindow;

    public static void OpenEventManager() => FsmEditor.Inspector.SetMode(InspectorMode.EventManager);

    public static void OpenSearchWindow() => EditorWindow.GetWindow<SearchWindow>();

    public static void DoDirtyFsmPrefab()
    {
      if (FsmEditor.dirtyFsmPrefab == null)
        return;
      FsmEditor.SetFsmDirty(FsmEditor.dirtyFsmPrefab, false);
      FsmEditor.dirtyFsmPrefab = (Fsm) null;
      FsmEditor.RepaintAll();
    }

    private void AddDelegatesToSelectedFsm()
    {
      if ((UnityEngine.Object) FsmEditor.SelectedFsmComponent == (UnityEngine.Object) null)
        return;
      this.RemoveDelegatesFromSelectedFsm();
      FsmEditor.SelectedFsmComponent.OnReset += new Action(this.OnResetFsm);
      FsmEditor.SelectedFsmComponent.OnValidated += new Action(FsmEditor.OnValidateFsm);
    }

    private void RemoveDelegatesFromSelectedFsm()
    {
      if ((UnityEngine.Object) FsmEditor.SelectedFsmComponent == (UnityEngine.Object) null)
        return;
      FsmEditor.SelectedFsmComponent.OnReset -= new Action(this.OnResetFsm);
      FsmEditor.SelectedFsmComponent.OnValidated -= new Action(FsmEditor.OnValidateFsm);
    }

    private void OnEditorDataChanged()
    {
      this.LoadEditorOnlyData(FsmEditor.SelectedFsm);
      FsmEditor.UpdateViews();
    }

    private void OnResetFsm()
    {
      FsmEditor.UpdateViews();
      FsmEditor.OnControlsChanged();
    }

    private static void OnValidateFsm()
    {
      if (FsmEditor.IgnoreOnValidate)
        FsmEditor.IgnoreOnValidate = false;
      else
        FsmEditor.UpdateViews();
    }

    private static void AutoAddPlayMakerGUI()
    {
      if (!FsmEditorSettings.AutoAddPlayMakerGUI)
        return;
      FsmEditor.AddPlayMakerGUI();
    }

    private static void AddPlayMakerGUI()
    {
      if (Application.isPlaying || Resources.FindObjectsOfTypeAll<PlayMakerGUI>().Length != 0)
        return;
      PlayMakerGUI.Instance.enabled = true;
      UnityEngine.Debug.Log((object) Strings.LOG_Auto_Added_Playmaker_GUI);
    }

    private void OpenReport() => this.openReport = true;

    private void DoOpenReport()
    {
      FsmEditor.OpenReportWindow();
      this.openReport = false;
    }

    public void ResetViews()
    {
      FsmEditor.InvalidatePixelCache();
      FsmSelector.RefreshView();
      this.graphView.RefreshView();
      this.inspector.ResetView();
      GlobalEventsWindow.ResetView();
      FsmLogger.ResetView();
      FsmEditor.RepaintAll();
    }

    private void ComponentWasAdded(UnityEngine.Component component) => this.ResetViews();

    private static void SanityCheckFsm(Fsm fsm)
    {
      string str = "" + fsm.SanityCheckStatePositions() + fsm.SanityCheckExposedEvents() + fsm.SanityCheckStartState() + fsm.SanityCheckOutputIndices();
      if (!FsmPrefabs.IsPrefabOrPreview(fsm.OwnerObject) && !FsmPrefabs.IsPrefabInstance(fsm))
      {
        if (fsm.Variables.DeleteEmptyVariables())
          str += "\nDeleted empty variable";
      }
      else
        fsm.Variables.CleanEmptyVariables();
      if (string.IsNullOrEmpty(str) || EditorApplication.isPlayingOrWillChangePlaymode)
        return;
      FsmEditor.SetFsmDirty(fsm, true);
      FsmEditor.GraphView.UpdateGraphBounds();
      FsmEditor.UpdateViews();
      FsmEditor.OnControlsChanged(fsm);
    }

    private static void ReloadActionsIfNeeded(FsmState state)
    {
      if (state.Fsm == null || state.Actions.Length == state.ActionData.ActionCount)
        return;
      state.LoadActions();
      if (!FsmEditorSettings.EnableRealtimeErrorChecker)
        return;
      FsmErrorChecker.CheckFsmForErrors(state.Fsm);
    }

    private static void CheckAllFsmPrefabInstances()
    {
      string str1 = "Some Prefab Instances were disconnected from their Prefab Parents because their FSMs were modified:\n\n";
      string str2 = "PlayMaker Update: " + str1;
      int num = 0;
      foreach (Fsm fsm in FsmEditor.FsmList)
      {
        if ((UnityEngine.Object) fsm.Owner != (UnityEngine.Object) null && FsmPrefabs.DisconnectIfModifiedInstance(fsm))
        {
          ++num;
          str2 = str2 + FsmUtility.GetFullFsmLabel(fsm) + "\n";
          if (num < 5)
            str1 = str1 + FsmUtility.GetFullFsmLabel(fsm) + "\n";
          else if (num == 5)
            str1 += "...\n\nCheck Console for full list.";
        }
      }
      if (num > 0)
      {
        Dialogs.OkDialog("Prefab Instance Check", str1 + "\n\nDisable this update in:\nPlayMaker > Preferences > Prefabs");
        UnityEngine.Debug.Log((object) (str2 + "\n\nDisable this update in:\nPlayMaker > Preferences > Prefabs"));
      }
      FsmEditor.RepaintAll();
    }

    public static bool DisconnectCheck(Fsm fsm = null)
    {
      if (!FsmEditorSettings.DisconnectModifiedInstances)
        return true;
      if (FsmEditor.breakPrefabAlreadyConfirmedThisUpdate)
        return FsmEditor.breakPrefabChoice;
      if (fsm == null)
        fsm = FsmEditor.SelectedFsm;
      if (!FsmPrefabs.IsPrefabInstance(fsm))
        return true;
      FsmEditor.breakPrefabAlreadyConfirmedThisUpdate = true;
      FsmEditor.breakPrefabChoice = Dialogs.BreakPrefabInstance();
      if (!FsmEditor.breakPrefabChoice)
      {
        Keyboard.ResetFocus();
        return false;
      }
      FsmEditor.DisconnectPrefabInstance();
      return true;
    }

    public static void DisconnectPrefabInstance()
    {
      FsmPrefabs.DisconnectPrefabInstance(FsmEditor.SelectedFsm);
      FsmEditor.StopEditingPrefabInstance();
      FsmEditor.RepaintAll();
    }

    private static void CheckFsmPrefabInstance()
    {
      if (FsmEditor.updateCount >= 4)
      {
        FsmEditor.updateCount = 0;
        EditorApplication.update -= new EditorApplication.CallbackFunction(FsmEditor.CheckFsmPrefabInstance);
        FsmPrefabs.DisconnectIfModifiedInstance(FsmEditor.SelectedFsm);
        FsmPrefabs.UpdateIsModifiedPrefabInstance(FsmEditor.SelectedFsm);
        FsmEditor.Repaint();
      }
      ++FsmEditor.updateCount;
    }

    [Conditional("PROFILE")]
    private static void ProfileStart(string message) => BlockTimer.Start("FsmEditor: " + message);

    [Conditional("PROFILE")]
    private static void ProfileEnd() => UnityEngine.Debug.Log((object) BlockTimer.End());

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }

    [Conditional("DEBUG_FSM_SELECTION")]
    private static void DebugSelection(object message, LogColor logColor = LogColor.None)
    {
    }

    [Conditional("DEBUG_EDITOR_LIFETIME")]
    private static void DebugLifetime(object message, LogColor logColor = LogColor.None)
    {
    }

    [Conditional("DEBUG_PLAY_MODE")]
    private static void DebugPlayMode(object message, LogColor logColor = LogColor.None)
    {
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugListCounts()
    {
      List<PlayMakerFSM> fsmComponentList = FsmEditor.fsmComponentList;
    }

    [Obsolete("Not used.")]
    public void OnSceneChange()
    {
    }

    [Obsolete("Use SanityCheckSelection")]
    public static void SanityCheck() => FsmEditor.SanityCheckSelection();
  }
}
