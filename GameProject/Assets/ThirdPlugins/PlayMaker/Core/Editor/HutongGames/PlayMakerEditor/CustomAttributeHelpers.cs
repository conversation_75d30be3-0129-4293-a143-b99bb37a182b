// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.CustomAttributeHelpers
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace HutongGames.PlayMakerEditor
{
  public static class CustomAttributeHelpers
  {
    private static readonly Dictionary<System.Type, object[]> TypeCustomAttributesLookup = new Dictionary<System.Type, object[]>();
    private static readonly Dictionary<FieldInfo, object[]> FieldCustomAttributesLookup = new Dictionary<FieldInfo, object[]>();

    public static object[] GetCustomAttributes(System.Type type)
    {
      object[] customAttributes;
      if (!CustomAttributeHelpers.TypeCustomAttributesLookup.TryGetValue(type, out customAttributes))
      {
        customAttributes = type.GetCustomAttributes(true);
        CustomAttributeHelpers.TypeCustomAttributesLookup.Add(type, customAttributes);
      }
      return customAttributes;
    }

    public static IEnumerable<Attribute> GetAttributes(
      System.Type type,
      System.Type attributeType)
    {
      object[] customAttributes = CustomAttributeHelpers.GetCustomAttributes(type);
      List<Attribute> attributeList = new List<Attribute>();
      foreach (object obj in customAttributes)
      {
        if (obj.GetType() == attributeType)
          attributeList.Add(obj as Attribute);
      }
      return (IEnumerable<Attribute>) attributeList;
    }

    public static IEnumerable<T> GetAttributes<T>(System.Type type) where T : Attribute => CustomAttributeHelpers.GetAttributes<T>((IEnumerable<object>) type.GetCustomAttributes(true));

    public static IEnumerable<T> GetAttributes<T>(FieldInfo field) where T : Attribute => CustomAttributeHelpers.GetAttributes<T>((IEnumerable<object>) field.GetCustomAttributes(true));

    public static IEnumerable<T> GetAttributes<T>(IEnumerable<object> attributes) where T : Attribute
    {
      List<T> objList = new List<T>();
      foreach (object attribute in attributes)
      {
        if (attribute is T)
          objList.Add(attribute as T);
      }
      return (IEnumerable<T>) objList;
    }

    public static object[] GetCustomAttributes(FieldInfo field)
    {
      object[] customAttributes;
      if (!CustomAttributeHelpers.FieldCustomAttributesLookup.TryGetValue(field, out customAttributes))
      {
        customAttributes = field.GetCustomAttributes(true);
        CustomAttributeHelpers.FieldCustomAttributesLookup.Add(field, customAttributes);
      }
      return customAttributes;
    }

    public static bool HasAttribute<T>(IEnumerable<object> attributes) where T : Attribute
    {
      if (attributes == null)
        return false;
      foreach (Attribute attribute in attributes)
      {
        if (attribute is T)
          return true;
      }
      return false;
    }

    public static bool HasAttribute<T>(FieldInfo field) where T : Attribute => CustomAttributeHelpers.HasAttribute<T>((IEnumerable<object>) CustomAttributeHelpers.GetCustomAttributes(field));

    public static bool HasAttribute<T>(System.Type type) where T : Attribute => CustomAttributeHelpers.HasAttribute<T>((IEnumerable<object>) type.GetCustomAttributes(true));

    public static T GetAttribute<T>(FieldInfo fieldInfo) where T : Attribute => CustomAttributeHelpers.GetAttribute<T>((IEnumerable<object>) fieldInfo.GetCustomAttributes(true));

    public static T GetAttribute<T>(System.Type type) where T : Attribute => CustomAttributeHelpers.GetAttribute<T>((IEnumerable<object>) CustomAttributeHelpers.GetCustomAttributes(type));

    public static T GetAttribute<T>(IEnumerable<object> attributes) where T : Attribute
    {
      if (attributes == null)
        return default (T);
      foreach (Attribute attribute in attributes)
      {
        if (attribute is T obj)
          return obj;
      }
      return default (T);
    }

    public static string GetTitle(IEnumerable<object> attributes) => CustomAttributeHelpers.GetAttribute<TitleAttribute>(attributes)?.Text;

    public static string GetNote(IEnumerable<object> attributes) => CustomAttributeHelpers.GetAttribute<NoteAttribute>(attributes)?.Text;

    public static string GetActionSection(IEnumerable<object> attributes) => CustomAttributeHelpers.GetAttribute<ActionSection>(attributes)?.Section;

    public static string GetTooltip(System.Type type, IEnumerable<object> attributes = null)
    {
      if (attributes == null)
        attributes = (IEnumerable<object>) CustomAttributeHelpers.GetCustomAttributes(type);
      string str = Labels.GetTypeTooltip(type);
      HutongGames.PlayMaker.TooltipAttribute attribute = CustomAttributeHelpers.GetAttribute<HutongGames.PlayMaker.TooltipAttribute>(attributes);
      if (attribute != null)
        str = str + Environment.NewLine + attribute.Text;
      return str;
    }

    public static System.Type GetObjectType(object[] attributes, System.Type defaultType = null)
    {
      ObjectTypeAttribute attribute = CustomAttributeHelpers.GetAttribute<ObjectTypeAttribute>((IEnumerable<object>) attributes);
      return attribute != null ? attribute.ObjectType : defaultType ?? typeof (UnityEngine.Object);
    }

    public static UIHint GetUIHint(object[] attributes)
    {
      UIHintAttribute attribute = CustomAttributeHelpers.GetAttribute<UIHintAttribute>((IEnumerable<object>) attributes);
      return attribute == null ? UIHint.None : attribute.Hint;
    }

    public static bool HasUIHint(FieldInfo field, UIHint uiHintValue) => CustomAttributeHelpers.HasUIHint(field.GetCustomAttributes(true), uiHintValue);

    public static bool HasUIHint(object[] attributes, UIHint uiHintValue) => CustomAttributeHelpers.GetAttributes<UIHintAttribute>((IEnumerable<object>) attributes).Any<UIHintAttribute>((Func<UIHintAttribute, bool>) (uiHint => uiHint.Hint == uiHintValue));

    public static bool IsObsolete(System.Type type) => type == null || CustomAttributeHelpers.GetAttribute<ObsoleteAttribute>(type) != null;

    public static string GetObsoleteMessage(System.Type type)
    {
      if (type == null)
        return "";
      ObsoleteAttribute attribute = CustomAttributeHelpers.GetAttribute<ObsoleteAttribute>(type);
      return attribute == null ? "" : attribute.Message;
    }
  }
}
