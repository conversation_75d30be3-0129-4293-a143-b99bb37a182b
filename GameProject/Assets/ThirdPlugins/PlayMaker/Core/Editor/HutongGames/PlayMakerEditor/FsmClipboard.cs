// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmClipboard
// Assembly: PlayMakerEditor, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using HutongGames.Utility;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class FsmClipboard
  {
    private FsmTemplate _buffer;

    private FsmTemplate buffer
    {
      get
      {
        if ((UnityEngine.Object) this._buffer == (UnityEngine.Object) null)
        {
          this._buffer = (FsmTemplate) ScriptableObject.CreateInstance(typeof (FsmTemplate));
          this._buffer.Hide();
        }
        return this._buffer;
      }
    }

    public bool IsEmpty => this.buffer.fsm == null;

    public int NumStatesCopied => !this.IsEmpty ? this.buffer.fsm.States.Length : 0;

    public bool HasVariables => this.buffer.fsm != null && (uint) this.buffer.fsm.Variables.GetAllNamedVariables().Length > 0U;

    public Fsm Fsm => this.buffer.fsm;

    public FsmVariables Variables => this.buffer.fsm == null ? new FsmVariables() : this.buffer.fsm.Variables;

    public void Empty() => this.buffer.fsm = (Fsm) null;

    public bool CanPaste() => !((UnityEngine.Object) this.buffer == (UnityEngine.Object) null) && this.buffer.fsm != null && (uint) this.buffer.fsm.States.Length > 0U;

    public bool CanPasteActions() => this.CanPaste() && this.buffer.fsm.States.Length <= 1 && (uint) this.buffer.fsm.States[0].Actions.Length > 0U;

    public List<string> FindVariableNameConflicts(Fsm target) => this.buffer.fsm.Variables.FindOverlappingVariables(target.Variables);

    public void CopyFsm(Fsm fsm) => this.buffer.fsm = new Fsm(fsm);

    public void CopyStates(Fsm fromFsm, List<FsmState> states)
    {
      if (fromFsm == null || states == null || states.Count == 0)
        return;
      Fsm toFsm = new Fsm()
      {
        DataVersion = fromFsm.DataVersion,
        States = FsmClipboard.CopyStates((IEnumerable<FsmState>) states),
        Events = FsmClipboard.GetEventsUsedByStates(fromFsm, (IEnumerable<FsmState>) states),
        GlobalTransitions = FsmClipboard.GetGlobalTransitionsUsedByStates(fromFsm, (IEnumerable<FsmState>) states)
      };
      FsmClipboard.CopyVariablesUsedByStates(fromFsm, (IEnumerable<FsmState>) states, toFsm);
      if (states.Find((Predicate<FsmState>) (x => x.Name == fromFsm.StartState)) != null)
        toFsm.StartState = fromFsm.StartState;
      Vector2 vector2 = new Vector2(float.PositiveInfinity, float.PositiveInfinity);
      foreach (FsmState state in toFsm.States)
      {
        vector2.x = Mathf.Min(state.Position.x, vector2.x);
        vector2.y = Mathf.Min(state.Position.y, vector2.y);
      }
      foreach (FsmState state in toFsm.States)
      {
        FsmState fsmState = state;
        double num1 = (double) state.Position.x - (double) vector2.x;
        double num2 = (double) state.Position.y - (double) vector2.y;
        Rect position = state.Position;
        double width = (double) position.width;
        position = state.Position;
        double height = (double) position.height;
        Rect rect = new Rect((float) num1, (float) num2, (float) width, (float) height);
        fsmState.Position = rect;
      }
      this.buffer.fsm = toFsm;
    }

    public void CopyActions(FsmState state, List<FsmStateAction> actions)
    {
      Fsm fsm = state.Fsm;
      if (fsm == null)
      {
        UnityEngine.Debug.LogError((object) "CopyActions: state.Fsm = null!");
      }
      else
      {
        this.buffer.fsm = Fsm.NewTempFsm();
        this.buffer.fsm.States = FsmClipboard.CopyStates((IEnumerable<FsmState>) new List<FsmState>()
        {
          state
        });
        this.buffer.fsm.DataVersion = fsm.DataVersion;
        FsmState state1 = this.buffer.fsm.States[0];
        state1.Name = "State";
        state1.Transitions = new FsmTransition[0];
        state1.SetColorIndex(0);
        this.buffer.fsm.UpdateStateSizes();
        this.buffer.fsm.Variables = new FsmVariables(fsm.Variables);
        this.buffer.fsm.Init((MonoBehaviour) null);
        FsmState state2 = this.buffer.fsm.States[0];
        FsmState fsmState = state2;
        Rect position = state2.Position;
        double width = (double) position.width;
        position = state2.Position;
        double height = (double) position.height;
        Rect rect = new Rect(0.0f, 0.0f, (float) width, (float) height);
        fsmState.Position = rect;
        state2.Transitions = new FsmTransition[0];
        state2.Description = "";
        List<FsmStateAction> fsmStateActionList = new List<FsmStateAction>((IEnumerable<FsmStateAction>) state.Actions);
        fsmStateActionList.RemoveAll((Predicate<FsmStateAction>) (x => !actions.Contains(x)));
        state2.Actions = fsmStateActionList.ToArray();
        this.buffer.fsm.Variables = new FsmVariables()
        {
          Categories = ArrayUtility.Copy<string>(fsm.Variables.Categories)
        };
        Dictionary<string, string> categoryMap = new Dictionary<string, string>();
        FsmSearch.Update(fsm);
        foreach (string variablesUsedByAction in FsmSearch.FindVariablesUsedByActions(fsm, (IEnumerable<FsmStateAction>) actions))
        {
          NamedVariable variable = fsm.Variables.FindVariable(variablesUsedByAction);
          this.buffer.fsm.AddVariable(variable.Clone());
          string category = fsm.Variables.GetCategory(variable);
          if (!string.IsNullOrEmpty(category))
            categoryMap.Add(variable.Name, category);
        }
        this.buffer.fsm.Variables.RebuildCategoryIDs(categoryMap);
        state2.SaveActions();
      }
    }

    public Fsm PasteFsmToSelected(bool confirmMultiple = true)
    {
      if (!this.CanPaste())
        return (Fsm) null;
      GameObject[] gameObjects = Selection.gameObjects;
      if (gameObjects.Length == 0)
        return (Fsm) null;
      if (confirmMultiple && gameObjects.Length > 1 && !Dialogs.AreYouSure(Strings.Command_Paste_FSM, Strings.Dialog_Paste_FSM_to_multiple_objects))
        return (Fsm) null;
      Fsm fsm = (Fsm) null;
      foreach (GameObject gameObject in Selection.gameObjects)
        fsm = this.PasteFsm(gameObject);
      return fsm;
    }

    public Fsm PasteFsm(GameObject go)
    {
      PlayMakerFSM fsmComponent = Undo.AddComponent<PlayMakerFSM>(go);
      FsmEditor.AddToFsmList(fsmComponent);
      fsmComponent.Fsm = new Fsm(this.buffer.fsm);
      fsmComponent.Fsm.CheckStartState();
      FsmGraphView.MoveAllStatesToOrigin(fsmComponent.Fsm);
      return fsmComponent.Fsm;
    }

    public List<FsmState> PasteStates(Fsm toFsm)
    {
      Vector2 position = new Vector2();
      if (FsmEditor.SelectedFsm == toFsm)
        position = FsmGraphView.GetViewCenter();
      return this.PasteStates(toFsm, position);
    }

    public List<FsmState> PasteStates(
      Fsm toFsm,
      Vector2 position,
      bool checkStartState = false)
    {
      if (this.buffer.fsm.States.Length == 0)
        return new List<FsmState>();
      if (toFsm.DataVersion != this.buffer.fsm.DataVersion)
      {
        this.buffer.fsm.DataVersion = toFsm.DataVersion;
        this.buffer.fsm.SaveActions();
      }
      FsmTemplate fsmTemplate = this.buffer.Copy();
      toFsm.AddStates((IEnumerable<FsmState>) fsmTemplate.fsm.States);
      foreach (FsmState state1 in fsmTemplate.fsm.States)
      {
        state1.Fsm = toFsm;
        string copyName = FsmClipboard.GenerateCopyName(state1);
        if (checkStartState && fsmTemplate.fsm.StartState == state1.Name && Dialogs.YesNoDialog(Strings.Dialog_Replace_Start_State, Strings.Dialog_Replace_Start_State_Description))
          toFsm.StartState = copyName;
        foreach (FsmState state2 in fsmTemplate.fsm.States)
        {
          foreach (FsmTransition transition in state2.Transitions)
          {
            if (!fsmTemplate.fsm.HasState(transition.ToState))
            {
              transition.ToState = "";
              transition.ToFsmState = (FsmState) null;
            }
            if (transition.ToState == state1.Name)
              transition.ToState = copyName;
          }
        }
        foreach (FsmTransition globalTransition in fsmTemplate.fsm.GlobalTransitions)
        {
          if (globalTransition.ToState == state1.Name)
            globalTransition.ToState = copyName;
        }
        state1.Name = copyName;
        FsmGraphView.TranslateState(state1, position);
      }
      toFsm.AddGlobalTransitions((IEnumerable<FsmTransition>) fsmTemplate.fsm.GlobalTransitions);
      toFsm.UpdateStateSizes();
      this.PasteVariables(toFsm);
      toFsm.Reload();
      FsmEditor.SetFsmDirty(toFsm, true);
      FsmEditor.UpdateActionUsage();
      FsmEditor.UpdateFsmInfo();
      Keyboard.ResetFocus();
      return new List<FsmState>((IEnumerable<FsmState>) fsmTemplate.fsm.States);
    }

    public void PasteVariables(Fsm toFsm, bool overwriteValues = true)
    {
      List<FsmVariable> fsmVariableList1 = FsmVariable.GetFsmVariableList((UnityEngine.Object) this.buffer);
      List<FsmVariable> fsmVariableList2 = FsmVariable.GetFsmVariableList(toFsm.OwnerObject);
      List<FsmVariable> fsmVariableList3 = new List<FsmVariable>((IEnumerable<FsmVariable>) fsmVariableList2);
      foreach (FsmVariable fsmVariable1 in fsmVariableList1)
      {
        FsmVariable sourceVariable = fsmVariable1;
        if (!FsmVariable.VariableNameUsed(fsmVariableList2, sourceVariable.Name))
        {
          fsmVariableList3.Add(sourceVariable);
        }
        else
        {
          FsmVariable fsmVariable2 = fsmVariableList3.Find((Predicate<FsmVariable>) (x => x.Name == sourceVariable.Name));
          if (fsmVariable2.Type != sourceVariable.Type)
            UnityEngine.Debug.LogError((object) string.Format(Strings.Error_Variable_name_already_exists_and_is_of_different_type, (object) sourceVariable.Name));
          else if (overwriteValues)
            fsmVariable2.NamedVar.RawValue = sourceVariable.NamedVar.RawValue;
        }
      }
      FsmVariable.ReplaceFsmVariables(toFsm, fsmVariableList3);
      FsmEditor.OnControlsChanged(toFsm);
    }

    public List<FsmStateAction> PasteActions(
      FsmState toState,
      int atIndex,
      bool undo = true)
    {
      if (toState == null || toState.Fsm == null || (atIndex == -1 || this.buffer.fsm == null) || (this.buffer.fsm.States.Length == 0 || this.buffer.fsm.States[0].Actions.Length == 0))
        return (List<FsmStateAction>) null;
      if (undo)
        FsmEditor.RecordUndo(Strings.Menu_Paste_Actions);
      FsmState state = new FsmState(this.buffer.fsm.States[0])
      {
        Fsm = this.buffer.fsm
      };
      state.LoadActions();
      state.LoadActionData();
      List<FsmStateAction> fsmStateActionList1 = new List<FsmStateAction>();
      int actionCount = state.ActionData.ActionCount;
      for (int actionIndex = 0; actionIndex < actionCount; ++actionIndex)
      {
        FsmStateAction action = state.ActionData.CreateAction(state, actionIndex);
        if (actionIndex < state.Actions.Length)
        {
          action.IsOpen = state.Actions[actionIndex].IsOpen;
          action.Name = state.Actions[actionIndex].Name;
        }
        fsmStateActionList1.Add(action);
      }
      List<FsmStateAction> fsmStateActionList2 = new List<FsmStateAction>((IEnumerable<FsmStateAction>) toState.Actions);
      fsmStateActionList2.InsertRange(atIndex, (IEnumerable<FsmStateAction>) fsmStateActionList1);
      toState.Actions = fsmStateActionList2.ToArray();
      FsmEditor.SaveActions(toState);
      this.PasteVariables(toState.Fsm);
      toState.Fsm.Reload();
      FsmEditor.SetFsmDirty(toState.Fsm, true);
      FsmEditor.UpdateActionUsage();
      FsmEditor.UpdateFsmInfo();
      List<FsmStateAction> fsmStateActionList3 = new List<FsmStateAction>();
      for (int index = atIndex; index < atIndex + actionCount; ++index)
        fsmStateActionList3.Add(toState.Actions[index]);
      return fsmStateActionList3;
    }

    public static List<FsmState> PasteTemplate(
      Fsm toFsm,
      FsmTemplate template,
      Vector2 position,
      bool checkStartState = false)
    {
      if ((UnityEngine.Object) template == (UnityEngine.Object) null)
        return new List<FsmState>();
      return new FsmClipboard() { _buffer = template }.PasteStates(toFsm, position, checkStartState);
    }

    private static string GenerateCopyName(FsmState state)
    {
      if (state == null)
        return "";
      if (state.Fsm == null)
        return state.Name;
      foreach (FsmState state1 in state.Fsm.States)
      {
        if (state1 != state && state1.Name == state.Name)
        {
          string str = StringUtils.IncrementStringCounter(state.Name);
          while (state.Fsm.HasState(str))
            str = StringUtils.IncrementStringCounter(str);
          return str;
        }
      }
      return state.Name;
    }

    private static void CopyVariablesUsedByStates(
      Fsm fromFsm,
      IEnumerable<FsmState> states,
      Fsm toFsm)
    {
      if (toFsm == null)
        return;
      FsmVariables variables = new FsmVariables();
      toFsm.Variables = variables;
      if (fromFsm == null || states == null)
        return;
      List<string> stringList1 = new List<string>()
      {
        ""
      };
      List<string> variablesUsedByStates = FsmSearch.FindVariablesUsedByStates(fromFsm, states);
      if (variablesUsedByStates.Count == 0)
        return;
      List<FsmVariable> fsmVariableList = FsmVariable.GetFsmVariableList(fromFsm.OwnerObject);
      foreach (FsmVariable fsmVariable in fsmVariableList)
      {
        if (variablesUsedByStates.Contains(fsmVariable.Name))
        {
          toFsm.AddVariable(fsmVariable.NamedVar.Clone());
          if (!string.IsNullOrEmpty(fsmVariable.Category))
            stringList1.Add(fsmVariable.Category);
        }
      }
      variables.Categories = stringList1.ToArray();
      Dictionary<string, FsmVariable> dictionary = new Dictionary<string, FsmVariable>();
      foreach (FsmVariable fsmVariable in fsmVariableList)
        dictionary.Add(fsmVariable.Name, fsmVariable);
      List<string> stringList2 = new List<string>();
      NamedVariable[] allNamedVariables = variables.GetAllNamedVariables();
      for (int index = 0; index < allNamedVariables.Length; ++index)
      {
        NamedVariable namedVariable = allNamedVariables[index];
        FsmVariable fsmVariable;
        if (dictionary.TryGetValue(namedVariable.Name, out fsmVariable))
        {
          variables.CategoryIDs[index] = variables.GetCategoryIndex(fsmVariable.Category);
          if (fsmVariable.IsOutput)
            stringList2.Add(namedVariable.Name);
        }
      }
      toFsm.SetOutputVariables(stringList2.ToArray());
    }

    private static FsmEvent[] GetEventsUsedByStates(Fsm fsm, IEnumerable<FsmState> states)
    {
      List<FsmEvent> fsmEventList = new List<FsmEvent>();
      foreach (FsmState state in states)
      {
        foreach (FsmTransition transition in state.Transitions)
        {
          if (!string.IsNullOrEmpty(transition.EventName))
          {
            FsmEvent fsmEvent = FsmEvent.GetFsmEvent(transition.EventName);
            if (!fsmEventList.Contains(fsmEvent))
              fsmEventList.Add(fsmEvent);
          }
        }
      }
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (!string.IsNullOrEmpty(globalTransition.EventName))
        {
          FsmEvent fsmEvent = FsmEvent.GetFsmEvent(globalTransition.EventName);
          if (!fsmEventList.Contains(fsmEvent))
            fsmEventList.Add(fsmEvent);
        }
      }
      return fsmEventList.ToArray();
    }

    private static FsmTransition[] GetGlobalTransitionsUsedByStates(
      Fsm fsm,
      IEnumerable<FsmState> states)
    {
      List<FsmTransition> fsmTransitionList = new List<FsmTransition>();
      foreach (FsmState state in states)
      {
        foreach (FsmTransition globalTransition in state.Fsm.GlobalTransitions)
        {
          if (globalTransition.ToState == state.Name)
            fsmTransitionList.Add(new FsmTransition(globalTransition));
        }
      }
      return fsmTransitionList.ToArray();
    }

    private static FsmState[] CopyStates(IEnumerable<FsmState> states)
    {
      List<FsmState> fsmStateList = new List<FsmState>();
      foreach (FsmState state in states)
        fsmStateList.Add(new FsmState(state));
      return fsmStateList.ToArray();
    }

    public string GetDebugString()
    {
      if ((UnityEngine.Object) this._buffer == (UnityEngine.Object) null)
        return "_buffer = null";
      Fsm fsm = this._buffer.fsm;
      if (fsm == null)
        return "fsm = null";
      string str1 = "Data Version" + (object) fsm.DataVersion + "\n\nStates:";
      foreach (FsmState state in fsm.States)
        str1 = str1 + "\n" + state.Name + " Actions: " + (object) state.Actions.Length;
      string str2 = str1 + "\n\nVariables:";
      foreach (NamedVariable allNamedVariable in fsm.Variables.GetAllNamedVariables())
        str2 = str2 + "\n" + allNamedVariable.Name;
      return str2 + "\n\nCategories (" + (object) fsm.Variables.Categories.Length + "):\n" + string.Join("\n", fsm.Variables.Categories) + "\n\nCategoryIDs (" + (object) fsm.Variables.CategoryIDs.Length + "):\n" + string.Join("\n", ((IEnumerable<int>) fsm.Variables.CategoryIDs).Select<int, string>((Func<int, string>) (i => i.ToString())).ToArray<string>());
    }

    [Conditional("DEBUG_LOG")]
    private void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }
  }
}
