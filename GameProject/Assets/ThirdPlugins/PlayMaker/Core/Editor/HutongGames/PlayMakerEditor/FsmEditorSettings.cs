// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmEditorSettings
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.ComponentModel;
using System.Globalization;
using System.IO;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Serializable]
  public static class FsmEditorSettings
  {
    private static bool settingsLoaded;
    public static string ProductName = Strings.ProductName;
    public static string ProductCopyright = Strings.ProductCopyright;
    public const string ProductUrl = "http://hutonggames.com/";
    public const string OnlineStoreUrl = "http://www.hutonggames.com/store.html";
    public const string AssetStoreUrl = "http://u3d.as/content/hutong-games-llc/playmaker/1Az";
    public const int MaxStateNameLength = 100;
    private static readonly string[] themeNames = new string[2]
    {
      "Legacy",
      Strings.Themes_Flat
    };
    private static readonly string[] themeFileNames = new string[2]
    {
      "",
      "flat"
    };
    private static int selectedTheme;
    public static string SelectedThemeFileName;
    [Localizable(false)]
    private static readonly string[] supportedCultures = new string[11]
    {
      "en-US",
      "zh-CN",
      "zh-TW",
      "nl",
      "fr-FR",
      "de-DE",
      "it",
      "ja-JP",
      "pt-BR",
      "es-ES",
      "sv-SE"
    };
    [Localizable(false)]
    private static readonly string[] cultureNames = new string[11]
    {
      "English",
      "Chinese Simplified",
      "Chinese Traditional",
      "Dutch",
      "French",
      "German",
      "Italian",
      "Japanese",
      "Portuguese Brazilian",
      "Spanish",
      "Swedish"
    };
    private static int selectedCulture;
    private static string selectedCultureName;
    private static string selectedCultureTranslators;
    private static bool selectedCultureSupportedInMenus;
    private static FsmEditorSettings.Categories selectedCategory;
    public const float GraphViewMinZoom = 0.3f;
    public const float GraphViewMaxZoom = 1f;
    private static Vector2 scrollPosition;

    public static bool UseTheme => FsmEditorSettings.selectedTheme > 0;

    public static StringHelper.NamingConvention VariableNamingConvention { get; set; }

    public static bool DebugPerformance { get; set; }

    public static bool UseAdvancedDropdowns { get; set; }

    public static bool InspectorOnLeft => FsmEditorSettings.InspectorPosition == FsmEditorSettings.InspectorPanelPosition.Left;

    public static FsmEditorSettings.InspectorPanelPosition InspectorPosition { get; set; }

    public static bool ShowFullFsmInspector { get; set; }

    public static bool FsmControlsShowEnabledCheckboxes { get; set; }

    public static bool FsmControlsShowEmptyFSMs { get; set; }

    public static bool GroupVariableInputOutputs { get; set; }

    public static int ErrorCheckTimeslice { get; set; }

    public static int ActionUsageTimeslice { get; set; }

    public static float InspectorColorsAlpha { get; set; }

    public static bool UseAlphaFeatures { get; set; }

    public static bool UsePixelCaching { get; set; }

    public static bool DebugPixelCaching { get; set; }

    public static bool LogFsmUpdatedMessages { get; set; }

    public static bool ModernUIStyle { get; set; }

    public static bool ShowFsmPasswordControls { get; set; }

    public static bool ShowExperimentalFsmSettings { get; set; }

    public static bool InspectorWideMode { get; set; }

    public static bool UseLegacyNetworking { get; set; }

    public static bool AutoSummarizeFoldedActions { get; set; }

    public static bool ShowNetworkSync { get; set; }

    public static bool PingOpenEditorWindows { get; set; }

    public static bool ShowSendEventsIconOnStates { get; set; }

    public static bool FadeLinksNotConnectedToSelectedStates { get; set; }

    public static bool AnimateUI { get; set; }

    public static bool DisconnectModifiedInstances { get; set; }

    public static bool DisconnectModifiedInstancesInScene { get; set; }

    public static int ActionBrowserRecentSize { get; set; }

    public static FsmDebugger.FsmStepMode DebuggerStepMode { get; set; }

    public static bool DisableUndoRedo { get; set; }

    public static bool DrawAssetThumbnail { get; set; }

    public static bool DrawLinksBehindStates { get; set; }

    public static bool DimFinishedActions { get; set; }

    public static bool AutoRefreshFsmInfo { get; set; }

    public static bool ConfirmEditingPrefabInstances { get; set; }

    public static bool DrawFrameAroundGraph { get; set; }

    public static bool GraphViewShowMinimap { get; set; }

    public static float GraphViewMinimapSize { get; set; }

    public static string ScreenshotsPath { get; set; }
    
    public static string ExportLuaPath { get; set; }

    public static bool ShowSelectedGameObjectLabel { get; set; }

    public static bool ShowSelectedFsmLabel { get; set; }

    public static bool ShowStateDescription { get; set; }

    [Obsolete("No longer saved. Always true.")]
    public static bool ShowActionParameters => true;

    public static bool DebugActionParameters { get; set; }

    public static bool DebugVariables { get; set; }

    public static bool HideUnusedEvents { get; set; }

    public static bool ShowUsedActionsOnly { get; set; }

    public static bool ShowActionPreview { get; set; }

    public static bool ShowTemplatePreview { get; set; }

    public static int SelectedActionCategory { get; set; }

    public static int SelectedTemplateCategory { get; set; }

    public static bool SelectFSMInGameView { get; set; }

    [Obsolete("Use PlayMakerPrefs.ArrowColor instead.")]
    public static Color DebugLookAtColor { get; set; }

    [Obsolete("Use PlayMakerPrefs.ArrowColor instead.")]
    public static Color DebugRaycastColor { get; set; }

    public static bool HideUnusedParams { get; set; }

    public static bool AutoAddPlayMakerGUI { get; set; }

    public static bool DimUnusedActionParameters { get; set; }

    public static bool AddPrefabLabel { get; set; }

    public static bool UnloadPrefabs { get; set; }

    public static bool AutoLoadPrefabs { get; set; }

    public static int StateMaxWidth { get; set; }

    public static int StateMinWidth { get; set; }

    public static bool ShowScrollBars { get; set; }

    public static bool EnableWatermarks { get; set; }

    public static bool ShowGrid { get; set; }

    public static int SnapGridSize { get; set; }

    public static bool SnapToGrid { get; set; }

    public static bool EnableLogging { get; set; }

    public static bool ColorLinks { get; set; }

    public static bool HideObsoleteActions { get; set; }

    public static bool LockGraphView { get; set; }

    public static GraphViewLinkStyle GraphViewLinkStyle { get; private set; }

    public static string StartStateName { get; private set; }

    public static string NewStateName { get; private set; }

    public static int GameStateIconSize { get; private set; }

    public static bool AutoSelectGameObject { get; private set; }

    public static bool SelectStateOnActivated { get; private set; }

    public static bool JumpToBreakpoint { get; private set; }

    public static bool FrameSelectedState { get; private set; }

    public static bool SyncLogSelection { get; private set; }

    public static bool BreakpointsEnabled { get; set; }

    public static bool ShowFsmDescriptionInGraphView { get; set; }

    public static bool ShowCommentsInGraphView { get; set; }

    public static bool DrawPlaymakerGizmos { get; set; }

    public static bool DrawPlaymakerGizmoInHierarchy { get; set; }

    public static bool ShowEditWhileRunningWarning { get; set; }

    public static bool MirrorDebugLog { get; private set; }

    public static float EdgeScrollSpeed { get; set; }

    public static float EdgeScrollZone { get; set; }

    public static int MaxLoopCount { get; set; }

    public static FsmEditorStyles.ColorScheme ColorScheme { get; set; }

    public static bool ShowStateLabelsInGameView { get; set; }

    public static bool ShowStateLabelsInBuild { get; set; }

    public static bool EnableRealtimeErrorChecker { get; set; }

    public static bool DisableErrorCheckerWhenPlaying { get; set; }

    public static bool CheckForRequiredComponent { get; set; }

    public static bool CheckForRequiredField { get; set; }

    public static bool CheckForTransitionMissingEvent { get; set; }

    public static bool CheckForTransitionMissingTarget { get; set; }

    public static bool CheckForDuplicateTransitionEvent { get; set; }

    public static bool CheckForMouseEventErrors { get; set; }

    public static bool CheckForCollisionEventErrors { get; set; }

    public static bool CheckForEventNotUsed { get; set; }

    public static bool CheckForPrefabRestrictions { get; set; }

    public static bool CheckForObsoleteActions { get; set; }

    public static bool CheckForMissingActions { get; set; }

    public static bool CheckForNetworkSetupErrors { get; set; }

    public static bool DisableActionBrowerWhenPlaying { get; set; }

    public static bool DisableEventBrowserWhenPlaying { get; set; }

    public static bool DisableEditorWhenPlaying { get; set; }

    public static bool DisableInspectorWhenPlaying { get; set; }

    public static bool DisableToolWindowsWhenPlaying { get; set; }

    public static bool ShowHints { get; set; }

    public static bool CloseActionBrowserOnEnter { get; set; }

    public static bool AutoRefreshActionUsage { get; set; }

    public static bool LogPauseOnSelect { get; set; }

    public static bool LogShowSentBy { get; set; }

    public static bool LogShowExit { get; set; }

    public static bool LogShowTimecode { get; set; }

    public static bool EnableDebugFlow { get; set; }

    public static bool EnableTransitionEffects { get; set; }

    public static bool ShowStateLoopCounts { get; set; }

    public static int ConsoleActionReportSortOptionIndex { get; set; }

    public static bool LoadAllPrefabs { get; set; }

    public static bool LoadAllTemplates { get; set; }

    public static bool SelectNewVariables { get; set; }

    public static bool FsmBrowserShowFullPath { get; set; }

    public static bool FsmBrowserShowDisabled { get; set; }

    public static bool FsmBrowserShowPrefabs { get; set; }

    public static bool FsmBrowserHidePrefabsWhenPlaying { get; set; }

    public static bool FsmBrowserShowAllStates { get; set; }

    public static bool FsmBrowserShowEnabledCheckboxes { get; set; }

    public static bool HideUnusedPools { get; set; }

    public static bool DisablePoolBrowserWhenPlaying { get; set; }

    public static bool MouseWheelScrollsGraphView { get; set; }

    public static float GraphViewZoomSpeed { get; set; }

    public static void LoadCategory() => FsmEditorSettings.selectedCategory = (FsmEditorSettings.Categories) EditorPrefs.GetInt("PlayMaker.SettingsCategory", 0);

    private static void SaveCategory(FsmEditorSettings.Categories category) => EditorPrefs.SetInt("PlayMaker.SettingsCategory", (int) category);

    public static void SetCategory(FsmEditorSettings.Categories category)
    {
      FsmEditorSettings.selectedCategory = category;
      FsmEditorSettings.SaveCategory(FsmEditorSettings.selectedCategory);
    }

    public static void OnGUI(Rect area)
    {
      FsmEditorSettings.DoHeader(area);
      FsmEditorSettings.scrollPosition = GUILayout.BeginScrollView(FsmEditorSettings.scrollPosition);
      EditorGUIUtility.labelWidth = 180f;
      EditorGUI.BeginChangeCheck();
      switch (FsmEditorSettings.selectedCategory)
      {
        case FsmEditorSettings.Categories.General:
          FsmEditorSettings.DoGeneralSettings();
          break;
        case FsmEditorSettings.Categories.GraphView:
          FsmEditorSettings.DoGraphViewSettings();
          break;
        case FsmEditorSettings.Categories.ErrorChecking:
          FsmEditorSettings.DoErrorCheckSettings();
          break;
        case FsmEditorSettings.Categories.Debugging:
          FsmEditorSettings.DoDebuggingSettings();
          break;
        case FsmEditorSettings.Categories.Colors:
          FsmEditorSettings.DoColorSettings();
          break;
        case FsmEditorSettings.Categories.Experimental:
          FsmEditorSettings.DoExperimentalSettings();
          break;
      }
      EditorGUILayout.Space();
      if (EditorGUI.EndChangeCheck())
      {
        FsmEditorSettings.ValidateSettings();
        FsmEditorSettings.ApplySettings();
        FsmEditorSettings.SaveSettings();
      }
      GUILayout.EndScrollView();
      GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG);
      EditorGUILayout.BeginHorizontal();
      if (FsmEditorGUILayout.CenteredButton(Strings.FsmEditorSettings_Restore_Default_Settings, 200f))
      {
        FsmEditorSettings.ResetDefaults();
        FsmEditorSettings.SaveSettings();
      }
      if (FsmEditorGUILayout.HelpButton())
        EditorCommands.OpenWikiPage(WikiPages.Preferences);
      GUILayout.EndHorizontal();
      EditorGUILayout.Space();
      GUILayout.EndVertical();
    }

    private static void DoHeader(Rect area)
    {
      Rect rect1 = GUILayoutUtility.GetRect(GUIContent.none, FsmEditorStyles.TopBarBG, GUILayout.Height(30f));
      Rect position = new Rect(rect1.x + 4f, rect1.y + 4f, rect1.width, rect1.height);
      Rect rect2 = new Rect(rect1.xMax - 134f, rect1.y + 8f, 130f, 20f);
      if (Event.current.type == UnityEngine.EventType.Repaint)
      {
        FsmEditorStyles.TopBarBG.Draw(rect1);
        GUI.Label(position, Strings.Command_Preferences, FsmEditorStyles.MediumTitleText);
      }
      EditorGUI.BeginChangeCheck();
      FsmEditorSettings.selectedCategory = (FsmEditorSettings.Categories) EditorGUI.EnumPopup(rect2, (Enum) FsmEditorSettings.selectedCategory);
      if (EditorGUI.EndChangeCheck())
        FsmEditorSettings.SaveCategory(FsmEditorSettings.selectedCategory);
      HighlighterHelper.FromRectInArea(area, rect2, "Preferences Category");
    }

    private static void DoColorSettings()
    {
      EditorGUIUtility.labelWidth = (float) ((double) FsmEditor.InspectorPanelWidth * 0.5 - 10.0);
      FsmEditorSettings.Section(Strings.Label_Scene_GUI_Colors);
      PlayMakerPrefs.TweenFromColor = EditorGUILayout.ColorField(new GUIContent(Strings.Label_Tween_From_Color, Strings.Label_Tween_From_Color_Tooltip), PlayMakerPrefs.TweenFromColor);
      PlayMakerPrefs.TweenToColor = EditorGUILayout.ColorField(new GUIContent(Strings.Label_Tween_To_Color, Strings.Label_Tween_To_Color_Tooltip), PlayMakerPrefs.TweenToColor);
      PlayMakerPrefs.ArrowColor = EditorGUILayout.ColorField(new GUIContent("Arrow Color", "Color used to draw arrows. E.g., velocity, force, direction..."), PlayMakerPrefs.ArrowColor);
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Default_Colors);
      for (int index = 0; index < 8; ++index)
      {
        GUILayout.BeginHorizontal();
        PlayMakerPrefs.ColorNames[index] = EditorGUILayout.TextField(PlayMakerPrefs.ColorNames[index]);
        PlayMakerPrefs.Colors[index] = EditorGUILayout.ColorField(PlayMakerPrefs.Colors[index]);
        GUILayout.EndHorizontal();
      }
      EditorGUILayout.Space();
      if (FsmEditorGUILayout.CenteredButton(Strings.FsmEditorSettings_Reset_Default_Colors, 200f))
      {
        PlayMakerPrefs.Instance.ResetDefaultColors();
        Keyboard.ResetFocus();
        GUI.changed = true;
      }
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Custom_Colors);
      for (int index = 8; index < 24; ++index)
      {
        GUILayout.BeginHorizontal();
        PlayMakerPrefs.ColorNames[index] = EditorGUILayout.TextField(PlayMakerPrefs.ColorNames[index]);
        PlayMakerPrefs.Colors[index] = EditorGUILayout.ColorField(PlayMakerPrefs.Colors[index]);
        GUILayout.EndHorizontal();
      }
      EditorGUILayout.HelpBox(Strings.FsmEditorSettings_Custom_Colors_Name_Hint, MessageType.Info);
      if (!GUI.changed)
        return;
      FsmEditorSettings.SavePlayMakerPrefs();
    }

    private static void DoExperimentalSettings()
    {
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box("These settings control experimental features.\nNOTE: Experimental features might be changed or removed in future versions of PlayMaker.", FsmEditorStyles.HintBox);
      FsmEditorSettings.Section("Experimental");
      FsmEditorSettings.UseAdvancedDropdowns = false;
      FsmEditorSettings.ShowFsmPasswordControls = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Show_FSM_Password_Controls, Strings.FsmEditorSettings_Show_FSM_Password_Controls_Tooltip), FsmEditorSettings.ShowFsmPasswordControls);
      FsmEditorSettings.ShowExperimentalFsmSettings = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Show_Experimental_Settings_in_FSM_Inspector), FsmEditorSettings.ShowExperimentalFsmSettings);
      FsmEditorSettings.AutoSummarizeFoldedActions = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Auto_Summarize_Folded_Actions, Strings.FsmEditorSettings_Auto_Summarize_Tooltip), FsmEditorSettings.AutoSummarizeFoldedActions);
      FsmEditorSettings.Section("Performance");
      FsmEditorSettings.DebugPerformance = FsmEditorGUILayout.RightAlignedToggle(new GUIContent("Debug Performance", "Log extra info to help debug performance problems"), FsmEditorSettings.DebugPerformance);
      FsmEditorSettings.UsePixelCaching = FsmEditorGUILayout.RightAlignedToggle(new GUIContent("Use Pixel Caching In Inspectors", "Optimizes Inspector panels by drawing a cached texture instead of the full UI when not interacting with it. "), FsmEditorSettings.UsePixelCaching);
      FsmEditorSettings.DebugPixelCaching = FsmEditorGUILayout.RightAlignedToggle(new GUIContent("Debug Pixel Caching", "For developer use. Draws a red X through the UI when it's being drawn from a texture. To help make sure this system is working as expected as we develop it!"), FsmEditorSettings.DebugPixelCaching);
      FsmEditorSettings.DisableUndoRedo = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Disable_Undo_Redo, Strings.FsmEditorSettings_Disable_Undo_Redo_Tooltip), FsmEditorSettings.DisableUndoRedo);
      EditorGUILayout.Space();
      EditorGUILayout.HelpBox("Expensive editor operations can be time-sliced to keep the editor responsive. These preferences set how many milliseconds an operation is allowed to run before yielding. \n\nLower = More responsive editor but operation takes longer to finish.\nHigher = Less responsive editor but operation finishes quicker.\n\nThese settings are exposed mainly so testers can find the best default values. Most of the time you should not need to change the default values.", MessageType.None);
      FsmEditorSettings.ErrorCheckTimeslice = EditorGUILayout.DelayedIntField(new GUIContent("Error Check Timeslice", "Milliseconds to spend error checking before yielding."), FsmEditorSettings.ErrorCheckTimeslice);
    }

    private static void Section(string title)
    {
      GUILayout.Space(10f);
      GUILayout.Label(title.ToUpper(), EditorStyles.boldLabel);
      FsmEditorGUILayout.LightDivider();
      GUILayout.Space(2f);
    }

    private static void SavePlayMakerPrefs()
    {
      PlayMakerPrefs.SaveChanges();
      EditorUtility.SetDirty((UnityEngine.Object) PlayMakerPrefs.Instance);
      if (AssetDatabase.Contains((UnityEngine.Object) PlayMakerPrefs.Instance))
        return;
      string path = Path.Combine(PlayMakerPaths.ResourcesPath, "PlayMakerPrefs.asset");
      FsmEditor.CreateAsset((UnityEngine.Object) PlayMakerPrefs.Instance, ref path);
      Debug.Log((object) (Strings.FsmEditorSettings_Creating_PlayMakerPrefs_Asset + path));
    }

    private static void DoDebuggingSettings()
    {
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box(Strings.Hint_Debugger_Settings, FsmEditorStyles.HintBox);
      FsmEditorSettings.Section("Debugging");
      FsmEditorSettings.ShowStateLabelsInGameView = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Show_State_Labels_in_Game_View, Strings.FsmEditorSettings_DoDebuggingSettings_Show_State_Labels_Tooltip), FsmEditorSettings.ShowStateLabelsInGameView);
      FsmEditorSettings.ShowStateLabelsInBuild = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.Label_Enable_State_Labels_in_Builds, Strings.Tooltip_Show_State_Labels_in_Standalone_Builds), FsmEditorSettings.ShowStateLabelsInBuild);
      FsmEditorSettings.EnableLogging = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Enable_Logging, Strings.FsmEditorSettings_Enable_Logging_Tooltip), FsmEditorSettings.EnableLogging);
      FsmEditorSettings.EnableDebugFlow = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Enable_DebugFlow, Strings.FsmEditorSettings_Enable_DebugFlow_Tooltip), FsmEditorSettings.EnableDebugFlow);
      FsmEditorSettings.EnableTransitionEffects = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Enable_Transition_Effects, Strings.FsmEditorSettings_Enable_Transition_Effects_Tooltip), FsmEditorSettings.EnableTransitionEffects);
      FsmEditorSettings.JumpToBreakpoint = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Jump_to_Breakpoint_Error, Strings.FsmEditorSettings_Jump_to_Breakpoint_Error_Tooltip), FsmEditorSettings.JumpToBreakpoint);
      FsmEditorSettings.MirrorDebugLog = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Forward_Playmaker_Log_to_Unity_Log, Strings.FsmEditorSettings_Forward_Playmaker_Log_to_Unity_Log_Tooltip), FsmEditorSettings.MirrorDebugLog);
      EditorGUI.BeginChangeCheck();
      PlayMakerPrefs.ShowEventHandlerComponents = FsmEditorGUILayout.RightAlignedToggle(new GUIContent("Show PlayMaker Event Handler Components", "Show components automatically added to handle events. Normally these are hidden, but it can be useful to see them for debugging."), PlayMakerPrefs.ShowEventHandlerComponents);
      FsmEditorSettings.Section("Gizmos");
      PlayMakerPrefs.DebugLinesDuration = EditorGUILayout.FloatField(new GUIContent("Debug Lines Duration", "How long debug lines are visible for (in seconds)."), PlayMakerPrefs.DebugLinesDuration);
      if (!EditorGUI.EndChangeCheck())
        return;
      FsmEditorSettings.SavePlayMakerPrefs();
    }

    private static void DoErrorCheckSettings()
    {
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box(Strings.Hint_Error_Checker_Settings, FsmEditorStyles.HintBox);
      FsmEditorSettings.Section("Error Checking");
      EditorGUI.BeginChangeCheck();
      FsmEditorSettings.EnableRealtimeErrorChecker = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Enable_Real_Time_Error_Checker, Strings.FsmEditorSettings_Enable_Real_Time_Error_Checker_Tooltip), FsmEditorSettings.EnableRealtimeErrorChecker);
      FsmEditorSettings.DisableErrorCheckerWhenPlaying = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Disable_Error_Checker_When_Game_Is_Playing, Strings.FsmEditorSettings_Disable_Error_Checker_When_Game_Is_Playing_Tooltip), FsmEditorSettings.DisableErrorCheckerWhenPlaying);
      FsmEditorSettings.CheckForRequiredComponent = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Required_Components, Strings.FsmEditorSettings_Check_for_Required_Components_Tooltip), FsmEditorSettings.CheckForRequiredComponent);
      FsmEditorSettings.CheckForRequiredField = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Required_Action_Fields, Strings.FsmEditorSettings_Check_for_Required_Action_Fields_Tooltip), FsmEditorSettings.CheckForRequiredField);
      FsmEditorSettings.CheckForEventNotUsed = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Events_Not_Used_by_Target_FSM, Strings.FsmEditorSettings_Check_for_Events_Not_Used_by_Target_FSM_Tooltip), FsmEditorSettings.CheckForEventNotUsed);
      FsmEditorSettings.CheckForTransitionMissingEvent = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Transitions_Missing_Events, Strings.FsmEditorSettings_Check_for_Transitions_Missing_Events_Tooltip), FsmEditorSettings.CheckForTransitionMissingEvent);
      FsmEditorSettings.CheckForTransitionMissingTarget = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Transitions_Missing_Targets, Strings.FsmEditorSettings_Check_for_Transitions_Missing_Targets_Tooltip), FsmEditorSettings.CheckForTransitionMissingTarget);
      FsmEditorSettings.CheckForDuplicateTransitionEvent = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Duplicate_Transition_Events, Strings.FsmEditorSettings_Check_for_Duplicate_Transition_Events_Tooltip), FsmEditorSettings.CheckForDuplicateTransitionEvent);
      FsmEditorSettings.CheckForMouseEventErrors = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Setup_Errors_With_Mouse_Events, Strings.FsmEditorSettings_Check_for_Setup_Errors_With_Mouse_Events_Tooltip), FsmEditorSettings.CheckForMouseEventErrors);
      FsmEditorSettings.CheckForCollisionEventErrors = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Setup_Errors_With_Collision_Events, Strings.FsmEditorSettings_Check_for_Setup_Errors_With_Collision_Events_Tooltip), FsmEditorSettings.CheckForCollisionEventErrors);
      FsmEditorSettings.CheckForPrefabRestrictions = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Prefab_Restrictions, Strings.FsmEditorSettings_Check_for_Prefab_Restrictions_Tooltip), FsmEditorSettings.CheckForPrefabRestrictions);
      FsmEditorSettings.CheckForObsoleteActions = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Obsolete_Actions, Strings.FsmEditorSettings_Check_for_Obsolete_Actions_Tooltip), FsmEditorSettings.CheckForObsoleteActions);
      FsmEditorSettings.CheckForMissingActions = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Missing_Actions, Strings.FsmEditorSettings_Check_for_Missing_Actions_Tooltip), FsmEditorSettings.CheckForMissingActions);
      FsmEditorSettings.CheckForNetworkSetupErrors = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Check_for_Network_Setup_Errors, Strings.FsmEditorSettings_Check_for_Network_Setup_Errors_Tooltip), FsmEditorSettings.CheckForNetworkSetupErrors);
      if (!EditorGUI.EndChangeCheck())
        return;
      FsmErrorChecker.Refresh();
      FsmEditor.RepaintAll();
    }

    private static void DoGeneralSettings()
    {
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box(Strings.FsmEditorHint_General_Settings, FsmEditorStyles.HintBox);
      FsmEditorSettings.Section("General");
      int cultureIndex = EditorGUILayout.Popup(Strings.Label_Language, FsmEditorSettings.selectedCulture, FsmEditorSettings.cultureNames);
      if (cultureIndex != FsmEditorSettings.selectedCulture)
        FsmEditorSettings.SetCulture(cultureIndex);
      if (FsmEditorSettings.selectedCultureTranslators != "")
        EditorGUILayout.HelpBox(Strings.FsmEditorSettings_Translators + FsmEditorSettings.selectedCultureTranslators, MessageType.None);
      if (!FsmEditorSettings.selectedCultureSupportedInMenus)
        EditorGUILayout.HelpBox(Strings.FsmEditorSettings_Selected_language_not_yet_supported_in_menus, MessageType.None);
      FsmEditorSettings.InspectorPosition = (FsmEditorSettings.InspectorPanelPosition) EditorGUILayout.EnumPopup("Inspector Panel Position", (Enum) FsmEditorSettings.InspectorPosition);
      FsmEditorSettings.VariableNamingConvention = (StringHelper.NamingConvention) EditorGUILayout.EnumPopup(new GUIContent("Variable Naming Convention", "Naming convention to use when automatically making new variables.\n\nNOTE: It is not enforced on variables you name yourself."), (Enum) FsmEditorSettings.VariableNamingConvention);
      FsmEditorSettings.PingOpenEditorWindows = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Ping_Open_Editor_Windows, Strings.FsmEditorSettings_Ping_Open_Editor_Windows_Tooltip), FsmEditorSettings.PingOpenEditorWindows);
      FsmEditorSettings.InspectorWideMode = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Inspector_Wide_Mode, Strings.FsmEditorSettings_Inspector_Wide_Mode_Tooltip), FsmEditorSettings.InspectorWideMode);
      FsmEditorSettings.AnimateUI = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Animate_UI, Strings.FsmEditorSettings_Animate_UI_Tooltip), FsmEditorSettings.AnimateUI);
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Category_Components_and_Gizmos);
      FsmEditorSettings.AutoAddPlayMakerGUI = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Auto_Add_PlayMakerGUI_to_Scene, Strings.FsmEditorSettings_Auto_Add_PlayMakerGUI_to_Scene_Tooltip), FsmEditorSettings.AutoAddPlayMakerGUI);
      FsmEditorSettings.ShowStateLabelsInGameView = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Show_State_Labels_in_Game_View, Strings.FsmEditorSettings_DoDebuggingSettings_Show_State_Labels_Tooltip), FsmEditorSettings.ShowStateLabelsInGameView);
      bool flag1 = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Draw_Playmaker_Gizmos_in_Scene_View, Strings.FsmEditorSettings_Draw_Playmaker_Gizmos_in_Scene_View_Tooltip), FsmEditorSettings.DrawPlaymakerGizmos);
      if (flag1 != FsmEditorSettings.DrawPlaymakerGizmos)
      {
        FsmEditorSettings.DrawPlaymakerGizmos = flag1;
        PlayMakerFSM.DrawGizmos = flag1;
        GUI.changed = true;
      }
      bool flag2 = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Draw_Playmaker_Gizmos_in_Hierarchy, Strings.FsmEditorSettings_Draw_Playmaker_Gizmos_in_Hierarchy_Tooltip), FsmEditorSettings.DrawPlaymakerGizmoInHierarchy);
      if (flag2 != FsmEditorSettings.DrawPlaymakerGizmoInHierarchy)
      {
        Gizmos.EnableHierarchyItemGizmos = flag2;
        FsmEditorSettings.DrawPlaymakerGizmoInHierarchy = flag2;
        EditorApplication.RepaintHierarchyWindow();
      }
      FsmEditorSettings.LogFsmUpdatedMessages = FsmEditorGUILayout.RightAlignedToggle(new GUIContent("Log FSM Updated Messages", "Sometime when loading an FSM it needs to be updated, e.g., when some actions have changed since it was saved. Check this to log messages whenever an FSM is updated."), false);
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Category_When_Game_Is_Playing);
      FsmEditorSettings.ShowEditWhileRunningWarning = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Show_Editing_While_Running_Warning, Strings.FsmEditorSettings_Show_Editing_While_Running_Warning_Tooltip), FsmEditorSettings.ShowEditWhileRunningWarning);
      FsmEditorSettings.DisableEditorWhenPlaying = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Disable_PlayMaker_Editor_When_Game_Is_Playing, Strings.FsmEditorSettings_Disable_PlayMaker_Editor_When_Game_Is_Playing_Tooltip), FsmEditorSettings.DisableEditorWhenPlaying);
      FsmEditorSettings.DisableInspectorWhenPlaying = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Disable_the_Inspector_Panel_When_Game_Is_Playing, Strings.FsmEditorSettings_Disable_the_Inspector_Panel_When_Game_Is_Playing_Tooltip), FsmEditorSettings.DisableInspectorWhenPlaying);
      FsmEditorSettings.DisableToolWindowsWhenPlaying = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Disable_Tool_Windows_When_Game_Is_Playing, Strings.FsmEditorSettings_Disable_Tool_Windows_When_Game_Is_Playing_Tooltip), FsmEditorSettings.DisableToolWindowsWhenPlaying);
      FsmEditorSettings.DisableErrorCheckerWhenPlaying = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Disable_Error_Checker_When_Game_Is_Playing, Strings.FsmEditorSettings_Disable_Error_Checker_When_Game_Is_Playing_Tooltip), FsmEditorSettings.DisableErrorCheckerWhenPlaying);
      FsmEditorSettings.DimFinishedActions = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Dim_Finished_Actions, Strings.FsmEditorSettings_Dim_Finished_Actions_Tooltip), FsmEditorSettings.DimFinishedActions);
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Category_Selection);
      FsmEditorSettings.AutoSelectGameObject = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Select_GameObject_When_FSM_Selected, Strings.FsmEditorSettings_Select_GameObject_When_FSM_Selected_Tooltip), FsmEditorSettings.AutoSelectGameObject);
      FsmEditorSettings.SelectStateOnActivated = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Select_State_On_Activated, Strings.FsmEditorSettings_Select_State_On_Activated_Tooltip), FsmEditorSettings.SelectStateOnActivated);
      FsmEditorSettings.FrameSelectedState = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Auto_Frame_Selected_State, Strings.FsmEditorSettings_Auto_Frame_Selected_State_Tooltip), FsmEditorSettings.FrameSelectedState);
      FsmEditorSettings.SelectFSMInGameView = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Select_Game_Objects_With_FSMs_in_Game_View, Strings.FsmEditorSettings_Select_Game_Objects_With_FSMs_in_Game_View_Tooltip), FsmEditorSettings.SelectFSMInGameView);
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Category_Prefabs);
      FsmEditorSettings.DisconnectModifiedInstances = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Disconnect_Modified_Prefab_Instances, Strings.FsmEditorSettings_Disconnect_Modified_Prefab_Instance_Tooltip), FsmEditorSettings.DisconnectModifiedInstances);
      FsmEditorSettings.DisconnectModifiedInstancesInScene = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Disconnect_All_Modified_Prefab_Instances_in_Scene, Strings.FsmEditorSettings_Disconnect_All_Modified_Prefab_Instances_in_Scene_Tooltip), FsmEditorSettings.DisconnectModifiedInstancesInScene);
      FsmEditorSettings.ConfirmEditingPrefabInstances = FsmEditorGUILayout.RightAlignedToggle(FsmEditorContent.ConfirmEditPrefabInstance, FsmEditorSettings.ConfirmEditingPrefabInstances);
      FsmEditorSettings.LoadAllPrefabs = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Load_All_PlayMakerFSM_Prefabs_When_Refactoring, Strings.FsmEditorSettings_Load_All_PlayMakerFSM_Prefabs_When_Refactoring_Tooltip), FsmEditorSettings.LoadAllPrefabs);
      FsmEditorSettings.AutoLoadPrefabs = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Auto_Load_Prefabs_in_Scene, Strings.FsmEditorSettings_Auto_Load_Prefabs_in_Scene_Tooltip), FsmEditorSettings.AutoLoadPrefabs);
      FsmEditorSettings.AddPrefabLabel = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Add_Prefab_Labels, Strings.FsmEditorSettings_Add_Prefab_Labels_Tooltip), FsmEditorSettings.AddPrefabLabel);
      FsmEditorSettings.Section(Strings.Label_Templates);
      FsmEditorSettings.LoadAllTemplates = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.Label_Load_All_Templates_on_Startup, Strings.Label_Load_All_Templates_on_Startup_Tooltip), FsmEditorSettings.LoadAllTemplates);
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Cetegory_Paths);
      GUILayout.Label(new GUIContent("FSM Export Directory", Strings.FsmEditorSettings_FSM_Screenshots_Directory_Tooltip));
      FsmEditorSettings.ExportLuaPath = EditorGUILayout.TextField(FsmEditorSettings.ExportLuaPath);
      GUILayout.Label(new GUIContent(Strings.FsmEditorSettings_FSM_Screenshots_Directory, Strings.FsmEditorSettings_FSM_Screenshots_Directory_Tooltip));
      FsmEditorSettings.ScreenshotsPath = EditorGUILayout.TextField(FsmEditorSettings.ScreenshotsPath);
    }

    private static void SetTheme(int themeIndex)
    {
      if (themeIndex >= FsmEditorSettings.themeNames.Length)
        themeIndex = 0;
      FsmEditorSettings.selectedTheme = themeIndex;
      FsmEditorSettings.SelectedThemeFileName = FsmEditorSettings.themeFileNames[themeIndex];
    }

    [Localizable(false)]
    private static void SetCulture(int cultureIndex)
    {
      if (cultureIndex >= FsmEditorSettings.cultureNames.Length)
        cultureIndex = 0;
      FsmEditorSettings.selectedCulture = cultureIndex;
      FsmEditorSettings.selectedCultureName = FsmEditorSettings.cultureNames[cultureIndex];
      FsmEditorSettings.selectedCultureTranslators = FsmEditorSettings.GetTranslators(FsmEditorSettings.selectedCultureName);
      FsmEditorSettings.selectedCultureSupportedInMenus = true;
      Strings.Culture = new CultureInfo(FsmEditorSettings.supportedCultures[cultureIndex]);
      InspectorPanel.InitLabels();
      BugReportWindow.frequencyChoices = new string[3]
      {
        Strings.BugReportWindow_FrequencyChoices_Always,
        Strings.BugReportWindow_FrequencyChoices_Sometimes__but_not_always,
        Strings.BugReportWindow_FrequencyChoices_This_is_the_first_time
      };
      FsmEditorStyles.Reinitialize();
      FsmEditor.RepaintAll();
      FsmEditorSettings.SaveSettings();
      FsmEditor.ChangeLanguage();
    }

    [Localizable(false)]
    private static string GetTranslators(string cultureName)
    {
      switch (cultureName)
      {
        case "Chinese Simplified":
          return "黄峻";
        case "Chinese Traditional":
          return "黄峻";
        case "Dutch":
          return "VisionaiR3D";
        case "English":
          return "";
        case "French":
          return "Jean Fabre";
        case "German":
          return "Steven 'Nightreaver' Barthen, Marc 'Dreamora' Schaerer";
        case "Italian":
          return "Marcello Gavioli";
        case "Japanese":
          return "gamesonytablet";
        case "Portuguese Brazilian":
          return "Nilton Felicio, Andre Dantas Lima";
        case "Spanish":
          return "Matias Ignacio, Eugenio 'Ryo567' Martínez";
        case "Swedish":
          return "Damiangto";
        default:
          return "";
      }
    }

    private static void DoGraphViewSettings()
    {
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box(Strings.Hint_Graph_View_Settings, FsmEditorStyles.HintBox);
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Category_Graph_Styles);
      int themeIndex = EditorGUILayout.Popup(Strings.Label_Theme, FsmEditorSettings.selectedTheme, FsmEditorSettings.themeNames);
      if (themeIndex != FsmEditorSettings.selectedTheme)
        FsmEditorSettings.SetTheme(themeIndex);
      FsmEditorStyles.ColorScheme colorScheme = (FsmEditorStyles.ColorScheme) EditorGUILayout.EnumPopup(new GUIContent(Strings.FsmEditorSettings_Color_Scheme, "Color scheme used with the selected Theme.\nDefault: Matches Unity editor skin."), (Enum) FsmEditorSettings.ColorScheme);
      if (colorScheme != FsmEditorSettings.ColorScheme)
      {
        FsmEditorSettings.ColorScheme = colorScheme;
        FsmEditorStyles.Init();
      }
      FsmEditorSettings.GraphViewLinkStyle = (GraphViewLinkStyle) EditorGUILayout.EnumPopup(new GUIContent(Strings.FsmEditorSettings_Link_Style, Strings.FsmEditorSettings_Link_Style_Tooltip), (Enum) FsmEditorSettings.GraphViewLinkStyle);
      FsmEditorSettings.ColorLinks = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Color_Links_With_State_Color, Strings.FsmEditorSettings_Color_Links_With_State_Color_Tooltip), FsmEditorSettings.ColorLinks);
      FsmEditorSettings.DrawFrameAroundGraph = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Draw_Frame_Around_FSM), FsmEditorSettings.DrawFrameAroundGraph);
      FsmEditorSettings.DrawLinksBehindStates = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Draw_Links_Behind_States), FsmEditorSettings.DrawLinksBehindStates);
      FsmEditorSettings.FadeLinksNotConnectedToSelectedStates = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Fade_Links_Not_Connected_to_Selected_States), FsmEditorSettings.FadeLinksNotConnectedToSelectedStates);
      FsmEditorSettings.ShowSendEventsIconOnStates = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Show_Send_Events_Icons, Strings.FsmEditorSettings_Show_Send_Events_Icons_Tooltip), FsmEditorSettings.ShowSendEventsIconOnStates);
      FsmEditorSettings.EnableWatermarks = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Enable_Watermarks, Strings.FsmEditorSettings_Enable_Watermarks_Tooltip), FsmEditorSettings.EnableWatermarks);
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Category_Labels);
      EditorGUI.BeginChangeCheck();
      FsmEditorSettings.ShowSelectedGameObjectLabel = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Show_Selected_GameObject_Label, ""), FsmEditorSettings.ShowSelectedGameObjectLabel);
      FsmEditorSettings.ShowSelectedFsmLabel = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Show_Selected_FSM_Label, ""), FsmEditorSettings.ShowSelectedFsmLabel);
      if (EditorGUI.EndChangeCheck())
      {
        FsmEditor.GraphView.UpdateGraphLabel();
        GUI.changed = true;
      }
      FsmEditorSettings.ShowFsmDescriptionInGraphView = FsmEditorGUILayout.RightAlignedToggle(new GUIContent("Show FSM Description", "Show the FSM's description in the Graph View."), FsmEditorSettings.ShowFsmDescriptionInGraphView);
      bool flag = FsmEditorGUILayout.RightAlignedToggle(new GUIContent("Show State Descriptions", "Show a State's description under the State in the Graph View."), FsmEditorSettings.ShowCommentsInGraphView);
      if (flag != FsmEditorSettings.ShowCommentsInGraphView)
      {
        FsmEditorSettings.ShowCommentsInGraphView = flag;
        FsmEditor.GraphView.UpdateAllStateSizes();
        GUI.changed = true;
      }
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Scrolling);
      FsmEditorSettings.MouseWheelScrollsGraphView = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Mouse_Wheel_Scrolls_Graph_View, Strings.FsmEditorSettings_DoGraphViewSettings_Tooltip), FsmEditorSettings.MouseWheelScrollsGraphView);
      FsmEditorSettings.ShowScrollBars = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Show_Scrollbars_All_The_Time, Strings.FsmEditorSettings_Show_Scrollbars_All_The_Time_Tooltip), FsmEditorSettings.ShowScrollBars);
      FsmEditorSettings.EdgeScrollSpeed = EditorGUILayout.FloatField(new GUIContent(Strings.FsmEditorSettings_Edge_Scroll_Speed, Strings.FsmEditorSettings_Edge_Scroll_Speed_Tooltip), FsmEditorSettings.EdgeScrollSpeed);
      FsmEditorSettings.GraphViewZoomSpeed = EditorGUILayout.FloatField(new GUIContent(Strings.FsmEditorSettings_Zoom_Speed, Strings.FsmEditorSettings_DoGraphViewSettings_Zoom_Speed_Tooltip), FsmEditorSettings.GraphViewZoomSpeed);
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Minimap);
      FsmEditorSettings.GraphViewShowMinimap = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Show_Minimap), FsmEditorSettings.GraphViewShowMinimap);
      FsmEditorSettings.GraphViewMinimapSize = EditorGUILayout.FloatField(new GUIContent(Strings.FsmEditorSettings_Minimap_Size, Strings.FsmEditorSettings_Minimap_Size_Tooltip), FsmEditorSettings.GraphViewMinimapSize);
      FsmEditorSettings.GraphViewMinimapSize = Mathf.Clamp(FsmEditorSettings.GraphViewMinimapSize, 50f, 1000f);
      FsmEditorSettings.Section(Strings.FsmEditorSettings_Category_Editing);
      FsmEditorSettings.NewStateName = EditorGUILayout.TextField(new GUIContent(Strings.FsmEditorSettings_New_State_Name, Strings.FsmEditorSettings_New_State_Name_Tooltip), FsmEditorSettings.NewStateName);
      int num1 = EditorGUILayout.DelayedIntField(new GUIContent(Strings.FsmEditorSettings_Max_State_Width, Strings.FsmEditorSettings_Max_State_Width_Tooltip), FsmEditorSettings.StateMaxWidth);
      if (num1 != FsmEditorSettings.StateMaxWidth)
      {
        FsmEditorSettings.StateMaxWidth = Mathf.Clamp(num1, 20, 1000);
        if (FsmEditorSettings.StateMaxWidth < FsmEditorSettings.StateMinWidth)
          FsmEditorSettings.StateMinWidth = FsmEditorSettings.StateMaxWidth;
        FsmEditor.GraphView.UpdateAllStateSizes();
      }
      int num2 = EditorGUILayout.DelayedIntField(new GUIContent("Min State Width", "Minimum width of a State."), FsmEditorSettings.StateMinWidth);
      if (num2 != FsmEditorSettings.StateMinWidth)
      {
        FsmEditorSettings.StateMinWidth = Mathf.Clamp(num2, 20, 1000);
        if (FsmEditorSettings.StateMinWidth > FsmEditorSettings.StateMaxWidth)
          FsmEditorSettings.StateMaxWidth = FsmEditorSettings.StateMinWidth;
        FsmEditor.GraphView.UpdateAllStateSizes();
      }
      FsmEditorSettings.SnapGridSize = EditorGUILayout.IntField(new GUIContent(Strings.FsmEditorSettings_Snap_Grid_Size, Strings.FsmEditorSettings_Snap_Grid_Size_Tooltip), FsmEditorSettings.SnapGridSize);
      FsmEditorSettings.SnapToGrid = FsmEditorGUILayout.RightAlignedToggle(new GUIContent(Strings.FsmEditorSettings_Snap_To_Grid), FsmEditorSettings.SnapToGrid);
    }

    private static void ResetDefaults()
    {
      FsmEditorSettings.selectedTheme = 1;
      FsmEditorSettings.ErrorCheckTimeslice = 100;
      FsmEditorSettings.ActionUsageTimeslice = 50;
      FsmEditorSettings.VariableNamingConvention = StringHelper.NamingConvention.CamelCase;
      FsmEditorSettings.DebugPerformance = false;
      FsmEditorSettings.InspectorPosition = FsmEditorSettings.InspectorPanelPosition.Right;
      FsmEditorSettings.ShowFullFsmInspector = false;
      FsmEditorSettings.FsmControlsShowEnabledCheckboxes = false;
      FsmEditorSettings.FsmControlsShowEmptyFSMs = false;
      FsmEditorSettings.GroupVariableInputOutputs = true;
      FsmEditorSettings.InspectorColorsAlpha = 0.5f;
      FsmEditorSettings.DebugPixelCaching = false;
      FsmEditorSettings.UsePixelCaching = false;
      FsmEditorSettings.LogFsmUpdatedMessages = false;
      FsmEditorSettings.UseAlphaFeatures = false;
      FsmEditorSettings.ModernUIStyle = false;
      FsmEditorSettings.ShowExperimentalFsmSettings = false;
      FsmEditorSettings.ShowFsmPasswordControls = false;
      FsmEditorSettings.AutoSummarizeFoldedActions = true;
      FsmEditorSettings.PingOpenEditorWindows = true;
      FsmEditorSettings.ShowSendEventsIconOnStates = true;
      FsmEditorSettings.FadeLinksNotConnectedToSelectedStates = true;
      FsmEditorSettings.AnimateUI = true;
      FsmEditorSettings.DisconnectModifiedInstances = true;
      FsmEditorSettings.DisconnectModifiedInstancesInScene = true;
      FsmEditor.InspectorPanelWidth = 350f;
      FsmEditorSettings.DisableUndoRedo = false;
      FsmEditorSettings.DrawAssetThumbnail = true;
      FsmEditorSettings.DrawLinksBehindStates = true;
      FsmEditorSettings.DimFinishedActions = true;
      FsmEditorSettings.ConfirmEditingPrefabInstances = true;
      FsmEditorSettings.ShowStateLoopCounts = false;
      FsmEditorSettings.GraphViewZoomSpeed = 0.01f;
      FsmEditorSettings.MouseWheelScrollsGraphView = false;
      FsmEditorSettings.GraphViewLinkStyle = GraphViewLinkStyle.BezierLinks;
      FsmEditorSettings.NewStateName = Strings.FsmEditorSettings_Default_State_Name;
      FsmEditorSettings.AutoSelectGameObject = true;
      FsmEditorSettings.SelectStateOnActivated = true;
      FsmEditorSettings.JumpToBreakpoint = true;
      FsmEditorSettings.MirrorDebugLog = false;
      FsmEditorSettings.ShowEditWhileRunningWarning = true;
      FsmEditorSettings.SelectFSMInGameView = true;
      FsmEditorSettings.ShowSelectedGameObjectLabel = true;
      FsmEditorSettings.ShowSelectedFsmLabel = true;
      FsmEditorSettings.ShowFsmDescriptionInGraphView = true;
      FsmEditorSettings.ShowCommentsInGraphView = true;
      FsmEditorSettings.DrawPlaymakerGizmos = true;
      FsmEditorSettings.DrawPlaymakerGizmoInHierarchy = true;
      FsmEditorSettings.AutoAddPlayMakerGUI = true;
      FsmEditorSettings.DimUnusedActionParameters = false;
      FsmEditorSettings.ShowHints = false;
      FsmEditorSettings.CloseActionBrowserOnEnter = false;
      FsmEditorSettings.SelectNewVariables = true;
      FsmEditorSettings.AddPrefabLabel = true;
      FsmEditorSettings.LoadAllPrefabs = true;
      FsmEditorSettings.AutoLoadPrefabs = true;
      FsmEditorSettings.UnloadPrefabs = true;
      FsmEditorSettings.LoadAllTemplates = true;
      FsmEditorSettings.StateMaxWidth = 400;
      FsmEditorSettings.StateMinWidth = 100;
      FsmEditorSettings.ShowScrollBars = true;
      FsmEditorSettings.EnableWatermarks = true;
      FsmEditorSettings.ColorLinks = true;
      FsmEditorSettings.SnapGridSize = 16;
      FsmEditorSettings.SnapToGrid = false;
      FsmEditorSettings.EnableLogging = true;
      FsmEditorSettings.HideUnusedEvents = false;
      FsmEditorSettings.EdgeScrollSpeed = 1f;
      FsmEditorSettings.EdgeScrollZone = 100f;
      FsmEditorSettings.EnableRealtimeErrorChecker = true;
      FsmEditorSettings.DisableErrorCheckerWhenPlaying = true;
      FsmEditorSettings.CheckForRequiredComponent = true;
      FsmEditorSettings.CheckForRequiredField = true;
      FsmEditorSettings.CheckForEventNotUsed = true;
      FsmEditorSettings.CheckForTransitionMissingEvent = true;
      FsmEditorSettings.CheckForTransitionMissingTarget = true;
      FsmEditorSettings.CheckForDuplicateTransitionEvent = true;
      FsmEditorSettings.CheckForMouseEventErrors = true;
      FsmEditorSettings.CheckForCollisionEventErrors = true;
      FsmEditorSettings.CheckForPrefabRestrictions = true;
      FsmEditorSettings.CheckForObsoleteActions = true;
      FsmEditorSettings.CheckForMissingActions = true;
      FsmEditorSettings.CheckForNetworkSetupErrors = true;
      FsmEditorSettings.DisableEditorWhenPlaying = false;
      FsmEditorSettings.DisableInspectorWhenPlaying = false;
      FsmEditorSettings.DisableToolWindowsWhenPlaying = true;
      FsmEditorSettings.DisableActionBrowerWhenPlaying = false;
      FsmEditorSettings.DisableEventBrowserWhenPlaying = false;
      FsmEditorSettings.FsmBrowserShowAllStates = true;
      FsmEditorSettings.FsmBrowserShowEnabledCheckboxes = false;
      FsmEditorSettings.FsmBrowserHidePrefabsWhenPlaying = true;
      FsmEditorSettings.FsmBrowserShowFullPath = true;
      FsmEditorSettings.FsmBrowserShowDisabled = true;
      FsmEditorSettings.FsmBrowserShowPrefabs = true;
      FsmEditorSettings.ScreenshotsPath = Strings.FsmEditorSettings_Default_Screenshots_Path;
      FsmEditorSettings.ExportLuaPath = "../../AOW_COMMON/lua/fsm";
      FsmEditorSettings.EnableDebugFlow = true;
      FsmEditorSettings.ApplySettings();
    }

    public static void SaveSettingsIfGuiChanged()
    {
      if (!GUI.changed)
        return;
      FsmEditorSettings.SaveSettings();
    }

    [Localizable(false)]
    public static void SaveSettings()
    {
      if (!FsmEditorSettings.settingsLoaded)
      {
        Debug.LogWarning((object) "PlayMaker: Attempting to SaveSettings before LoadSettings.");
      }
      else
      {
        FsmEditorSettings.ValidateSettings();
        EditorPrefs.SetInt("PlayMaker.VariableNamingConvention", (int) FsmEditorSettings.VariableNamingConvention);
        EditorPrefs.SetBool("PlayMaker.DebugPerformance", FsmEditorSettings.DebugPerformance);
        EditorPrefs.SetInt("PlayMaker.InspectorPosition", (int) FsmEditorSettings.InspectorPosition);
        EditorPrefs.SetBool("PlayMaker.ShowFullFsmInspector", FsmEditorSettings.ShowFullFsmInspector);
        EditorPrefs.SetBool("PlayMaker.FsmControlsShowEnabledCheckboxes", FsmEditorSettings.FsmControlsShowEnabledCheckboxes);
        EditorPrefs.SetBool("PlayMaker.FsmControlsShowEmptyFSMs", FsmEditorSettings.FsmControlsShowEmptyFSMs);
        EditorPrefs.SetBool("PlayMaker.GroupVariableInputOutputs", FsmEditorSettings.GroupVariableInputOutputs);
        EditorPrefs.SetInt("PlayMaker.ErrorCheckTimeslice", FsmEditorSettings.ErrorCheckTimeslice);
        EditorPrefs.SetInt("PlayMaker.ActionUsageTimeslice", FsmEditorSettings.ActionUsageTimeslice);
        EditorPrefs.SetFloat("PlayMaker.InspectorColorsAlpha", FsmEditorSettings.InspectorColorsAlpha);
        EditorPrefs.SetBool("PlayMaker.UseAlphaFeatures", FsmEditorSettings.UseAlphaFeatures);
        EditorPrefs.SetBool("PlayMaker.UsePixelCaching", FsmEditorSettings.UsePixelCaching);
        EditorPrefs.SetBool("PlayMaker.DebugPixelCaching", FsmEditorSettings.DebugPixelCaching);
        EditorPrefs.SetBool("PlayMaker.LogFsmUpdatedMessages", FsmEditorSettings.LogFsmUpdatedMessages);
        EditorPrefs.SetBool("PlayMaker.ModernUIStyle", FsmEditorSettings.ModernUIStyle);
        EditorPrefs.SetBool("PlayMaker.ShowFsmLockControls", FsmEditorSettings.ShowFsmPasswordControls);
        EditorPrefs.SetBool("PlayMaker.ShowExperimentalFsmSettings", FsmEditorSettings.ShowExperimentalFsmSettings);
        EditorPrefs.SetBool("PlayMaker.InspectorWideMode", FsmEditorSettings.InspectorWideMode);
        EditorPrefs.SetBool("PlayMaker.AutoSummarizeFoldedActions", FsmEditorSettings.AutoSummarizeFoldedActions);
        EditorPrefs.SetBool("PlayMaker.PingOpenEditorWindows", FsmEditorSettings.PingOpenEditorWindows);
        EditorPrefs.SetBool("PlayMaker.ShowSendEventsIconOnStates_", FsmEditorSettings.ShowSendEventsIconOnStates);
        EditorPrefs.SetBool("PlayMaker.FadeLinksNotConnectedToSelectedStates_2", FsmEditorSettings.FadeLinksNotConnectedToSelectedStates);
        EditorPrefs.SetBool("PlayMaker.AnimateUI", FsmEditorSettings.AnimateUI);
        EditorPrefs.SetBool("PlayMaker.DisconnectModifiedInstances", FsmEditorSettings.DisconnectModifiedInstances);
        EditorPrefs.SetBool("PlayMaker.DisconnectModifiedInstancesInScene", FsmEditorSettings.DisconnectModifiedInstancesInScene);
        EditorPrefs.SetFloat("PlayMaker.InspectorPanelWidth", FsmEditor.InspectorPanelWidth);
        EditorPrefs.SetInt("PlayMaker.SelectedTheme_2", FsmEditorSettings.selectedTheme);
        EditorPrefs.SetInt("PlayMaker.ActionBrowserRecentSize", FsmEditorSettings.ActionBrowserRecentSize);
        EditorPrefs.SetInt("PlayMaker.DebuggerStepMode", (int) FsmEditorSettings.DebuggerStepMode);
        EditorPrefs.SetBool("PlayMaker.DisableUndoRedo", FsmEditorSettings.DisableUndoRedo);
        EditorPrefs.SetBool("PlayMaker.DrawAssetThumbnail", FsmEditorSettings.DrawAssetThumbnail);
        EditorPrefs.SetBool("PlayMaker.DrawLinksBehindStates", FsmEditorSettings.DrawLinksBehindStates);
        EditorPrefs.SetBool("PlayMaker.DimFinishedActions", FsmEditorSettings.DimFinishedActions);
        EditorPrefs.SetBool("PlayMaker.AutoRefreshFsmInfo", FsmEditorSettings.AutoRefreshFsmInfo);
        EditorPrefs.SetBool("PlayMaker.ConfirmEditingPrefabInstances", FsmEditorSettings.ConfirmEditingPrefabInstances);
        EditorPrefs.SetBool("PlayMaker.ShowStateLoopCounts", FsmEditorSettings.ShowStateLoopCounts);
        EditorPrefs.SetBool("PlayMaker.DrawFrameAroundGraph", FsmEditorSettings.DrawFrameAroundGraph);
        EditorPrefs.SetBool("PlayMaker.GraphViewShowMinimap", FsmEditorSettings.GraphViewShowMinimap);
        EditorPrefs.SetFloat("PlayMaker.GraphViewMinimapSize", FsmEditorSettings.GraphViewMinimapSize);
        EditorPrefs.SetFloat("PlayMaker.GraphViewZoomSpeed", FsmEditorSettings.GraphViewZoomSpeed);
        EditorPrefs.SetBool("PlayMaker.MouseWheelScrollsGraphView", FsmEditorSettings.MouseWheelScrollsGraphView);
        EditorPrefs.SetString("PlayMaker.Language", FsmEditorSettings.supportedCultures[FsmEditorSettings.selectedCulture]);
        EditorPrefs.SetString("PlayMaker.ScreenshotsPath", FsmEditorSettings.ScreenshotsPath);
        EditorPrefs.SetString("PlayMaker.ExportLuaPath", FsmEditorSettings.ExportLuaPath);
        EditorPrefs.SetString("PlayMaker.StartStateName", FsmEditorSettings.StartStateName);
        EditorPrefs.SetString("PlayMaker.NewStateName", FsmEditorSettings.NewStateName);
        EditorPrefs.SetBool("PlayMaker.AutoSelectGameObject", FsmEditorSettings.AutoSelectGameObject);
        EditorPrefs.SetBool("PlayMaker.SelectStateOnActivated", FsmEditorSettings.SelectStateOnActivated);
        EditorPrefs.SetBool("PlayMaker.GotoBreakpoint", FsmEditorSettings.JumpToBreakpoint);
        EditorPrefs.SetInt("PlayMaker.GameStateIconSize", FsmEditorSettings.GameStateIconSize);
        EditorPrefs.SetBool("PlayMaker.FrameSelectedState", FsmEditorSettings.FrameSelectedState);
        EditorPrefs.SetBool("PlayMaker.SyncLogSelection", FsmEditorSettings.SyncLogSelection);
        EditorPrefs.SetBool("PlayMaker.BreakpointsEnabled", FsmEditorSettings.BreakpointsEnabled);
        EditorPrefs.SetBool("PlayMaker.MirrorDebugLog", FsmEditorSettings.MirrorDebugLog);
        EditorPrefs.SetBool("PlayMaker.LockGraphView", FsmEditorSettings.LockGraphView);
        EditorPrefs.SetInt("PlayMaker.GraphLinkStyle", (int) FsmEditorSettings.GraphViewLinkStyle);
        EditorPrefs.SetBool("PlayMaker.ShowFsmDescriptionInGraphView", FsmEditorSettings.ShowFsmDescriptionInGraphView);
        EditorPrefs.SetBool("PlayMaker.ShowCommentsInGraphView", FsmEditorSettings.ShowCommentsInGraphView);
        EditorPrefs.SetBool("PlayMaker.ShowStateLabelsInGameView", FsmEditorSettings.ShowStateLabelsInGameView);
        EditorPrefs.SetBool("PlayMaker.ShowStateLabelsInBuild", FsmEditorSettings.ShowStateLabelsInBuild);
        EditorPrefs.SetBool("PlayMaker.ShowSelectedGameObjectLabel", FsmEditorSettings.ShowSelectedGameObjectLabel);
        EditorPrefs.SetBool("PlayMaker.ShowSelectedFsmLabel", FsmEditorSettings.ShowSelectedFsmLabel);
        EditorPrefs.SetBool("PlayMaker.ShowStateDescription", FsmEditorSettings.ShowStateDescription);
        EditorPrefs.SetBool("PlayMaker.DebugActionParameters", FsmEditorSettings.DebugActionParameters);
        EditorPrefs.SetBool("PlayMaker.DrawPlaymakerGizmos", FsmEditorSettings.DrawPlaymakerGizmos);
        EditorPrefs.SetBool("PlayMaker.DrawPlaymakerGizmoInHierarchy", FsmEditorSettings.DrawPlaymakerGizmoInHierarchy);
        EditorPrefs.SetBool("PlayMaker.ShowEditWhileRunningWarning", FsmEditorSettings.ShowEditWhileRunningWarning);
        EditorPrefs.SetBool("PlayMaker.HideUnusedEvents", FsmEditorSettings.HideUnusedEvents);
        EditorPrefs.SetBool("PlayMaker.ShowActionPreview", FsmEditorSettings.ShowActionPreview);
        EditorPrefs.SetBool("PlayMaker.ShowTemplatePreview", FsmEditorSettings.ShowTemplatePreview);
        EditorPrefs.SetInt("PlayMaker.SelectedActionCategory", FsmEditorSettings.SelectedActionCategory);
        EditorPrefs.SetBool("PlayMaker.SelectFSMInGameView", FsmEditorSettings.SelectFSMInGameView);
        EditorPrefs.SetBool("PlayMaker.HideUnusedParams", FsmEditorSettings.HideUnusedParams);
        EditorPrefs.SetBool("PlayMaker.EnableRealtimeErrorChecker", FsmEditorSettings.EnableRealtimeErrorChecker);
        EditorPrefs.SetBool("PlayMaker.AutoAddPlayMakerGUI", FsmEditorSettings.AutoAddPlayMakerGUI);
        EditorPrefs.SetBool("PlayMaker.DimUnusedParameters", FsmEditorSettings.DimUnusedActionParameters);
        EditorPrefs.SetBool("PlayMaker.AddPrefabLabel", FsmEditorSettings.AddPrefabLabel);
        EditorPrefs.SetBool("PlayMaker.AutoLoadPrefabs", FsmEditorSettings.AutoLoadPrefabs);
        EditorPrefs.SetBool("PlayMaker.LoadAllPrefabs", FsmEditorSettings.LoadAllPrefabs);
        EditorPrefs.SetBool("PlayMaker.LoadAllTemplates", FsmEditorSettings.LoadAllTemplates);
        EditorPrefs.SetBool("PlayMaker.UnloadPrefabs", FsmEditorSettings.UnloadPrefabs);
        EditorPrefs.SetInt("PlayMaker.StateMaxWidth", FsmEditorSettings.StateMaxWidth);
        EditorPrefs.SetInt("PlayMaker.StateMinWidth", FsmEditorSettings.StateMinWidth);
        EditorPrefs.SetBool("PlayMaker.ShowScrollBars", FsmEditorSettings.ShowScrollBars);
        EditorPrefs.SetBool("PlayMaker.ShowWatermark", FsmEditorSettings.EnableWatermarks);
        EditorPrefs.SetBool("PlayMaker.ShowGrid", FsmEditorSettings.ShowGrid);
        EditorPrefs.SetInt("PlayMaker.SnapGridSize", FsmEditorSettings.SnapGridSize);
        EditorPrefs.SetBool("PlayMaker.SnapToGrid", FsmEditorSettings.SnapToGrid);
        EditorPrefs.SetBool("PlayMaker.EnableLogging", FsmEditorSettings.EnableLogging);
        EditorPrefs.SetBool("PlayMaker.DisableErrorCheckerWhenPlaying", FsmEditorSettings.DisableErrorCheckerWhenPlaying);
        EditorPrefs.SetBool("PlayMaker.ColorLinks_2", FsmEditorSettings.ColorLinks);
        EditorPrefs.SetBool("PlayMaker.HideObsoleteActions", FsmEditorSettings.HideObsoleteActions);
        EditorPrefs.SetFloat("PlayMaker.EdgeScrollSpeed", FsmEditorSettings.EdgeScrollSpeed);
        EditorPrefs.SetFloat("PlayMaker.AutoPanZone", FsmEditorSettings.EdgeScrollZone);
        EditorPrefs.SetBool("PlayMaker.CheckForRequiredComponent", FsmEditorSettings.CheckForRequiredComponent);
        EditorPrefs.SetBool("PlayMaker.CheckForRequiredField", FsmEditorSettings.CheckForRequiredField);
        EditorPrefs.SetBool("PlayMaker.CheckForTransitionMissingEvent", FsmEditorSettings.CheckForTransitionMissingEvent);
        EditorPrefs.SetBool("PlayMaker.CheckForTransitionMissingTarget", FsmEditorSettings.CheckForTransitionMissingTarget);
        EditorPrefs.SetBool("PlayMaker.CheckForDuplicateTransitionEvent", FsmEditorSettings.CheckForDuplicateTransitionEvent);
        EditorPrefs.SetBool("PlayMaker.CheckForMouseEventErrors", FsmEditorSettings.CheckForMouseEventErrors);
        EditorPrefs.SetBool("PlayMaker.CheckForCollisionEventErrors", FsmEditorSettings.CheckForCollisionEventErrors);
        EditorPrefs.SetBool("PlayMaker.CheckForEventNotUsed", FsmEditorSettings.CheckForEventNotUsed);
        EditorPrefs.SetBool("PlayMaker.CheckForPrefabRestrictions", FsmEditorSettings.CheckForPrefabRestrictions);
        EditorPrefs.SetBool("PlayMaker.CheckForObsoleteActions", FsmEditorSettings.CheckForObsoleteActions);
        EditorPrefs.SetBool("PlayMaker.CheckForMissingActions", FsmEditorSettings.CheckForMissingActions);
        EditorPrefs.SetBool("PlayMaker.CheckForNetworkSetupErrors", FsmEditorSettings.CheckForNetworkSetupErrors);
        EditorPrefs.SetInt("PlayMaker.ColorScheme", (int) FsmEditorSettings.ColorScheme);
        EditorPrefs.SetBool("PlayMaker.DisableEditorWhenPlaying", FsmEditorSettings.DisableEditorWhenPlaying);
        EditorPrefs.SetBool("PlayMaker.DisableInspectorWhenPlaying", FsmEditorSettings.DisableInspectorWhenPlaying);
        EditorPrefs.SetBool("PlayMaker.DisableToolWindowsWhenPlaying", FsmEditorSettings.DisableToolWindowsWhenPlaying);
        EditorPrefs.SetBool("PlayMaker.DisableActionBrowerWhenPlaying", FsmEditorSettings.DisableActionBrowerWhenPlaying);
        EditorPrefs.SetBool("PlayMaker.DisableEventBrowserWhenPlaying", FsmEditorSettings.DisableEventBrowserWhenPlaying);
        EditorPrefs.SetBool("PlayMaker.ShowHints_", FsmEditorSettings.ShowHints);
        EditorPrefs.SetBool("PlayMaker.CloseActionBrowserOnEnter", FsmEditorSettings.CloseActionBrowserOnEnter);
        EditorPrefs.SetBool("PlayMaker.LogPauseOnSelect", FsmEditorSettings.LogPauseOnSelect);
        EditorPrefs.SetBool("PlayMaker.LogShowSentBy", FsmEditorSettings.LogShowSentBy);
        EditorPrefs.SetBool("PlayMaker.LogShowExit", FsmEditorSettings.LogShowExit);
        EditorPrefs.SetBool("PlayMaker.LogShowTimecode", FsmEditorSettings.LogShowTimecode);
        EditorPrefs.SetBool("PlayMaker.EnableDebugFlow", FsmEditorSettings.EnableDebugFlow);
        EditorPrefs.SetBool("PlayMaker.EnableTransitionEffects", FsmEditorSettings.EnableTransitionEffects);
        EditorPrefs.SetInt("PlayMaker.ConsoleActionReportSortOptionIndex", FsmEditorSettings.ConsoleActionReportSortOptionIndex);
        EditorPrefs.SetBool("PlayMaker.DebugVariables", FsmEditorSettings.DebugVariables);
        EditorPrefs.SetBool("PlayMaker.SelectNewVariables", FsmEditorSettings.SelectNewVariables);
        EditorPrefs.SetBool("PlayMaker.FsmBrowserShowFullPath", FsmEditorSettings.FsmBrowserShowFullPath);
        EditorPrefs.SetBool("PlayMaker.FsmBrowserShowDisabled", FsmEditorSettings.FsmBrowserShowDisabled);
        EditorPrefs.SetBool("PlayMaker.FsmBrowserShowPrefabs", FsmEditorSettings.FsmBrowserShowPrefabs);
        EditorPrefs.SetBool("PlayMaker.FsmBrowserHidePrefabsWhenPlaying", FsmEditorSettings.FsmBrowserHidePrefabsWhenPlaying);
        EditorPrefs.SetBool("PlayMaker.FsmBrowserShowAllStates", FsmEditorSettings.FsmBrowserShowAllStates);
        EditorPrefs.SetBool("PlayMaker.FsmBrowserShowEnabledCheckboxes", FsmEditorSettings.FsmBrowserShowEnabledCheckboxes);
        EditorPrefs.SetBool("PlayMaker.AutoRefreshActionUsage", FsmEditorSettings.AutoRefreshActionUsage);
        EditorPrefs.SetBool("PlayMaker.ShowUsedActionsOnly", FsmEditorSettings.ShowUsedActionsOnly);
        EditorPrefs.SetBool("PlayMaker.HideUnusedPools", FsmEditorSettings.HideUnusedPools);
        EditorPrefs.SetBool("PlayMaker.DisablePoolBrowserWhenPlaying", FsmEditorSettings.DisablePoolBrowserWhenPlaying);
        FsmEditor.Repaint(true);
      }
    }

    [Localizable(false)]
    public static void LoadSettings()
    {
      if (FsmEditorSettings.settingsLoaded)
        return;
      FsmEditorSettings.settingsLoaded = true;
      FsmEditorSettings.UseAdvancedDropdowns = EditorPrefs.GetBool("PlayMaker.UseAdvancedDropdowns", false);
      FsmEditorSettings.UseLegacyNetworking = false;
      System.Type globalType = ReflectionUtils.GetGlobalType("HutongGames.PlayMakerEditor.EditorStartupPrefs");
      if (globalType != null)
      {
        PropertyInfo property = globalType.GetProperty("UseLegacyNetworking", BindingFlags.Static | BindingFlags.Public);
        if (property != null)
          FsmEditorSettings.UseLegacyNetworking = (bool) property.GetValue((object) null, (object[]) null);
      }
      FsmEditorSettings.selectedTheme = EditorPrefs.GetInt("PlayMaker.SelectedTheme_2", 1);
      FsmEditorSettings.selectedCulture = 0;
      string str = EditorPrefs.GetString("PlayMaker.Language", "");
      for (int index = 0; index < FsmEditorSettings.supportedCultures.Length; ++index)
      {
        if (FsmEditorSettings.supportedCultures[index] == str)
          FsmEditorSettings.selectedCulture = index;
      }
      FsmEditorSettings.VariableNamingConvention = (StringHelper.NamingConvention) EditorPrefs.GetInt("PlayMaker.VariableNamingConvention", 1);
      FsmEditorSettings.DebugPerformance = EditorPrefs.GetBool("PlayMaker.DebugPerformance", false);
      FsmEditorSettings.InspectorPosition = (FsmEditorSettings.InspectorPanelPosition) EditorPrefs.GetInt("PlayMaker.InspectorPosition", 0);
      FsmEditorSettings.ShowFullFsmInspector = EditorPrefs.GetBool("PlayMaker.ShowFullFsmInspector", false);
      FsmEditorSettings.FsmControlsShowEnabledCheckboxes = EditorPrefs.GetBool("PlayMaker.FsmControlsShowEnabledCheckboxes", false);
      FsmEditorSettings.FsmControlsShowEmptyFSMs = EditorPrefs.GetBool("PlayMaker.FsmControlsShowEmptyFSMs", false);
      FsmEditorSettings.GroupVariableInputOutputs = EditorPrefs.GetBool("PlayMaker.GroupVariableInputOutputs", true);
      FsmEditorSettings.ErrorCheckTimeslice = EditorPrefs.GetInt("PlayMaker.ErrorCheckTimeslice", 100);
      FsmEditorSettings.ActionUsageTimeslice = EditorPrefs.GetInt("PlayMaker.ActionUsageTimeslice", 50);
      FsmEditorSettings.InspectorColorsAlpha = EditorPrefs.GetFloat("PlayMaker.InspectorColorsAlpha", 0.5f);
      FsmEditorSettings.UseAlphaFeatures = EditorPrefs.GetBool("PlayMaker.UseAlphaFeatures", false);
      FsmEditorSettings.UsePixelCaching = false;
      FsmEditorSettings.DebugPixelCaching = EditorPrefs.GetBool("PlayMaker.DebugPixelCaching", false);
      FsmEditorSettings.LogFsmUpdatedMessages = EditorPrefs.GetBool("PlayMaker.LogFsmUpdatedMessages", false);
      FsmEditorSettings.ModernUIStyle = false;
      FsmEditorSettings.ShowFsmPasswordControls = EditorPrefs.GetBool("PlayMaker.ShowFsmLockControls", false);
      FsmEditorSettings.ShowExperimentalFsmSettings = EditorPrefs.GetBool("PlayMaker.ShowExperimentalFsmSettings", false);
      FsmEditorSettings.InspectorWideMode = EditorPrefs.GetBool("PlayMaker.InspectorWideMode", true);
      FsmEditorSettings.AutoSummarizeFoldedActions = EditorPrefs.GetBool("PlayMaker.AutoSummarizeFoldedActions", true);
      FsmEditorSettings.PingOpenEditorWindows = EditorPrefs.GetBool("PlayMaker.PingOpenEditorWindows", true);
      FsmEditorSettings.ShowSendEventsIconOnStates = EditorPrefs.GetBool("PlayMaker.ShowSendEventsIconOnStates_", true);
      FsmEditorSettings.FadeLinksNotConnectedToSelectedStates = EditorPrefs.GetBool("PlayMaker.FadeLinksNotConnectedToSelectedStates_2", true);
      FsmEditorSettings.AnimateUI = EditorPrefs.GetBool("PlayMaker.AnimateUI", true);
      FsmEditorSettings.DisconnectModifiedInstances = EditorPrefs.GetBool("PlayMaker.DisconnectModifiedInstances", true);
      FsmEditorSettings.DisconnectModifiedInstancesInScene = EditorPrefs.GetBool("PlayMaker.DisconnectModifiedInstancesInScene", true);
      FsmEditor.InspectorPanelWidth = EditorPrefs.GetFloat("PlayMaker.InspectorPanelWidth", 350f);
      FsmEditorSettings.ActionBrowserRecentSize = EditorPrefs.GetInt("PlayMaker.ActionBrowserRecentSize", 20);
      FsmEditorSettings.DebuggerStepMode = (FsmDebugger.FsmStepMode) EditorPrefs.GetInt("PlayMaker.DebuggerStepMode", 0);
      FsmEditorSettings.DisableUndoRedo = EditorPrefs.GetBool("PlayMaker.DisableUndoRedo", false);
      FsmEditorSettings.DrawAssetThumbnail = EditorPrefs.GetBool("PlayMaker.DrawAssetThumbnail", true);
      FsmEditorSettings.DrawLinksBehindStates = EditorPrefs.GetBool("PlayMaker.DrawLinksBehindStates", true);
      FsmEditorSettings.DimFinishedActions = EditorPrefs.GetBool("PlayMaker.DimFinishedActions", true);
      FsmEditorSettings.AutoRefreshFsmInfo = EditorPrefs.GetBool("PlayMaker.AutoRefreshFsmInfo", true);
      FsmEditorSettings.ConfirmEditingPrefabInstances = EditorPrefs.GetBool("PlayMaker.ConfirmEditingPrefabInstances", true);
      FsmEditorSettings.ShowStateLoopCounts = EditorPrefs.GetBool("PlayMaker.ShowStateLoopCounts", false);
      FsmEditorSettings.DrawFrameAroundGraph = EditorPrefs.GetBool("PlayMaker.DrawFrameAroundGraph", false);
      FsmEditorSettings.GraphViewShowMinimap = EditorPrefs.GetBool("PlayMaker.GraphViewShowMinimap", true);
      FsmEditorSettings.GraphViewMinimapSize = EditorPrefs.GetFloat("PlayMaker.GraphViewMinimapSize", 300f);
      FsmEditorSettings.GraphViewZoomSpeed = EditorPrefs.GetFloat("PlayMaker.GraphViewZoomSpeed", 0.01f);
      FsmEditorSettings.MouseWheelScrollsGraphView = EditorPrefs.GetBool("PlayMaker.MouseWheelScrollsGraphView", false);
      FsmEditorSettings.ScreenshotsPath = EditorPrefs.GetString("PlayMaker.ScreenshotsPath", "PlayMaker/Screenshots");
      FsmEditorSettings.ExportLuaPath = EditorPrefs.GetString("PlayMaker.ExportLuaPathEx", "../../AOW_COMMON/lua/fsm");
      FsmEditorSettings.DebugVariables = EditorPrefs.GetBool("PlayMaker.DebugVariables", false);
      FsmEditorSettings.ConsoleActionReportSortOptionIndex = EditorPrefs.GetInt("PlayMaker.ConsoleActionReportSortOptionIndex", 1);
      FsmEditorSettings.LogPauseOnSelect = EditorPrefs.GetBool("PlayMaker.LogPauseOnSelect", true);
      FsmEditorSettings.LogShowSentBy = EditorPrefs.GetBool("PlayMaker.LogShowSentBy", true);
      FsmEditorSettings.LogShowExit = EditorPrefs.GetBool("PlayMaker.LogShowExit", true);
      FsmEditorSettings.LogShowTimecode = EditorPrefs.GetBool("PlayMaker.LogShowTimecode", false);
      FsmEditorSettings.ShowHints = EditorPrefs.GetBool("PlayMaker.ShowHints_", false);
      FsmEditorSettings.CloseActionBrowserOnEnter = EditorPrefs.GetBool("PlayMaker.CloseActionBrowserOnEnter", false);
      FsmEditorSettings.DisableEditorWhenPlaying = EditorPrefs.GetBool("PlayMaker.DisableEditorWhenPlaying", false);
      FsmEditorSettings.DisableInspectorWhenPlaying = EditorPrefs.GetBool("PlayMaker.DisableInspectorWhenPlaying", false);
      FsmEditorSettings.DisableToolWindowsWhenPlaying = EditorPrefs.GetBool("PlayMaker.DisableToolWindowsWhenPlaying", true);
      FsmEditorSettings.DisableActionBrowerWhenPlaying = EditorPrefs.GetBool("PlayMaker.DisableActionBrowerWhenPlaying", false);
      FsmEditorSettings.DisableEventBrowserWhenPlaying = EditorPrefs.GetBool("PlayMaker.DisableEventBrowserWhenPlaying", false);
      FsmEditorSettings.ColorScheme = (FsmEditorStyles.ColorScheme) EditorPrefs.GetInt("PlayMaker.ColorScheme", 0);
      FsmEditorSettings.EnableRealtimeErrorChecker = EditorPrefs.GetBool("PlayMaker.EnableRealtimeErrorChecker", true);
      FsmEditorSettings.CheckForRequiredComponent = EditorPrefs.GetBool("PlayMaker.CheckForRequiredComponent", true);
      FsmEditorSettings.CheckForRequiredField = EditorPrefs.GetBool("PlayMaker.CheckForRequiredField", true);
      FsmEditorSettings.CheckForEventNotUsed = EditorPrefs.GetBool("PlayMaker.CheckForEventNotUsed", true);
      FsmEditorSettings.CheckForTransitionMissingEvent = EditorPrefs.GetBool("PlayMaker.CheckForTransitionMissingEvent", true);
      FsmEditorSettings.CheckForTransitionMissingTarget = EditorPrefs.GetBool("PlayMaker.CheckForTransitionMissingTarget", true);
      FsmEditorSettings.CheckForDuplicateTransitionEvent = EditorPrefs.GetBool("PlayMaker.CheckForDuplicateTransitionEvent", true);
      FsmEditorSettings.CheckForMouseEventErrors = EditorPrefs.GetBool("PlayMaker.CheckForMouseEventErrors", true);
      FsmEditorSettings.CheckForCollisionEventErrors = EditorPrefs.GetBool("PlayMaker.CheckForCollisionEventErrors", true);
      FsmEditorSettings.CheckForPrefabRestrictions = EditorPrefs.GetBool("PlayMaker.CheckForPrefabRestrictions", true);
      FsmEditorSettings.CheckForObsoleteActions = EditorPrefs.GetBool("PlayMaker.CheckForObsoleteActions", true);
      FsmEditorSettings.CheckForMissingActions = EditorPrefs.GetBool("PlayMaker.CheckForMissingActions", true);
      FsmEditorSettings.CheckForNetworkSetupErrors = EditorPrefs.GetBool("PlayMaker.CheckForNetworkSetupErrors", true);
      FsmEditorSettings.EdgeScrollZone = EditorPrefs.GetFloat("PlayMaker.AutoPanZone", 100f);
      FsmEditorSettings.EdgeScrollSpeed = EditorPrefs.GetFloat("PlayMaker.EdgeScrollSpeed", 1f);
      FsmEditorSettings.HideObsoleteActions = EditorPrefs.GetBool("PlayMaker.HideObsoleteActions", true);
      FsmEditorSettings.ColorLinks = EditorPrefs.GetBool("PlayMaker.ColorLinks_2", true);
      FsmEditorSettings.DisableErrorCheckerWhenPlaying = EditorPrefs.GetBool("PlayMaker.DisableErrorCheckerWhenPlaying", true);
      FsmEditorSettings.EnableLogging = EditorPrefs.GetBool("PlayMaker.EnableLogging", true);
      FsmEditorSettings.ShowGrid = EditorPrefs.GetBool("PlayMaker.ShowGrid", true);
      FsmEditorSettings.SnapGridSize = EditorPrefs.GetInt("PlayMaker.SnapGridSize", 16);
      FsmEditorSettings.SnapToGrid = EditorPrefs.GetBool("PlayMaker.SnapToGrid", false);
      FsmEditorSettings.ShowScrollBars = EditorPrefs.GetBool("PlayMaker.ShowScrollBars", true);
      FsmEditorSettings.EnableWatermarks = EditorPrefs.GetBool("PlayMaker.ShowWatermark", true);
      FsmEditorSettings.StateMaxWidth = EditorPrefs.GetInt("PlayMaker.StateMaxWidth", 400);
      FsmEditorSettings.StateMinWidth = EditorPrefs.GetInt("PlayMaker.StateMinWidth", 100);
      FsmEditorSettings.AddPrefabLabel = EditorPrefs.GetBool("PlayMaker.AddPrefabLabel", true);
      FsmEditorSettings.AutoLoadPrefabs = EditorPrefs.GetBool("PlayMaker.AutoLoadPrefabs", true);
      FsmEditorSettings.LoadAllPrefabs = EditorPrefs.GetBool("PlayMaker.LoadAllPrefabs", true);
      FsmEditorSettings.LoadAllTemplates = EditorPrefs.GetBool("PlayMaker.LoadAllTemplates", true);
      FsmEditorSettings.UnloadPrefabs = EditorPrefs.GetBool("PlayMaker.UnloadPrefabs", true);
      FsmEditorSettings.StartStateName = EditorPrefs.GetString("PlayMaker.StartStateName", "State 1");
      FsmEditorSettings.NewStateName = EditorPrefs.GetString("PlayMaker.NewStateName", Strings.FsmEditorSettings_Default_State_Name);
      FsmEditorSettings.AutoSelectGameObject = EditorPrefs.GetBool("PlayMaker.AutoSelectGameObject", true);
      FsmEditorSettings.SelectStateOnActivated = EditorPrefs.GetBool("PlayMaker.SelectStateOnActivated", true);
      FsmEditorSettings.JumpToBreakpoint = EditorPrefs.GetBool("PlayMaker.GotoBreakpoint", true);
      FsmEditorSettings.GameStateIconSize = EditorPrefs.GetInt("PlayMaker.GameStateIconSize", 32);
      FsmEditorSettings.FrameSelectedState = EditorPrefs.GetBool("PlayMaker.FrameSelectedState", false);
      FsmEditorSettings.SyncLogSelection = EditorPrefs.GetBool("PlayMaker.SyncLogSelection", true);
      FsmEditorSettings.BreakpointsEnabled = EditorPrefs.GetBool("PlayMaker.BreakpointsEnabled", true);
      FsmEditorSettings.MirrorDebugLog = EditorPrefs.GetBool("PlayMaker.MirrorDebugLog", false);
      FsmEditorSettings.LockGraphView = EditorPrefs.GetBool("PlayMaker.LockGraphView", false);
      FsmEditorSettings.GraphViewLinkStyle = (GraphViewLinkStyle) EditorPrefs.GetInt("PlayMaker.GraphLinkStyle", 0);
      FsmEditorSettings.ShowFsmDescriptionInGraphView = EditorPrefs.GetBool("PlayMaker.ShowFsmDescriptionInGraphView", true);
      FsmEditorSettings.ShowCommentsInGraphView = EditorPrefs.GetBool("PlayMaker.ShowCommentsInGraphView", true);
      FsmEditorSettings.ShowStateLabelsInGameView = EditorPrefs.GetBool(EditorPrefStrings.ShowStateLabelsInGameView, true);
      FsmEditorSettings.ShowStateLabelsInBuild = EditorPrefs.GetBool(EditorPrefStrings.ShowStateLabelsInBuild, false);
      FsmEditorSettings.DrawPlaymakerGizmos = EditorPrefs.GetBool("PlayMaker.DrawPlaymakerGizmos", true);
      FsmEditorSettings.DrawPlaymakerGizmoInHierarchy = EditorPrefs.GetBool("PlayMaker.DrawPlaymakerGizmoInHierarchy", true);
      FsmEditorSettings.ShowEditWhileRunningWarning = EditorPrefs.GetBool("PlayMaker.ShowEditWhileRunningWarning", true);
      FsmEditorSettings.ShowSelectedGameObjectLabel = EditorPrefs.GetBool("PlayMaker.ShowSelectedGameObjectLabel", true);
      FsmEditorSettings.ShowSelectedFsmLabel = EditorPrefs.GetBool("PlayMaker.ShowSelectedFsmLabel", true);
      FsmEditorSettings.ShowStateDescription = EditorPrefs.GetBool("PlayMaker.ShowStateDescription", true);
      FsmEditorSettings.DebugActionParameters = EditorPrefs.GetBool("PlayMaker.DebugActionParameters", false);
      FsmEditorSettings.HideUnusedEvents = EditorPrefs.GetBool("PlayMaker.HideUnusedEvents", false);
      FsmEditorSettings.ShowActionPreview = EditorPrefs.GetBool("PlayMaker.ShowActionPreview", true);
      FsmEditorSettings.ShowTemplatePreview = EditorPrefs.GetBool("PlayMaker.ShowTemplatePreview", true);
      FsmEditorSettings.SelectedActionCategory = EditorPrefs.GetInt("PlayMaker.SelectedActionCategory", 0);
      FsmEditorSettings.SelectedTemplateCategory = EditorPrefs.GetInt("PlayMaker.SelectedTemplateCategory", 0);
      FsmEditorSettings.SelectFSMInGameView = EditorPrefs.GetBool("PlayMaker.SelectFSMInGameView", true);
      FsmEditorSettings.HideUnusedParams = EditorPrefs.GetBool("PlayMaker.HideUnusedParams", false);
      FsmEditorSettings.AutoAddPlayMakerGUI = EditorPrefs.GetBool("PlayMaker.AutoAddPlayMakerGUI", true);
      FsmEditorSettings.DimUnusedActionParameters = EditorPrefs.GetBool("PlayMaker.DimUnusedParameters", false);
      FsmEditorSettings.SelectNewVariables = EditorPrefs.GetBool("PlayMaker.SelectNewVariables", true);
      FsmEditorSettings.FsmBrowserShowFullPath = EditorPrefs.GetBool("PlayMaker.FsmBrowserShowFullPath", true);
      FsmEditorSettings.FsmBrowserShowDisabled = EditorPrefs.GetBool("PlayMaker.FsmBrowserShowDisabled", true);
      FsmEditorSettings.FsmBrowserShowPrefabs = EditorPrefs.GetBool("PlayMaker.FsmBrowserShowPrefabs", true);
      FsmEditorSettings.FsmBrowserHidePrefabsWhenPlaying = EditorPrefs.GetBool("PlayMaker.FsmBrowserHidePrefabsWhenPlaying", true);
      FsmEditorSettings.FsmBrowserShowAllStates = EditorPrefs.GetBool("PlayMaker.FsmBrowserShowAllStates", true);
      FsmEditorSettings.FsmBrowserShowEnabledCheckboxes = EditorPrefs.GetBool("PlayMaker.FsmBrowserShowEnabledCheckboxes", false);
      FsmEditorSettings.EnableDebugFlow = EditorPrefs.GetBool("PlayMaker.EnableDebugFlow", true);
      FsmEditorSettings.EnableTransitionEffects = EditorPrefs.GetBool("PlayMaker.EnableTransitionEffects", true);
      FsmEditorSettings.AutoRefreshActionUsage = EditorPrefs.GetBool("PlayMaker.AutoRefreshActionUsage", true);
      FsmEditorSettings.ShowUsedActionsOnly = EditorPrefs.GetBool("PlayMaker.ShowUsedActionsOnly", false);
      FsmEditorSettings.HideUnusedPools = EditorPrefs.GetBool("PlayMaker.HideUnusedPools", false);
      FsmEditorSettings.DisablePoolBrowserWhenPlaying = EditorPrefs.GetBool("PlayMaker.DisablePoolBrowserWhenPlaying", false);
      FsmEditorSettings.ValidateSettings();
      FsmEditorSettings.ApplySettings();
      FsmEditorSettings.SaveSettings();
    }

    private static void ValidateSettings()
    {
      if (string.IsNullOrEmpty(FsmEditorSettings.NewStateName) || FsmEditorSettings.StateMaxWidth == 0)
      {
        Debug.LogWarning((object) Strings.FsmEditorSettings_Preferences_Reset);
        FsmEditorSettings.ResetDefaults();
      }
      FsmEditorSettings.EdgeScrollSpeed = Mathf.Clamp(FsmEditorSettings.EdgeScrollSpeed, 0.1f, 10f);
      FsmEditorSettings.InspectorColorsAlpha = Mathf.Clamp(FsmEditorSettings.InspectorColorsAlpha, 0.0f, 1f);
      FsmEditorSettings.ErrorCheckTimeslice = Mathf.Clamp(FsmEditorSettings.ErrorCheckTimeslice, 1, 10000);
      FsmEditorSettings.ActionUsageTimeslice = Mathf.Clamp(FsmEditorSettings.ActionUsageTimeslice, 1, 10000);
    }

    public static void ApplySettings()
    {
      PixelCache.DebugArea = FsmEditorSettings.DebugPixelCaching;
      FsmDebugger.Instance.StepMode = FsmEditorSettings.DebuggerStepMode;
      FsmLog.MirrorDebugLog = FsmEditorSettings.MirrorDebugLog;
      FsmLog.LoggingEnabled = FsmEditorSettings.EnableLogging;
      FsmLog.EnableDebugFlow = FsmEditorSettings.EnableDebugFlow;
      Fsm.BreakpointsEnabled = FsmEditorSettings.BreakpointsEnabled;
      PlayMakerFSM.DrawGizmos = FsmEditorSettings.DrawPlaymakerGizmos;
      PlayMakerGUI.EnableStateLabels = FsmEditorSettings.ShowStateLabelsInGameView;
      PlayMakerGUI.EnableStateLabelsInBuild = FsmEditorSettings.ShowStateLabelsInBuild;
      FsmEditorSettings.SetCulture(FsmEditorSettings.selectedCulture);
      FsmEditorSettings.SetTheme(FsmEditorSettings.selectedTheme);
      if (FsmEditor.Instance == null)
        return;
      FsmEditor.GraphView.ApplySettings();
      FsmEditor.RepaintAll();
    }

    public static int PackColorIntoInt(Color color) => (int) ((double) color.r * (double) byte.MaxValue) << 16 | (int) ((double) color.g * (double) byte.MaxValue) << 8 | (int) ((double) color.b * (double) byte.MaxValue);

    public static Color UnpackColorFromInt(int packedValue) => new Color((float) (packedValue >> 16) / (float) byte.MaxValue, (float) (packedValue >> 8 & (int) byte.MaxValue) / (float) byte.MaxValue, (float) (packedValue & (int) byte.MaxValue) / (float) byte.MaxValue, 1f);

    public enum Categories
    {
      General,
      GraphView,
      ErrorChecking,
      Debugging,
      Colors,
      Experimental,
    }

    public enum InspectorPanelPosition
    {
      Right,
      Left,
    }
  }
}
