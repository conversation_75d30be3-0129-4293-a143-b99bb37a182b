// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.MainToolbar
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class MainToolbar
  {
    private static Rect toolbarRect;

    public void OnGUI(Rect area)
    {
      MainToolbar.toolbarRect = area;
      GUILayout.BeginArea(MainToolbar.toolbarRect);
      EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
      if (FsmEditorStyles.UsingFlatStyle)
        GUILayout.Space(4f);
      MainToolbar.DoFsmSelectorGUI();
      MainToolbar.DoPrefabTypeGUI();
      GUILayout.FlexibleSpace();
      MainToolbar.DoMinimapButtonGUI();
      if (FsmEditorStyles.UsingFlatStyle)
        GUILayout.Box(GUIContent.none, EditorStyles.toolbarButton, GUILayout.Width(10f));
      HighlighterHelper.EndHorizontal("Selection Toolbar", 1);
      GUILayout.EndArea();
    }

    private static void DoFsmSelectorGUI()
    {
      FsmEditorContent.MainToolbarSelectedGO.text = !((UnityEngine.Object) FsmEditor.SelectedTemplate != (UnityEngine.Object) null) ? ((UnityEngine.Object) FsmEditor.SelectedFsmGameObject == (UnityEngine.Object) null ? Strings.Label_None : FsmEditor.SelectedFsmGameObject.name) : FsmEditor.SelectedTemplate.name;
      FsmEditorContent.MainToolbarSelectedGO.tooltip = Strings.Hint_Select_Game_Object;
      EditorGUI.BeginDisabledGroup(!FsmEditor.SelectionHistory.CanMoveBack());
      if (FsmEditorGUILayout.ToolbarIconButton(FsmEditorContent.BackButton))
        FsmEditor.SelectFsm(FsmEditor.SelectionHistory.MoveBack());
      HighlighterHelper.EndDisabledGroup("Back");
      EditorGUI.BeginDisabledGroup(!FsmEditor.SelectionHistory.CanMoveForward());
      if (FsmEditorGUILayout.ToolbarIconButton(FsmEditorContent.ForwardButton))
        FsmEditor.SelectFsm(FsmEditor.SelectionHistory.MoveForward());
      HighlighterHelper.EndDisabledGroup("Forward");
      EditorGUI.BeginDisabledGroup(FsmEditor.SelectionHistory.RecentlySelectedCount <= 0);
      if (FsmEditorGUILayout.ToolbarIconButton(FsmEditorContent.RecentButton))
      {
        GenericMenu genericMenu = new GenericMenu();
        foreach (Fsm recentlySelectedFsM in FsmEditor.SelectionHistory.GetRecentlySelectedFSMs())
          genericMenu.AddItem(new GUIContent(Labels.GetFullFsmLabel(recentlySelectedFsM)), recentlySelectedFsM == FsmEditor.SelectedFsm, new GenericMenu.MenuFunction2(FsmEditor.SelectFsm), (object) recentlySelectedFsM);
        genericMenu.ShowAsContext();
      }
      else
      {
        HighlighterHelper.EndDisabledGroup("History");
        Rect rect1 = GUILayoutUtility.GetRect(FsmEditorContent.MainToolbarSelectedGO, EditorStyles.toolbarDropDown, GUILayout.MinWidth(100f));
        if (GUI.Button(rect1, FsmEditorContent.MainToolbarSelectedGO, EditorStyles.toolbarDropDown))
          Menus.ShowFsmOwnerSelectionDropdownMenu(rect1);
        HighlighterHelper.FromGUILayoutInArea(MainToolbar.toolbarRect, "Selected GameObject");
        FsmEditorContent.MainToolbarSelectedFSM.text = FsmEditor.SelectedFsm == null ? "" : FsmEditor.SelectedFsm.Name;
        FsmEditorContent.MainToolbarSelectedFSM.tooltip = Strings.Tooltip_Select_FSM;
        Rect rect2 = GUILayoutUtility.GetRect(FsmEditorContent.MainToolbarSelectedFSM, EditorStyles.toolbarDropDown, GUILayout.MinWidth(100f));
        if (GUI.Button(rect2, FsmEditorContent.MainToolbarSelectedFSM, EditorStyles.toolbarDropDown))
          Menus.ShowGameObjectFsmSelectionDropdownMenu(rect2);
        HighlighterHelper.FromGUILayoutInArea(MainToolbar.toolbarRect, "Selected FSM");
        EditorGUI.BeginChangeCheck();
        FsmEditorSettings.LockGraphView = GUILayout.Toggle(FsmEditorSettings.LockGraphView, FsmEditorContent.MainToolbarLock, EditorStyles.toolbarButton);
        if (EditorGUI.EndChangeCheck())
          FsmEditor.UpdateLockSelection();
        HighlighterHelper.FromGUILayoutInArea(MainToolbar.toolbarRect, "Lock Selection");
        if (!GUI.changed)
          return;
        FsmEditorSettings.SaveSettings();
      }
    }

    private static void DoPrefabTypeGUI()
    {
      if ((UnityEngine.Object) FsmEditor.SelectedFsmGameObject == (UnityEngine.Object) null || (UnityEngine.Object) FsmEditor.SelectedTemplate != (UnityEngine.Object) null)
        return;
      Fsm selectedFsm = FsmEditor.SelectedFsm;
      if (selectedFsm == null)
        return;
      if (!PrefabUtility.IsPartOfAnyPrefab((UnityEngine.Object) selectedFsm.Owner) && !FsmPrefabs.IsPrefabPreview(selectedFsm))
      {
        if (PrefabUtility.IsAddedComponentOverride((UnityEngine.Object) selectedFsm.Owner))
        {
          if (GUILayout.Button("Prefab Override", EditorStyles.toolbarDropDown))
          {
            GenericMenu genericMenu = new GenericMenu();
            genericMenu.AddItem(new GUIContent(Strings.Menu_Select_GameObject), false, new GenericMenu.MenuFunction(FsmEditor.SelectFsmGameObject));
            genericMenu.AddItem(new GUIContent("Revert Added Component"), false, new GenericMenu.MenuFunction(FsmPrefabs.RevertAddedComponent));
            genericMenu.AddItem(new GUIContent("Apply Added Component"), false, new GenericMenu.MenuFunction(FsmPrefabs.ApplyAddedComponent));
            genericMenu.ShowAsContext();
          }
        }
        else if (GUILayout.Button(FsmEditorContent.MainToolbarPrefabTypeNone, EditorStyles.toolbarButton))
          FsmEditor.SelectFsmGameObject();
      }
      else if (PrefabUtility.IsPartOfPrefabInstance((UnityEngine.Object) selectedFsm.Owner))
      {
        bool modifiedPrefabInstance = selectedFsm.IsModifiedPrefabInstance;
        string str = "";
        if (modifiedPrefabInstance)
          str = Strings.Label_Modified_postfix;
        string text = Strings.Label_Prefab_Instance + str;
        Rect rect = GUILayoutUtility.GetRect(FsmEditorContent.TempContent(text), EditorStyles.toolbarDropDown);
        if (GUI.Button(rect, FsmEditorContent.TempContent(text), EditorStyles.toolbarDropDown))
        {
          GenericMenu genericMenu = new GenericMenu();
          genericMenu.AddItem(new GUIContent(Strings.Menu_Select_GameObject), false, new GenericMenu.MenuFunction(FsmEditor.SelectFsmGameObject));
          genericMenu.AddItem(new GUIContent(Strings.Menu_Select_Prefab), false, new GenericMenu.MenuFunction(FsmEditor.SelectPrefabParent));
          genericMenu.AddItem(new GUIContent("Open Prefab"), false, new GenericMenu.MenuFunction(FsmEditor.OpenPrefabParent));
          if (modifiedPrefabInstance)
          {
            genericMenu.AddItem(new GUIContent("Overrides/Revert All"), false, new GenericMenu.MenuFunction(FsmPrefabs.Revert));
            genericMenu.AddItem(new GUIContent("Overrides/Apply All"), false, new GenericMenu.MenuFunction(FsmPrefabs.Apply));
          }
          else
          {
            genericMenu.AddDisabledItem(new GUIContent("Overrides/Revert All"));
            genericMenu.AddDisabledItem(new GUIContent("Overrides/Apply All"));
          }
          genericMenu.DropDown(rect);
        }
      }
      else if (PrefabUtility.IsPartOfPrefabAsset((UnityEngine.Object) selectedFsm.Owner) || FsmPrefabs.IsPrefabPreview(selectedFsm))
      {
        if (!GUILayout.Button(Strings.Label_Prefab, EditorStyles.toolbarButton))
          ;
      }
      else if (PrefabUtility.IsDisconnectedFromPrefabAsset((UnityEngine.Object) selectedFsm.Owner) && GUILayout.Button(Strings.Label_Prefab_Instance_disconnected, EditorStyles.toolbarDropDown))
      {
        GenericMenu genericMenu = new GenericMenu();
        genericMenu.AddItem(new GUIContent(Strings.Menu_Select_GameObject), false, new GenericMenu.MenuFunction(FsmEditor.SelectFsmGameObject));
        genericMenu.AddItem(new GUIContent(Strings.Menu_Reconnect_to_Prefab), false, new GenericMenu.MenuFunction(FsmEditor.ReconnectToLastPrefab));
        genericMenu.ShowAsContext();
      }
      HighlighterHelper.FromGUILayoutInArea(MainToolbar.toolbarRect, "Select Owner");
    }

    private static void DoMinimapButtonGUI()
    {
      bool flag = FsmEditorGUILayout.ToolbarIconToggle(FsmEditorSettings.GraphViewShowMinimap, FsmEditorContent.MainToolbarShowMinimap);
      if (flag != FsmEditorSettings.GraphViewShowMinimap)
      {
        if (Event.current.button == 1)
        {
          Menus.ShowStateSelectionContextMenu();
        }
        else
        {
          FsmEditorSettings.GraphViewShowMinimap = flag;
          FsmEditorSettings.SaveSettings();
        }
      }
      HighlighterHelper.FromGUILayoutInArea(MainToolbar.toolbarRect, "Minimap Toggle");
    }

    [Obsolete("Use OnGUI(Rect area) instead.")]
    public void OnGUI()
    {
    }
  }
}
