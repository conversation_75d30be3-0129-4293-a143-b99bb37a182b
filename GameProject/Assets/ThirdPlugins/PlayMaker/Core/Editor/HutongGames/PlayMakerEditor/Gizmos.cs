// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Gizmos
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace HutongGames.PlayMakerEditor
{
  [InitializeOnLoad]
  public class Gizmos
  {
    private static bool enableHierarchyItemGizmos;
    public static readonly Dictionary<int, int> lookupFsmCount = new Dictionary<int, int>();
    private static Texture2D _wanIcon;
    private static Rect itemRect = new Rect();

    public static bool EnableHierarchyItemGizmos
    {
      get => Gizmos.enableHierarchyItemGizmos;
      set
      {
        Gizmos.RemoveCallbacks();
        Gizmos.enableHierarchyItemGizmos = value;
        if (!Gizmos.enableHierarchyItemGizmos)
          return;
        Gizmos.AddCallbacks();
      }
    }

    private static Texture2D WanIcon => Gizmos._wanIcon ?? (Gizmos._wanIcon = Files.LoadTexture("wanIcon", 15, 15));

    static Gizmos() => Gizmos.EnableHierarchyItemGizmos = EditorPrefs.GetBool(EditorPrefStrings.DrawPlaymakerGizmoInHierarchy, true);

    ~Gizmos() => Gizmos.RemoveCallbacks();

    public static void Update() => Gizmos.lookupFsmCount.Clear();

    private static void AddCallbacks()
    {
      Gizmos.RemoveCallbacks();
      EditorSceneManager.sceneOpened += new EditorSceneManager.SceneOpenedCallback(Gizmos.OnSceneOpened);
      EditorApplication.hierarchyWindowItemOnGUI += new EditorApplication.HierarchyWindowItemCallback(Gizmos.HierarchyWindowItemCallback);
      EditorApplication.hierarchyChanged += new Action(Gizmos.Update);
    }

    private static void RemoveCallbacks()
    {
      EditorSceneManager.sceneOpened -= new EditorSceneManager.SceneOpenedCallback(Gizmos.OnSceneOpened);
      EditorApplication.hierarchyWindowItemOnGUI -= new EditorApplication.HierarchyWindowItemCallback(Gizmos.HierarchyWindowItemCallback);
      EditorApplication.hierarchyChanged -= new Action(Gizmos.Update);
    }

    private static void OnSceneOpened(Scene scene, OpenSceneMode mode) => Gizmos.Update();

    private static void HierarchyWindowItemCallback(int instanceID, Rect selectionRect)
    {
      int num;
      if (Gizmos.lookupFsmCount.TryGetValue(instanceID, out num))
      {
        if (num <= 0)
          return;
        Gizmos.itemRect.Set((float) ((double) selectionRect.x + (double) selectionRect.width - 14.0), selectionRect.y + 1f, 14f, 14f);
        GUI.DrawTexture(Gizmos.itemRect, (Texture) Gizmos.WanIcon);
      }
      else
      {
        GameObject gameObject = EditorUtility.InstanceIDToObject(instanceID) as GameObject;
        bool flag = (UnityEngine.Object) gameObject != (UnityEngine.Object) null && (UnityEngine.Object) gameObject.GetComponent<PlayMakerFSM>() != (UnityEngine.Object) null;
        Gizmos.lookupFsmCount.Add(instanceID, flag ? 1 : 0);
      }
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("Gizmos: " + message));
  }
}
