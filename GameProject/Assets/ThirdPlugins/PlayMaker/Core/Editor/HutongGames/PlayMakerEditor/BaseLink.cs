// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.BaseLink
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  internal abstract class BaseLink
  {
    protected FsmTransition transition;
    protected FsmTransition.CustomLinkConstraint linkConstraint;
    protected FsmTransition.CustomLinkTarget linkTarget;
    protected float rowHeight;
    protected float rowHeightDiv2;
    protected float fromStateY;
    protected float toStateY;
    protected float fromStateLeftX;
    protected float fromStateRightX;
    protected float toStateLeftX;
    protected float toStateRightX;
    protected Vector3 fromPos;
    protected Vector3 toPos;
    protected Vector3 fromTangent;
    protected Vector3 toTangent;
    protected Texture leftArrow;
    protected Texture rightArrow;
    protected bool flipLeftArrow;
    protected float arrowWidthUnscaled;
    protected float arrowWidth;
    protected float arrowHeight;
    protected float drawScale;
    protected float horizontalMinDistance;
    private Rect arrowRect;

    public abstract GraphViewLinkStyle LinkStyle { get; }

    protected void Init(FsmState fromState, FsmState toState, int transitionIndex, float scale)
    {
      this.arrowWidthUnscaled = (float) FsmEditorStyles.LeftArrow.width;
      this.leftArrow = (Texture) FsmEditorStyles.LeftArrow;
      this.rightArrow = (Texture) FsmEditorStyles.RightArrow;
      this.arrowWidth = (float) this.leftArrow.width * scale / EditorGUIUtility.pixelsPerPoint;
      this.arrowHeight = (float) this.leftArrow.height * scale / EditorGUIUtility.pixelsPerPoint;
      this.drawScale = scale;
      this.transition = fromState.Transitions[transitionIndex];
      this.linkConstraint = this.transition.LinkConstraint;
      this.linkTarget = this.transition.LinkTarget;
      this.rowHeight = 16f;
      this.rowHeightDiv2 = this.rowHeight * 0.5f;
      this.fromStateY = fromState.Position.y + (float) (transitionIndex + 1) * this.rowHeight + this.rowHeightDiv2;
      this.toStateY = toState.Position.y + this.rowHeightDiv2;
      this.fromStateLeftX = fromState.Position.x - 1f;
      this.fromStateRightX = (float) ((double) this.fromStateLeftX + (double) fromState.Position.width + 2.0);
      this.toStateLeftX = toState.Position.x - 1f;
      this.toStateRightX = (float) ((double) this.toStateLeftX + (double) toState.Position.width + 2.0);
      this.fromPos = new Vector3(0.0f, this.fromStateY, 0.0f);
      this.toPos = new Vector3(0.0f, this.toStateY, 0.0f);
      this.fromTangent = this.fromPos;
      this.toTangent = this.toPos;
      this.flipLeftArrow = false;
      this.horizontalMinDistance = 0.0f;
    }

    protected void DoDefaultLinker()
    {
      switch (this.linkConstraint)
      {
        case FsmTransition.CustomLinkConstraint.None:
          switch (this.linkTarget)
          {
            case FsmTransition.CustomLinkTarget.None:
              if ((double) this.toStateRightX + (double) this.horizontalMinDistance < (double) this.fromStateLeftX - (double) this.horizontalMinDistance)
              {
                this.LinkLeftSideToRightSide();
                this.flipLeftArrow = true;
                break;
              }
              if ((double) this.toStateLeftX < (double) this.fromStateLeftX)
              {
                this.LinkLeftSideToLeftSide();
                break;
              }
              if ((double) this.toStateLeftX - (double) this.horizontalMinDistance > (double) this.fromStateRightX + (double) this.horizontalMinDistance)
              {
                this.LinkRightSideToLeftSide();
                break;
              }
              this.LinkRightSideToRightSide();
              this.flipLeftArrow = true;
              break;
            case FsmTransition.CustomLinkTarget.LockLeft:
              if ((double) this.fromStateRightX + (double) this.horizontalMinDistance < (double) this.toStateLeftX)
              {
                this.LinkRightSideToLeftSide();
                break;
              }
              this.LinkLeftSideToLeftSide();
              break;
            case FsmTransition.CustomLinkTarget.LockRight:
              if ((double) this.fromStateLeftX - (double) this.horizontalMinDistance > (double) this.toStateRightX)
              {
                this.LinkLeftSideToRightSide();
                this.flipLeftArrow = true;
                break;
              }
              this.LinkRightSideToRightSide();
              this.flipLeftArrow = true;
              break;
          }
          break;
        case FsmTransition.CustomLinkConstraint.LockLeft:
          switch (this.linkTarget)
          {
            case FsmTransition.CustomLinkTarget.None:
              if ((double) this.toStateRightX < (double) this.fromStateLeftX)
              {
                this.LinkLeftSideToRightSide();
                this.flipLeftArrow = true;
                break;
              }
              this.LinkLeftSideToLeftSide();
              break;
            case FsmTransition.CustomLinkTarget.LockLeft:
              this.LinkLeftSideToLeftSide();
              break;
            case FsmTransition.CustomLinkTarget.LockRight:
              this.LinkLeftSideToRightSide();
              this.flipLeftArrow = true;
              break;
          }
          break;
        case FsmTransition.CustomLinkConstraint.LockRight:
          switch (this.linkTarget)
          {
            case FsmTransition.CustomLinkTarget.None:
              if ((double) this.toStateLeftX > (double) this.fromStateRightX)
              {
                this.LinkRightSideToLeftSide();
                break;
              }
              this.LinkRightSideToRightSide();
              this.flipLeftArrow = true;
              break;
            case FsmTransition.CustomLinkTarget.LockLeft:
              this.LinkRightSideToLeftSide();
              break;
            case FsmTransition.CustomLinkTarget.LockRight:
              this.LinkRightSideToRightSide();
              this.flipLeftArrow = true;
              break;
          }
          break;
      }
      this.fromPos *= this.drawScale;
      this.fromTangent *= this.drawScale;
      this.toPos *= this.drawScale;
      this.toTangent *= this.drawScale;
    }

    protected abstract void LinkRightSideToRightSide();

    protected abstract void LinkRightSideToLeftSide();

    protected abstract void LinkLeftSideToLeftSide();

    protected abstract void LinkLeftSideToRightSide();

    public abstract void Draw(
      FsmState fromState,
      FsmState toState,
      int transitionIndex,
      Color linkColor,
      float linkWidth,
      float scale);

    public abstract bool HitTest(Vector2 point, float hitDistance);

    public static void DrawPolyline(
      Color color,
      float width,
      float scale,
      params Vector3[] points)
    {
      Color color1 = Handles.color;
      Handles.color = color;
      Handles.DrawAAPolyLine((double) scale < 1.0 ? (Texture2D) null : FsmEditorStyles.LineTexture, width * 2f, points);
      Handles.color = color1;
    }

    public static void DrawPolylineCircuit(
      Color color,
      float width,
      float scale,
      params Vector3[] points)
    {
      foreach (Vector3 point in points)
        point.Set(Mathf.Floor(point.x) + 0.5f, Mathf.Floor(point.y) + 0.5f, 0.0f);
      Color color1 = Handles.color;
      Handles.color = color;
      Handles.DrawAAPolyLine((Texture2D) null, width * 1.5f, points);
      Handles.DrawAAPolyLine((Texture2D) null, width * 1.5f, points);
      Handles.color = color1;
    }

    [Obsolete("Use DrawPolyline(Color color, float width, float scale, params Vector3[] points)")]
    public static void DrawPolyline(Color color, float width, params Vector3[] points) => BaseLink.DrawPolyline(color, width, 1f, points);

    public void DrawArrowHead(Color color)
    {
      if (Event.current.type != UnityEngine.EventType.Repaint)
        return;
      GUIStyle guiStyle = FsmEditorStyles.LeftArrowHead;
      this.arrowRect.Set(this.toPos.x, (float) ((double) this.toPos.y - (double) this.arrowHeight * 0.5 + 1.0), this.arrowWidth, this.arrowHeight);
      if (!this.flipLeftArrow)
      {
        guiStyle = FsmEditorStyles.RightArrowHead;
        this.arrowRect.x -= this.arrowWidth;
        this.toPos.x -= this.arrowWidth;
      }
      else
        this.toPos.x += this.arrowWidth;
      --this.arrowRect.y;
      if (EditorApplication.isCompiling)
        color.a = 2f;
      guiStyle.Draw(this.arrowRect, color);
    }
  }
}
