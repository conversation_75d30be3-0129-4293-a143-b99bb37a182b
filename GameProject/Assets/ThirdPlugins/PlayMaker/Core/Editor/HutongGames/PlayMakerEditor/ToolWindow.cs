// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.ToolWindow
// Assembly: PlayMakerEditor, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class ToolWindow : BaseEditorWindow, IHasCustomMenu
  {
    private const string editorPrefsKey = "PlayMaker.ToolWindow.";
    private const string showButtonTooltipsKey = "PlayMaker.ToolWindow.ShowButtonTooltips";
    private static ToolWindow instance;
    private static FsmTransition editingTransition;
    private static bool showButtonTooltips;
    private static Vector2 scrollPosition;
    private static GUIStyle buttonStyle;
    private static readonly GUIContent buttonLabel = new GUIContent();
    private static FsmTransition selectedGlobalTransition;
    private const float buttonPanelPadding = 20f;

    public static void UpdateView()
    {
      if (!((UnityEngine.Object) ToolWindow.instance != (UnityEngine.Object) null))
        return;
      ToolWindow.instance.Repaint();
    }

    public override void Initialize()
    {
      if ((UnityEngine.Object) ToolWindow.instance == (UnityEngine.Object) null)
        ToolWindow.instance = this;
      this.isToolWindow = true;
      this.minSize = new Vector2(200f, 100f);
      ToolWindow.showButtonTooltips = EditorPrefs.GetBool("PlayMaker.ToolWindow.ShowButtonTooltips", true);
      ToolWindow.buttonStyle = (GUIStyle) "Button";
    }

    public void OnDisable()
    {
      if (!((UnityEngine.Object) ToolWindow.instance == (UnityEngine.Object) this))
        return;
      ToolWindow.instance = (ToolWindow) null;
    }

    void IHasCustomMenu.AddItemsToMenu(GenericMenu menu) => menu.AddItem(new GUIContent("Show button tooltips."), ToolWindow.showButtonTooltips, new GenericMenu.MenuFunction(ToolWindow.ToggleButtonTooltips));

    private static void ToggleButtonTooltips()
    {
      ToolWindow.showButtonTooltips = !ToolWindow.showButtonTooltips;
      EditorPrefs.SetBool("PlayMaker.ToolWindow.ShowButtonTooltips", ToolWindow.showButtonTooltips);
    }

    public override void InitWindowTitle() => this.SetTitle(Strings.ToolWindow_Title);

    public override void DoGUI()
    {
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box(Strings.Hint_Context_Tools, FsmEditorStyles.HintBox);
      if (FsmEditor.SelectedFsm == null)
      {
        if ((UnityEngine.Object) Selection.activeGameObject == (UnityEngine.Object) null)
          EditorGUILayout.HelpBox(Strings.Hint_Select_a_GameObject, MessageType.Info);
        else
          ToolWindow.EditGameObject();
      }
      else
      {
        ToolWindow.scrollPosition = GUILayout.BeginScrollView(ToolWindow.scrollPosition);
        if (FsmEditor.SelectedTransition != null)
          this.EditTransition();
        else if (FsmEditor.SelectedState != null)
          this.EditState();
        else if (FsmEditor.SelectedFsm != null)
          ToolWindow.EditFsm();
        GUILayout.EndScrollView();
      }
    }

    private static void EditGameObject()
    {
      ToolWindow.Header("Edit GameObject");
      ToolWindow.BeginButtonPanel();
      if (ToolWindow.Button(Strings.Menu_Add_FSM))
        FsmBuilder.AddFsmToSelected();
      EditorGUI.BeginDisabledGroup(!FsmEditor.Clipboard.CanPaste());
      if (ToolWindow.Button(Strings.Menu_Paste_FSM))
        EditorCommands.PasteFsm();
      EditorGUI.EndDisabledGroup();
      ToolWindow.Section("Templates");
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box("Templates are re-usable FSMs.\nPasted Templates are independent of the original.\nSelect Use Template if you want to reference the Template instead.", FsmEditorStyles.HintBox);
      if (ToolWindow.Button(Strings.Command_Paste_Template))
      {
        GenericMenu menu = new GenericMenu();
        Menus.AddTemplateItems(menu, "", new GenericMenu.MenuFunction2(EditorCommands.AddTemplateToSelected));
        menu.ShowAsContext();
      }
      if (ToolWindow.Button("Use Template"))
      {
        GenericMenu menu = new GenericMenu();
        Menus.AddTemplateItems(menu, "", new GenericMenu.MenuFunction2(EditorCommands.AddFsmAndUseTemplateWithSelected));
        menu.ShowAsContext();
      }
      ToolWindow.EndButtonPanel();
    }

    private static void EditFsm()
    {
      ToolWindow.Header("Edit FSM");
      ToolWindow.BeginButtonPanel();
      if (ToolWindow.Button(Strings.Command_Add_State, "Add a new empty state."))
        ToolWindow.AddState();
      EditorGUI.BeginDisabledGroup(!FsmEditor.Clipboard.CanPaste());
      if (ToolWindow.Button(Strings.Command_Paste_States, "Paste previously copied States."))
      {
        EditorCommands.PasteStates(FsmGraphView.GetViewCenter());
        FsmEditor.RepaintAll();
      }
      EditorGUI.EndDisabledGroup();
      if (ToolWindow.Button(Strings.Command_Paste_Template, "Paste a Template into this FSM.\nNOTE: Pasted states are independent of the original Template. Use Run FSM action if you want to reference the Template instead."))
      {
        GenericMenu menu = new GenericMenu();
        Menus.AddTemplateItems(menu, "", new GenericMenu.MenuFunction2(EditorCommands.PasteTemplate));
        menu.ShowAsContext();
      }
      if (ToolWindow.Button(Strings.Command_Add_Global_Transition, "Add a new Global Transition and target State."))
      {
        GenericMenu menu = new GenericMenu();
        foreach (FsmEvent fsmEvent in FsmEditor.SelectedFsm.Events)
          menu.AddItem(Menus.FormatItem(fsmEvent.Name), false, new GenericMenu.MenuFunction2(ToolWindow.AddGlobalTransition), (object) fsmEvent);
        FsmEvent fsmEvent1 = FsmEvent.GetFsmEvent("FINISHED");
        menu.AddItem(new GUIContent("FINISHED"), false, new GenericMenu.MenuFunction2(ToolWindow.AddGlobalTransition), (object) fsmEvent1);
        ActionEditor.AddCommonEventMenus(menu, "", (FsmEvent) null, new GenericMenu.MenuFunction2(ToolWindow.AddGlobalTransition)).ShowAsContext();
      }
      ToolWindow.Spacer();
      if (ToolWindow.Button(Strings.Menu_GraphView_Copy_FSM, "Copy the FSM to the clipboard."))
        EditorCommands.CopyFsm();
      if (ToolWindow.Button(Strings.Menu_GraphView_Save_Template, "Save the FSM as a Template that can be re-used."))
        EditorCommands.SaveFsmAsTemplate();
      ToolWindow.Spacer();
      if (ToolWindow.Button(Strings.Menu_GraphView_Set_Watermark, "Set the watermark used on the Graph View canvas."))
        EditorCommands.ChooseWatermark();
      if (ToolWindow.Button(Strings.Menu_GraphView_Save_Screenshot, "Save a screenshot of the FSM."))
        EditorCommands.SaveScreenshot();
      if ((UnityEngine.Object) FsmEditor.SelectedTemplate == (UnityEngine.Object) null)
      {
        ToolWindow.Spacer();
        if (ToolWindow.Button(Strings.Menu_Add_FSM, "Add another FSM component to this GameObject."))
          Menus.ShowAddFsmContextMenu();
        if (ToolWindow.Button("Delete FSM", "Remove this FSM component from the GameObject."))
          EditorCommands.RemoveFsmComponent();
      }
      else
      {
        ToolWindow.Section("Template");
        if (FsmEditorSettings.ShowHints)
          GUILayout.Box("The selected FSM is a Template that can be re-used.", FsmEditorStyles.HintBox);
        if (ToolWindow.Button(Strings.Menu_GraphView_Add_To_Selected, "Add this Template to selected GameObject"))
          EditorCommands.AddTemplateToSelected((object) FsmEditor.SelectedTemplate);
        if (ToolWindow.Button(Strings.Menu_GraphView_Delete_Template, "Delete this Template asset."))
          EditorCommands.DeleteTemplate();
      }
      ToolWindow.EndButtonPanel();
    }

    private static void AddState()
    {
      FsmEditor.GraphView.AddState(FsmGraphView.GetViewCenter());
      FsmEditor.RepaintAll();
    }

    private static void AddGlobalTransition(object userdata)
    {
      if (FsmEditor.SelectedState == null)
        ToolWindow.AddState();
      ToolWindow.selectedGlobalTransition = EditorCommands.AddGlobalTransitionToSelectedState();
      if (ToolWindow.selectedGlobalTransition == null)
        return;
      ToolWindow.SelectGlobalEvent(userdata);
    }

    private static void SelectGlobalEvent(object userdata)
    {
      FsmEvent fsmEvent = (FsmEvent) userdata;
      FsmEditor.SelectedFsm.AddEvent(fsmEvent);
      EditorCommands.SetTransitionEvent(ToolWindow.selectedGlobalTransition, fsmEvent);
      FsmEditor.EventsManager.Reset();
      FsmEditor.EventsManager.SelectEvent(fsmEvent);
      ToolWindow.selectedGlobalTransition = (FsmTransition) null;
    }

    private void EditState()
    {
      ToolWindow.Header("Edit State");
      ToolWindow.BeginButtonPanel();
      if (ToolWindow.Button(Strings.Command_Set_As_Start_State))
      {
        EditorCommands.SetSelectedStateAsStartState();
        FsmEditor.RepaintAll();
      }
      if (ToolWindow.Button(Strings.Command_Toggle_Breakpoint))
      {
        EditorCommands.ToggleBreakpointOnSelectedState();
        FsmEditor.RepaintAll();
      }
      if (ToolWindow.Button("Save As Template"))
        EditorCommands.SaveSelectionAsTemplate();
      ToolWindow.Spacer();
      if (ToolWindow.Button(Strings.Command_Copy))
        EditorCommands.CopyStateSelection();
      EditorGUI.BeginDisabledGroup(!FsmEditor.Clipboard.CanPaste());
      if (ToolWindow.Button(Strings.Command_Paste))
      {
        EditorCommands.PasteStates(FsmGraphView.GetViewCenter());
        FsmEditor.RepaintAll();
      }
      EditorGUI.EndDisabledGroup();
      if (ToolWindow.Button(Strings.Command_Delete))
      {
        FsmEditor.RepaintAll();
        EditorCommands.DeleteMultiSelection();
      }
      ToolWindow.Section("Edit Transitions");
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box("Transitions define events that trigger transitions to and from this state.", FsmEditorStyles.HintBox);
      if (ToolWindow.Button(Strings.Command_Add_Transition))
      {
        EditorCommands.AddTransitionToSelectedState();
        FsmEditor.RepaintAll();
      }
      if (ToolWindow.Button(Strings.Command_Add_Global_Transition))
      {
        EditorCommands.AddGlobalTransitionToSelectedState();
        FsmEditor.RepaintAll();
      }
      ToolWindow.EndButtonPanel();
      if (!FsmEditor.SelectedFsm.IsGlobalTransitionToState(FsmEditor.SelectedState))
        return;
      ToolWindow.Section("Global Transitions");
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box("Global transitions define events that always trigger a transition to this state.", FsmEditorStyles.HintBox);
      FsmState selectedState = FsmEditor.SelectedState;
      foreach (FsmTransition globalTransition in FsmEditor.SelectedFsm.GlobalTransitions)
      {
        if (!(globalTransition.ToState != selectedState.Name))
        {
          GUILayout.BeginHorizontal();
          Rect rect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.popup);
          if (GUI.Button(rect, Labels.GetEventLabel(globalTransition), EditorStyles.popup))
            Menus.ShowTransitionEventSelectionMenu((EditorWindow) this, FsmEditor.SelectedFsm, globalTransition, rect);
          if (FsmEditorGUILayout.DeleteButton())
          {
            EditorCommands.DeleteGlobalTransition(globalTransition);
            FsmEditor.RepaintAll();
          }
          GUILayout.EndHorizontal();
        }
      }
    }

    private void EditTransition()
    {
      ToolWindow.Header("Edit Transition");
      float labelWidth = EditorGUIUtility.labelWidth;
      EditorGUIUtility.labelWidth = 65f;
      FsmState selectedState = FsmEditor.SelectedState;
      FsmTransition selectedTransition = FsmEditor.SelectedTransition;
      GUILayout.BeginHorizontal();
      GUILayout.Label(Strings.Label_Event, GUILayout.MaxWidth(65f));
      Rect rect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.popup);
      if (GUI.Button(rect, Labels.GetEventLabel(selectedTransition), EditorStyles.popup))
        Menus.ShowTransitionEventSelectionMenu((EditorWindow) this, FsmEditor.SelectedFsm, selectedTransition, rect);
      GUILayout.EndHorizontal();
      GUILayout.BeginHorizontal();
      GUILayout.Label("To State", GUILayout.MaxWidth(65f));
      if (GUILayout.Button(Labels.GetStateLabel(selectedTransition.ToState), EditorStyles.popup))
      {
        ToolWindow.editingTransition = selectedTransition;
        FsmEditorGUILayout.GenerateStateSelectionMenu(FsmEditor.SelectedFsm, selectedTransition.ToState, new GenericMenu.MenuFunction2(ToolWindow.SelectToState)).ShowAsContext();
      }
      GUILayout.EndHorizontal();
      ToolWindow.Spacer();
      ToolWindow.Spacer();
      ToolWindow.BeginButtonPanel();
      EditorGUIUtility.labelWidth = 80f;
      EditorGUILayout.BeginHorizontal();
      EditorGUILayout.PrefixLabel("Link Color");
      GUIHelpers.BeginGuiColor(PlayMakerPrefs.Colors[selectedTransition.ColorIndex]);
      if (GUILayout.Button(GUIContent.none, FsmEditorStyles.ColorSwatch, GUILayout.ExpandWidth(true), GUILayout.Height(16f)))
        Menus.AddColorMenu(new GenericMenu(), "", selectedState.ColorIndex, new GenericMenu.MenuFunction2(EditorCommands.SetTransitionColorIndex)).ShowAsContext();
      GUIHelpers.EndGuiColor();
      EditorGUILayout.EndHorizontal();
      FsmTransition.CustomLinkStyle customLinkStyle = (FsmTransition.CustomLinkStyle) EditorGUILayout.EnumPopup("Link Style", (Enum) selectedTransition.LinkStyle);
      if (customLinkStyle != selectedTransition.LinkStyle)
        EditorCommands.SetTransitionLinkStyle((object) customLinkStyle);
      FsmTransition.CustomLinkConstraint customLinkConstraint = (FsmTransition.CustomLinkConstraint) EditorGUILayout.EnumPopup("Link Start", (Enum) (ToolWindow.LockLink) selectedTransition.LinkConstraint);
      if (customLinkConstraint != selectedTransition.LinkConstraint)
        EditorCommands.SetTransitionLinkConstraint((object) customLinkConstraint);
      FsmTransition.CustomLinkTarget customLinkTarget = (FsmTransition.CustomLinkTarget) EditorGUILayout.EnumPopup("Link End", (Enum) (ToolWindow.LockLink) selectedTransition.LinkTarget);
      if (customLinkTarget != selectedTransition.LinkTarget)
        EditorCommands.SetTransitionLinkTarget((object) customLinkTarget);
      ToolWindow.Spacer();
      ToolWindow.Spacer();
      if (ToolWindow.Button("Move Up"))
        EditorCommands.MoveTransitionUp((object) selectedTransition);
      if (ToolWindow.Button("Move Down"))
        EditorCommands.MoveTransitionDown((object) selectedTransition);
      ToolWindow.Spacer();
      if (ToolWindow.Button("Delete Transition"))
      {
        EditorCommands.DeleteTransition(selectedState, selectedTransition);
        FsmEditor.Selection.SelectTransition((FsmTransition) null);
        FsmEditor.RepaintAll();
      }
      ToolWindow.EndButtonPanel();
      EditorGUIUtility.labelWidth = labelWidth;
    }

    private static void SelectGlobalTransitionEvent(object userdata)
    {
      FsmEvent fsmEvent = userdata as FsmEvent;
      EditorCommands.SetTransitionEvent(ToolWindow.editingTransition, fsmEvent);
      FsmEditor.RepaintAll();
    }

    private static void SelectEvent(object userdata)
    {
      FsmEvent fsmEvent = userdata as FsmEvent;
      EditorCommands.SetTransitionEvent(ToolWindow.editingTransition, fsmEvent);
      FsmEditor.RepaintAll();
    }

    private static void SelectToState(object userdata)
    {
      string toState = userdata as string;
      EditorCommands.SetTransitionTarget(ToolWindow.editingTransition, toState);
      FsmEditor.RepaintAll();
    }

    private static void Header(string label)
    {
      ToolWindow.Spacer();
      GUILayout.Label(label, FsmEditorStyles.CenteredLabel);
      ToolWindow.Spacer();
    }

    private static void Section(string label)
    {
      ToolWindow.Spacer();
      GUILayout.Label(label, FsmEditorStyles.CenteredLabel);
      ToolWindow.Spacer();
    }

    private static void BeginButtonPanel()
    {
      GUILayout.BeginHorizontal();
      GUILayout.Space(20f);
      GUILayout.BeginVertical();
    }

    private static void EndButtonPanel()
    {
      GUILayout.EndVertical();
      GUILayout.Space(20f);
      GUILayout.EndHorizontal();
    }

    private static bool Button(GUIContent label) => !ToolWindow.showButtonTooltips ? GUILayout.Button(label.text, ToolWindow.buttonStyle) : GUILayout.Button(label, ToolWindow.buttonStyle);

    private static bool Button(string label) => GUILayout.Button(label, ToolWindow.buttonStyle);

    private static bool Button(string label, string tooltip)
    {
      ToolWindow.buttonLabel.text = label;
      ToolWindow.buttonLabel.tooltip = tooltip;
      return GUILayout.Button(ToolWindow.buttonLabel, ToolWindow.buttonStyle);
    }

    private static bool Button(string label, GUIStyle style) => GUILayout.Button(label, style);

    private static bool PopupButton(string label, string value, out Rect buttonRect)
    {
      GUILayout.BeginHorizontal();
      GUILayout.Label(label, GUILayout.MaxWidth(40f));
      buttonRect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.popup);
      int num = GUI.Button(buttonRect, value, EditorStyles.popup) ? 1 : 0;
      GUILayout.EndHorizontal();
      return num != 0;
    }

    private static void Spacer() => GUILayout.Space(10f);

    private enum LockLink : byte
    {
      Auto,
      LockToLeft,
      LockToRight,
    }
  }
}
