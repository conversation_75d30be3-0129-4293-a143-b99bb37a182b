// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.ArrayUtility
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Extensions;
using System.Collections.Generic;

namespace HutongGames.PlayMakerEditor
{
  internal static class ArrayUtility
  {
    public static T[] Resize<T>(T[] array, int newSize)
    {
      List<T> list = new List<T>((IEnumerable<T>) array);
      list.Resize<T>(newSize);
      return list.ToArray();
    }

    public static T[] Copy<T>(T[] array) => new List<T>((IEnumerable<T>) array).ToArray();

    public static T[] Add<T>(T[] array, T item) => new List<T>((IEnumerable<T>) array)
    {
      item
    }.ToArray();

    public static T[] AddRange<T>(T[] array, T[] items)
    {
      List<T> objList = new List<T>((IEnumerable<T>) array);
      objList.AddRange((IEnumerable<T>) items);
      return objList.ToArray();
    }

    public static T[] AddIfNotExists<T>(T[] array, T item)
    {
      List<T> objList = new List<T>((IEnumerable<T>) array);
      if (objList.Contains(item))
        return array;
      objList.Add(item);
      return objList.ToArray();
    }

    public static T[] AddAndSort<T>(T[] array, T item)
    {
      List<T> objList = new List<T>((IEnumerable<T>) array);
      objList.Add(item);
      objList.Sort();
      return objList.ToArray();
    }

    public static T[] Sort<T>(T[] array)
    {
      List<T> objList = new List<T>((IEnumerable<T>) array);
      objList.Sort();
      return objList.ToArray();
    }

    public static T[] Insert<T>(T[] array, int index, T item)
    {
      List<T> objList = new List<T>((IEnumerable<T>) array);
      objList.Insert(index, item);
      return objList.ToArray();
    }

    public static T[] RemoveAt<T>(T[] array, int index)
    {
      List<T> objList = new List<T>((IEnumerable<T>) array);
      objList.RemoveAt(index);
      return objList.ToArray();
    }

    public static T[] Remove<T>(T[] array, T item)
    {
      List<T> objList = new List<T>((IEnumerable<T>) array);
      objList.Remove(item);
      return objList.ToArray();
    }

    public static T[] MoveItem<T>(T[] array, int oldIndex, int newIndex)
    {
      List<T> objList = new List<T>((IEnumerable<T>) array);
      T obj = objList[oldIndex];
      objList.RemoveAt(oldIndex);
      objList.Insert(newIndex, obj);
      return objList.ToArray();
    }

    public static string GetDebugString<T>(T[] array)
    {
      string str = "";
      foreach (T obj in array)
        str = str + (object) obj + ",";
      return str;
    }
  }
}
