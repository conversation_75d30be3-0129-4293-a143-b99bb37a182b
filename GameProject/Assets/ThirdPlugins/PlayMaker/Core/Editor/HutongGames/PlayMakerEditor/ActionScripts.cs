// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.ActionScripts
// Assembly: PlayMakerEditor, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class ActionScripts
  {
    private static Dictionary<System.Type, UnityEngine.Object> actionScriptLookup;

    public static Dictionary<System.Type, UnityEngine.Object> ActionScriptLookup
    {
      get
      {
        if (ActionScripts.actionScriptLookup == null)
          ActionScripts.Init();
        return ActionScripts.actionScriptLookup;
      }
    }

    private static void Init()
    {
      ActionScripts.actionScriptLookup = new Dictionary<System.Type, UnityEngine.Object>();
      List<System.Type> typeList = new List<System.Type>((IEnumerable<System.Type>) Actions.List);
      MonoScript[] objectsOfTypeAll = (MonoScript[]) Resources.FindObjectsOfTypeAll(typeof (MonoScript));
      foreach (MonoScript monoScript in objectsOfTypeAll)
      {
        System.Type key = monoScript.GetClass();
        if (key != null && key.IsSubclassOf(typeof (FsmStateAction)))
        {
          ActionScripts.actionScriptLookup.Add(key, (UnityEngine.Object) monoScript);
          typeList.Remove(key);
        }
      }
      for (int index = typeList.Count - 1; index >= 0; --index)
      {
        System.Type key = typeList[index];
        foreach (MonoScript monoScript in objectsOfTypeAll)
        {
          if (monoScript.text.Contains("public class " + key.Name))
          {
            ActionScripts.actionScriptLookup.Add(key, (UnityEngine.Object) monoScript);
            typeList.RemoveAt(index);
            break;
          }
        }
      }
    }

    public static string GetScriptPath(System.Type actionType)
    {
      UnityEngine.Object asset = ActionScripts.GetAsset(actionType);
      return !(asset != (UnityEngine.Object) null) ? "" : AssetDatabase.GetAssetPath(asset);
    }

    public static void PingAsset(object userdata)
    {
      FsmStateAction action = (FsmStateAction) userdata;
      UnityEngine.Object asset = ActionScripts.GetAsset(action);
      if (asset != (UnityEngine.Object) null)
        EditorGUIUtility.PingObject(asset);
      else
        Debug.LogError((object) string.Format(Strings.Error_Missing_Script, (object) action.Name));
    }

    public static void Edit(System.Type actionType)
    {
      UnityEngine.Object asset = ActionScripts.GetAsset(actionType);
      if (asset != (UnityEngine.Object) null)
        AssetDatabase.OpenAsset(asset);
      else
        Debug.LogError((object) string.Format(Strings.Error_Missing_Script, (object) Labels.GetActionLabel(actionType)));
    }

    public static void EditAsset(object userdata)
    {
      FsmStateAction action = (FsmStateAction) userdata;
      UnityEngine.Object asset = ActionScripts.GetAsset(action);
      if (asset != (UnityEngine.Object) null)
        AssetDatabase.OpenAsset(asset);
      else
        Debug.LogError((object) string.Format(Strings.Error_Missing_Script, (object) action.Name));
    }

    public static void PingAssetByType(object userdata)
    {
      System.Type actionType = (System.Type) userdata;
      UnityEngine.Object asset = ActionScripts.GetAsset(actionType);
      if (asset != (UnityEngine.Object) null)
        EditorGUIUtility.PingObject(asset);
      else
        Debug.LogError((object) string.Format(Strings.Error_Missing_Script, (object) actionType));
    }

    public static void SelectUsedActions()
    {
      List<System.Type> usedActions = Actions.GetUsedActions();
      List<UnityEngine.Object> objectList = new List<UnityEngine.Object>();
      foreach (System.Type actionType in usedActions)
      {
        UnityEngine.Object asset = ActionScripts.GetAsset(actionType);
        if (asset != (UnityEngine.Object) null)
          objectList.Add(asset);
        else
          Debug.LogWarning((object) string.Format("Could not find action type: {0}", (object) actionType.FullName));
      }
      Selection.objects = objectList.ToArray();
    }

    public static void SelectFilteredActions()
    {
      List<System.Type> filteredActions = Actions.GetFilteredActions();
      List<UnityEngine.Object> objectList = new List<UnityEngine.Object>();
      foreach (System.Type actionType in filteredActions)
      {
        UnityEngine.Object asset = ActionScripts.GetAsset(actionType);
        if (asset != (UnityEngine.Object) null)
          objectList.Add(asset);
        else
          Debug.LogWarning((object) string.Format("Could not find action type: {0}", (object) actionType.FullName));
      }
      Selection.objects = objectList.ToArray();
    }

    public static void SelectAssetByType(object userdata)
    {
      System.Type actionType = (System.Type) userdata;
      UnityEngine.Object asset = ActionScripts.GetAsset(actionType);
      if (asset != (UnityEngine.Object) null)
      {
        EditorGUIUtility.PingObject(asset);
        Selection.activeObject = asset;
      }
      else
        Debug.LogError((object) string.Format(Strings.Error_Missing_Script, (object) actionType));
    }

    public static void EditAssetByType(object userdata)
    {
      System.Type actionType = (System.Type) userdata;
      UnityEngine.Object asset = ActionScripts.GetAsset(actionType);
      if (asset != (UnityEngine.Object) null)
        AssetDatabase.OpenAsset(asset);
      else
        Debug.LogError((object) string.Format(Strings.Error_Missing_Script, (object) actionType));
    }

    public static UnityEngine.Object GetAsset(FsmStateAction action) => action != null ? ActionScripts.GetAsset(action.GetType()) : (UnityEngine.Object) null;

    public static UnityEngine.Object GetAsset(System.Type actionType)
    {
      if (ActionScripts.ActionScriptLookup.Count == 0)
      {
        Actions.BuildList();
        ActionScripts.Init();
      }
      UnityEngine.Object @object;
      ActionScripts.ActionScriptLookup.TryGetValue(actionType, out @object);
      return @object;
    }
  }
}
