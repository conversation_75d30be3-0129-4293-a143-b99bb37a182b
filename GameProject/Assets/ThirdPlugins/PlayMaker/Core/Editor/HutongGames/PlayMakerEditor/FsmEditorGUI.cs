// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmEditorGUI
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class FsmEditorGUI
  {
    public static bool ButtonNoGuiChanged(Rect rect, GUIContent label, GUIStyle guiStyle)
    {
      bool changed = GUI.changed;
      int num = GUI.Button(rect, label, guiStyle) ? 1 : 0;
      GUI.changed = changed;
      return num != 0;
    }

    public static void ReadonlyTextField(Rect rect, string value)
    {
      if (Event.current.type != UnityEngine.EventType.Repaint)
        return;
      GUIHelpers.BeginGuiBackgroundColor(Color.white);
      EditorGUI.BeginDisabledGroup(true);
      EditorStyles.textField.Draw(rect, value);
      EditorGUI.EndDisabledGroup();
      GUIHelpers.EndGuiBackgroundColor();
    }

    public static void ReadonlyObjectField(Rect rect, UnityEngine.Object value, System.Type objectType)
    {
      GUIContent content = EditorGUIUtility.ObjectContent(value, objectType);
      GUIHelpers.BeginGuiColorFaded();
      if (GUI.Button(rect, content, (bool) value ? FsmEditorStyles.ReadonlyObjectField : FsmEditorStyles.ReadonlyObjectFieldNone) && (bool) value)
        EditorGUIUtility.PingObject(value);
      GUIHelpers.EndGuiColor();
    }

    public static bool Foldout(Rect rect, bool open)
    {
      int num = GUI.enabled ? 1 : 0;
      GUI.enabled = true;
      bool changed = GUI.changed;
      open = EditorGUI.Toggle(rect, open, EditorStyles.foldout);
      if (Application.isPlaying)
        GUI.changed = changed;
      GUI.enabled = num != 0;
      return open;
    }

    public static bool HelpButton(Rect rect, string tooltip = "Online Help")
    {
      FsmEditorContent.HelpButton.tooltip = !DragAndDropManager.IsDragging ? tooltip : (string) null;
      int num = GUI.Button(rect, FsmEditorContent.HelpButton, GUIStyle.none) ? 1 : 0;
      HighlighterHelper.FromRect(rect, "Online Help");
      return num != 0;
    }

    public static bool DeleteButton(Rect rect)
    {
      FsmEditorContent.DeleteButton.tooltip = !DragAndDropManager.IsDragging ? Strings.Command_Delete : (string) null;
      if (!GUIHelpers.IsHidpi)
        ++rect.y;
      rect.height = EditorStyles.popup.fixedHeight;
      rect.width = rect.height + (!GUIHelpers.IsHidpi ? 2f : 0.0f);
      --rect.height;
      return GUI.Button(rect, FsmEditorContent.DeleteButton, FsmEditorStyles.MiniButton);
    }
  }
}
