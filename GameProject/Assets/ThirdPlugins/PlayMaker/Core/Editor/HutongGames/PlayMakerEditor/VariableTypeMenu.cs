// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.VariableTypeMenu
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class VariableTypeMenu
  {
    private static string currentDefinitionPath;
    private static readonly List<VariableTypeDefinition> DefaultVariableTypesDefinition = new List<VariableTypeDefinition>()
    {
      new VariableTypeDefinition("Float"),
      new VariableTypeDefinition("Int"),
      new VariableTypeDefinition("Bool"),
      new VariableTypeDefinition("GameObject"),
      new VariableTypeDefinition("String"),
      new VariableTypeDefinition("Vector2"),
      new VariableTypeDefinition("Vector3"),
      new VariableTypeDefinition("Color"),
      new VariableTypeDefinition("Rect"),
      new VariableTypeDefinition("Material"),
      new VariableTypeDefinition("Texture"),
      new VariableTypeDefinition("Sprite", "UnityEngine.Sprite"),
      new VariableTypeDefinition("AudioClip", "UnityEngine.AudioClip"),
      new VariableTypeDefinition("Quaternion"),
      new VariableTypeDefinition("Object"),
      new VariableTypeDefinition("Array"),
      new VariableTypeDefinition("Enum")
    };
    private readonly List<VariableTypeMenu.MenuItem> menuItems = new List<VariableTypeMenu.MenuItem>();
    private static readonly Dictionary<System.Type, string> typeAliases = new Dictionary<System.Type, string>();

    public VariableTypeMenu() => this.LoadMenuTemplate();

    public static GUIContent GetTypeLabel(FsmVariable fsmVariable)
    {
      GUIContent guiContent = new GUIContent();
      string name1 = "";
      if (fsmVariable.Type == VariableType.Object)
      {
        string name2 = "Object";
        if (fsmVariable.ObjectType != null && !VariableTypeMenu.typeAliases.TryGetValue(fsmVariable.ObjectType, out name2))
          name2 = fsmVariable.ObjectType.ToString();
        guiContent.text = Labels.StripNamespace(name2);
        guiContent.tooltip = "Object: " + name2;
      }
      else if (fsmVariable.Type == VariableType.Enum)
      {
        string name2 = "Enum";
        if (fsmVariable.ObjectType != null && !VariableTypeMenu.typeAliases.TryGetValue(fsmVariable.ObjectType, out name2))
          name2 = fsmVariable.ObjectType.ToString();
        guiContent.text = Labels.StripNamespace(name2);
        guiContent.tooltip = "Enum: " + name2;
      }
      else if (fsmVariable.Type == VariableType.Array)
      {
        if (string.IsNullOrEmpty(fsmVariable.Name))
        {
          guiContent.text = "Array";
          return guiContent;
        }
        if (fsmVariable.ElementType == VariableType.Object || fsmVariable.ElementType == VariableType.Enum)
        {
          FsmArray namedVar = (FsmArray) fsmVariable.NamedVar;
          if (!VariableTypeMenu.typeAliases.TryGetValue(namedVar.ObjectType, out name1))
            name1 = namedVar.ObjectType.ToString();
        }
        else
          name1 = fsmVariable.ElementType.ToString();
        guiContent.text = "Array: " + Labels.StripNamespace(name1);
        guiContent.tooltip = "Array: " + name1;
      }
      else
        guiContent.text = fsmVariable.Type.ToString();
      return guiContent;
    }

    public GUIContent GetArrayElementLabel(FsmArray fsmArray)
    {
      GUIContent guiContent = new GUIContent();
      if (fsmArray.ElementType == VariableType.Object || fsmArray.ElementType == VariableType.Enum)
      {
        string name = fsmArray.ObjectType == null ? fsmArray.ElementType.ToString() : fsmArray.ObjectType.ToString();
        guiContent.text = Labels.StripNamespace(name);
        guiContent.tooltip = name;
      }
      else
        guiContent.text = fsmArray.ElementType.ToString();
      return guiContent;
    }

    [Obsolete("Use GetTypeButtonLabel instead")]
    public string GetButtonLabel(FsmVariable fsmVariable) => "Obsolete!";

    public GenericMenu GetTypeMenu(
      FsmVariable fsmVariable,
      GenericMenu.MenuFunction2 func)
    {
      this.LoadMenuTemplate();
      GenericMenu genericMenu = new GenericMenu();
      foreach (VariableTypeMenu.MenuItem menuItem in this.menuItems)
      {
        bool on = fsmVariable != null && fsmVariable.Type == menuItem.data.variableType && fsmVariable.ObjectType == menuItem.data.objectType;
        genericMenu.AddItem(menuItem.content, on, func, (object) menuItem.data);
      }
      return genericMenu;
    }

    public void Show(FsmVariable fsmVariable, GenericMenu.MenuFunction2 func) => this.GetTypeMenu(fsmVariable, func).ShowAsContext();

    public void DropDown(Rect rect, FsmVariable fsmVariable, GenericMenu.MenuFunction2 func) => this.GetTypeMenu(fsmVariable, func).DropDown(rect);

    public void ShowArrayTypeMenu(FsmVariable fsmVariable, GenericMenu.MenuFunction2 func)
    {
      this.LoadMenuTemplate();
      GenericMenu genericMenu = new GenericMenu();
      foreach (VariableTypeMenu.MenuItem menuItem in this.menuItems)
      {
        if (menuItem.data.variableType != VariableType.Array)
          genericMenu.AddItem(menuItem.content, false, func, (object) menuItem.data);
      }
      genericMenu.ShowAsContext();
    }

    public static void ShowEnumTypesMenu(FsmVariable fsmVariable, GenericMenu.MenuFunction2 func) => VariableTypeMenu.ShowTypesMenu(VariableType.Enum, TypeHelpers.EnumTypeList, fsmVariable, func);

    public static void ShowObjectTypesMenu(FsmVariable fsmVariable, GenericMenu.MenuFunction2 func) => VariableTypeMenu.ShowTypesMenu(VariableType.Object, TypeHelpers.ObjectTypeList, fsmVariable, func);

    public static void ShowTypesMenu(
      VariableType variableType,
      List<System.Type> types,
      FsmVariable fsmVariable,
      GenericMenu.MenuFunction2 func)
    {
      GenericMenu genericMenu = new GenericMenu();
      foreach (System.Type type in types)
      {
        string fullName = type.FullName;
        if (!string.IsNullOrEmpty(fullName))
        {
          string text = fullName.Replace('.', '/');
          VariableTypeMenu.MenuItemSelectionData itemSelectionData = new VariableTypeMenu.MenuItemSelectionData()
          {
            variableType = variableType,
            objectType = type
          };
          genericMenu.AddItem(new GUIContent(text), fullName == fsmVariable.TypeName, func, (object) itemSelectionData);
        }
      }
      genericMenu.ShowAsContext();
    }

    public void SelectVariableType(FsmVariable fsmVariable) => this.Show(fsmVariable, (GenericMenu.MenuFunction2) (userdata =>
    {
      VariableTypeMenu.MenuItemSelectionData itemSelectionData = (VariableTypeMenu.MenuItemSelectionData) userdata;
      EditorCommands.ChangeVariableType(fsmVariable, itemSelectionData.variableType, itemSelectionData.objectType);
    }));

    public void SelectArrayType(FsmVariable fsmVariable) => this.ShowArrayTypeMenu(fsmVariable, (GenericMenu.MenuFunction2) (userdata =>
    {
      VariableTypeMenu.MenuItemSelectionData itemSelectionData = (VariableTypeMenu.MenuItemSelectionData) userdata;
      EditorCommands.ChangeArrayType(fsmVariable, itemSelectionData.variableType, itemSelectionData.objectType);
    }));

    public static string GetTypeAlias(System.Type type)
    {
      string str;
      VariableTypeMenu.typeAliases.TryGetValue(type, out str);
      return str;
    }

    public static bool IsTypeAliased(System.Type type) => VariableTypeMenu.typeAliases.ContainsKey(type);

    private void LoadMenuTemplate()
    {
      this.menuItems.Clear();
      VariableTypeMenu.typeAliases.Clear();
      this.ParseList(VariableTypeMenu.DefaultVariableTypesDefinition);
      this.ParseVariableTypesDefinitions();
    }

    private void ParseVariableTypesDefinitions()
    {
      foreach (KeyValuePair<string, List<VariableTypeDefinition>> variableTypesDefinition in VariableTypeMenuEditor.GetAllVariableTypesDefinitions())
      {
        VariableTypeMenu.currentDefinitionPath = variableTypesDefinition.Key;
        this.ParseList(variableTypesDefinition.Value);
      }
      VariableTypeMenu.currentDefinitionPath = (string) null;
    }

    private void ParseList(List<VariableTypeDefinition> variableTypes)
    {
      foreach (VariableTypeDefinition variableType in variableTypes)
        this.ParseDefinition(variableType.Name, variableType.Type);
    }

    private void ParseDefinition(string name, string typeName)
    {
      if (string.IsNullOrEmpty(name))
      {
        VariableTypeMenu.ParseError("", "Variable Name is null or empty");
      }
      else
      {
        string str = ((IEnumerable<string>) name.Split('/')).Last<string>();
        if (string.IsNullOrEmpty(typeName))
        {
          try
          {
            VariableType variableType = (VariableType) Enum.Parse(typeof (VariableType), str);
            this.menuItems.Add(new VariableTypeMenu.MenuItem(name, variableType));
          }
          catch (Exception ex)
          {
            VariableTypeMenu.ParseError(name + ":" + typeName, ex.Message);
          }
        }
        else
        {
          System.Type globalType = ReflectionUtils.GetGlobalType(typeName);
          if (globalType == null)
            VariableTypeMenu.ParseError(name + ":" + typeName, "Could not parse object type");
          else if (globalType.IsSubclassOf(typeof (UnityEngine.Object)))
          {
            this.menuItems.Add(new VariableTypeMenu.MenuItem(name, VariableType.Object, globalType));
            VariableTypeMenu.typeAliases.Add(globalType, str);
          }
          else if (globalType.IsEnum)
          {
            this.menuItems.Add(new VariableTypeMenu.MenuItem(name, VariableType.Enum, globalType));
            VariableTypeMenu.typeAliases.Add(globalType, str);
          }
          else
            VariableTypeMenu.ParseError(name + ":" + typeName, "Invalid object type (must be UnityEngine.Object or Enum)");
        }
      }
    }

    private static void ParseError(string line, string error = "Error Parsing") => Debug.LogError((object) ("Custom Variable Type Definition Error: " + error + ": " + line + " (" + VariableTypeMenu.currentDefinitionPath + ")"));

    public struct MenuItemSelectionData
    {
      public VariableType variableType;
      public System.Type objectType;
    }

    private struct MenuItem
    {
      public readonly GUIContent content;
      public readonly VariableTypeMenu.MenuItemSelectionData data;

      public MenuItem(string content, VariableType variableType, System.Type objectType = null)
      {
        this.content = new GUIContent(content);
        this.data = new VariableTypeMenu.MenuItemSelectionData()
        {
          variableType = variableType,
          objectType = objectType
        };
      }
    }
  }
}
