// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.TimelineControl
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class TimelineControl
  {
    public TimelineControl.TimelineClickedHandler TimelineClicked;
    private float length;
    private float offset;
    private Rect area;
    private float timeScale = 200f;
    private const float minLabelSpacing = 50f;
    private float majorTickUnits;
    private float minorTickUnits;
    private float toolbarHeight;
    private Rect toolbarRect;
    private Rect graphRect;
    private Rect tickLabelRect = new Rect(0.0f, 0.0f, 100f, 20f);
    private Rect debugLineRect;
    private Color labelColor;
    private readonly float[] tickUnits = new float[13]
    {
      0.01f,
      0.05f,
      0.1f,
      0.5f,
      1f,
      5f,
      10f,
      30f,
      60f,
      300f,
      600f,
      1800f,
      3600f
    };
    private readonly LineDrawer line = new LineDrawer();

    public bool NeedsRepaint { get; private set; }

    public float Length
    {
      get => this.length;
      set => this.length = Mathf.Clamp(value, 0.0f, float.PositiveInfinity);
    }

    public float CanvasWidth => Mathf.Max(this.length * this.timeScale, this.offset * this.timeScale + this.area.width);

    public float CanvasOffset
    {
      get => this.offset * this.timeScale;
      set => this.offset = value / this.timeScale;
    }

    public float VisibleRangeStart => this.Offset;

    public float VisibleRangeEnd => this.Offset + this.area.width / this.timeScale;

    public float VisibleTimeSpan => this.area.width / this.timeScale;

    public float Offset
    {
      get => this.offset;
      set => this.offset = Mathf.Clamp(value, 0.0f, this.Length);
    }

    public float TimeScale
    {
      get => this.timeScale;
      set => this.timeScale = Mathf.Clamp(value, 1f, 100000f);
    }

    public float TimeToScreenPosition(float time) => (time - this.offset) * this.timeScale;

    public float ScreenPositionToTime(float screenPos) => (screenPos + this.offset * this.timeScale) / this.timeScale;

    public void Move(float deltaX)
    {
      this.Offset += deltaX / this.timeScale;
      this.NeedsRepaint = true;
    }

    public void SetLength(float time)
    {
      if ((double) this.length <= (double) this.VisibleRangeEnd && (double) time > (double) this.VisibleRangeEnd - 20.0 / (double) this.timeScale)
      {
        this.Offset = (float) ((double) time - (double) this.area.width / (double) this.timeScale + (double) this.VisibleTimeSpan * 0.5);
        this.NeedsRepaint = true;
      }
      this.length = time;
    }

    public void Zoom(float center, float delta)
    {
      float time = this.ScreenPositionToTime(center);
      this.TimeScale += delta / this.majorTickUnits;
      this.Offset = time - center / this.timeScale;
      this.NeedsRepaint = true;
    }

    public void FrameTime(float time, float padding = 0.0f)
    {
      if ((double) time < (double) this.VisibleRangeStart)
        this.Offset = time - padding / this.timeScale;
      if ((double) time <= (double) this.VisibleRangeEnd)
        return;
      this.Offset = time + padding / this.timeScale - this.VisibleTimeSpan;
    }

    public void OnGUI(Rect area)
    {
      this.area = area;
      Event current = Event.current;
      UnityEngine.EventType type = Event.current.type;
      this.toolbarHeight = EditorStyles.toolbar.fixedHeight;
      this.UpdateTicks();
      this.labelColor = EditorStyles.label.normal.textColor;
      this.line.SetColor(this.labelColor);
      this.toolbarRect.Set(area.x, area.y, area.width, this.toolbarHeight);
      GUI.Box(this.toolbarRect, GUIContent.none, EditorStyles.toolbar);
      GUI.BeginGroup(area);
      GUIHelpers.BeginGuiColor(Color.white);
      if (type == UnityEngine.EventType.Repaint)
      {
        this.graphRect.Set(0.0f, this.toolbarHeight, area.width, area.height - this.toolbarHeight);
        FsmEditorStyles.Background.Draw(this.graphRect, false, false, false, false);
        this.DrawTicks(area);
      }
      if (type == UnityEngine.EventType.ScrollWheel && (double) current.mousePosition.x > 0.0)
      {
        this.Zoom(current.mousePosition.x, -current.delta.y);
        current.Use();
      }
      if (type == UnityEngine.EventType.MouseDrag && (current.button == 2 || Keyboard.AltAction()))
      {
        this.Move(-current.delta.x);
        current.Use();
      }
      GUIHelpers.EndGuiColor();
      GUI.EndGroup();
      if (!area.Contains(current.mousePosition) || type != UnityEngine.EventType.MouseDown || ((double) current.mousePosition.y >= (double) this.toolbarHeight || this.TimelineClicked == null))
        return;
      this.TimelineClicked(this.ScreenPositionToTime(current.mousePosition.x - area.x));
    }

    private void DrawTicks(Rect area)
    {
      float num1 = this.majorTickUnits * this.timeScale;
      float time = this.majorTickUnits * Mathf.Floor(this.offset / this.majorTickUnits);
      float screenPosition = this.TimeToScreenPosition(time);
      float num2 = this.offset * this.timeScale + area.width;
      while ((double) screenPosition < (double) num2)
      {
        this.tickLabelRect.x = screenPosition;
        GUI.Label(this.tickLabelRect, this.FormatTime(time), EditorStyles.miniLabel);
        this.line.DrawVerticalLine(0.75f, screenPosition, 7f, this.toolbarHeight);
        this.line.DrawVerticalLine(0.25f, screenPosition, this.toolbarHeight, area.height);
        if ((double) this.minorTickUnits > 0.0)
        {
          float num3 = this.majorTickUnits / this.minorTickUnits;
          float num4 = num1 / num3;
          float x = screenPosition + num4;
          for (int index = 0; (double) index < (double) num3 - 1.0; ++index)
          {
            this.line.DrawVerticalLine(0.5f, x, 14f, this.toolbarHeight);
            this.line.DrawVerticalLine(0.15f, x, this.toolbarHeight, area.height);
            x += num4;
          }
        }
        screenPosition += num1;
        time += this.majorTickUnits;
      }
    }

    [Localizable(false)]
    private string FormatTime(float time) => string.Format("{0:0.00}", (object) time).Replace('.', ':');

    private void UpdateTicks() => this.UpdateTickUnits();

    private void UpdateTickUnits()
    {
      this.majorTickUnits = this.tickUnits[this.tickUnits.Length - 1];
      this.minorTickUnits = 0.0f;
      for (int index = 0; index < this.tickUnits.Length; ++index)
      {
        float tickUnit = this.tickUnits[index];
        if ((double) tickUnit * (double) this.timeScale > 50.0)
        {
          this.majorTickUnits = tickUnit;
          if (index <= 0)
            break;
          this.minorTickUnits = this.tickUnits[index - 1];
          break;
        }
      }
    }

    public void DrawDebugLine(float time)
    {
      float screenPosition = this.TimeToScreenPosition(time);
      if ((double) screenPosition < 0.0)
        return;
      float num = screenPosition + (this.area.x - 3f);
      this.debugLineRect.Set(num, 0.0f, num, this.area.height - 15f);
      GUIHelpers.BeginGuiColor(Color.white);
      GUI.Box(this.debugLineRect, GUIContent.none, FsmEditorStyles.TimelineDebugLine);
      GUIHelpers.EndGuiColor();
    }

    public delegate void TimelineClickedHandler(float time);
  }
}
