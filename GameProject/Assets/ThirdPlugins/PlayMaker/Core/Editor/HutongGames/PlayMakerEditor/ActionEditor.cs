// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.ActionEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;
using UnityEngine.UI;

namespace HutongGames.PlayMakerEditor
{
  public class ActionEditor
  {
    public static GameObject GameObjectContext;
    private static string fsmNameContext;
    private static MonoBehaviour behaviourContext;
    private static FsmEventTarget fsmEventTargetContext;
    public static FsmEventTarget FSMEventTargetContextGlobal;
    public static bool PreviewMode;
    private static FsmStateAction editingAction;
    private static FieldInfo editingField;
    private static string editingFieldName;
    private static object editingObject;
    private static Array editingArray;
    private static FsmArray editingFsmArray;
    private static System.Type editingType;
    private static VariableType editingTypeConstraint;
    private static int editingIndex;
    private static NamedVariable editingVariable;
    private static VariableType editingVariableType;
    private bool actionIsDirty;
    private static object contextMenuObject;
    private static FieldInfo contextMenuField;
    private static string contextMenuFieldName;
    private static int contextMenuIndex;
    private static VariableType contextVariableType;
    private static FsmArray contextFsmArray;
    private static VariableType contextTypeConstraint;
    private static System.Type contextType;
    private static NamedVariable contextVariable;
    private static bool exitGUI;
    private bool showRequiredFieldFootnote;
    private readonly Dictionary<System.Type, ActionEditor.CachedData> cachedActionData = new Dictionary<System.Type, ActionEditor.CachedData>();
    private ActionEditor.CachedData cachedData;
    private static FieldInfo FsmEventTargetFsmNameField;
    private static FieldInfo FsmEventTargetExcludeSelfField;
    private static FieldInfo FsmEventTargetSendToChildrenField;
    private static FieldInfo FsmOwnerDefaultGameObjectField;
    private GameObject addComponentTarget;
    private readonly GUIContent compoundArrayLabel = new GUIContent();
    private FieldInfo editingEnumField;

    public ActionEditor()
    {
      ActionEditor.FsmEventTargetFsmNameField = typeof (FsmEventTarget).GetField("fsmName", BindingFlags.Instance | BindingFlags.Public);
      ActionEditor.FsmEventTargetExcludeSelfField = typeof (FsmEventTarget).GetField("excludeSelf", BindingFlags.Instance | BindingFlags.Public);
      ActionEditor.FsmEventTargetSendToChildrenField = typeof (FsmEventTarget).GetField("sendToChildren", BindingFlags.Instance | BindingFlags.Public);
      ActionEditor.FsmOwnerDefaultGameObjectField = typeof (FsmOwnerDefault).GetField("gameObject", BindingFlags.Instance | BindingFlags.NonPublic);
    }

    public void Reset() => this.showRequiredFieldFootnote = false;

    public static void SetVariableSelectionContext(object obj, FieldInfo field)
    {
      ActionEditor.contextMenuObject = obj;
      ActionEditor.contextMenuField = field;
    }

    public static void SetEditingContext(object obj, System.Type type, int index = -1)
    {
      ActionEditor.editingObject = obj;
      ActionEditor.editingType = type;
      ActionEditor.editingIndex = index;
    }

    public bool OnGUI(FsmStateAction action)
    {
      if (ActionEditor.exitGUI)
      {
        FsmEditor.Repaint(true);
        GUIUtility.ExitGUI();
      }
      if (action == null)
        return false;
      ActionEditor.editingAction = action;
      this.actionIsDirty = false;
      ActionEditor.GameObjectContext = (GameObject) null;
      ActionEditor.behaviourContext = (MonoBehaviour) null;
      ActionEditor.fsmEventTargetContext = (FsmEventTarget) null;
      this.UpdateCachedData(action);
      if (!CustomActionEditors.HasCustomEditor(action.GetType()))
        return this.DrawDefaultInspector(action);
      CustomActionEditor customEditor = CustomActionEditors.GetCustomEditor(action);
      if (customEditor != null)
        return customEditor.OnGUI();
      EditorGUILayout.HelpBox(Strings.Error_Failed_to_draw_inspector, MessageType.Error);
      return false;
    }

    private void UpdateCachedData(FsmStateAction action)
    {
      System.Type type = action.GetType();
      this.cachedData = (ActionEditor.CachedData) null;
      if (this.cachedActionData.TryGetValue(type, out this.cachedData))
        return;
      this.cachedData = new ActionEditor.CachedData(action);
      this.cachedActionData.Add(type, this.cachedData);
    }

    public bool DrawDefaultInspector(FsmStateAction action)
    {
      if (action == null)
        return false;
      VariableEditor.DebugVariables = FsmEditorSettings.DebugActionParameters && !ActionEditor.PreviewMode;
      if (this.cachedData.actionNote != null)
      {
        GUILayout.Label(this.cachedData.actionNote, FsmEditorStyles.LabelWithWordWrap);
        GUILayout.Space(2f);
      }
      foreach (FieldInfo field in this.cachedData.fields)
      {
        if (this.cachedData.IsVisible(action, field))
          this.EditField(action, field);
      }
      return this.actionIsDirty;
    }

    public FieldInfo FindField(string fieldName)
    {
      foreach (FieldInfo field in this.cachedData.fields)
      {
        if (field.Name == fieldName)
          return field;
      }
      return (FieldInfo) null;
    }

    public void EditField(FsmStateAction action, string fieldName)
    {
      FieldInfo field = this.FindField(fieldName);
      if (field != null)
        this.EditField(action, field);
      else
        EditorGUILayout.HelpBox(Strings.Label_Could_Not_Find_Field + fieldName, MessageType.Error);
    }

    public void EditField(
      FsmStateAction action,
      FieldInfo field,
      string label = null,
      object[] attributes = null)
    {
      VariableEditor.DebugVariables = FsmEditorSettings.DebugActionParameters && !ActionEditor.PreviewMode;
      EditorGUI.BeginChangeCheck();
      ActionEditor.editingField = field;
      System.Type fieldType = field.FieldType;
      object fieldValue = field.GetValue((object) action);
      if (attributes == null)
        attributes = CustomAttributeHelpers.GetCustomAttributes(field);
      string labelText = label == null ? Labels.NicifyVariableName(field.Name) : label;
      bool flag1 = false;
      List<FsmError> parameterErrors = FsmErrorChecker.GetParameterErrors(action, field.Name);
      foreach (FsmError fsmError in parameterErrors)
      {
        if (fsmError.Type == FsmError.ErrorType.requiredField)
        {
          GUIHelpers.BeginGuiBackgroundColor(FsmEditorStyles.GuiBackgroundErrorColor);
          this.showRequiredFieldFootnote = true;
          flag1 = true;
          break;
        }
      }
      string actionSection = CustomAttributeHelpers.GetActionSection((IEnumerable<object>) attributes);
      if (actionSection != null)
      {
        GUILayout.Space(5f);
        if (actionSection != string.Empty)
          GUILayout.Label(actionSection, EditorStyles.boldLabel);
        FsmEditorGUILayout.LightDivider();
      }
      bool flag2 = !ActionEditor.PreviewMode && (FsmEditor.SelectedState.HideUnused || FsmEditorSettings.DimUnusedActionParameters) && ActionEditor.HideField(fieldType, fieldValue, attributes);
      if (flag2 && FsmEditor.SelectedState.HideUnused)
        return;
      if (flag2 && FsmEditorSettings.DimUnusedActionParameters)
        GUIHelpers.BeginGuiColorFaded(0.3f);
      else
        GUIHelpers.BeginGuiColor();
      if (fieldType.IsArray)
      {
        if (!this.cachedData.compoundArrayParent.Contains(field))
        {
          if (this.cachedData.compoundArrayChild.Contains(field))
            this.EditCompoundArray(action, this.cachedData.compoundArrayChild.IndexOf(field));
          else
            this.EditField((object) action, field, labelText, fieldType, fieldValue, attributes);
        }
      }
      else
        this.EditField((object) action, field, labelText, fieldType, fieldValue, attributes);
      foreach (FsmError error in parameterErrors)
      {
        if (error.Type != FsmError.ErrorType.requiredField)
        {
          if (error.Type == FsmError.ErrorType.missingRequiredComponent)
          {
            if (this.CanAutoAddComponent(error))
            {
              if (GUILayout.Button(error.ErrorString + Environment.NewLine + Strings.ActionEditor_Click_to_Add_Required_Component, FsmEditorStyles.ActionErrorBox))
                this.AutoAddComponent(error);
            }
            else
              GUILayout.Box(error.ErrorString, FsmEditorStyles.ActionErrorBox);
          }
          else if (error.Type == FsmError.ErrorType.missingTransitionEvent)
          {
            if (GUILayout.Button(error.ErrorString + Environment.NewLine + Strings.ActionEditor_Click_to_Add_Transition_to_State, FsmEditorStyles.ActionErrorBox))
              EditorCommands.AddTransitionToSelectedState(error.info);
          }
          else if (error.Type == FsmError.ErrorType.eventNotGlobal)
          {
            if (GUILayout.Button(error.ErrorString + Environment.NewLine + Strings.Label_Click_to_Make_Global, FsmEditorStyles.ActionErrorBox))
            {
              EditorCommands.MakeEventGlobal(error.info);
              FsmErrorChecker.Update();
            }
          }
          else
            GUILayout.Box(error.ErrorString, FsmEditorStyles.ActionErrorBox);
        }
      }
      if (EditorGUI.EndChangeCheck())
        this.actionIsDirty = true;
      if (flag1)
        GUIHelpers.EndGuiBackgroundColor();
      GUIHelpers.EndGuiColor();
      this.cachedData.DrawPreview(action, field);
    }

    private bool CanAutoAddComponent(FsmError error) => error != null && !((UnityEngine.Object) error.GameObject == (UnityEngine.Object) null) && error.ObjectType != null && (error.ObjectType == typeof (Collider) || error.ObjectType == typeof (Renderer) || error.ObjectType.IsSubclassOf(typeof (UnityEngine.Component)) && !error.ObjectType.IsAbstract && error.ObjectType != typeof (Graphic));

    private void AutoAddComponent(FsmError error)
    {
      if ((UnityEngine.Object) error.GameObject == (UnityEngine.Object) null)
        return;
      if (error.ObjectType == typeof (Collider))
        this.DoAddComponentMenu(error.GameObject, typeof (BoxCollider), typeof (SphereCollider), typeof (CapsuleCollider), typeof (MeshCollider), typeof (WheelCollider), typeof (TerrainCollider));
      else if (error.ObjectType == typeof (Renderer))
        EditorCommands.AddComponent(error.GameObject, typeof (MeshRenderer));
      else if (error.ObjectType.IsSubclassOf(typeof (UnityEngine.Component)) && !error.ObjectType.IsAbstract && error.ObjectType != typeof (Graphic))
        EditorCommands.AddComponent(error.GameObject, error.ObjectType);
      else
        Debug.LogError((object) string.Format(Strings.ActionEditor_Cannot_Add_Component_Type_XXX, (object) error.ObjectType.Name));
    }

    private void DoAddComponentMenu(GameObject go, params System.Type[] componentTypes)
    {
      this.addComponentTarget = go;
      GenericMenu genericMenu = new GenericMenu();
      foreach (System.Type componentType in componentTypes)
        genericMenu.AddItem(new GUIContent(Labels.StripUnityEngineNamespace(componentType.FullName)), false, new GenericMenu.MenuFunction2(this.DoAddComponent), (object) componentType);
      genericMenu.ShowAsContext();
    }

    private void DoAddComponent(object userdata)
    {
      if (!(userdata is System.Type componentType))
        return;
      EditorCommands.AddComponent(this.addComponentTarget, componentType);
    }

    public void EditField(
      string fieldName,
      string labelText,
      object fieldValue,
      object[] attributes)
    {
      FieldInfo field = ActionEditor.editingObject.GetType().GetField(fieldName);
      this.EditField(ActionEditor.editingObject, field, labelText, field.FieldType, fieldValue, attributes);
    }

    public void EditField(
      object obj,
      FieldInfo field,
      string labelText,
      object fieldValue,
      object[] attributes)
    {
      this.EditField(obj, field, labelText, field.FieldType, fieldValue, attributes);
    }

    [Localizable(false)]
    private void EditField(
      object obj,
      FieldInfo field,
      string labelText,
      System.Type fieldType,
      object fieldValue,
      object[] attributes)
    {
      try
      {
        ActionEditor.editingObject = obj;
        ActionEditor.editingField = field;
        object obj1 = this.GUIForFieldTypes(labelText, fieldType, fieldValue, attributes);
        if (fieldValue == obj1)
          return;
        field.SetValue(obj, obj1);
      }
      catch (Exception ex)
      {
        if (ex is ExitGUIException)
        {
          throw;
        }
        else
        {
          EditorGUILayout.HelpBox(string.Format("Error editing field: {0}\n{1}", (object) labelText, (object) ex.Message), MessageType.Error);
          GUILayout.BeginHorizontal();
          if (GUILayout.Button("Show More Info..."))
            EditorUtility.DisplayDialog("Action Error", ex.ToString(), "OK");
          if (GUILayout.Button("Copy To Clipboard"))
            EditorGUIUtility.systemCopyBuffer = ex.ToString();
          GUILayout.EndHorizontal();
        }
      }
    }

    [Localizable(false)]
    private void EditCompoundArray(FsmStateAction action, int compoundArrayIndex)
    {
      this.compoundArrayLabel.text = this.cachedData.compoundArrayList[compoundArrayIndex].Name;
      FieldInfo field1 = this.cachedData.compoundArrayParent[compoundArrayIndex];
      System.Type fieldType1 = field1.FieldType;
      object obj1 = field1.GetValue((object) action);
      System.Type elementType1 = fieldType1.GetElementType();
      object[] customAttributes1 = CustomAttributeHelpers.GetCustomAttributes(field1);
      string firstArrayName = this.cachedData.compoundArrayList[compoundArrayIndex].FirstArrayName;
      if (elementType1 == null)
        return;
      Array array1;
      if (obj1 != null)
      {
        array1 = (Array) obj1;
      }
      else
      {
        array1 = Array.CreateInstance(elementType1, 0);
        field1.SetValue((object) ActionEditor.editingAction, (object) array1);
      }
      FieldInfo field2 = this.cachedData.compoundArrayChild[compoundArrayIndex];
      System.Type fieldType2 = field2.FieldType;
      object obj2 = field2.GetValue((object) action);
      System.Type elementType2 = fieldType2.GetElementType();
      object[] customAttributes2 = CustomAttributeHelpers.GetCustomAttributes(field2);
      string secondArrayName = this.cachedData.compoundArrayList[compoundArrayIndex].SecondArrayName;
      if (elementType2 == null)
        return;
      Array array2;
      if (obj2 != null)
      {
        array2 = (Array) obj2;
      }
      else
      {
        array2 = Array.CreateInstance(elementType2, 0);
        field2.SetValue((object) ActionEditor.editingAction, (object) array2);
      }
      if (array2.Length != array1.Length)
        array2 = this.ResizeArray(array2, array1.Length);
      int newSize = EditorGUILayout.DelayedIntField(this.compoundArrayLabel, array1.Length);
      if (newSize != array1.Length)
      {
        field1.SetValue((object) ActionEditor.editingAction, (object) this.ResizeArray(array1, newSize));
        field2.SetValue((object) ActionEditor.editingAction, (object) this.ResizeArray(array2, newSize));
        this.actionIsDirty = true;
      }
      else
      {
        if (array1.Length > 0)
          FsmEditorGUILayout.LightDivider();
        for (int index = 0; index < array2.Length; ++index)
        {
          try
          {
            ActionEditor.editingObject = (object) array1;
            ActionEditor.editingIndex = index;
            array1.SetValue(this.GUIForFieldTypes(" " + firstArrayName, elementType1, array1.GetValue(index), customAttributes1), index);
          }
          catch
          {
            FsmEditor.Repaint(true);
            GUIUtility.ExitGUI();
            return;
          }
          ActionEditor.editingObject = (object) array2;
          ActionEditor.editingIndex = index;
          array2.SetValue(this.GUIForFieldTypes(" " + secondArrayName, elementType2, array2.GetValue(index), customAttributes2), index);
          FsmEditorGUILayout.LightDivider();
        }
        if (!GUI.changed)
          return;
        field1.SetValue((object) action, (object) array1);
        field2.SetValue((object) action, (object) array2);
        this.actionIsDirty = true;
      }
    }

    private static bool HideField(System.Type type, object fieldValue, object[] attributes)
    {
      if (type.IsEnum)
        return false;
      bool flag = false;
      foreach (Attribute attribute in attributes)
      {
        switch (attribute)
        {
          case RequiredFieldAttribute _:
            return false;
          case UIHintAttribute _:
            flag = (attribute as UIHintAttribute).Hint == UIHint.Variable;
            break;
        }
      }
      if (type.IsSubclassOf(typeof (NamedVariable)))
      {
        NamedVariable namedVariable = (NamedVariable) fieldValue;
        if (namedVariable == null)
          return true;
        if (namedVariable.UseVariable | flag)
          return string.IsNullOrEmpty(namedVariable.Name);
        if (type == typeof (FsmGameObject))
          return (UnityEngine.Object) ((FsmGameObject) fieldValue).Value == (UnityEngine.Object) null;
        if (type == typeof (FsmString))
          return string.IsNullOrEmpty(((FsmString) fieldValue).Value);
        if (type == typeof (FsmTexture))
          return (UnityEngine.Object) ((FsmTexture) fieldValue).Value == (UnityEngine.Object) null;
        if (type == typeof (FsmMaterial))
          return (UnityEngine.Object) ((FsmMaterial) fieldValue).Value == (UnityEngine.Object) null;
        if (type == typeof (FsmObject))
          return ((FsmObject) fieldValue).Value == (UnityEngine.Object) null;
      }
      else
      {
        if (type == typeof (FsmEvent))
          return FsmEvent.IsNullOrEmpty((FsmEvent) fieldValue);
        if (type.IsArray)
          return !(fieldValue is Array array) || array.Length == 0;
      }
      return false;
    }

    public object EditField(string labelText, System.Type type, object fieldValue, object[] attributes)
    {
      object obj = this.GUIForFieldTypes(labelText, type, fieldValue, attributes);
      if (obj == fieldValue)
        return obj;
      FsmEditor.RecordUndo("Edit Action Parameter");
      return obj;
    }

    public object EditField(System.Type type, object fieldValue)
    {
      object obj = this.GUIForFieldTypes("", type, fieldValue, new object[0]);
      if (obj == fieldValue)
        return obj;
      FsmEditor.RecordUndo("Edit Action Parameter");
      return obj;
    }

    private object GUIForFieldTypes(
      string labelText,
      System.Type type,
      object fieldValue,
      object[] attributes)
    {
      if (type == null)
      {
        EditorGUI.BeginDisabledGroup(true);
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.EnumPopup(labelText, (Enum) HutongGames.PlayMaker.None.None);
        GUILayout.Toggle(false, FsmEditorContent.VariableButton, FsmEditorStyles.MiniToggle);
        EditorGUILayout.EndHorizontal();
        EditorGUI.EndDisabledGroup();
        return fieldValue;
      }
      string title = CustomAttributeHelpers.GetTitle((IEnumerable<object>) attributes);
      if (title != null)
        labelText = title;
      GUIContent tooltipLabelContent = FsmEditorGUILayout.GetTooltipLabelContent(labelText, CustomAttributeHelpers.GetTooltip(type, (IEnumerable<object>) attributes));
      if (type == typeof (int))
        return (object) ActionEditor.EditIntValue(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (float))
        return (object) ActionEditor.EditFloatValue(tooltipLabelContent, fieldValue, (IEnumerable<object>) attributes);
      if (type == typeof (bool))
        return (object) EditorGUILayout.Toggle(tooltipLabelContent, (bool) fieldValue);
      if (type == typeof (string))
        return (object) ActionEditor.EditStringValue(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (Vector2))
        return (object) EditorGUILayout.Vector2Field(labelText, (Vector2) fieldValue);
      if (type == typeof (Vector3))
        return (object) EditorGUILayout.Vector3Field(labelText, (Vector3) fieldValue);
      if (type == typeof (Vector4))
        return (object) EditorGUILayout.Vector4Field(labelText, (Vector4) fieldValue);
      if (type == typeof (Rect))
        return (object) EditorGUILayout.RectField(labelText, (Rect) fieldValue);
      if (type == typeof (FunctionCall))
        return (object) this.EditFunctionCall(fieldValue, attributes);
      if (type == typeof (FsmTemplateControl))
        return (object) this.EditFsmTemplateControl(fieldValue);
      if (type == typeof (FsmVar))
        return (object) this.EditFsmVar(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmEventTarget))
        return (object) this.EditFsmEventTarget(fieldValue);
      if (type == typeof (LayoutOption))
        return (object) ActionEditor.EditLayoutOption(fieldValue);
      if (type == typeof (FsmFloat))
        return (object) ActionEditor.EditFsmFloat(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmInt))
        return (object) ActionEditor.EditFsmInt(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmBool))
        return (object) ActionEditor.EditFsmBool(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmVector2))
        return (object) ActionEditor.EditFsmVector2(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmVector3))
        return (object) ActionEditor.EditFsmVector3(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmRect))
        return (object) ActionEditor.EditFsmRect(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmQuaternion))
        return (object) ActionEditor.EditFsmQuaternion(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmObject))
        return (object) ActionEditor.EditFsmObject(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmMaterial))
        return (object) ActionEditor.EditFsmMaterial(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmTexture))
        return (object) ActionEditor.EditFsmTexture(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmColor))
        return (object) ActionEditor.EditFsmColor(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmGameObject))
      {
        FsmGameObject fsmGameObject = ActionEditor.EditFsmGameObject(tooltipLabelContent, fieldValue, attributes);
        if ((UnityEngine.Object) fsmGameObject.Value != (UnityEngine.Object) null)
          ActionEditor.GameObjectContext = fsmGameObject.Value;
        return (object) fsmGameObject;
      }
      if (type == typeof (FsmOwnerDefault))
        return (object) ActionEditor.EditFsmOwnerDefault(tooltipLabelContent, fieldValue);
      if (type == typeof (FsmEvent))
      {
        EventTargetAttribute attribute = CustomAttributeHelpers.GetAttribute<EventTargetAttribute>((IEnumerable<object>) attributes);
        if (attribute != null)
        {
          if (ActionEditor.fsmEventTargetContext == null)
            ActionEditor.fsmEventTargetContext = new FsmEventTarget();
          ActionEditor.fsmEventTargetContext.target = attribute.Target;
        }
        ActionEditor.EventSelector(tooltipLabelContent, fieldValue as FsmEvent, ActionEditor.fsmEventTargetContext ?? ActionEditor.FSMEventTargetContextGlobal);
        return fieldValue;
      }
      if (type == typeof (FsmString))
        return (object) ActionEditor.EditFsmString(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (AnimationState))
        return (object) FsmEditorGUILayout.AnimationStatePopup(tooltipLabelContent, (AnimationState) fieldValue, ActionEditor.GameObjectContext);
      if (type == typeof (Behaviour))
        return (object) FsmEditorGUILayout.BehaviorPopup(tooltipLabelContent, (Behaviour) fieldValue, ActionEditor.GameObjectContext);
      if (type == typeof (MonoBehaviour))
        return (object) FsmEditorGUILayout.BehaviorPopup(tooltipLabelContent, (MonoBehaviour) fieldValue, ActionEditor.GameObjectContext);
      if (type == typeof (Color))
        return (object) EditorGUILayout.ColorField(tooltipLabelContent, (Color) fieldValue);
      if (type == typeof (FsmAnimationCurve))
      {
        FsmAnimationCurve fsmAnimationCurve = (FsmAnimationCurve) fieldValue ?? new FsmAnimationCurve();
        GUILayout.BeginHorizontal();
        EditorGUI.BeginChangeCheck();
        AnimationCurve animationCurve = EditorGUILayout.CurveField(labelText, fsmAnimationCurve.curve);
        if (EditorGUI.EndChangeCheck())
        {
          FsmEditor.RecordUndo("Edit Animation Curve");
          fsmAnimationCurve.curve = animationCurve;
        }
        if (FsmEditorGUILayout.DeleteButton())
          fsmAnimationCurve = new FsmAnimationCurve();
        GUILayout.EndHorizontal();
        return (object) fsmAnimationCurve;
      }
      if (type == typeof (GameObject))
      {
        GameObject gameObject = ActionEditor.GameObjectField(tooltipLabelContent, fieldValue);
        if ((UnityEngine.Object) gameObject != (UnityEngine.Object) null)
          ActionEditor.GameObjectContext = gameObject;
        return (object) gameObject;
      }
      if (type.IsArray)
        return (object) this.EditArray(tooltipLabelContent, type.GetElementType(), fieldValue, attributes);
      if (type == typeof (FsmArray))
        return (object) this.EditFsmArray(tooltipLabelContent, fieldValue, attributes);
      if (type == typeof (FsmEnum))
        return (object) this.EditFsmEnum(tooltipLabelContent, fieldValue, attributes);
      if (fieldValue is Enum fieldEnum)
        return this.EditEnum(tooltipLabelContent, type, fieldEnum);
      if (type.IsSubclassOf(typeof (UnityEngine.Object)) || type == typeof (UnityEngine.Object))
        return (object) EditorGUILayout.ObjectField(tooltipLabelContent.text, (UnityEngine.Object) fieldValue, type, !FsmEditor.SelectedFsmIsPersistent());
      if (type == typeof (FsmProperty))
        return (object) this.EditFsmProperty(fieldValue, attributes);
      if (type.IsClass)
        return this.EditCustomType(tooltipLabelContent, type, fieldValue, attributes);
      GUILayout.Label(string.Format(Strings.ActionEditor_Error_Unsupported_Type_XXX, (object) type), FsmEditorStyles.ErrorBox);
      return fieldValue;
    }

    private object EditEnum(GUIContent label, System.Type enumType, Enum fieldEnum)
    {
      if (!FsmEditorSettings.UseAdvancedDropdowns)
        return (object) EditorGUILayout.EnumPopup(label, fieldEnum);
      HutongGames.EnumData enumData = HutongGames.EnumDataUtility.GetCachedEnumData(enumType);
      if (enumData.displayNames.Length < 6)
        return (object) EditorGUILayout.EnumPopup(label, fieldEnum);
      this.editingEnumField = ActionEditor.editingField;
      GUILayout.BeginHorizontal();
      FsmEditorGUILayout.PrefixLabel(label);
      Rect rect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.popup);
      int index = Array.IndexOf<Enum>(enumData.values, fieldEnum);
      if (GUI.Button(rect, enumData.displayNames[index], EditorStyles.popup))
      {
        Action<int> onSelectionMade = (Action<int>) (i =>
        {
          this.editingEnumField.SetValue(ActionEditor.editingObject, (object) enumData.values[i]);
          FsmEditor.SaveActions();
        });
        EnumDropdown.ShowEnumDropdown(rect, enumType, onSelectionMade);
      }
      GUILayout.EndHorizontal();
      return (object) fieldEnum;
    }

    private object EditCustomType(
      GUIContent label,
      System.Type customType,
      object fieldValue,
      object[] attributes)
    {
      if (fieldValue == null)
        fieldValue = Activator.CreateInstance(customType);
      ActionEditor.editingObject = fieldValue;
      if (label != null && !string.IsNullOrEmpty(label.text))
        GUILayout.Label(label);
      PropertyDrawer propertyDrawer = PropertyDrawers.GetPropertyDrawer(customType);
      if (propertyDrawer != null)
        return propertyDrawer.OnGUI(label, fieldValue, !FsmEditor.SelectedFsmIsPersistent(), attributes);
      foreach (FieldInfo field in ActionData.GetFields(customType))
      {
        string labelText = Labels.NicifyVariableName(field.Name);
        ActionEditor.editingField = field;
        attributes = CustomAttributeHelpers.GetCustomAttributes(field);
        this.EditField(fieldValue, field, labelText, field.FieldType, field.GetValue(fieldValue), attributes);
      }
      FsmEditorGUILayout.LightDivider();
      return fieldValue;
    }

    private Array EditArray(
      GUIContent label,
      System.Type elementType,
      object fieldValue,
      object[] attributes)
    {
      Array array = fieldValue == null ? Array.CreateInstance(elementType, 0) : (Array) fieldValue;
      return this.EditArray(label, elementType, array, attributes);
    }

    [Localizable(false)]
    private Array EditArray(
      GUIContent label,
      System.Type elementType,
      Array array,
      object[] attributes)
    {
      ArrayEditorAttribute arrayEditorAttribute = CustomAttributeHelpers.GetAttribute<ArrayEditorAttribute>((IEnumerable<object>) attributes) ?? new ArrayEditorAttribute(VariableType.Unknown);
      bool changed = GUI.changed;
      if (arrayEditorAttribute.Resizable)
      {
        if (ActionEditor.editingArray == array)
        {
          int num = EditorGUILayout.DelayedIntField(label, array.Length);
          if (num != array.Length)
            return this.ResizeArray(Mathf.Clamp(num, arrayEditorAttribute.MinSize, arrayEditorAttribute.MaxSize));
        }
        else
        {
          int num = EditorGUILayout.DelayedIntField(label, array.Length);
          if (num != array.Length)
          {
            ActionEditor.editingArray = array;
            return this.ResizeArray(Mathf.Clamp(num, arrayEditorAttribute.MinSize, arrayEditorAttribute.MaxSize));
          }
        }
      }
      else if (array.Length != arrayEditorAttribute.FixedSize)
      {
        ActionEditor.editingArray = array;
        return this.ResizeArray(arrayEditorAttribute.FixedSize);
      }
      if (array.Length < arrayEditorAttribute.MinSize)
      {
        ActionEditor.editingArray = array;
        return this.ResizeArray(arrayEditorAttribute.MinSize);
      }
      if (array.Length > arrayEditorAttribute.MaxSize)
      {
        ActionEditor.editingArray = array;
        return this.ResizeArray(arrayEditorAttribute.MaxSize);
      }
      GUI.changed = changed;
      ++EditorGUI.indentLevel;
      string str = "Element ";
      if (!string.IsNullOrEmpty(arrayEditorAttribute.ElementName))
        str = arrayEditorAttribute.ElementName + " ";
      for (int index = 0; index < array.Length; ++index)
      {
        ActionEditor.editingObject = (object) array;
        ActionEditor.editingIndex = index;
        if (elementType == typeof (FsmVar) && array.GetValue(index) is FsmVar fsmVar)
          ActionEditor.editingType = fsmVar.ObjectType;
        array.SetValue(this.GUIForFieldTypes(str + (object) (index + 1), elementType, array.GetValue(index), attributes), index);
      }
      FsmEditorGUILayout.LightDivider();
      --EditorGUI.indentLevel;
      if (GUI.changed)
        this.actionIsDirty = true;
      ActionEditor.editingFsmArray = (FsmArray) null;
      return array;
    }

    public bool Update()
    {
      ActionEditor.exitGUI = false;
      this.UpdateHelpBoxes();
      return false;
    }

    private void UpdateHelpBoxes() => StateInspector.ShowRequiredFieldFootnote = this.showRequiredFieldFootnote;

    private Array ResizeArray(int newSize)
    {
      Array array = this.ResizeArray(ActionEditor.editingArray, newSize);
      if (ActionEditor.editingFsmArray != null && ActionEditor.editingArray == null)
      {
        ActionEditor.editingFsmArray.Values = (object[]) array;
        ActionEditor.editingFsmArray.SaveChanges();
      }
      ActionEditor.editingArray = (Array) null;
      ActionEditor.editingFsmArray = (FsmArray) null;
      this.actionIsDirty = true;
      return array;
    }

    private Array ResizeArray(Array array, int newSize)
    {
      if (newSize < 0)
        newSize = 0;
      if (array == null || newSize == array.Length)
        return array;
      FsmEditor.RecordUndo(Strings.ActionEditor_Undo_Resize_Array);
      Array instance = Array.CreateInstance(array.GetType().GetElementType(), newSize);
      Array.Copy(array, instance, Math.Min(array.Length, newSize));
      return instance;
    }

    public static object GetDefault(System.Type type) => !type.IsValueType ? (object) null : Activator.CreateInstance(type);

    private static GameObject GameObjectField(GUIContent label, object fieldValue) => (GameObject) EditorGUILayout.ObjectField(label, (UnityEngine.Object) fieldValue, typeof (GameObject), !FsmEditor.SelectedFsmIsPersistent());

    private static LayoutOption EditLayoutOption(object fieldValue)
    {
      LayoutOption layoutOption = (LayoutOption) fieldValue ?? new LayoutOption();
      layoutOption.option = (LayoutOption.LayoutOptionType) EditorGUILayout.EnumPopup(Strings.ActionEditor_EditLayoutOption_Option, (Enum) layoutOption.option);
      switch (layoutOption.option)
      {
        case LayoutOption.LayoutOptionType.Width:
        case LayoutOption.LayoutOptionType.Height:
        case LayoutOption.LayoutOptionType.MinWidth:
        case LayoutOption.LayoutOptionType.MaxWidth:
        case LayoutOption.LayoutOptionType.MinHeight:
        case LayoutOption.LayoutOptionType.MaxHeight:
          layoutOption.floatParam = VariableEditor.FsmFloatField(GUIContent.none, FsmEditor.SelectedFsm, layoutOption.floatParam);
          break;
        case LayoutOption.LayoutOptionType.ExpandWidth:
        case LayoutOption.LayoutOptionType.ExpandHeight:
          layoutOption.boolParam = VariableEditor.FsmBoolField(GUIContent.none, FsmEditor.SelectedFsm, layoutOption.boolParam);
          break;
      }
      return layoutOption;
    }

    [Localizable(false)]
    private FsmProperty EditFsmProperty(object fieldValue, object[] attributes)
    {
      FsmProperty fsmProperty = (FsmProperty) fieldValue ?? new FsmProperty();
      fsmProperty.CheckForReinitialize();
      bool changed = GUI.changed;
      GUI.changed = false;
      ActionEditor.editingObject = (object) fsmProperty;
      ActionEditor.editingFieldName = "TargetObject";
      FsmObject fsmObject = ActionEditor.EditFsmObject(new GUIContent(Strings.ActionEditor_EditFsmProperty_Target_Object, Strings.ActionEditor_EditFsmProperty_Target_Object_Tooltip), (object) fsmProperty.TargetObject, attributes);
      if (GUI.changed)
      {
        fsmProperty.TargetObject = fsmObject;
        fsmProperty.TargetTypeName = fsmProperty.TargetObject.Value != (UnityEngine.Object) null ? fsmProperty.TargetObject.Value.GetType().ToString() : (string) null;
        if (fsmProperty.TargetObject.Value == (UnityEngine.Object) null)
          fsmProperty.TargetTypeName = (string) null;
        fsmProperty.Init();
      }
      if (!GUI.changed)
        GUI.changed = changed;
      if (fsmProperty.TargetObject.Value == (UnityEngine.Object) null && !fsmProperty.TargetObject.UseVariable || fsmProperty.TargetObject.IsNone)
      {
        FsmEditorGUILayout.LightDivider();
        return fsmProperty;
      }
      GUILayout.BeginHorizontal();
      FsmEditorGUILayout.PrefixLabel(Strings.ActionEditor_EditFsmProperty_Object_Type);
      FsmEditorGUILayout.ReadonlyTextField(fsmProperty.TargetTypeName);
      GUILayout.EndHorizontal();
      string str = Strings.Label_None;
      if (!string.IsNullOrEmpty(fsmProperty.PropertyName))
        str = fsmProperty.PropertyName;
      GUILayout.BeginHorizontal();
      FsmEditorGUILayout.PrefixLabel(FsmEditorContent.TempContent(Strings.Label_Property, Strings.Label_Property_Tooltip), EditorStyles.popup);
      Rect rect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.popup);
      if (GUI.Button(rect, FsmEditorContent.TempContent(str, str), EditorStyles.popup))
      {
        if (FsmEditorSettings.UseAdvancedDropdowns)
          PropertyDropdown.ShowPropertyDropdown(rect, fsmProperty);
        else
          TypeHelpers.GeneratePropertyMenu(fsmProperty).DropDown(rect);
      }
      GUILayout.EndHorizontal();
      if (string.IsNullOrEmpty(fsmProperty.PropertyName))
      {
        FsmEditorGUILayout.LightDivider();
        return fsmProperty;
      }
      if (fsmProperty.PropertyType == null)
      {
        if (!string.IsNullOrEmpty(fsmProperty.PropertyName))
          fsmProperty.SetPropertyName(fsmProperty.PropertyName);
        if (fsmProperty.PropertyType == null)
        {
          FsmEditorGUILayout.LightDivider();
          return fsmProperty;
        }
      }
      string typeTooltip = Labels.GetTypeTooltip(fsmProperty.PropertyType);
      GUIContent label = FsmEditorContent.TempContent(Strings.Label_Set_Value, typeTooltip);
      ActionEditor.editingFieldName = "SetVariable";
      if (fsmProperty.PropertyType.IsArray)
      {
        if (fsmProperty.ArrayParameter.ElementType == VariableType.Unknown)
        {
          System.Type elementType = fsmProperty.PropertyType.GetElementType();
          fsmProperty.ArrayParameter.ElementType = FsmVar.GetVariableType(elementType);
          fsmProperty.ArrayParameter.ObjectType = elementType;
        }
        ActionEditor.editingType = fsmProperty.ArrayParameter.ObjectType;
      }
      if (fsmProperty.setProperty)
      {
        if (fsmProperty.PropertyType.IsAssignableFrom(typeof (bool)))
          fsmProperty.BoolParameter = VariableEditor.FsmBoolField(label, FsmEditor.SelectedFsm, fsmProperty.BoolParameter);
        else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (float)))
          fsmProperty.FloatParameter = VariableEditor.FsmFloatField(label, FsmEditor.SelectedFsm, fsmProperty.FloatParameter);
        else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (int)))
          fsmProperty.IntParameter = VariableEditor.FsmIntField(label, FsmEditor.SelectedFsm, fsmProperty.IntParameter, (object[]) null);
        else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (GameObject)))
          fsmProperty.GameObjectParameter = VariableEditor.FsmGameObjectField(label, FsmEditor.SelectedFsm, fsmProperty.GameObjectParameter);
        else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (string)))
          fsmProperty.StringParameter = VariableEditor.FsmStringField(label, FsmEditor.SelectedFsm, fsmProperty.StringParameter, (object[]) null);
        else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Vector2)))
          fsmProperty.Vector2Parameter = VariableEditor.FsmVector2Field(label, FsmEditor.SelectedFsm, fsmProperty.Vector2Parameter);
        else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Vector3)))
          fsmProperty.Vector3Parameter = VariableEditor.FsmVector3Field(label, FsmEditor.SelectedFsm, fsmProperty.Vector3Parameter);
        else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Rect)))
          fsmProperty.RectParamater = VariableEditor.FsmRectField(label, FsmEditor.SelectedFsm, fsmProperty.RectParamater);
        else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Material)))
          fsmProperty.MaterialParameter = VariableEditor.FsmMaterialField(label, FsmEditor.SelectedFsm, fsmProperty.MaterialParameter);
        else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Texture)))
          fsmProperty.TextureParameter = VariableEditor.FsmTextureField(label, FsmEditor.SelectedFsm, fsmProperty.TextureParameter);
        else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Color)))
          fsmProperty.ColorParameter = VariableEditor.FsmColorField(label, FsmEditor.SelectedFsm, fsmProperty.ColorParameter);
        else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Quaternion)))
          fsmProperty.QuaternionParameter = VariableEditor.FsmQuaternionField(label, FsmEditor.SelectedFsm, fsmProperty.QuaternionParameter);
        else if (fsmProperty.PropertyType.IsSubclassOf(typeof (UnityEngine.Object)))
          fsmProperty.ObjectParameter = VariableEditor.FsmObjectField(label, FsmEditor.SelectedFsm, fsmProperty.ObjectParameter, fsmProperty.PropertyType, attributes);
        else if (fsmProperty.PropertyType.IsEnum)
          fsmProperty.EnumParameter = VariableEditor.FsmEnumField(label, FsmEditor.SelectedFsm, fsmProperty.EnumParameter, fsmProperty.PropertyType);
        else if (fsmProperty.PropertyType.IsArray)
          fsmProperty.ArrayParameter = this.EditFsmArray(label, (object) fsmProperty.ArrayParameter, attributes);
      }
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (bool)))
        fsmProperty.BoolParameter = VariableEditor.FsmBoolPopup(new GUIContent(Strings.ActionEditor_Store_Bool, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.BoolParameter);
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (float)))
        fsmProperty.FloatParameter = VariableEditor.FsmFloatPopup(new GUIContent(Strings.ActionEditor_Store_Float, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.FloatParameter);
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (int)))
        fsmProperty.IntParameter = VariableEditor.FsmIntPopup(new GUIContent(Strings.ActionEditor_Store_Int, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.IntParameter);
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (GameObject)))
        fsmProperty.GameObjectParameter = VariableEditor.FsmGameObjectPopup(new GUIContent(Strings.ActionEditor_Store_GameObject, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.GameObjectParameter);
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (string)))
        fsmProperty.StringParameter = VariableEditor.FsmStringPopup(new GUIContent(Strings.ActionEditor_Store_String, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.StringParameter);
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Vector2)))
        fsmProperty.Vector2Parameter = VariableEditor.FsmVector2Popup(new GUIContent(Strings.ActionEditor_Store_Vector2, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.Vector2Parameter);
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Vector3)))
        fsmProperty.Vector3Parameter = VariableEditor.FsmVector3Popup(new GUIContent(Strings.ActionEditor_Store_Vector3, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.Vector3Parameter);
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Rect)))
        fsmProperty.RectParamater = VariableEditor.FsmRectPopup(new GUIContent(Strings.ActionEditor_Store_Rect, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.RectParamater);
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Material)))
        fsmProperty.MaterialParameter = VariableEditor.FsmMaterialPopup(new GUIContent(Strings.ActionEditor_Store_Material, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.MaterialParameter);
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Texture)))
        fsmProperty.TextureParameter = VariableEditor.FsmTexturePopup(new GUIContent(Strings.ActionEditor_Store_Texture, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.TextureParameter);
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Color)))
        fsmProperty.ColorParameter = VariableEditor.FsmColorPopup(new GUIContent(Strings.ActionEditor_Store_Color, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.ColorParameter);
      else if (fsmProperty.PropertyType.IsAssignableFrom(typeof (Quaternion)))
        fsmProperty.QuaternionParameter = VariableEditor.FsmQuaternionPopup(new GUIContent(Strings.ActionEditor_Store_Quaternion, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.QuaternionParameter);
      else if (fsmProperty.PropertyType.IsSubclassOf(typeof (UnityEngine.Object)))
        fsmProperty.ObjectParameter = VariableEditor.FsmObjectPopup(new GUIContent(Strings.ActionEditor_Store_Object, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.ObjectParameter, fsmProperty.PropertyType);
      else if (fsmProperty.PropertyType.IsEnum)
        fsmProperty.EnumParameter = VariableEditor.FsmEnumPopup(new GUIContent(Strings.ActionEditor_Store_Enum, typeTooltip), FsmEditor.SelectedFsm, fsmProperty.EnumParameter, fsmProperty.PropertyType);
      else if (fsmProperty.PropertyType.IsArray)
        fsmProperty.ArrayParameter = VariableEditor.FsmArrayPopup(new GUIContent("Store Array", typeTooltip), FsmEditor.SelectedFsm, fsmProperty.ArrayParameter, FsmVar.GetVariableType(fsmProperty.PropertyType.GetElementType()));
      FsmEditorGUILayout.LightDivider();
      return fsmProperty;
    }

    public FsmVar EditFsmVar(GUIContent label, object fieldValue, object[] attributes)
    {
      FsmVar fsmVar = (FsmVar) fieldValue ?? new FsmVar();
      MatchElementTypeAttribute attribute = CustomAttributeHelpers.GetAttribute<MatchElementTypeAttribute>((IEnumerable<object>) attributes);
      if (attribute != null)
      {
        if (this.MatchElementType(fsmVar, attribute.FieldName))
        {
          if (fsmVar.Type != VariableType.Unknown)
            fsmVar.NamedVar = fsmVar.NamedVar.ResetVariableReference();
          ActionEditor.exitGUI = true;
          return fsmVar;
        }
      }
      else if (!CustomAttributeHelpers.HasAttribute<HideTypeFilter>((IEnumerable<object>) attributes))
      {
        VariableType variableType = (VariableType) EditorGUILayout.EnumPopup(new GUIContent(Strings.Label_Type), (Enum) (VariableTypeNicified) fsmVar.Type);
        if (variableType != fsmVar.Type)
        {
          fsmVar.variableName = "";
          fsmVar.Type = variableType;
        }
        else if (fsmVar.Type == VariableType.Object)
        {
          GUILayout.BeginHorizontal();
          EditorGUILayout.PrefixLabel(Strings.Label_Object_Type);
          if (GUILayout.Button(FsmEditorContent.TempContent(fsmVar.ObjectType.Name, fsmVar.ObjectType.FullName), EditorStyles.popup))
          {
            ActionEditor.contextMenuObject = (object) fsmVar;
            GenericMenu genericMenu = new GenericMenu();
            foreach (System.Type objectType in TypeHelpers.ObjectTypeList)
            {
              string fullName = objectType.FullName;
              string text = fullName.Replace('.', '/');
              genericMenu.AddItem(new GUIContent(text), fullName == fsmVar.ObjectType.FullName, new GenericMenu.MenuFunction2(this.SetFsmVarObjectType), (object) fullName);
            }
            genericMenu.ShowAsContext();
          }
          GUILayout.EndHorizontal();
        }
      }
      ActionEditor.editingObject = (object) fsmVar;
      ActionEditor.editingVariableType = fsmVar.Type;
      ActionEditor.editingType = fsmVar.ObjectType;
      if (CustomAttributeHelpers.GetUIHint(attributes) == UIHint.Variable)
      {
        if (fsmVar.Type == VariableType.Array)
          VariableEditor.FsmVarPopup(label, FsmEditor.SelectedFsm, fsmVar, fsmVar.arrayValue.ElementType, fsmVar.arrayValue.ObjectType);
        else
          VariableEditor.FsmVarPopup(label, FsmEditor.SelectedFsm, fsmVar, attribute != null ? fsmVar.Type : VariableType.Unknown, fsmVar.RealType);
      }
      else
        fsmVar.NamedVar = (NamedVariable) this.GUIForFieldTypes(label.text, fsmVar.NamedVarType, (object) fsmVar.NamedVar, attributes);
      return fsmVar;
    }

    private void SetFsmVarEnumType(object userdata)
    {
      if (!(ActionEditor.contextMenuObject is FsmVar contextMenuObject))
        return;
      contextMenuObject.EnumType = ReflectionUtils.GetGlobalType(userdata as string);
    }

    private void SetFsmVarObjectType(object userdata)
    {
      if (!(ActionEditor.contextMenuObject is FsmVar contextMenuObject))
        return;
      contextMenuObject.NamedVar = (NamedVariable) null;
      contextMenuObject.ObjectType = ReflectionUtils.GetGlobalType(userdata as string);
    }

    private bool MatchElementType(FsmVar fsmVar, string fieldName)
    {
      bool flag = false;
      foreach (FieldInfo field in ActionEditor.editingAction.GetType().GetFields())
      {
        if (field.Name == fieldName)
        {
          if (field.FieldType == typeof (FsmArray))
          {
            FsmArray fsmArray = (FsmArray) field.GetValue((object) ActionEditor.editingAction);
            if (fsmVar.Type != fsmArray.ElementType)
            {
              fsmVar.Type = fsmArray.ElementType;
              flag = true;
            }
            if (fsmVar.Type == VariableType.Enum)
            {
              if (flag || fsmVar.EnumType != fsmArray.ObjectType)
              {
                fsmVar.EnumType = fsmArray.ObjectType;
                flag = true;
                break;
              }
              break;
            }
            if (fsmVar.Type == VariableType.Object && (flag || fsmVar.ObjectType != fsmArray.ObjectType))
            {
              fsmVar.ObjectType = fsmArray.ObjectType;
              flag = true;
              break;
            }
            break;
          }
          break;
        }
      }
      return flag;
    }

    private void MatchFieldType(FsmEnum fsmEnum, string fieldName)
    {
      foreach (FieldInfo field in ActionEditor.editingAction.GetType().GetFields())
      {
        if (field.Name == fieldName)
        {
          if (field.FieldType == typeof (FsmEnum))
            fsmEnum.EnumType = ((FsmEnum) field.GetValue((object) ActionEditor.editingAction)).EnumType;
          else if (field.FieldType.IsEnum)
            fsmEnum.EnumType = field.FieldType;
        }
      }
    }

    private FsmTemplateControl EditFsmTemplateControl(object fieldValue)
    {
      FsmTemplateControl fsmTemplateControl = (FsmTemplateControl) fieldValue ?? new FsmTemplateControl();
      FsmTemplateControlEditor.GetEditor(fsmTemplateControl).OnGUI();
      return fsmTemplateControl;
    }

    private FunctionCall EditFunctionCall(object fieldValue, object[] attributes)
    {
      FunctionCall functionCall = (FunctionCall) fieldValue ?? new FunctionCall();
      ActionEditor.editingObject = (object) functionCall;
      bool coroutinesOnly = CustomAttributeHelpers.GetUIHint(attributes) == UIHint.Coroutine;
      functionCall.FunctionName = ActionEditor.EditMethodName(new GUIContent(Strings.ActionEditor_Method_Name), functionCall.FunctionName, coroutinesOnly);
      string str = FsmEditorGUILayout.ParameterTypePopup(Strings.ActionEditor_Parameter, functionCall.ParameterType);
      if (str != functionCall.ParameterType)
      {
        functionCall.ParameterType = str;
        functionCall.ResetParameters();
      }
      switch (functionCall.ParameterType)
      {
        case "Array":
          ActionEditor.contextFsmArray = functionCall.ArrayParameter;
          ActionEditor.contextTypeConstraint = VariableType.String;
          functionCall.ArrayParameter.SetType(VariableType.String);
          functionCall.ArrayParameter = VariableEditor.FsmArrayField(new GUIContent("Array"), FsmEditor.SelectedFsm, functionCall.ArrayParameter, functionCall.ArrayParameter.TypeConstraint);
          break;
        case "Color":
          functionCall.ColorParameter = VariableEditor.FsmColorField(new GUIContent(Strings.Label_Color), FsmEditor.SelectedFsm, functionCall.ColorParameter);
          break;
        case "Enum":
          GUILayout.BeginHorizontal();
          EditorGUILayout.PrefixLabel(Strings.Label_Enum_Type);
          if (GUILayout.Button(FsmEditorContent.TempContent(functionCall.EnumParameter.EnumType.Name, functionCall.EnumParameter.EnumType.FullName), EditorStyles.popup))
          {
            ActionEditor.contextMenuObject = (object) functionCall;
            GenericMenu genericMenu = new GenericMenu();
            foreach (System.Type enumType in TypeHelpers.EnumTypeList)
            {
              string fullName = enumType.FullName;
              string text = fullName.Replace('.', '/');
              genericMenu.AddItem(new GUIContent(text), fullName == functionCall.EnumParameter.EnumType.FullName, new GenericMenu.MenuFunction2(ActionEditor.SetFunctionCallEnumType), (object) fullName);
            }
            genericMenu.ShowAsContext();
          }
          GUILayout.EndHorizontal();
          ActionEditor.editingType = functionCall.EnumParameter.EnumType;
          functionCall.EnumParameter = VariableEditor.FsmEnumField(new GUIContent(Strings.Label_Enum_Value), FsmEditor.SelectedFsm, functionCall.EnumParameter, functionCall.EnumParameter.EnumType);
          break;
        case "GameObject":
          functionCall.GameObjectParameter = VariableEditor.FsmGameObjectField(new GUIContent(Strings.Label_GameObject), FsmEditor.SelectedFsm, functionCall.GameObjectParameter);
          break;
        case "Material":
          functionCall.MaterialParameter = VariableEditor.FsmMaterialField(new GUIContent(Strings.Label_Material), FsmEditor.SelectedFsm, functionCall.MaterialParameter);
          break;
        case "Object":
          ActionEditor.editingType = functionCall.ObjectParameter.ObjectType;
          functionCall.ObjectParameter = VariableEditor.FsmObjectField(new GUIContent(Strings.Label_Object), FsmEditor.SelectedFsm, functionCall.ObjectParameter, typeof (UnityEngine.Object), attributes);
          break;
        case "Quaternion":
          functionCall.QuaternionParameter = VariableEditor.FsmQuaternionField(new GUIContent(Strings.Label_Quaternion), FsmEditor.SelectedFsm, functionCall.QuaternionParameter);
          break;
        case "Rect":
          functionCall.RectParamater = VariableEditor.FsmRectField(new GUIContent(Strings.Label_Rect), FsmEditor.SelectedFsm, functionCall.RectParamater);
          break;
        case "Texture":
          functionCall.TextureParameter = VariableEditor.FsmTextureField(new GUIContent(Strings.Label_Texture), FsmEditor.SelectedFsm, functionCall.TextureParameter);
          break;
        case "Vector2":
          functionCall.Vector2Parameter = VariableEditor.FsmVector2Field(new GUIContent(Strings.Label_Vector2), FsmEditor.SelectedFsm, functionCall.Vector2Parameter);
          break;
        case "Vector3":
          functionCall.Vector3Parameter = VariableEditor.FsmVector3Field(new GUIContent(Strings.Label_Vector3), FsmEditor.SelectedFsm, functionCall.Vector3Parameter);
          break;
        case "bool":
          functionCall.BoolParameter = VariableEditor.FsmBoolField(new GUIContent(Strings.Label_Bool), FsmEditor.SelectedFsm, functionCall.BoolParameter);
          break;
        case "float":
          functionCall.FloatParameter = VariableEditor.FsmFloatField(new GUIContent(Strings.Label_Float), FsmEditor.SelectedFsm, functionCall.FloatParameter);
          break;
        case "int":
          functionCall.IntParameter = VariableEditor.FsmIntField(new GUIContent(Strings.Label_Int), FsmEditor.SelectedFsm, functionCall.IntParameter, attributes);
          break;
        case "string":
          functionCall.StringParameter = VariableEditor.FsmStringField(new GUIContent(Strings.Label_String), FsmEditor.SelectedFsm, functionCall.StringParameter, attributes);
          break;
      }
      return functionCall;
    }

    private static void SetFunctionCallEnumType(object userdata)
    {
      if (!(ActionEditor.contextMenuObject is FunctionCall contextMenuObject))
        return;
      contextMenuObject.EnumParameter.EnumType = ReflectionUtils.GetGlobalType(userdata as string);
    }

    private FsmEventTarget EditFsmEventTarget(object fieldValue)
    {
      FsmEventTarget fsmEventTarget = (FsmEventTarget) fieldValue ?? new FsmEventTarget();
      ActionEditor.editingObject = (object) fsmEventTarget;
      FsmEventTarget.EventTarget eventTarget = (FsmEventTarget.EventTarget) EditorGUILayout.EnumPopup(Strings.ActionEditor_Event_Target, (Enum) fsmEventTarget.target);
      if (eventTarget != fsmEventTarget.target)
      {
        fsmEventTarget.target = eventTarget;
        fsmEventTarget.ResetParameters();
      }
      if (this.cachedData.realActionName == Strings.ActionEditor_Set_Event_Target)
        ActionEditor.FSMEventTargetContextGlobal = fsmEventTarget;
      else
        ActionEditor.fsmEventTargetContext = fsmEventTarget;
      switch (fsmEventTarget.target)
      {
        case FsmEventTarget.EventTarget.GameObject:
          if (FsmEditorSettings.ShowHints)
            GUILayout.Box(Strings.Hint_Send_Event_to_GameObject, FsmEditorStyles.HintBox);
          fsmEventTarget.gameObject = ActionEditor.EditFsmOwnerDefault(new GUIContent(Strings.Label_GameObject, Strings.ActionEditor_Tooltip_Send_Event_to_GameObject), (object) fsmEventTarget.gameObject);
          ActionEditor.editingObject = (object) fsmEventTarget;
          ActionEditor.editingField = ActionEditor.FsmEventTargetSendToChildrenField;
          fsmEventTarget.sendToChildren = VariableEditor.FsmBoolField(new GUIContent(Strings.ActionEditor_Send_To_Children, Strings.ActionEditor_Send_To_Children_Tooltip), FsmEditor.SelectedFsm, fsmEventTarget.sendToChildren);
          break;
        case FsmEventTarget.EventTarget.GameObjectFSM:
          if (FsmEditorSettings.ShowHints)
            GUILayout.Box(Strings.Hint_Send_Event_to_FSM_on_GameObject, FsmEditorStyles.HintBox);
          fsmEventTarget.gameObject = ActionEditor.EditFsmOwnerDefault(new GUIContent(Strings.Label_GameObject, Strings.ActionEditor_Send_Event_to_FSM_on_GameObject_Tooltip), (object) fsmEventTarget.gameObject);
          ActionEditor.editingObject = (object) fsmEventTarget;
          ActionEditor.editingField = ActionEditor.FsmEventTargetFsmNameField;
          fsmEventTarget.fsmName = VariableEditor.FsmStringField(new GUIContent(Strings.ActionEditor_EditFsmEventTarget_FSM_Name, Strings.ActionEditor_EditFsmEventTarget_FSM_Name_Tooltip), FsmEditor.SelectedFsm, fsmEventTarget.fsmName, new object[1]
          {
            (object) new UIHintAttribute(UIHint.FsmName)
          });
          break;
        case FsmEventTarget.EventTarget.FSMComponent:
          if (FsmEditorSettings.ShowHints)
            GUILayout.Box(Strings.Hint_Send_Event_to_FsmComponent, FsmEditorStyles.HintBox);
          fsmEventTarget.fsmComponent = (PlayMakerFSM) EditorGUILayout.ObjectField(new GUIContent(Strings.ActionEditor_EditFsmEventTarget_FSM_Component, Strings.ActionEditor_EditFsmEventTarget_FSM_Component_Tooltip), (UnityEngine.Object) fsmEventTarget.fsmComponent, typeof (PlayMakerFSM), !FsmEditor.SelectedFsmIsPersistent());
          FsmEditorGUILayout.ReadonlyTextField((UnityEngine.Object) fsmEventTarget.fsmComponent == (UnityEngine.Object) null ? "" : fsmEventTarget.fsmComponent.Fsm.Name);
          break;
        case FsmEventTarget.EventTarget.BroadcastAll:
          if (FsmEditorSettings.ShowHints)
            GUILayout.Box(Strings.Hint_Broadcast_Event, FsmEditorStyles.HintBox);
          ActionEditor.editingField = ActionEditor.FsmEventTargetExcludeSelfField;
          fsmEventTarget.excludeSelf = VariableEditor.FsmBoolField(new GUIContent(Strings.ActionEditor_EditFsmEventTarget_Exclude_Self), FsmEditor.SelectedFsm, fsmEventTarget.excludeSelf);
          break;
        case FsmEventTarget.EventTarget.HostFSM:
          if (FsmEditorSettings.ShowHints)
          {
            GUILayout.Box(Strings.Hint_Send_Event_To_Host, FsmEditorStyles.HintBox);
            break;
          }
          break;
        case FsmEventTarget.EventTarget.SubFSMs:
          if (FsmEditorSettings.ShowHints)
          {
            GUILayout.Box(Strings.Hint_Send_Event_To_SubFSMs, FsmEditorStyles.HintBox);
            break;
          }
          break;
      }
      return fsmEventTarget;
    }

    private static string EditStringValue(GUIContent label, object fieldValue, object[] attributes)
    {
      string str1 = (string) fieldValue ?? string.Empty;
      switch (CustomAttributeHelpers.GetUIHint(attributes))
      {
        case UIHint.TextArea:
          GUILayout.Label(label);
          return EditorGUILayout.TextArea(str1, FsmEditorStyles.TextArea, GUILayout.MinHeight(44f));
        case UIHint.Behaviour:
          return ActionEditor.EditComponentName(label, str1, typeof (Behaviour));
        case UIHint.Script:
          string behaviourName = ActionEditor.EditComponentName(label, str1, typeof (MonoBehaviour));
          ActionEditor.TrySetBehaviourContext(behaviourName);
          return behaviourName;
        case UIHint.Method:
          return ActionEditor.EditMethodName(label, str1, false);
        case UIHint.Animation:
          GUILayout.BeginHorizontal();
          string str2 = EditorGUILayout.TextField(label, str1);
          StringEditor.AnimationNamePopup(ActionEditor.GameObjectContext, (FsmString) null, (object) ActionEditor.editingAction, ActionEditor.editingField);
          GUILayout.EndHorizontal();
          return str2;
        case UIHint.Tag:
          return EditorGUILayout.TagField(label, str1);
        case UIHint.Description:
          GUILayout.Box(str1, FsmEditorStyles.InfoBox);
          return str1;
        case UIHint.ScriptComponent:
          return ActionEditor.EditScriptName(label, str1);
        case UIHint.Comment:
          return EditorGUILayout.TextArea(str1, FsmEditorStyles.TextArea, GUILayout.ExpandHeight(!ActionEditor.PreviewMode));
        case UIHint.AnimatorFloat:
          GUILayout.BeginHorizontal();
          string str3 = EditorGUILayout.TextField(label, str1);
          StringEditor.AnimatorFloatPopup(ActionEditor.GameObjectContext, (FsmString) null, (object) ActionEditor.editingAction, ActionEditor.editingField);
          GUILayout.EndHorizontal();
          return str3;
        case UIHint.AnimatorBool:
          GUILayout.BeginHorizontal();
          string str4 = EditorGUILayout.TextField(label, str1);
          StringEditor.AnimatorBoolPopup(ActionEditor.GameObjectContext, (FsmString) null, (object) ActionEditor.editingAction, ActionEditor.editingField);
          GUILayout.EndHorizontal();
          return str4;
        case UIHint.AnimatorInt:
          GUILayout.BeginHorizontal();
          string str5 = EditorGUILayout.TextField(label, str1);
          StringEditor.AnimatorIntPopup(ActionEditor.GameObjectContext, (FsmString) null, (object) ActionEditor.editingAction, ActionEditor.editingField);
          GUILayout.EndHorizontal();
          return str5;
        case UIHint.AnimatorTrigger:
          GUILayout.BeginHorizontal();
          string str6 = EditorGUILayout.TextField(label, str1);
          StringEditor.AnimatorTriggerPopup(ActionEditor.GameObjectContext, (FsmString) null, (object) ActionEditor.editingAction, ActionEditor.editingField);
          GUILayout.EndHorizontal();
          return str6;
        case UIHint.TagMenu:
          return ActionEditor.EditTag(label, str1);
        default:
          return EditorGUILayout.TextField(label, str1);
      }
    }

    public static string EditTag(GUIContent label, string tag)
    {
      List<string> stringList = new List<string>()
      {
        "<Any Tag>"
      };
      stringList.AddRange((IEnumerable<string>) InternalEditorUtility.tags);
      int selectedIndex = 0;
      if (tag != "")
      {
        for (int index = 1; index < stringList.Count; ++index)
        {
          if (!(stringList[index] != tag))
          {
            selectedIndex = index;
            break;
          }
        }
      }
      int index1 = EditorGUILayout.Popup(label.text, selectedIndex, stringList.ToArray());
      tag = index1 == 0 ? "" : stringList[index1];
      return tag;
    }

    public static string EditScriptName(GUIContent label, string name)
    {
      GUILayout.BeginHorizontal();
      name = EditorGUILayout.TextField(label, name);
      bool changed = GUI.changed;
      GUI.changed = false;
      string str = FsmEditorGUILayout.ScriptListPopup();
      if (GUI.changed)
        name = str;
      else
        GUI.changed = changed;
      GUILayout.EndHorizontal();
      return name;
    }

    public static string EditComponentName(GUIContent label, string name, System.Type componentType)
    {
      GUILayout.BeginHorizontal();
      name = EditorGUILayout.TextField(label, name);
      bool changed = GUI.changed;
      GUI.changed = false;
      string str = FsmEditorGUILayout.ComponentNamePopup(ActionEditor.GameObjectContext, componentType);
      if (GUI.changed)
        name = str;
      else
        GUI.changed = changed;
      GUILayout.EndHorizontal();
      return name;
    }

    [Obsolete]
    public static string EditFsmName(GUIContent label, string name) => name;

    public static string EditFsmEvent(GUIContent label, string name)
    {
      GUILayout.BeginHorizontal();
      name = EditorGUILayout.TextField(label, name);
      bool changed = GUI.changed;
      GUI.changed = false;
      string str = FsmEditorGUILayout.FsmEventPopup(ActionEditor.GameObjectContext, ActionEditor.fsmNameContext);
      if (GUI.changed)
        name = str;
      else
        GUI.changed = changed;
      GUILayout.EndHorizontal();
      return name;
    }

    public static void AnimatorFloatPopup(FsmString fsmString) => StringEditor.AnimatorFloatPopup(ActionEditor.GameObjectContext, fsmString);

    public static void AnimatorIntPopup(FsmString fsmString) => StringEditor.AnimatorIntPopup(ActionEditor.GameObjectContext, fsmString);

    public static void AnimatorBoolPopup(FsmString fsmString) => StringEditor.AnimatorBoolPopup(ActionEditor.GameObjectContext, fsmString);

    public static void AnimatorTriggerPopup(FsmString fsmString) => StringEditor.AnimatorTriggerPopup(ActionEditor.GameObjectContext, fsmString);

    public static void AnimationNamePopup(FsmString fsmString) => StringEditor.AnimationNamePopup(ActionEditor.GameObjectContext, fsmString);

    public static void FsmNamePopup(FsmString fsmString)
    {
      ActionEditor.fsmNameContext = fsmString.Value;
      StringEditor.FsmNamePopup(ActionEditor.GameObjectContext, fsmString);
    }

    public static void VariablePopup(FsmString fsmString, UIHint hint) => StringEditor.VariablesPopup(ActionEditor.GameObjectContext, ActionEditor.fsmNameContext, hint, fsmString);

    [Obsolete]
    public static string EditFsmVariableName(GUIContent label, string name, UIHint hint) => name;

    public static string EditMethodName(GUIContent label, string name, bool coroutinesOnly)
    {
      GUILayout.BeginHorizontal();
      name = EditorGUILayout.TextField(label, name);
      bool changed = GUI.changed;
      GUI.changed = false;
      string str = FsmEditorGUILayout.MethodNamePopup(ActionEditor.GameObjectContext, ActionEditor.behaviourContext, 1, coroutinesOnly);
      if (GUI.changed)
        name = str;
      else
        GUI.changed = changed;
      GUILayout.EndHorizontal();
      return name;
    }

    private static float EditFloatValue(
      GUIContent label,
      object fieldValue,
      IEnumerable<object> attributes)
    {
      HasFloatSliderAttribute attribute = CustomAttributeHelpers.GetAttribute<HasFloatSliderAttribute>(attributes);
      return attribute == null ? EditorGUILayout.FloatField(label, Convert.ToSingle(fieldValue)) : FsmEditorGUILayout.FloatSlider(label, (float) fieldValue, attribute.MinValue, attribute.MaxValue);
    }

    private static int EditIntValue(GUIContent label, object fieldValue, object[] attributes)
    {
      HasIntSliderAttribute attribute = CustomAttributeHelpers.GetAttribute<HasIntSliderAttribute>((IEnumerable<object>) attributes);
      if (attribute != null)
        return FsmEditorGUILayout.IntSlider(label, (int) fieldValue, attribute.MinValue, attribute.MaxValue);
      switch (CustomAttributeHelpers.GetUIHint(attributes))
      {
        case UIHint.Layer:
          return EditorGUILayout.LayerField(label, Convert.ToInt32(fieldValue, (IFormatProvider) CultureInfo.InvariantCulture));
        case UIHint.LayerMask:
          return GUIHelpers.LayerMaskField(label, Convert.ToInt32(fieldValue, (IFormatProvider) CultureInfo.InvariantCulture));
        default:
          return EditorGUILayout.IntField(label, Convert.ToInt32(fieldValue, (IFormatProvider) CultureInfo.CurrentCulture));
      }
    }

    public static FsmFloat EditFsmFloat(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmFloat fsmFloat = (FsmFloat) fieldValue ?? new FsmFloat(string.Empty);
      if (CustomAttributeHelpers.GetUIHint(attributes) == UIHint.Variable)
        return VariableEditor.FsmFloatPopup(label, FsmEditor.SelectedFsm, fsmFloat);
      HasFloatSliderAttribute attribute = CustomAttributeHelpers.GetAttribute<HasFloatSliderAttribute>((IEnumerable<object>) attributes);
      return attribute == null ? VariableEditor.FsmFloatField(label, FsmEditor.SelectedFsm, fsmFloat) : VariableEditor.FsmFloatSlider(label, FsmEditor.SelectedFsm, fsmFloat, attribute.MinValue, attribute.MaxValue);
    }

    private static FsmInt EditFsmInt(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmInt fsmInt = (FsmInt) fieldValue ?? new FsmInt("");
      if (CustomAttributeHelpers.GetUIHint(attributes) == UIHint.Variable)
        return VariableEditor.FsmIntPopup(label, FsmEditor.SelectedFsm, fsmInt);
      HasIntSliderAttribute attribute = CustomAttributeHelpers.GetAttribute<HasIntSliderAttribute>((IEnumerable<object>) attributes);
      return attribute == null ? VariableEditor.FsmIntField(label, FsmEditor.SelectedFsm, fsmInt, attributes) : VariableEditor.FsmIntSlider(label, FsmEditor.SelectedFsm, fsmInt, attribute.MinValue, attribute.MaxValue);
    }

    private static FsmBool EditFsmBool(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmBool fsmBool = (FsmBool) fieldValue ?? new FsmBool("");
      return CustomAttributeHelpers.GetUIHint(attributes) != UIHint.Variable ? VariableEditor.FsmBoolField(label, FsmEditor.SelectedFsm, fsmBool) : VariableEditor.FsmBoolPopup(label, FsmEditor.SelectedFsm, fsmBool);
    }

    private static FsmString EditFsmString(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmString fsmString = (FsmString) fieldValue ?? new FsmString();
      return CustomAttributeHelpers.GetUIHint(attributes) != UIHint.Variable ? VariableEditor.FsmStringField(label, FsmEditor.SelectedFsm, fsmString, attributes) : VariableEditor.FsmStringPopup(label, FsmEditor.SelectedFsm, fsmString);
    }

    private static FsmVector2 EditFsmVector2(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmVector2 fsmVector2 = (FsmVector2) fieldValue ?? new FsmVector2();
      return CustomAttributeHelpers.GetUIHint(attributes) != UIHint.Variable ? VariableEditor.FsmVector2Field(label, FsmEditor.SelectedFsm, fsmVector2) : VariableEditor.FsmVector2Popup(label, FsmEditor.SelectedFsm, fsmVector2);
    }

    private static FsmVector3 EditFsmVector3(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmVector3 fsmVector3 = (FsmVector3) fieldValue ?? new FsmVector3();
      return CustomAttributeHelpers.GetUIHint(attributes) != UIHint.Variable ? VariableEditor.FsmVector3Field(label, FsmEditor.SelectedFsm, fsmVector3) : VariableEditor.FsmVector3Popup(label, FsmEditor.SelectedFsm, fsmVector3);
    }

    private static FsmRect EditFsmRect(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmRect fsmRect = (FsmRect) fieldValue ?? new FsmRect();
      return CustomAttributeHelpers.GetUIHint(attributes) != UIHint.Variable ? VariableEditor.FsmRectField(label, FsmEditor.SelectedFsm, fsmRect) : VariableEditor.FsmRectPopup(label, FsmEditor.SelectedFsm, fsmRect);
    }

    private static FsmQuaternion EditFsmQuaternion(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmQuaternion fsmQauternion = (FsmQuaternion) fieldValue ?? new FsmQuaternion();
      return CustomAttributeHelpers.GetUIHint(attributes) != UIHint.Variable ? VariableEditor.FsmQuaternionField(label, FsmEditor.SelectedFsm, fsmQauternion) : VariableEditor.FsmQuaternionPopup(label, FsmEditor.SelectedFsm, fsmQauternion);
    }

    private static FsmObject EditFsmObject(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmObject fsmObject = (FsmObject) fieldValue ?? new FsmObject();
      return CustomAttributeHelpers.GetUIHint(attributes) != UIHint.Variable ? VariableEditor.FsmObjectField(label, FsmEditor.SelectedFsm, fsmObject, CustomAttributeHelpers.GetObjectType(attributes, fsmObject.ObjectType), attributes) : VariableEditor.FsmObjectPopup(label, FsmEditor.SelectedFsm, fsmObject, CustomAttributeHelpers.GetObjectType(attributes));
    }

    private static FsmMaterial EditFsmMaterial(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmMaterial fsmObject = (FsmMaterial) fieldValue ?? new FsmMaterial();
      return CustomAttributeHelpers.GetUIHint(attributes) != UIHint.Variable ? VariableEditor.FsmMaterialField(label, FsmEditor.SelectedFsm, fsmObject) : VariableEditor.FsmMaterialPopup(label, FsmEditor.SelectedFsm, fsmObject);
    }

    private static FsmTexture EditFsmTexture(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmTexture fsmObject = (FsmTexture) fieldValue ?? new FsmTexture();
      return CustomAttributeHelpers.GetUIHint(attributes) != UIHint.Variable ? VariableEditor.FsmTextureField(label, FsmEditor.SelectedFsm, fsmObject) : VariableEditor.FsmTexturePopup(label, FsmEditor.SelectedFsm, fsmObject);
    }

    private static FsmColor EditFsmColor(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmColor fsmColor = (FsmColor) fieldValue ?? new FsmColor();
      return CustomAttributeHelpers.GetUIHint(attributes) != UIHint.Variable ? VariableEditor.FsmColorField(label, FsmEditor.SelectedFsm, fsmColor) : VariableEditor.FsmColorPopup(label, FsmEditor.SelectedFsm, fsmColor);
    }

    private static FsmGameObject EditFsmGameObject(
      GUIContent label,
      object fieldValue,
      object[] attributes)
    {
      FsmGameObject fsmGameObject1 = (FsmGameObject) fieldValue ?? new FsmGameObject(string.Empty);
      if (CustomAttributeHelpers.GetUIHint(attributes) == UIHint.Variable)
        return VariableEditor.FsmGameObjectPopup(label, FsmEditor.SelectedFsm, fsmGameObject1);
      FsmGameObject fsmGameObject2 = VariableEditor.FsmGameObjectField(label, FsmEditor.SelectedFsm, fsmGameObject1);
      if ((UnityEngine.Object) fsmGameObject2.Value != (UnityEngine.Object) null)
        ActionEditor.GameObjectContext = fsmGameObject2.Value;
      return fsmGameObject2;
    }

    private FsmEnum EditFsmEnum(GUIContent label, object fieldValue, object[] attributes)
    {
      FsmEnum fsmEnum = (FsmEnum) fieldValue ?? new FsmEnum();
      System.Type objectType = (System.Type) null;
      ObjectTypeAttribute attribute1 = CustomAttributeHelpers.GetAttribute<ObjectTypeAttribute>((IEnumerable<object>) attributes);
      if (attribute1 != null)
      {
        objectType = attribute1.ObjectType;
        fsmEnum.EnumType = objectType;
      }
      MatchFieldTypeAttribute attribute2 = CustomAttributeHelpers.GetAttribute<MatchFieldTypeAttribute>((IEnumerable<object>) attributes);
      if (attribute2 != null)
      {
        this.MatchFieldType(fsmEnum, attribute2.FieldName);
        objectType = fsmEnum.EnumType;
      }
      if (objectType == null && !CustomAttributeHelpers.HasAttribute<HideTypeFilter>((IEnumerable<object>) attributes))
        VariableEditor.EnumTypeSelector(fsmEnum);
      return CustomAttributeHelpers.GetUIHint(attributes) == UIHint.Variable ? VariableEditor.FsmEnumPopup(label, FsmEditor.SelectedFsm, fsmEnum, objectType) : VariableEditor.FsmEnumField(label, FsmEditor.SelectedFsm, fsmEnum, fsmEnum.EnumType);
    }

    private FsmArray EditFsmArray(GUIContent label, object fieldValue, object[] attributes)
    {
      FsmArray fsmArray1 = (FsmArray) fieldValue ?? new FsmArray();
      ActionEditor.editingFsmArray = fsmArray1;
      int num = CustomAttributeHelpers.GetUIHint(attributes) == UIHint.Variable ? 1 : 0;
      ArrayEditorAttribute attribute = CustomAttributeHelpers.GetAttribute<ArrayEditorAttribute>((IEnumerable<object>) attributes);
      VariableType variableType;
      if (attribute != null)
      {
        variableType = attribute.VariableType;
        if (attribute.ObjectType != null)
          fsmArray1.ObjectType = attribute.ObjectType;
      }
      else
        variableType = fsmArray1.ElementType;
      if (num != 0)
      {
        ActionEditor.editingTypeConstraint = variableType;
        return VariableEditor.FsmArrayPopup(label, FsmEditor.SelectedFsm, fsmArray1, variableType);
      }
      if (variableType == VariableType.Unknown && fsmArray1.ElementType == VariableType.Unknown)
        throw new ArrayTypeMismatchException(Strings.EditFsmArray_Unknown_FsmArray_Type);
      if (attribute == null)
        variableType = fsmArray1.ElementType;
      FsmArray fsmArray2 = VariableEditor.FsmArrayField(label, FsmEditor.SelectedFsm, fsmArray1, variableType);
      ActionEditor.editingTypeConstraint = variableType;
      if (!fsmArray2.UseVariable)
      {
        System.Type elementType = FsmUtility.GetVariableRealType(variableType);
        if (elementType == typeof (UnityEngine.Object))
          elementType = fsmArray2.ObjectType;
        fsmArray2.Values = (object[]) this.EditArray(new GUIContent(Strings.Label_Size), elementType, (Array) fsmArray2.Values, attributes);
        if (this.actionIsDirty)
          fsmArray2.SaveChanges();
      }
      ActionEditor.editingFsmArray = (FsmArray) null;
      return fsmArray2;
    }

    [Localizable(false)]
    private static FsmOwnerDefault EditFsmOwnerDefault(
      GUIContent label,
      object fieldValue)
    {
      FsmOwnerDefault fsmOwnerDefault = (FsmOwnerDefault) fieldValue ?? new FsmOwnerDefault();
      OwnerDefaultOption ownerDefaultOption = (OwnerDefaultOption) EditorGUILayout.EnumPopup(label, (Enum) fsmOwnerDefault.OwnerOption);
      if (ownerDefaultOption != fsmOwnerDefault.OwnerOption)
      {
        FsmEditor.RecordUndo("Select Variable");
        fsmOwnerDefault.OwnerOption = ownerDefaultOption;
        if (fsmOwnerDefault.OwnerOption == OwnerDefaultOption.UseOwner)
          fsmOwnerDefault.GameObject = new FsmGameObject();
      }
      if (fsmOwnerDefault.OwnerOption == OwnerDefaultOption.SpecifyGameObject)
      {
        ActionEditor.editingObject = (object) fsmOwnerDefault;
        ActionEditor.editingField = ActionEditor.FsmOwnerDefaultGameObjectField;
        fsmOwnerDefault.GameObject = VariableEditor.FsmGameObjectField(new GUIContent("  "), FsmEditor.SelectedFsm, fsmOwnerDefault.GameObject);
        if ((UnityEngine.Object) fsmOwnerDefault.GameObject.Value != (UnityEngine.Object) null)
          ActionEditor.GameObjectContext = fsmOwnerDefault.GameObject.Value;
      }
      else
      {
        if (fsmOwnerDefault.GameObject.UseVariable || (UnityEngine.Object) fsmOwnerDefault.GameObject.Value != (UnityEngine.Object) null)
          fsmOwnerDefault.GameObject = new FsmGameObject();
        if (FsmEditor.SelectedFsm != null)
          ActionEditor.GameObjectContext = FsmEditor.SelectedFsm.GameObject;
      }
      return fsmOwnerDefault;
    }

    public static void TrySetBehaviourContext(string behaviourName)
    {
      if ((UnityEngine.Object) ActionEditor.GameObjectContext == (UnityEngine.Object) null || string.IsNullOrEmpty(behaviourName))
        return;
      System.Type globalType = ReflectionUtils.GetGlobalType(behaviourName);
      if (globalType == null)
        return;
      ActionEditor.behaviourContext = (MonoBehaviour) ActionEditor.GameObjectContext.GetComponent(globalType);
    }

    private static bool CanConvert(NamedVariable from, NamedVariable to)
    {
      if (from == null || to == null)
        return false;
      VariableType variableType1 = from.VariableType;
      VariableType variableType2 = to.VariableType;
      if (variableType1 == variableType2)
        return false;
      if (variableType2 == VariableType.String)
        return true;
      switch (variableType1)
      {
        case VariableType.Unknown:
        case VariableType.Vector3:
        case VariableType.Color:
        case VariableType.Rect:
        case VariableType.Quaternion:
        case VariableType.Array:
          return false;
        case VariableType.Float:
          if (variableType2 == VariableType.Int)
            return true;
          goto case VariableType.Unknown;
        case VariableType.Int:
          if (variableType2 == VariableType.Float || variableType2 == VariableType.Bool)
            return true;
          goto case VariableType.Unknown;
        case VariableType.Bool:
          if (variableType2 == VariableType.Int)
            return true;
          goto case VariableType.Unknown;
        case VariableType.GameObject:
          if (variableType2 == VariableType.Object && to.ObjectType == typeof (GameObject))
            return true;
          goto case VariableType.Unknown;
        case VariableType.String:
          if (variableType2 == VariableType.Int || variableType2 == VariableType.Float)
            return true;
          goto case VariableType.Unknown;
        case VariableType.Vector2:
          if (variableType2 == VariableType.Vector3)
            return true;
          goto case VariableType.Unknown;
        case VariableType.Material:
          if (variableType2 == VariableType.Object && to.ObjectType == typeof (Material))
            return true;
          goto case VariableType.Unknown;
        case VariableType.Texture:
          if (variableType2 == VariableType.Object && to.ObjectType == typeof (Texture))
            return true;
          goto case VariableType.Unknown;
        case VariableType.Object:
          if (variableType2 == VariableType.GameObject && from.ObjectType == typeof (GameObject) || variableType2 == VariableType.Material && from.ObjectType == typeof (Material) || variableType2 == VariableType.Texture && from.ObjectType == typeof (Texture))
            return true;
          goto case VariableType.Unknown;
        case VariableType.Enum:
          if (variableType2 == VariableType.Int)
            return true;
          goto case VariableType.Unknown;
        default:
          throw new ArgumentOutOfRangeException();
      }
    }

    [Localizable(false)]
    public static void DoVariableSelector(
      GUIContent label,
      Fsm fsm,
      VariableType variableType,
      NamedVariable selected,
      VariableType typeConstraint = VariableType.Unknown,
      System.Type objectConstraint = null)
    {
      ActionEditor.editingVariable = selected;
      ActionEditor.editingVariableType = variableType;
      ActionEditor.editingTypeConstraint = typeConstraint;
      ActionEditor.editingType = objectConstraint;
      if (selected != null && variableType != VariableType.Unknown && (selected.VariableType != variableType || !selected.TestTypeConstraint(typeConstraint, objectConstraint)))
      {
        if (selected.UsesVariable)
        {
          ActionEditor.editingField.SetValue(ActionEditor.editingObject, (object) selected.ResetVariableReference());
          ActionEditor.exitGUI = true;
          return;
        }
        selected = (NamedVariable) null;
      }
      string text = selected != null ? selected.GetDisplayName() : Strings.Label_None;
      FsmEditorGUILayout.PrefixLabel(label);
      Rect rect = GUILayoutUtility.GetRect(label, EditorStyles.popup);
      bool changed = GUI.changed;
      if (!GUI.Button(rect, text, EditorStyles.popup))
        return;
      if (!changed)
        GUI.changed = false;
      if (Event.current.button == 1)
      {
        ActionEditor.DoVariableButtonContextMenu(rect, selected);
      }
      else
      {
        ActionEditor.SaveEditingContext();
        if (Event.current.control)
        {
          if (!ActionEditor.CanAddNewVariable(variableType, typeConstraint, objectConstraint))
            return;
          if (ActionEditor.editingObject is FsmProperty editingObject)
            ActionEditor.DoNewVariableDropdown(rect, Labels.EnforceNamingConvention(editingObject.PropertyName));
          else
            ActionEditor.DoNewVariableDropdown(rect, Labels.EnforceNamingConvention(label.text));
        }
        else
        {
          GenericMenu genericMenu = new GenericMenu();
          genericMenu.AddItem(new GUIContent(Strings.Label_None), selected == null || selected.IsNone, new GenericMenu.MenuFunction2(ActionEditor.DoVariableSelection), (object) null);
          bool flag = CustomAttributeHelpers.HasUIHint(ActionEditor.editingField, UIHint.Variable) && !CustomAttributeHelpers.HasAttribute<ReadonlyAttribute>(ActionEditor.editingField);
          if (fsm != null)
          {
            foreach (NamedVariable namedVariable in fsm.Variables.GetNamedVariablesSorted(variableType))
            {
              if (namedVariable.TestTypeConstraint(typeConstraint, objectConstraint))
                genericMenu.AddItem(Menus.FormatItem(namedVariable.Name), selected == namedVariable, new GenericMenu.MenuFunction2(ActionEditor.DoVariableSelection), (object) namedVariable);
            }
            if (!flag)
            {
              foreach (NamedVariable from in fsm.Variables.GetAllNamedVariablesSorted())
              {
                if (ActionEditor.CanConvert(from, ActionEditor.editingVariable))
                  genericMenu.AddItem(Menus.FormatItem("Convert/" + from.Name), selected != null && selected.Name == from.Name, new GenericMenu.MenuFunction2(ActionEditor.DoCastVariableSelection), (object) from);
              }
            }
          }
          foreach (NamedVariable namedVariable in FsmVariables.GlobalVariables.GetNamedVariablesSorted(variableType))
          {
            if (namedVariable.TestTypeConstraint(typeConstraint, objectConstraint))
              genericMenu.AddItem(Menus.FormatItem(Strings.Menu_GlobalsRoot + namedVariable.Name), selected == namedVariable, new GenericMenu.MenuFunction2(ActionEditor.DoVariableSelection), (object) namedVariable);
          }
          if (!flag)
          {
            foreach (NamedVariable from in FsmVariables.GlobalVariables.GetAllNamedVariablesSorted())
            {
              if (ActionEditor.CanConvert(from, ActionEditor.editingVariable))
                genericMenu.AddItem(Menus.FormatItem("Globals/Convert/" + from.Name), selected != null && selected.Name == from.Name, new GenericMenu.MenuFunction2(ActionEditor.DoCastVariableSelection), (object) from);
            }
          }
          if (ActionEditor.CanAddNewVariable(ActionEditor.contextVariableType, typeConstraint, objectConstraint))
          {
            genericMenu.AddSeparator("");
            genericMenu.AddItem(new GUIContent(Strings.Menu_NewVariable), false, new GenericMenu.MenuFunction2(ActionEditor.DoNewVariableDropdown), (object) rect);
            genericMenu.AddItem(new GUIContent(Strings.Menu_NewGlobalVariable), false, new GenericMenu.MenuFunction2(ActionEditor.DoNewGlobalVariableDropdown), (object) rect);
          }
          genericMenu.DropDown(rect);
        }
      }
    }

    private static void DoVariableButtonContextMenu(Rect area, NamedVariable variable)
    {
      FsmSearch.Update(FsmEditor.SelectedFsm);
      GenericMenu menu = new GenericMenu();
      if (variable.IsNone)
        menu.AddDisabledItem(new GUIContent(Strings.Label_Edit_Variable));
      else if (FsmVariables.GlobalVariables.Contains(variable))
      {
        menu.AddItem(new GUIContent(Strings.Command_Edit_Global_Variable), false, new GenericMenu.MenuFunction2(ActionEditor.EditVariable), (object) variable);
      }
      else
      {
        menu.AddItem(new GUIContent(Strings.Label_Edit_Variable), false, new GenericMenu.MenuFunction2(ActionEditor.EditVariable), (object) variable);
        menu = Menus.AddVariableUsedByItems(menu, variable, Strings.Label_Used_By + "/");
      }
      menu.DropDown(area);
    }

    private static void EditVariable(object userdata)
    {
      if (!(userdata is NamedVariable variable))
        return;
      if (FsmVariables.GlobalVariables.Contains(variable))
      {
        FsmEditor.OpenGlobalVariablesWindow();
        GlobalVariablesWindow.SelectAfterOpening(variable);
      }
      else
      {
        VariableManager.SelectAfterOpening(variable);
        FsmEditor.Inspector.SetMode(InspectorMode.VariableManager);
      }
    }

    private static bool CanAddNewVariable(
      VariableType variableType,
      VariableType typeConstraint,
      System.Type objectConstraint)
    {
      switch (variableType)
      {
        case VariableType.Unknown:
          return false;
        case VariableType.Object:
        case VariableType.Enum:
          if (objectConstraint == typeof (MonoBehaviour) || objectConstraint == typeof (UnityEngine.Component) || (objectConstraint == typeof (UnityEngine.Object) || objectConstraint == typeof (Enum)) || objectConstraint != null && objectConstraint.IsAbstract)
            return false;
          break;
        case VariableType.Array:
          return ActionEditor.CanAddNewVariable(typeConstraint, typeConstraint, objectConstraint);
      }
      return true;
    }

    private static void DoNewVariableDropdown(object userdata) => ActionEditor.DoNewVariableDropdown((Rect) userdata, "");

    private static Rect ConvertToWindowPos(Rect localRect)
    {
      localRect.x += FsmEditorSettings.InspectorOnLeft ? FsmEditor.Window.position.x : FsmEditor.Window.position.xMax - FsmEditor.InspectorPanelWidth;
      ref Rect local = ref localRect;
      double y1 = (double) local.y;
      Rect rect = FsmEditor.Window.position;
      double y2 = (double) rect.y;
      rect = StateInspector.ActionsPanelRect;
      double y3 = (double) rect.y;
      double num = y2 + y3 + 3.0 - (double) StateInspector.ScrollPosition.y;
      local.y = (float) (y1 + num);
      return localRect;
    }

    private static void DoNewVariableDropdown(Rect buttonRect, string variableName) => NewVariableWindow.CreateDropdown(Strings.Title_NewVariable, ActionEditor.ConvertToWindowPos(buttonRect), FsmEditor.SelectedFsm.Variables, variableName).EditCommited += new TextField.EditCommitedCallback(ActionEditor.DoNewVariable);

    private static void DoNewVariable(TextField textField)
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Command_Add_Variable);
      FsmEditor.SelectedFsm.AddVariable(ActionEditor.contextVariableType, textField.Text, ActionEditor.contextType, ActionEditor.contextTypeConstraint);
      ActionEditor.DoVariableSelection(FsmEditor.SelectedFsm.Variables.GetVariable(textField.Text), false);
    }

    private static void DoNewGlobalVariableDropdown(object userdata) => NewVariableWindow.CreateDropdown(Strings.Title_NewGlobalVariable, ActionEditor.ConvertToWindowPos((Rect) userdata), FsmVariables.GlobalVariables, "").EditCommited += new TextField.EditCommitedCallback(ActionEditor.DoNewGlobalVariable);

    private static void DoNewGlobalVariable(TextField textField)
    {
      FsmEditor.RecordGlobalsUndo(Strings.Command_Add_Variable);
      FsmVariables.GlobalVariables.AddVariable(ActionEditor.contextVariableType, textField.Text, ActionEditor.contextType, ActionEditor.contextTypeConstraint);
      NamedVariable variable = FsmVariables.GlobalVariables.LoadGlobalVariable(textField.Text);
      GlobalVariablesWindow.ResetView();
      ActionEditor.DoVariableSelection(variable, false);
    }

    private static void DoCastVariableSelection(object userdata)
    {
      if (!(userdata is NamedVariable namedVariable))
        return;
      NamedVariable variableOfSameType = ActionEditor.contextVariable.GetNewVariableOfSameType();
      variableOfSameType.UseVariable = true;
      variableOfSameType.SetName(namedVariable.Name);
      variableOfSameType.CastVariable = namedVariable;
      ActionEditor.DoVariableSelection((object) variableOfSameType);
    }

    private static void DoVariableSelection(object userdata) => ActionEditor.DoVariableSelection(userdata as NamedVariable, true);

    [Localizable(false)]
    private static void DoVariableSelection(NamedVariable variable, bool undo)
    {
      if (undo)
      {
        Undo.IncrementCurrentGroup();
        FsmEditor.RecordUndo("Select Variable");
      }
      if (variable == null)
      {
        variable = ActionEditor.contextVariable.GetNewVariableOfSameType();
        if (variable != null)
          variable.UseVariable = true;
      }
      else if (!EditorApplication.isPlaying)
        variable = variable.Copy();
      switch (ActionEditor.contextMenuObject)
      {
        case Array array:
          array.SetValue((object) variable, ActionEditor.contextMenuIndex);
          break;
        case FsmVar _:
          ((FsmVar) ActionEditor.contextMenuObject).NamedVar = variable;
          break;
        case FsmProperty _:
          FsmProperty contextMenuObject1 = (FsmProperty) ActionEditor.contextMenuObject;
          if (ActionEditor.contextMenuFieldName == "TargetObject")
          {
            contextMenuObject1.TargetObject = variable as FsmObject;
            break;
          }
          if (ActionEditor.contextMenuFieldName == "SetVariable")
          {
            contextMenuObject1.SetVariable(variable);
            if (variable != null)
            {
              variable.ObjectType = contextMenuObject1.PropertyType.IsArray ? contextMenuObject1.PropertyType.GetElementType() : contextMenuObject1.PropertyType;
              break;
            }
            break;
          }
          break;
        case FunctionCall _:
          FunctionCall contextMenuObject2 = (FunctionCall) ActionEditor.contextMenuObject;
          switch (contextMenuObject2.ParameterType)
          {
            case "Array":
              contextMenuObject2.ArrayParameter = variable as FsmArray;
              break;
            case "Color":
              contextMenuObject2.ColorParameter = variable as FsmColor;
              break;
            case "Enum":
              contextMenuObject2.EnumParameter = variable as FsmEnum;
              break;
            case "GameObject":
              contextMenuObject2.GameObjectParameter = variable as FsmGameObject;
              break;
            case "Material":
              contextMenuObject2.MaterialParameter = variable as FsmMaterial;
              break;
            case "Object":
              contextMenuObject2.ObjectParameter = variable as FsmObject;
              break;
            case "Quaternion":
              contextMenuObject2.QuaternionParameter = variable as FsmQuaternion;
              break;
            case "Rect":
              contextMenuObject2.RectParamater = variable as FsmRect;
              break;
            case "Texture":
              contextMenuObject2.TextureParameter = variable as FsmTexture;
              break;
            case "Vector2":
              contextMenuObject2.Vector2Parameter = variable as FsmVector2;
              break;
            case "Vector3":
              contextMenuObject2.Vector3Parameter = variable as FsmVector3;
              break;
            case "bool":
              contextMenuObject2.BoolParameter = variable as FsmBool;
              break;
            case "float":
              contextMenuObject2.FloatParameter = variable as FsmFloat;
              break;
            case "int":
              contextMenuObject2.IntParameter = variable as FsmInt;
              break;
            case "string":
              contextMenuObject2.StringParameter = variable as FsmString;
              break;
          }
          break;
        default:
          ActionEditor.contextMenuField.SetValue(ActionEditor.contextMenuObject, (object) variable);
          break;
      }
      FsmEditor.SaveActions(false);
      FsmEditor.SetFsmDirty();
      ActionEditor.UpdateErrorChecks();
    }

    private static void UpdateErrorChecks()
    {
      if (EditorApplication.isPlayingOrWillChangePlaymode || !FsmEditor.SelectedFsm.HasErrors)
        return;
      ActionReport.Remove(FsmEditor.SelectedFsm);
      FsmEditor.CheckFsmForErrors();
    }

    public static void EventSelector(
      GUIContent label,
      FsmEvent selected,
      FsmEventTarget eventTarget = null)
    {
      if (eventTarget == null)
        eventTarget = ActionEditor.fsmEventTargetContext ?? ActionEditor.FSMEventTargetContextGlobal;
      string text = selected != null ? selected.Name : "";
      GUILayout.BeginHorizontal();
      FsmEditorGUILayout.PrefixLabel(label, EditorStyles.popup);
      Rect rect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.popup);
      if (GUI.Button(rect, text, EditorStyles.popup))
      {
        ActionEditor.SaveEditingContext();
        if (Event.current.control)
          ActionEditor.DoNewEvent((object) rect);
        else
          ActionEditor.GenerateEventMenu(FsmEditor.SelectedFsm, selected, eventTarget, rect).DropDown(rect);
      }
      GUILayout.EndHorizontal();
    }

    private static GenericMenu GenerateEventMenu(
      Fsm fsm,
      FsmEvent selected,
      FsmEventTarget eventTarget,
      Rect buttonRect)
    {
      GenericMenu menu1 = new GenericMenu();
      menu1.AddItem(new GUIContent(Strings.Label_None), selected == null, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection), (object) null);
      if (eventTarget == null)
        eventTarget = FsmEventTarget.TargetSelf;
      switch (eventTarget.target)
      {
        case FsmEventTarget.EventTarget.Self:
          GenericMenu menu2 = ActionEditor.AddFsmEventsMenu(fsm, menu1, selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection));
          menu2.AddSeparator("");
          return ActionEditor.AddNewEvent(ActionEditor.AddCommonEventMenus(menu2, "", selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection)), buttonRect);
        case FsmEventTarget.EventTarget.GameObject:
        case FsmEventTarget.EventTarget.BroadcastAll:
        case FsmEventTarget.EventTarget.SubFSMs:
          GenericMenu menu3 = ActionEditor.AddFsmGlobalEventsMenu(fsm, menu1, selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection));
          menu3.AddSeparator("");
          return ActionEditor.AddNewGlobalEvent(ActionEditor.AddGlobalEventsMenus(menu3, "", selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection)), buttonRect);
        case FsmEventTarget.EventTarget.GameObjectFSM:
          if (fsm != null)
          {
            GenericMenu menu4 = ActionEditor.AddFsmGlobalEventsMenu(ActionEditor.GetTargetFSM((FsmGameObject) fsm.GetOwnerDefaultTarget(eventTarget.gameObject), eventTarget.fsmName), menu1, selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection));
            menu1 = ActionEditor.AddFsmGlobalEventsMenu(fsm, menu4, selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection));
          }
          menu1.AddSeparator("");
          return ActionEditor.AddNewGlobalEvent(ActionEditor.AddGlobalEventsMenus(menu1, "", selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection)), buttonRect);
        case FsmEventTarget.EventTarget.FSMComponent:
          if ((UnityEngine.Object) eventTarget.fsmComponent != (UnityEngine.Object) null)
            menu1 = ActionEditor.AddFsmGlobalEventsMenu(eventTarget.fsmComponent.Fsm, menu1, selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection));
          GenericMenu menu5 = ActionEditor.AddFsmGlobalEventsMenu(fsm, menu1, selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection));
          menu5.AddSeparator("");
          return ActionEditor.AddNewGlobalEvent(ActionEditor.AddGlobalEventsMenus(menu5, "", selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection)), buttonRect);
        case FsmEventTarget.EventTarget.HostFSM:
          menu1.AddSeparator("");
          return ActionEditor.AddNewEvent(ActionEditor.AddCommonEventMenus(ActionEditor.AddFinishedEvent(menu1, selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection)), "", selected, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection)), buttonRect);
        default:
          throw new ArgumentOutOfRangeException();
      }
    }

    [Localizable(false)]
    public static GenericMenu AddFinishedEvent(
      GenericMenu menu,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      menu.AddItem(new GUIContent("FINISHED"), selectedEvent == FsmEvent.Finished, menuFunction, (object) FsmEvent.Finished);
      return menu;
    }

    public static GenericMenu AddEventListMenu(
      GenericMenu menu,
      string menuRoot,
      IEnumerable<FsmEvent> eventList,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      // string str = selectedEvent != null ? selectedEvent.Name : "";
      // if (eventList != null)
      // {
      //   foreach (FsmEvent fsmEvent in eventList)
      //     menu.AddItem(Menus.FormatItem(fsmEvent.Name), str == fsmEvent.Name, menuFunction, (object) fsmEvent);
      // }
      return menu;
    }

    public static GenericMenu AddFsmEventsMenu(
      Fsm fsm,
      GenericMenu menu,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      menu = ActionEditor.AddEventListMenu(menu, "", (IEnumerable<FsmEvent>) new List<FsmEvent>(fsm != null ? (IEnumerable<FsmEvent>) fsm.Events : (IEnumerable<FsmEvent>) new FsmEvent[0]), selectedEvent, menuFunction);
      return menu;
    }

    public static GenericMenu AddCommonEventMenus(
      GenericMenu menu,
      string menuRoot,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      menu = ActionEditor.AddCustomEventsMenu(menu, menuRoot, selectedEvent, menuFunction);
      menu = ActionEditor.AddSystemEventsMenu(menu, menuRoot, selectedEvent, menuFunction);
      return menu;
    }

    public static GenericMenu AddFsmGlobalEventsMenu(
      Fsm fsm,
      GenericMenu menu,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      string str = selectedEvent != null ? selectedEvent.Name : "";
      if (fsm != null)
      {
        foreach (FsmEvent fsmEvent in fsm.Events)
        {
          if (fsmEvent.IsGlobal)
            menu.AddItem(Menus.FormatItem(fsmEvent.Name), str == fsmEvent.Name, new GenericMenu.MenuFunction2(ActionEditor.DoEventSelection), (object) fsmEvent);
        }
      }
      return menu;
    }

    public static GenericMenu AddCustomEventsMenu(
      GenericMenu menu,
      string eventRoot,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      string str1 = selectedEvent != null ? selectedEvent.Name : "";
      string str2 = Menus.MakeMenuRoot(eventRoot + Strings.Menu_GraphView_CustomEvents);
      foreach (FsmEvent fsmEvent in FsmEvent.EventList.OrderBy<FsmEvent, string>((Func<FsmEvent, string>) (o => o.Name)).ToList<FsmEvent>())
      {
        if (!fsmEvent.IsSystemEvent)
          menu.AddItem(Menus.FormatItem(str2 + fsmEvent.Name), str1 == fsmEvent.Name, menuFunction, (object) fsmEvent);
      }
      return menu;
    }

    public static GenericMenu AddGlobalEventsMenus(
      GenericMenu menu,
      string eventRoot,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      string str1 = selectedEvent != null ? selectedEvent.Name : "";
      string str2 = Menus.MakeMenuRoot(eventRoot + Strings.Menu_GraphView_GlobalEvents);
      foreach (FsmEvent fsmEvent in FsmEvent.EventList.OrderBy<FsmEvent, string>((Func<FsmEvent, string>) (o => o.Name)).ToList<FsmEvent>())
      {
        if (fsmEvent.IsGlobal)
          menu.AddItem(Menus.FormatItem(str2 + fsmEvent.Name), str1 == fsmEvent.Name, menuFunction, (object) fsmEvent);
      }
      return menu;
    }

    public static GenericMenu AddSystemEventsMenu(
      GenericMenu menu,
      string eventRoot,
      FsmEvent selectedEvent,
      GenericMenu.MenuFunction2 menuFunction)
    {
      string str = selectedEvent != null ? selectedEvent.Name : "";
      foreach (FsmEvent fsmEvent in FsmEvent.EventList.OrderBy<FsmEvent, string>((Func<FsmEvent, string>) (o => o.Name)).ToList<FsmEvent>())
      {
        if (fsmEvent.IsSystemEvent && fsmEvent != FsmEvent.Finished && (FsmEditorSettings.UseLegacyNetworking || !fsmEvent.IsLegacyNetworkEvent))
          menu.AddItem(new GUIContent(eventRoot + fsmEvent.Path + fsmEvent.Name), str == fsmEvent.Name, menuFunction, (object) fsmEvent);
      }
      return menu;
    }

    public static GenericMenu AddNewEvent(GenericMenu menu, Rect buttonRect)
    {
      menu.AddSeparator("");
      menu.AddItem(new GUIContent(Strings.Menu_NewEvent), false, new GenericMenu.MenuFunction2(ActionEditor.DoNewEvent), (object) buttonRect);
      return menu;
    }

    public static GenericMenu AddNewGlobalEvent(GenericMenu menu, Rect buttonRect)
    {
      menu.AddSeparator("");
      menu.AddItem(new GUIContent(Strings.Menu_NewGlobalEvent), false, new GenericMenu.MenuFunction2(ActionEditor.DoNewGlobalEvent), (object) buttonRect);
      return menu;
    }

    private static void DoNewEvent(object userdata) => ActionEditor.DoNewEvent((Rect) userdata, "");

    private static void DoNewGlobalEvent(object userdata) => ActionEditor.DoNewGlobalEvent((Rect) userdata, "");

    private static void DoNewEvent(Rect buttonRect, string eventName) => NewEventWindow.CreateDropdown(Strings.Title_NewEvent, ActionEditor.ConvertToWindowPos(buttonRect), eventName).EditCommited += new TextField.EditCommitedCallback(ActionEditor.DoNewEvent);

    private static void DoNewGlobalEvent(Rect buttonRect, string eventName) => NewEventWindow.CreateDropdown(Strings.Command_New_Global_Event, ActionEditor.ConvertToWindowPos(buttonRect), eventName).EditCommited += new TextField.EditCommitedCallback(ActionEditor.DoNewGlobalEvent);

    private static void DoNewEvent(TextField textField) => ActionEditor.DoEventSelection((object) FsmEvent.GetFsmEvent(textField.Text));

    private static void DoNewGlobalEvent(TextField textField)
    {
      FsmEvent fsmEvent = FsmEvent.GetFsmEvent(textField.Text);
      fsmEvent.IsGlobal = true;
      EditorCommands.MakeEventGlobal(fsmEvent.Name);
      ActionEditor.DoEventSelection((object) fsmEvent);
    }

    private static void DoEventSelection(object userdata)
    {
      FsmEvent fsmEvent = userdata as FsmEvent;
      if (ActionEditor.contextMenuObject is Array contextMenuObject)
      {
        if (ActionEditor.contextType == typeof (FsmEventMapping))
        {
          if (contextMenuObject.GetValue(ActionEditor.contextMenuIndex) is FsmEventMapping fsmEventMapping)
            contextMenuObject.SetValue((object) new FsmEventMapping(fsmEventMapping.fromEvent, fsmEvent), ActionEditor.contextMenuIndex);
        }
        else
          contextMenuObject.SetValue((object) fsmEvent, ActionEditor.contextMenuIndex);
      }
      else
        ActionEditor.contextMenuField.SetValue(ActionEditor.contextMenuObject, (object) fsmEvent);
      FsmEditor.SelectedFsm.AddEvent(fsmEvent);
      FsmEditor.SaveActions();
      FsmEditor.SetFsmDirty();
    }

    private static Fsm GetTargetFSM(FsmGameObject go, FsmString fsmName)
    {
      if (go.UseVariable || fsmName.UseVariable)
        return (Fsm) null;
      if (string.IsNullOrEmpty(fsmName.Value))
      {
        foreach (Fsm fsm in FsmEditor.FsmList)
        {
          if ((UnityEngine.Object) fsm.GameObject == (UnityEngine.Object) go.Value)
            return fsm;
        }
      }
      else
      {
        foreach (Fsm fsm in FsmEditor.FsmList)
        {
          if ((UnityEngine.Object) fsm.GameObject == (UnityEngine.Object) go.Value && fsmName.Value == fsm.Name)
            return fsm;
        }
      }
      return (Fsm) null;
    }

    private static void SaveEditingContext()
    {
      ActionEditor.contextVariable = ActionEditor.editingVariable;
      ActionEditor.contextMenuObject = ActionEditor.editingObject;
      ActionEditor.contextMenuField = ActionEditor.editingField;
      ActionEditor.contextMenuIndex = ActionEditor.editingIndex;
      ActionEditor.contextMenuFieldName = ActionEditor.editingFieldName;
      ActionEditor.contextVariableType = ActionEditor.editingVariableType;
      ActionEditor.contextFsmArray = ActionEditor.editingFsmArray;
      ActionEditor.contextType = ActionEditor.editingType;
      ActionEditor.contextTypeConstraint = ActionEditor.editingTypeConstraint;
      if (ActionEditor.contextFsmArray != null)
        ActionEditor.contextType = ActionEditor.contextFsmArray.ObjectType;
      if (!(ActionEditor.editingVariable is FsmEnum editingVariable))
        return;
      ActionEditor.contextType = editingVariable.EnumType;
    }

    [Localizable(false)]
    public static void DebugEditingContext()
    {
      Debug.Log((object) "EditingContext");
      Debug.Log((object) ("editingObject: " + ActionEditor.editingObject.GetType().FullName));
      Debug.Log((object) ("fieldInfo: " + ActionEditor.editingField.Name));
      Debug.Log((object) ("fieldName: " + ActionEditor.editingFieldName));
      Debug.Log((object) ("variableType: " + (object) ActionEditor.editingVariableType));
      Debug.Log((object) ("type: " + (object) ActionEditor.contextType));
      Debug.Log((object) ("typeConstraint: " + (object) ActionEditor.editingTypeConstraint));
    }

    private class CachedData
    {
      public readonly string realActionName;
      public readonly string actionNote;
      public readonly List<CompoundArrayAttribute> compoundArrayList = new List<CompoundArrayAttribute>();
      public readonly List<FieldInfo> compoundArrayParent = new List<FieldInfo>();
      public readonly List<FieldInfo> compoundArrayChild = new List<FieldInfo>();
      private readonly bool openParent;
      public readonly FieldInfo[] fields;
      private readonly Dictionary<FieldInfo, Func<object, bool>> hideIfTests = new Dictionary<FieldInfo, Func<object, bool>>();
      private readonly Dictionary<FieldInfo, Action<object, object>> previewMethod = new Dictionary<FieldInfo, Action<object, object>>();
      private readonly bool hasDynamicUI;

      [Localizable(false)]
      public CachedData(FsmStateAction action)
      {
        System.Type type = action.GetType();
        this.realActionName = Labels.GetActionLabel(action);
        this.actionNote = CustomAttributeHelpers.GetNote((IEnumerable<object>) CustomAttributeHelpers.GetCustomAttributes(type));
        this.fields = ActionData.GetFields(type);
        for (int oldIndex = 0; oldIndex < this.fields.Length; ++oldIndex)
        {
          FieldInfo field = this.fields[oldIndex];
          object[] customAttributes = CustomAttributeHelpers.GetCustomAttributes(field);
          PreviewFieldAttribute attribute1 = CustomAttributeHelpers.GetAttribute<PreviewFieldAttribute>((IEnumerable<object>) customAttributes);
          if (attribute1 != null)
          {
            MethodInfo method = type.GetMethod(attribute1.MethodName, BindingFlags.Instance | BindingFlags.Public);
            if (method != null && method.ReturnType == typeof (void) && method.GetParameters().Length == 1)
            {
              ParameterExpression parameterExpression = Expression.Parameter(typeof (object), "input");
              ParameterExpression[] parameterExpressionArray1 = new ParameterExpression[1]
              {
                Expression.Parameter(typeof (object), "field")
              };
              ParameterExpression[] parameterExpressionArray2 = new ParameterExpression[2]
              {
                parameterExpression,
                parameterExpressionArray1[0]
              };
              Action<object, object> action1 = Expression.Lambda<Action<object, object>>((Expression) Expression.Call((Expression) Expression.Convert((Expression) parameterExpression, type), method, (Expression[]) parameterExpressionArray1), parameterExpressionArray2).Compile();
              this.previewMethod.Add(field, action1);
              this.hasDynamicUI = true;
            }
            else
              Debug.LogWarning((object) ("Could not find Preview method: " + attribute1.MethodName));
          }
          HideIfAttribute attribute2 = CustomAttributeHelpers.GetAttribute<HideIfAttribute>((IEnumerable<object>) customAttributes);
          if (attribute2 != null)
          {
            MethodInfo method = type.GetMethod(attribute2.Test, BindingFlags.Instance | BindingFlags.Public);
            if (method != null && method.ReturnType == typeof (bool) && method.GetParameters().Length == 0)
            {
              // Func<object, bool> func = ((Expression<Func<object, bool>>) (input => Expression.Call((Expression) Expression.Convert(input, type), method))).Compile();
              Func<object, bool> func = input => (bool)method.Invoke(input, null);
              this.hideIfTests.Add(field, func);
              this.hasDynamicUI = true;
            }
            else
              Debug.LogWarning((object) ("Could not find HideIf method: " + attribute2.Test));
          }
          if (this.openParent)
          {
            this.compoundArrayChild.Add(field);
            this.openParent = false;
          }
          CompoundArrayAttribute attribute3 = CustomAttributeHelpers.GetAttribute<CompoundArrayAttribute>((IEnumerable<object>) customAttributes);
          if (attribute3 != null)
          {
            this.compoundArrayList.Add(attribute3);
            this.compoundArrayParent.Add(field);
            this.openParent = true;
          }
          DisplayOrderAttribute attribute4 = CustomAttributeHelpers.GetAttribute<DisplayOrderAttribute>((IEnumerable<object>) customAttributes);
          if (attribute4 != null)
            this.fields = ArrayUtility.MoveItem<FieldInfo>(this.fields, oldIndex, Mathf.Clamp(attribute4.Index, 0, this.fields.Length - 1));
        }
        for (int index = this.fields.Length - 1; index >= 0; --index)
        {
          if (CustomAttributeHelpers.GetAttribute<HideInInspector>((IEnumerable<object>) CustomAttributeHelpers.GetCustomAttributes(this.fields[index])) != null)
            this.fields = ArrayUtility.RemoveAt<FieldInfo>(this.fields, index);
        }
      }

      public bool IsVisible(FsmStateAction action, FieldInfo field)
      {
        Func<object, bool> func;
        return !this.hasDynamicUI || !this.hideIfTests.TryGetValue(field, out func) || !func((object) action);
      }

      public void DrawPreview(FsmStateAction action, FieldInfo field)
      {
        Action<object, object> action1;
        if (!this.hasDynamicUI || !this.previewMethod.TryGetValue(field, out action1))
          return;
        action1((object) action, field.GetValue((object) action));
      }
    }
  }
}
