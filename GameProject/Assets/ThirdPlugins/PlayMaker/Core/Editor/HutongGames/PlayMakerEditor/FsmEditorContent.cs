// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmEditorContent
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class FsmEditorContent
  {
    private static readonly GUIContent tempContent = new GUIContent();
    private const string copyShortcut = " %c";
    private const string cutShortcut = " %x";
    private const string pasteShortcut = " %v";
    private const string selectAllShortcut = " %a";

    public static GUIContent RefreshVariablesList { get; private set; }

    public static GUIContent RefreshEventList { get; private set; }

    public static GUIContent ActionPreview { get; private set; }

    public static GUIContent DebugActions { get; private set; }

    public static GUIContent HideUnused { get; private set; }

    public static GUIContent RuntimeSection { get; private set; }

    public static GUIContent SettingsSection { get; private set; }
    
    public static GUIContent ConditionsSection { get; private set; }

    public static GUIContent InputsSection { get; private set; }

    public static GUIContent OutputsSection { get; private set; }

    public static GUIContent EventsSection { get; private set; }

    public static GUIContent EditorLogButton { get; private set; }

    public static GUIContent RefreshActionUsage { get; private set; }

    public static GUIContent AddActionToState { get; private set; }

    public static GUIContent AddActionToNewFsm { get; private set; }

    public static GUIContent IsInputLabel { get; private set; }

    public static GUIContent IsOutputLabel { get; private set; }

    public static GUIContent IsOutputEventLabel { get; private set; }

    public static GUIContent SceneGizmoIcon { get; private set; }

    public static GUIContent PrefabWarning { get; private set; }

    public static GUIContent DisconnectedPrefabLabel { get; private set; }

    public static GUIContent ModifiedPrefabLabel { get; private set; }

    public static GUIContent AddedComponentOverrideLabel { get; private set; }

    public static GUIContent FavoritesLabel { get; private set; }

    public static GUIContent ManualUpdate { get; private set; }

    public static GUIContent KeepDelayedEvents { get; private set; }

    public static GUIContent LockFsmButton { get; private set; }

    public static GUIContent MenuGraphViewCopyStates { get; private set; }

    public static GUIContent MenuGraphViewPasteStates { get; private set; }

    public static GUIContent GroupStates { get; private set; }

    public static GUIContent MenuSelectAllActions { get; private set; }

    public static GUIContent MenuCopySelectedActions { get; private set; }

    public static GUIContent MenuPasteActions { get; private set; }

    public static GUIContent EditCategoryLabel { get; private set; }

    public static GUIContent GlobalVariableName { get; set; }

    public static GUIContent GlobalEventName { get; set; }

    public static GUIContent FsmBrowserButton { get; private set; }

    public static GUIContent RecentButton { get; private set; }

    public static GUIContent BackButton { get; private set; }

    public static GUIContent ForwardButton { get; private set; }

    public static GUIContent StateBrowserButton { get; private set; }

    public static GUIContent BrowseButton { get; private set; }

    public static GUIContent HelpButton { get; private set; }

    public static GUIContent DeleteButton { get; private set; }

    public static GUIContent ResetButton { get; private set; }

    public static GUIContent UpButton { get; private set; }

    public static GUIContent DownButton { get; private set; }

    public static GUIContent VariableButton { get; private set; }

    public static GUIContent SettingsButton { get; private set; }

    public static GUIContent PopupButton { get; private set; }

    public static GUIContent Play { get; private set; }

    public static GUIContent Pause { get; private set; }

    public static GUIContent Step { get; private set; }

    public static GUIContent MainToolbarSelectedGO { get; private set; }

    public static GUIContent MainToolbarSelectedFSM { get; private set; }

    public static GUIContent MainToolbarLock { get; private set; }

    public static GUIContent MainToolbarPrefabTypeNone { get; private set; }

    public static GUIContent MainToolbarSelectTemplateUsage { get; private set; }

    public static GUIContent MainToolbarShowMinimap { get; private set; }

    public static GUIContent NewVariableLabel { get; private set; }

    public static GUIContent EditVariableLabel { get; private set; }

    public static GUIContent GlobalVariablesLabel { get; private set; }

    public static GUIContent VariableNameLabel { get; private set; }

    public static GUIContent VariableTypeLabel { get; private set; }

    public static GUIContent VariableValueLabel { get; private set; }

    public static GUIContent GlobalsLabel { get; private set; }

    public static GUIContent VariableUseCountLabel { get; private set; }

    public static GUIContent TooltipLabel { get; private set; }

    public static GUIContent TypeLabel { get; private set; }

    public static GUIContent InspectorLabel { get; private set; }

    public static GUIContent NetworkSyncNotSupportedLabel { get; private set; }

    public static GUIContent EditVariableTypeLabel { get; private set; }

    public static GUIContent AddLabel { get; private set; }

    public static GUIContent GlobalVariablesNameLabel { get; private set; }

    public static GUIContent GlobalVariablesTypeLabel { get; private set; }

    public static GUIContent AddGlobalVariableLabel { get; private set; }

    public static GUIContent GlobalVariableUseCountLabel { get; private set; }

    public static GUIContent SystemEventNameLabel { get; private set; }

    public static GUIContent EditEventNameLabel { get; private set; }

    public static GUIContent AddEventLabel { get; private set; }

    public static GUIContent EventInspectorLabel { get; private set; }

    public static GUIContent EventBrowserButtonLabel { get; private set; }

    public static GUIContent EventBroadcastIcon { get; private set; }

    public static GUIContent EventBroadcastButton { get; private set; }

    public static GUIContent EventSendButton { get; private set; }

    public static GUIContent EventSendGlobalButton { get; private set; }

    public static GUIContent EventHeaderLabel { get; private set; }

    public static GUIContent EventUsedHeaderLabel { get; private set; }

    public static GUIContent GlobalEventTooltipLabel { get; private set; }

    public static GUIContent DebugToolbarErrorCount { get; private set; }

    public static GUIContent DebugToolbarDebug { get; private set; }

    public static GUIContent DebugToolbarPrev { get; private set; }

    public static GUIContent DebugToolbarNext { get; private set; }

    public static GUIContent ErrorIcon { get; private set; }

    public static GUIContent StateTitleBox { get; private set; }

    public static GUIContent StateDescription { get; private set; }

    public static GUIContent FsmDescription { get; private set; }

    public static GUIContent EventLabel { get; private set; }

    public static GUIContent HintGettingStarted { get; private set; }

    public static GUIContent HintGraphShortcuts { get; private set; }

    public static GUIContent HintGraphCommands { get; private set; }

    public static Vector2 HintGettingStartedSize { get; private set; }

    public static Vector2 HintGraphShortcutsSize { get; private set; }

    public static Vector2 HintGraphCommandsSize { get; private set; }

    public static GUIContent BrowseTemplateButton { get; private set; }

    public static GUIContent MaxLoopOverrideLabel { get; private set; }

    public static GUIContent ShowStateLabelsLabel { get; private set; }

    public static GUIContent EnableBreakpointsLabel { get; private set; }

    public static GUIContent EnableDebugFlowLabel { get; private set; }

    public static GUIContent EnableDebugFlowInFsmLabel { get; private set; }

    public static GUIContent ResetOnDisableLabel { get; private set; }

    public static GUIContent ResetVariablesOnDisableLabel { get; private set; }

    public static GUIContent FsmControlsLabel { get; private set; }

    public static GUIContent DebugControlsLabel { get; private set; }

    public static GUIContent FsmInfoLabel { get; private set; }

    public static GUIContent NetworkSyncLabel { get; private set; }

    public static GUIContent UseTemplateLabel { get; private set; }

    public static GUIContent RefreshTemplateLabel { get; private set; }

    public static GUIContent EditFsmButton { get; private set; }

    public static GUIContent ConfirmEditPrefabInstance { get; set; }

    public static GUIContent SortUpButton { get; private set; }

    public static GUIContent SortDownButton { get; private set; }

    public static void Init(bool usingProSkin)
    {
      FsmEditorContent.PrefabWarning = new GUIContent(string.Format("<b>{0}</b>\r\n{1}", (object) Strings.Label_Prefab_Instance, (object) Strings.EditPrefab_Disconnect_Info), (Texture) EditorGUIUtility.FindTexture("Prefab Icon"));
      FsmEditorContent.DisconnectedPrefabLabel = new GUIContent(string.Format("<b>{0}</b>\r\n{1}", (object) Strings.Label_Disconnected_FSM_Instance, (object) Strings.EditPrefab_Appy_or_Revert), (Texture) EditorGUIUtility.FindTexture("Prefab Icon"));
      FsmEditorContent.ModifiedPrefabLabel = new GUIContent(string.Format("<b>{0}</b>\r\n{1}", (object) Strings.Label_Prefab_Instance_Modified, (object) Strings.EditPrefab_Disconnect_Info), (Texture) EditorGUIUtility.FindTexture("Prefab Icon"));
      FsmEditorContent.AddedComponentOverrideLabel = new GUIContent(string.Format("<b>{0}</b>\r\n{1}", (object) "Prefab Override", (object) "This FSM has been added as an override to a Prefab Instance."), (Texture) EditorGUIUtility.FindTexture("Prefab Icon"));
      FsmEditorContent.RefreshVariablesList = new GUIContent("Refresh", "Update Global Variables Used Counts.\n\nNOTE: This only counts variables used in currently loaded FSMs. Variables might also be used in other scenes or prefabs.");
      FsmEditorContent.RefreshEventList = new GUIContent("Refresh", "Refresh Event List");
      FsmEditorContent.ActionPreview = new GUIContent(Strings.ActionSelector_Preview, "Show action preview.");
      FsmEditorContent.DebugActions = new GUIContent(Strings.Label_Debug, Strings.Tooltip_Variables_Debug);
      FsmEditorContent.HideUnused = new GUIContent(Strings.Label_Hide_Unused, Strings.Tooltip_Hide_Unused);
      FsmEditorContent.RuntimeSection = new GUIContent("Runtime");
      FsmEditorContent.SettingsSection = new GUIContent("Settings");
      FsmEditorContent.ConditionsSection = new GUIContent("Conditions");
      FsmEditorContent.InputsSection = new GUIContent("Inputs");
      FsmEditorContent.OutputsSection = new GUIContent("Outputs");
      FsmEditorContent.EventsSection = new GUIContent("Events");
      FsmEditorContent.EditorLogButton = new GUIContent((Texture) FsmEditorStyles.LogIcon, "Open the Editor Log");
      FsmEditorContent.RefreshActionUsage = new GUIContent("Refresh", Strings.Label_Refresh_Action_Usage_Tooltip);
      FsmEditorContent.AddActionToState = new GUIContent(Strings.ActionSelector_Add_Action_To_State, Strings.ActionSelector_Add_Action_To_State_Tooltip);
      FsmEditorContent.AddActionToNewFsm = new GUIContent(Strings.ActionSelector_Add_Action_to_New_FSM, Strings.ActionSelector_Add_Action_to_New_FSM_Tooltip);
      FsmEditorContent.IsInputLabel = new GUIContent(Strings.Label_Input, Strings.Label_Variable_Input_Tooltip);
      FsmEditorContent.IsOutputLabel = new GUIContent(Strings.Label_Output, Strings.Label_Variable_Output_Tooltip);
      FsmEditorContent.IsOutputEventLabel = new GUIContent(Strings.Label_Output, "Expose event in Template Inspectors.\nFor example, in Run FSM Action.");
      FsmEditorContent.SceneGizmoIcon = new GUIContent((Texture) Files.LoadTexture("sceneGizmo", 14, 14));
      FsmEditorContent.FavoritesLabel = new GUIContent(Strings.Label_Favorites, Strings.Tooltip_Favorites);
      FsmEditorContent.ManualUpdate = new GUIContent(Strings.Label_Manual_Update, Strings.Tooltip_ManualUpdate);
      FsmEditorContent.KeepDelayedEvents = new GUIContent(Strings.Label_KeepDelayedEvents, Strings.Tooltip_KeepDelayedEvents);
      FsmEditorContent.LockFsmButton = new GUIContent(Strings.Label_Lock, Strings.Tooltip_Lock_and_password_protect_FSM);
      FsmEditorContent.ConfirmEditPrefabInstance = new GUIContent(Strings.Label_Confirm_Editing_Prefab_Instances, Strings.Tooltip_Disable_editing_of_prefab_intances);
      FsmEditorContent.EditFsmButton = new GUIContent(Strings.Label_Edit, Strings.Tooltip_Edit_in_the_PlayMaker_Editor);
      FsmEditorContent.RefreshTemplateLabel = new GUIContent(Strings.Label_Refresh_Template, Strings.Tooltip_Refresh_Template);
      FsmEditorContent.UseTemplateLabel = new GUIContent(Strings.Label_Use_Template, Strings.Tooltip_Use_Template);
      FsmEditorContent.BrowseTemplateButton = new GUIContent(Strings.Label_Browse, Strings.Tooltip_Browse_Templates);
      FsmEditorContent.MaxLoopOverrideLabel = new GUIContent(Strings.Label_Max_Loop_Override, Strings.Tooltip_Max_Loop_Override);
      FsmEditorContent.ShowStateLabelsLabel = new GUIContent(Strings.Label_Show_State_Labels, Strings.Tooltip_Show_State_Label);
      FsmEditorContent.EnableBreakpointsLabel = new GUIContent(Strings.Label_Enable_Breakpoints, Strings.Tooltip_Enable_Breakpoints);
      FsmEditorContent.EnableDebugFlowLabel = new GUIContent(Strings.FsmEditorSettings_Enable_DebugFlow, Strings.FsmEditorSettings_Enable_DebugFlow_Tooltip);
      FsmEditorContent.EnableDebugFlowInFsmLabel = new GUIContent(Strings.FsmEditorSettings_Enable_DebugFlow, Strings.FsmEditorSettings_Enable_DebugFlowInFSM);
      FsmEditorContent.ResetOnDisableLabel = new GUIContent(Strings.Label_Reset_On_Disable, Strings.Tooltip_Reset_On_Disable);
      FsmEditorContent.ResetVariablesOnDisableLabel = new GUIContent(Strings.Label_Reset_Variables, Strings.Label_Reset_Variables_Tooltip);
      FsmEditorContent.FsmControlsLabel = new GUIContent(Strings.Label_Controls, Strings.Tooltip_Controls);
      FsmEditorContent.DebugControlsLabel = new GUIContent(Strings.Label_Debug, Strings.Label_Debug_Tooltip);
      FsmEditorContent.FsmInfoLabel = new GUIContent(Strings.Label_Info, "Summary info:\nEvents used, templates used...");
      FsmEditorContent.NetworkSyncLabel = new GUIContent(Strings.Label_Network_Sync, Strings.Tooltip_Variables_Set_To_Network_Sync);
      FsmEditorContent.GroupStates = new GUIContent("Group");
      FsmEditorContent.MenuGraphViewCopyStates = new GUIContent(Strings.Menu_GraphView_Copy_States + " %c");
      FsmEditorContent.MenuGraphViewPasteStates = new GUIContent(Strings.Menu_GraphView_Paste_States + " %v");
      FsmEditorContent.MenuSelectAllActions = new GUIContent(Strings.Menu_Select_All_Actions + " %a");
      FsmEditorContent.MenuPasteActions = new GUIContent(Strings.Menu_Paste_Actions + " %v");
      FsmEditorContent.MenuCopySelectedActions = new GUIContent(Strings.Menu_Copy_Selected_Actions + " %c");
      FsmEditorContent.EditCategoryLabel = new GUIContent(Strings.Category, Strings.Category_Tooltip);
      FsmEditorContent.GlobalVariableName = new GUIContent(Strings.Variable, Strings.Variable_Tooltip_Warning);
      FsmEditorContent.GlobalEventName = new GUIContent(Strings.Label_Global_Event, Strings.Event_Tooltip_Warning);
      FsmEditorContent.StateBrowserButton = new GUIContent((Texture) Files.LoadTextureFromDll("browserIcon", 14, 14), Strings.Command_State_Browser);
      FsmEditorContent.HelpButton = new GUIContent(GUIHelpers.GetBuiltinIcon("_Help"), Strings.Tooltip_Doc_Button);
      FsmEditorContent.Play = new GUIContent((Texture) Files.LoadTextureFromDll("playButton", 17, 17));
      FsmEditorContent.Pause = new GUIContent((Texture) Files.LoadTextureFromDll("pauseButton", 17, 17));
      FsmEditorContent.Step = new GUIContent((Texture) Files.LoadTextureFromDll("stepButton", 17, 17));
      FsmEditorContent.NewVariableLabel = new GUIContent(Strings.Label_New_Variable, Strings.Tooltip_New_Variable);
      FsmEditorContent.EditVariableLabel = new GUIContent(Strings.Label_Name);
      FsmEditorContent.GlobalVariablesLabel = new GUIContent(Strings.Label_Global_Variables, Strings.Tooltip_Global_Variables);
      FsmEditorContent.VariableNameLabel = new GUIContent(Strings.Label_Name, Strings.Tooltip_Variable_Name);
      FsmEditorContent.VariableTypeLabel = new GUIContent(Strings.Label_Type, Strings.Tooltip_Variable_Type);
      FsmEditorContent.VariableValueLabel = new GUIContent(Strings.Label_Value);
      FsmEditorContent.GlobalsLabel = new GUIContent(Strings.Label_Globals, Strings.Tooltip_Globals);
      FsmEditorContent.VariableUseCountLabel = new GUIContent(Strings.Label_Used, Strings.Tooltip_Variable_Used_Count);
      FsmEditorContent.TooltipLabel = new GUIContent(Strings.Label_Tooltip, "Tooltip visible when rolling over the variable.");
      FsmEditorContent.TypeLabel = new GUIContent(Strings.Label_Type, Strings.Label_Variable_Type_Tooltip);
      FsmEditorContent.InspectorLabel = new GUIContent(Strings.Label_Inspector, Strings.Tooltip_Inspector);
      FsmEditorContent.NetworkSyncLabel = new GUIContent(Strings.Label_Network_Sync, Strings.Tooltip_Network_Sync);
      FsmEditorContent.NetworkSyncNotSupportedLabel = new GUIContent(Strings.Label_Network_Sync, Strings.Tooltip_Network_Sync_Not_Supported);
      FsmEditorContent.EditVariableTypeLabel = new GUIContent(Strings.Label_Variable_Type, Strings.Tooltip_Edit_Variable_Type);
      FsmEditorContent.AddLabel = new GUIContent(Strings.Label_Add);
      FsmEditorContent.GlobalVariablesNameLabel = new GUIContent(Strings.Label_Name, Strings.Tooltip_Global_Variables_Header);
      FsmEditorContent.GlobalVariablesTypeLabel = new GUIContent(Strings.Label_Type, Strings.Tooltip_Global_Variables_Type);
      FsmEditorContent.AddGlobalVariableLabel = new GUIContent(Strings.Label_New_Variable, Strings.Tooltip_Add_Global_Variable);
      FsmEditorContent.GlobalVariableUseCountLabel = new GUIContent(Strings.Label_Used, Strings.Tooltip_Global_Variables_Used_Count);
      FsmEditorContent.SystemEventNameLabel = new GUIContent(Strings.Label_System_Event, Strings.Label_System_Event_Tooltip);
      FsmEditorContent.EditEventNameLabel = new GUIContent(Strings.Label_Event_Name, Strings.Tooltip_EventManager_Edit_Event);
      FsmEditorContent.AddEventLabel = new GUIContent(Strings.Label_Add_Event, Strings.Tooltip_EventManager_Add_Event);
      FsmEditorContent.EventInspectorLabel = new GUIContent(Strings.Label_Inspector, Strings.Tooltip_EventManager_Inspector_Checkbox);
      FsmEditorContent.EventBrowserButtonLabel = new GUIContent(Strings.Command_Event_Browser, Strings.Tooltip_Event_Browser_Button_in_Events_Tab);
      FsmEditorContent.EventBroadcastIcon = new GUIContent((Texture) FsmEditorStyles.BroadcastIcon, Strings.Tooltip_Global_Event_Flag);
      FsmEditorContent.EventBroadcastButton = new GUIContent((Texture) FsmEditorStyles.BroadcastIcon, "Press to broadcast the event.");
      FsmEditorContent.EventSendButton = new GUIContent("", "Press to send the event.");
      FsmEditorContent.EventSendGlobalButton = new GUIContent("", "Press to broadcast the event.");
      FsmEditorContent.EventHeaderLabel = new GUIContent(Strings.Label_Event, "Hint: Right-click event to see usage.");
      FsmEditorContent.EventUsedHeaderLabel = new GUIContent(Strings.Label_Used, Strings.Tooltip_Events_Used_By_States);
      FsmEditorContent.GlobalEventTooltipLabel = new GUIContent("", Strings.Label_Global);
      FsmEditorContent.SettingsButton = new GUIContent(GUIHelpers.GetBuiltinIcon("_Popup"), Strings.Command_Settings);
      FsmEditorContent.MainToolbarShowMinimap = FsmEditorContent.GetIconContent("minimapIcon", 14, 14, Strings.Tooltip_Toggle_Minimap);
      FsmEditorContent.BackButton = FsmEditorContent.GetIconContent("backIcon", 11, 14, Strings.Tooltip_Select_Previous_FSM);
      FsmEditorContent.ForwardButton = FsmEditorContent.GetIconContent("forwardIcon", 11, 14, Strings.Tooltip_Select_Next_FSM);
      FsmEditorContent.RecentButton = FsmEditorContent.GetIconContent("recentListIcon", 10, 14, Strings.MainToolbar_Recent);
      if (usingProSkin)
      {
        FsmEditorContent.FsmBrowserButton = new GUIContent((Texture) Files.LoadTextureFromDll("browserIcon", 14, 14), Strings.Tooltip_Editor_Windows);
        FsmEditorContent.DeleteButton = new GUIContent((Texture) Files.LoadTextureFromDll("deleteIcon", 17, 14), Strings.Command_Delete);
        FsmEditorContent.ResetButton = new GUIContent((Texture) Files.LoadTextureFromDll("deleteIcon", 17, 14), Strings.Command_Reset);
        FsmEditorContent.UpButton = new GUIContent((Texture) Files.LoadTextureFromDll("upIcon", 17, 14), Strings.Command_Move_Up);
        FsmEditorContent.DownButton = new GUIContent((Texture) Files.LoadTextureFromDll("downIcon", 17, 14), Strings.Command_Move_Down);
        FsmEditorContent.VariableButton = new GUIContent((Texture) Files.LoadTextureFromDll("variableIcon", 17, 14), Strings.Option_Use_Variable);
        FsmEditorContent.BrowseButton = new GUIContent((Texture) Files.LoadTextureFromDll("browseIcon", 17, 14), Strings.Command_Browse);
        FsmEditorContent.PopupButton = new GUIContent((Texture) Files.LoadTextureFromDll("browseIcon", 17, 14), "");
        FsmEditorContent.SortUpButton = new GUIContent((Texture) Files.LoadTextureFromDll("SortOrderUp", 8, 16), "");
        FsmEditorContent.SortDownButton = new GUIContent((Texture) Files.LoadTextureFromDll("SortOrderDown", 8, 16), "");
      }
      else
      {
        FsmEditorContent.FsmBrowserButton = new GUIContent((Texture) Files.LoadTextureFromDll("browserIcon_indie", 14, 14), Strings.Tooltip_Editor_Windows);
        FsmEditorContent.DeleteButton = new GUIContent((Texture) Files.LoadTextureFromDll("deleteIcon_indie", 17, 14), Strings.Command_Delete);
        FsmEditorContent.ResetButton = new GUIContent((Texture) Files.LoadTextureFromDll("deleteIcon_indie", 17, 14), Strings.Command_Reset);
        FsmEditorContent.UpButton = new GUIContent((Texture) Files.LoadTextureFromDll("upIcon_indie", 17, 14), Strings.Command_Move_Up);
        FsmEditorContent.DownButton = new GUIContent((Texture) Files.LoadTextureFromDll("downIcon_indie", 17, 14), Strings.Command_Move_Down);
        FsmEditorContent.VariableButton = new GUIContent((Texture) Files.LoadTextureFromDll("variableIcon_indie", 17, 14), Strings.Option_Use_Variable);
        FsmEditorContent.BrowseButton = new GUIContent((Texture) Files.LoadTextureFromDll("browseIcon_indie", 17, 14), Strings.Command_Browse);
        FsmEditorContent.PopupButton = new GUIContent((Texture) Files.LoadTextureFromDll("browseIcon_indie", 17, 14), "");
        FsmEditorContent.SortUpButton = new GUIContent((Texture) Files.LoadTextureFromDll("SortOrderUp_indie", 8, 16), "");
        FsmEditorContent.SortDownButton = new GUIContent((Texture) Files.LoadTextureFromDll("SortOrderDown_indie", 8, 16), "");
      }
      FsmEditorContent.DebugToolbarErrorCount = new GUIContent("", Strings.DebugToolbar_Error_Count_Tooltip);
      FsmEditorContent.DebugToolbarDebug = new GUIContent(Strings.DebugToolbar_Label_Debug, Strings.DebugToolbar_Label_Debug_Tooltip);
      FsmEditorContent.DebugToolbarPrev = new GUIContent(Strings.DebugToolbar_Button_Prev, Strings.DebugToolbar_Button_Prev_Toolrip);
      FsmEditorContent.DebugToolbarNext = new GUIContent(Strings.DebugToolbar_Button_Next, Strings.DebugToolbar_Button_Next_Tooltip);
      FsmEditorContent.ErrorIcon = new GUIContent((Texture) FsmEditorStyles.StateErrorIcon, "Has Errors!");
      FsmEditorContent.StateTitleBox = new GUIContent();
      FsmEditorContent.StateDescription = new GUIContent();
      FsmEditorContent.FsmDescription = new GUIContent();
      FsmEditorContent.EventLabel = new GUIContent();
      FsmEditorContent.MainToolbarSelectedGO = new GUIContent();
      FsmEditorContent.MainToolbarSelectedFSM = new GUIContent();
      FsmEditorContent.MainToolbarLock = new GUIContent(Strings.Command_Lock_Selected_FSM, Strings.Tooltip_Lock_Selected_FSM);
      FsmEditorContent.MainToolbarPrefabTypeNone = new GUIContent(Strings.Label_Select, Strings.Command_Select_GameObject);
      FsmEditorContent.MainToolbarSelectTemplateUsage = new GUIContent(Strings.Label_Select, Strings.Label_Find_Template_Usages_Tooltip);
      FsmEditorContent.HintGettingStarted = new GUIContent(Strings.Hint_GraphView_Getting_Started);
      FsmEditorContent.HintGraphCommands = new GUIContent("Toggle Hints:\nAdd State:\nAdd FINISHED Event:\nAdd Transition State:\nQuick Delete:\nOverride Grid Snap:\nConstrain Drag:\nSelect Start State:\nFollow Transition:\nLock Link Direction:\nMove Selected Transition:");
      string text = "F1\nCtrl Click Canvas\nCtrl Click State\nCtrl Drag Transition\nCtrl Shift Click\nCtrl Drag States\nShift Drag States\nHome\nAlt Click Transition\nCtrl Left/Right\nCtrl Up/Down";
      if (Application.platform == RuntimePlatform.OSXEditor)
        text = text.Replace("Ctrl", "Cmd");
      FsmEditorContent.HintGraphShortcuts = new GUIContent(text);
      FsmEditorContent.HintGettingStartedSize = FsmEditorContent.CalcLineWrappedContentSize(FsmEditorContent.HintGettingStarted, FsmEditorStyles.HintBox);
      FsmEditorContent.HintGraphShortcutsSize = FsmEditorContent.CalcLineWrappedContentSize(FsmEditorContent.HintGraphShortcuts, FsmEditorStyles.HintBox);
      FsmEditorContent.HintGraphCommandsSize = FsmEditorContent.CalcLineWrappedContentSize(FsmEditorContent.HintGraphCommands, FsmEditorStyles.HintBox);
    }

    public static GUIContent GetIconContent(
      string resourceName,
      int width,
      int height,
      string tooltip)
    {
      // if (FsmEditorStyles.UsingProSkin())
      //   resourceName = "d_" + resourceName;
      if ((double) EditorGUIUtility.pixelsPerPoint > 1.0)
      {
        resourceName += "@2x";
        width *= 2;
        height *= 2;
      }
      return new GUIContent((Texture) Files.LoadTextureFromDll(resourceName, width, height), tooltip);
    }

    public static GUIContent TempContent(string text, string tooltip = "")
    {
      FsmEditorContent.tempContent.text = text;
      FsmEditorContent.tempContent.image = (Texture) null;
      FsmEditorContent.tempContent.tooltip = tooltip;
      return FsmEditorContent.tempContent;
    }

    public static GUIContent TempContent(Texture texture, string tooltip = "")
    {
      FsmEditorContent.tempContent.text = "";
      FsmEditorContent.tempContent.image = texture;
      FsmEditorContent.tempContent.tooltip = tooltip;
      return FsmEditorContent.tempContent;
    }

    public static GUIContent TempContent(string text, Texture texture, string tooltip = "")
    {
      FsmEditorContent.tempContent.text = text;
      FsmEditorContent.tempContent.image = texture;
      FsmEditorContent.tempContent.tooltip = tooltip;
      return FsmEditorContent.tempContent;
    }

    private static Vector2 CalcLineWrappedContentSize(GUIContent content, GUIStyle guiStyle)
    {
      float maxWidth;
      guiStyle.CalcMinMaxWidth(content, out float _, out maxWidth);
      return new Vector2(maxWidth, guiStyle.CalcHeight(content, maxWidth));
    }
  }
}
