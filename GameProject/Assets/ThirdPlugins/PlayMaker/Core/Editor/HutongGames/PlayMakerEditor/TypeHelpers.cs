// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.TypeHelpers
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public static class TypeHelpers
  {
    private static readonly Dictionary<System.Type, string> friendlyNames = new Dictionary<System.Type, string>();
    private static readonly Dictionary<System.Type, IEnumerable<FieldInfo>> typeFields = new Dictionary<System.Type, IEnumerable<FieldInfo>>();
    private static List<System.Type> objectTypeList;
    private static List<System.Type> enumTypeList;
    private static FsmProperty targetFsmProperty;
    private static FsmVariable targetFsmVariable;
    private static NamedVariable targetVariable;
    private static string subPropertyPath;
    private static readonly string[] filterGameObjectMembers = new string[18]
    {
      "animation",
      "audio",
      "camera",
      "collider",
      "collider2D",
      "constantForce",
      "gameObject",
      "guiElement",
      "guiText",
      "guiTexture",
      "hingeJoint",
      "light",
      "networkView",
      "particleEmitter",
      "particleSystem",
      "renderer",
      "rigidbody",
      "rigidbody2D"
    };

    public static List<System.Type> ObjectTypeList => TypeHelpers.objectTypeList ?? (TypeHelpers.objectTypeList = TypeHelpers.GetDerivedTypeList(typeof (UnityEngine.Object)));

    public static List<System.Type> EnumTypeList => TypeHelpers.enumTypeList ?? (TypeHelpers.enumTypeList = TypeHelpers.GetDerivedTypeList(typeof (Enum), false));

    static TypeHelpers()
    {
      TypeHelpers.friendlyNames.Clear();
      TypeHelpers.friendlyNames.Add(typeof (void), "void");
      TypeHelpers.friendlyNames.Add(typeof (bool), "bool");
      TypeHelpers.friendlyNames.Add(typeof (int), "int");
      TypeHelpers.friendlyNames.Add(typeof (float), "float");
      TypeHelpers.friendlyNames.Add(typeof (string), "string");
    }

    public static string GetFriendlyName(System.Type t)
    {
      if (t == null)
        return "";
      if (t.IsArray)
        return TypeHelpers.GetFriendlyName(t.GetElementType()) + "[]";
      string str;
      return !TypeHelpers.friendlyNames.TryGetValue(t, out str) ? t.Name : str;
    }

    public static List<System.Type> GetDerivedTypeList(System.Type ofType, bool includeBaseType = true)
    {
      List<System.Type> typeList = new List<System.Type>();
      if (includeBaseType)
        typeList.Add(ofType);
      foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
      {
        if (!TypeHelpers.IsEditorAssembly(assembly))
        {
          try
          {
            foreach (System.Type exportedType in assembly.GetExportedTypes())
            {
              if (!TypeHelpers.IsEditorType(exportedType) && !TypeHelpers.IsFilteredType(exportedType) && exportedType.IsSubclassOf(ofType))
                typeList.Add(exportedType);
            }
          }
          catch (Exception ex)
          {
            NotSupportedException supportedException = ex as NotSupportedException;
          }
        }
      }
      typeList.Sort((Comparison<System.Type>) ((o1, o2) => string.CompareOrdinal(o1.ToString(), o2.ToString())));
      return typeList;
    }

    private static bool IsEditorType(System.Type type) => type.IsSubclassOf(typeof (UnityEditor.Editor)) || type.IsSubclassOf(typeof (EditorWindow)) || (type.FullName.StartsWith("UnityEditor.") || type.FullName.StartsWith("TreeEditor.")) || type.FullName.StartsWith("HutongGames.PlayMakerEditor");

    private static bool IsFilteredType(System.Type type) => type.FullName.StartsWith("Boo.Lan") || type.FullName.StartsWith("Mono.") || type.FullName.StartsWith("JetBrains.Annotations") || type.FullName.StartsWith("PlayMaker.ConditionalExpression");

    [Localizable(false)]
    private static bool IsEditorAssembly(Assembly assembly)
    {
      string name = assembly.GetName().Name;
      return name == "UnityEditor" || name == "PlayMakerEditor";
    }

    private static void RebuildTypeList() => TypeHelpers.objectTypeList = (List<System.Type>) null;

    public static IEnumerable<FieldInfo> GetSerializedFields(System.Type type)
    {
      IEnumerable<FieldInfo> fieldInfos1;
      if (TypeHelpers.typeFields.TryGetValue(type, out fieldInfos1))
        return fieldInfos1;
      IEnumerable<FieldInfo> fieldInfos2 = ((IEnumerable<FieldInfo>) type.GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic)).Where<FieldInfo>((Func<FieldInfo, bool>) (f => f.IsPublic || f.IsDefined(typeof (SerializeField), false))).Where<FieldInfo>((Func<FieldInfo, bool>) (f => f.FieldType != typeof (Fsm) && f.FieldType != typeof (FsmTemplate))).Where<FieldInfo>((Func<FieldInfo, bool>) (f => !f.FieldType.IsSubclassOf(typeof (UnityEngine.Object))));
      TypeHelpers.typeFields.Add(type, fieldInfos2);
      return fieldInfos2;
    }

    public static GenericMenu GenerateMethodMenu(
      System.Type type,
      GenericMenu.MenuFunction2 selectionFunction)
    {
      return TypeHelpers.GenerateMethodMenu(type, selectionFunction, BindingFlags.Instance | BindingFlags.Public);
    }

    public static GenericMenu GenerateStaticMethodMenu(
      System.Type type,
      GenericMenu.MenuFunction2 selectionFunction)
    {
      return TypeHelpers.GenerateMethodMenu(type, selectionFunction, BindingFlags.Static | BindingFlags.Public);
    }

    public static GenericMenu GenerateMethodMenu(
      System.Type type,
      GenericMenu.MenuFunction2 selectionFunction,
      BindingFlags bindingFlags)
    {
      GenericMenu genericMenu = new GenericMenu();
      if (type == null)
        return genericMenu;
      foreach (MethodInfo method in type.GetMethods(bindingFlags))
      {
        if (TypeHelpers.IsValidMethod(method))
        {
          string text = TypeHelpers.GetMethodSignature(method);
          if (method.DeclaringType == typeof (MonoBehaviour) || method.DeclaringType == typeof (UnityEngine.Component) || (method.DeclaringType == typeof (Behaviour) || method.DeclaringType == typeof (UnityEngine.Object)))
            text = "Inherited/" + text;
          genericMenu.AddItem(new GUIContent(text), false, selectionFunction, (object) method);
        }
      }
      return genericMenu;
    }

    public static string GetMethodSignature(MethodInfo method)
    {
      string str = TypeHelpers.GetFriendlyName(method.ReturnType) + " " + method.Name + " (";
      bool flag = true;
      foreach (ParameterInfo parameter in method.GetParameters())
      {
        if (!flag)
          str += ", ";
        str += TypeHelpers.GetFriendlyName(parameter.ParameterType);
        flag = false;
      }
      return str + ")";
    }

    public static string GetMethodSignature(string methodName, FsmVar[] parameters, FsmVar result)
    {
      if (parameters == null || result == null)
        return "";
      string str = TypeHelpers.GetFriendlyName(result.RealType ?? typeof (void)) + " " + methodName + " (";
      bool flag = true;
      foreach (FsmVar parameter in parameters)
      {
        if (!flag)
          str += ", ";
        str += TypeHelpers.GetFriendlyName(parameter.RealType);
        flag = false;
      }
      return str + ")";
    }

    public static MethodInfo FindMethod(System.Type type, string methodSignature)
    {
      if (type == null)
        return (MethodInfo) null;
      foreach (MethodInfo method in type.GetMethods(BindingFlags.Instance | BindingFlags.Public))
      {
        if (!method.IsSpecialName && string.Equals(methodSignature, TypeHelpers.GetMethodSignature(method), StringComparison.OrdinalIgnoreCase))
          return method;
      }
      return (MethodInfo) null;
    }

    private static bool IsValidMethod(MethodInfo method)
    {
      if (method.IsGenericMethod || method.IsSpecialName || method.ReturnType != typeof (void) && !TypeHelpers.IsSupportedParameterType(method.ReturnType))
        return false;
      foreach (ParameterInfo parameter in method.GetParameters())
      {
        if (!TypeHelpers.IsSupportedParameterType(parameter.ParameterType))
          return false;
      }
      return true;
    }

    public static GenericMenu GenerateObjectTypesMenu(FsmProperty fsmProperty)
    {
      TypeHelpers.RebuildTypeList();
      TypeHelpers.targetFsmProperty = fsmProperty;
      GenericMenu genericMenu = new GenericMenu();
      foreach (System.Type objectType in TypeHelpers.ObjectTypeList)
      {
        string fullName = objectType.FullName;
        if (fullName != null)
        {
          string text = fullName.Replace('.', '/');
          genericMenu.AddItem(new GUIContent(text), false, new GenericMenu.MenuFunction2(TypeHelpers.SetFsmPropertyTargetType), (object) fullName);
        }
      }
      return genericMenu;
    }

    private static void SetFsmPropertyTargetType(object userdata)
    {
      if (TypeHelpers.targetFsmProperty == null)
        return;
      TypeHelpers.targetFsmProperty.TargetTypeName = userdata as string;
      FsmEditor.SetFsmDirty(true);
      FsmEditor.SaveActions();
    }

    public static GenericMenu GenerateObjectTypesMenu(FsmVariable fsmVariable)
    {
      TypeHelpers.targetFsmVariable = fsmVariable;
      GenericMenu genericMenu = new GenericMenu();
      foreach (System.Type objectType in TypeHelpers.ObjectTypeList)
      {
        string fullName = objectType.FullName;
        string text = fullName.Replace('.', '/');
        genericMenu.AddItem(new GUIContent(text), fullName == fsmVariable.TypeName, new GenericMenu.MenuFunction2(TypeHelpers.SetFsmVariableObjectType), (object) fullName);
      }
      return genericMenu;
    }

    public static GenericMenu GenerateObjectTypesMenu(NamedVariable variable)
    {
      TypeHelpers.targetVariable = variable;
      GenericMenu genericMenu = new GenericMenu();
      foreach (System.Type objectType in TypeHelpers.ObjectTypeList)
      {
        string fullName = objectType.FullName;
        string text = fullName.Replace('.', '/');
        genericMenu.AddItem(new GUIContent(text), fullName == variable.ObjectType.Name, new GenericMenu.MenuFunction2(TypeHelpers.SetVariableObjectType), (object) fullName);
      }
      return genericMenu;
    }

    private static void SetVariableObjectType(object userdata)
    {
      if (TypeHelpers.targetVariable == null)
        return;
      TypeHelpers.targetVariable.ObjectType = ReflectionUtils.GetGlobalType(userdata as string);
      FsmEditor.SetFsmDirty(true);
      FsmEditor.SaveActions();
    }

    private static void SetFsmVariableObjectType(object userdata)
    {
      if (TypeHelpers.targetFsmVariable == null)
        return;
      TypeHelpers.targetFsmVariable.TypeName = userdata as string;
      TypeHelpers.targetFsmVariable.ObjectType = ReflectionUtils.GetGlobalType(TypeHelpers.targetFsmVariable.TypeName);
      TypeHelpers.targetFsmVariable.UpdateVariableValue();
      FsmEditor.SetFsmDirty(true);
      FsmEditor.SaveActions();
    }

    public static GenericMenu GenerateEnumTypesMenu(FsmVariable fsmVariable)
    {
      TypeHelpers.targetFsmVariable = fsmVariable;
      GenericMenu genericMenu = new GenericMenu();
      foreach (System.Type enumType in TypeHelpers.EnumTypeList)
      {
        string fullName = enumType.FullName;
        string text = fullName.Replace('.', '/');
        genericMenu.AddItem(new GUIContent(text), fullName == fsmVariable.TypeName, new GenericMenu.MenuFunction2(TypeHelpers.SetFsmVariableObjectType), (object) fullName);
      }
      return genericMenu;
    }

    public static GenericMenu GenerateEnumTypesMenu(NamedVariable variable)
    {
      TypeHelpers.targetVariable = variable;
      GenericMenu genericMenu = new GenericMenu();
      foreach (System.Type enumType in TypeHelpers.EnumTypeList)
      {
        string fullName = enumType.FullName;
        string text = fullName.Replace('.', '/');
        genericMenu.AddItem(new GUIContent(text), false, new GenericMenu.MenuFunction2(TypeHelpers.SetVariableObjectType), (object) fullName);
      }
      return genericMenu;
    }

    public static GenericMenu GeneratePropertyMenu(FsmProperty fsmProperty)
    {
      TypeHelpers.targetFsmProperty = fsmProperty;
      GenericMenu menu = new GenericMenu();
      System.Type globalType = ReflectionUtils.GetGlobalType(fsmProperty.TargetTypeName);
      if (globalType != null)
        TypeHelpers.AddPropertyMenuItems(ref menu, globalType, "", 0, fsmProperty.setProperty);
      return menu;
    }

    private static void GenerateSubPropertyMenu(object userdata)
    {
      TypeHelpers.subPropertyPath = userdata as string;
      FsmEditor.OnRepaint += new Action(TypeHelpers.OpenSubPropertyMenu);
    }

    private static void OpenSubPropertyMenu()
    {
      FsmEditor.OnRepaint -= new Action(TypeHelpers.OpenSubPropertyMenu);
      string subPropertyPath = TypeHelpers.subPropertyPath;
      FsmProperty targetFsmProperty = TypeHelpers.targetFsmProperty;
      GenericMenu menu = new GenericMenu();
      System.Type globalType = ReflectionUtils.GetGlobalType(targetFsmProperty.TargetTypeName);
      if (globalType != null)
      {
        string path = TypeHelpers.NicifyPropertyPath(subPropertyPath);
        System.Type propertyType = ReflectionUtils.GetPropertyType(globalType, subPropertyPath);
        TypeHelpers.AddPropertyMenuItems(ref menu, propertyType, path, 0, targetFsmProperty.setProperty);
      }
      TypeHelpers.subPropertyPath = "";
      menu.ShowAsContext();
    }

    public static bool IsSupportedParameterType(System.Type parameterType)
    {
      for (; parameterType != null; parameterType = parameterType.GetElementType())
      {
        if (!parameterType.IsArray)
          return parameterType == typeof (string) || parameterType == typeof (int) || (parameterType == typeof (float) || parameterType == typeof (Vector2)) || (parameterType == typeof (Vector3) || parameterType == typeof (Color) || (parameterType == typeof (bool) || parameterType == typeof (Quaternion))) || (parameterType == typeof (Material) || parameterType == typeof (Texture) || (parameterType == typeof (Rect) || parameterType.IsEnum)) || parameterType.IsSubclassOf(typeof (UnityEngine.Object));
      }
      return false;
    }

    private static void AddPropertyMenuItems(
      ref GenericMenu menu,
      System.Type type,
      string path,
      int depth,
      bool setProperty)
    {
      if (type == null || type.IsEnum || depth >= 3)
        return;
      List<MemberInfo> fieldsAndProperties = ReflectionUtils.GetFieldsAndProperties(type, BindingFlags.Instance | BindingFlags.Public);
      fieldsAndProperties.Sort((Comparison<MemberInfo>) ((x, y) => string.CompareOrdinal(x.Name, y.Name)));
      foreach (MemberInfo memberInfo in fieldsAndProperties)
      {
        if (!TypeHelpers.FilterMember(memberInfo))
        {
          System.Type memberUnderlyingType = ReflectionUtils.GetMemberUnderlyingType(memberInfo);
          string name = memberInfo.Name;
          bool flag = memberUnderlyingType.IsClass || (setProperty ? TypeHelpers.CanSetProperty(memberInfo) : TypeHelpers.CanGetProperty(memberInfo));
          if (flag)
          {
            string text = path != "" ? path + "/" + name : name;
            if (TypeHelpers.HasProperties(memberUnderlyingType))
            {
              if (TypeHelpers.CanSetProperty(memberInfo))
                menu.AddItem(new GUIContent(text + "/" + Labels.StripNamespace(memberUnderlyingType.ToString())), false, new GenericMenu.MenuFunction2(TypeHelpers.SetFsmPropertyName), (object) text);
            }
            else
              menu.AddItem(new GUIContent(text), false, new GenericMenu.MenuFunction2(TypeHelpers.SetFsmPropertyName), (object) text);
          }
          if (string.IsNullOrEmpty(TypeHelpers.subPropertyPath) && depth > 0 && memberUnderlyingType.IsSubclassOf(typeof (UnityEngine.Component)))
          {
            menu.AddItem(new GUIContent(path + Strings.SubMenu_More_), false, new GenericMenu.MenuFunction2(TypeHelpers.GenerateSubPropertyMenu), (object) path);
            break;
          }
          if (flag || memberUnderlyingType.IsSubclassOf(typeof (UnityEngine.Component)))
            TypeHelpers.AddPropertyMenuItems(ref menu, memberUnderlyingType, path != "" ? path + "/" + name : name, depth + 1, setProperty);
        }
      }
    }

    public static bool FilterMember(MemberInfo memberInfo)
    {
      if (memberInfo.DeclaringType == typeof (GameObject) && ((IEnumerable<string>) TypeHelpers.filterGameObjectMembers).Contains<string>(memberInfo.Name) || memberInfo.DeclaringType == typeof (UnityEngine.Component))
        return true;
      System.Type memberUnderlyingType = ReflectionUtils.GetMemberUnderlyingType(memberInfo);
      return memberInfo.DeclaringType == memberUnderlyingType || memberUnderlyingType == typeof (Matrix4x4);
    }

    public static bool CanSetProperty(MemberInfo member) => ReflectionUtils.CanSetMemberValue(member) && TypeHelpers.IsSupportedParameterType(ReflectionUtils.GetMemberUnderlyingType(member));

    public static bool CanGetProperty(MemberInfo member) => ReflectionUtils.CanGetMemberValue(member) && TypeHelpers.IsSupportedParameterType(ReflectionUtils.GetMemberUnderlyingType(member));

    public static bool HasProperties(System.Type type)
    {
      if (type.IsClass)
        return true;
      return !type.IsPrimitive && !type.IsEnum;
    }

    private static void SetFsmPropertyName(object userdata)
    {
      if (TypeHelpers.targetFsmProperty != null)
        TypeHelpers.targetFsmProperty.SetPropertyName(TypeHelpers.NicifyPropertyPath(userdata as string));
      FsmEditor.SetFsmDirty(true);
      FsmEditor.SaveActions();
    }

    public static string NicifyPropertyPath(string path) => !string.IsNullOrEmpty(path) ? path.Replace('/', '.') : "";
  }
}
