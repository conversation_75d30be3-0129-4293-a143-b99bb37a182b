// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.PlayMakerEditorPrefs
// Assembly: PlayMakerEditor, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Obsolete("Use HutongGames.PlayMakerEditor.StartupPrefs instead.")]
  public class PlayMakerEditorPrefs : ScriptableObject
  {
    [SerializeField]
    private bool showWelcomeScreen = true;
    private static PlayMakerEditorPrefs instance;
    [SerializeField]
    private string playmakerVersion;

    public static PlayMakerEditorPrefs Instance
    {
      get
      {
        if ((UnityEngine.Object) PlayMakerEditorPrefs.instance == (UnityEngine.Object) null)
        {
          string path = Path.Combine(PlayMakerPaths.EditorPath, "PlayMakerEditorPrefs.asset");
          PlayMakerEditorPrefs.instance = AssetDatabase.LoadAssetAtPath(path, typeof (PlayMakerEditorPrefs)) as PlayMakerEditorPrefs;
          if ((UnityEngine.Object) PlayMakerEditorPrefs.instance == (UnityEngine.Object) null)
          {
            PlayMakerEditorPrefs.instance = ScriptableObject.CreateInstance<PlayMakerEditorPrefs>();
            FsmEditor.CreateAsset((UnityEngine.Object) PlayMakerEditorPrefs.instance, ref path);
            Debug.Log((object) ("Creating PlayMakerEditorPrefs asset: " + path));
          }
        }
        return PlayMakerEditorPrefs.instance;
      }
    }

    public static string PlaymakerVersion
    {
      get => PlayMakerEditorPrefs.Instance.playmakerVersion;
      set
      {
        PlayMakerEditorPrefs.Instance.playmakerVersion = value;
        PlayMakerEditorPrefs.Save();
      }
    }

    public static bool ShowWelcomeScreen
    {
      get => PlayMakerEditorPrefs.Instance.showWelcomeScreen;
      set
      {
        if (value == PlayMakerEditorPrefs.Instance.showWelcomeScreen)
          return;
        PlayMakerEditorPrefs.Instance.showWelcomeScreen = value;
        PlayMakerEditorPrefs.Save();
      }
    }

    public static void Save() => EditorUtility.SetDirty((UnityEngine.Object) PlayMakerEditorPrefs.Instance);
  }
}
