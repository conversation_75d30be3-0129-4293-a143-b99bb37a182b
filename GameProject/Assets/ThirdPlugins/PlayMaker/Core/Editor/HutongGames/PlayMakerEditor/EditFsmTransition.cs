// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.EditFsmTransition
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class EditFsmTransition
  {
    public static void SetEvent(this FsmTransition transition, FsmEvent fsmEvent)
    {
      if (transition == null)
        return;
      transition.FsmEvent = new FsmEvent(fsmEvent);
    }

    public static void SetColorIndex(this FsmTransition transition, int colorIndex)
    {
      if (transition == null)
        return;
      colorIndex = Mathf.Clamp(colorIndex, 0, PlayMakerPrefs.Colors.Length - 1);
      transition.ColorIndex = colorIndex;
    }
  }
}
