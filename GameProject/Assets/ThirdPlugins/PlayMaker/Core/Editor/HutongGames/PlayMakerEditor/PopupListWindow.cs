// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.PopupListWindow
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class PopupListWindow : EditorWindow
  {
    private TextField editField;
    private Vector2 scrollPosition;
    private float scrollViewHeight;
    private Rect selectedRect;
    private bool autoScroll;
    private int selectedIndex = -1;

    public string SelectedText
    {
      get => this.selectedIndex == -1 || this.ListItems.Length == 0 ? "" : this.ListItems[Mathf.Min(this.selectedIndex, this.ListItems.Length - 1)].text;
      set
      {
        this.selectedIndex = -1;
        for (int index = 0; index < this.ListItems.Length; ++index)
        {
          if (this.ListItems[index].text == value)
            this.selectedIndex = index;
        }
      }
    }

    public GUIContent[] ListItems { get; set; }

    public static PopupListWindow CreateWindow(Rect buttonRect, Vector2 windowSize)
    {
      PopupListWindow instance = ScriptableObject.CreateInstance<PopupListWindow>();
      instance.ShowAsDropDown(buttonRect, windowSize);
      instance.Init();
      return instance;
    }

    private void Init()
    {
      this.scrollPosition = new Vector2();
      this.editField = new TextField((EditorWindow) this, GUIContent.none);
      this.editField.Focus();
    }

    private void OnGUI()
    {
      FsmEditorStyles.Init();
      this.editField.OnGUI();
      this.DoKeyboardGUI();
      this.DoListView();
    }

    private void DoKeyboardGUI()
    {
      if (GUIUtility.keyboardControl != 0 || Event.current.GetTypeForControl(GUIUtility.GetControlID(FocusType.Keyboard)) != UnityEngine.EventType.KeyDown)
        return;
      switch (Event.current.keyCode)
      {
        case KeyCode.Escape:
          Event.current.Use();
          int keyboardControl = GUIUtility.keyboardControl;
          GUIUtility.ExitGUI();
          break;
        case KeyCode.UpArrow:
          Event.current.Use();
          this.SelectPrevious();
          GUIUtility.ExitGUI();
          break;
        case KeyCode.DownArrow:
          Event.current.Use();
          this.SelectNext();
          GUIUtility.ExitGUI();
          break;
      }
    }

    private void DoListView()
    {
      if (this.ListItems == null || this.ListItems.Length == 0)
        return;
      this.scrollPosition = EditorGUILayout.BeginScrollView(this.scrollPosition);
      foreach (GUIContent listItem in this.ListItems)
      {
        bool flag = listItem.text == this.SelectedText;
        GUILayout.BeginHorizontal(flag ? FsmEditorStyles.SelectedEventBox : FsmEditorStyles.TableRowBoxNoDivider);
        GUIStyle style = flag ? FsmEditorStyles.TableRowTextSelected : FsmEditorStyles.TableRowText;
        GUILayout.Label(listItem.text, style);
        GUILayout.EndHorizontal();
        if (flag)
        {
          this.selectedRect = GUILayoutUtility.GetLastRect();
          this.selectedRect.y -= this.scrollPosition.y;
          this.selectedRect.y += 20f;
        }
      }
      EditorGUILayout.EndScrollView();
      this.DoAutoScroll();
    }

    private void DoAutoScroll()
    {
      if (string.IsNullOrEmpty(this.SelectedText) || Event.current.type != UnityEngine.EventType.Repaint || !this.autoScroll)
        return;
      this.scrollViewHeight = GUILayoutUtility.GetLastRect().height;
      if ((double) this.selectedRect.y < 0.0)
      {
        this.scrollPosition.y += this.selectedRect.y;
        this.Repaint();
      }
      else if ((double) this.selectedRect.y + (double) this.selectedRect.height > (double) this.scrollViewHeight)
      {
        this.scrollPosition.y += this.selectedRect.y + this.selectedRect.height - this.scrollViewHeight;
        this.Repaint();
      }
      this.autoScroll = false;
    }

    private void SelectListItem(string listItem)
    {
      this.SelectedText = listItem;
      this.autoScroll = true;
    }

    private void SelectListItem(int listItemIndex)
    {
      this.selectedIndex = listItemIndex;
      this.autoScroll = true;
    }

    private void SelectPrevious()
    {
      if (this.selectedIndex == -1)
        this.SelectFirst();
      else
        this.SelectListItem(this.selectedIndex - 1);
    }

    private void SelectNext()
    {
      if (this.selectedIndex == -1)
        this.SelectLast();
      else
        this.SelectListItem(this.selectedIndex + 1);
    }

    private void SelectFirst() => this.SelectListItem(0);

    private void SelectLast() => this.SelectListItem(this.ListItems.Length - 1);
  }
}
