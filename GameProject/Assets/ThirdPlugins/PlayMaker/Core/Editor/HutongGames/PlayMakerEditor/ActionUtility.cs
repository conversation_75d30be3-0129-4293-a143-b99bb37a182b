// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.ActionUtility
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class ActionUtility
  {
    public static void ShowObjectContextMenu(
      Fsm fsm,
      FsmState state,
      UnityEngine.Object obj,
      FsmStateAction beforeAction = null)
    {
      if (obj == (UnityEngine.Object) null)
        return;
      Actions.BuildListIfNeeded();
      ActionUtility.ActionCreationParams actionParams = new ActionUtility.ActionCreationParams()
      {
        state = state,
        parameter = obj,
        beforeAction = beforeAction
      };
      GenericMenu menu = new GenericMenu();
      GameObject gameObject = obj as GameObject;
      if ((UnityEngine.Object) gameObject != (UnityEngine.Object) null)
      {
        if (EditorUtility.IsPersistent((UnityEngine.Object) gameObject))
        {
          ActionUtility.AddMenuTitle(ref menu, Strings.Menu_Prefab_Actions);
          ActionUtility.AddPrefabMenuItems(ref menu, actionParams);
        }
        else
        {
          ActionUtility.AddMenuTitle(ref menu, Strings.Menu_GameObject_Actions);
          ActionUtility.AddTopLevelGameObjectMenuItems(ref menu, actionParams);
          ActionUtility.AddObjectMenuItems(ref menu, actionParams, true);
          foreach (UnityEngine.Component component in gameObject.GetComponents<UnityEngine.Component>())
          {
            actionParams.parameter = (UnityEngine.Object) component;
            ActionUtility.AddObjectMenuItems(ref menu, actionParams, true);
          }
        }
      }
      else
      {
        ActionUtility.AddMenuTitle(ref menu, string.Format(Strings.Menu_Type_Actions, (object) obj.GetType().Name));
        ActionUtility.AddObjectMenuItems(ref menu, actionParams);
      }
      if (menu.GetItemCount() == 2)
        menu.AddDisabledItem(new GUIContent(Strings.Menu_No_Context_Actions_Found));
      menu.ShowAsContext();
    }

    private static void AddMenuTitle(ref GenericMenu menu, string title)
    {
      menu.AddDisabledItem(new GUIContent(title));
      menu.AddSeparator("");
    }

    private static void AddPrefabMenuItems(
      ref GenericMenu menu,
      ActionUtility.ActionCreationParams actionParams)
    {
      System.Type type = actionParams.parameter.GetType();
      foreach (System.Type actionType in ActionTargets.GetActionsSortedByCategory())
      {
        ActionUtility.ActionCreationParams actionCreationParams = new ActionUtility.ActionCreationParams(actionParams);
        foreach (ActionTarget actionTarget in ActionTargets.GetActionTargets(actionType))
        {
          if (actionTarget.AllowPrefabs && actionTarget.ObjectType.IsAssignableFrom(type))
          {
            actionCreationParams.actionType = actionType;
            actionCreationParams.actionTarget = actionTarget;
            menu.AddItem(new GUIContent(Labels.GetActionLabel(actionType)), false, new GenericMenu.MenuFunction2(ActionUtility.AddAction), (object) actionCreationParams);
          }
        }
      }
    }

    private static void AddTopLevelGameObjectMenuItems(
      ref GenericMenu menu,
      ActionUtility.ActionCreationParams actionParams)
    {
      GameObject parameter = actionParams.parameter as GameObject;
      if ((UnityEngine.Object) parameter == (UnityEngine.Object) null)
        return;
      PlayMakerFSM component = parameter.GetComponent<PlayMakerFSM>();
      if (!((UnityEngine.Object) component != (UnityEngine.Object) null))
        return;
      ActionUtility.ActionCreationParams actionCreationParams = new ActionUtility.ActionCreationParams(actionParams)
      {
        parameter = (UnityEngine.Object) component,
        actionType = Actions.FindByName("StartFSM")
      };
      menu.AddItem(new GUIContent("Start FSM"), false, new GenericMenu.MenuFunction2(ActionUtility.AddAction), (object) actionCreationParams);
    }

    private static void AddObjectMenuItems(
      ref GenericMenu menu,
      ActionUtility.ActionCreationParams actionParams,
      bool isSubMenu = false)
    {
      System.Type type1 = actionParams.parameter.GetType();
      string path = isSubMenu ? Labels.StripNamespace(type1.FullName) + "/" : "";
      List<System.Type> sortedByCategory = ActionTargets.GetActionsSortedByCategory();
      foreach (System.Type actionType in sortedByCategory)
      {
        ActionUtility.ActionCreationParams actionCreationParams = new ActionUtility.ActionCreationParams(actionParams);
        foreach (ActionTarget actionTarget in ActionTargets.GetActionTargets(actionType))
        {
          if (ActionUtility.PinToTopOfMenu(actionType) && actionTarget.ObjectType.IsAssignableFrom(type1))
          {
            actionCreationParams.actionType = actionType;
            actionCreationParams.actionTarget = actionTarget;
            menu.AddItem(new GUIContent(path + Labels.GetActionLabel(actionType)), false, new GenericMenu.MenuFunction2(ActionUtility.AddAction), (object) actionCreationParams);
          }
        }
      }
      if (menu.GetItemCount() > 2)
        menu.AddSeparator(path);
      foreach (System.Type type2 in sortedByCategory)
      {
        ActionUtility.ActionCreationParams actionCreationParams = new ActionUtility.ActionCreationParams(actionParams);
        foreach (ActionTarget actionTarget in ActionTargets.GetActionTargets(type2))
        {
          if (!ActionUtility.PinToTopOfMenu(type2) && actionTarget.ObjectType.IsAssignableFrom(type1))
          {
            actionCreationParams.actionType = type2;
            actionCreationParams.actionTarget = actionTarget;
            string actionLabel = Labels.GetActionLabel(type2);
            string str = Actions.GetActionCategory(type2) + "/";
            menu.AddItem(new GUIContent(path + str + actionLabel), false, new GenericMenu.MenuFunction2(ActionUtility.AddAction), (object) actionCreationParams);
          }
        }
      }
    }

    [Localizable(false)]
    private static bool PinToTopOfMenu(System.Type actionType) => new List<string>()
    {
      "GetProperty",
      "SetProperty",
      "StartFSM",
      "ActivateGameObject"
    }.Contains(actionType.Name);

    private static void AddAction(object userdata)
    {
      ActionUtility.ActionCreationParams actionCreationParams = (ActionUtility.ActionCreationParams) userdata;
      FsmEditor.SelectAction(ActionUtility.AddAction(actionCreationParams.state, actionCreationParams.actionType, actionCreationParams.actionTarget, actionCreationParams.parameter, actionCreationParams.beforeAction));
    }

    public static FsmStateAction AddAction(
      FsmState state,
      System.Type actionType,
      ActionTarget actionTarget,
      UnityEngine.Object targetObject = null,
      FsmStateAction beforeAction = null)
    {
      FsmStateAction action = state.InsertAction(actionType, beforeAction);
      if (actionTarget != null && !string.IsNullOrEmpty(actionTarget.FieldName))
      {
        string fieldName = actionTarget.FieldName;
        char[] chArray = new char[1]{ ',' };
        foreach (string str in fieldName.Split(chArray))
          ActionUtility.SetActionFieldValue(action, str.Trim(), (object) targetObject);
      }
      action.OnActionTargetInvoked((object) targetObject);
      FsmEditor.SaveActions(state);
      return action;
    }

    public static FsmStateAction AddAction(
      FsmState state,
      string actionTypeName,
      FsmStateAction beforeAction = null)
    {
      System.Type actionType = ActionData.GetActionType(actionTypeName);
      if (actionType != null)
        return state.InsertAction(actionType, beforeAction);
      Debug.LogError((object) string.Format(Strings.ActionUtility_AddAction_Missing_Action, (object) actionTypeName));
      return (FsmStateAction) null;
    }

    public static void SetOwnerDefault(
      Fsm fsm,
      FsmStateAction action,
      UnityEngine.Component component,
      string fieldName = "gameObject")
    {
      if ((UnityEngine.Object) component == (UnityEngine.Object) null)
        return;
      ActionUtility.SetOwnerDefault(fsm, action, component.gameObject, fieldName);
    }

    public static void SetOwnerDefault(
      Fsm fsm,
      FsmStateAction action,
      GameObject targetGO,
      string fieldName = "gameObject")
    {
      FieldInfo field = action.GetType().GetField(fieldName, BindingFlags.Instance | BindingFlags.Public);
      if (field == null || !((UnityEngine.Object) fsm.GameObject != (UnityEngine.Object) targetGO))
        return;
      field.SetValue((object) action, (object) new FsmOwnerDefault()
      {
        OwnerOption = OwnerDefaultOption.SpecifyGameObject,
        GameObject = (FsmGameObject) targetGO
      });
    }

    public static void SetActionFieldValue(FsmStateAction action, string fieldName, object value)
    {
      if (action == null)
      {
        Debug.LogError((object) Strings.Error_Action_is_null_);
      }
      else
      {
        FieldInfo field = action.GetType().GetField(fieldName, BindingFlags.Instance | BindingFlags.Public);
        if (field != null)
        {
          if (field.FieldType.IsInstanceOfType(value))
          {
            field.SetValue((object) action, value);
          }
          else
          {
            if (ActionUtility.TrySetValue(action, field, value))
              return;
            Debug.LogError((object) string.Format(Strings.Error_Could_Not_Set_Action_Field_Value, (object) fieldName));
          }
        }
        else
          Debug.LogError((object) string.Format(Strings.Error_Could_Not_Find_Action_Field, (object) fieldName));
      }
    }

    private static bool TrySetValue(FsmStateAction action, FieldInfo fieldInfo, object value)
    {
      object currentValue = fieldInfo.GetValue((object) action);
      if (fieldInfo.FieldType.IsArray)
      {
        System.Type elementType = fieldInfo.FieldType.GetElementType();
        if (elementType == null)
          return false;
        value = ActionUtility.TryConvertValue(elementType, currentValue, value);
        if (!elementType.IsInstanceOfType(value))
          return false;
        Array instance = Array.CreateInstance(elementType, 1);
        instance.SetValue(value, 0);
        fieldInfo.SetValue((object) action, (object) instance);
        return true;
      }
      value = ActionUtility.TryConvertValue(fieldInfo.FieldType, currentValue, value);
      if (!fieldInfo.FieldType.IsInstanceOfType(value))
        return false;
      fieldInfo.SetValue((object) action, value);
      return true;
    }

    private static object TryConvertValue(System.Type fieldType, object currentValue, object value)
    {
      if (value == null)
        return (object) null;
      System.Type type = value.GetType();
      if (fieldType == typeof (GameObject))
      {
        if (type.IsSubclassOf(typeof (UnityEngine.Component)))
          return (object) ((UnityEngine.Component) value).gameObject;
      }
      else if (fieldType == typeof (FsmGameObject))
      {
        if (type == typeof (GameObject))
          return (object) new FsmGameObject((FsmGameObject) (value as GameObject));
        if (type.IsSubclassOf(typeof (UnityEngine.Component)))
          return (object) new FsmGameObject((FsmGameObject) ((UnityEngine.Component) value).gameObject);
      }
      else if (fieldType == typeof (FsmOwnerDefault))
      {
        if (type == typeof (GameObject))
          return (object) new FsmOwnerDefault()
          {
            OwnerOption = OwnerDefaultOption.SpecifyGameObject,
            GameObject = (FsmGameObject) (value as GameObject)
          };
        if (type.IsSubclassOf(typeof (UnityEngine.Component)))
        {
          GameObject gameObject = ((UnityEngine.Component) value).gameObject;
          if (!((UnityEngine.Object) gameObject != (UnityEngine.Object) FsmEditor.SelectedFsmGameObject))
            return (object) new FsmOwnerDefault();
          return (object) new FsmOwnerDefault()
          {
            OwnerOption = OwnerDefaultOption.SpecifyGameObject,
            GameObject = (FsmGameObject) gameObject
          };
        }
      }
      else
      {
        if (fieldType == typeof (FsmProperty))
        {
          FsmProperty fsmProperty = currentValue as FsmProperty;
          return (object) new FsmProperty()
          {
            TargetObject = new FsmObject()
            {
              Value = (value as UnityEngine.Object)
            },
            setProperty = (fsmProperty != null && fsmProperty.setProperty)
          };
        }
        if (fieldType == typeof (FsmObject))
          return (object) new FsmObject()
          {
            Value = (value as UnityEngine.Object)
          };
        if (fieldType == typeof (FsmMaterial))
          return (object) new FsmMaterial()
          {
            Value = (value as Material)
          };
        if (fieldType == typeof (FsmTexture))
          return (object) new FsmTexture()
          {
            Value = (value as Texture)
          };
        if (fieldType == typeof (FsmEventTarget))
        {
          if (type == typeof (PlayMakerFSM))
            return (object) new FsmEventTarget()
            {
              target = FsmEventTarget.EventTarget.FSMComponent,
              fsmComponent = (value as PlayMakerFSM)
            };
          if (type == typeof (GameObject))
            return (object) new FsmEventTarget()
            {
              target = FsmEventTarget.EventTarget.GameObject,
              gameObject = new FsmOwnerDefault()
              {
                OwnerOption = OwnerDefaultOption.SpecifyGameObject,
                GameObject = (FsmGameObject) (value as GameObject)
              }
            };
        }
        else if (fieldType == typeof (FsmString))
        {
          if (type == typeof (PlayMakerFSM))
            return (object) new FsmString()
            {
              Value = ((PlayMakerFSM) value).FsmName
            };
          if (type == typeof (AnimationClip))
            return (object) new FsmString()
            {
              Value = ((UnityEngine.Object) value).name
            };
        }
      }
      return value;
    }

    public static FsmStateAction AddPlayAnimationAction(
      Fsm targetFsm,
      FsmState state,
      AnimationClip anim,
      FsmStateAction beforeAction = null)
    {
      FsmStateAction fsmStateAction = ActionUtility.AddAction(state, "HutongGames.PlayMaker.Actions.PlayAnimation", beforeAction);
      if (fsmStateAction == null)
        return (FsmStateAction) null;
      if (!ActionUtility.GameObjectHasAnimationClip(targetFsm.GameObject, anim.name) && Dialogs.YesNoDialog(Strings.ActionUtility_Add_Animation_Clip_to_GameObject))
        ActionUtility.AddAnimationClip(targetFsm.GameObject, anim);
      FieldInfo field = fsmStateAction.GetType().GetField("animName", BindingFlags.Instance | BindingFlags.Public);
      if (field != null)
        field.SetValue((object) fsmStateAction, (object) new FsmString()
        {
          Value = anim.name
        });
      FsmEditor.SetFsmDirty(targetFsm, true);
      FsmEditor.SaveActions(targetFsm);
      return fsmStateAction;
    }

    public static void AddAnimationClip(GameObject go, AnimationClip anim)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null || (UnityEngine.Object) anim == (UnityEngine.Object) null)
        return;
      (go.GetComponent<Animation>() ?? go.AddComponent<Animation>()).AddClip(anim, anim.name);
    }

    public static bool GameObjectHasAnimationClip(GameObject go, string clipName)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null || string.IsNullOrEmpty(clipName) || (UnityEngine.Object) go.GetComponent<Animation>() == (UnityEngine.Object) null)
        return false;
      foreach (AnimationClip animationClip in AnimationUtility.GetAnimationClips(go))
      {
        if (!((UnityEngine.Object) animationClip == (UnityEngine.Object) null) && animationClip.name == clipName)
          return true;
      }
      return false;
    }

    public static FsmStateAction AddPlaySoundAction(
      Fsm targetFsm,
      FsmState state,
      AudioClip audioClip,
      FsmStateAction beforeAction = null)
    {
      FsmStateAction fsmStateAction = ActionUtility.AddAction(state, "HutongGames.PlayMaker.Actions.PlaySound", beforeAction);
      if (fsmStateAction == null)
        return (FsmStateAction) null;
      FieldInfo field = fsmStateAction.GetType().GetField("clip", BindingFlags.Instance | BindingFlags.Public);
      if (field != null)
        field.SetValue((object) fsmStateAction, (object) new FsmObject()
        {
          Value = (UnityEngine.Object) audioClip
        });
      FsmEditor.SetFsmDirty(targetFsm, true);
      FsmEditor.SaveActions(targetFsm);
      return fsmStateAction;
    }

    public class ActionCreationParams
    {
      public Fsm fsm;
      public FsmState state;
      public System.Type actionType;
      public ActionTarget actionTarget;
      public UnityEngine.Object parameter;
      public FsmStateAction beforeAction;
      public Vector2 position;

      public ActionCreationParams()
      {
      }

      public ActionCreationParams(ActionUtility.ActionCreationParams source)
      {
        this.fsm = source.fsm;
        this.state = source.state;
        this.actionType = source.actionType;
        this.actionTarget = source.actionTarget;
        this.parameter = source.parameter;
        this.beforeAction = source.beforeAction;
        this.position = source.position;
      }
    }
  }
}
