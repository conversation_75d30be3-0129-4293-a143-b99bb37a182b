// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.GameStateTracker
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using UnityEditor;

namespace HutongGames.PlayMakerEditor
{
  internal static class GameStateTracker
  {
    public static GameState CurrentState { get; private set; }

    public static GameState PreviousState { get; private set; }

    public static bool StateChanged => GameStateTracker.CurrentState != GameStateTracker.PreviousState;

    public static void Init() => GameStateTracker.Update();

    public static void Update()
    {
      GameStateTracker.PreviousState = GameStateTracker.CurrentState;
      GameStateTracker.CurrentState = GameStateTracker.GetCurrentState();
    }

    private static GameState GetCurrentState()
    {
      if (!EditorApplication.isPaused)
        return !EditorApplication.isPlaying ? GameState.Stopped : GameState.Running;
      if (Fsm.IsErrorBreak)
        return GameState.Error;
      return Fsm.IsBreak ? GameState.Break : GameState.Paused;
    }
  }
}
