// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.VariableTypeDropdown
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class VariableTypeDropdown : AdvancedDropdown
  {
    private readonly GenericMenu.MenuFunction2 selectedFunction;
    private readonly Dictionary<int, System.Type> itemLookup = new Dictionary<int, System.Type>();
    private int currentId;
    private readonly VariableTypeDropdown.DropdownType dropdownType;

    public static void ShowEnumsTypeDropdown(Rect rect, GenericMenu.MenuFunction2 func)
    {
      rect.width += 100f;
      rect.x -= 100f;
      new VariableTypeDropdown(new AdvancedDropdownState(), VariableTypeDropdown.DropdownType.EnumTypes, func).Show(rect);
    }

    public static void ShowObjectTypeDropdown(Rect rect, GenericMenu.MenuFunction2 func)
    {
      rect.width += 100f;
      rect.x -= 100f;
      new VariableTypeDropdown(new AdvancedDropdownState(), VariableTypeDropdown.DropdownType.ObjectTypes, func).Show(rect);
    }

    private VariableTypeDropdown(
      AdvancedDropdownState state,
      VariableTypeDropdown.DropdownType type,
      GenericMenu.MenuFunction2 func)
      : base(state)
    {
      this.selectedFunction = func;
      this.dropdownType = type;
    }

    protected override AdvancedDropdownItem BuildRoot()
    {
      switch (this.dropdownType)
      {
        case VariableTypeDropdown.DropdownType.EnumTypes:
          return this.BuildEnumTypeDropdown();
        case VariableTypeDropdown.DropdownType.ObjectTypes:
          return this.BuildObjectTypeDropdown();
        default:
          throw new ArgumentOutOfRangeException();
      }
    }

    private AdvancedDropdownItem BuildEnumTypeDropdown() => this.BuildTypePathDropdown("Enum Types", TypeHelpers.EnumTypeList);

    private AdvancedDropdownItem BuildObjectTypeDropdown() => this.BuildTypePathDropdown("Object Types", TypeHelpers.ObjectTypeList);

    private AdvancedDropdownItem BuildTypePathDropdown(
      string title,
      List<System.Type> types)
    {
      AdvancedDropdownItem advancedDropdownItem = new AdvancedDropdownItem(title);
      foreach (System.Type type in types)
      {
        if (type != null && !string.IsNullOrEmpty(type.FullName))
        {
          string[] strArray = type.FullName.Split('.');
          AdvancedDropdownItem root = advancedDropdownItem;
          string str1 = "";
          for (int index = 0; index < strArray.Length - 1; ++index)
          {
            string str2 = strArray[index];
            AdvancedDropdownItem child = this.FindChildItem(root, str2);
            if (child == null)
            {
              child = new AdvancedDropdownItem(str2);
              root.AddChild(child);
            }
            root = child;
          }
          root.AddChild(new AdvancedDropdownItem(strArray[strArray.Length - 1] + str1)
          {
            id = this.currentId
          });
          this.itemLookup.Add(this.currentId, type);
          ++this.currentId;
        }
      }
      return advancedDropdownItem;
    }

    private AdvancedDropdownItem FindChildItem(
      AdvancedDropdownItem root,
      string findName)
    {
      if (root == null)
        return (AdvancedDropdownItem) null;
      foreach (AdvancedDropdownItem child in root.children)
      {
        if (child.name == findName)
          return child;
      }
      return (AdvancedDropdownItem) null;
    }

    protected override void ItemSelected(AdvancedDropdownItem item)
    {
      this.selectedFunction((object) this.itemLookup[item.id]);
      FsmEditor.SaveActions();
      FsmEditor.Repaint(true);
    }

    private enum DropdownType
    {
      EnumTypes,
      ObjectTypes,
    }
  }
}
