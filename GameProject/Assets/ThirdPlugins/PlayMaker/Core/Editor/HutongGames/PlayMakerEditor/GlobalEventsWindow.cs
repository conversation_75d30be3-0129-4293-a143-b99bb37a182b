// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.GlobalEventsWindow
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using HutongGames.PlayMakerEditorData;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class GlobalEventsWindow : BaseEditorWindow
  {
    private static GlobalEventsWindow instance;
    private ScrollView _scrollView;
    private FsmEvent selectedEvent;
    private SearchBox searchBox;
    private string searchString = "";
    private double clickTime;
    private const double doubleClickTime = 0.3;
    private List<GlobalEventsWindow.EventLine> eventList = new List<GlobalEventsWindow.EventLine>();
    private static readonly Dictionary<FsmEvent, int> usageCount = new Dictionary<FsmEvent, int>();
    private string newEventName = "";
    private bool sortByUsageCount;
    private bool updateEventList;

    private ScrollView scrollView => this._scrollView ?? (this._scrollView = new ScrollView(FsmEditor.Window));

    public override void Initialize()
    {
      this.isToolWindow = true;
      if ((UnityEngine.Object) GlobalEventsWindow.instance == (UnityEngine.Object) null)
        GlobalEventsWindow.instance = this;
      this.minSize = new Vector2(200f, 200f);
      this.searchString = "";
      this.selectedEvent = (FsmEvent) null;
      this.InitSearchBox();
      this.BuildFilteredList();
    }

    public override void InitWindowTitle() => this.SetTitle(Strings.EventsWindow_Title);

    private void InitSearchBox()
    {
      if (this.searchBox != null)
        return;
      this.searchBox = new SearchBox((EditorWindow) this)
      {
        SearchModes = new string[4]
        {
          "All",
          "Global",
          "Local",
          "System"
        },
        HasPopupSearchModes = true
      };
      this.searchBox.SearchChanged += new EditorApplication.CallbackFunction(this.UpdateSearchResults);
      this.searchBox.Focus();
    }

    public void OnDisable()
    {
      if (!((UnityEngine.Object) GlobalEventsWindow.instance == (UnityEngine.Object) this))
        return;
      GlobalEventsWindow.instance = (GlobalEventsWindow) null;
    }

    public static void DeselectAll()
    {
      if (!((UnityEngine.Object) GlobalEventsWindow.instance != (UnityEngine.Object) null))
        return;
      GlobalEventsWindow.instance.selectedEvent = (FsmEvent) null;
      GlobalEventsWindow.instance.Repaint();
    }

    public static void ResetView()
    {
      if ((UnityEngine.Object) GlobalEventsWindow.instance == (UnityEngine.Object) null)
        return;
      GlobalEventsWindow.instance.selectedEvent = (FsmEvent) null;
      GlobalEventsWindow.instance.BuildFilteredList();
      GlobalEventsWindow.instance.Repaint();
    }

    public static void UpdateView(string eventName)
    {
      if ((UnityEngine.Object) GlobalEventsWindow.instance == (UnityEngine.Object) null)
        return;
      GlobalEventsWindow.instance.UpdateUseCount(eventName);
      GlobalEventsWindow.instance.Repaint();
    }

    protected override void DoUpdateHighlightIdentifiers()
    {
      Rect position = this.position;
      double width = (double) position.width;
      position = this.position;
      double num = (double) position.height - 40.0;
      HighlighterHelper.FromPosition(0.0f, 14f, (float) width, (float) num, "Event List");
    }

    public override void DoGUI()
    {
      if (this.updateEventList)
      {
        this.BuildFilteredList();
        this.updateEventList = false;
      }
      if (EditorApplication.isPlaying && FsmEditorSettings.DisableEventBrowserWhenPlaying)
      {
        GUILayout.Label(Strings.EventsWindow_Disabled_When_Playing);
        FsmEditorSettings.DisableEventBrowserWhenPlaying = !GUILayout.Toggle(!FsmEditorSettings.DisableEventBrowserWhenPlaying, Strings.EventsWindow_Enable_When_Playing);
        if (!GUI.changed)
          return;
        FsmEditorSettings.SaveSettings();
      }
      else
      {
        this.DoToolbar();
        this.DoTableHeaders();
        this.DoEventTable();
        if (Event.current.type == UnityEngine.EventType.ContextClick)
          GlobalEventsWindow.GenerateEventManagerMenu().ShowAsContext();
        this.DoBottomPanel();
        if (Event.current.type == UnityEngine.EventType.MouseUp && Event.current.button == 0 && GUIUtility.hotControl == 0)
          GUIUtility.keyboardControl = 0;
        if (Event.current.type != UnityEngine.EventType.MouseDown || GUIUtility.keyboardControl != 0)
          return;
        this.selectedEvent = (FsmEvent) null;
        this.Repaint();
      }
    }

    private void DoToolbar()
    {
      EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
      GUILayout.Space(4f);
      this.searchBox.OnGUI();
      if (GUILayout.Button(FsmEditorContent.RefreshEventList, EditorStyles.toolbarButton, GUILayout.Width(60f)))
        GlobalEventsWindow.ResetView();
      HighlighterHelper.FromGUILayout("Refresh");
      if (FsmEditorGUILayout.ToolbarSettingsButton())
        GlobalEventsWindow.GenerateEventManagerMenu().ShowAsContext();
      EditorGUILayout.EndHorizontal();
    }

    private void UpdateSearchResults()
    {
      this.searchString = this.searchBox.SearchString;
      this.BuildFilteredList();
    }

    private void BuildFilteredList()
    {
      FsmEvent.SanityCheckEventList();
      this.UpdateUseCount();
      List<FsmEvent> fsmEventList;
      switch (this.searchBox.SearchMode)
      {
        case 0:
          fsmEventList = new List<FsmEvent>((IEnumerable<FsmEvent>) FsmEvent.EventList);
          break;
        case 1:
          fsmEventList = FsmEvent.EventList.Where<FsmEvent>((Func<FsmEvent, bool>) (p => p.IsGlobal)).ToList<FsmEvent>();
          break;
        case 2:
          fsmEventList = FsmEvent.EventList.Where<FsmEvent>((Func<FsmEvent, bool>) (p => !p.IsGlobal)).ToList<FsmEvent>();
          break;
        case 3:
          fsmEventList = FsmEvent.EventList.Where<FsmEvent>((Func<FsmEvent, bool>) (p => p.IsSystemEvent)).ToList<FsmEvent>();
          break;
        default:
          fsmEventList = new List<FsmEvent>((IEnumerable<FsmEvent>) FsmEvent.EventList);
          break;
      }
      if (string.IsNullOrEmpty(this.searchString))
      {
        this.eventList = new List<GlobalEventsWindow.EventLine>();
        foreach (FsmEvent fsmEvent in fsmEventList)
          this.eventList.Add(new GlobalEventsWindow.EventLine(fsmEvent));
        this.eventList.Sort(new Comparison<GlobalEventsWindow.EventLine>(GlobalEventsWindow.CompareEventsByName));
      }
      else
      {
        this.eventList.Clear();
        string upper = this.searchString.ToUpper();
        foreach (FsmEvent fsmEvent in fsmEventList)
        {
          if (fsmEvent.Name.ToUpper().Contains(upper))
            this.eventList.Add(new GlobalEventsWindow.EventLine(fsmEvent));
        }
        this.eventList.Sort(new Comparison<GlobalEventsWindow.EventLine>(GlobalEventsWindow.CompareEventsByName));
      }
    }

    private static int CompareEventsByName(
      GlobalEventsWindow.EventLine event1,
      GlobalEventsWindow.EventLine event2)
    {
      if (event1.fsmEvent.IsSystemEvent && !event2.fsmEvent.IsSystemEvent)
        return -1;
      return !event1.fsmEvent.IsSystemEvent && event2.fsmEvent.IsSystemEvent ? 1 : string.Compare(event1.fsmEvent.Name, event2.fsmEvent.Name, StringComparison.Ordinal);
    }

    private void DoTableHeaders()
    {
      EditorGUILayout.BeginHorizontal();
      bool changed = GUI.changed;
      GUI.changed = false;
      GUILayout.BeginHorizontal(FsmEditorStyles.TopBarBG);
      GUILayout.Label(new GUIContent((Texture) FsmEditorStyles.BroadcastIcon, Strings.Tooltip_Global_Event_Flag), GUILayout.Width(18f), GUILayout.Height(18f));
      GUILayout.Space(2f);
      this.sortByUsageCount = !GUILayout.Toggle(!this.sortByUsageCount, new GUIContent(Strings.Label_Event, Strings.Tooltip_Event_GUI), FsmEditorStyles.TableRowHeader);
      GUILayout.FlexibleSpace();
      this.sortByUsageCount = GUILayout.Toggle((this.sortByUsageCount ? 1 : 0) != 0, new GUIContent(Strings.Label_Used, Strings.Tooltip_Events_Used), FsmEditorStyles.TableRowHeader, GUILayout.MaxWidth(40f));
      GUILayout.Space(20f);
      GUILayout.EndHorizontal();
      if (!GUI.changed)
        GUI.changed = changed;
      EditorGUILayout.EndHorizontal();
      if (!FsmEditorSettings.ShowHints)
        return;
      GUILayout.Box(Strings.Hint_Events_Window, FsmEditorStyles.HintBox);
    }

    private void DoEventTable()
    {
      this.scrollView.Begin(true);
      foreach (GlobalEventsWindow.EventLine eventLine in this.eventList)
        this.DoEventLine(eventLine);
      GUILayout.Space(20f);
      this.scrollView.End();
      GUILayout.FlexibleSpace();
    }

    [Localizable(false)]
    private void DoEventLine(GlobalEventsWindow.EventLine eventLine)
    {
      int count = eventLine.count;
      if (count == 0 && FsmEditorSettings.HideUnusedEvents)
        return;
      GUILayout.BeginHorizontal(this.selectedEvent == null || !(eventLine.fsmEvent.Name == this.selectedEvent.Name) ? FsmEditorStyles.TableRowBox : FsmEditorStyles.SelectedEventBox);
      EditorGUI.BeginDisabledGroup(eventLine.fsmEvent.IsSystemEvent);
      bool isGlobal = GUILayout.Toggle((eventLine.fsmEvent.IsGlobal ? 1 : 0) != 0, new GUIContent("", Strings.Label_Global), FsmEditorStyles.TableRowCheckBox, GUILayout.MaxWidth(17f), GUILayout.MinWidth(17f));
      if (isGlobal != eventLine.fsmEvent.IsGlobal)
        EditPlayMakerGlobals.SetEventIsGlobal(eventLine.fsmEvent, isGlobal);
      EditorGUI.EndDisabledGroup();
      GUIStyle style = this.selectedEvent == null || !(eventLine.fsmEvent.Name == this.selectedEvent.Name) ? FsmEditorStyles.TableRowText : FsmEditorStyles.TableRowTextSelected;
      if (GUILayout.Button(eventLine.label, style, GUILayout.MinWidth(this.position.width - 100f)))
      {
        this.SelectEvent(eventLine.fsmEvent);
        if (Event.current.button == 1 || EditorGUI.actionKey)
          this.GenerateUsageContextMenu(this.selectedEvent).ShowAsContext();
        if (EditorApplication.timeSinceStartup - this.clickTime < 0.3)
          this.AddSelectedEventToState();
        this.clickTime = EditorApplication.timeSinceStartup;
      }
      GUILayout.FlexibleSpace();
      GUILayout.Label(eventLine.countLabel, style);
      GUILayout.Space(10f);
      EditorGUI.BeginDisabledGroup(eventLine.fsmEvent.IsSystemEvent);
      if (FsmEditorGUILayout.DeleteButton())
      {
        string dialogDeleteEvent = Strings.Dialog_Delete_Event;
        string deleteEventAreYouSure = Strings.Dialog_Delete_Event_Are_you_sure;
        string str;
        if (count <= 0)
          str = "";
        else
          str = "\n\n" + Strings.Dialog_Delete_Event_Used_By + (object) count + (count > 1 ? (object) Strings.Label_Postfix_FSMs_Plural : (object) Strings.Label_Postfix_FSM);
        string message = string.Format(deleteEventAreYouSure, (object) str);
        if (Dialogs.YesNoDialog(dialogDeleteEvent, message))
        {
          EditorCommands.DeleteEventFromAll(eventLine.fsmEvent);
          FsmEditor.EventsManager.Reset();
          FsmEvent.RemoveEventFromEventList(eventLine.fsmEvent);
          if (eventLine.fsmEvent.IsGlobal)
          {
            PlayMakerGlobals.RemoveGlobalEvent(eventLine.fsmEvent.Name);
            FsmEditor.SaveGlobals();
          }
          this.BuildFilteredList();
          this.Repaint();
        }
      }
      EditorGUI.EndDisabledGroup();
      GUILayout.EndHorizontal();
      if (eventLine.fsmEvent != this.selectedEvent)
        return;
      this.scrollView.SetAutoScrollTarget(GUILayoutUtility.GetLastRect());
    }

    private void DoEventEditor()
    {
      FsmEditorGUILayout.PanelDivider();
      if ((UnityEngine.Object) EditorWindow.focusedWindow != (UnityEngine.Object) this || this.selectedEvent == null || this.selectedEvent.IsSystemEvent)
        return;
      EditorGUILayout.BeginHorizontal();
      GUILayout.Label(Strings.Command_Rename, GUILayout.MaxWidth(80f));
      this.newEventName = EditorGUILayout.TextField(this.newEventName);
      EditorGUILayout.EndHorizontal();
      string text = FsmEventManager.ValidateRenameEvent(this.selectedEvent, this.newEventName);
      bool flag = string.IsNullOrEmpty(text);
      if (!flag)
        GUILayout.Box(text, FsmEditorStyles.ErrorBox);
      if (!Event.current.isKey)
        return;
      if (flag && Keyboard.EnterKeyPressed())
      {
        this.RenameEvent();
        GUIUtility.ExitGUI();
      }
      if (Event.current.keyCode != KeyCode.Escape)
        return;
      this.newEventName = this.selectedEvent.Name;
    }

    public void UpdateUseCount()
    {
      if (EditorApplication.isPlayingOrWillChangePlaymode)
        return;
      GlobalEventsWindow.usageCount.Clear();
      foreach (FsmEvent fsmEvent in FsmEvent.EventList)
      {
        int num = GlobalEventsWindow.CountEventUsage(fsmEvent);
        GlobalEventsWindow.usageCount.Add(fsmEvent, num);
      }
      this.updateEventList = true;
    }

    public void UpdateUseCount(string eventName)
    {
      if (EditorApplication.isPlayingOrWillChangePlaymode)
        return;
      FsmEvent fsmEvent = FsmEvent.EventList.Find((Predicate<FsmEvent>) (x => x.Name == eventName));
      if (fsmEvent == null)
        return;
      GlobalEventsWindow.usageCount.Remove(fsmEvent);
      int num = GlobalEventsWindow.CountEventUsage(fsmEvent);
      GlobalEventsWindow.usageCount.Add(fsmEvent, num);
      this.updateEventList = true;
    }

    private static int CountEventUsage(FsmEvent fsmEvent)
    {
      if (fsmEvent == null)
        return 0;
      int num = 0;
      foreach (Fsm fsm in FsmEditor.FsmList)
      {
        foreach (FsmEvent fsmEvent1 in fsm.Events)
        {
          if (fsmEvent.Name == fsmEvent1.Name)
          {
            ++num;
            break;
          }
        }
      }
      return num;
    }

    private GenericMenu GenerateUsageContextMenu(FsmEvent fsmEvent)
    {
      this.UpdateUseCount();
      GenericMenu genericMenu = new GenericMenu();
      int num;
      GlobalEventsWindow.usageCount.TryGetValue(fsmEvent, out num);
      if (num == 0)
      {
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_No_FSMs_Use_This_Event));
        genericMenu.AddSeparator("");
        if (FsmEditor.SelectedFsm == null || FsmEditor.SelectedFsm.HasEvent(this.selectedEvent.Name))
          genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Add_Event_to_FSM));
        else
          genericMenu.AddItem(new GUIContent(Strings.Menu_Add_Event_to_FSM), false, new GenericMenu.MenuFunction(this.AddSelectedEventToState));
        return genericMenu;
      }
      List<string> labels = new List<string>();
      foreach (Fsm fsm in FsmEditor.FsmList)
      {
        if (fsm != null && fsm.OwnerObject != (UnityEngine.Object) null)
        {
          foreach (FsmEvent fsmEvent1 in fsm.Events)
          {
            if (fsmEvent1.Name == fsmEvent.Name)
            {
              string uniqueLabel = Labels.GenerateUniqueLabel(labels, Labels.GetFullFsmLabel(fsm));
              genericMenu.AddItem(new GUIContent(uniqueLabel), FsmEditor.SelectedFsm == fsm, new GenericMenu.MenuFunction2(EditorCommands.SelectFsm), (object) fsm);
              labels.Add(uniqueLabel);
            }
          }
        }
      }
      genericMenu.AddSeparator("");
      if (FsmEditor.SelectedFsm == null || FsmEditor.SelectedFsm.HasEvent(this.selectedEvent.Name))
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Add_Event_to_FSM));
      else
        genericMenu.AddItem(new GUIContent(Strings.Menu_Add_Event_to_FSM), false, new GenericMenu.MenuFunction(this.AddSelectedEventToState));
      return genericMenu;
    }

    public static void SyncSelection(FsmEvent fsmEvent)
    {
      if (!((UnityEngine.Object) GlobalEventsWindow.instance != (UnityEngine.Object) null))
        return;
      GlobalEventsWindow.instance.SelectEvent(fsmEvent);
      GlobalEventsWindow.instance.Repaint();
    }

    private void SelectEvent(FsmEvent fsmEvent)
    {
      if (fsmEvent == null)
        return;
      if (Event.current != null)
        GUIUtility.keyboardControl = 0;
      this.newEventName = !fsmEvent.IsSystemEvent ? fsmEvent.Name : "";
      this.selectedEvent = fsmEvent;
      FsmEditor.EventsManager.SelectEvent(fsmEvent, false);
      this.scrollView.AutoScroll();
    }

    private void RenameEvent()
    {
      GlobalEventsWindow.DeselectAll();
      FsmEditor.EventsManager.DeselectAll();
      FsmEditor.RepaintAll();
    }

    private void AddSelectedEventToState()
    {
      if (this.selectedEvent == null || FsmEditor.SelectedFsm == null || FsmEditor.SelectedFsm.HasEvent(this.selectedEvent.Name))
        return;
      EditorCommands.AddEvent(this.selectedEvent.Name);
      FsmEditor.Inspector.ResetView();
      GUIHelpers.SafeExitGUI();
    }

    private void DoBottomPanel()
    {
      GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG);
      GUILayout.BeginHorizontal();
      bool enabled = GUI.enabled;
      if (FsmEditor.SelectedFsm == null || this.selectedEvent == null || FsmEditor.SelectedFsm.HasEvent(this.selectedEvent.Name))
        GUI.enabled = false;
      if (GUILayout.Button(Strings.Command_Add_Selected_Event_To_FSM))
        this.AddSelectedEventToState();
      GUI.enabled = enabled;
      if (FsmEditorGUILayout.HelpButton())
        EditorCommands.OpenWikiPage(WikiPages.EventBrowser);
      GUILayout.EndHorizontal();
      GUILayout.EndVertical();
    }

    private static GenericMenu GenerateEventManagerMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent(Strings.Menu_Hide_Unused_Events), FsmEditorSettings.HideUnusedEvents, new GenericMenu.MenuFunction(GlobalEventsWindow.ToggleHideUnusedEvents));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Disable_Window_When_Playing), FsmEditorSettings.DisableEventBrowserWhenPlaying, new GenericMenu.MenuFunction(GlobalEventsWindow.ToggleDisableWindow));
      return genericMenu;
    }

    private static void ToggleHideUnusedEvents()
    {
      FsmEditorSettings.HideUnusedEvents = !FsmEditorSettings.HideUnusedEvents;
      FsmEditorSettings.SaveSettings();
    }

    private static void ToggleDisableWindow()
    {
      FsmEditorSettings.DisableEventBrowserWhenPlaying = !FsmEditorSettings.DisableEventBrowserWhenPlaying;
      FsmEditorSettings.SaveSettings();
    }

    private class EventLine
    {
      public readonly FsmEvent fsmEvent;
      public readonly GUIContent label;
      public readonly int count;
      public readonly string countLabel;

      public EventLine(FsmEvent fsmEvent)
      {
        this.fsmEvent = fsmEvent;
        this.label = new GUIContent(fsmEvent.Name, EventEditorData.GetEventTooltip(fsmEvent));
        this.count = 0;
        GlobalEventsWindow.usageCount.TryGetValue(fsmEvent, out this.count);
        this.countLabel = this.count > 0 ? this.count.ToString((IFormatProvider) CultureInfo.CurrentCulture) : "-";
      }
    }
  }
}
