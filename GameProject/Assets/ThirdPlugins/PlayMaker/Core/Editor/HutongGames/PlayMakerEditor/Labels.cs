// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Labels
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public static class Labels
  {
    private static readonly Dictionary<string, string> niceVariableNames = new Dictionary<string, string>();
    private static readonly Dictionary<System.Type, string> shortTypeNames = new Dictionary<System.Type, string>();
    private static readonly Dictionary<System.Type, string> actionNames = new Dictionary<System.Type, string>();
    private static readonly Dictionary<System.Type, string> typeTooltips = new Dictionary<System.Type, string>();
    private static readonly Dictionary<Fsm, string> fsmName = new Dictionary<Fsm, string>();
    private static readonly Dictionary<Fsm, string> fsmFullName = new Dictionary<Fsm, string>();
    private static readonly List<string> existingLabels = new List<string>();

    public static void Update(Fsm fsm)
    {
      if (fsm == null)
        return;
      Labels.fsmName.Remove(fsm);
      Labels.fsmFullName.Remove(fsm);
    }

    public static string EnforceNamingConvention(string s) => StringHelper.EnforceNamingConvention(s, FsmEditorSettings.VariableNamingConvention);

    public static string NicifyVariableName(string name)
    {
      string str1;
      if (Labels.niceVariableNames.TryGetValue(name, out str1))
        return str1;
      string str2 = Labels.ReplaceStart(ObjectNames.NicifyVariableName(name).Replace("Vector 2", "Vector2").Replace("Vector 3", "Vector3").Replace("Vector2XY", "Vector2 XY").Replace("Vector3XYZ", "Vector3 XYZ").Replace("I Tween", "iTween").Replace("I Phone", "iPhone").Replace("i Phone", "iPhone").Replace("Player Prefs", "PlayerPrefs").Replace("Network View ", "NetworkView ").Replace("Master Server ", "MasterServer ").Replace("Rpc ", "RPC ").Replace("Collision 2d", "Collision2D").Replace("Trigger 2d", "Trigger2D").Replace(" Ui ", " UI "), "Ui ", "UI ").Replace("A Cosine", "ACosine").Replace("A Sine", "ASine").Replace("Atan 2", "Atan2");
      Labels.niceVariableNames.Add(name, str2);
      return str2;
    }

    private static string ReplaceStart(string text, string oldValue, string newValue) => !text.StartsWith(oldValue) ? text : text.Replace(oldValue, newValue);

    public static string StripNamespace(string name) => name?.Substring(name.LastIndexOf(".", StringComparison.Ordinal) + 1);

    public static string StripUnityEngineNamespace(string name)
    {
      if (name == null)
        return (string) null;
      return name.IndexOf("UnityEngine.", StringComparison.Ordinal) != 0 ? name : name.Replace("UnityEngine.", "");
    }

    public static string FormatTime(float time) => new DateTime(Convert.ToInt64(Mathf.Max(time, 0.0f) * 1E+07f), DateTimeKind.Unspecified).ToString("mm:ss:ff");

    public static string GenerateUniqueLabelWithNumber(List<string> labels, string label)
    {
      int num = 2;
      string str = label;
      object[] objArray;
      for (; labels.Contains(label); label = string.Concat(objArray))
        objArray = new object[4]
        {
          (object) str,
          (object) " (",
          (object) num++,
          (object) ")"
        };
      return label;
    }

    public static void StartUniqueLabels() => Labels.existingLabels.Clear();

    public static string GetUniqueLabel(string label) => Labels.GenerateUniqueLabel(Labels.existingLabels, label);

    public static string GenerateUniqueLabel(List<string> labels, string label)
    {
      while (labels.Contains(label))
        label += " ";
      return label;
    }

    public static string NicifyParameterName(string name) => Labels.NicifyVariableName(Labels.StripNamespace(name));

    public static string GetStateLabel(string stateName) => !string.IsNullOrEmpty(stateName) ? stateName : "None (State)";

    public static GUIContent GetEventLabel(FsmTransition transition)
    {
      FsmEditorContent.EventLabel.text = "...";
      FsmEditorContent.EventLabel.tooltip = "";
      if (!FsmEvent.IsNullOrEmpty(transition.FsmEvent))
        FsmEditorContent.EventLabel.text = transition.FsmEvent.Name;
      return FsmEditorContent.EventLabel;
    }

    public static string GetCurrentStateLabel(Fsm fsm)
    {
      if (EditorApplication.isPlaying)
        return fsm.ActiveState != null ? fsm.ActiveState.Name : "No Active State";
      FsmState state = fsm.GetState(fsm.StartState);
      return state != null ? state.Name : "No Start State";
    }

    public static string GetFsmLabel(Fsm fsm)
    {
      if (fsm == null)
        return "None (Fsm)";
      string str1;
      if (Labels.fsmName.TryGetValue(fsm, out str1))
        return str1;
      string str2 = fsm.IsSubFsm ? fsm.Host.Name + " : " + fsm.Name : fsm.Name;
      int fsmNameIndex = Labels.GetFsmNameIndex(fsm);
      if (fsmNameIndex > 0)
        str2 = str2 + " (" + (object) (fsmNameIndex + 1) + ")";
      Labels.fsmName.Add(fsm, str2);
      return str2;
    }

    public static string GetFullFsmLabel(PlayMakerFSM fsmComponent) => !((UnityEngine.Object) fsmComponent == (UnityEngine.Object) null) ? Labels.GetFullFsmLabel(fsmComponent.Fsm) : "None (PlayMakerFSM)";

    public static string GetFullFsmLabel(Fsm fsm)
    {
      if (fsm == null)
        return "None (FSM)";
      if (fsm.OwnerObject == (UnityEngine.Object) null)
        return "Missing Owner";
      string str1;
      if (Labels.fsmFullName.TryGetValue(fsm, out str1))
        return str1;
      string str2;
      if ((UnityEngine.Object) fsm.UsedInTemplate != (UnityEngine.Object) null)
      {
        str2 = "Template: " + fsm.UsedInTemplate.name;
      }
      else
      {
        str2 = fsm.OwnerName + " : " + Labels.GetFsmLabel(fsm);
        if (FsmEditorSettings.AddPrefabLabel && FsmPrefabs.IsPrefab(fsm))
          str2 += " (Prefab)";
      }
      Labels.fsmFullName.Add(fsm, str2);
      return str2;
    }

    public static string GetRuntimeFsmLabel(Fsm fsm)
    {
      if (fsm == null)
        return "None (FSM)";
      return (UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null ? fsm.Name : fsm.OwnerName + " : " + fsm.Name;
    }

    public static string GetFullFsmLabelWithInstanceID(Fsm fsm)
    {
      string str = string.Empty;
      if (fsm != null && fsm.OwnerObject != (UnityEngine.Object) null)
        str = " [" + (object) fsm.OwnerObject.GetInstanceID() + "]";
      return Labels.GetFullFsmLabel(fsm) + str;
    }

    public static string GetFullFsmLabelWithInstanceID(PlayMakerFSM fsm)
    {
      string str = string.Empty;
      if ((UnityEngine.Object) fsm != (UnityEngine.Object) null)
        str = " [" + (object) fsm.GetInstanceID() + "]";
      return Labels.GetFullFsmLabel(fsm) + str;
    }

    public static GUIContent GetRuntimeFsmLabelToFit(
      Fsm fsm,
      float width,
      GUIStyle style)
    {
      string runtimeFsmLabel = Labels.GetRuntimeFsmLabel(fsm);
      return (double) style.CalcSize(new GUIContent(runtimeFsmLabel)).x < (double) width ? new GUIContent(runtimeFsmLabel, runtimeFsmLabel) : new GUIContent(fsm.Name, runtimeFsmLabel);
    }

    public static string GetFullStateLabel(FsmState state) => state == null ? "None (State)" : Labels.GetFullFsmLabel(state.Fsm) + " : " + state.Name;

    public static string GetUniqueFsmName(GameObject go)
    {
      PlayMakerFSM[] components = go.GetComponents<PlayMakerFSM>();
      string name = Strings.FSM;
      int num = 1;
      for (; Labels.FsmNameExists((IEnumerable<PlayMakerFSM>) components, name); name = "FSM " + (object) num)
        ++num;
      return name;
    }

    private static bool FsmNameExists(IEnumerable<PlayMakerFSM> fsmComponents, string name)
    {
      foreach (PlayMakerFSM fsmComponent in fsmComponents)
      {
        if (fsmComponent.Fsm.Name == name)
          return true;
      }
      return false;
    }

    public static string GetShortTypeName(System.Type type)
    {
      if (type == null)
        return "";
      string str1;
      if (Labels.shortTypeNames.TryGetValue(type, out str1))
        return str1;
      string str2 = Labels.StripNamespace(type.FullName);
      Labels.shortTypeNames.Add(type, str2);
      return str2;
    }

    public static string GetActionLabel(System.Type actionType)
    {
      if (actionType == null)
        return "";
      string str;
      if (!Labels.actionNames.TryGetValue(actionType, out str))
      {
        str = Labels.NicifyVariableName(Labels.StripNamespace(actionType.ToString()));
        Labels.actionNames.Add(actionType, str);
      }
      return str;
    }

    public static string GetActionLabel(FsmStateAction action)
    {
      if (action == null)
        return Strings.Label_None_Action;
      if (!string.IsNullOrEmpty(action.Name))
        return action.Name;
      return !string.IsNullOrEmpty(action.DisplayName) ? action.DisplayName : Labels.GetActionLabel(action.GetType());
    }

    public static void UpdateAutoName(FsmStateAction action)
    {
      if (action == null || !string.IsNullOrEmpty(action.Name))
        return;
      action.DisplayName = action.AutoName();
    }

    public static string GetTypeTooltip(System.Type type)
    {
      if (type == null)
        return "";
      string str1;
      if (Labels.typeTooltips.TryGetValue(type, out str1))
        return str1;
      string str2 = "Type: ";
      string tooltip;
      if (type == typeof (FsmOwnerDefault))
        tooltip = str2 + "GameObject";
      else if (type == typeof (FsmEvent))
        tooltip = str2 + "FsmEvent";
      else if (type == typeof (FsmVar))
        tooltip = str2 + "FsmVar";
      else if (type == typeof (FsmArray))
        tooltip = str2 + "Array";
      else if (type.IsSubclassOf(typeof (NamedVariable)))
      {
        PropertyInfo property = type.GetProperty("Value", BindingFlags.DeclaredOnly | BindingFlags.Instance | BindingFlags.Public);
        tooltip = property == null ? str2 + "Unknown" : str2 + Labels.StripUnityEngineNamespace(TypeHelpers.GetFriendlyName(property.PropertyType));
      }
      else
        tooltip = !type.IsArray ? str2 + TypeHelpers.GetFriendlyName(type) : Labels.GetTypeTooltip(type.GetElementType()) + " Array";
      Labels.typeTooltips.Add(type, Labels.NicifyTypeTooltip(tooltip));
      return tooltip;
    }

    private static string NicifyTypeTooltip(string tooltip)
    {
      if (tooltip == "Single")
        return Strings.Label_Float;
      return tooltip == "FsmOwnerDefault" ? Strings.Label_GameObject : tooltip;
    }

    public static int GetFsmIndex(Fsm fsm)
    {
      if (fsm == null || (UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null || (UnityEngine.Object) fsm.GameObject == (UnityEngine.Object) null)
        return -1;
      int num = 0;
      foreach (PlayMakerFSM component in fsm.GameObject.GetComponents<PlayMakerFSM>())
      {
        if (component.Fsm == fsm)
          return num;
        ++num;
      }
      return -1;
    }

    public static int GetFsmNameIndex(Fsm fsm)
    {
      if (fsm == null || (UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null || (UnityEngine.Object) fsm.GameObject == (UnityEngine.Object) null)
        return 0;
      int num = 0;
      foreach (PlayMakerFSM component in fsm.GameObject.GetComponents<PlayMakerFSM>())
      {
        if (component.Fsm == fsm)
          return num;
        if (component.Fsm.Name == fsm.Name)
          ++num;
      }
      return 0;
    }

    public static string GetUniqueVariableName(FsmVariables variables, string name)
    {
      int num = 2;
      string str = name;
      while (variables.Contains(name))
      {
        name = str + " " + (object) num;
        ++num;
      }
      return name;
    }

    public static string GetUniqueVariableName(FsmVariables variables, UnityEngine.Object obj) => Labels.GetUniqueVariableName(variables, obj.GetType().Name);
  }
}
