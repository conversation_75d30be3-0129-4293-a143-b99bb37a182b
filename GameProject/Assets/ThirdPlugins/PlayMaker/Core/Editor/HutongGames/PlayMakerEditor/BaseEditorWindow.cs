// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.BaseEditorWindow
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using JetBrains.Annotations;
using System;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public abstract class BaseEditorWindow : EditorWindow, IHasCustomMenu
  {
    public bool Initialized;
    protected bool isToolWindow;
    protected bool needsMainEditor;
    protected Event currentEvent;
    protected UnityEngine.EventType eventType;
    protected bool isRepaint;
    private bool justEnabled;
    private float lastWindowWidth;
    private float lastWindowHeight;

    protected virtual void OnEnable()
    {
      FsmEditorSettings.LoadSettings();
      this.justEnabled = true;
      EditorApplication.update -= new EditorApplication.CallbackFunction(this.CheckForWindowResize);
      EditorApplication.update += new EditorApplication.CallbackFunction(this.CheckForWindowResize);
      Undo.undoRedoPerformed += new Undo.UndoRedoCallback(((EditorWindow) this).Repaint);
      Selection.selectionChanged += new Action(((EditorWindow) this).Repaint);
      FsmEditor.OnFsmChanged += new Action<Fsm>(this.OnFsmChanged);
      this.InitWindowTitle();
    }

    [UsedImplicitly]
    private void OnHierarchyChange() => this.Repaint();

    [UsedImplicitly]
    private void OnDisable()
    {
      Undo.undoRedoPerformed -= new Undo.UndoRedoCallback(((EditorWindow) this).Repaint);
      Selection.selectionChanged -= new Action(((EditorWindow) this).Repaint);
      FsmEditor.OnFsmChanged -= new Action<Fsm>(this.OnFsmChanged);
    }

    public abstract void Initialize();

    public virtual void OnWindowResized()
    {
    }

    public virtual void OnFsmChanged(Fsm fsm)
    {
    }

    public virtual void InitWindowTitle()
    {
    }

    protected void SetTitle(string titleText)
    {
      this.titleContent.text = titleText;
      this.titleContent.image = (Texture) FsmEditorStyles.WanTabIcon;
    }

    protected void SetMinSize(Vector2 _minSize)
    {
      Rect position;
      if ((double) this.position.height < (double) this.minSize.y)
      {
        position = this.position;
        double x = (double) position.x;
        position = this.position;
        double y1 = (double) position.y;
        position = this.position;
        double width = (double) position.width;
        double y2 = (double) _minSize.y;
        this.position = new Rect((float) x, (float) y1, (float) width, (float) y2);
      }
      position = this.position;
      if ((double) position.width < (double) this.minSize.x)
      {
        position = this.position;
        double x1 = (double) position.x;
        position = this.position;
        double y = (double) position.y;
        double x2 = (double) _minSize.x;
        position = this.position;
        double height = (double) position.height;
        this.position = new Rect((float) x1, (float) y, (float) x2, (float) height);
      }
      this.minSize = _minSize;
    }

    protected virtual void DoUpdateHighlightIdentifiers()
    {
    }

    public void OnGUI()
    {
      if (this.justEnabled)
      {
        this.Initialize();
        this.justEnabled = false;
        this.Initialized = true;
      }
      if (this.isToolWindow && !FsmEditorGUILayout.ToolWindowsCommonGUI((EditorWindow) this))
        return;
      this.currentEvent = Event.current;
      this.eventType = this.currentEvent.type;
      this.isRepaint = this.eventType == UnityEngine.EventType.Repaint;
      HighlighterHelper.Init((EditorWindow) this);
      HighlighterHelper.FromWindowArea("Window");
      if (this.isRepaint)
      {
        HighlighterHelper.FromWindowArea(this.titleContent.text);
        this.DoUpdateHighlightIdentifiers();
      }
      this.DoGUI();
      HighlighterHelper.OnGUI();
    }

    public abstract void DoGUI();

    protected void LargeHeader(string header)
    {
      FsmEditorStyles.Init();
      FsmEditorGUILayout.ToolWindowLargeTitle((EditorWindow) this, header);
    }

    public void SafeClose()
    {
      EditorApplication.update -= new EditorApplication.CallbackFunction(this.CheckForWindowResize);
      HighlighterHelper.Reset(this.GetType());
      if (this.justEnabled)
        return;
      this.Close();
    }

    public static void CloseAllWindowsThatNeedMainEditor()
    {
      foreach (BaseEditorWindow baseEditorWindow in Resources.FindObjectsOfTypeAll<BaseEditorWindow>())
      {
        if (baseEditorWindow.needsMainEditor || baseEditorWindow.isToolWindow)
          baseEditorWindow.SafeClose();
      }
    }

    protected void CheckForWindowResize()
    {
      if ((double) Math.Abs(this.position.width - this.lastWindowWidth) > 1.40129846432482E-45 || (double) Math.Abs(this.position.height - this.lastWindowHeight) > 1.40129846432482E-45)
        this.OnWindowResized();
      Rect position = this.position;
      this.lastWindowWidth = position.width;
      position = this.position;
      this.lastWindowHeight = position.height;
    }

    public virtual void AddItemsToMenu(GenericMenu menu)
    {
    }
  }
}
