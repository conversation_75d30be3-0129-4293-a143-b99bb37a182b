// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmControls
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class FsmControls
  {
    public bool UnityInspectorMode;
    public Action OnReset;
    public int controlsCount;
    private Fsm fsm;
    private PlayMakerFSM fsmComponent;
    private float panelWidth = 200f;
    private bool needsReset;
    private List<FsmVariable> inputVariables = new List<FsmVariable>();
    private Owner owner;
    private Rect checkboxRect;
    private Rect fsmTitleRect;

    private static bool showEnableCheckBoxes
    {
      get => FsmEditorSettings.FsmControlsShowEnabledCheckboxes;
      set
      {
        FsmEditorSettings.FsmControlsShowEnabledCheckboxes = value;
        FsmEditorSettings.SaveSettings();
      }
    }

    private static bool showEmptyFSMs
    {
      get => FsmEditorSettings.FsmControlsShowEmptyFSMs;
      set
      {
        FsmEditorSettings.FsmControlsShowEmptyFSMs = value;
        FsmEditorSettings.SaveSettings();
      }
    }

    private FsmTemplate SelectedTemplate => this.fsm == null ? (FsmTemplate) null : this.fsm.Template;

    public FsmControls(Fsm targetFsm) => this.SetTarget(targetFsm);

    ~FsmControls() => FsmEditor.OnFsmControlsChanged -= new Action<Fsm>(this.CheckIfResetIsNeeded);

    private void SetTarget(Fsm targetFsm)
    {
      if (this.fsm == targetFsm)
        return;
      this.fsm = targetFsm;
      this.Reset();
    }

    [Localizable(false)]
    private void Init()
    {
      if (this.fsm == null)
        return;
      this.owner = Owner.Create((object) this.fsm);
      this.fsmComponent = this.fsm.FsmComponent;
      this.BuildControls();
      FsmEditor.UpdateFsmInfo(this.fsm);
      FsmEditor.OnFsmControlsChanged -= new Action<Fsm>(this.CheckIfResetIsNeeded);
      FsmEditor.OnFsmControlsChanged += new Action<Fsm>(this.CheckIfResetIsNeeded);
    }

    public void Reset()
    {
      this.RefreshTemplate();
      this.Init();
      if (this.OnReset == null)
        return;
      this.OnReset();
    }

    private void CheckIfResetIsNeeded(Fsm changedFsm)
    {
      if (changedFsm != this.fsm && (!((UnityEngine.Object) this.fsmComponent != (UnityEngine.Object) null) || !((UnityEngine.Object) changedFsm.UsedInTemplate == (UnityEngine.Object) this.fsmComponent.FsmTemplate)))
        return;
      this.needsReset = true;
    }

    public static void AddSettingsMenuItems(GenericMenu menu)
    {
      menu.AddItem(new GUIContent("Show Enabled Checkboxes"), FsmControls.showEnableCheckBoxes, (GenericMenu.MenuFunction) (() => FsmControls.showEnableCheckBoxes = !FsmControls.showEnableCheckBoxes));
      menu.AddItem(new GUIContent("Show FSMs With No Controls"), FsmControls.showEmptyFSMs, (GenericMenu.MenuFunction) (() => FsmControls.showEmptyFSMs = !FsmControls.showEmptyFSMs));
    }

    public void OnGUI()
    {
      if (this.fsm == null)
      {
        GUILayout.FlexibleSpace();
      }
      else
      {
        if (this.needsReset)
          this.Reset();
        FsmEditorStyles.Init();
        this.panelWidth = EditorGUIUtility.currentViewWidth;
        EditorGUIUtility.labelWidth = this.panelWidth * 0.4f;
        EditorGUIUtility.hierarchyMode = this.UnityInspectorMode;
        FsmVariableEditor.UnityInspectorMode = this.UnityInspectorMode;
        this.owner.BeginEditing("Edit FSM");
        this.DoControlsGUI();
        if (!this.owner.EndEditing())
          return;
        this.UpdateExposedVariables();
      }
    }

    private void UpdateExposedVariables()
    {
      foreach (FsmVariable inputVariable in this.inputVariables)
      {
        if (inputVariable.NamedVar != null)
          inputVariable.NamedVar.Init();
      }
    }

    private void DoControlsGUI()
    {
      if ((UnityEngine.Object) this.fsmComponent == (UnityEngine.Object) null || !FsmControls.showEmptyFSMs && this.controlsCount == 0)
        return;
      Rect rect = GUILayoutUtility.GetRect(GUIContent.none, GUIStyle.none, GUILayout.Height(26f));
      if (this.UnityInspectorMode)
      {
        rect.x -= 4f;
        rect.width += 4f;
      }
      if (Event.current.type == UnityEngine.EventType.Repaint)
        FsmEditorStyles.SectionHeader.Draw(rect);
      if (FsmControls.showEnableCheckBoxes)
      {
        this.checkboxRect.Set(rect.x + 2f, rect.yMax - 24f, 16f, 16f);
        this.fsmComponent.enabled = GUI.Toggle(this.checkboxRect, this.fsmComponent.enabled, "");
      }
      int num = FsmControls.showEnableCheckBoxes ? 18 : 4;
      this.fsmTitleRect.Set(rect.x + (float) num, rect.yMax - 23f, this.panelWidth - (float) num, 18f);
      if (GUI.Button(this.fsmTitleRect, new GUIContent(this.fsm.Name, this.fsm.Description), FsmEditorStyles.BoldLabel))
      {
        if (Event.current.button == 0)
        {
          FsmEditor.Open(this.fsmComponent);
        }
        else
        {
          GenericMenu menu = new GenericMenu();
          FsmControls.AddSettingsMenuItems(menu);
          menu.ShowAsContext();
        }
      }
      EditorGUIUtility.AddCursorRect(rect, MouseCursor.Link);
      if (this.inputVariables.Count > 0)
        FsmVariable.DoVariableListGUI(this.inputVariables);
      this.DoEventButtons();
      GUILayout.Space(10f);
      if (!GUI.changed)
        return;
      FsmEditor.RepaintAll();
    }

    private void DoEventButtons()
    {
      Fsm targetFsm = Application.isPlaying ? this.fsm : ((UnityEngine.Object) this.fsm.Template != (UnityEngine.Object) null ? this.fsm.Template.fsm : this.fsm);
      if (targetFsm == null || targetFsm.ExposedEvents.Count == 0)
        return;
      FsmInspector.DoEventButtons(targetFsm);
    }

    private void BuildControls()
    {
      if (this.fsm != null)
      {
        this.BuildFsmVariableList();
        this.controlsCount = this.inputVariables.Count + this.fsm.ExposedEvents.Count;
      }
      else
        this.controlsCount = 0;
    }

    [Localizable(false)]
    private void BuildFsmVariableList()
    {
      List<FsmVariable> fsmVariableList = FsmVariable.GetFsmVariableList(this.owner);
      foreach (FsmVariable fsmVariable in fsmVariableList)
        fsmVariable.NamedVar.Init();
      this.inputVariables = fsmVariableList.Where<FsmVariable>((Func<FsmVariable, bool>) (x => x.ShowInInspector)).ToList<FsmVariable>();
    }

    private void RefreshTemplate()
    {
      if ((UnityEngine.Object) this.fsmComponent == (UnityEngine.Object) null || (UnityEngine.Object) this.SelectedTemplate == (UnityEngine.Object) null)
        return;
      FsmVariables source = new FsmVariables(this.fsm.Variables);
      this.fsmComponent.SetFsmTemplate(this.SelectedTemplate);
      this.fsm.Variables.OverrideVariableValues(source);
      this.BuildFsmVariableList();
      if (Event.current == null)
        return;
      HandleUtility.Repaint();
    }

    [Localizable(false)]
    [Conditional("DEBUG_LOG")]
    private void DebugLog(string text) => UnityEngine.Debug.Log((object) string.Format("FsmControls: {0} {1}", (object) text, (object) FsmUtility.GetFsmLabel(this.fsm)));
  }
}
