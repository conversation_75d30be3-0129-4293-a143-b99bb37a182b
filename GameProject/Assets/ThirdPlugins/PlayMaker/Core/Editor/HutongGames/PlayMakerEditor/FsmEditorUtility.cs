// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmEditorUtility
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public static class FsmEditorUtility
  {
    [Obsolete("Removed")]
    public static string[] StateColorNames = new string[0];

    [Obsolete("Use Actions.List instead.")]
    public static List<System.Type> Actionslist => Actions.List;

    [Obsolete("Use Actions.CategoryLookup instead.")]
    public static List<string> ActionCategoryLookup => Actions.CategoryLookup;

    [Obsolete("Use Actions.Categories instead.")]
    public static List<string> ActionCategoryList => Actions.Categories;

    [Obsolete("Use Actions.GetUsage instead.")]
    public static List<FsmInfo> GetActionUsage(System.Type actionType) => Actions.GetUsage(actionType);

    [Obsolete("Use Actions.GetUsageCount instead.")]
    public static int GetActionUsageCount(System.Type actionType) => Actions.GetUsageCount(actionType);

    [Obsolete("Use Actions.BuildList instead.")]
    public static void BuildActionsList() => Actions.BuildList();

    [Obsolete("Use Actions.UpdateUsage instead.")]
    public static void UpdateActionUsage() => Actions.UpdateUsage();

    [Obsolete("Use Actions.GetCategory instead.")]
    public static string GetActionCategory(System.Type actionType) => Actions.GetCategory(actionType);

    [Obsolete("Use Actions.GetActionIndex instead.")]
    public static int GetActionIndex(FsmState state, FsmStateAction action) => Actions.GetActionIndex(state, action);

    [Obsolete("Use Actions.GetTooltip instead.")]
    public static string GetActionTooltip(FsmStateAction action) => Actions.GetTooltip(action);

    [Obsolete("Use Actions.UpdateTooltip instead.")]
    public static string UpdateActionTooltip(FsmStateAction action) => Actions.UpdateTooltip(action);

    [Obsolete("Use Actions.GetActionCategory instead.")]
    public static string GetActionCategoryAttribute(System.Type objType) => Actions.GetActionCategory(objType);

    [Obsolete("Use Actions.GetActionCategory instead.")]
    public static string GetActionCategoryAttribute(object[] attributes) => Actions.GetActionCategory(attributes);

    [Obsolete("Use Actions.GetTooltip instead.")]
    public static string GetTooltipAttribute(object instance) => Actions.GetTooltip(instance);

    [Obsolete("Use Actions.GetTooltip instead.")]
    public static string GetTooltipAttribute(System.Type objType) => Actions.GetTooltip(objType);

    [Obsolete("Use Actions.GetTooltip instead.")]
    public static string GetTooltipAttribute(FieldInfo field) => Actions.GetTooltip(field);

    [Obsolete("Use Actions.GetTooltip instead.")]
    public static string GetTooltipAttribute(object[] attributes) => Actions.GetTooltip(attributes);

    [Obsolete("Use Fsm.HasVariable extension method instead.")]
    public static bool FsmHasVariable(Fsm fsm, string name) => fsm.HasVariable(name);

    [Obsolete("Use Labels.NicifyVariableName instead.")]
    public static string NicifyVariableName(string name) => Labels.NicifyVariableName(name);

    [Obsolete("Use Labels.NicifyParameterName instead.")]
    public static string NicifyParameterName(string name) => Labels.NicifyParameterName(name);

    [Obsolete("Use Labels.StripNamespace instead.")]
    public static string StripNamespace(string name) => Labels.StripNamespace(name);

    [Obsolete("Use Labels.StripUnityEngineNamespace instead.")]
    public static string StripUnityEngineNamespace(string name) => Labels.StripUnityEngineNamespace(name);

    [Obsolete("Use Labels.GenerateUniqueLabel instead.")]
    public static string GenerateUniqueLabel(List<string> labels, string label) => Labels.GenerateUniqueLabel(labels, label);

    [Obsolete("Use Labels.GenerateUniqueLabelWithNumber instead.")]
    public static string GenerateUniqueLabelWithNumber(List<string> labels, string label) => Labels.GenerateUniqueLabelWithNumber(labels, label);

    [Obsolete("Use Labels.FormateTime instead.")]
    public static string FormatTime(float time) => Labels.FormatTime(time);

    [Obsolete("Use Labels.GetStateLabel instead.")]
    public static string GetStateLabel(string stateName) => Labels.GetStateLabel(stateName);

    [Obsolete("Use Labels.GetEventLabel instead.")]
    public static GUIContent GetEventLabel(FsmTransition transition) => Labels.GetEventLabel(transition);

    [Obsolete("Use Labels.GetCurrentStateLabel instead.")]
    public static string GetCurrentStateLabel(Fsm fsm) => Labels.GetCurrentStateLabel(fsm);

    [Obsolete("Use Labels.GetFsmLabel instead.")]
    public static string GetFsmLabel(Fsm fsm) => Labels.GetFsmLabel(fsm);

    [Obsolete("Use Labels.GetFullFsmLabel instead.")]
    public static string GetFullFsmLabel(PlayMakerFSM fsmComponent) => Labels.GetFullFsmLabel(fsmComponent);

    [Obsolete("Use Labels.GetFullFsmLabel instead.")]
    public static string GetFullFsmLabel(Fsm fsm) => Labels.GetFullFsmLabel(fsm);

    [Obsolete("Use Labels.GetRuntimeFsmLabel instead.")]
    public static string GetRuntimeFsmLabel(Fsm fsm) => Labels.GetRuntimeFsmLabel(fsm);

    [Obsolete("Use Labels.GetFullFsmLabelWithInstanceID instead.")]
    public static string GetFullFsmLabelWithInstanceID(Fsm fsm) => Labels.GetFullFsmLabelWithInstanceID(fsm);

    [Obsolete("Use Labels.GetFullFsmLabelWithInstanceID instead.")]
    public static string GetFullFsmLabelWithInstanceID(PlayMakerFSM fsm) => Labels.GetFullFsmLabelWithInstanceID(fsm);

    [Obsolete("Use Labels.GetRuntimeFsmLabelToFit instead")]
    public static GUIContent GetRuntimeFsmLabelToFit(
      Fsm fsm,
      float width,
      GUIStyle style)
    {
      return Labels.GetRuntimeFsmLabelToFit(fsm, width, style);
    }

    [Obsolete("Use Labels.GetFullStateLabel instead.")]
    public static string GetFullStateLabel(FsmState state) => Labels.GetFullStateLabel(state);

    [Obsolete("Use Labels.GetUniqueFsmName instead.")]
    public static string GenerateUniqueFsmName(GameObject go) => Labels.GetUniqueFsmName(go);

    [Obsolete("Use Labels.GetActionLabel instead.")]
    public static string GetActionLabel(System.Type actionType) => Labels.GetActionLabel(actionType);

    [Obsolete("Use Labels.GetActionLabel instead.")]
    public static string GetActionLabel(FsmStateAction action) => Labels.GetActionLabel(action);

    [Obsolete("Use Labels.GetTypeTooltip instead.")]
    public static string GetTypeTooltip(System.Type type) => Labels.GetTypeTooltip(type);

    [Obsolete("Use Labels.GetFsmIndex instead.")]
    public static int GetFsmIndex(Fsm fsm) => Labels.GetFsmIndex(fsm);

    [Obsolete("Use Labels.GetFsmNameIndex instead.")]
    public static int GetFsmNameIndex(Fsm fsm) => Labels.GetFsmNameIndex(fsm);

    [Obsolete("Use GlobalsAsset.Export instead.")]
    public static void ExportGlobals() => GlobalsAsset.Export();

    [Obsolete("Use GlobalsAsset.Import instead.")]
    public static void ImportGlobals() => GlobalsAsset.Import();

    [Obsolete("Use FsmVariable.Sort instead.")]
    public static void SortFsmVariables(Fsm fsm)
    {
    }

    [Obsolete("Use FsmVariable.Sort instead.")]
    public static void SortFsmVariables(FsmVariables fsmVariables)
    {
    }

    [Obsolete("Use Watermarks.GetLabel instead.")]
    public static string GetWatermarkLabel(PlayMakerFSM fsmComponent, string defaultLabel = "No Watermark") => Watermarks.GetLabel(fsmComponent, defaultLabel);

    [Obsolete("Use Watermarks.Set instead.")]
    public static Texture SetWatermarkTexture(Fsm fsm, string textureName) => Watermarks.Set(fsm, textureName);

    [Obsolete("Use Watermarks.Get instead.")]
    public static Texture GetWatermarkTexture(Fsm fsm) => Watermarks.Get(fsm);

    [Obsolete("Use Watermarks.Load instead.")]
    public static Texture LoadWatermarkTexture(string name) => Watermarks.Load(name);

    [Obsolete("Use Watermarks.GetNames instead.")]
    public static string[] GetWatermarkNames() => Watermarks.GetNames();

    [Obsolete("Use Watermarks.GetTextures instead.")]
    public static Texture[] GetWatermarkTextures(bool showProgress = true) => Watermarks.GetTextures(showProgress);

    [Obsolete("Use ActionScripts.ActionScriptLookup instead.")]
    public static Dictionary<System.Type, UnityEngine.Object> ActionScriptLookup => ActionScripts.ActionScriptLookup;

    [Obsolete("Use ActionScripts.PingAsset instead.")]
    public static void FindActionScript(object userdata) => ActionScripts.PingAsset(userdata);

    [Obsolete("Use ActionScripts.EditAsset instead.")]
    public static void EditActionScript(object userdata) => ActionScripts.EditAsset(userdata);

    [Obsolete("Use ActionScripts.PingAssetByType instead.")]
    public static void FindActionTypeScript(object userdata) => ActionScripts.PingAssetByType(userdata);

    [Obsolete("Use ActionScripts.SelectAssetByType instead.")]
    public static void SelectActionTypeScript(object userdata) => ActionScripts.SelectAssetByType(userdata);

    [Obsolete("Use ActionScripts.EditAssetByType instead.")]
    public static void EditActionTypeScript(object userdata) => ActionScripts.EditAssetByType(userdata);

    [Obsolete("Use ActionScripts.GetAsset instead.")]
    public static UnityEngine.Object GetActionScriptAsset(FsmStateAction action) => ActionScripts.GetAsset(action);

    [Obsolete("Use ActionScripts.GetAsset instead.")]
    public static UnityEngine.Object GetActionScriptAsset(System.Type actionType) => ActionScripts.GetAsset(actionType);

    [Obsolete("Use FsmVariable.GetVariableType instead.")]
    public static System.Type GetUIHintVariableType(UIHint hint) => EditNamedVariable.GetVariableType(hint);

    [Obsolete("Use Templates.List instead.")]
    public static List<FsmTemplate> TemplateList => Templates.List;

    [Obsolete("Use Templates.Categories instead.")]
    public static List<string> TemplateCategories => Templates.Categories;

    [Obsolete("Use Templates.CategoryLookup instead.")]
    public static Dictionary<FsmTemplate, string> TemplateCategoryLookup => Templates.CategoryLookup;

    [Obsolete("Use Templates.LoadAll instead.")]
    public static void LoadAllTemplates() => Templates.LoadAll();

    [Obsolete("Use Templates.InitList instead.")]
    public static void BuildTemplateList() => Templates.InitList();

    [Obsolete("Use Templates.SortList instead.")]
    public static void SortTemplateList() => Templates.SortList();

    [Obsolete("Use Templates.DoSelectTemplateMenu instead.")]
    public static void DoSelectTemplateMenu(
      FsmTemplate SelectedTemplate,
      GenericMenu.MenuFunction ClearTemplate,
      GenericMenu.MenuFunction2 SelectTemplate)
    {
      Templates.DoSelectTemplateMenu(SelectedTemplate, ClearTemplate, SelectTemplate);
    }

    [Obsolete("Use FsmEventManager.SanityCheckEventList instead.")]
    public static void SanityCheckEventList(Fsm fsm) => FsmEventManager.SanityCheckEventList(fsm);

    [Obsolete("Use Events.GetFsmTarget instead.")]
    public static Fsm GetFsmTarget(Fsm fsm, FsmEventTarget fsmEventTarget) => Events.GetFsmTarget(fsm, fsmEventTarget);

    [Obsolete("Use Events.FsmStateRespondsToEvent instead.")]
    public static bool FsmStateRespondsToEvent(FsmState state, FsmEvent fsmEvent) => Events.FsmStateRespondsToEvent(state, fsmEvent);

    [Obsolete("Use Events.FsmRespondsToEvent instead.")]
    public static bool FsmRespondsToEvent(Fsm fsm, FsmEvent fsmEvent) => Events.FsmRespondsToEvent(fsm, fsmEvent);

    [Obsolete("Use Events.FsmRespondsToEvent instead.")]
    public static bool FsmRespondsToEvent(Fsm fsm, string fsmEventName) => Events.FsmRespondsToEvent(fsm, fsmEventName);

    [Obsolete("Use Events.GetGlobalEventList instead.")]
    public static List<FsmEvent> GetGlobalEventList() => Events.GetGlobalEventList();

    [Obsolete("Use Events.GetGlobalEventList instead.")]
    public static List<FsmEvent> GetGlobalEventList(Fsm fsm) => Events.GetGlobalEventList(fsm);

    [Obsolete("Use Events.GetEventList instead.")]
    public static List<FsmEvent> GetEventList(Fsm fsm) => Events.GetEventList(fsm);

    [Obsolete("Use Events.GetEventList instead.")]
    public static List<FsmEvent> GetEventList(PlayMakerFSM fsmComponent) => Events.GetEventList(fsmComponent);

    [Obsolete("Use Events.GetEventList instead.")]
    public static List<FsmEvent> GetEventList(GameObject go) => Events.GetEventList(go);

    [Obsolete("Use Events.GetEventNamesFromList instead.")]
    public static GUIContent[] GetEventNamesFromList(List<FsmEvent> eventList) => Events.GetEventNamesFromList(eventList);

    [Obsolete("Use Events.EventListContainsEventName instead.")]
    public static bool EventListContainsEventName(List<FsmEvent> eventList, string fsmEventName) => Events.EventListContainsEventName(eventList, fsmEventName);

    [Obsolete("Use Files.LoadTextureFromDll instead.")]
    public static Texture2D LoadDllResource(string resourceName, int width, int height) => Files.LoadTextureFromDll(resourceName, width, height);

    [Obsolete("Use Files.LoadAllAssetsOfType instead.")]
    public static void LoadAllAssetsOfType(string type) => Files.LoadAllAssetsOfType(type);

    [Obsolete("Use Files.GetFiles instead.")]
    public static string[] GetFiles(string path, string searchPattern, SearchOption searchOption = SearchOption.TopDirectoryOnly) => Files.GetFiles(path, searchPattern, searchOption);

    [Obsolete("Use Files.CreateFilePath instead.")]
    public static bool CreateFilePath(string fullFileName) => Files.CreateFilePath(fullFileName);

    [Obsolete("Use Files.ScriptList instead.")]
    public static List<string> ScriptList => Files.ScriptList;

    [Obsolete("Use Files.BuildScriptList instead.")]
    public static void BuildScriptList() => Files.BuildScriptList();

    [Obsolete("Use Files.LoadAllPlaymakerPrefabs instead.")]
    public static List<string> LoadAllPrefabsInProject() => Files.LoadAllPlaymakerPrefabs();

    [Obsolete("Removed. Use CustomAttributeHelpers.GetAttribute<> instead.")]
    public static System.Type GetCustomActionEditorAttribute(System.Type objType) => (System.Type) null;

    [Obsolete("Removed. Use CustomAttributeHelpers.GetAttribute<> instead.")]
    public static System.Type GetCustomActionEditorAttribute(object[] attributes) => (System.Type) null;

    [Obsolete("Removed. Use CustomAttributeHelpers.GetAttribute<> instead.")]
    public static System.Type GetPropertyDrawerAttribute(System.Type objType) => (System.Type) null;

    [Obsolete("Removed. Use CustomAttributeHelpers.GetAttribute<> instead.")]
    public static System.Type GetPropertyDrawerAttribute(object[] attributes) => (System.Type) null;

    [Obsolete("Removed. Use CustomAttributeHelpers.GetAttribute<> instead.")]
    public static System.Type GetObjectPropertyDrawerAttribute(System.Type objType) => (System.Type) null;

    [Obsolete("Removed. Use CustomAttributeHelpers.GetAttribute<> instead.")]
    public static System.Type GetObjectPropertyDrawerAttribute(object[] attributes) => (System.Type) null;

    [Obsolete("Removed. Use CustomAttributeHelpers.GetAttribute<> instead.")]
    public static string GetHelpUrlAttribute(System.Type objType) => (string) null;

    [Obsolete("Removed. Use CustomAttributeHelpers.GetAttribute<> instead.")]
    public static string GetHelpUrlAttribute(object[] attributes) => (string) null;

    [Obsolete("Removed. Use CustomAttributeHelpers.GetActionSection instead.")]
    public static string GetFieldSection(object[] attributes) => (string) null;

    [Obsolete("Use FsmEditorSettings.PackColorIntoInt instead.")]
    public static int PackColorIntoInt(Color color) => FsmEditorSettings.PackColorIntoInt(color);

    [Obsolete("Use FsmEditorSettings.UnpackColorFromInt instead.")]
    public static Color UnpackColorFromInt(int packedValue) => FsmEditorSettings.UnpackColorFromInt(packedValue);

    [Obsolete("Use FsmState.GetStateIndex instead.")]
    public static int GetStateIndex(FsmState state) => FsmState.GetStateIndex(state);

    [Obsolete("Use FsmPrefabs.LoadUsedPrefabs instead.")]
    public static void LoadUsedPrefabs() => FsmPrefabs.LoadUsedPrefabs();

    [Obsolete("Use FsmPrefabs.IsModifiedPrefabInstance instead.")]
    public static bool IsModifiedPrefabInstance(Fsm fsm) => FsmPrefabs.IsModifiedPrefabInstance(fsm);

    [Obsolete("Use FsmPrefabs.UpdateIsModifiedPrefabInstance instead.")]
    public static void UpdateIsModifiedPrefabInstance(Fsm fsm) => FsmPrefabs.UpdateIsModifiedPrefabInstance(fsm);

    [Obsolete("Use FsmPrefabs.ShouldModify instead.")]
    public static bool ShouldModify(Fsm fsm) => FsmPrefabs.ShouldModify(fsm);

    [Obsolete("Use FsmPrefabs.IsPersistent instead.")]
    public static bool IsPersistent(UnityEngine.Object obj) => FsmPrefabs.IsPersistent(obj);

    [Obsolete("Use FsmPrefabs.IsPrefab instead.")]
    public static bool IsPrefab(Fsm fsm) => FsmPrefabs.IsPrefab(fsm);

    [Obsolete("Use FsmPrefabs.IsPrefabInstance instead.")]
    public static bool IsPersistent(Fsm fsm) => FsmPrefabs.IsPrefabInstance(fsm);

    [Obsolete("Use FsmPrefabs.IsPrefabInstance instead.")]
    public static bool IsPrefabInstance(Fsm fsm) => FsmPrefabs.IsPrefabInstance(fsm);

    [Obsolete("Use FsmPrefabs.IsFsmInstanceOfPrefab instead.")]
    public static bool IsFsmInstanceOfPrefab(Fsm fsm, Fsm prefab) => FsmPrefabs.IsFsmInstanceOfPrefab(fsm, prefab);

    [Obsolete("Use FsmPrefabs.BuildAssetsWithPlayMakerFSMsList instead.")]
    public static void BuildAssetsWithPlayMakerFSMsList() => FsmPrefabs.BuildAssetsWithPlayMakerFSMsList();

    [Obsolete("Use FsmPrefabs.AssetHasPlayMakerFSM instead.")]
    public static bool AssetHasPlayMakerFSM(string guid) => FsmPrefabs.AssetHasPlayMakerFSM(guid);

    [Obsolete("Use FsmPrefabs.StateExistsInPrefabParent instead.")]
    public static bool StateExistsInPrefabParent(FsmState state) => FsmPrefabs.StateExistsInPrefabParent(state);

    [Obsolete("Use TypeHelpers.ObjectTypeList instead.")]
    public static List<System.Type> ObjectTypeList => TypeHelpers.ObjectTypeList;

    [Obsolete("Use TypeHelpers.EnumTypeList instead.")]
    public static List<System.Type> EnumTypeList => TypeHelpers.EnumTypeList;

    [Obsolete("Use TypeHelpers.GetSerializedFields instead.")]
    public static List<System.Type> GetDerivedTypeList(System.Type ofType, bool includeBaseType = true) => TypeHelpers.GetDerivedTypeList(ofType, includeBaseType);

    [Obsolete("Use TypeHelpers.GetSerializedFields instead.")]
    public static IEnumerable<FieldInfo> GetSerializedFields(System.Type type) => TypeHelpers.GetSerializedFields(type);

    [Obsolete("Use TypeHelpers.GenerateObjectTypesMenu instead.")]
    public static GenericMenu GenerateObjectTypesMenu(FsmProperty fsmProperty) => TypeHelpers.GenerateObjectTypesMenu(fsmProperty);

    [Obsolete("Use TypeHelpers.GenerateObjectTypesMenu instead.")]
    public static GenericMenu GenerateObjectTypesMenu(FsmVariable fsmVariable) => TypeHelpers.GenerateObjectTypesMenu(fsmVariable);

    [Obsolete("Use TypeHelpers.GenerateEnumTypesMenu instead.")]
    public static GenericMenu GenerateEnumTypesMenu(FsmVariable fsmVariable) => TypeHelpers.GenerateEnumTypesMenu(fsmVariable);

    [Obsolete("Use TypeHelpers.GeneratePropertyMenu instead.")]
    public static GenericMenu GeneratePropertyMenu(FsmProperty fsmProperty) => TypeHelpers.GeneratePropertyMenu(fsmProperty);

    [Obsolete("Use TypeHelpers.IsSupportedParameterType instead.")]
    public static bool IsSupportedParameterType(System.Type parameterType) => TypeHelpers.IsSupportedParameterType(parameterType);

    [Obsolete("Removed")]
    public static void GenerateSubPropertyMenu(object userdata)
    {
    }

    [Obsolete("Removed")]
    public static void OpenSubPropertyMenu()
    {
    }

    [Obsolete("Removed")]
    public static Fsm GetGameObjectFSM(FsmGameObject go, FsmString fsmName) => (Fsm) null;

    [Obsolete("Use FsmSelection.FindFsmOnGameObject instead.")]
    public static Fsm FindFsmOnGameObject(GameObject go) => FsmSelection.FindFsmOnGameObject(go);

    [Obsolete("Use FsmSelection.FindFsmComponentOnGameObject instead.")]
    public static PlayMakerFSM FindFsmComponentOnGameObject(GameObject go) => FsmSelection.FindFsmComponentOnGameObject(go);

    [Obsolete("Use FsmSelection.FindFsmOnGameObject instead.")]
    public static Fsm FindFsmOnGameObject(GameObject go, string name) => FsmSelection.FindFsmOnGameObject(go, name);

    [Obsolete("Use FsmSelection.GameObjectHasFSM instead.")]
    public static bool GameObjectHasFSM(GameObject go) => FsmSelection.GameObjectHasFSM(go);
  }
}
