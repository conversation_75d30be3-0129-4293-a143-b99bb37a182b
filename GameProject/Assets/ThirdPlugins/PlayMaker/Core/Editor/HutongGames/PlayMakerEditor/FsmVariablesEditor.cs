// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmVariablesEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using JetBrains.Annotations;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class FsmVariablesEditor
  {
    private readonly EditorWindow window;
    private FsmVariables target;
    private Owner owner;
    private Fsm subFsm;
    private bool editingGlobalVariables;
    private bool editingAsset;
    private List<FsmVariable> fsmVariables = new List<FsmVariable>();
    private bool restoreSelection;
    private ScrollView scrollView;
    private Rect selectedRect;
    private FsmVariable selectedFsmVariable;
    private bool isVariableSelected;
    private int selectedIndex = -1;
    private Rect editArea;
    private TextField editNameTextField;
    private TextField addVariableTextField;
    private TextField categoryTextField;
    private readonly List<EditableLabel> categoryLabels = new List<EditableLabel>();
    private bool sortByVariableType;
    private VariableTypeMenu _typeMenu;
    private FsmVariable tempNewVariable;
    private bool draggingSplitter;
    private Rect splitterRect;
    private float fixedHeight;
    private List<FsmVariable> filteredVariables = new List<FsmVariable>();
    private string searchString;
    private SearchBox searchBox;
    private bool isRepaint;
    public FsmVariablesEditor.ContextClickVariable VariableContextClicked;
    public EditorApplication.CallbackFunction SettingsButtonClicked;
    public Action RefreshButtonClicked;
    private const string saveSelectionKey = "Playmaker.{0}.SelectedVariable";
    private bool needsReset;

    public FsmVariable SelectedVariable => this.selectedFsmVariable;

    private bool groupInputOutput => FsmEditorSettings.GroupVariableInputOutputs && !this.editingGlobalVariables;

    private VariableTypeMenu typeMenu => this._typeMenu ?? (this._typeMenu = new VariableTypeMenu());

    public FsmVariablesEditor(EditorWindow window, UnityEngine.Object owner)
    {
      this.window = window;
      this.SetTarget(owner);
    }

    public FsmVariablesEditor(EditorWindow window, Fsm fsm)
    {
      this.window = window;
      this.SetTarget(fsm);
    }

    public void SetTarget(UnityEngine.Object newOwner)
    {
      if (this.owner != null && newOwner == this.owner.Object)
        return;
      this.owner = Owner.Create((object) newOwner);
      this.target = this.owner.FsmVariables;
      this.subFsm = (Fsm) null;
      this.Reset();
    }

    public void SetTarget(Fsm fsm)
    {
      if (fsm == null)
      {
        this.target = (FsmVariables) null;
      }
      else
      {
        if (this.target == fsm.Variables)
          return;
        this.owner = Owner.Create((object) fsm);
        this.target = fsm.Variables;
        this.subFsm = fsm;
        this.Reset();
      }
    }

    public void Reset()
    {
      this.needsReset = false;
      this.editingGlobalVariables = this.owner.IsPlayMakerGlobals;
      this.editingAsset = this.owner.IsAsset;
      FsmEditor.ReloadFSM();
      if (!this.editingGlobalVariables && FsmEditorSettings.AutoRefreshFsmInfo && FsmSearch.FsmUsesGlobalVariables(this.owner.Fsm))
        FsmSearch.RefreshAll();
      this.BuildFsmVariableList();
      this.SanityCheckSelection();
      FsmEditor.Repaint(true);
      FsmEditor.RepaintAll();
      this.restoreSelection = true;
      this.tempNewVariable = new FsmVariable((object) null, (NamedVariable) new FsmFloat());
    }

    private void InitControls()
    {
      FsmVariableEditor.UnityInspectorMode = false;
      if (this.searchBox == null)
      {
        this.searchBox = new SearchBox(this.window)
        {
          SearchModes = new string[1]{ "Name" },
          HasPopupSearchModes = true
        };
        this.searchBox.SearchChanged += new EditorApplication.CallbackFunction(this.UpdateSearchResults);
      }
      if (this.categoryTextField != null)
        return;
      this.categoryTextField = new TextField(this.window, FsmEditorContent.EditCategoryLabel)
      {
        EditCommited = new TextField.EditCommitedCallback(this.SetVariableCategory),
        FocusLost = new TextField.LostFocusCallback(this.SetVariableCategory),
        AutoCompleteStrings = this.target.Categories
      };
      TextField textField1 = new TextField(this.window, FsmEditorContent.EditVariableLabel);
      textField1.EditCommited = new TextField.EditCommitedCallback(this.SetVariableName);
      textField1.FocusLost = new TextField.LostFocusCallback(this.ResetVariableName);
      textField1.ValidateText = new TextField.ValidateCallback(this.ValidateVariableName);
      textField1.ControlName = "EditName";
      this.editNameTextField = textField1;
      TextField textField2 = new TextField(this.window, FsmEditorContent.NewVariableLabel);
      textField2.EditCommited = new TextField.EditCommitedCallback(this.AddVariable);
      textField2.ValidateText = new TextField.ValidateCallback(this.ValidateVariableName);
      textField2.ControlName = "EditName";
      this.addVariableTextField = textField2;
    }

    public void UpdateView()
    {
      this.BuildFsmVariableList();
      FsmEditor.Repaint();
    }

    private void UpdateSearchResults()
    {
      this.searchString = this.searchBox.SearchString;
      this.BuildFilteredList();
    }

    private void BuildFilteredList()
    {
      if (string.IsNullOrEmpty(this.searchString))
      {
        this.filteredVariables = new List<FsmVariable>((IEnumerable<FsmVariable>) this.fsmVariables);
      }
      else
      {
        this.filteredVariables.Clear();
        string upper = this.searchString.ToUpper();
        foreach (FsmVariable fsmVariable in this.fsmVariables)
        {
          if (fsmVariable.Name.ToUpper().Contains(upper))
            this.filteredVariables.Add(fsmVariable);
          else if (this.selectedFsmVariable == fsmVariable)
          {
            this.Deselect();
            this.searchBox.Focus();
          }
        }
      }
    }

    private void SanityCheckSelection()
    {
      if (this.selectedFsmVariable == null)
        return;
      this.SelectVariable(this.selectedFsmVariable.Name);
    }

    private void SelectVariable(int index)
    {
      if (index < 0 || index > this.fsmVariables.Count - 1)
        return;
      this.SelectVariable(this.fsmVariables[index]);
    }

    public void SelectVariable(string name) => this.SelectVariable(this.GetVariable(name));

    public void SelectVariable(FsmVariable variable)
    {
      if (variable == null)
      {
        this.Deselect();
      }
      else
      {
        if (this.selectedFsmVariable != null)
          Keyboard.ResetFocus();
        this.isVariableSelected = true;
        this.selectedFsmVariable = variable;
        if (this.editNameTextField != null)
          this.editNameTextField.Text = variable.Name;
        if (this.categoryTextField != null)
          this.categoryTextField.Text = variable.GetCategory();
        if (this.scrollView != null)
          this.scrollView.AutoScroll();
        this.SaveSelection(variable.Name);
      }
    }

    public void Deselect()
    {
      this.fixedHeight = 0.0f;
      this.isVariableSelected = false;
      this.selectedFsmVariable = (FsmVariable) null;
      this.selectedIndex = -1;
      if (this.tempNewVariable == null)
        this.tempNewVariable = new FsmVariable((object) null, (NamedVariable) new FsmFloat());
      else
        this.tempNewVariable.Name = "";
      if (this.addVariableTextField != null)
        this.addVariableTextField.Text = "";
      FsmEditor.RepaintAll();
      Keyboard.ResetFocus();
      if (this.searchBox != null)
        this.searchBox.Focus();
      this.SaveSelection("");
    }

    [Localizable(false)]
    private void SaveSelection(string variableName)
    {
      if ((UnityEngine.Object) this.window == (UnityEngine.Object) null)
        return;
      EditorPrefs.SetString(string.Format("Playmaker.{0}.SelectedVariable", (object) this.window.titleContent.text), variableName);
    }

    [Localizable(false)]
    public void RestoreSelection() => this.SelectVariable(EditorPrefs.GetString(string.Format("Playmaker.{0}.SelectedVariable", (object) this.window.titleContent.text), ""));

    private void InitScrollView()
    {
      if (this.scrollView == null)
        this.scrollView = new ScrollView(this.window);
      this.scrollView.Animate = false;
    }

    public void OnGUI()
    {
      if (this.target == null || this.owner == null)
      {
        GUILayout.FlexibleSpace();
      }
      else
      {
        FsmEditorStyles.Init();
        this.InitScrollView();
        this.owner.BeginEditing("Edit Variable");
        this.InitControls();
        if (Event.current.type == UnityEngine.EventType.Layout && this.restoreSelection)
        {
          this.RestoreSelection();
          this.restoreSelection = false;
        }
        this.isRepaint = Event.current.type == UnityEngine.EventType.Repaint;
        this.DoToolbar();
        this.DoTableHeaders();
        this.DoVariableTable();
        if (Event.current.type == UnityEngine.EventType.ContextClick && this.SettingsButtonClicked != null)
          this.SettingsButtonClicked();
        this.DoVariableEditor();
        this.DoSplitter();
        this.HandleKeyboardInput();
        Keyboard.Update();
        this.HandleDragAndDrop();
        this.owner.EndEditing();
      }
    }

    private void DoSplitter()
    {
      if (this.selectedFsmVariable == null)
        return;
      Event current = Event.current;
      UnityEngine.EventType type = Event.current.type;
      Rect position = this.window.position;
      if (this.draggingSplitter)
        this.splitterRect.Set(0.0f, 0.0f, position.width, position.height);
      else
        this.splitterRect.Set(this.editArea.x, this.editArea.y - 4f, this.editArea.width, 6f);
      EditorGUIUtility.AddCursorRect(this.splitterRect, MouseCursor.ResizeVertical);
      if (this.splitterRect.Contains(current.mousePosition) && type == UnityEngine.EventType.MouseDown)
      {
        this.draggingSplitter = true;
        this.fixedHeight = this.editArea.height;
        current.Use();
      }
      if (!this.draggingSplitter)
        return;
      this.scrollView.AutoScroll();
      if (type == UnityEngine.EventType.MouseDrag && (double) Event.current.mousePosition.y > 150.0)
      {
        this.fixedHeight -= current.delta.y;
        this.fixedHeight = Mathf.Min(this.fixedHeight, this.window.position.height - 150f);
        current.Use();
      }
      if (current.rawType != UnityEngine.EventType.MouseUp)
        return;
      this.draggingSplitter = false;
    }

    private void DoToolbar()
    {
      EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
      GUILayout.Space(4f);
      this.searchBox.OnGUI();
      if (this.RefreshButtonClicked != null)
      {
        if (GUILayout.Button(FsmEditorContent.RefreshVariablesList, EditorStyles.toolbarButton, GUILayout.Width(60f)))
          this.RefreshButtonClicked();
        HighlighterHelper.FromGUILayout("Refresh");
      }
      if (FsmEditorGUILayout.ToolbarSettingsButton() && this.SettingsButtonClicked != null)
        this.SettingsButtonClicked();
      EditorGUILayout.EndHorizontal();
    }

    private void DoTableHeaders()
    {
      EditorGUI.BeginChangeCheck();
      GUILayout.BeginVertical();
      EditorGUILayout.BeginHorizontal();
      GUILayout.BeginVertical(FsmEditorStyles.TopBarBG);
      GUILayout.BeginHorizontal(FsmEditorStyles.TableRowBox);
      this.sortByVariableType = !GUILayout.Toggle(!this.sortByVariableType, FsmEditorContent.VariableNameLabel, FsmEditorStyles.TableRowHeader);
      GUILayout.FlexibleSpace();
      GUILayout.Label(FsmEditorContent.VariableUseCountLabel);
      GUILayout.Space(5f);
      this.sortByVariableType = GUILayout.Toggle(this.sortByVariableType, FsmEditorContent.VariableTypeLabel, FsmEditorStyles.TableRowHeader);
      GUILayout.Space((float) (123 - (this.scrollView.VerticalScrollbarVisible ? 0 : FsmEditorStyles.ScrollBarWidth)));
      GUILayout.EndHorizontal();
      EditorGUILayout.EndHorizontal();
      GUILayout.EndVertical();
      if (!EditorGUI.EndChangeCheck())
        return;
      this.UpdateView();
    }

    [Localizable(false)]
    private void DoVariableTable()
    {
      this.scrollView.Begin(true);
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box(this.owner.Fsm != null ? Strings.Hint_Variable_Panel : Strings.Hint_Global_Variables, FsmEditorStyles.HintBox);
      if (this.fsmVariables.Count == 0)
      {
        GUILayout.BeginHorizontal(FsmEditorStyles.TableRowBox);
        GUILayout.Label(Strings.Label_None_In_Table);
        GUILayout.EndHorizontal();
      }
      else if (this.filteredVariables.Count == 0)
        GUILayout.Label("No search results for: " + this.searchString);
      this.DoVariableListRows(this.filteredVariables);
      if (this.owner.Fsm != null)
        this.DoGlobalVariablesTable();
      if (!GUILayout.Button("", GUIStyle.none))
      {
        if (!GUILayout.Button("", GUIStyle.none, GUILayout.ExpandHeight(true)))
          goto label_11;
      }
      this.Deselect();
      GUI.changed = false;
      GUIUtility.ExitGUI();
label_11:
      GUILayout.Space(4f);
      this.scrollView.End();
      GUILayout.EndVertical();
      HighlighterHelper.FromGUILayout("Variables List");
    }

    private void DoVariableListRows(List<FsmVariable> variables)
    {
      int num1 = 0;
      int num2 = 0;
      bool flag1 = false;
      bool flag2 = false;
      for (int index1 = 0; index1 < variables.Count; ++index1)
      {
        FsmVariable variable = variables[index1];
        if (!flag1 && variable.InputSection)
        {
          GUILayout.Space(5f);
          GUILayout.Box(FsmEditorContent.InputsSection, FsmEditorStyles.SectionTitle);
          FsmEditorGUILayout.LightDivider();
          num1 = 0;
          num2 = 1;
          flag1 = true;
        }
        if (!flag2 && variable.OutputSection)
        {
          GUILayout.Space(5f);
          GUILayout.Box(FsmEditorContent.OutputsSection, FsmEditorStyles.SectionTitle);
          FsmEditorGUILayout.LightDivider();
          num1 = 0;
          num2 = 2;
          flag2 = true;
        }
        int categoryId = variable.CategoryID;
        if (categoryId > 0 && categoryId != num1)
        {
          num1 = categoryId;
          int index2 = categoryId * 3 + num2;
          if (index2 >= this.categoryLabels.Count)
            GUILayout.Label("ERROR: categoryID: " + (object) index2 + " controls: " + (object) this.categoryLabels.Count);
          else
            this.categoryLabels[index2].OnGUI();
        }
        bool isSelected = variable == this.selectedFsmVariable;
        if (isSelected)
          this.selectedIndex = index1;
        this.DoVariableRow(variable, isSelected);
      }
    }

    private void DoVariableRow(FsmVariable fsmVariable, bool isSelected)
    {
      Rect rect1 = GUILayoutUtility.GetRect(GUIContent.none, FsmEditorStyles.TableRowBox);
      if (this.isRepaint)
      {
        if (isSelected)
          FsmEditorStyles.SelectedEventBox.Draw(rect1);
        else
          FsmEditorStyles.TableRowBoxNoDivider.Draw(rect1);
      }
      GUIStyle style1;
      GUIStyle style2;
      if (isSelected)
      {
        style1 = FsmEditorStyles.TableRowTextSelected;
        style2 = FsmEditorStyles.TableRowTextSelectedAlignRight;
      }
      else
      {
        style1 = FsmEditorStyles.TableRowText;
        style2 = FsmEditorStyles.TableRowTextAlignRight;
      }
      Rect rect2 = new Rect(rect1)
      {
        x = (float) style1.margin.left
      };
      rect2.width = rect2.xMax - 184f - (float) style1.margin.right;
      GUI.Label(rect2, new GUIContent(fsmVariable.Name, fsmVariable.Tooltip), style1);
      int usedCount = this.GetUsedCount(fsmVariable.NamedVar);
      rect2.x = rect2.xMax + (float) style1.margin.right + (float) style2.margin.left;
      rect2.width = (float) (30 - style2.margin.right);
      if (this.editingGlobalVariables)
        GUI.Label(rect2, usedCount > 0 ? usedCount.ToString((IFormatProvider) CultureInfo.CurrentCulture) : "-", style2);
      else
        GUI.Label(rect2, usedCount >= 0 ? usedCount.ToString((IFormatProvider) CultureInfo.CurrentCulture) : "-", style2);
      rect2.x = rect2.xMax + 5f;
      rect2.y += 3f;
      rect2.height = EditorStyles.popup.fixedHeight;
      rect2.width = 118f;
      this.DoVariableTypePopup(rect2, fsmVariable);
      rect2.x = rect2.xMax + 2f;
      if (FsmEditorGUI.DeleteButton(rect2))
      {
        this.DeleteVariable(fsmVariable);
        GUIUtility.ExitGUI();
      }
      else
      {
        if (FsmEditorSettings.DebugVariables)
          VariableEditor.DebugVariableValue((INamedVariable) fsmVariable.NamedVar);
        if (Event.current.type == UnityEngine.EventType.MouseDown && rect1.Contains(Event.current.mousePosition))
        {
          this.SelectVariable(fsmVariable);
          if ((Event.current.button == 1 || EditorGUI.actionKey) && this.VariableContextClicked != null)
            this.VariableContextClicked(fsmVariable);
          Event.current.Use();
        }
        if (!isSelected)
          return;
        this.selectedRect = rect1;
        this.scrollView.SetAutoScrollTarget(this.selectedRect);
      }
    }

    private void DoVariableTypePopup(Rect rect, FsmVariable fsmVariable)
    {
      if (!GUI.Button(rect, fsmVariable.TypeLabel, EditorStyles.popup))
        return;
      this.SelectVariable(fsmVariable);
      this.typeMenu.DropDown(rect, fsmVariable, new GenericMenu.MenuFunction2(this.SelectVariableType));
    }

    private void DoVariableTypePopup(FsmVariable fsmVariable)
    {
      if (!GUILayout.Button(fsmVariable.TypeLabel, EditorStyles.popup, GUILayout.Width(114f)))
        return;
      this.SelectVariable(fsmVariable);
      this.typeMenu.Show(fsmVariable, new GenericMenu.MenuFunction2(this.SelectVariableType));
    }

    private void SelectVariableType(object userdata)
    {
      VariableTypeMenu.MenuItemSelectionData itemSelectionData = (VariableTypeMenu.MenuItemSelectionData) userdata;
      this.ChangeVariableType(this.SelectedVariable, itemSelectionData.variableType, itemSelectionData.objectType);
    }

    private void SelectNewVariableType(object userdata)
    {
      VariableTypeMenu.MenuItemSelectionData itemSelectionData = (VariableTypeMenu.MenuItemSelectionData) userdata;
      this.tempNewVariable.Type = itemSelectionData.variableType;
      this.tempNewVariable.ObjectType = itemSelectionData.objectType;
      this.tempNewVariable.TypeLabel = VariableTypeMenu.GetTypeLabel(this.tempNewVariable);
    }

    private void DoGlobalVariablesTable()
    {
      if (FsmSearch.GetGlobalVariablesUsedCount(FsmEditor.SelectedFsm) == 0)
        return;
      GUILayout.Space(10f);
      FsmEditorGUILayout.LightDivider();
      GUILayout.BeginHorizontal(FsmEditorStyles.TableRowBox);
      GUILayout.Label(FsmEditorContent.GlobalsLabel);
      GUILayout.FlexibleSpace();
      GUILayout.Label(FsmEditorContent.VariableUseCountLabel);
      GUILayout.EndHorizontal();
      foreach (NamedVariable variable in FsmSearch.GetGlobalVariablesUsed(FsmEditor.SelectedFsm))
      {
        GUILayout.BeginHorizontal(FsmEditorStyles.TableRowBox);
        GUIStyle tableRowText = FsmEditorStyles.TableRowText;
        if (GUILayout.Button(new GUIContent(variable.Name, variable.Tooltip), tableRowText))
        {
          Keyboard.ResetFocus();
          this.Deselect();
          if (Event.current.button == 1 || EditorGUI.actionKey)
            FsmVariablesEditor.DoGlobalVariableContextMenu(variable);
        }
        int variablesUsageCount = FsmSearch.GetGlobalVariablesUsageCount(variable);
        GUILayout.FlexibleSpace();
        GUILayout.Label(variablesUsageCount.ToString((IFormatProvider) CultureInfo.CurrentCulture), tableRowText);
        GUILayout.Space(10f);
        GUILayout.EndHorizontal();
        if (FsmEditorSettings.DebugVariables)
          FsmEditorGUILayout.ReadonlyTextField(variable.ToString());
      }
    }

    private static void DoGlobalVariableContextMenu(NamedVariable variable)
    {
      FsmSearch.RefreshAll();
      GenericMenu genericMenu = new GenericMenu();
      List<FsmInfo> variablesUsageList = FsmSearch.GetGlobalVariablesUsageList(variable);
      if (variablesUsageList.Count == 0)
      {
        genericMenu.AddDisabledItem(new GUIContent("No loaded FSMs use this variable..."));
      }
      else
      {
        foreach (FsmInfo fsmInfo in variablesUsageList)
        {
          if (fsmInfo.fsm == FsmEditor.SelectedFsm)
            genericMenu.AddItem(new GUIContent(fsmInfo.AsMenuItemShort()), FsmEditor.SelectedState == fsmInfo.state, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
        }
        foreach (FsmInfo fsmInfo in variablesUsageList)
        {
          if (fsmInfo.fsm != FsmEditor.SelectedFsm)
            genericMenu.AddItem(new GUIContent(Labels.GetFullFsmLabel(fsmInfo.fsm)), FsmEditor.SelectedFsm == fsmInfo.fsm, new GenericMenu.MenuFunction2(EditorCommands.SelectFsm), (object) fsmInfo.fsm);
        }
      }
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Make_Local_Variable), false, new GenericMenu.MenuFunction2(FsmVariablesEditor.MoveToLocal), (object) variable);
      genericMenu.ShowAsContext();
    }

    private static void MoveToLocal(object userdata)
    {
      if (!(userdata is NamedVariable namedVariable) || FsmEditor.SelectedFsm == null)
        return;
      if (FsmEditor.SelectedFsm.Variables.Contains(namedVariable.Name))
      {
        Dialogs.OkDialog(Strings.Dialog_Make_Local_Variable, Strings.Dialog_Error_Variable_with_same_name_already_exists);
      }
      else
      {
        FsmEditor.SelectedFsm.AddVariable(namedVariable.Clone());
        FsmEditor.SelectedFsm.Reload();
        FsmEditor.SetFsmDirty();
        FsmEditor.VariablesManager.Reset();
        FsmEditor.RepaintAll();
      }
    }

    [Localizable(false)]
    private int GetUsedCount(NamedVariable variable)
    {
      if (this.owner.Fsm != null)
        return FsmSearch.GetVariableUseCount(this.owner.Fsm, variable);
      return this.owner.IsPlayMakerGlobals ? FsmSearch.GetGlobalVariablesUsageCount(variable) : -1;
    }

    private void HandleKeyboardInput()
    {
      if (!Keyboard.IsGuiEventKeyboardShortcut())
        return;
      switch (Event.current.keyCode)
      {
        case KeyCode.Escape:
          this.Deselect();
          break;
        case KeyCode.UpArrow:
          this.SelectPrevious();
          break;
        case KeyCode.DownArrow:
          this.SelectNext();
          break;
        default:
          return;
      }
      Event.current.Use();
      GUIUtility.ExitGUI();
    }

    private void SelectPrevious()
    {
      if (this.selectedIndex == -1)
        this.SelectFirst();
      else
        this.SelectVariable(this.selectedIndex - 1);
    }

    private void SelectNext()
    {
      if (this.selectedIndex == -1)
        this.SelectLast();
      else
        this.SelectVariable(this.selectedIndex + 1);
    }

    private void SelectFirst() => this.SelectVariable(0);

    private void SelectLast() => this.SelectVariable(this.fsmVariables.Count - 1);

    private void DoVariableEditor()
    {
      EditorGUIUtility.labelWidth = 100f;
      if ((double) this.fixedHeight > 0.0)
        GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG, GUILayout.MinHeight(this.fixedHeight));
      else
        GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG);
      if (this.isVariableSelected)
      {
        this.selectedFsmVariable.BeginEditing();
        EditorGUI.BeginDisabledGroup(this.editingGlobalVariables);
        this.editNameTextField.OnGUI();
        EditorGUI.EndDisabledGroup();
        EditorGUI.BeginChangeCheck();
        this.EditVariableValue();
        this.categoryTextField.OnGUI();
        if (!this.editingGlobalVariables)
        {
          EditorGUI.BeginChangeCheck();
          this.selectedFsmVariable.DoIsInputField();
          this.selectedFsmVariable.DoIsOutputField();
          if (EditorGUI.EndChangeCheck())
          {
            this.NeedReset();
            this.UpdateControls();
          }
        }
        this.selectedFsmVariable.DoNetworkSyncField();
        if (EditorGUI.EndChangeCheck())
        {
          if (this.editingGlobalVariables)
            FsmEditor.RecordGlobalsUndo("Edit Global Variable");
          else
            FsmEditor.RecordUndo("Edit Variable");
          this.selectedFsmVariable.EndEditing();
          if (this.needsReset)
            this.Reset();
          GUI.changed = true;
          if (!this.editingGlobalVariables)
            FsmEditor.SetFsmDirty();
        }
        if (!this.editNameTextField.IsValid)
          GUILayout.Box(Strings.Error_Variable_Name_Taken, FsmEditorStyles.ErrorBox);
      }
      else
      {
        this.addVariableTextField.OnGUI();
        Rect rect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.textField);
        Rect position = new Rect(rect) { width = 102f };
        GUI.Label(position, FsmEditorContent.EditVariableTypeLabel);
        rect.width -= 139f;
        rect.x = position.xMax;
        GUIContent typeLabel = this.tempNewVariable.TypeLabel;
        if (FsmEditorGUI.ButtonNoGuiChanged(rect, typeLabel, EditorStyles.popup))
          this.typeMenu.DropDown(rect, this.tempNewVariable, new GenericMenu.MenuFunction2(this.SelectNewVariableType));
        rect.x = rect.xMax + 2f;
        rect.width = 35f;
        if (!FsmEditorStyles.UsingFlatStyle)
          --rect.height;
        EditorGUI.BeginDisabledGroup(!this.addVariableTextField.IsValid || this.addVariableTextField.Text.Length == 0);
        if (GUI.Button(rect, FsmEditorContent.AddLabel))
          this.AddVariable(this.addVariableTextField);
        EditorGUI.EndDisabledGroup();
        if (!this.addVariableTextField.IsValid)
          GUILayout.Box(Strings.Error_Variable_Name_Taken, FsmEditorStyles.ErrorBox);
      }
      HighlighterHelper.EndVertical("Variable Editor");
      if (this.isRepaint)
        this.editArea = GUILayoutUtility.GetLastRect();
      if (!FsmEditorSettings.ShowHints)
        return;
      GUILayout.Box(!string.IsNullOrEmpty(this.editNameTextField.Text) ? Strings.Hint_Inspector_Usage : Strings.Hint_Variable_Usage, FsmEditorStyles.HintBox);
    }

    private void EditVariableValue()
    {
      if (this.selectedFsmVariable == null)
        return;
      GUIContent label = FsmEditorContent.VariableValueLabel;
      label.tooltip = this.selectedFsmVariable.Type.ToString();
      if (this.selectedFsmVariable.Type == VariableType.Array)
        label = (GUIContent) null;
      this.selectedFsmVariable.DoSubTypeSelector();
      this.selectedFsmVariable.DoValuePropertyField(label, this.owner.IsAsset);
      EditorGUI.BeginChangeCheck();
      this.selectedFsmVariable.DoTooltipField((double) this.fixedHeight > 0.0);
      if (!EditorGUI.EndChangeCheck() || !this.selectedFsmVariable.IsControl)
        return;
      FsmEditor.OnControlsChanged();
    }

    private void NeedReset()
    {
      this.needsReset = true;
      GUI.changed = true;
    }

    private void SetVariableName(TextField textField)
    {
      if (textField.IsValid)
        this.RenameVariable(this.selectedFsmVariable, textField.Text);
      else
        textField.CancelTextEdit();
    }

    private void ResetVariableName(TextField textField) => textField.Text = this.SelectedVariable.Name;

    private void AddVariable(TextField textField)
    {
      if (string.IsNullOrEmpty(textField.Text))
        return;
      FsmVariable variable = this.AddVariable(textField.Text);
      if (!FsmEditorSettings.SelectNewVariables)
        return;
      this.SelectVariable(variable);
    }

    private void SetVariableCategory(TextField textField)
    {
      this.owner.RecordObject(Strings.Label_Set_Variable_Category);
      this.selectedFsmVariable.SetCategory(textField.Text);
      this.RemoveUnusedCategories();
      this.BuildFsmVariableList();
      if (!this.selectedFsmVariable.IsControl)
        return;
      FsmEditor.OnControlsChanged();
    }

    private void CategoryClicked(EditableLabel label)
    {
      this.Deselect();
      GUIHelpers.SafeExitGUI();
    }

    private void CommitCategoryEdit(EditableLabel label)
    {
      this.owner.RecordObject(Strings.Label_Rename_Variable_Category);
      this.target.Categories[label.ID] = label.Text;
      this.SetDirty();
      this.Reset();
    }

    public void DoCategoryContextMenu(EditableLabel label)
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent(Strings.Menu_Rename_Category), false, new GenericMenu.MenuFunction2(this.RenameCategory), (object) label);
      genericMenu.AddItem(new GUIContent(Strings.Menu_Delete_Category), false, new GenericMenu.MenuFunction2(this.DeleteCategory), (object) label);
      genericMenu.ShowAsContext();
    }

    private void RenameCategory(object userdata) => ((EditableLabel) userdata).StartEditing();

    private void DeleteCategory(object userdata)
    {
      string text = ((EditableLabel) userdata).Text;
      foreach (FsmVariable fsmVariable in this.fsmVariables)
      {
        if (fsmVariable.Category == text)
          fsmVariable.SetCategory("");
      }
      this.RemoveUnusedCategories();
      this.BuildFsmVariableList();
    }

    private bool ValidateVariableName(string newName) => this.isVariableSelected ? this.IsValidVariableRename(this.selectedFsmVariable.Name, newName) : !this.VariableNameExists(newName);

    private bool IsValidVariableRename(string currentName, string name)
    {
      if (string.IsNullOrEmpty(name))
        return false;
      foreach (FsmVariable fsmVariable in this.fsmVariables)
      {
        if (currentName != fsmVariable.Name && name == fsmVariable.Name)
          return false;
      }
      return true;
    }

    private bool VariableNameExists(string name)
    {
      foreach (FsmVariable fsmVariable in this.fsmVariables)
      {
        if (name == fsmVariable.Name)
          return true;
      }
      return false;
    }

    [Localizable(false)]
    public void BuildFsmVariableList(bool keepSelection = true)
    {
      if (this.target == null)
        return;
      FsmEditorStyles.Init();
      string name = "";
      if (this.selectedFsmVariable != null)
        name = this.selectedFsmVariable.Name;
      if (this.groupInputOutput)
      {
        this.fsmVariables = FsmVariable.GetUnsortedFsmVariableList(this.owner).Where<FsmVariable>((Func<FsmVariable, bool>) (x => !x.IsOutput && !x.IsInput)).OrderBy<FsmVariable, string>((Func<FsmVariable, string>) (x => x.GetCategory() + "_" + (this.sortByVariableType ? x.Type.ToString() : x.Name))).ToList<FsmVariable>();
        this.fsmVariables.AddRange((IEnumerable<FsmVariable>) FsmVariable.GetUnsortedInputVariablesList(this.owner).OrderBy<FsmVariable, string>((Func<FsmVariable, string>) (x => x.GetCategory() + "_" + (this.sortByVariableType ? x.Type.ToString() : x.Name))).ToList<FsmVariable>());
        this.fsmVariables.AddRange((IEnumerable<FsmVariable>) FsmVariable.GetUnsortedOutputVariablesList(this.owner).OrderBy<FsmVariable, string>((Func<FsmVariable, string>) (x => x.GetCategory() + "_" + (this.sortByVariableType ? x.Type.ToString() : x.Name))).ToList<FsmVariable>());
      }
      else
        this.fsmVariables = FsmVariable.GetUnsortedFsmVariableList(this.owner.Object, this.subFsm).OrderBy<FsmVariable, string>((Func<FsmVariable, string>) (x => x.GetCategory() + "_" + (this.sortByVariableType ? x.Type.ToString() : x.Name))).ToList<FsmVariable>();
      foreach (FsmVariable fsmVariable in this.fsmVariables)
        fsmVariable.NamedVar.Init();
      this.BuildCategoryLabels();
      if (keepSelection && this.categoryTextField != null)
        this.SelectVariable(name);
      this.BuildFilteredList();
    }

    private void BuildCategoryLabels()
    {
      this.categoryLabels.Clear();
      for (int index1 = 0; index1 < this.target.Categories.Length; ++index1)
      {
        string category = this.target.Categories[index1];
        for (int index2 = 0; index2 < 3; ++index2)
        {
          EditableLabel editableLabel = new EditableLabel(this.window, category);
          editableLabel.ID = index1;
          editableLabel.Click = new EditableLabel.ClickCallback(this.CategoryClicked);
          editableLabel.EditCommited = new EditableLabel.EditCommitedCallback(this.CommitCategoryEdit);
          editableLabel.ContextClick = new EditableLabel.ContextClickCallback(this.DoCategoryContextMenu);
          editableLabel.Style = EditorStyles.boldLabel;
          editableLabel.EditStyle = EditorStyles.textField;
          this.categoryLabels.Add(editableLabel);
        }
      }
    }

    private void ChangeVariableType(FsmVariable fsmVariable, VariableType newType, System.Type objectType)
    {
      if (fsmVariable == null)
        return;
      if (!this.editingGlobalVariables)
      {
        EditorCommands.ChangeVariableType(fsmVariable, newType, objectType);
      }
      else
      {
        if (!this.CheckDeleteVariable(Strings.Dialog_Edit_Variable_Type, Strings.Dialog_Edit_Variable_Type_Are_you_sure, fsmVariable))
          return;
        FsmBuilder.RemoveGlobalVariableUsage(fsmVariable.NamedVar);
        FsmVariables.GlobalVariables.DeleteVariable(fsmVariable.NamedVar);
        this.AddVariable(newType, fsmVariable.Name);
      }
      this.BuildFsmVariableList();
    }

    public FsmVariable AddVariable(string name, bool undo = true)
    {
      if (!FsmEditor.DisconnectCheck())
        return (FsmVariable) null;
      if (undo)
        FsmEditor.RecordUndo("Add Variable");
      if (this.owner.IsPlayMakerGlobals)
        this.target.AddVariable(this.tempNewVariable.Type, name, this.tempNewVariable.ObjectType);
      else
        this.owner.Fsm.AddVariable(this.tempNewVariable.Type, name, this.tempNewVariable.ObjectType);
      this.BuildFsmVariableList(false);
      this.SetDirty(true);
      return this.GetVariable(name);
    }

    public FsmVariable AddVariable(VariableType type, string name, bool undo = true)
    {
      if (!FsmEditor.DisconnectCheck())
        return (FsmVariable) null;
      if (undo)
        FsmEditor.RecordUndo("Add Variable");
      if (this.owner.IsPlayMakerGlobals)
        this.target.AddVariable(type, name);
      else
        this.owner.Fsm.AddVariable(type, name);
      this.BuildFsmVariableList(false);
      this.SetDirty(true);
      return this.GetVariable(name);
    }

    private void RenameVariable(FsmVariable fsmVariable, string newName)
    {
      if (fsmVariable == null)
        return;
      if (!FsmEditor.DisconnectCheck())
      {
        this.SelectVariable(fsmVariable);
      }
      else
      {
        if (!string.IsNullOrEmpty(newName))
        {
          FsmEditor.ReloadFSM();
          this.owner.RecordObject(Strings.Label_Edit_Variable);
          FsmEditor.SelectedFsm.RenameVariable(fsmVariable.NamedVar, newName);
          fsmVariable.Name = newName;
          this.BuildFsmVariableList(false);
        }
        Fsm fsm = this.owner.Fsm;
        if (fsm != null)
        {
          FsmEditor.SaveActions(fsm);
          this.SetDirty();
          this.UpdateFsmInfo();
          this.UpdateControls();
          fsm.InitData();
        }
        this.SelectVariable(newName);
      }
    }

    private bool CheckDeleteVariable(string title, string warning, FsmVariable fsmVariable)
    {
      if (this.owner.IsPlayMakerGlobals)
        return Dialogs.YesNoDialog(title, Strings.Label_Check_Edit_Global_Variable);
      return this.owner.Fsm == null || FsmSearch.GetUnusedVariables(this.owner.Fsm).Contains(fsmVariable) || Dialogs.YesNoDialog(title, warning);
    }

    public void DeleteVariable(FsmVariable fsmVariable, bool undo = true, bool checkDialog = true)
    {
      if (!FsmEditor.DisconnectCheck() || checkDialog && !this.CheckDeleteVariable(Strings.Dialog_Delete_Variable, Strings.Dialog_Delete_Variable_Are_you_sure, fsmVariable))
        return;
      if (undo)
        this.owner.RecordObject(Strings.Dialog_Delete_Variable);
      if (!this.editingGlobalVariables)
      {
        FsmEditor.SelectedFsm.DeleteVariable(fsmVariable.NamedVar);
        this.UpdateControls();
      }
      else
      {
        FsmBuilder.RemoveGlobalVariableUsage(fsmVariable.NamedVar);
        FsmVariables.GlobalVariables.DeleteVariable(fsmVariable.NamedVar);
      }
      this.BuildFsmVariableList(false);
      this.SetDirty(true);
      this.UpdateFsmInfo();
      this.Reset();
    }

    public FsmVariable GetVariable(string name)
    {
      foreach (FsmVariable fsmVariable in this.fsmVariables)
      {
        if (fsmVariable.Name == name)
          return fsmVariable;
      }
      return (FsmVariable) null;
    }

    private void HandleDragAndDrop()
    {
      if (!FsmEditor.MouseOverInspector || (double) Event.current.mousePosition.y > (double) this.editArea.y)
        return;
      switch (Event.current.type)
      {
        case UnityEngine.EventType.DragUpdated:
        case UnityEngine.EventType.DragPerform:
          this.UpdateDragAndDrop();
          break;
      }
    }

    private void UpdateDragAndDrop()
    {
      UnityEngine.Object[] objectReferences = DragAndDrop.objectReferences;
      if (objectReferences == null || objectReferences.Length == 0)
        return;
      UnityEngine.Object @object = objectReferences[0];
      if (@object == (UnityEngine.Object) null)
        return;
      DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
      if (Event.current.type != UnityEngine.EventType.DragPerform)
        return;
      this.PerformDrop(@object);
    }

    private string ValidateDrop(UnityEngine.Object obj) => this.editingAsset && !EditorUtility.IsPersistent(obj) ? Strings.Error_You_cannot_reference_Scene_Objects_in_Project_Assets : "";

    private void PerformDrop(UnityEngine.Object obj)
    {
      string message = this.ValidateDrop(obj);
      if (!string.IsNullOrEmpty(message))
      {
        Dialogs.OkDialog(message);
      }
      else
      {
        string name = this.MakeVariableNameUnique(obj.name);
        FsmVariable fsmVariable = this.AddVariable(VariableType.Object, name);
        if (fsmVariable == null)
          return;
        fsmVariable.TypeName = obj.GetType().ToString();
        fsmVariable.ObjectType = obj.GetType();
        fsmVariable.ObjectValue = obj;
        fsmVariable.UpdateVariableValue();
        this.SetDirty(true);
        this.BuildFsmVariableList(false);
        this.SelectVariable(name);
        GUIUtility.ExitGUI();
      }
    }

    [Localizable(false)]
    private string MakeVariableNameUnique(string name)
    {
      if (!this.VariableNameExists(name))
        return name;
      int num = 1;
      string name1;
      for (name1 = string.Format("{0} {1}", (object) name, (object) num); this.VariableNameExists(name1); name1 = string.Format("{0} {1}", (object) name, (object) num))
        ++num;
      return name1;
    }

    protected void UpdateFsmInfo()
    {
      Fsm fsm = this.owner.Fsm;
      if (fsm == null)
        return;
      FsmEditor.UpdateFsmInfo(fsm);
    }

    protected void UpdateControls()
    {
      Fsm fsm = this.owner.Fsm;
      if (fsm == null)
        return;
      FsmEditor.OnControlsChanged(fsm);
    }

    protected void SetDirty(bool checkAll = false)
    {
      Fsm fsm = this.owner.Fsm;
      if (fsm != null)
      {
        if (!EditorApplication.isPlayingOrWillChangePlaymode && fsm.HasErrors)
        {
          ActionReport.Remove(fsm);
          FsmErrorChecker.CheckFsmForErrors(fsm);
        }
        FsmEditor.SetFsmDirty(fsm, checkAll);
      }
      if (!this.owner.IsPlayMakerGlobals)
        return;
      EditorUtility.SetDirty(this.owner.Object);
      if (EditorApplication.isPlayingOrWillChangePlaymode || !((UnityEngine.Object) FsmEditor.SelectedFsmComponent != (UnityEngine.Object) null) || !FsmEditor.SelectedFsm.HasErrors)
        return;
      ActionReport.Remove(FsmEditor.SelectedFsm);
      FsmEditor.CheckFsmForErrors();
    }

    [Localizable(false)]
    [UsedImplicitly]
    private void DebugFsmVariables(string prefix = "")
    {
      string str = prefix;
      foreach (FsmVariable fsmVariable in this.fsmVariables)
        str = str + fsmVariable.Name + "[" + fsmVariable.Category + "],";
      UnityEngine.Debug.Log((object) str);
    }

    [Localizable(false)]
    [UsedImplicitly]
    private void DebugCategories()
    {
      string str1 = "";
      foreach (FsmVariable unsortedFsmVariable in FsmVariable.GetUnsortedFsmVariableList(this.owner.Object, this.subFsm))
        str1 = str1 + unsortedFsmVariable.Name + "[" + unsortedFsmVariable.Category + "],";
      GUILayout.Label(str1.Substring(0, str1.Length - 1));
      string str2 = "";
      foreach (int categoryId in this.target.CategoryIDs)
        str2 = str2 + (object) categoryId + ",";
      GUILayout.Label(str2.Substring(0, str2.Length - 1));
      string str3 = "";
      foreach (string category in this.target.Categories)
        str3 = str3 + category + ",";
      GUILayout.Label(str3.Substring(0, str3.Length - 1));
    }

    private void RemoveUnusedCategories()
    {
      this.RemoveUnusedCategories(this.target, this.fsmVariables);
      this.categoryTextField.AutoCompleteStrings = this.target.Categories;
    }

    private void RemoveUnusedCategories(FsmVariables variables, List<FsmVariable> _fsmVariables)
    {
      List<string> variableCategories = FsmVariablesEditor.GetUsedVariableCategories((IEnumerable<FsmVariable>) _fsmVariables);
      variableCategories.Add("");
      List<string> source = new List<string>((IEnumerable<string>) variables.Categories);
      source.Sort();
      variables.Categories = source.Where<string>(new Func<string, bool>(variableCategories.Contains)).ToArray<string>();
      FsmVariable.RemapVariableCategories(variables, _fsmVariables);
    }

    private static List<string> GetUsedVariableCategories(IEnumerable<FsmVariable> fsmVariables)
    {
      List<string> stringList = new List<string>();
      foreach (FsmVariable fsmVariable in fsmVariables)
        stringList.Add(fsmVariable.Category);
      return stringList;
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }

    public delegate void ContextClickVariable(FsmVariable variable);
  }
}
