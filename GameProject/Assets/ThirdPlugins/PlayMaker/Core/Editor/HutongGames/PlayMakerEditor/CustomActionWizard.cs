// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.CustomActionWizard
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class CustomActionWizard : BaseEditorWindow
  {
    private Vector2 controlsScrollPosition;
    private Vector2 previewScrollPosition;
    private Rect previewDividerRect;
    private float previewHeight;
    private bool draggingPreviewDivider;
    private string actionName = "";
    private string tooltip = "";
    private string[] actionCategories;
    private int selectedCategory;
    private string customCategory = "";
    private string rootFolder = "PlayMaker/Actions/";
    private string actionFolder = "Animation";
    private bool folderSameAsCategory = true;
    private bool handlesOnEnter = true;
    private bool handlesOnUpdate;
    private bool handlesOnFixedUpdate;
    private bool handlesOnLateUpdate;
    private bool handlesOnExit;
    private bool hasCustomErrorChecker;
    private bool isValid;
    private string errorString;
    private string code;
    private string fullFileName;
    private string localAssetFilename;

    [Localizable(false)]
    public override void Initialize()
    {
      this.SetTitle(Strings.CustomActionWizard_Title);
      this.rootFolder = EditorPrefs.GetString("PlayMaker.CustomActionWizard.RootFolder", "PlayMaker/Actions/");
      this.previewHeight = EditorPrefs.GetFloat("PlayMaker.CustomActionWizard.PreviewHeight", 200f);
      this.wantsMouseMove = true;
      List<string> list = ((IEnumerable<string>) Enum.GetNames(typeof (ActionCategory))).ToList<string>();
      list.Sort();
      this.actionCategories = list.ToArray();
      if ((double) this.position.height < 400.0)
        this.position = new Rect(this.position.x, this.position.y, this.position.width, 600f);
      this.minSize = new Vector2(500f, 400f);
      this.UpdateGUI();
    }

    private void Reset()
    {
      EditorPrefs.DeleteKey("PlayMaker.CustomActionWizard.RootFolder");
      EditorPrefs.DeleteKey("PlayMaker.CustomActionWizard.PreviewHeight");
      this.rootFolder = "PlayMaker/Actions/";
      this.customCategory = "";
    }

    public override void DoGUI()
    {
      FsmEditorStyles.Init();
      FsmEditorGUILayout.ToolWindowLargeTitle((EditorWindow) this, Strings.CustomActionWizard_Full_Title);
      EditorGUIUtility.labelWidth = 200f;
      this.HandleDragPreviewDivider();
      this.controlsScrollPosition = EditorGUILayout.BeginScrollView(this.controlsScrollPosition);
      CustomActionWizard.ControlGroup(Strings.CustomActionWizard_Group_Name_and_Description);
      this.actionName = FsmEditorGUILayout.TextFieldWithHint(this.actionName, Strings.CustomActionWizard_Label_Action_Name);
      this.tooltip = FsmEditorGUILayout.TextAreaWithHint(this.tooltip, Strings.CustomActionWizard_Label_Description, GUILayout.Height(80f));
      CustomActionWizard.ControlGroup(Strings.CustomActionWizard_Group_Category);
      GUI.enabled = string.IsNullOrEmpty(this.customCategory);
      this.selectedCategory = EditorGUILayout.Popup(Strings.CustomActionWizard_Select_Category, this.selectedCategory, this.actionCategories);
      GUI.enabled = true;
      this.customCategory = EditorGUILayout.TextField(Strings.CustomActionWizard_Custom_Category, this.customCategory);
      CustomActionWizard.ControlGroup(Strings.CustomActionWizard_Generated_Code_Folder);
      this.rootFolder = EditorGUILayout.TextField(Strings.CustomActionWizard_Root_Folder, this.rootFolder);
      GUI.enabled = !this.folderSameAsCategory;
      this.actionFolder = EditorGUILayout.TextField(Strings.CustomActionWizard_Action_Folder, this.actionFolder);
      GUI.enabled = true;
      this.folderSameAsCategory = EditorGUILayout.Toggle(Strings.CustomActionWizard_Same_as_Category, this.folderSameAsCategory);
      CustomActionWizard.ControlGroup(Strings.CustomActionWizard_Add_Methods);
      this.handlesOnEnter = EditorGUILayout.Toggle("OnEnter", this.handlesOnEnter);
      this.handlesOnUpdate = EditorGUILayout.Toggle("OnUpdate", this.handlesOnUpdate);
      this.handlesOnFixedUpdate = EditorGUILayout.Toggle("OnFixedUpdate", this.handlesOnFixedUpdate);
      this.handlesOnLateUpdate = EditorGUILayout.Toggle("OnLateUpdate", this.handlesOnLateUpdate);
      this.handlesOnExit = EditorGUILayout.Toggle("OnExit", this.handlesOnExit);
      this.hasCustomErrorChecker = EditorGUILayout.Toggle(Strings.CustomActionWizard_Custom_Error_Checker, this.hasCustomErrorChecker);
      EditorGUILayout.EndScrollView();
      if (!this.isValid)
      {
        FsmEditorGUILayout.Divider();
        EditorGUI.indentLevel = 0;
        EditorGUILayout.HelpBox(this.errorString, MessageType.Error, true);
      }
      FsmEditorGUILayout.Divider();
      GUILayout.BeginHorizontal(EditorStyles.toolbar);
      GUILayout.Label(Strings.CustomActionWizard_Code_Preview);
      GUILayout.FlexibleSpace();
      GUILayout.EndHorizontal();
      if (Event.current.type == UnityEngine.EventType.Repaint)
        this.previewDividerRect = GUILayoutUtility.GetLastRect();
      EditorGUIUtility.AddCursorRect(this.previewDividerRect, MouseCursor.ResizeVertical);
      this.previewScrollPosition = EditorGUILayout.BeginScrollView(this.previewScrollPosition, GUILayout.MinHeight(this.previewHeight));
      GUILayout.Label(this.code);
      EditorGUILayout.EndScrollView();
      GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG);
      GUILayout.Label(Strings.CustomActionWizard_File_Path_Prefix + this.localAssetFilename);
      GUILayout.BeginHorizontal();
      GUI.enabled = this.isValid;
      if (GUILayout.Button(Strings.CustomActionWizard_Save_Button))
      {
        this.SaveCustomAction();
        GUIUtility.ExitGUI();
      }
      else
      {
        GUI.enabled = true;
        if (GUILayout.Button(new GUIContent(Strings.CustomActionWizard_Find_File, Strings.CustomActionWizard_Find_File_Tooltip), GUILayout.MaxWidth(100f)))
          this.PingScriptFile();
        if (GUILayout.Button(new GUIContent(Strings.CustomActionWizard_Copy_Code, Strings.CustomActionWizard_Copy_Code_Tooltip), GUILayout.MaxWidth(100f)))
          this.CopyCodeToClipboard();
        GUILayout.EndHorizontal();
        GUILayout.EndVertical();
        EditorGUI.indentLevel = 0;
        if (!GUI.changed)
          return;
        this.UpdateGUI();
        GUIUtility.ExitGUI();
      }
    }

    private void OnFocus()
    {
      if (this.Initialized)
        this.UpdateGUI();
      this.draggingPreviewDivider = false;
    }

    [Localizable(false)]
    private void HandleDragPreviewDivider()
    {
      Rect position;
      switch (Event.current.type)
      {
        case UnityEngine.EventType.MouseDown:
          if (this.previewDividerRect.Contains(Event.current.mousePosition))
          {
            this.draggingPreviewDivider = true;
            break;
          }
          break;
        case UnityEngine.EventType.MouseUp:
          this.draggingPreviewDivider = false;
          EditorPrefs.SetFloat("PlayMaker.CustomActionWizard.PreviewHeight", this.previewHeight);
          break;
        default:
          if (this.draggingPreviewDivider && Event.current.isMouse)
          {
            position = this.position;
            this.previewHeight = (float) ((double) position.height - (double) Event.current.mousePosition.y - 60.0);
            this.Repaint();
            break;
          }
          break;
      }
      double previewHeight = (double) this.previewHeight;
      position = this.position;
      double num = (double) position.height - 200.0;
      this.previewHeight = Mathf.Clamp((float) previewHeight, 40f, (float) num);
    }

    private static void ControlGroup(string title)
    {
      GUILayout.Space(10f);
      GUILayout.Label(title, EditorStyles.boldLabel);
      GUILayout.Space(5f);
    }

    [Localizable(false)]
    private void UpdateGUI()
    {
      this.previewHeight = Mathf.Clamp(this.previewHeight, 40f, this.position.height - 190f);
      EditorPrefs.SetString("PlayMaker.CustomActionWizard.RootFolder", this.rootFolder);
      if (this.folderSameAsCategory)
        this.actionFolder = string.IsNullOrEmpty(this.customCategory) ? this.actionCategories[this.selectedCategory] : this.customCategory;
      try
      {
        this.localAssetFilename = Path.Combine(Path.Combine(this.rootFolder, this.actionFolder), this.actionName + ".cs");
        this.fullFileName = Path.Combine(Application.dataPath, this.localAssetFilename);
      }
      catch (Exception ex)
      {
        Debug.LogWarning((object) ex);
        this.Reset();
        return;
      }
      this.localAssetFilename = this.localAssetFilename.Replace('\\', '/');
      this.fullFileName = this.fullFileName.Replace('\\', '/');
      this.BuildCustomAction();
      this.isValid = this.IsValidSetup();
    }

    private bool IsValidSetup()
    {
      this.errorString = "";
      if (string.IsNullOrEmpty(this.actionName))
        this.errorString = this.errorString + Strings.CustomActionWizard_Invalid_Action_Name + Environment.NewLine;
      if (!CodeGenerator.IsValidLanguageIndependentIdentifier(this.actionName))
        this.errorString = this.errorString + Strings.CustomActionWizard_Action_Name_contains_invalid_characters + Environment.NewLine;
      if (File.Exists(this.fullFileName))
        this.errorString = this.errorString + Strings.CustomActionWizard_FileExists_Error + Environment.NewLine;
      return this.errorString == "";
    }

    private void SaveCustomAction()
    {
      Debug.Log((object) (Strings.CustomActionWizard_Log_Creating_custom_action__ + this.fullFileName));
      string directoryName = Path.GetDirectoryName(this.fullFileName);
      if (string.IsNullOrEmpty(directoryName))
      {
        Debug.LogError((object) string.Format(Strings.CustomActionWizard_Error_InvalidPath, (object) this.fullFileName));
      }
      else
      {
        try
        {
          if (!Directory.Exists(directoryName))
            Directory.CreateDirectory(directoryName);
        }
        catch (Exception ex)
        {
          Debug.LogError((object) (Strings.CustomActionWizard_Error_Could_not_create_directory + directoryName));
          return;
        }
        using (StreamWriter streamWriter = new StreamWriter(this.fullFileName))
        {
          streamWriter.Write(this.code);
          streamWriter.Close();
        }
        AssetDatabase.Refresh();
        this.PingScriptFile();
      }
    }

    [Localizable(false)]
    private void PingScriptFile() => EditorGUIUtility.PingObject(AssetDatabase.LoadMainAssetAtPath("Assets/" + this.localAssetFilename));

    private void CopyCodeToClipboard() => EditorGUIUtility.systemCopyBuffer = this.code;

    [Localizable(false)]
    private void BuildCustomAction()
    {
      this.code = "using UnityEngine;\n\nnamespace HutongGames.PlayMaker.Actions\n{\n\n";
      this.code = !string.IsNullOrEmpty(this.customCategory) ? this.code + "\t[ActionCategory(\"" + this.customCategory + "\")]\n" : this.code + "\t[ActionCategory(ActionCategory." + this.actionCategories[this.selectedCategory] + ")]\n";
      if (!string.IsNullOrEmpty(this.tooltip))
        this.code = this.code + "\t[Tooltip(\"" + this.tooltip + "\")]\n";
      this.code = this.code + "\tpublic class " + this.actionName + " : FsmStateAction\n\t{\n\n";
      if (this.handlesOnEnter)
        this.code += CustomActionWizard.BuildOverrideMethod("void OnEnter()", "Code that runs on entering the state.", this.HasUpdateMethod() ? "" : "Finish();");
      if (this.handlesOnUpdate)
        this.code += CustomActionWizard.BuildOverrideMethod("void OnUpdate()", "Code that runs every frame.");
      if (this.handlesOnFixedUpdate)
        this.code += CustomActionWizard.BuildOverrideMethod("void OnFixedUpdate()");
      if (this.handlesOnLateUpdate)
        this.code += CustomActionWizard.BuildOverrideMethod("void OnLateUpdate()");
      if (this.handlesOnExit)
        this.code += CustomActionWizard.BuildOverrideMethod("void OnExit()", "Code that runs when exiting the state.");
      if (this.hasCustomErrorChecker)
        this.code += CustomActionWizard.BuildOverrideMethod("string ErrorCheck()", "Perform custom error checking here.", "// Return an error string or null if no error.\n\nreturn null;");
      this.code += "\n\t}\n\n}\n";
    }

    [Localizable(false)]
    private static string BuildOverrideMethod(string methodName, string comment = "", string body = "")
    {
      string str = "";
      if (!string.IsNullOrEmpty(comment))
        str = str + "\t\t// " + comment + "\n";
      return str + "\t\tpublic override " + methodName + "\n\t\t{\n" + "\t\t\t" + body.Replace("\n", "\n\t\t\t") + "\n" + "\t\t}\n\n";
    }

    private bool HasUpdateMethod() => this.handlesOnUpdate || this.handlesOnFixedUpdate || this.handlesOnLateUpdate;
  }
}
