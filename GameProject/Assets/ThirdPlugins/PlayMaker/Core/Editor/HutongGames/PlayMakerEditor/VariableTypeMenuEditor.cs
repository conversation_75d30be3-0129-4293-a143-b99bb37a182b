// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.VariableTypeMenuEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  internal class VariableTypeMenuEditor : UnityEditor.Editor
  {
    // [UnityEditor.MenuItem("Assets/Create/PlayMaker/New Custom Variable Type Definition", false, 3000)]
    private static void CreateCustomVariableTypeDefinitionAsset() => VariableTypeMenuEditor.CreateAssetOfType<VariableTypeDefinitionAsset>("Variable Type Definition");

    public static T CreateAssetOfType<T>(string preferredName) where T : ScriptableObject
    {
      string str = string.IsNullOrEmpty(preferredName) ? typeof (T).Name : preferredName;
      string path = "Assets";
      Object[] filtered = Selection.GetFiltered(typeof (Object), SelectionMode.Assets);
      int index = 0;
      if (index < filtered.Length)
      {
        path = AssetDatabase.GetAssetPath(filtered[index]);
        if (File.Exists(path))
          path = Path.GetDirectoryName(path);
      }
      string uniqueAssetPath = AssetDatabase.GenerateUniqueAssetPath(path + "/" + str + ".asset");
      T instance = ScriptableObject.CreateInstance<T>();
      AssetDatabase.CreateAsset((Object) instance, uniqueAssetPath);
      EditorUtility.FocusProjectWindow();
      Selection.activeObject = (Object) instance;
      return instance;
    }

    public static Dictionary<string, List<VariableTypeDefinition>> GetAllVariableTypesDefinitions()
    {
      Dictionary<string, List<VariableTypeDefinition>> dictionary = new Dictionary<string, List<VariableTypeDefinition>>();
      foreach (string asset in AssetDatabase.FindAssets("t:VariableTypeDefinitionAsset"))
      {
        string assetPath = AssetDatabase.GUIDToAssetPath(asset);
        VariableTypeDefinitionAsset typeDefinitionAsset = AssetDatabase.LoadAssetAtPath<VariableTypeDefinitionAsset>(assetPath);
        dictionary[assetPath] = typeDefinitionAsset.VariableTypesDefinition;
      }
      return dictionary;
    }
  }
}
