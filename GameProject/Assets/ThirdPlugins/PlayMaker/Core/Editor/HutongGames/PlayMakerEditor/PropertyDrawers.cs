// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.PropertyDrawers
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  internal class PropertyDrawers
  {
    private static Dictionary<Type, PropertyDrawer> drawers;

    public static PropertyDrawer GetPropertyDrawer(Type objType)
    {
      if (PropertyDrawers.drawers == null)
        PropertyDrawers.Rebuild();
      PropertyDrawer propertyDrawer;
      PropertyDrawers.drawers.TryGetValue(objType, out propertyDrawer);
      return propertyDrawer;
    }

    public static void Init() => PropertyDrawers.Rebuild();

    private static void Rebuild()
    {
      PropertyDrawers.drawers = new Dictionary<Type, PropertyDrawer>();
      foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
      {
        try
        {
          foreach (Type exportedType in assembly.GetExportedTypes())
          {
            if (typeof (PropertyDrawer).IsAssignableFrom(exportedType) && exportedType.IsClass && !exportedType.IsAbstract)
            {
              Type inspectedType = CustomAttributeHelpers.GetAttribute<PropertyDrawerAttribute>(exportedType)?.InspectedType;
              if (inspectedType != null && !PropertyDrawers.drawers.ContainsKey(inspectedType))
                PropertyDrawers.drawers.Add(inspectedType, (PropertyDrawer) Activator.CreateInstance(exportedType));
            }
          }
        }
        catch (Exception ex)
        {
          NotSupportedException supportedException = ex as NotSupportedException;
        }
      }
    }
  }
}
