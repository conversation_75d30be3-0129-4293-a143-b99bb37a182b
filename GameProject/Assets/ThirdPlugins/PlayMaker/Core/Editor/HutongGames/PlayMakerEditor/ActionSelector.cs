// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.ActionSelector
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class ActionSelector : BaseEditorWindow
  {
    public const string FavoritesCategory = "Favorites";
    public const string RecentCategory = "Recent";
    private static ActionSelector instance;
    private Vector2 mousePos;
    private System.Type mouseOverAction;
    private System.Type mouseDownAction;
    private bool addingAction;
    private Vector2 scrollPosition;
    private float scrollViewHeight;
    private float descriptionHeight;
    private Rect selectedRect;
    private bool autoScroll;
    private float previewScrollViewHeight;
    private Vector2 previewScrollPosition;
    private float previewActionGUIHeight;
    private System.Type selectedAction;
    private System.Type prevAction;
    private System.Type beforeSelected;
    private System.Type afterSelected;
    private string selectedActionCategory;
    private string prevActionCategory;
    private string beforeSelectedCategory;
    private string afterSelectedCategory;
    private string obsoleteWarning;
    private bool prevActionWasSelected;
    private FsmStateAction previewAction;
    private SearchBox searchBox;
    private string searchString = "";
    private const string selectedActionKey = "PlayMaker.ActionSelector.SelectedAction";
    private const string selectedActionCategoryKey = "PlayMaker.ActionSelector.SelectedActionCategory";
    private readonly GUIContent countLabel = new GUIContent();

    public override void Initialize()
    {
      if ((UnityEngine.Object) ActionSelector.instance == (UnityEngine.Object) null)
        ActionSelector.instance = this;
      this.minSize = new Vector2(200f, 200f);
      this.wantsMouseMove = true;
      this.isToolWindow = true;
      if (FsmEditorSettings.AutoRefreshActionUsage && !EditorApplication.isPlayingOrWillChangePlaymode)
        this.RefreshActionUsage();
      this.InitSearchBox();
      Actions.BuildListIfNeeded();
      this.FilterActionList();
      this.RestoreSelectedAction();
      this.Repaint();
      FsmSearch.OnUpdated -= new Action(ActionSelector.UpdateActionUsage);
      FsmSearch.OnUpdated += new Action(ActionSelector.UpdateActionUsage);
      EditorApplication.delayCall += new EditorApplication.CallbackFunction(ActionSelector.UpdateActionUsage);
      EditorApplication.playModeStateChanged += new Action<PlayModeStateChange>(this.OnPlayModeChanged);
    }

    private void InitSearchBox()
    {
      if (this.searchBox != null)
        return;
      this.searchBox = new SearchBox((EditorWindow) this)
      {
        SearchModes = new string[2]{ "Name", "Description" },
        HasPopupSearchModes = true
      };
      this.searchBox.SearchChanged += new EditorApplication.CallbackFunction(this.UpdateSearchResults);
      this.searchBox.Focus();
    }

    public override void InitWindowTitle() => this.SetTitle(Strings.ActionSelector_Title);

    public static void FindAction(System.Type actionType)
    {
      Debug.Log((object) ("FindActionType: " + (object) actionType));
      FsmEditor.Open();
      FsmEditor.OpenActionWindow();
      EditorApplication.delayCall += (EditorApplication.CallbackFunction) (() => ActionSelector.DoFindAction(actionType));
    }

    private static void DoFindAction(System.Type actionType)
    {
      if ((UnityEngine.Object) ActionSelector.instance == (UnityEngine.Object) null)
      {
        EditorApplication.delayCall += (EditorApplication.CallbackFunction) (() => ActionSelector.DoFindAction(actionType));
      }
      else
      {
        ActionSelector.instance.ClearSearch();
        ActionSelector.instance.SelectAction(actionType, Actions.GetActionCategory(actionType));
      }
    }

    private void OnFocus()
    {
      if (!this.Initialized || EditorApplication.isPlaying && FsmEditorSettings.DisableActionBrowerWhenPlaying)
        return;
      this.InitSearchBox();
      this.searchBox.Focus();
      this.FilterActionList();
      this.Repaint();
      this.mouseDownAction = (System.Type) null;
    }

    public override void DoGUI()
    {
      if (FsmEditorSettings.AutoRefreshActionUsage && !EditorApplication.isPlayingOrWillChangePlaymode && FsmSearch.UpdateAllIfNeeded())
        Actions.UpdateNumActionsUsedInCategory();
      EditorGUIUtility.wideMode = true;
      if (EditorApplication.isPlaying && FsmEditorSettings.DisableActionBrowerWhenPlaying)
      {
        GUILayout.Label(Strings.ActionSelector_Disabled_when_playing);
        FsmEditorSettings.DisableActionBrowerWhenPlaying = !GUILayout.Toggle(!FsmEditorSettings.DisableActionBrowerWhenPlaying, Strings.ActionSelector_Enable_Action_Browser_When_Playing);
        if (!GUI.changed)
          return;
        FsmEditorSettings.SaveSettings();
      }
      else
      {
        this.HandleKeyboardInput();
        this.DoMainToolbar();
        this.DoActionList();
        this.HandleDragAndDrop();
        this.DoBottomPanel();
      }
    }

    private void OnPlayModeChanged(PlayModeStateChange playMode)
    {
      if (!FsmEditorSettings.AutoRefreshActionUsage || playMode != PlayModeStateChange.EnteredEditMode)
        return;
      this.RefreshActionUsage();
    }

    public static void UpdateActionUsage()
    {
      FsmSearch.UpdateActionUsageCount();
      Actions.UpdateNumActionsUsedInCategory();
    }

    private void RefreshActionUsage()
    {
      if (!FsmSearch.RefreshAll())
        return;
      ActionSelector.UpdateActionUsage();
    }

    private void OnDisable()
    {
      HighlighterHelper.Reset(this.GetType());
      EditorPrefs.SetString("PlayMaker.ActionSelector.SelectedAction", this.selectedAction != null ? this.selectedAction.FullName : "");
      EditorPrefs.SetString("PlayMaker.ActionSelector.SelectedActionCategory", this.selectedActionCategory);
      FsmSearch.OnUpdated -= new Action(ActionSelector.UpdateActionUsage);
      EditorApplication.playModeStateChanged -= new Action<PlayModeStateChange>(this.OnPlayModeChanged);
      if (!((UnityEngine.Object) ActionSelector.instance == (UnityEngine.Object) this))
        return;
      ActionSelector.instance = (ActionSelector) null;
    }

    private void RestoreSelectedAction()
    {
      string typeName = EditorPrefs.GetString("PlayMaker.ActionSelector.SelectedAction", "");
      string category = EditorPrefs.GetString("PlayMaker.ActionSelector.SelectedActionCategory", "");
      if (string.IsNullOrEmpty(typeName))
        return;
      System.Type globalType = ReflectionUtils.GetGlobalType(typeName);
      if (globalType == null)
        return;
      this.SelectAction(globalType, category);
    }

    private void HandleDragAndDrop()
    {
      if (!this.addingAction && this.eventType == UnityEngine.EventType.MouseDrag && this.mouseDownAction != null)
      {
        this.StartDragAction(this.selectedAction, this.selectedActionCategory);
        GUIUtility.ExitGUI();
      }
      else
      {
        if (this.eventType == UnityEngine.EventType.MouseUp)
        {
          DragAndDropManager.Reset();
          this.mouseDownAction = (System.Type) null;
          this.addingAction = false;
        }
        if (this.eventType != UnityEngine.EventType.DragUpdated && this.eventType != UnityEngine.EventType.DragPerform)
          return;
        if (DragAndDropManager.MoveActions != null)
        {
          DragAndDrop.visualMode = DragAndDropVisualMode.Move;
          if (this.eventType == UnityEngine.EventType.DragPerform)
          {
            DragAndDrop.AcceptDrag();
            FsmEditor.StateInspector.DeleteSelectedActions();
          }
        }
        if (DragAndDropManager.AddAction != null)
        {
          DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
          if (this.eventType == UnityEngine.EventType.DragPerform)
          {
            DragAndDropManager.Reset();
            this.mouseDownAction = (System.Type) null;
          }
        }
        Event.current.Use();
      }
    }

    private void DoMainToolbar()
    {
      EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
      GUILayout.Space(4f);
      this.searchBox.OnGUI();
      if (GUILayout.Button(FsmEditorContent.RefreshActionUsage, EditorStyles.toolbarButton, GUILayout.Width(60f)))
        this.RefreshActionUsage();
      if (FsmEditorGUILayout.ToolbarSettingsButton())
        this.GenerateSettingsMenu().ShowAsContext();
      EditorGUILayout.EndHorizontal();
    }

    private void UpdateSearchResults()
    {
      this.searchString = this.searchBox.SearchString;
      if (!string.IsNullOrEmpty(this.searchString))
      {
        this.FilterActionList();
        this.SelectFirstMatchingAction();
      }
      else
      {
        FsmEditorSettings.SelectedActionCategory = Actions.GetCategoryIndex(this.selectedActionCategory);
        FsmEditorSettings.SaveSettings();
        Actions.BuildList();
        this.autoScroll = true;
      }
      this.SanityCheckSelection();
    }

    private void SanityCheckSelection()
    {
      if (this.selectedAction == null || Actions.IsVisible(this.selectedAction))
        return;
      this.SelectNone();
    }

    private void DoActionList()
    {
      this.scrollPosition = GUILayout.BeginScrollView(this.scrollPosition, false, true);
      this.mouseOverAction = (System.Type) null;
      if (Event.current.isMouse)
        this.mousePos = Event.current.mousePosition;
      if (string.IsNullOrEmpty(this.searchString) && !FsmEditorSettings.ShowUsedActionsOnly)
        this.DoFullActionList();
      else
        this.DoFilteredActionList();
      HighlighterHelper.EndScrollView("Action List");
      this.DoAutoScroll();
    }

    public static void AddToFavorites(object actionType)
    {
      Actions.AddActionToCategory("Favorites", actionType as System.Type);
      ActionSelector.RefreshFilteredList();
    }

    public static void RemoveFromFavorites(object actionType)
    {
      Actions.RemoveActionFromCategory("Favorites", actionType as System.Type);
      ActionSelector.RefreshFilteredList();
    }

    private void DoFilteredActionList()
    {
      if (Actions.FilteredCategoryIDs.Count == 0)
      {
        if (FsmEditorSettings.ShowUsedActionsOnly)
        {
          GUILayout.Label("No actions used in this project.");
          if (!GUILayout.Button("Turn Off: " + Strings.Menu_Show_Used_Actions_Only))
            return;
          FsmEditorSettings.ShowUsedActionsOnly = false;
          FsmEditorSettings.SaveSettings();
          ActionSelector.RefreshFilteredList();
        }
        else
          GUILayout.Label(Strings.Label_No_search_results_for__ + this.searchString);
      }
      else
      {
        foreach (int filteredCategoryId in Actions.FilteredCategoryIDs)
        {
          EditorGUI.BeginChangeCheck();
          ActionSelector.DoCategoryButton(filteredCategoryId);
          if (EditorGUI.EndChangeCheck())
          {
            this.ClearSearch();
            this.OpenCategory(filteredCategoryId);
            break;
          }
          string category = Actions.Categories[filteredCategoryId];
          List<System.Type> categoryFiltered = Actions.GetActionsInCategoryFiltered(category);
          for (int index = 0; index < categoryFiltered.Count; ++index)
            this.DoActionButton(category, categoryFiltered[index]);
        }
      }
    }

    private void DoFullActionList()
    {
      foreach (int filteredCategoryId in Actions.FilteredCategoryIDs)
      {
        if (ActionSelector.DoCategoryButton(filteredCategoryId))
          this.OpenCategory(filteredCategoryId);
        else
          this.CloseCategory(filteredCategoryId);
        string category = Actions.Categories[filteredCategoryId];
        if (FsmEditorSettings.SelectedActionCategory == filteredCategoryId)
        {
          List<System.Type> actionsInCategory = Actions.GetActionsInCategory(category);
          this.DoActionList(category, actionsInCategory);
        }
      }
    }

    private static bool DoCategoryButton(int categoryIndex)
    {
      string category = Actions.Categories[categoryIndex];
      Texture categoryIcon = Actions.GetCategoryIcon(category);
      GUIContent content = FsmEditorContent.TempContent(category);
      Rect rect1 = GUILayoutUtility.GetRect(content, FsmEditorStyles.ActionCategory);
      int num = GUI.Toggle(rect1, FsmEditorSettings.SelectedActionCategory == categoryIndex, content, FsmEditorStyles.ActionCategory) ? 1 : 0;
      if (Event.current.type != UnityEngine.EventType.Repaint)
        return num != 0;
      Rect rect2 = new Rect(rect1);
      rect2.x += 6f;
      rect2.y += 3f;
      rect2.width = rect2.height = 16f;
      GUIStyle.none.Draw(rect2, categoryIcon);
      int actionUsedInCategory = Actions.GetNumActionUsedInCategory(category);
      if (actionUsedInCategory <= 0)
        return num != 0;
      FsmEditorStyles.ActionCategoryCount.Draw(rect1, "[" + (object) actionUsedInCategory + "]");
      return num != 0;
    }

    private void OpenCategory(int i)
    {
      if (FsmEditorSettings.SelectedActionCategory == i)
        return;
      FsmEditorSettings.SelectedActionCategory = i;
      string category = Actions.Categories[i];
      List<System.Type> actionsInCategory = Actions.GetActionsInCategory(category);
      if (actionsInCategory.Count > 0)
        this.SelectAction(actionsInCategory[0], category);
      FsmEditorSettings.SaveSettings();
      this.Repaint();
    }

    private void CloseCategory(int i)
    {
      if (FsmEditorSettings.SelectedActionCategory != i)
        return;
      FsmEditorSettings.SelectedActionCategory = -1;
      FsmEditorSettings.SaveSettings();
      this.selectedAction = (System.Type) null;
      this.Repaint();
    }

    private void DoAutoScroll()
    {
      if (Event.current.type != UnityEngine.EventType.Repaint)
        return;
      this.scrollViewHeight = GUILayoutUtility.GetLastRect().height;
      if (!this.autoScroll)
        return;
      if ((double) this.selectedRect.y < 0.0)
      {
        this.scrollPosition.y += this.selectedRect.y;
        this.Repaint();
      }
      else if ((double) this.selectedRect.y + (double) this.selectedRect.height > (double) this.scrollViewHeight)
      {
        this.scrollPosition.y += this.selectedRect.y + this.selectedRect.height - this.scrollViewHeight;
        this.Repaint();
      }
      this.autoScroll = false;
    }

    private void DoActionList(string category, List<System.Type> actions)
    {
      if (actions == null)
        return;
      GUILayout.Space(3f);
      for (int index = 0; index < actions.Count; ++index)
        this.DoActionButton(category, actions[index]);
      GUILayout.Space(3f);
    }

    private void DoActionButton(string category, System.Type actionType)
    {
      int usageCount = FsmSearch.GetUsageCount(actionType);
      if (usageCount == 0 && FsmEditorSettings.HideObsoleteActions && CustomAttributeHelpers.IsObsolete(actionType))
        return;
      string actionLabel = Labels.GetActionLabel(actionType);
      bool flag = actionType == this.selectedAction && category == this.selectedActionCategory;
      GUIStyle style1 = flag ? FsmEditorStyles.ActionItemSelected : FsmEditorStyles.ActionItem;
      GUIStyle style2 = flag ? FsmEditorStyles.ActionLabelSelected : FsmEditorStyles.ActionLabel;
      GUILayoutOption[] guiLayoutOptionArray = new GUILayoutOption[0];
      GUILayout.BeginHorizontal(style1, guiLayoutOptionArray);
      string text = usageCount > 0 ? string.Format(Strings.ActionSelector_Count_Postfix, (object) usageCount) : "";
      float maxWidth = this.position.width - 42f;
      if (usageCount > 0)
      {
        this.countLabel.text = text;
        maxWidth -= style2.CalcSize(this.countLabel).x + 3f;
      }
      GUILayout.Label(actionLabel, style2, GUILayout.MaxWidth(maxWidth));
      if (usageCount > 0)
      {
        GUILayout.FlexibleSpace();
        GUILayout.Label(text, style2);
      }
      GUILayout.EndHorizontal();
      Rect lastRect = GUILayoutUtility.GetLastRect();
      if ((double) this.mousePos.y > (double) this.scrollPosition.y && (double) this.mousePos.y < (double) this.scrollPosition.y + (double) this.scrollViewHeight && lastRect.Contains(this.mousePos))
        this.mouseOverAction = actionType;
      if (this.mouseOverAction == actionType && this.eventType == UnityEngine.EventType.MouseDown)
      {
        this.mouseDownAction = actionType;
        this.SelectAction(actionType, category);
        if (Event.current.button == 1 || EditorGUI.actionKey)
          this.GenerateActionContextMenu().ShowAsContext();
        if (Event.current.clickCount > 1)
          this.AddAction();
        GUIUtility.ExitGUI();
      }
      else
      {
        if (flag)
        {
          this.beforeSelected = this.prevAction;
          this.beforeSelectedCategory = this.prevActionCategory;
          if (Event.current.type == UnityEngine.EventType.Repaint)
          {
            this.selectedRect = GUILayoutUtility.GetLastRect();
            this.selectedRect.y -= this.scrollPosition.y + 20f;
            this.selectedRect.height += 20f;
          }
        }
        if (this.prevActionWasSelected)
        {
          this.afterSelected = actionType;
          this.afterSelectedCategory = category;
        }
        this.prevAction = actionType;
        this.prevActionCategory = category;
        this.prevActionWasSelected = flag;
      }
    }

    private void AddAction()
    {
      if (FsmGraphView.EditingDisable)
        return;
      if (FsmEditor.SelectedFsm == null)
      {
        this.AddSelectedActionToNewFSM();
      }
      else
      {
        this.AddSelectedActionToState();
        this.addingAction = true;
      }
    }

    private void DoBottomPanel()
    {
      GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG);
      if (this.selectedAction != null)
      {
        GUILayout.BeginVertical();
        GUILayout.Space(4f);
        GUILayout.BeginHorizontal();
        string actionLabel = Labels.GetActionLabel(this.selectedAction);
        GUIStyle actionPreviewTitle = FsmEditorStyles.ActionPreviewTitle;
        GUILayoutOption[] guiLayoutOptionArray = new GUILayoutOption[1];
        Rect rect = this.position;
        guiLayoutOptionArray[0] = GUILayout.MaxWidth(rect.width - 30f);
        GUILayout.Label(actionLabel, actionPreviewTitle, guiLayoutOptionArray);
        GUILayout.FlexibleSpace();
        if (Actions.ActionHasSceneGUI(this.selectedAction))
        {
          GUILayout.BeginVertical();
          GUILayout.Space(2f);
          FsmEditorGUILayout.SceneGizmoIcon(Strings.Tooltip_Uses_scene_gizmos);
          GUILayout.EndVertical();
        }
        if (FsmEditorGUILayout.HelpButton())
          EditorCommands.OpenWikiPage(this.previewAction);
        GUILayout.EndHorizontal();
        GUILayout.Box(Actions.GetTooltip(this.selectedAction), FsmEditorStyles.LabelWithWordWrap);
        if (Event.current.type == UnityEngine.EventType.Repaint)
        {
          rect = GUILayoutUtility.GetLastRect();
          this.descriptionHeight = rect.height;
        }
        HighlighterHelper.EndVertical("Action Description");
        if (!string.IsNullOrEmpty(this.obsoleteWarning))
          EditorGUILayout.HelpBox(this.obsoleteWarning, MessageType.Warning);
        ActionEditor.PreviewMode = true;
        GUILayout.Space(5f);
        if (FsmEditorSettings.ShowActionPreview)
          this.DoSelectedActionPreview();
        ActionEditor.PreviewMode = false;
      }
      GUILayout.EndVertical();
      GUILayout.BeginHorizontal(FsmEditorStyles.ToolbarBottom);
      GUILayout.Space(6f);
      bool flag = GUILayout.Toggle(FsmEditorSettings.ShowActionPreview, FsmEditorContent.ActionPreview, FsmEditorStyles.ToolbarCheckbox);
      if (flag != FsmEditorSettings.ShowActionPreview)
      {
        FsmEditorSettings.ShowActionPreview = flag;
        FsmEditorSettings.SaveSettings();
      }
      HighlighterHelper.FromGUILayout("Preview Toggle");
      if (FsmEditor.SelectedFsm == null)
      {
        EditorGUI.BeginDisabledGroup((UnityEngine.Object) Selection.activeGameObject == (UnityEngine.Object) null);
        if (GUILayout.Button(FsmEditorContent.AddActionToNewFsm, EditorStyles.toolbarButton))
          this.AddSelectedActionToNewFSM();
      }
      else
      {
        EditorGUI.BeginDisabledGroup(FsmEditor.SelectedState == null || this.selectedAction == null || FsmGraphView.EditingDisable);
        if (GUILayout.Button(FsmEditorContent.AddActionToState, EditorStyles.toolbarButton))
          this.AddSelectedActionToState();
      }
      HighlighterHelper.EndDisabledGroup("Add Action");
      GUILayout.EndHorizontal();
    }

    private void DoSelectedActionPreview()
    {
      FsmEditorGUILayout.AutoLabelWidth(this.position.width);
      this.previewScrollViewHeight = Mathf.Min(this.position.height - 150f - this.descriptionHeight, this.previewActionGUIHeight);
      this.previewScrollPosition = EditorGUILayout.BeginScrollView(this.previewScrollPosition, GUILayout.Height(this.previewScrollViewHeight));
      GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG);
      GUILayout.Space(5f);
      int num = GUI.enabled ? 1 : 0;
      GUI.enabled = false;
      FsmEditor.ActionEditor.OnGUI(this.previewAction);
      GUI.enabled = num != 0;
      GUILayout.EndVertical();
      if (Event.current.type == UnityEngine.EventType.Repaint)
      {
        float previewActionGuiHeight = this.previewActionGUIHeight;
        this.previewActionGUIHeight = GUILayoutUtility.GetLastRect().height;
        if ((double) Math.Abs(this.previewActionGUIHeight - previewActionGuiHeight) > 1.0)
        {
          this.Repaint();
          this.autoScroll = true;
        }
      }
      HighlighterHelper.EndScrollView("Action Preview");
    }

    private void HandleKeyboardInput()
    {
      this.prevAction = (System.Type) null;
      if (Event.current.GetTypeForControl(GUIUtility.GetControlID(FocusType.Keyboard)) != UnityEngine.EventType.KeyDown)
        return;
      switch (Event.current.keyCode)
      {
        case KeyCode.Return:
        case KeyCode.KeypadEnter:
          this.AddAction();
          break;
        case KeyCode.Escape:
          if (!FsmEditorSettings.CloseActionBrowserOnEnter || !string.IsNullOrEmpty(this.searchString) || EditorHacks.IsDocked((EditorWindow) this))
            break;
          this.Close();
          GUIUtility.ExitGUI();
          break;
        case KeyCode.UpArrow:
          Event.current.Use();
          this.SelectPreviousAction();
          GUIUtility.ExitGUI();
          break;
        case KeyCode.DownArrow:
          Event.current.Use();
          this.SelectNextAction();
          GUIUtility.ExitGUI();
          break;
      }
    }

    public static void RefreshFilteredList()
    {
      if ((UnityEngine.Object) ActionSelector.instance == (UnityEngine.Object) null)
        return;
      ActionSelector.instance.FilterActionList();
    }

    private void FilterActionList()
    {
      Actions.FilterActions(this.searchString, this.searchBox.SearchMode);
      this.SanityCheckSelection();
    }

    private void SelectFirstMatchingAction()
    {
      if (Actions.FilteredCategoryIDs.Count == 0)
        return;
      string category = Actions.Categories[Actions.FilteredCategoryIDs[0]];
      this.SelectAction(Actions.GetActionsInCategoryFiltered(category)[0], category);
    }

    private void SelectAction(System.Type actionType, string category)
    {
      if (actionType == null || !actionType.IsSubclassOf(typeof (FsmStateAction)))
        return;
      this.autoScroll = true;
      this.Repaint();
      if (actionType == this.selectedAction && category == this.selectedActionCategory)
        return;
      this.selectedAction = actionType;
      this.selectedActionCategory = category;
      this.obsoleteWarning = CustomAttributeHelpers.GetObsoleteMessage(this.selectedAction);
      this.previewAction = (FsmStateAction) Activator.CreateInstance(actionType);
      this.previewAction.Reset();
      FsmEditorSettings.SelectedActionCategory = Actions.GetCategoryIndex(category);
      FsmEditorSettings.SaveSettings();
    }

    private void StartDragAction(System.Type actionType, string category)
    {
      DragAndDropManager.StartAddAction(actionType);
      this.SelectAction(actionType, category);
    }

    private void SelectPreviousAction()
    {
      if (this.selectedAction == null || this.beforeSelected == null)
        return;
      this.SelectAction(this.beforeSelected, this.beforeSelectedCategory);
    }

    private void SelectNextAction()
    {
      if (this.selectedAction == null || this.afterSelected == null)
        return;
      this.SelectAction(this.afterSelected, this.afterSelectedCategory);
    }

    public void ClearSearch()
    {
      this.searchBox.Clear();
      this.searchString = "";
      this.UpdateSearchResults();
      this.Repaint();
    }

    public void SelectNone()
    {
      this.previewAction = (FsmStateAction) null;
      this.selectedAction = (System.Type) null;
      this.selectedActionCategory = "";
      this.obsoleteWarning = "";
      this.Repaint();
    }

    private GenericMenu GenerateActionContextMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      if (this.selectedAction == null)
      {
        genericMenu.AddDisabledItem(new GUIContent("Select an Action"));
        return genericMenu;
      }
      if (FsmSearch.GetUsageCount(this.selectedAction) == 0)
      {
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Find_Action));
      }
      else
      {
        foreach (FsmInfo fsmInfo in FsmSearch.GetUsage(this.selectedAction))
          genericMenu.AddItem(new GUIContent(string.Format("{0}/{1}", (object) Strings.Menu_Find_Action, (object) fsmInfo.AsMenuItemActionSelector())), false, new GenericMenu.MenuFunction2(FsmInfo.SelectFsmInfo), (object) fsmInfo);
      }
      if (FsmEditor.SelectedFsm == null || FsmEditor.SelectedState == null)
      {
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Select_a_State_to_add_Actions));
      }
      else
      {
        genericMenu.AddSeparator("");
        genericMenu.AddItem(new GUIContent(Strings.Menu_Add_to_Top_of_Action_List), false, new GenericMenu.MenuFunction(this.AddSelectedActionToTop));
        if (FsmEditor.StateInspector.SelectedAction == null)
        {
          genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Add_Before_Selected_Action));
          genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_Add_After_Selected_Action));
        }
        else
        {
          genericMenu.AddItem(new GUIContent(Strings.Menu_Add_Before_Selected_Action), false, new GenericMenu.MenuFunction(this.AddSelectedActionBefore));
          genericMenu.AddItem(new GUIContent(Strings.Menu_Add_After_Selected_Action), false, new GenericMenu.MenuFunction(this.AddSelectedActionAfter));
        }
        genericMenu.AddItem(new GUIContent(Strings.Menu_Add_to_End_of_Action_List), false, new GenericMenu.MenuFunction(this.AddSelectedActionToEnd));
      }
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Find_Script), false, new GenericMenu.MenuFunction2(ActionScripts.PingAssetByType), (object) this.selectedAction);
      genericMenu.AddItem(new GUIContent(Strings.Menu_Select_Script), false, new GenericMenu.MenuFunction2(ActionScripts.SelectAssetByType), (object) this.selectedAction);
      genericMenu.AddItem(new GUIContent(Strings.Menu_Edit_Script), false, new GenericMenu.MenuFunction2(ActionScripts.EditAssetByType), (object) this.selectedAction);
      genericMenu.AddSeparator("");
      if (Actions.CategoryContainsAction("Favorites", this.selectedAction))
        genericMenu.AddItem(new GUIContent(Strings.Menu_Remove_From_Favorrites), false, new GenericMenu.MenuFunction2(ActionSelector.RemoveFromFavorites), (object) this.selectedAction);
      else
        genericMenu.AddItem(new GUIContent(Strings.Menu_Add_To_Favorites), false, new GenericMenu.MenuFunction2(ActionSelector.AddToFavorites), (object) this.selectedAction);
      if (Actions.CategoryContainsAction("Recent", this.selectedAction))
        genericMenu.AddItem(new GUIContent("Remove from Recent List"), false, (GenericMenu.MenuFunction) (() => Actions.RemoveActionFromCategory("Recent", this.selectedAction)));
      return genericMenu;
    }

    public void FinishAddAction()
    {
      if ((UnityEngine.Object) ActionSelector.instance == (UnityEngine.Object) null)
        return;
      ActionSelector.AddActionToRecent(ActionSelector.instance.selectedAction);
      if (FsmEditorSettings.CloseActionBrowserOnEnter && !EditorHacks.IsDocked((EditorWindow) this))
        ActionSelector.instance.Close();
      GUIHelpers.SafeExitGUI();
      this.Repaint();
    }

    public static void AddActionToRecent(System.Type actionType)
    {
      Actions.InsertActionAtTopOfCategory("Recent", actionType);
      ActionSelector.EnforceRecentCategorySize();
      ActionSelector.RefreshFilteredList();
    }

    private void AddSelectedActionToState()
    {
      if (this.selectedAction == null || FsmEditor.SelectedState == null)
        return;
      FsmEditor.StateInspector.AddAction(this.selectedAction);
      this.FinishAddAction();
    }

    private void AddSelectedActionToTop()
    {
      if (this.selectedAction == null || FsmEditor.SelectedState == null)
        return;
      FsmEditor.StateInspector.AddActionToTop(this.selectedAction);
      this.FinishAddAction();
    }

    private void AddSelectedActionToEnd()
    {
      if (this.selectedAction == null || FsmEditor.SelectedState == null)
        return;
      FsmEditor.StateInspector.AddActionToEnd(this.selectedAction);
      this.FinishAddAction();
    }

    private void AddSelectedActionBefore()
    {
      if (this.selectedAction == null || FsmEditor.SelectedState == null)
        return;
      FsmEditor.StateInspector.AddActionBeforeSelectedAction(this.selectedAction);
      this.FinishAddAction();
    }

    private void AddSelectedActionAfter()
    {
      if (this.selectedAction == null || FsmEditor.SelectedState == null)
        return;
      FsmEditor.StateInspector.AddActionAfterSelectedAction(this.selectedAction);
      this.FinishAddAction();
    }

    private void AddSelectedActionToNewFSM()
    {
      if (this.selectedAction == null || (UnityEngine.Object) Selection.activeGameObject == (UnityEngine.Object) null)
        return;
      FsmEditor.AddFsm();
      FsmEditor.StateInspector.AddAction(this.selectedAction);
      this.FinishAddAction();
    }

    private GenericMenu GenerateSettingsMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent(Strings.Menu_Show_Preview), FsmEditorSettings.ShowActionPreview, new GenericMenu.MenuFunction(this.ToggleShowActionPreview));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Hide_Obsolete_Actions), FsmEditorSettings.HideObsoleteActions, new GenericMenu.MenuFunction(this.ToggleHideObsoleteActions));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Show_Used_Actions_Only), FsmEditorSettings.ShowUsedActionsOnly, new GenericMenu.MenuFunction(this.ToggleShowUsedActionsOnly));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Auto_Refresh_Action_Usage), FsmEditorSettings.AutoRefreshActionUsage, (GenericMenu.MenuFunction) (() =>
      {
        ActionSelector.ToggleAutoRefreshActionUsage();
        if (!FsmEditorSettings.AutoRefreshActionUsage)
          return;
        this.RefreshActionUsage();
      }));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Close_Window_After_Adding_Action), FsmEditorSettings.CloseActionBrowserOnEnter, new GenericMenu.MenuFunction(ActionSelector.ToggleCloseActionBrowser));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Tools_Custom_Action_Wizard), false, new GenericMenu.MenuFunction(ActionSelector.OpenCustomActionWizard));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Tools_Select_Used_Actions_in_Project_View), false, new GenericMenu.MenuFunction(ActionScripts.SelectUsedActions));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Tools_Select_Filtered_Actions_in_Project_View), false, new GenericMenu.MenuFunction(ActionScripts.SelectFilteredActions));
      string path = Strings.Menu_Recent_Category_Size + "/";
      genericMenu.AddItem(new GUIContent(path + "10"), FsmEditorSettings.ActionBrowserRecentSize == 10, new GenericMenu.MenuFunction2(ActionSelector.SetRecentSize), (object) 10);
      genericMenu.AddItem(new GUIContent(path + "20"), FsmEditorSettings.ActionBrowserRecentSize == 20, new GenericMenu.MenuFunction2(ActionSelector.SetRecentSize), (object) 20);
      genericMenu.AddItem(new GUIContent(path + "50"), FsmEditorSettings.ActionBrowserRecentSize == 50, new GenericMenu.MenuFunction2(ActionSelector.SetRecentSize), (object) 50);
      genericMenu.AddItem(new GUIContent(path + "100"), FsmEditorSettings.ActionBrowserRecentSize == 100, new GenericMenu.MenuFunction2(ActionSelector.SetRecentSize), (object) 100);
      genericMenu.AddSeparator(path);
      genericMenu.AddItem(new GUIContent(path + Strings.Menu_Clear_List), false, new GenericMenu.MenuFunction(ActionSelector.ClearRecentCategory));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Disable_Window_When_Playing), FsmEditorSettings.DisableActionBrowerWhenPlaying, new GenericMenu.MenuFunction(ActionSelector.ToggleDisableWhenPlayingSetting));
      return genericMenu;
    }

    public static void OpenCustomActionWizard() => EditorWindow.GetWindow<CustomActionWizard>(true);

    private static void ClearRecentCategory()
    {
      List<System.Type> actionsInCategory = Actions.GetActionsInCategory("Recent");
      if (actionsInCategory == null)
        return;
      actionsInCategory.Clear();
      ActionSelector.RefreshFilteredList();
    }

    private static void SetRecentSize(object userdata)
    {
      FsmEditorSettings.ActionBrowserRecentSize = (int) userdata;
      FsmEditorSettings.SaveSettings();
      ActionSelector.EnforceRecentCategorySize();
    }

    private static void EnforceRecentCategorySize()
    {
      int browserRecentSize = FsmEditorSettings.ActionBrowserRecentSize;
      List<System.Type> actionsInCategory = Actions.GetActionsInCategory("Recent");
      if (actionsInCategory.Count <= browserRecentSize)
        return;
      actionsInCategory.RemoveRange(browserRecentSize, actionsInCategory.Count - browserRecentSize);
    }

    private void ToggleShowUsedActionsOnly()
    {
      FsmEditorSettings.ShowUsedActionsOnly = !FsmEditorSettings.ShowUsedActionsOnly;
      FsmEditorSettings.SaveSettings();
      this.FilterActionList();
      this.Repaint();
    }

    private void ToggleShowActionPreview()
    {
      FsmEditorSettings.ShowActionPreview = !FsmEditorSettings.ShowActionPreview;
      FsmEditorSettings.SaveSettings();
      this.Repaint();
    }

    private void ToggleHideObsoleteActions()
    {
      FsmEditorSettings.HideObsoleteActions = !FsmEditorSettings.HideObsoleteActions;
      FsmEditorSettings.SaveSettings();
      Actions.BuildList();
      this.FilterActionList();
      this.Repaint();
    }

    private static void ToggleCloseActionBrowser()
    {
      FsmEditorSettings.CloseActionBrowserOnEnter = !FsmEditorSettings.CloseActionBrowserOnEnter;
      FsmEditorSettings.SaveSettings();
    }

    private static void ToggleAutoRefreshActionUsage()
    {
      FsmEditorSettings.AutoRefreshActionUsage = !FsmEditorSettings.AutoRefreshActionUsage;
      FsmEditorSettings.SaveSettings();
    }

    private static void ToggleDisableWhenPlayingSetting()
    {
      FsmEditorSettings.DisableActionBrowerWhenPlaying = !FsmEditorSettings.DisableActionBrowerWhenPlaying;
      FsmEditorSettings.SaveSettings();
    }
  }
}
