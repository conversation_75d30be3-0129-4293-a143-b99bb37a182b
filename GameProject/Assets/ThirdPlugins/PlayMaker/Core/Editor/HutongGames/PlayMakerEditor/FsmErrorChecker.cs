// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmErrorChecker
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using HutongGames.PlayMaker.Actions;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  internal static class FsmErrorChecker
  {
    public static Action OnChanged;
    private static GameObject gameObject;
    private static Fsm checkingFsm;
    private static FsmState checkingState;
    private static FsmStateAction checkingAction;
    private static string checkingParameter;
    private static bool ownerIsAsset;
    private static object[] attributes;
    private static readonly List<FsmError> FsmErrorList = new List<FsmError>();
    private static FsmEventTarget fsmEventTargetContext;
    private static FsmEventTarget fsmEventTargetContextGlobal;
    private static bool checkForErrors;
    private static Fsm checkFsm;
    private static readonly Dictionary<FsmState, string> stateTransitionErrors = new Dictionary<FsmState, string>();
    private static readonly Dictionary<FsmState, string> stateErrors = new Dictionary<FsmState, string>();
    private static readonly Dictionary<FsmStateAction, string> actionErrors = new Dictionary<FsmStateAction, string>();
    private static EditorCoroutine routine;

    private static FsmError AddError(FsmError error)
    {
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.SameAs(error))
          return error;
      }
      FsmErrorChecker.FsmErrorList.Add(error);
      return error;
    }

    private static FsmError AddError(
      FsmState state,
      FsmStateAction action,
      string parameter,
      string error)
    {
      return FsmErrorChecker.AddError(new FsmError(state, action, parameter, error));
    }

    private static FsmError AddError(
      FsmState state,
      FsmTransition transition,
      string error)
    {
      return FsmErrorChecker.AddError(new FsmError(state, transition, error));
    }

    private static FsmError AddParameterError(string error) => FsmErrorChecker.AddError(FsmErrorChecker.checkingState, FsmErrorChecker.checkingAction, FsmErrorChecker.checkingParameter, error);

    private static FsmError AddRequiredFieldError()
    {
      FsmError fsmError = FsmErrorChecker.AddError(FsmErrorChecker.checkingState, FsmErrorChecker.checkingAction, FsmErrorChecker.checkingParameter, "Required Field: " + FsmErrorChecker.checkingParameter);
      fsmError.Type = FsmError.ErrorType.requiredField;
      return fsmError;
    }

    public static FsmError AddRuntimeError(string error) => FsmErrorChecker.AddError(new FsmError()
    {
      Fsm = FsmExecutionStack.ExecutingFsm,
      State = FsmExecutionStack.ExecutingState,
      Action = FsmExecutionStack.ExecutingAction,
      ErrorString = error,
      RuntimeError = true
    });

    public static void Init()
    {
      FsmErrorChecker.ClearErrors();
      FsmErrorChecker.CheckForErrors();
    }

    public static void ClearErrors(bool deleteRuntimeErrors = true)
    {
      if (!Application.isPlaying && !deleteRuntimeErrors)
        FsmErrorChecker.FsmErrorList.RemoveAll((Predicate<FsmError>) (x => !x.RuntimeError));
      else
        FsmErrorChecker.FsmErrorList.Clear();
      FsmErrorChecker.gameObject = (GameObject) null;
      FsmErrorChecker.checkingFsm = (Fsm) null;
      FsmErrorChecker.checkingState = (FsmState) null;
      FsmErrorChecker.checkingAction = (FsmStateAction) null;
      FsmErrorChecker.checkingParameter = (string) null;
      FsmErrorChecker.attributes = (object[]) null;
      FsmErrorChecker.checkForErrors = false;
      FsmErrorChecker.checkFsm = (Fsm) null;
      FsmErrorChecker.fsmEventTargetContext = (FsmEventTarget) null;
      FsmErrorChecker.fsmEventTargetContextGlobal = (FsmEventTarget) null;
      FsmErrorChecker.ClearErrorStringCaches();
    }

    public static void Refresh() => FsmErrorChecker.CheckForErrors();

    public static void CheckForErrors() => FsmErrorChecker.checkForErrors = true;

    public static void CheckFsmForErrors(Fsm fsm, bool immediate = false)
    {
      if (fsm == null)
        return;
      if (immediate)
      {
        FsmErrorChecker.ClearFsmErrors(fsm);
        FsmErrorChecker.DoCheckFsmForErrors(fsm);
      }
      else
        FsmErrorChecker.checkFsm = fsm;
    }

    public static void Update()
    {
      if (FsmErrorChecker.SkipErrorCheck() || FsmEditor.NeedRepaint)
        return;
      if (FsmErrorChecker.checkForErrors)
      {
        if (FsmErrorChecker.routine != null)
          FsmErrorChecker.routine.Stop();
        FsmErrorChecker.CheckingForErrors = true;
        FsmErrorChecker.routine = EditorCoroutine.Start(FsmErrorChecker.DoCheckForErrors());
        FsmSelector.RefreshView();
      }
      else if (FsmErrorChecker.checkFsm != null)
      {
        FsmErrorChecker.ClearFsmErrors(FsmErrorChecker.checkFsm);
        FsmErrorChecker.DoCheckFsmForErrors(FsmErrorChecker.checkFsm);
        FsmSelector.RefreshView();
      }
      FsmErrorChecker.checkForErrors = false;
      FsmErrorChecker.checkFsm = (Fsm) null;
    }

    private static bool SkipErrorCheck()
    {
      if (!FsmEditorSettings.EnableRealtimeErrorChecker || PlayMakerFSM.ApplicationIsQuitting)
        return true;
      return FsmEditorSettings.DisableErrorCheckerWhenPlaying && Application.isPlaying;
    }

    public static bool CheckingForErrors { get; private set; }

    public static float CheckingForErrorsProgress { get; private set; }

    private static IEnumerator DoCheckForErrors()
    {
      int timeslice = FsmEditorSettings.ErrorCheckTimeslice;
      Stopwatch timer = Stopwatch.StartNew();
      long totalTime = 0;
      int slices = 0;
      FsmErrorChecker.ClearErrors(false);
      int fsmCount = FsmEditor.FsmList.Count;
      float totalCount = (float) fsmCount + (float) Templates.List.Count;
      for (int i = 0; i < FsmEditor.FsmList.Count; ++i)
      {
        Fsm fsm = FsmEditor.FsmList[i];
        FsmErrorChecker.CheckingForErrorsProgress = (float) i / totalCount;
        FsmErrorChecker.DoCheckFsmForErrors(fsm);
        if (timer.ElapsedMilliseconds > (long) timeslice)
        {
          timer = Stopwatch.StartNew();
          totalTime += (long) timeslice;
          ++slices;
          yield return (object) null;
        }
      }
      for (int i = 0; i < Templates.List.Count; ++i)
      {
        FsmTemplate template = Templates.List[i];
        if (!template.IsHidden())
        {
          FsmErrorChecker.CheckingForErrorsProgress = (float) (fsmCount + i) / totalCount;
          FsmErrorChecker.DoCheckFsmForErrors(template.fsm);
          if (timer.ElapsedMilliseconds > (long) timeslice)
          {
            timer = Stopwatch.StartNew();
            totalTime += (long) timeslice;
            ++slices;
            yield return (object) null;
          }
        }
      }
      totalTime += timer.ElapsedMilliseconds;
      if (FsmErrorChecker.OnChanged != null)
        FsmErrorChecker.OnChanged();
      FsmErrorChecker.CheckingForErrors = false;
      FsmEditor.RepaintAll();
    }

    private static void DoCheckFsmForErrors(Fsm fsm)
    {
      if (fsm == null || fsm.OwnerObject == (UnityEngine.Object) null)
        return;
      PlayMakerFSM owner = fsm.Owner as PlayMakerFSM;
      if ((UnityEngine.Object) owner != (UnityEngine.Object) null && owner.UsesTemplate)
        return;
      if (!fsm.Initialized)
        return;
      try
      {
        FsmErrorChecker.checkingFsm = fsm;
        foreach (FsmState state in fsm.States)
        {
          state.Fsm = fsm;
          FsmErrorChecker.fsmEventTargetContextGlobal = (FsmEventTarget) null;
          foreach (FsmStateAction action in state.Actions)
          {
            if (action.Enabled)
              FsmErrorChecker.CheckActionForErrors(state, action);
          }
          FsmErrorChecker.CheckTransitionsForErrors(state);
          state.HasErrors = FsmErrorChecker.StateHasErrors(state);
        }
        fsm.HasErrors = FsmErrorChecker.FsmHasErrors(fsm);
        FsmErrorChecker.CheckActionReportForErrors();
      }
      catch (Exception ex)
      {
        UnityEngine.Debug.LogError((object) ex.ToString());
        throw;
      }
      FsmEditor.RepaintAll();
    }

    private static void ClearFsmErrors(Fsm fsm)
    {
      if (!fsm.HasErrors)
        return;
      FsmErrorChecker.FsmErrorList.RemoveAll((Predicate<FsmError>) (x => x.Fsm == fsm));
      FsmErrorChecker.ClearErrorStringCaches();
      fsm.HasErrors = false;
    }

    private static void ClearErrorStringCaches()
    {
      FsmErrorChecker.stateTransitionErrors.Clear();
      FsmErrorChecker.stateErrors.Clear();
      FsmErrorChecker.actionErrors.Clear();
    }

    [Localizable(false)]
    private static void CheckActionForErrors(FsmState state, FsmStateAction action)
    {
      if (action == null)
      {
        FsmErrorChecker.AddError(new FsmError(state, (FsmStateAction) null, Strings.FsmErrorChecker_MissingActionError));
      }
      else
      {
        action.Init(state);
        FsmErrorChecker.fsmEventTargetContext = (FsmEventTarget) null;
        string actionLabel = Labels.GetActionLabel(action);
        if (FsmEditorSettings.CheckForMissingActions && action is MissingAction)
        {
          FsmErrorChecker.AddError(new FsmError(state, action, Strings.FsmErrorChecker_StateHasMissingActionError));
        }
        else
        {
          if (FsmEditorSettings.CheckForObsoleteActions)
          {
            string obsoleteMessage = CustomAttributeHelpers.GetObsoleteMessage(action.GetType());
            if (!string.IsNullOrEmpty(obsoleteMessage))
              FsmErrorChecker.AddError(new FsmError(state, action, "Obsolete: " + obsoleteMessage));
          }
          System.Type type1 = action.GetType();
          foreach (FieldInfo field in ActionData.GetFields(type1))
          {
            if (!CustomAttributeHelpers.HasAttribute<NoErrorCheckAttribute>(field))
            {
              System.Type fieldType = field.FieldType;
              object obj = field.GetValue((object) action);
              System.Type type2 = typeof (FsmEventTarget);
              if (fieldType == type2)
              {
                FsmEventTarget fsmEventTarget = (FsmEventTarget) obj;
                if (actionLabel == "Set Event Target")
                  FsmErrorChecker.fsmEventTargetContextGlobal = fsmEventTarget;
                else
                  FsmErrorChecker.fsmEventTargetContext = fsmEventTarget;
              }
              FsmErrorChecker.CheckActionParameter(state, action, field);
            }
          }
          string errorString = "";
          try
          {
            errorString = action.ErrorCheck();
          }
          catch (Exception ex)
          {
            UnityEngine.Debug.Log((object) ("Bad ErrorCheck: " + (object) type1 + "\n" + (object) ex));
          }
          if (string.IsNullOrEmpty(errorString))
            return;
          if (!errorString.StartsWith("@"))
          {
            FsmErrorChecker.AddError(new FsmError(state, action, errorString));
          }
          else
          {
            int num = errorString.IndexOf(':');
            if (num > 1)
            {
              string parameter = errorString.Substring(1, num - 1);
              FsmErrorChecker.AddError(new FsmError(state, action, parameter, errorString.Substring(num + 1)));
            }
            else
              FsmErrorChecker.AddError(new FsmError(state, action, errorString));
          }
        }
      }
    }

    private static void CheckActionParameter(
      FsmState state,
      FsmStateAction action,
      FieldInfo field)
    {
      if (state == null || action == null || (field == null || state.Fsm == null))
        return;
      FsmErrorChecker.ownerIsAsset = FsmPrefabs.IsPersistent(state.Fsm.OwnerObject);
      FsmErrorChecker.gameObject = state.Fsm.GameObject;
      FsmErrorChecker.checkingFsm = state.Fsm;
      FsmErrorChecker.checkingState = state;
      FsmErrorChecker.checkingAction = action;
      FsmErrorChecker.checkingParameter = field.Name;
      System.Type fieldType = field.FieldType;
      object obj = field.GetValue((object) action);
      FsmErrorChecker.attributes = CustomAttributeHelpers.GetCustomAttributes(field);
      object fieldValue = obj;
      FsmErrorChecker.CheckParameterType(fieldType, fieldValue);
    }

    private static void CheckParameterType(System.Type type, object fieldValue)
    {
      if (type == null)
        return;
      if (type == typeof (FsmGameObject))
        FsmErrorChecker.CheckFsmGameObjectParameter((FsmGameObject) fieldValue);
      else if (type == typeof (FsmOwnerDefault))
        FsmErrorChecker.CheckOwnerDefaultParameter((FsmOwnerDefault) fieldValue);
      else if (type == typeof (GameObject))
        FsmErrorChecker.CheckGameObjectParameter((GameObject) fieldValue);
      else if (type == typeof (FsmEvent))
        FsmErrorChecker.CheckFsmEventParameter((FsmEvent) fieldValue);
      else if (type == typeof (FsmString))
        FsmErrorChecker.CheckFsmStringParameter((FsmString) fieldValue);
      else if (type == typeof (string))
        FsmErrorChecker.CheckStringParameter((string) fieldValue);
      else if (type.IsArray)
      {
        Array array = (Array) fieldValue;
        if (array != null)
        {
          System.Type elementType = type.GetElementType();
          for (int index = 0; index < array.Length; ++index)
            FsmErrorChecker.CheckParameterType(elementType, array.GetValue(index));
        }
      }
      else
      {
        UnityEngine.Object unityObject = fieldValue as UnityEngine.Object;
        if (unityObject != (UnityEngine.Object) null)
          FsmErrorChecker.CheckObjectParameter(unityObject);
      }
      if (type.IsSubclassOf(typeof (NamedVariable)))
      {
        if (!FsmEditorSettings.CheckForRequiredField || !FsmErrorChecker.IsRequiredField())
          return;
        if (fieldValue == null)
        {
          FsmErrorChecker.AddRequiredFieldError();
        }
        else
        {
          NamedVariable namedVariable = (NamedVariable) fieldValue;
          if (!namedVariable.UseVariable && !FsmErrorChecker.IsVariableField() || !string.IsNullOrEmpty(namedVariable.Name))
            return;
          FsmErrorChecker.AddRequiredFieldError();
        }
      }
      else
      {
        if (type != typeof (FsmVar) || !FsmEditorSettings.CheckForRequiredField || !FsmErrorChecker.IsRequiredField())
          return;
        FsmVar fsmVar = (FsmVar) fieldValue;
        if (fsmVar == null || !fsmVar.useVariable || fsmVar.NamedVar != null && !fsmVar.NamedVar.IsNone)
          return;
        FsmErrorChecker.AddRequiredFieldError();
      }
    }

    private static void CheckFsmStringParameter(FsmString fsmString)
    {
      if (fsmString == null || fsmString.UseVariable)
        return;
      FsmErrorChecker.CheckStringParameter(fsmString.Value);
    }

    private static void CheckStringParameter(string text)
    {
      if (!FsmEditorSettings.CheckForRequiredField || !string.IsNullOrEmpty(text) || !FsmErrorChecker.IsRequiredField())
        return;
      FsmErrorChecker.AddRequiredFieldError();
    }

    private static void CheckFsmEventParameter(FsmEvent fsmEvent)
    {
      if (FsmEditorSettings.CheckForRequiredField && fsmEvent == null && FsmErrorChecker.IsRequiredField())
      {
        FsmErrorChecker.AddRequiredFieldError();
      }
      else
      {
        if (!FsmEditorSettings.CheckForEventNotUsed || CustomAttributeHelpers.HasAttribute<EventNotSentAttribute>((IEnumerable<object>) FsmErrorChecker.attributes))
          return;
        FsmErrorChecker.CheckForEventErrors(fsmEvent);
      }
    }

    private static void CheckForEventErrors(FsmEvent fsmEvent)
    {
      if (FsmEvent.IsNullOrEmpty(fsmEvent))
        return;
      FsmEventTarget fsmEventTarget = FsmErrorChecker.fsmEventTargetContextGlobal;
      if (FsmErrorChecker.fsmEventTargetContext != null)
        fsmEventTarget = FsmErrorChecker.fsmEventTargetContext;
      if (fsmEventTarget == null)
        fsmEventTarget = new FsmEventTarget();
      Fsm fsmTarget = Events.GetFsmTarget(FsmErrorChecker.checkingFsm, fsmEventTarget);
      switch (fsmEventTarget.target)
      {
        case FsmEventTarget.EventTarget.Self:
          if (FsmErrorChecker.checkingState == null || Events.FsmStateRespondsToEvent(FsmErrorChecker.checkingState, fsmEvent))
            break;
          FsmError fsmError = FsmErrorChecker.AddParameterError(Strings.FsmErrorChecker_InvalidEventError);
          fsmError.Type = FsmError.ErrorType.missingTransitionEvent;
          fsmError.info = fsmEvent.Name;
          break;
        case FsmEventTarget.EventTarget.FSMComponent:
          if (fsmTarget == null || Events.FsmRespondsToEvent(fsmTarget, fsmEvent))
            break;
          FsmErrorChecker.AddParameterError(Strings.FsmErrorChecker_TargetFsmMissingEventError);
          break;
        case FsmEventTarget.EventTarget.BroadcastAll:
          FsmErrorChecker.CheckGlobalEvent(fsmEvent);
          break;
      }
    }

    private static void CheckGlobalEvent(FsmEvent fsmEvent)
    {
      if (FsmEvent.IsEventGlobal(fsmEvent.Name))
        return;
      FsmEvent.SetEventIsGlobal(fsmEvent.Name);
    }

    private static void CheckOwnerDefaultParameter(FsmOwnerDefault ownerDefault)
    {
      if (ownerDefault == null)
        ownerDefault = new FsmOwnerDefault();
      if (ownerDefault.OwnerOption == OwnerDefaultOption.UseOwner)
        FsmErrorChecker.CheckBaseGameObject(FsmErrorChecker.gameObject);
      else
        FsmErrorChecker.CheckFsmGameObjectParameter(ownerDefault.GameObject);
    }

    private static void CheckFsmGameObjectParameter(FsmGameObject fsmGameObject)
    {
      if (fsmGameObject == null)
        fsmGameObject = new FsmGameObject(string.Empty);
      if (fsmGameObject.UseVariable)
      {
        if (FsmEditorSettings.CheckForRequiredField && string.IsNullOrEmpty(fsmGameObject.Name) && FsmErrorChecker.IsRequiredField())
          FsmErrorChecker.AddRequiredFieldError();
        else
          FsmErrorChecker.CheckBaseGameObject(fsmGameObject.Value);
      }
      else if (FsmEditorSettings.CheckForRequiredField && (UnityEngine.Object) fsmGameObject.Value == (UnityEngine.Object) null && FsmErrorChecker.IsRequiredField())
        FsmErrorChecker.AddRequiredFieldError();
      else
        FsmErrorChecker.CheckBaseGameObject(fsmGameObject.Value);
    }

    private static void CheckPrefabRestrictions(GameObject go)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null || !FsmErrorChecker.ownerIsAsset || EditorUtility.IsPersistent((UnityEngine.Object) go))
        return;
      FsmErrorChecker.AddParameterError(Strings.FsmErrorChecker_PrefabReferencingSceneObjectError);
    }

    private static void CheckGameObjectParameter(GameObject go)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null)
      {
        if (!FsmEditorSettings.CheckForRequiredField || !FsmErrorChecker.IsRequiredField())
          return;
        FsmErrorChecker.AddRequiredFieldError();
      }
      else
        FsmErrorChecker.CheckBaseGameObject(go);
    }

    private static void CheckBaseGameObject(GameObject go)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null)
        return;
      if (FsmEditorSettings.CheckForRequiredComponent)
        FsmErrorChecker.CheckForRequiredComponents(go);
      if (!FsmEditorSettings.CheckForPrefabRestrictions)
        return;
      FsmErrorChecker.CheckPrefabRestrictions(go);
    }

    private static void CheckForRequiredComponents(GameObject go)
    {
      if (!FsmEditorSettings.CheckForRequiredComponent || (UnityEngine.Object) go == (UnityEngine.Object) null)
        return;
      foreach (Attribute attribute in FsmErrorChecker.attributes)
      {
        if (attribute is CheckForComponentAttribute componentAttribute)
        {
          FsmErrorChecker.CheckGameObjectHasComponent(go, componentAttribute.Type0);
          FsmErrorChecker.CheckGameObjectHasComponent(go, componentAttribute.Type1);
          FsmErrorChecker.CheckGameObjectHasComponent(go, componentAttribute.Type2);
        }
      }
    }

    private static void CheckGameObjectHasComponent(GameObject go, System.Type component)
    {
      if ((UnityEngine.Object) go == (UnityEngine.Object) null || component == null || !((UnityEngine.Object) go.GetComponent(component) == (UnityEngine.Object) null))
        return;
      FsmError fsmError = FsmErrorChecker.AddParameterError(Strings.FsmErrorChecker_RequiresComponentError + Labels.StripUnityEngineNamespace(component.ToString()) + " Component!");
      fsmError.Type = FsmError.ErrorType.missingRequiredComponent;
      fsmError.GameObject = go;
      fsmError.ObjectType = component;
    }

    private static void CheckObjectParameter(UnityEngine.Object unityObject)
    {
    }

    private static bool IsRequiredField()
    {
      foreach (Attribute attribute in FsmErrorChecker.attributes)
      {
        if (attribute is RequiredFieldAttribute)
          return true;
      }
      return false;
    }

    private static bool IsVariableField()
    {
      foreach (Attribute attribute in FsmErrorChecker.attributes)
      {
        if (attribute is UIHintAttribute uiHintAttribute && uiHintAttribute.Hint == UIHint.Variable)
          return true;
      }
      return false;
    }

    private static void CheckTransitionsForErrors(FsmState state)
    {
      List<string> stringList = new List<string>();
      foreach (FsmTransition transition in state.Transitions)
      {
        if (FsmEditorSettings.CheckForTransitionMissingEvent && string.IsNullOrEmpty(transition.EventName))
          FsmErrorChecker.AddError(state, transition, Strings.FsmErrorChecker_TransitionMissingEventError);
        if (FsmEditorSettings.CheckForDuplicateTransitionEvent && stringList.Contains(transition.EventName))
          FsmErrorChecker.AddError(state, transition, Strings.FsmErrorChecker_DuplicateTransitionEventError);
        if (!string.IsNullOrEmpty(transition.EventName))
          stringList.Add(transition.EventName);
        if (FsmEditorSettings.CheckForTransitionMissingTarget && string.IsNullOrEmpty(transition.ToState) && !FsmEventManager.IsOutputEvent(state.Fsm, transition.FsmEvent))
          FsmErrorChecker.AddError(state, transition, Strings.FsmErrorChecker_TransitionMissingTargetError);
        if (state.Fsm != null)
        {
          FsmEvent fsmEvent = transition.FsmEvent;
          if (fsmEvent != null && fsmEvent.IsSystemEvent)
            FsmErrorChecker.CheckSystemEventsForErrors(state, transition, fsmEvent);
        }
      }
    }

    [Localizable(false)]
    private static void CheckSystemEventsForErrors(
      FsmState state,
      FsmTransition transition,
      FsmEvent fsmEvent)
    {
      GameObject gameObject = state.Fsm.GameObject;
      if ((UnityEngine.Object) gameObject == (UnityEngine.Object) null)
        return;
      if (FsmEditorSettings.CheckForMouseEventErrors && fsmEvent.Name.Contains("MOUSE") && ((UnityEngine.Object) gameObject.GetComponent<Collider>() == (UnityEngine.Object) null && (UnityEngine.Object) gameObject.GetComponent<Collider2D>() == (UnityEngine.Object) null))
        FsmErrorChecker.AddError(state, transition, Strings.FsmErrorChecker_MouseEventsNeedCollider);
      if (FsmEditorSettings.CheckForCollisionEventErrors && fsmEvent.Name.Contains("COLLISION") || fsmEvent.Name.Contains("TRIGGER"))
      {
        if (fsmEvent.Name.Contains("2D"))
        {
          if ((UnityEngine.Object) gameObject.GetComponent<Collider2D>() == (UnityEngine.Object) null && (UnityEngine.Object) gameObject.GetComponent<Rigidbody2D>() == (UnityEngine.Object) null)
            FsmErrorChecker.AddError(state, transition, Strings.FsmErrorChecker_CollisionEventsNeedCollider2D);
        }
        else if ((UnityEngine.Object) gameObject.GetComponent<Collider>() == (UnityEngine.Object) null && (UnityEngine.Object) gameObject.GetComponent<Rigidbody>() == (UnityEngine.Object) null)
          FsmErrorChecker.AddError(state, transition, Strings.FsmErrorChecker_CollisionEventsNeedCollider);
      }
      if (!FsmEditorSettings.CheckForCollisionEventErrors || !fsmEvent.Name.Contains("CONTROLLER COLLIDER") || !((UnityEngine.Object) gameObject.GetComponent<CharacterController>() == (UnityEngine.Object) null))
        return;
      FsmErrorChecker.AddError(state, transition, Strings.FsmErrorChecker_ControllerCollisionEventsNeedController);
    }

    private static void CheckActionReportForErrors()
    {
      foreach (ActionReport actionReport in ActionReport.ActionReportList)
      {
        if (actionReport.isError && actionReport.actionIndex < actionReport.state.Actions.Length)
          FsmErrorChecker.AddError(actionReport.state, actionReport.state.Actions[actionReport.actionIndex], actionReport.parameter, actionReport.logText);
      }
    }

    public static List<FsmError> GetErrors()
    {
      FsmErrorChecker.SanityCheckErrorList();
      return FsmErrorChecker.FsmErrorList;
    }

    public static FsmError FindError(FsmError error)
    {
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.SameAs(error))
          return fsmError;
      }
      return (FsmError) null;
    }

    public static int CountAllErrors() => FsmErrorChecker.FsmErrorList.Count;

    public static int CountSetupErrors()
    {
      int num = 0;
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (!fsmError.RuntimeError)
          ++num;
      }
      return num;
    }

    public static int CountRuntimeErrors()
    {
      int num = 0;
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.RuntimeError)
          ++num;
      }
      return num;
    }

    public static int CountFsmErrors(Fsm fsm)
    {
      int num = 0;
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.Fsm == fsm)
          ++num;
      }
      return num;
    }

    public static int CountStateErrors(FsmState state)
    {
      int num = 0;
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.State == state)
          ++num;
      }
      return num;
    }

    public static int CountActionErrors(FsmStateAction action)
    {
      int num = 0;
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.Action == action)
          ++num;
      }
      return num;
    }

    public static List<FsmError> GetParameterErrors(
      FsmStateAction action,
      string parameter)
    {
      List<FsmError> fsmErrorList = new List<FsmError>();
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.Action == action && fsmError.Parameter == parameter)
          fsmErrorList.Add(fsmError);
      }
      return fsmErrorList;
    }

    public static string GetStateTransitionErrors(FsmState state)
    {
      string str1;
      if (!FsmErrorChecker.stateTransitionErrors.TryGetValue(state, out str1))
      {
        string str2 = "State has errors\n";
        foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
        {
          if (fsmError.State == state && fsmError.Transition != null)
            str2 = str2 + fsmError.ErrorString + "\n";
        }
        str1 = str2.TrimEnd();
        FsmErrorChecker.stateTransitionErrors.Add(state, str1);
      }
      return str1;
    }

    public static string GetStateErrors(FsmState state)
    {
      string str1;
      if (!FsmErrorChecker.stateErrors.TryGetValue(state, out str1))
      {
        string str2 = "";
        FsmStateAction fsmStateAction = (FsmStateAction) null;
        foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
        {
          if (fsmError.State == state)
          {
            if (fsmStateAction != fsmError.Action)
            {
              if (fsmStateAction != null)
                str2 += "\n";
              fsmStateAction = fsmError.Action;
              str2 = str2 + Labels.GetActionLabel(fsmError.Action) + "\n";
            }
            str2 = str2 + fsmError.ErrorString + "\n";
          }
        }
        str1 = str2.TrimEnd();
        FsmErrorChecker.stateErrors.Add(state, str1);
      }
      return str1;
    }

    public static string GetActionErrors(FsmStateAction action)
    {
      string str1;
      if (!FsmErrorChecker.actionErrors.TryGetValue(action, out str1))
      {
        string str2 = "";
        foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
        {
          if (fsmError.Action == action)
            str2 = str2 + fsmError.ErrorString + "\n";
        }
        str1 = str2.TrimEnd();
        FsmErrorChecker.actionErrors.Add(action, str1);
      }
      return str1;
    }

    public static List<string> GetRuntimeErrors(FsmStateAction action)
    {
      List<string> stringList = new List<string>();
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.RuntimeError && fsmError.Action == action)
          stringList.Add(fsmError.ErrorString);
      }
      return stringList;
    }

    public static bool FsmHasErrors(Fsm fsm)
    {
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.Fsm == fsm)
          return true;
      }
      return false;
    }

    public static bool StateHasErrors(FsmState state)
    {
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.State == state)
          return true;
      }
      return false;
    }

    public static bool StateHasActionErrors(FsmState state)
    {
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.State == state && fsmError.Action != null)
          return true;
      }
      return false;
    }

    public static bool ActionHasErrors(FsmStateAction action)
    {
      foreach (FsmError fsmError in FsmErrorChecker.FsmErrorList)
      {
        if (fsmError.Action == action)
          return true;
      }
      return false;
    }

    public static void SanityCheckErrorList() => FsmErrorChecker.FsmErrorList.RemoveAll((Predicate<FsmError>) (x => x == null || x.Fsm == null || x.Fsm.OwnerObject == (UnityEngine.Object) null));

    [Conditional("PROFILE")]
    private static void ProfileStart(string message) => BlockTimer.Start(message);

    [Conditional("PROFILE")]
    private static void ProfileEnd() => UnityEngine.Debug.Log((object) BlockTimer.End());

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }
  }
}
