// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.BezierLink
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  internal class BezierLink : BaseLink
  {
    private static BezierLink instance;
    private float fromTangentDir;
    private float toTangentDir;

    public static BezierLink Instance => BezierLink.instance ?? (BezierLink.instance = new BezierLink());

    public override GraphViewLinkStyle LinkStyle => GraphViewLinkStyle.BezierLinks;

    public override void Draw(
      FsmState fromState,
      FsmState toState,
      int transitionIndex,
      Color linkColor,
      float linkWidth,
      float scale)
    {
      this.Init(fromState, toState, transitionIndex, scale);
      this.DoDefaultLinker();
      this.DrawArrowHead(linkColor);
      float a1;
      float a2;
      if (fromState == toState)
      {
        a2 = a1 = 50f * scale;
      }
      else
      {
        Vector3 vector3 = this.fromPos - this.toPos;
        a2 = a1 = Mathf.Min(vector3.magnitude * 0.5f, 40f * scale);
        if ((double) this.fromTangentDir > 0.0 && (double) this.toTangentDir > 0.0)
        {
          if ((double) this.fromStateRightX > (double) this.toStateRightX)
            a1 = Mathf.Max(a1, this.fromStateRightX - this.toStateRightX);
          else
            a2 = Mathf.Max(a2, this.toStateRightX - this.fromStateRightX);
        }
        else if ((double) this.fromTangentDir < 0.0 && (double) this.toTangentDir < 0.0)
        {
          if ((double) this.fromStateLeftX < (double) this.toStateLeftX)
            a1 = Mathf.Max(a1, this.toStateLeftX - this.fromStateLeftX);
          else
            a2 = Mathf.Max(a2, this.fromStateLeftX - this.toStateLeftX);
        }
      }
      this.fromTangent = this.fromPos;
      this.fromTangent.x += this.fromTangentDir * a2;
      this.toTangent = this.toPos;
      this.toTangent.x += this.toTangentDir * a1;
      if ((double) Mathf.Abs(this.fromPos.y - this.toPos.y) < 1.0)
        BaseLink.DrawPolylineCircuit(linkColor, linkWidth, scale, this.fromPos, this.toPos);
      else
        Handles.DrawBezier(this.fromPos, this.toPos, this.fromTangent, this.toTangent, linkColor, (double) scale < 1.0 ? (Texture2D) null : FsmEditorStyles.LineTexture, Mathf.Min(linkWidth * 2f, linkWidth * 4f * scale));
    }

    protected override void LinkRightSideToRightSide()
    {
      this.fromPos.x = this.fromStateRightX;
      this.toPos.x = this.toStateRightX;
      this.fromTangentDir = 1f;
      this.toTangentDir = 1f;
    }

    protected override void LinkRightSideToLeftSide()
    {
      this.fromPos.x = this.fromStateRightX;
      this.toPos.x = this.toStateLeftX;
      this.fromTangentDir = 1f;
      this.toTangentDir = -1f;
    }

    protected override void LinkLeftSideToLeftSide()
    {
      this.fromPos.x = this.fromStateLeftX;
      this.toPos.x = this.toStateLeftX;
      this.fromTangentDir = -1f;
      this.toTangentDir = -1f;
    }

    protected override void LinkLeftSideToRightSide()
    {
      this.fromPos.x = this.fromStateLeftX;
      this.toPos.x = this.toStateRightX;
      this.fromTangentDir = -1f;
      this.toTangentDir = 1f;
    }

    public override bool HitTest(Vector2 point, float hitDistance) => throw new NotImplementedException();
  }
}
