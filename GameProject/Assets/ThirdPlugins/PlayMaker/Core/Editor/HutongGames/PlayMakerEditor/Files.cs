// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Files
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using UnityEditor;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class Files
  {
    private static List<string> scriptList;
    private static string[] scriptPopupNames;

    public static Texture2D LoadTexture(
      string resourceName,
      int width,
      int height,
      UnityEngine.FilterMode filterMode = UnityEngine.FilterMode.Bilinear)
    {
      if ((double) EditorGUIUtility.pixelsPerPoint > 1.0)
      {
        resourceName += "@2x";
        width *= 2;
        height *= 2;
      }
      return Files.LoadTextureFromDll(resourceName, width, height, filterMode);
    }

    public static Texture2D LoadTextureFromDll(
      string resourceName,
      int width,
      int height,
      UnityEngine.FilterMode filterMode = UnityEngine.FilterMode.Bilinear)
    {
      // bool useTheme = FsmEditorSettings.UseTheme;
      // string selectedThemeFileName = FsmEditorSettings.SelectedThemeFileName;
      List<string> stringList = new List<string>();
      // if (FsmEditorStyles.UsingProSkin())
      // {
      //   if (useTheme)
      //     stringList.Add("d_" + resourceName + "_" + selectedThemeFileName);
      //   stringList.Add("d_" + resourceName);
      // }
      // if (useTheme)
      //   stringList.Add(resourceName + "_" + selectedThemeFileName);
      stringList.Add(resourceName);
      Texture2D texture2D = (Texture2D) null;
      foreach (string str in stringList)
      {
        // texture2D = !EditorApp.IsSourceCodeVersion ? Files.TryLoadTextureFromDll(str, width, height, filterMode) : Resources.Load(str, typeof (Texture2D)) as Texture2D;
        texture2D = Resources.Load(str, typeof (Texture2D)) as Texture2D;
        if ((UnityEngine.Object) texture2D != (UnityEngine.Object) null)
          break;
      }
      if ((UnityEngine.Object) texture2D == (UnityEngine.Object) null)
      {
        Debug.LogError((object) (Strings.Could_not_load_resource + resourceName));
        if (!resourceName.EndsWith("@2x"))
          texture2D = new Texture2D(width, height);
      }
      return texture2D;
    }

    private static Texture2D TryLoadTextureFromDll(
      string resourceName,
      int width,
      int height,
      UnityEngine.FilterMode filterMode = UnityEngine.FilterMode.Bilinear)
    {
      Stream manifestResourceStream = Assembly.GetExecutingAssembly().GetManifestResourceStream("HutongGames.PlayMakerEditor.Playmaker.source.unity.Assets.PlayMaker.Editor.Resources." + resourceName + ".png");
      if (manifestResourceStream == null)
        return (Texture2D) null;
      Texture2D tex = new Texture2D(width, height, TextureFormat.ARGB32, false);
      tex.LoadImage(Files.ReadToEnd(manifestResourceStream));
      manifestResourceStream.Close();
      tex.hideFlags = HideFlags.HideAndDontSave;
      tex.filterMode = filterMode;
      return tex;
    }

    private static byte[] ReadToEnd(Stream stream)
    {
      long position = stream.Position;
      stream.Position = 0L;
      try
      {
        byte[] buffer = new byte[4096];
        int length = 0;
        int num1;
        while ((num1 = stream.Read(buffer, length, buffer.Length - length)) > 0)
        {
          length += num1;
          if (length == buffer.Length)
          {
            int num2 = stream.ReadByte();
            if (num2 != -1)
            {
              byte[] numArray = new byte[buffer.Length * 2];
              Buffer.BlockCopy((Array) buffer, 0, (Array) numArray, 0, buffer.Length);
              Buffer.SetByte((Array) numArray, length, (byte) num2);
              buffer = numArray;
              ++length;
            }
          }
        }
        byte[] numArray1 = buffer;
        if (buffer.Length != length)
        {
          numArray1 = new byte[length];
          Buffer.BlockCopy((Array) buffer, 0, (Array) numArray1, 0, length);
        }
        return numArray1;
      }
      finally
      {
        stream.Position = position;
      }
    }

    public static void LoadAllTemplates()
    {
      foreach (string asset in AssetDatabase.FindAssets("t:FsmTemplate"))
        AssetDatabase.LoadAssetAtPath<FsmTemplate>(AssetDatabase.GUIDToAssetPath(asset));
    }

    public static void LoadAllAssetsOfType(string type)
    {
      if (string.IsNullOrEmpty(type))
        return;
      foreach (string asset in AssetDatabase.FindAssets("t:" + type))
        AssetDatabase.LoadAssetAtPath(AssetDatabase.GUIDToAssetPath(asset), ReflectionUtils.GetGlobalType(type));
    }

    public static string[] GetFiles(string path, string searchPattern, SearchOption searchOption = SearchOption.TopDirectoryOnly)
    {
      string[] strArray = searchPattern.Split('|');
      List<string> stringList = new List<string>();
      foreach (string searchPattern1 in strArray)
        stringList.AddRange((IEnumerable<string>) Directory.GetFiles(path, searchPattern1, searchOption));
      stringList.Sort();
      return stringList.ToArray();
    }

    public static bool CreateFilePath(string fullFileName)
    {
      string directoryName = Path.GetDirectoryName(fullFileName);
      if (string.IsNullOrEmpty(directoryName))
      {
        Debug.LogError((object) string.Format(Strings.Error_Invalid_path__, (object) fullFileName));
        return false;
      }
      try
      {
        if (!Directory.Exists(directoryName))
          Directory.CreateDirectory(directoryName);
      }
      catch (Exception ex)
      {
        Debug.LogError((object) string.Format(Strings.Error_Could_not_create_directory__, (object) directoryName));
        return false;
      }
      return true;
    }

    public static string GetFsmSavePath(Fsm fsm)
    {
      if (fsm == null)
        return "";
      if ((bool) (UnityEngine.Object) fsm.UsedInTemplate)
        return "Template." + fsm.UsedInTemplate.name;
      string str1 = fsm.OwnerName + "." + Labels.GetFsmLabel(fsm);
      if (FsmPrefabs.IsPrefab(fsm))
        return "Prefab." + str1;
      string str2;
      if ((UnityEngine.Object) fsm.GameObject != (UnityEngine.Object) null)
      {
        Scene scene = fsm.GameObject.scene;
        if (scene.name != null)
        {
          scene = fsm.GameObject.scene;
          str2 = scene.name.Replace(".unity", "").Replace("Assets/", "").Replace("/", ".");
          goto label_10;
        }
      }
      str2 = "";
label_10:
      string str3 = str1;
      return str2 + "." + str3;
    }

    public static List<string> ScriptList
    {
      get
      {
        if (Files.scriptList == null)
          Files.BuildScriptList();
        return Files.scriptList;
      }
    }

    public static string[] ScriptPopupNames
    {
      get
      {
        if (Files.scriptList == null)
          Files.BuildScriptList();
        return Files.scriptPopupNames;
      }
    }

    public static void BuildScriptList()
    {
      Files.scriptList = new List<string>();
      foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
      {
        try
        {
          foreach (System.Type exportedType in assembly.GetExportedTypes())
          {
            if (Files.ShowScriptInMenu(exportedType))
              Files.scriptList.Add(exportedType.FullName);
          }
        }
        catch (Exception ex)
        {
          NotSupportedException supportedException = ex as NotSupportedException;
        }
      }
      Files.scriptList.Sort();
      List<string> stringList = new List<string>()
      {
        Strings.Label_None
      };
      foreach (string script in Files.ScriptList)
      {
        if (!script.Contains<char>('.'))
          stringList.Add("No Namespace/" + script);
        else
          stringList.Add(script.Replace('.', '/'));
      }
      Files.scriptPopupNames = stringList.ToArray();
    }

    private static bool ShowScriptInMenu(System.Type type) => type.IsSubclassOf(typeof (UnityEngine.Component)) && !type.IsSubclassOf(typeof (PlayMakerProxyBase)) && (type.IsClass && !type.IsAbstract) && (type != typeof (Behaviour) && type != typeof (MonoBehaviour) && type != typeof (PlayMakerProxyBase)) && type != typeof (PlayMakerOnGUI);

    public static string GetScriptName(int popupIndex) => popupIndex <= 0 || popupIndex >= Files.ScriptPopupNames.Length ? "" : Files.ScriptPopupNames[popupIndex].Replace("No Namespace/", "").Replace('/', '.');

    public static List<string> LoadAllPlaymakerPrefabs()
    {
      List<string> withFsmComponent = Files.GetPrefabsWithFsmComponent();
      float count = (float) withFsmComponent.Count;
      for (int index = 0; index < withFsmComponent.Count; ++index)
      {
        EditorUtility.DisplayProgressBar("PlayMaker", Strings.Dialog_Loading_FSM_Prefabs, (float) index / count);
        AssetDatabase.LoadAssetAtPath(withFsmComponent[index], typeof (GameObject));
      }
      EditorUtility.ClearProgressBar();
      return withFsmComponent;
    }

    public static List<string> GetPrefabsWithFsmComponent()
    {
      FileInfo[] files = new DirectoryInfo(Application.dataPath).GetFiles("*.prefab", SearchOption.AllDirectories);
      List<string> stringList = new List<string>();
      float length = (float) files.Length;
      for (int index = 0; index < files.Length; ++index)
      {
        FileInfo fileInfo = files[index];
        EditorUtility.DisplayProgressBar("PlayMaker", "Finding prefabs with PlayMakerFSM component...", (float) index / length);
        string str = fileInfo.FullName.Replace('\\', '/').Replace(Application.dataPath, "Assets");
        string[] pathNames = new string[1]{ str };
        foreach (string dependency in AssetDatabase.GetDependencies(pathNames))
        {
          if (dependency.Contains("/PlayMaker.dll"))
          {
            stringList.Add(str);
            break;
          }
        }
      }
      EditorUtility.ClearProgressBar();
      return stringList;
    }
  }
}
