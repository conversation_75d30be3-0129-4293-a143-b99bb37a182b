// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.DocHelpers
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public static class DocHelpers
  {
    private const string MinicapPath = "C:\\Program Files\\MiniCap\\MiniCap.exe";
    private static StreamWriter sw;

    private static void MiniCap(string arguments, bool waitForExit)
    {
      ProcessStartInfo startInfo = new ProcessStartInfo()
      {
        Arguments = arguments,
        FileName = "C:\\Program Files\\MiniCap\\MiniCap.exe",
        WindowStyle = ProcessWindowStyle.Hidden,
        CreateNoWindow = true
      };
      if (waitForExit)
      {
        using (Process process = Process.Start(startInfo))
          process.WaitForExit();
      }
      else
        Process.Start(startInfo);
    }

    private static void CaptureRegion(Rect region, string savePath, string filename)
    {
      string str = " -save \"" + savePath + "\"";
      DocHelpers.MiniCap(" -captureregion " + (object) region.xMin + " " + (object) region.yMin + " " + (object) region.xMax + " " + (object) region.yMax + " " + str + "\"" + filename + ".png\" -exit", true);
    }

    public static void StartStateActionListCapture()
    {
      DocHelpers.sw = File.CreateText("C:\\ActionScreens\\SampleScreens\\" + (FsmEditor.SelectedFsmGameObject.name + "_" + FsmEditor.SelectedFsm.Name + "_" + FsmEditor.SelectedState.Name + ".txt"));
      DocHelpers.sw.WriteLine("<div id=\"actionBreakdown\">");
      DocHelpers.sw.WriteLine("<h3>Overview</h3>");
      DocHelpers.sw.WriteLine("<p>TODO</p>");
      DocHelpers.sw.WriteLine("<h3>Actions</h3>");
      DocHelpers.sw.WriteLine("<table>");
    }

    public static void CaptureStateInspectorAction(Rect region, string actionName, int actionIndex)
    {
      if (DocHelpers.sw == null)
      {
        UnityEngine.Debug.LogError((object) "Must call StartStateActionListCapture first!");
      }
      else
      {
        UnityEngine.Debug.Log((object) ("CaptureStateInspectorAction: " + actionName));
        string str1 = Labels.StripNamespace(actionName);
        actionName = FsmEditor.SelectedFsmGameObject.name + "_" + FsmEditor.SelectedFsm.Name + "_" + FsmEditor.SelectedState.Name + "_" + (object) actionIndex + "_" + str1;
        ref Rect local1 = ref region;
        double x1 = (double) local1.x;
        Rect rect1 = FsmEditor.Window.position;
        double x2 = (double) rect1.x;
        rect1 = FsmEditor.Inspector.View;
        double x3 = (double) rect1.x;
        double num1 = x2 + x3;
        local1.x = (float) (x1 + num1);
        ref Rect local2 = ref region;
        double y1 = (double) local2.y;
        Rect rect2 = FsmEditor.Window.position;
        double y2 = (double) rect2.y;
        rect2 = FsmEditor.Inspector.View;
        double y3 = (double) rect2.y;
        double num2 = y2 + y3 + 43.0;
        local2.y = (float) (y1 + num2);
        DocHelpers.CaptureRegion(region, "C:\\ActionScreens\\SampleScreens\\", actionName);
        DocHelpers.sw.WriteLine("<tr>");
        string str2 = "https://hutonggames.fogbugz.com/default.asp?";
        int wikiPageNumber = DocHelpers.GetWikiPageNumber(str1);
        string str3 = wikiPageNumber <= 0 ? str2 + "ixWiki=1&pg=pgSearchWiki&qWiki=" + str1 : str2 + "W" + (object) wikiPageNumber;
        DocHelpers.sw.WriteLine("<td width=\"301px\"><a href = \"" + str3 + "\">");
        DocHelpers.sw.WriteLine("<div id=\"actionSample\"><img src=\"http://hutonggames.com/docs/img/" + actionName + ".png\" title=\"\" /></div>");
        DocHelpers.sw.WriteLine("</a></td>");
        DocHelpers.sw.WriteLine("<td><p><strong>" + Labels.NicifyVariableName(str1) + "</strong></p>");
        DocHelpers.sw.WriteLine("<p>TODO</p></td>");
        DocHelpers.sw.WriteLine("</tr>");
      }
    }

    public static void EndStateActionListCapture()
    {
      if (DocHelpers.sw == null)
        return;
      DocHelpers.sw.WriteLine("</table>");
      DocHelpers.sw.WriteLine("</div>");
      DocHelpers.sw.Close();
    }

    public static int GetWikiPageNumber(string topic) => Enum.IsDefined(typeof (WikiPages), (object) topic) ? (int) Enum.Parse(typeof (WikiPages), topic, true) : 0;

    public static string GetWikiPageUrl(WikiPages page) => "https://hutonggames.fogbugz.com/default.asp?W" + (object) (int) page;

    public static string GetWikiPageUrl(string topic)
    {
      int wikiPageNumber = DocHelpers.GetWikiPageNumber(topic);
      return wikiPageNumber > 0 ? "https://hutonggames.fogbugz.com/default.asp?W" + (object) wikiPageNumber : string.Empty;
    }
  }
}
