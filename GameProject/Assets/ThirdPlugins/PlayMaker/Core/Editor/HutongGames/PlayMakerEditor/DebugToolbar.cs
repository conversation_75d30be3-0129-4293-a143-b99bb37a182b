// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.DebugToolbar
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  internal static class DebugToolbar
  {
    public static void OnGUI(Rect area)
    {
      GUILayout.BeginArea(area);
      GUILayout.BeginHorizontal(FsmEditorStyles.ToolbarBottom);
      DebugToolbar.DoDebugControls(area);
      // GUILayout.Space(10f);
      // DebugToolbar.DoPlaybackControls(area);
      // GUILayout.Space(10f);
      // DebugToolbar.DoDebugFlowControls();
      GUILayout.EndHorizontal();
      GUILayout.EndArea();
    }

    [Localizable(false)]
    private static void DoDebugControls(Rect area)
    {
      int num = FsmErrorChecker.CountAllErrors();
      string str = Strings.DebugToolbar_No_errors;
      if (num > 0)
        str = string.Format("{0} {1}", (object) num, num > 1 ? (object) Strings.DebugToolbar_Label_Errors : (object) Strings.DebugToolbar_Label_Error);
      FsmEditorContent.DebugToolbarErrorCount.text = str;
      if (GUILayout.Button(FsmEditorContent.DebugToolbarErrorCount, FsmEditorStyles.ErrorCount))
      {
        FsmEditor.OpenErrorWindow();
        GUIUtility.ExitGUI();
      }
      if (Event.current.type == UnityEngine.EventType.Repaint)
      {
        Rect lastRect = GUILayoutUtility.GetLastRect();
        HighlighterHelper.FromRectInArea(area, lastRect, "Error Count");
        lastRect.x += 4f;
        lastRect.y += FsmEditorStyles.UsingFlatStyle ? 3f : 2f;
        lastRect.width = lastRect.height = 14f;
        GUIHelpers.DrawTexture(lastRect, num > 0 ? (Texture) FsmEditorStyles.Errors : (Texture) FsmEditorStyles.NoErrors, Color.white);
      }
      // if (ActionReport.InfoCount > 0 && GUILayout.Button(FsmEditorContent.EditorLogButton, EditorStyles.toolbarButton))
      //   FsmEditor.OpenReportWindow();
      // GUILayout.Space(10f);
      // if (GUILayout.Button(FsmEditorContent.DebugToolbarDebug, EditorStyles.toolbarDropDown))
      //   DebugToolbar.DoDebugMenu();
      // HighlighterHelper.FromGUILayoutInArea(area, "Debug Menu");
    }

    private static void DoPlaybackControls(Rect area)
    {
      GUIHelpers.BeginGuiContentColor(!FsmEditorStyles.UsingProSkin() ? Color.black : EditorStyles.label.normal.textColor);
      GUILayout.BeginHorizontal();
      EditorGUI.BeginChangeCheck();
      bool flag1 = GUILayout.Toggle((EditorApplication.isPlayingOrWillChangePlaymode ? 1 : 0) != 0, FsmEditorContent.Play, EditorStyles.toolbarButton, GUILayout.MaxWidth(40f));
      if (EditorGUI.EndChangeCheck())
        EditorApplication.isPlaying = flag1;
      EditorGUI.BeginChangeCheck();
      bool flag2 = GUILayout.Toggle((EditorApplication.isPaused ? 1 : 0) != 0, FsmEditorContent.Pause, EditorStyles.toolbarButton, GUILayout.MaxWidth(40f));
      if (EditorGUI.EndChangeCheck())
        EditorApplication.isPaused = flag2;
      if (GUILayout.Button(FsmEditorContent.Step, EditorStyles.toolbarButton, GUILayout.MaxWidth(40f)))
      {
        FsmDebugger.Instance.Step();
        GUIUtility.ExitGUI();
      }
      GUILayout.EndHorizontal();
      HighlighterHelper.FromGUILayoutInArea(area, "Play Controls");
      GUIHelpers.EndGuiContentColor();
    }

    private static void DoDebugFlowControls()
    {
      if (GameStateTracker.CurrentState == GameState.Stopped)
        GUILayout.FlexibleSpace();
      else if (DebugFlow.Active)
      {
        GUIHelpers.BeginGuiContentColor(Color.yellow);
        GUILayout.Label(Labels.FormatTime(DebugFlow.CurrentDebugTime), EditorStyles.toolbarButton);
        GUIHelpers.EndGuiContentColor();
        GUILayout.Space(10f);
        if (GUILayout.Button(FsmEditorContent.DebugToolbarPrev, EditorStyles.toolbarButton))
          DebugFlow.SelectPrevTransition();
        if (GUILayout.Button(FsmEditorContent.DebugToolbarNext, EditorStyles.toolbarButton))
          DebugFlow.SelectNextTransition();
        GUILayout.FlexibleSpace();
        if (!GUILayout.Button(Strings.DebugToolbar_Button_Open_Log, EditorStyles.toolbarButton))
          return;
        FsmEditor.OpenFsmLogWindow();
        GUIUtility.ExitGUI();
      }
      else
      {
        GUILayout.FlexibleSpace();
        if (!GUILayout.Button(Strings.DebugToolbar_Button_Open_Log, EditorStyles.toolbarButton))
          return;
        FsmEditor.OpenFsmLogWindow();
        GUIUtility.ExitGUI();
      }
    }

    private static void DoDebugMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent(Strings.Menu_Enable_Breakpoints), FsmEditorSettings.BreakpointsEnabled, new GenericMenu.MenuFunction(DebugToolbar.ToggleEnableBreakpoints));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Clear_Breakpoints), false, new GenericMenu.MenuFunction(EditorCommands.ClearBreakpoints));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Show_State_Labels_in_Game_View), FsmEditorSettings.ShowStateLabelsInGameView, new GenericMenu.MenuFunction(DebugToolbar.ToggleShowStateLabels));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Show_State_Loop_Counts), FsmEditorSettings.ShowStateLoopCounts, new GenericMenu.MenuFunction(DebugToolbar.ToggleShowStateLoopCounts));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Step_Single_Frame), FsmDebugger.Instance.StepMode == FsmDebugger.FsmStepMode.StepFrame, new GenericMenu.MenuFunction(DebugToolbar.SetDebuggerStepFrame));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Step_To_Next_State_Change_in_this_FSM), FsmDebugger.Instance.StepMode == FsmDebugger.FsmStepMode.StepToStateChange, new GenericMenu.MenuFunction(DebugToolbar.SetDebuggerStepToStateChange));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Step_To_Next_State_Change_in_any_FSM), FsmDebugger.Instance.StepMode == FsmDebugger.FsmStepMode.StepToAnyStateChange, new GenericMenu.MenuFunction(DebugToolbar.SetDebuggerStepToAnyStateChange));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Open_Log_Window), false, new GenericMenu.MenuFunction(FsmEditor.OpenFsmLogWindow));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Enable_Logging), FsmLog.LoggingEnabled, new GenericMenu.MenuFunction(EditorCommands.ToggleLogging));
      genericMenu.ShowAsContext();
    }

    private static void ToggleShowStateLoopCounts()
    {
      FsmEditorSettings.ShowStateLoopCounts = !FsmEditorSettings.ShowStateLoopCounts;
      FsmEditorSettings.SaveSettings();
    }

    private static void ToggleEnableBreakpoints()
    {
      FsmEditorSettings.BreakpointsEnabled = !FsmEditorSettings.BreakpointsEnabled;
      Fsm.BreakpointsEnabled = FsmEditorSettings.BreakpointsEnabled;
      FsmEditorSettings.SaveSettings();
    }

    private static void ToggleShowStateLabels()
    {
      FsmEditorSettings.ShowStateLabelsInGameView = !FsmEditorSettings.ShowStateLabelsInGameView;
      FsmEditorSettings.ApplySettings();
      FsmEditorSettings.SaveSettings();
    }

    private static void SaveStepMode()
    {
      FsmEditorSettings.DebuggerStepMode = FsmDebugger.Instance.StepMode;
      FsmEditorSettings.SaveSettings();
    }

    private static void SetDebuggerStepFrame()
    {
      FsmDebugger.Instance.StepMode = FsmDebugger.FsmStepMode.StepFrame;
      DebugToolbar.SaveStepMode();
    }

    private static void SetDebuggerStepToStateChange()
    {
      FsmDebugger.Instance.StepMode = FsmDebugger.FsmStepMode.StepToStateChange;
      DebugToolbar.SaveStepMode();
    }

    private static void SetDebuggerStepToAnyStateChange()
    {
      FsmDebugger.Instance.StepMode = FsmDebugger.FsmStepMode.StepToAnyStateChange;
      DebugToolbar.SaveStepMode();
    }
  }
}
