// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.NewEventWindow
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class NewEventWindow : EditorWindow
  {
    private string titleLabel;
    private TextField editField;
    private string validationError;
    private Rect frame;

    public TextField.EditCommitedCallback EditCommited
    {
      get => this.editField.EditCommited;
      set => this.editField.EditCommited = value;
    }

    public static NewEventWindow CreateDropdown(
      string title,
      Rect buttonRect,
      string eventName)
    {
      NewEventWindow instance = ScriptableObject.CreateInstance<NewEventWindow>();
      instance.Init(title, eventName);
      buttonRect.y += EditorGUIUtility.singleLineHeight + 6f;
      instance.ShowAsDropDown(buttonRect, new Vector2(buttonRect.width, 70f));
      return instance;
    }

    private void Init(string windowTitle, string eventName)
    {
      this.titleLabel = windowTitle;
      this.editField = new TextField((EditorWindow) this, GUIContent.none);
      this.editField.Focus();
      this.editField.EditCommited = new TextField.EditCommitedCallback(this.CommitNewEvent);
      this.editField.EditCanceled = new TextField.EditingCancelledCallback(this.Cancel);
      this.editField.ValidateText = new TextField.ValidateCallback(this.ValidateEvent);
      this.editField.Text = eventName;
      this.editField.Validate();
    }

    public void OnGUI()
    {
      FsmEditorStyles.Init();
      GUILayout.Label(this.titleLabel, EditorStyles.boldLabel);
      this.editField.OnGUI();
      if (!string.IsNullOrEmpty(this.validationError))
        GUILayout.Box(this.validationError, FsmEditorStyles.ErrorBox);
      else if (GUILayout.Button(Strings.Label_Create_Event))
        this.editField.CommitEdit();
      if (Event.current.type != UnityEngine.EventType.Repaint)
        return;
      this.frame = new Rect(this.position)
      {
        x = 0.0f,
        y = 0.0f
      };
      GUIHelpers.BeginGuiColor(FsmEditorStyles.LabelTextColor, 0.5f);
      GUI.Box(this.frame, GUIContent.none, FsmEditorStyles.SinglePixelFrame);
      GUIHelpers.EndGuiColor();
    }

    private void CommitNewEvent(TextField textField) => this.Close();

    private void Cancel(TextField textField) => this.Close();

    private bool ValidateEvent(string eventName)
    {
      this.validationError = "";
      if (!string.IsNullOrEmpty(eventName))
        return true;
      this.validationError = Strings.Error_Invalid_Event_Name;
      return false;
    }
  }
}
