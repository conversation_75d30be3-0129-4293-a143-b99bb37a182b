// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Actions
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using HutongGames.PlayMaker.Actions;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class Actions
  {
    private const int INIT_ACTIONS_CAPACITY = 1200;
    private const int INIT_CATEGORIES_CAPACITY = 100;
    private static readonly System.Collections.Generic.List<System.Type> actionsList = new System.Collections.Generic.List<System.Type>(1200);
    private static readonly System.Collections.Generic.List<System.Type> visibleActions = new System.Collections.Generic.List<System.Type>(1200);
    private static readonly System.Collections.Generic.List<string> actionCategoryLookup = new System.Collections.Generic.List<string>(1200);
    private static readonly System.Collections.Generic.List<string> actionCategoryList = new System.Collections.Generic.List<string>(100);
    private static readonly Dictionary<System.Type, string> tooltipLookup = new Dictionary<System.Type, string>();
    private static readonly Dictionary<System.Type, System.Collections.Generic.List<string>> actionCategories = new Dictionary<System.Type, System.Collections.Generic.List<string>>(1200);
    private static readonly Dictionary<string, System.Collections.Generic.List<System.Type>> actionsInCategory = new Dictionary<string, System.Collections.Generic.List<System.Type>>(100);
    private static readonly Dictionary<string, int> numActionsUsedInCategory = new Dictionary<string, int>(100);
    private static Dictionary<string, System.Collections.Generic.List<System.Type>> actionsInCategoryFiltered;
    private static readonly System.Collections.Generic.List<int> filteredCategoryIDs = new System.Collections.Generic.List<int>();
    private static readonly System.Collections.Generic.List<string> filteredCategories = new System.Collections.Generic.List<string>();
    private static readonly Dictionary<string, Texture> categoryIcons = new Dictionary<string, Texture>(100);
    private static readonly Dictionary<System.Type, bool> actionHasSceneGUI = new Dictionary<System.Type, bool>(1200);
    public static Dictionary<string, string> categoryDescriptions = new Dictionary<string, string>();
    private static bool initialized;
    private static bool usingProSkin;
    private static System.Type comment;
    private static System.Type runFSM;

    public static System.Collections.Generic.List<System.Type> List
    {
      get
      {
        HutongGames.PlayMakerEditor.Actions.BuildListIfNeeded();
        return HutongGames.PlayMakerEditor.Actions.actionsList;
      }
    }

    public static System.Collections.Generic.List<string> CategoryLookup
    {
      get
      {
        HutongGames.PlayMakerEditor.Actions.BuildListIfNeeded();
        return HutongGames.PlayMakerEditor.Actions.actionCategoryLookup;
      }
    }

    public static System.Collections.Generic.List<string> Categories
    {
      get
      {
        HutongGames.PlayMakerEditor.Actions.BuildListIfNeeded();
        return HutongGames.PlayMakerEditor.Actions.actionCategoryList;
      }
    }

    public static System.Collections.Generic.List<int> FilteredCategoryIDs
    {
      get
      {
        HutongGames.PlayMakerEditor.Actions.BuildListIfNeeded();
        return HutongGames.PlayMakerEditor.Actions.filteredCategoryIDs;
      }
    }

    public static System.Collections.Generic.List<string> FilteredCategories
    {
      get
      {
        HutongGames.PlayMakerEditor.Actions.BuildListIfNeeded();
        return HutongGames.PlayMakerEditor.Actions.filteredCategories;
      }
    }

    public static System.Collections.Generic.List<FsmInfo> GetUsage(System.Type actionType) => FsmSearch.GetUsage(actionType);

    public static int GetUsageCount(System.Type actionType) => FsmSearch.GetUsageCount(actionType);

    public static int GetNumActionUsedInCategory(string category)
    {
      int num;
      HutongGames.PlayMakerEditor.Actions.numActionsUsedInCategory.TryGetValue(category, out num);
      return num;
    }

    public static void BuildListIfNeeded()
    {
      if (HutongGames.PlayMakerEditor.Actions.actionsList != null && HutongGames.PlayMakerEditor.Actions.actionsList.Count != 0)
        return;
      HutongGames.PlayMakerEditor.Actions.BuildList();
    }

    public static void Init()
    {
    }

    public static void BuildList()
    {
      HutongGames.PlayMakerEditor.Actions.actionsList.Clear();
      foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
      {
        try
        {
          foreach (System.Type exportedType in assembly.GetExportedTypes())
          {
            if (exportedType.IsClass && !exportedType.IsAbstract && typeof (IFsmStateAction).IsAssignableFrom(exportedType))
              HutongGames.PlayMakerEditor.Actions.actionsList.Add(exportedType);
          }
        }
        catch (Exception ex)
        {
          NotSupportedException supportedException = ex as NotSupportedException;
        }
      }
      HutongGames.PlayMakerEditor.Actions.BuildCategoryList();
      HutongGames.PlayMakerEditor.Actions.InitCategories();
      HutongGames.PlayMakerEditor.Actions.FilterActions();
    }

    private static bool ShouldShow(System.Type actionType) => FsmSearch.GetUsageCount(actionType) > 0 || !FsmEditorSettings.ShowUsedActionsOnly && (!FsmEditorSettings.HideObsoleteActions || !CustomAttributeHelpers.IsObsolete(actionType));

    public static bool IsVisible(System.Type actionType) => HutongGames.PlayMakerEditor.Actions.visibleActions.Contains(actionType);

    private static void BuildCategoryList()
    {
      HutongGames.PlayMakerEditor.Actions.actionCategoryLookup.Clear();
      HutongGames.PlayMakerEditor.Actions.actionCategories.Clear();
      foreach (System.Type actions in HutongGames.PlayMakerEditor.Actions.actionsList)
      {
        string actionCategory = HutongGames.PlayMakerEditor.Actions.GetActionCategory(actions);
        HutongGames.PlayMakerEditor.Actions.actionCategoryLookup.Add(actionCategory);
        HutongGames.PlayMakerEditor.Actions.actionCategories.Add(actions, new System.Collections.Generic.List<string>()
        {
          actionCategory
        });
      }
    }

    public static void InitCategories()
    {
      HutongGames.PlayMakerEditor.Actions.LoadCategory("Recent");
      HutongGames.PlayMakerEditor.Actions.LoadCategory("Favorites");
      for (int index = 0; index < HutongGames.PlayMakerEditor.Actions.actionsList.Count; ++index)
      {
        System.Type actions = HutongGames.PlayMakerEditor.Actions.actionsList[index];
        string key = HutongGames.PlayMakerEditor.Actions.CategoryLookup[index];
        System.Collections.Generic.List<System.Type> typeList;
        if (!HutongGames.PlayMakerEditor.Actions.actionsInCategory.TryGetValue(key, out typeList))
          HutongGames.PlayMakerEditor.Actions.actionsInCategory.Add(key, new System.Collections.Generic.List<System.Type>()
          {
            actions
          });
        else if (!typeList.Contains(actions))
          typeList.Add(actions);
      }
      HutongGames.PlayMakerEditor.Actions.actionCategoryList.Clear();
      HutongGames.PlayMakerEditor.Actions.actionCategoryList.AddRange((IEnumerable<string>) HutongGames.PlayMakerEditor.Actions.actionsInCategory.Keys);
      HutongGames.PlayMakerEditor.Actions.actionCategoryList.Sort();
      HutongGames.PlayMakerEditor.Actions.actionCategoryList.Remove("Favorites");
      HutongGames.PlayMakerEditor.Actions.actionCategoryList.Insert(0, "Favorites");
      HutongGames.PlayMakerEditor.Actions.actionCategoryList.Remove("Recent");
      HutongGames.PlayMakerEditor.Actions.actionCategoryList.Insert(0, "Recent");
      HutongGames.PlayMakerEditor.Actions.actionCategoryList.Remove(ActionCategory.PlayMakerInternal.ToString());
      HutongGames.PlayMakerEditor.Actions.categoryDescriptions = new Dictionary<string, string>()
      {
        ["AnimateVariables"] = "Actions to animate variables over time. These actions include some more complex techniques (e.g., animating color channels separately). For simpler animations use Ease Actions or Tween Actions.",
        ["Animation"] = "Animation component actions. Note: Animation components are considered a legacy system, replaced with the newer Animator/Mecanim system. But they can still be useful for quick one-off animations.",
        ["Animator"] = "Animator component actions. Part of the Mecanim system that uses state machines to control animations and transitions."
      };
    }

    public static string GetCategoryDescription(string category)
    {
      string str;
      if (!HutongGames.PlayMakerEditor.Actions.categoryDescriptions.TryGetValue(category, out str))
        UnityEngine.Debug.LogWarning((object) ("Missing category description: " + category));
      return str;
    }

    [Localizable(false)]
    private static void InitDefaultCategoryIcons()
    {
      FsmEditorStyles.Init();
      HutongGames.PlayMakerEditor.Actions.usingProSkin = EditorGUIUtility.isProSkin;
      HutongGames.PlayMakerEditor.Actions.initialized = true;
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("AnimateVariables", (Texture) FsmEditorStyles.AnimateVariableIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Animation", (Texture) FsmEditorStyles.AnimationIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Animator", (Texture) FsmEditorStyles.AnimatorIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Application", (Texture) FsmEditorStyles.ApplicationIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Array", (Texture) FsmEditorStyles.ArrayIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Audio", (Texture) FsmEditorStyles.AudioIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Camera", (Texture) FsmEditorStyles.CameraIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Character", (Texture) FsmEditorStyles.ControllerIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Convert", (Texture) FsmEditorStyles.ConvertIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Color", (Texture) FsmEditorStyles.ColorIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Debug", (Texture) FsmEditorStyles.DebugIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Device", (Texture) FsmEditorStyles.DeviceIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Effects", (Texture) FsmEditorStyles.EffectsIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Enum", (Texture) FsmEditorStyles.EnumIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Favorites", (Texture) FsmEditorStyles.FavoritesIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("GameObject", (Texture) FsmEditorStyles.GameObjectIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("GUI", (Texture) FsmEditorStyles.GuiIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("GUIElement", (Texture) FsmEditorStyles.GuiElementIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("GUILayout", (Texture) FsmEditorStyles.GuiLayoutIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Input", (Texture) FsmEditorStyles.InputIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Level", (Texture) FsmEditorStyles.SceneIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Lights", (Texture) FsmEditorStyles.LightIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Logic", (Texture) FsmEditorStyles.LogicIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Material", (Texture) FsmEditorStyles.MaterialIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Substance", (Texture) FsmEditorStyles.SubstanceIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Mesh", (Texture) FsmEditorStyles.MeshIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Math", (Texture) FsmEditorStyles.MathIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Movie", (Texture) FsmEditorStyles.MovieCamIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Profiler", (Texture) FsmEditorStyles.StopwatchIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Physics", (Texture) FsmEditorStyles.SphereColliderIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Physics2D", (Texture) FsmEditorStyles.CircleColliderIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Quaternion", (Texture) FsmEditorStyles.QuaternionIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Rect", (Texture) FsmEditorStyles.RectIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("PlayerPrefs", (Texture) FsmEditorStyles.SaveIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("RectTransform", (Texture) FsmEditorStyles.RectTransformIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Recent", (Texture) FsmEditorStyles.RecentIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("RenderSettings", (Texture) FsmEditorStyles.RenderIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Scene", (Texture) FsmEditorStyles.SceneIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("ScriptControl", "cs Script Icon");
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("StateMachine", (Texture) FsmEditorStyles.StateMachineIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("String", (Texture) FsmEditorStyles.StringIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Tests", (Texture) FsmEditorStyles.TestsIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Time", (Texture) FsmEditorStyles.TimeIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Transform", (Texture) FsmEditorStyles.TransformIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Vector2", (Texture) FsmEditorStyles.Vector2Icon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Vector3", (Texture) FsmEditorStyles.Vector3Icon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("UnityObject", (Texture) FsmEditorStyles.UnityObjectIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Video", (Texture) FsmEditorStyles.MovieCamIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Tween", (Texture) FsmEditorStyles.TweenIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("iTween", (Texture) FsmEditorStyles.TweenIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("WWW", (Texture) FsmEditorStyles.WwwIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Trigonometry", (Texture) FsmEditorStyles.TrigIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Pool", (Texture) FsmEditorStyles.PoolIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("SpriteRenderer", (Texture) FsmEditorStyles.SpriteIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("UI", (Texture) FsmEditorStyles.CanvasIcon);
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("PlayerInput", (Texture) EditorHacks.GetScriptTypeIcon("PlayerInput"));
      HutongGames.PlayMakerEditor.Actions.AddCategoryIcon("Gamepad", (Texture) FsmEditorStyles.GamepadIcon);
    }

    public static void AddCategoryIcon(string categoryName, System.Type objType)
    {
      HutongGames.PlayMakerEditor.Actions.categoryIcons.Remove(categoryName);
      GUIContent guiContent = EditorGUIUtility.ObjectContent((UnityEngine.Object) null, objType);
      if (guiContent != null)
        HutongGames.PlayMakerEditor.Actions.categoryIcons.Add(categoryName, guiContent.image);
      else
        UnityEngine.Debug.LogError((object) ("Could not find icon for: " + objType.FullName));
    }

    public static void AddCategoryIcon(string categoryName, string builtinIcon)
    {
      GUIContent guiContent = EditorGUIUtility.IconContent(builtinIcon);
      if (guiContent != null)
      {
        HutongGames.PlayMakerEditor.Actions.categoryIcons.Remove(categoryName);
        HutongGames.PlayMakerEditor.Actions.categoryIcons.Add(categoryName, guiContent.image);
      }
      else
        UnityEngine.Debug.LogError((object) ("Could not find built-in icon: " + builtinIcon));
    }

    public static void AddCategoryIcon(string categoryName, Texture iconTexture)
    {
      HutongGames.PlayMakerEditor.Actions.categoryIcons.Remove(categoryName);
      HutongGames.PlayMakerEditor.Actions.categoryIcons.Add(categoryName, iconTexture);
    }

    public static Texture GetCategoryIcon(string categoryName)
    {
      if (!HutongGames.PlayMakerEditor.Actions.initialized || EditorGUIUtility.isProSkin != HutongGames.PlayMakerEditor.Actions.usingProSkin)
        HutongGames.PlayMakerEditor.Actions.InitDefaultCategoryIcons();
      Texture texture;
      return !HutongGames.PlayMakerEditor.Actions.categoryIcons.TryGetValue(categoryName, out texture) ? (Texture) null : texture;
    }

    public static Texture GetCategoryIcon(System.Type actionType) => HutongGames.PlayMakerEditor.Actions.GetCategoryIcon(HutongGames.PlayMakerEditor.Actions.GetCategory(actionType));

    public static System.Collections.Generic.List<System.Type> GetActionsInCategory(
      string categoryName)
    {
      System.Collections.Generic.List<System.Type> typeList;
      return !HutongGames.PlayMakerEditor.Actions.actionsInCategory.TryGetValue(categoryName, out typeList) ? new System.Collections.Generic.List<System.Type>() : typeList;
    }

    public static System.Collections.Generic.List<System.Type> GetActionsInCategoryFiltered(
      string categoryName)
    {
      System.Collections.Generic.List<System.Type> typeList;
      return !HutongGames.PlayMakerEditor.Actions.actionsInCategoryFiltered.TryGetValue(categoryName, out typeList) ? (System.Collections.Generic.List<System.Type>) null : typeList;
    }

    public static void InsertActionAtTopOfCategory(string categoryName, System.Type actionType) => HutongGames.PlayMakerEditor.Actions.AddActionToCategory(categoryName, actionType, 0);

    public static void AddActionToCategory(string categoryName, System.Type actionType, int atIndex = -1)
    {
      if (actionType == null)
        return;
      HutongGames.PlayMakerEditor.Actions.BuildListIfNeeded();
      System.Collections.Generic.List<System.Type> typeList;
      if (!HutongGames.PlayMakerEditor.Actions.actionsInCategory.TryGetValue(categoryName, out typeList))
        HutongGames.PlayMakerEditor.Actions.actionsInCategory.Add(categoryName, new System.Collections.Generic.List<System.Type>()
        {
          actionType
        });
      else if (atIndex == -1)
      {
        if (!typeList.Contains(actionType))
          typeList.Add(actionType);
      }
      else
      {
        typeList.Remove(actionType);
        typeList.Insert(atIndex, actionType);
      }
      HutongGames.PlayMakerEditor.Actions.actionCategories[actionType].Add(categoryName);
      HutongGames.PlayMakerEditor.Actions.SaveCategory(categoryName);
    }

    public static void RemoveActionFromCategory(string categoryName, System.Type actionType)
    {
      if (actionType == null)
        return;
      HutongGames.PlayMakerEditor.Actions.BuildListIfNeeded();
      System.Collections.Generic.List<System.Type> typeList;
      if (HutongGames.PlayMakerEditor.Actions.actionsInCategory.TryGetValue(categoryName, out typeList) && typeList != null)
        typeList.Remove(actionType);
      HutongGames.PlayMakerEditor.Actions.actionCategories[actionType].Remove(categoryName);
      HutongGames.PlayMakerEditor.Actions.SaveCategory(categoryName);
    }

    public static bool CategoryContainsAction(string categoryName, System.Type actionType)
    {
      System.Collections.Generic.List<System.Type> typeList;
      return !string.IsNullOrEmpty(categoryName) && actionType != null && (HutongGames.PlayMakerEditor.Actions.actionsInCategory.TryGetValue(categoryName, out typeList) && typeList != null) && typeList.Contains(actionType);
    }

    public static void SaveCategories()
    {
      HutongGames.PlayMakerEditor.Actions.SaveCategory("Favorites");
      HutongGames.PlayMakerEditor.Actions.SaveCategory("Recent");
    }

    private static void LoadCategory(string categoryName)
    {
      System.Collections.Generic.List<System.Type> typeList = new System.Collections.Generic.List<System.Type>();
      string str = EditorPrefs.GetString("PlayMaker.ActionCategory." + categoryName, "");
      char[] chArray = new char[1]{ '\n' };
      foreach (string actionName in str.Split(chArray))
      {
        System.Type byName = HutongGames.PlayMakerEditor.Actions.FindByName(actionName);
        if (byName != null && !typeList.Contains(byName))
        {
          typeList.Add(byName);
          HutongGames.PlayMakerEditor.Actions.actionCategories[byName].Add(categoryName);
        }
      }
      HutongGames.PlayMakerEditor.Actions.actionsInCategory.Remove(categoryName);
      HutongGames.PlayMakerEditor.Actions.actionsInCategory.Add(categoryName, typeList);
    }

    private static void SaveCategory(string categoryName)
    {
      System.Collections.Generic.List<System.Type> typeList;
      if (!HutongGames.PlayMakerEditor.Actions.actionsInCategory.TryGetValue(categoryName, out typeList))
      {
        UnityEngine.Debug.Log((object) (Strings.Error_Could_not_save_action_category + categoryName));
      }
      else
      {
        string str = "";
        foreach (System.Type type in typeList)
        {
          if (type != null)
            str = str + type.FullName + "\n";
        }
        EditorPrefs.SetString("PlayMaker.ActionCategory." + categoryName, str);
      }
    }

    public static bool ActionHasSceneGUI(System.Type actionType)
    {
      bool flag;
      if (!HutongGames.PlayMakerEditor.Actions.actionHasSceneGUI.TryGetValue(actionType, out flag))
      {
        System.Type customEditor = CustomActionEditors.GetCustomEditor(actionType);
        flag = customEditor != null && customEditor.ImplementsMethod("OnSceneGUI");
        HutongGames.PlayMakerEditor.Actions.actionHasSceneGUI.Add(actionType, flag);
      }
      return flag;
    }

    public static bool UpdatingActionUsage { get; private set; }

    public static float UpdatingActionUsageProgress { get; private set; }

    public static void UpdateUsage()
    {
      FsmSearch.UpdateAllIfNeeded();
      HutongGames.PlayMakerEditor.Actions.UpdateNumActionsUsedInCategory();
    }

    public static void UpdateNumActionsUsedInCategory()
    {
      HutongGames.PlayMakerEditor.Actions.numActionsUsedInCategory.Clear();
      foreach (string actionCategory in HutongGames.PlayMakerEditor.Actions.actionCategoryList)
      {
        int num = 0;
        foreach (System.Type actionType in HutongGames.PlayMakerEditor.Actions.GetActionsInCategory(actionCategory))
          num += FsmSearch.GetUsageCount(actionType);
        HutongGames.PlayMakerEditor.Actions.numActionsUsedInCategory.Add(actionCategory, num);
      }
    }

    public static System.Type FindByName(string actionName)
    {
      if (string.IsNullOrEmpty(actionName))
        return (System.Type) null;
      HutongGames.PlayMakerEditor.Actions.BuildListIfNeeded();
      if (!actionName.Contains("."))
        actionName = "HutongGames.PlayMaker.Actions." + actionName;
      foreach (System.Type actions in HutongGames.PlayMakerEditor.Actions.actionsList)
      {
        if (actions.FullName != null && actions.FullName == actionName)
          return actions;
      }
      return (System.Type) null;
    }

    public static string GetCategory(System.Type actionType)
    {
      HutongGames.PlayMakerEditor.Actions.BuildListIfNeeded();
      return HutongGames.PlayMakerEditor.Actions.actionCategories[actionType][0];
    }

    public static string FindFirstCategory(System.Type actionType)
    {
      foreach (string category in HutongGames.PlayMakerEditor.Actions.Categories)
      {
        if (HutongGames.PlayMakerEditor.Actions.GetActionsInCategory(category).Contains(actionType))
          return category;
      }
      return (string) null;
    }

    public static int GetCategoryIndex(string categoryName) => HutongGames.PlayMakerEditor.Actions.Categories.FindIndex((Predicate<string>) (x => x == categoryName));

    public static System.Collections.Generic.List<string> GetCategories(System.Type actionType) => HutongGames.PlayMakerEditor.Actions.actionCategories[actionType];

    public static int GetActionIndex(FsmState state, FsmStateAction action)
    {
      int num = 0;
      foreach (FsmStateAction action1 in state.Actions)
      {
        if (action1 == action)
          return num;
        ++num;
      }
      return -1;
    }

    public static int GetLowestActionIndex(FsmState state, IEnumerable<FsmStateAction> actions)
    {
      int num = int.MaxValue;
      System.Collections.Generic.List<FsmStateAction> fsmStateActionList = actions as System.Collections.Generic.List<FsmStateAction>;
      if (state == null || fsmStateActionList == null)
        return num;
      for (int index = 0; index < state.Actions.Length; ++index)
      {
        FsmStateAction action = state.Actions[index];
        if (fsmStateActionList.Contains(action) && index < num)
          num = index;
      }
      return num;
    }

    public static int GetHighestActionIndex(FsmState state, IEnumerable<FsmStateAction> actions)
    {
      int num = 0;
      System.Collections.Generic.List<FsmStateAction> fsmStateActionList = actions as System.Collections.Generic.List<FsmStateAction>;
      if (state == null || fsmStateActionList == null)
        return num;
      for (int index = 0; index < state.Actions.Length; ++index)
      {
        FsmStateAction action = state.Actions[index];
        if (fsmStateActionList.Contains(action) && index > num)
          num = index;
      }
      return num;
    }

    public static string GetTooltip(FsmStateAction action)
    {
      if (action == null)
        return "";
      string str;
      return HutongGames.PlayMakerEditor.Actions.tooltipLookup.TryGetValue(action.GetType(), out str) ? str : HutongGames.PlayMakerEditor.Actions.UpdateTooltip(action);
    }

    public static string UpdateTooltip(FsmStateAction action)
    {
      if (action == null)
        return "";
      System.Type type = action.GetType();
      HutongGames.PlayMakerEditor.Actions.tooltipLookup.Remove(type);
      string tooltip = HutongGames.PlayMakerEditor.Actions.GetTooltip(type);
      string str = string.Format("{0}\n\n{1}", (object) Labels.NicifyVariableName(Labels.StripNamespace(type.ToString())), (object) tooltip);
      HutongGames.PlayMakerEditor.Actions.tooltipLookup.Add(type, str);
      return str;
    }

    public static string GetTooltipCodedText(System.Type objType)
    {
      if (objType == null)
        return (string) null;
      return CustomAttributeHelpers.GetAttribute<HutongGames.PlayMaker.TooltipAttribute>(objType)?.CodedText;
    }

    public static string GetTooltipCodedText(FieldInfo field)
    {
      if (field == null)
        return (string) null;
      return CustomAttributeHelpers.GetAttribute<HutongGames.PlayMaker.TooltipAttribute>(field)?.CodedText;
    }

    public static string GetTooltip(object instance) => instance == null ? (string) null : HutongGames.PlayMakerEditor.Actions.GetTooltip(instance.GetType());

    public static string GetTooltip(System.Type objType) => objType == null ? (string) null : HutongGames.PlayMakerEditor.Actions.GetTooltip(CustomAttributeHelpers.GetCustomAttributes(objType));

    public static string GetTooltip(FieldInfo field) => field == null ? (string) null : HutongGames.PlayMakerEditor.Actions.GetTooltip(CustomAttributeHelpers.GetCustomAttributes(field));

    public static string GetTooltip(object[] attributes)
    {
      foreach (object attribute in attributes)
      {
        if (attribute is HutongGames.PlayMaker.TooltipAttribute tooltipAttribute)
          return tooltipAttribute.Text;
      }
      return string.Empty;
    }

    public static string GetActionCategory(System.Type objType) => objType == null ? (string) null : HutongGames.PlayMakerEditor.Actions.GetActionCategory(CustomAttributeHelpers.GetCustomAttributes(objType));

    public static string GetActionCategory(object[] attributes)
    {
      foreach (object attribute in attributes)
      {
        if (attribute is ActionCategoryAttribute categoryAttribute)
          return categoryAttribute.Category;
      }
      return Strings.Label_Misc;
    }

    public static void FilterActions() => HutongGames.PlayMakerEditor.Actions.FilterActions("", 0);

    public static void FilterActions(string searchString, int searchMode)
    {
      string[] strArray = searchString.ToUpper().Split(' ');
      HutongGames.PlayMakerEditor.Actions.visibleActions.Clear();
      HutongGames.PlayMakerEditor.Actions.actionsInCategoryFiltered = HutongGames.PlayMakerEditor.Actions.actionsInCategory.ToDictionary<KeyValuePair<string, System.Collections.Generic.List<System.Type>>, string, System.Collections.Generic.List<System.Type>>((Func<KeyValuePair<string, System.Collections.Generic.List<System.Type>>, string>) (x => x.Key), (Func<KeyValuePair<string, System.Collections.Generic.List<System.Type>>, System.Collections.Generic.List<System.Type>>) (x => new System.Collections.Generic.List<System.Type>((IEnumerable<System.Type>) x.Value)));
      foreach (System.Type actions in HutongGames.PlayMakerEditor.Actions.actionsList)
      {
        if (!(!string.IsNullOrEmpty(searchString) ? !HutongGames.PlayMakerEditor.Actions.ActionMatchesFilter(actions, (IEnumerable<string>) strArray, searchMode) : !HutongGames.PlayMakerEditor.Actions.ShouldShow(actions)))
        {
          HutongGames.PlayMakerEditor.Actions.visibleActions.Add(actions);
        }
        else
        {
          foreach (string category in HutongGames.PlayMakerEditor.Actions.GetCategories(actions))
            HutongGames.PlayMakerEditor.Actions.actionsInCategoryFiltered[category].Remove(actions);
        }
      }
      HutongGames.PlayMakerEditor.Actions.filteredCategoryIDs.Clear();
      HutongGames.PlayMakerEditor.Actions.filteredCategories.Clear();
      for (int index = 0; index < HutongGames.PlayMakerEditor.Actions.actionCategoryList.Count; ++index)
      {
        string actionCategory = HutongGames.PlayMakerEditor.Actions.actionCategoryList[index];
        if (HutongGames.PlayMakerEditor.Actions.actionsInCategoryFiltered[actionCategory].Count > 0)
        {
          HutongGames.PlayMakerEditor.Actions.filteredCategoryIDs.Add(index);
          HutongGames.PlayMakerEditor.Actions.filteredCategories.Add(actionCategory);
        }
      }
    }

    public static System.Collections.Generic.List<System.Type> GetUsedActions()
    {
      System.Collections.Generic.List<System.Type> typeList = new System.Collections.Generic.List<System.Type>();
      foreach (System.Type actions in HutongGames.PlayMakerEditor.Actions.actionsList)
      {
        if (FsmSearch.GetUsageCount(actions) > 0)
          typeList.Add(actions);
      }
      return typeList;
    }

    public static System.Collections.Generic.List<System.Type> GetFilteredActions()
    {
      System.Collections.Generic.List<System.Type> typeList = new System.Collections.Generic.List<System.Type>();
      foreach (int filteredCategoryId in HutongGames.PlayMakerEditor.Actions.filteredCategoryIDs)
      {
        foreach (System.Type type in HutongGames.PlayMakerEditor.Actions.GetActionsInCategoryFiltered(HutongGames.PlayMakerEditor.Actions.actionCategoryList[filteredCategoryId]))
          typeList.Add(type);
      }
      return typeList;
    }

    private static bool ActionMatchesFilter(
      System.Type actionType,
      IEnumerable<string> filter,
      int searchMode)
    {
      filter = (IEnumerable<string>) (filter as string[]);
      if (filter == null)
        return true;
      if (!HutongGames.PlayMakerEditor.Actions.ShouldShow(actionType))
        return false;
      bool flag = HutongGames.PlayMakerEditor.Actions.ActionNameMatchesFilter(actionType, filter);
      return flag || searchMode == 0 ? flag : HutongGames.PlayMakerEditor.Actions.ActionDescriptionMatchesFilter(actionType, filter);
    }

    private static bool ActionNameMatchesFilter(System.Type actionType, IEnumerable<string> filter)
    {
      string str1 = Labels.GetActionLabel(actionType).ToUpper().Replace(" ", "");
      foreach (string str2 in filter)
      {
        if (!str1.Contains(str2))
          return false;
      }
      return true;
    }

    private static bool ActionDescriptionMatchesFilter(System.Type actionType, IEnumerable<string> filter)
    {
      string upper = HutongGames.PlayMakerEditor.Actions.GetTooltip(actionType).ToUpper();
      foreach (string str in filter)
      {
        if (!upper.Contains(str))
          return false;
      }
      return true;
    }

    public static System.Type GetCommentAction()
    {
      if (HutongGames.PlayMakerEditor.Actions.comment == null)
        HutongGames.PlayMakerEditor.Actions.comment = ReflectionUtils.GetGlobalType("HutongGames.PlayMaker.Actions.Comment");
      return HutongGames.PlayMakerEditor.Actions.comment ?? (HutongGames.PlayMakerEditor.Actions.comment = typeof (MissingAction));
    }

    public static void SetComment(FsmStateAction action, string text) => action.GetType().GetField("comment", BindingFlags.Instance | BindingFlags.Public)?.SetValue((object) action, (object) text);

    public static string GetComment(FsmStateAction action)
    {
      FieldInfo field = action.GetType().GetField("comment", BindingFlags.Instance | BindingFlags.Public);
      return field == null ? "" : field.GetValue((object) action) as string;
    }

    public static System.Type GetRunFsmAction()
    {
      if (HutongGames.PlayMakerEditor.Actions.runFSM == null)
        HutongGames.PlayMakerEditor.Actions.runFSM = ReflectionUtils.GetGlobalType("HutongGames.PlayMaker.Actions.RunFSM");
      return HutongGames.PlayMakerEditor.Actions.runFSM ?? (HutongGames.PlayMakerEditor.Actions.runFSM = typeof (MissingAction));
    }

    public static void SetRunFsmActionTemplate(FsmStateAction action, FsmTemplate template)
    {
      if (action is MissingAction)
        return;
      FieldInfo field = HutongGames.PlayMakerEditor.Actions.runFSM.GetField("fsmTemplateControl");
      if (field == null || !(field.GetValue((object) action) is FsmTemplateControl fsmTemplateControl))
        return;
      fsmTemplateControl.fsmTemplate = template;
      field.SetValue((object) action, (object) fsmTemplateControl);
    }

    [Conditional("PROFILE")]
    private static void ProfileStart(string message) => BlockTimer.Start("Actions: " + message);

    [Conditional("PROFILE")]
    private static void ProfileEnd() => UnityEngine.Debug.Log((object) BlockTimer.End());

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }
  }
}
