// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.HeaderEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker.ActionsInternal;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [CustomActionEditor(typeof (Header))]
  public class HeaderEditor : CustomActionEditor
  {
    private Header header;
    private HtmlTextEditor htmlTextEditor;

    public override bool showCategoryIcon => false;

    public override bool showEnabledCheckbox => false;

    public override void OnEnable()
    {
      this.header = (Header) this.target;
      this.htmlTextEditor = new HtmlTextEditor();
    }

    public override void OnDisable() => this.htmlTextEditor = (HtmlTextEditor) null;

    public override bool OnGUI()
    {
      EditorGUI.BeginChangeCheck();
      GUILayout.BeginVertical(FsmEditorStyles.StandardMargins);
      GUILayout.Space(10f);
      this.header.comment = this.htmlTextEditor.OnGUI(this.header.comment, CustomActionEditor.maxEditorWidth - 20f);
      if (!string.IsNullOrEmpty(this.header.comment))
        GUILayout.Space(10f);
      GUILayout.EndVertical();
      return EditorGUI.EndChangeCheck();
    }
  }
}
