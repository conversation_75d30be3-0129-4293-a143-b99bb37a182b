// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.NewVariableWindow
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class NewVariableWindow : EditorWindow
  {
    private string titleLabel;
    private FsmVariables fsmVariables;
    private TextField editField;
    private string validationError;
    private Rect frame;

    public TextField.EditCommitedCallback EditCommited
    {
      get => this.editField.EditCommited;
      set => this.editField.EditCommited = value;
    }

    public static NewVariableWindow CreateDropdown(
      string title,
      Rect buttonRect,
      FsmVariables fsmVariables,
      string variableName)
    {
      NewVariableWindow instance = ScriptableObject.CreateInstance<NewVariableWindow>();
      instance.Init(title, fsmVariables, variableName);
      buttonRect.y += EditorGUIUtility.singleLineHeight + 6f;
      instance.ShowAsDropDown(buttonRect, new Vector2(buttonRect.width, 70f));
      return instance;
    }

    private void Init(string windowTitle, FsmVariables variables, string variableName)
    {
      this.titleLabel = windowTitle;
      this.fsmVariables = variables;
      this.editField = new TextField((EditorWindow) this, GUIContent.none);
      this.editField.Focus();
      this.editField.EditCommited = new TextField.EditCommitedCallback(this.Commit);
      this.editField.EditCanceled = new TextField.EditingCancelledCallback(this.Cancel);
      this.editField.ValidateText = new TextField.ValidateCallback(this.ValidateVariableName);
      this.editField.Text = variableName;
      this.editField.Validate();
    }

    public void OnGUI()
    {
      FsmEditorStyles.Init();
      GUILayout.Label(this.titleLabel, EditorStyles.boldLabel);
      this.editField.OnGUI();
      if (!string.IsNullOrEmpty(this.validationError))
      {
        GUILayout.Box(this.validationError, FsmEditorStyles.ErrorBox);
      }
      else
      {
        EditorGUI.BeginDisabledGroup(string.IsNullOrEmpty(this.editField.Text));
        if (GUILayout.Button(Strings.Label_Create_Variable))
          this.editField.CommitEdit();
        EditorGUI.EndDisabledGroup();
      }
      if (Event.current.type != UnityEngine.EventType.Repaint)
        return;
      this.frame = new Rect(this.position)
      {
        x = 0.0f,
        y = 0.0f
      };
      GUIHelpers.BeginGuiColor(FsmEditorStyles.LabelTextColor, 0.5f);
      GUI.Box(this.frame, GUIContent.none, FsmEditorStyles.SinglePixelFrame);
      GUIHelpers.EndGuiColor();
    }

    private void Commit(TextField textField) => this.Close();

    private void Cancel(TextField textField) => this.Close();

    private bool ValidateVariableName(string variableName)
    {
      this.validationError = "";
      if (string.IsNullOrEmpty(variableName))
        return false;
      if (!this.fsmVariables.Contains(variableName))
        return true;
      this.validationError = Strings.Error_Name_already_exists;
      return false;
    }
  }
}
