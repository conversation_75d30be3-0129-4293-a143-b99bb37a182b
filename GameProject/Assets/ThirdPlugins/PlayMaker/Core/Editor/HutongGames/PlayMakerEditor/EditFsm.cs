// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.EditFsm
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class EditFsm
  {
    public static void UseTemplate(this PlayMakerFSM fsmComponent, FsmTemplate template)
    {
      fsmComponent.SetFsmTemplate(template);
      fsmComponent.OptimizeTemplateControls();
    }

    public static void OptimizeTemplateControls(this PlayMakerFSM fsmComponent)
    {
    }

    public static bool IsPrefab(this Fsm fsm) => FsmPrefabs.IsPrefabOrPreview(fsm.OwnerObject);

    public static void Reload(this Fsm fsm)
    {
      if (fsm == null)
        return;
      fsm.Reinitialize();
      fsm.InitEditorData();
    }

    public static void InitEditorData(this Fsm fsm)
    {
      foreach (FsmState state in fsm.States)
        state.ActionData.InitEditorData(state);
    }

    public static void Rename(this Fsm fsm, string newName)
    {
      if (fsm == null || fsm.Name == newName)
        return;
      UndoUtility.RecordObject(fsm.OwnerObject, "Edit FSM Name");
      fsm.Name = newName;
      FsmEditor.UpdateEditors(fsm);
      FsmEditor.OnChanged(fsm);
    }

    public static string GetLabelWithPath(this Fsm fsm)
    {
      if (fsm == null)
        return "None (FSM)";
      if ((UnityEngine.Object) fsm.UsedInTemplate != (UnityEngine.Object) null)
        return "Template: " + fsm.UsedInTemplate.name;
      return (UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null ? "FSM Missing Owner" : fsm.OwnerName + " : " + Labels.GetFsmLabel(fsm);
    }

    public static string[] GetStateNames(this Fsm fsm) => ((IEnumerable<FsmState>) fsm.States).Select<FsmState, string>((Func<FsmState, string>) (x => x.Name)).ToArray<string>();

    public static FsmState AddState(this Fsm fsm) => fsm.AddState(Vector2.zero);

    public static FsmState AddState(this Fsm fsm, string stateName) => fsm.AddState(Vector2.zero, stateName);

    public static FsmState AddState(this Fsm fsm, Vector2 position, string newStateName = "")
    {
      newStateName = newStateName == "" ? fsm.GetUniqueStateName(FsmEditorSettings.NewStateName) : fsm.VerifyNewStateName(newStateName);
      FsmState fsmState = new FsmState(fsm)
      {
        Name = newStateName
      };
      fsmState.Position = new Rect(fsmState.Position)
      {
        x = position.x,
        y = position.y
      };
      fsm.States = ArrayUtility.Add<FsmState>(fsm.States, fsmState);
      if (fsm.States.Length == 1)
        fsm.SetStartState(newStateName);
      return fsmState;
    }

    public static void RenameState(this Fsm fsm, FsmState state, string newName)
    {
      foreach (FsmState state1 in fsm.States)
      {
        foreach (FsmTransition transition in state1.Transitions)
        {
          if (transition.ToState == state.Name)
            transition.ToState = newName;
        }
      }
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (globalTransition.ToState == state.Name)
          globalTransition.ToState = newName;
      }
      if (fsm.StartState == state.Name)
        fsm.StartState = newName;
      state.Name = newName;
    }

    public static void DeleteStates(this Fsm fsm, List<FsmState> states, bool dialog = true)
    {
      foreach (FsmState state in states)
        fsm.DeleteState(state);
      if (!(fsm.StartState == ""))
        return;
      if (fsm.States.Length == 0)
      {
        fsm.StartState = fsm.AddState(new Vector2(100f, 100f)).Name;
        if (!dialog)
          return;
        Dialogs.OkDialog("Start State was deleted!\n\nNew Start State created:\n" + fsm.StartState);
      }
      else
      {
        fsm.StartState = fsm.States[0].Name;
        if (!dialog)
          return;
        Dialogs.OkDialog("Start State was deleted!\n\nNew Start State:\n" + fsm.StartState);
      }
    }

    public static void DeleteState(this Fsm fsm, FsmState state)
    {
      if (state == null)
        return;
      foreach (FsmState state1 in fsm.States)
      {
        foreach (FsmTransition transition in state1.Transitions)
        {
          if (transition.ToState == state.Name)
          {
            transition.ToState = "";
            transition.ToFsmState = (FsmState) null;
          }
        }
      }
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (globalTransition.ToState == state.Name)
          fsm.DeleteGlobalTransition(globalTransition);
      }
      if (fsm.StartState == state.Name)
        fsm.StartState = "";
      foreach (FsmTransition transition in state.Transitions)
        state.DeleteTransition(transition);
      fsm.States = ArrayUtility.Remove<FsmState>(fsm.States, state);
    }

    public static void AddStates(this Fsm fsm, IEnumerable<FsmState> states)
    {
      List<FsmState> fsmStateList = new List<FsmState>((IEnumerable<FsmState>) fsm.States);
      fsmStateList.AddRange(states);
      fsm.States = fsmStateList.ToArray();
    }

    public static void UpdateStateSizes(this Fsm fsm)
    {
      foreach (FsmState state in fsm.States)
        FsmEditor.GraphView.UpdateStateSize(state);
    }

    public static bool HasStartState(this Fsm fsm) => !string.IsNullOrEmpty(fsm.StartState) && ((IEnumerable<FsmState>) fsm.States).First<FsmState>((Func<FsmState, bool>) (state => state.Name == fsm.StartState)) != null;

    public static bool HasState(this Fsm fsm, string stateName) => ((IEnumerable<FsmState>) fsm.States).First<FsmState>((Func<FsmState, bool>) (state => state.Name == stateName)) != null;

    public static void CheckStartState(this Fsm fsm, bool confirmFix = false)
    {
      if (fsm.HasStartState() || confirmFix && !EditorUtility.DisplayDialog(Strings.Command_Set_Start_State, Strings.Dialog_Template_Missing_Start_State, Strings.OK))
        return;
      fsm.StartState = fsm.States[0].Name;
    }

    public static void SetStartState(this Fsm fsm, string stateName)
    {
      if (string.IsNullOrEmpty(stateName))
        Debug.LogError((object) "StartState name cannot be empty!");
      else
        fsm.StartState = stateName;
    }

    public static FsmState GetPrepareState(this Fsm fsm)
    {
      foreach (var state in fsm.States)
      {
        if (state.IsPrepareState())
          return state;
      }
      return null;
    }
    
    public static FsmState GetCleanState(this Fsm fsm)
    {
      foreach (var state in fsm.States)
      {
        if (state.IsCleanupState())
          return state;
      }
      return null;
    }

    public static void SetPrepareState(this Fsm fsm, string stateName)
    {
      if (string.IsNullOrEmpty(stateName))
        Debug.LogError((object) "PrepareState name cannot be empty!");
      else
      {
        var state = fsm.GetState(stateName);
        if (state == null)
          Debug.LogErrorFormat("PrepareState[{0}] isn't exist!", stateName);
        else if (!state.IsPrepareState())
        {
          fsm.DeletePrepareState();
          state.AddGlobalTransition(FsmEvent.GetOrCreateGlobalEvent("PREPARE"));    
        }
      }
    }
    
    public static void SetCleanupState(this Fsm fsm, string stateName)
    {
      if (string.IsNullOrEmpty(stateName))
        Debug.LogError((object) "CleanupState name cannot be empty!");
      else
      {
        var state = fsm.GetState(stateName);
        if (state == null)
          Debug.LogErrorFormat("CleanupState[{0}] isn't exist!", stateName);
        else if (!state.IsCleanupState())
        {
          fsm.DeleteCleanState();
          state.AddGlobalTransition(FsmEvent.GetOrCreateGlobalEvent("CLEANUP"));
        }
      }
    }

    public static void DeleteCleanState(this Fsm fsm)
    {
      foreach (var state in fsm.States)
      {
        if (state.IsCleanupState())
        {
          var transitions = fsm.GetGlobalTransitionsToState(state);
          foreach (var transition in transitions)
          {
            if (transition.FsmEvent.IsGlobal && transition.FsmEvent.Name == "CLEANUP")
            {
              fsm.DeleteGlobalTransition(transition);
              break;
            }
          }
        }
      }
    }
    
    public static void DeletePrepareState(this Fsm fsm)
    {
      foreach (var state in fsm.States)
      {
        if (state.IsPrepareState())
        {
          var transitions = fsm.GetGlobalTransitionsToState(state);
          foreach (var transition in transitions)
          {
            if (transition.FsmEvent.IsGlobal && transition.FsmEvent.Name == "PREPARE")
            {
              fsm.DeleteGlobalTransition(transition);
              break;
            }
          }
        }
      }
    }

    public static FsmState GetStartState(this Fsm fsm) => fsm.GetState(fsm.StartState);

    public static void SetStatesColorIndex(
      this Fsm fsm,
      IEnumerable<FsmState> states,
      int colorIndex)
    {
      foreach (FsmState state in states)
        state.SetColorIndex(colorIndex);
    }

    public static string VerifyNewStateName(this Fsm fsm, string desiredName) => !fsm.HasState(desiredName) ? desiredName : fsm.GetUniqueStateName(desiredName);

    public static string GetUniqueStateName(this Fsm fsm, string desiredName)
    {
      int num = 1;
      string stateName = desiredName + num.ToString((IFormatProvider) CultureInfo.InvariantCulture);
      while (fsm.HasState(stateName))
        stateName = desiredName + num++.ToString((IFormatProvider) CultureInfo.InvariantCulture);
      return stateName;
    }

    public static string ValidateNewStateName(this Fsm fsm, FsmState state, string newName)
    {
      if (string.IsNullOrEmpty(newName))
        return Strings.Error_Name_is_empty;
      foreach (FsmState state1 in fsm.States)
      {
        if (state1 != state && state1.Name == newName)
          return Strings.Error_Name_already_used_in_this_FSM;
      }
      if (newName.Length > 100)
        return Strings.Error_Name_is_too_long;
      return !newName.Contains("\\") ? "" : Strings.Error_Name_contains_illegal_character;
    }

    public static FsmTransition FindTransitionToStateFromActiveState(
      this Fsm fsm,
      FsmState toState)
    {
      if (toState == null)
        return (FsmTransition) null;
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (globalTransition.ToState == toState.Name)
          return globalTransition;
      }
      if (fsm.ActiveState == null)
        return (FsmTransition) null;
      foreach (FsmTransition transition in fsm.ActiveState.Transitions)
      {
        if (transition.ToState == toState.Name)
          return transition;
      }
      return (FsmTransition) null;
    }

    public static FsmTransition AddGlobalTransitionToState(
      this Fsm fsm,
      FsmState toState,
      FsmEvent fsmEvent)
    {
      FsmTransition fsmTransition = new FsmTransition()
      {
        ToState = toState.Name,
        FsmEvent = fsmEvent
      };
      fsm.GlobalTransitions = ArrayUtility.Add<FsmTransition>(fsm.GlobalTransitions, fsmTransition);
      return fsmTransition;
    }

    public static void AddGlobalTransitions(
      this Fsm fsm,
      IEnumerable<FsmTransition> globalTransitions)
    {
      List<FsmTransition> fsmTransitionList = new List<FsmTransition>((IEnumerable<FsmTransition>) fsm.GlobalTransitions);
      fsmTransitionList.AddRange(globalTransitions);
      fsm.GlobalTransitions = fsmTransitionList.ToArray();
    }

    public static List<FsmTransition> GetGlobalTransitionsToState(
      this Fsm fsm,
      FsmState state)
    {
      List<FsmTransition> fsmTransitionList = new List<FsmTransition>();
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (globalTransition.ToState == state.Name)
          fsmTransitionList.Add(globalTransition);
      }
      return fsmTransitionList;
    }

    public static bool IsGlobalTransitionToState(this Fsm fsm, FsmState state)
    {
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (globalTransition.ToState == state.Name)
          return true;
      }
      return false;
    }

    public static void DeleteGlobalTransition(this Fsm fsm, FsmTransition transition) => fsm.GlobalTransitions = ArrayUtility.Remove<FsmTransition>(fsm.GlobalTransitions, transition);

    public static void SetTransitionTargetState(
      this Fsm fsm,
      FsmTransition transition,
      string toState)
    {
      transition.ToState = toState;
      transition.ToFsmState = fsm.GetState(toState);
    }

    public static FsmState GetTransitionTargetState(this Fsm fsm, FsmTransition transition)
    {
      if (transition == null)
        return (FsmState) null;
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (globalTransition == transition)
          return fsm.GetState(globalTransition.ToState);
      }
      foreach (FsmState state in fsm.States)
      {
        foreach (FsmTransition transition1 in state.Transitions)
        {
          if (transition1 == transition)
            return state;
        }
      }
      return (FsmState) null;
    }

    public static bool HasVariable(this Fsm fsm, string name) => fsm != null && !string.IsNullOrEmpty(name) && fsm.Variables.Contains(name);

    public static NamedVariable AddVariable(
      this Fsm fsm,
      VariableType type,
      string name,
      System.Type objectType = null,
      VariableType typeConstraint = VariableType.Float)
    {
      if (fsm.HasVariable(name))
      {
        Debug.LogWarning((object) ("Trying to add variable with name that already exists: " + name));
        return (NamedVariable) null;
      }
      string[] outputVariableNames = fsm.GetOutputVariableNames();
      fsm.Variables.AddVariable(type, name, objectType, typeConstraint);
      fsm.SetOutputVariables(outputVariableNames);
      return fsm.Variables.FindVariable(name);
    }

    public static NamedVariable AddVariable(this Fsm fsm, NamedVariable variable)
    {
      if (fsm.HasVariable(variable.Name))
      {
        Debug.LogWarning((object) ("Trying to add variable with name that already exists: " + variable.Name));
        return (NamedVariable) null;
      }
      string[] array = ((IEnumerable<NamedVariable>) fsm.GetOutputVariables()).Select<NamedVariable, string>((Func<NamedVariable, string>) (x => x.Name)).ToArray<string>();
      fsm.Variables.AddVariable(variable);
      fsm.SetOutputVariables(array);
      return variable;
    }

    public static void SetVariableIsOutput(this Fsm fsm, NamedVariable variable, bool isOutput)
    {
      if (fsm == null || variable == null)
        return;
      List<string> list = ((IEnumerable<NamedVariable>) fsm.GetOutputVariables()).Where<NamedVariable>((Func<NamedVariable, bool>) (x => x != null)).Select<NamedVariable, string>((Func<NamedVariable, string>) (x => x.Name)).ToList<string>();
      if (isOutput)
      {
        if (!list.Contains(variable.Name))
          list.Add(variable.Name);
      }
      else
        list.RemoveAll((Predicate<string>) (x => x == variable.Name));
      fsm.SetOutputVariables(list.ToArray());
    }

    public static bool GetVariableIsOutput(this Fsm fsm, NamedVariable variable) => variable != null && new List<NamedVariable>((IEnumerable<NamedVariable>) fsm.GetOutputVariables()).Contains(variable);

    public static void DeleteVariable(this Fsm fsm, string variableName)
    {
      if (fsm == null || string.IsNullOrEmpty(variableName))
        return;
      fsm.DeleteVariable(fsm.Variables.GetVariable(variableName));
    }

    public static void DeleteVariable(this Fsm fsm, NamedVariable variable)
    {
      if (fsm == null || variable == null)
        return;
      string name = variable.Name;
      int variableIndex = fsm.Variables.GetVariableIndex(variable);
      if (variableIndex == -1)
        return;
      List<string> stringList = new List<string>((IEnumerable<string>) fsm.GetOutputVariableNames());
      fsm.Variables.CategoryIDs[variableIndex] = 0;
      fsm.Variables.CategoryIDs = ArrayUtility.RemoveAt<int>(fsm.Variables.CategoryIDs, variableIndex);
      stringList.RemoveAll((Predicate<string>) (x => x == variable.Name));
      FsmBuilder.RemoveVariableUsage(fsm, variable);
      fsm.Variables.DeleteVariable(variable, fsm.IsPrefab());
      fsm.SetOutputVariables(stringList.ToArray());
      if (!fsm.HasVariable(name))
        return;
      Debug.LogWarning((object) ("Deleted variable, but duplicate still exists! " + name));
    }

    public static void RenameVariable(this Fsm fsm, NamedVariable variable, string newName)
    {
      bool variableIsOutput = fsm.GetVariableIsOutput(variable);
      List<string> stringList = new List<string>((IEnumerable<string>) fsm.GetOutputVariableNames());
      stringList.Remove(variable.Name);
      FsmSearch.Update(fsm);
      foreach (FsmInfo variableUsage in FsmSearch.GetVariableUsageList(fsm, variable))
        FsmBuilder.RenameVariableInField(variableUsage.fieldInObject, variableUsage.field, variable, newName, 0);
      fsm.Variables.RenameVariable(variable, newName);
      if (variableIsOutput)
        stringList.Add(newName);
      fsm.SetOutputVariables(stringList.ToArray());
      FsmEditor.SaveActions(fsm);
    }

    public static FsmEvent AddEvent(this Fsm fsm, string name) => fsm.AddEvent(FsmEvent.GetFsmEvent(name));

    public static FsmEvent AddEvent(this Fsm fsm, FsmEvent fsmEvent)
    {
      if (fsmEvent == null)
        return (FsmEvent) null;
      if (fsm.FindEvent(fsmEvent.Name) != null)
        return fsmEvent;
      fsm.Events = ArrayUtility.Add<FsmEvent>(fsm.Events, fsmEvent);
      return fsmEvent;
    }

    public static FsmEvent FindEvent(this Fsm fsm, string eventName)
    {
      foreach (FsmEvent fsmEvent in fsm.Events)
      {
        if (fsmEvent.Name == eventName)
          return fsmEvent;
      }
      return (FsmEvent) null;
    }

    public static void RenameEvent(this Fsm fsm, string oldEventName, string newEventName)
    {
      if (fsm == null || newEventName == oldEventName)
        return;
      foreach (FsmInfo eventSentBy in FsmSearch.GetSearch(fsm).GetEventSentByList(oldEventName))
      {
        if (eventSentBy.fieldInArray != null)
          eventSentBy.fieldInArray.SetValue((object) new FsmEvent(newEventName), eventSentBy.arrayIndex);
        else
          eventSentBy.field.SetValue(eventSentBy.fieldInObject, (object) new FsmEvent(newEventName));
      }
      fsm.SaveActions();
      foreach (FsmInfo fsmInfo in FsmInfo.FindTransitionsUsingEvent(fsm, oldEventName))
        fsmInfo.transition.FsmEvent.Name = newEventName;
      foreach (FsmEvent fsmEvent in fsm.Events)
      {
        if (fsmEvent.Name == oldEventName)
          fsmEvent.Name = newEventName;
      }
      foreach (FsmEvent exposedEvent in fsm.ExposedEvents)
      {
        if (exposedEvent.Name == oldEventName)
          exposedEvent.Name = newEventName;
      }
      if (FsmEditor.GraphView != null)
        FsmEditor.GraphView.UpdateStateSizes(fsm);
      FsmEditor.SetFsmDirty(fsm, true);
      FsmEditor.UpdateFsmInfo(fsm);
    }

    public static void AddExposedEvent(this Fsm fsm, FsmEvent fsmEvent) => fsm?.ExposedEvents.Add(fsmEvent);

    public static void RemoveExposedEvent(this Fsm fsm, FsmEvent fsmEvent)
    {
      if (fsm == null)
        return;
      fsm.RemoveExposedEvent(fsmEvent.Name);
    }

    public static void RemoveExposedEvent(this Fsm fsm, string eventName) => fsm?.ExposedEvents.RemoveAll((Predicate<FsmEvent>) (x => x.Name == eventName));

    public static void DeleteEvent(this Fsm fsm, string fsmEventName)
    {
      if (fsm == null || string.IsNullOrEmpty(fsmEventName))
        return;
      List<FsmEvent> fsmEventList = new List<FsmEvent>((IEnumerable<FsmEvent>) fsm.Events);
      fsmEventList.RemoveAll((Predicate<FsmEvent>) (x => x.Name == fsmEventName));
      fsm.Events = fsmEventList.ToArray();
      fsm.RemoveExposedEvent(fsmEventName);
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (globalTransition.EventName == fsmEventName)
          globalTransition.FsmEvent = (FsmEvent) null;
      }
      foreach (FsmState state in fsm.States)
      {
        foreach (FsmTransition transition in state.Transitions)
        {
          if (transition.EventName == fsmEventName)
            transition.FsmEvent = (FsmEvent) null;
        }
        foreach (FsmStateAction action in state.Actions)
          state.DeleteEvent(action, fsmEventName);
      }
    }

    public static void DeleteEvent(this Fsm fsm, FsmEvent fsmEvent)
    {
      if (fsm == null || FsmEvent.IsNullOrEmpty(fsmEvent))
        return;
      fsm.DeleteEvent(fsmEvent.Name);
    }

    public static string ValidateAddEvent(this Fsm fsm, string newEventName)
    {
      if (string.IsNullOrEmpty(newEventName))
        return "";
      if (newEventName.Replace(" ", "") == "")
        return Strings.Error_Invalid_Name;
      foreach (FsmEvent fsmEvent in fsm.Events)
      {
        if (fsmEvent.Name == newEventName)
          return Strings.Error_Event_already_used;
      }
      return "";
    }

    public static string SanityCheckExposedEvents(this Fsm fsm)
    {
      if (fsm == null)
        return "";
      int count = fsm.ExposedEvents.Count;
      fsm.ExposedEvents.RemoveAll((Predicate<FsmEvent>) (x => !Array.Exists<FsmEvent>(fsm.Events, (Predicate<FsmEvent>) (fsmEvent => fsmEvent.Name == x.Name))));
      List<FsmEvent> fsmEventList = new List<FsmEvent>();
      foreach (FsmEvent exposedEvent1 in fsm.ExposedEvents)
      {
        FsmEvent exposedEvent = exposedEvent1;
        if (fsmEventList.Find((Predicate<FsmEvent>) (x => x.Name == exposedEvent.Name)) == null)
          fsmEventList.Add(exposedEvent);
      }
      fsm.ExposedEvents = fsmEventList;
      return fsm.ExposedEvents.Count == count ? "" : "\nFixed ExposedEvents";
    }

    public static string SanityCheckStatePositions(this Fsm fsm)
    {
      if (fsm == null)
        return "";
      bool flag = false;
      for (int index = 0; index < fsm.States.Length; ++index)
      {
        FsmState state = fsm.States[index];
        Rect position = state.Position;
        if (EditFsm.IsValidFloat(position.x))
        {
          position = state.Position;
          if (EditFsm.IsValidFloat(position.y))
            continue;
        }
        FsmState fsmState = state;
        double num = (double) (50 + 100 * index);
        position = state.Position;
        double width = (double) position.width;
        position = state.Position;
        double height = (double) position.height;
        Rect rect = new Rect(100f, (float) num, (float) width, (float) height);
        fsmState.Position = rect;
        flag = true;
      }
      return !flag ? "" : "\nFixed StatePosition IsNaN";
    }

    public static string SanityCheckStartState(this Fsm fsm)
    {
      if (!string.IsNullOrEmpty(fsm.StartState))
        return "";
      if (string.IsNullOrEmpty(fsm.States[0].Name))
        fsm.States[0].Name = fsm.GetUniqueStateName("State");
      fsm.StartState = fsm.States[0].Name;
      Debug.Log((object) ("PlayMaker: Fixed missing Start State in " + FsmUtility.GetFullFsmLabel(fsm)), fsm.OwnerObject);
      return "\nMissing StartState (fixed).";
    }

    private static bool IsValidFloat(float f) => !float.IsNaN(f) && !float.IsInfinity(f);
  }
}
