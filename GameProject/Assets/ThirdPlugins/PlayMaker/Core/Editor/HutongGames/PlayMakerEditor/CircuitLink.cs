// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.CircuitLink
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  internal class CircuitLink : BaseLink
  {
    private static CircuitLink instance;
    private const float TangentYFactor = 0.4f;
    private const float MinTangentLength = 30f;
    private const float MaxTangentLength = 48f;
    private float tangentLength;

    public static CircuitLink Instance => CircuitLink.instance ?? (CircuitLink.instance = new CircuitLink());

    public override GraphViewLinkStyle LinkStyle => GraphViewLinkStyle.CircuitLinks;

    public override void Draw(
      FsmState fromState,
      FsmState toState,
      int transitionIndex,
      Color linkColor,
      float linkWidth,
      float scale)
    {
      this.Init(fromState, toState, transitionIndex, scale);
      this.horizontalMinDistance = 30f;
      this.tangentLength = Mathf.Abs(this.fromStateY - this.toStateY) * 0.4f;
      this.tangentLength = Mathf.Clamp(this.tangentLength, 30f, 48f);
      this.DoDefaultLinker();
      this.DrawArrowHead(linkColor);
      linkWidth = Mathf.Min(linkWidth, linkWidth * 2f * scale);
      if ((double) Mathf.Abs(this.fromPos.y - this.toPos.y) < 1.0)
        BaseLink.DrawPolylineCircuit(linkColor, linkWidth, scale, this.fromPos, this.toPos);
      else if ((double) linkWidth > 2.0)
        BaseLink.DrawPolyline(linkColor, linkWidth, scale, this.fromPos, this.fromTangent, this.toTangent, this.toPos);
      else
        BaseLink.DrawPolylineCircuit(linkColor, linkWidth, scale, this.fromPos, this.fromTangent, this.toTangent, this.toPos);
    }

    protected override void LinkRightSideToRightSide()
    {
      this.fromPos.x = this.fromStateRightX;
      this.toPos.x = this.toStateRightX;
      this.fromTangent.x = Mathf.Max(this.fromPos.x + this.tangentLength, this.toPos.x + this.tangentLength);
      this.toTangent.x = this.fromTangent.x;
    }

    protected override void LinkRightSideToLeftSide()
    {
      this.fromPos.x = this.fromStateRightX;
      this.toPos.x = this.toStateLeftX;
      float num1 = this.fromPos.x + this.tangentLength;
      float num2 = this.toPos.x - this.tangentLength;
      this.fromTangent.x = num1 + (float) (((double) num2 - (double) num1) * 0.300000011920929);
      this.toTangent.x = this.fromTangent.x;
    }

    protected override void LinkLeftSideToLeftSide()
    {
      this.fromPos.x = this.fromStateLeftX;
      this.toPos.x = this.toStateLeftX;
      this.fromTangent.x = Mathf.Min(this.fromPos.x - this.tangentLength, this.toPos.x - this.tangentLength);
      this.toTangent.x = this.fromTangent.x;
    }

    protected override void LinkLeftSideToRightSide()
    {
      this.fromPos.x = this.fromStateLeftX;
      this.toPos.x = this.toStateRightX;
      float num1 = this.fromPos.x - this.tangentLength;
      float num2 = this.toPos.x + this.tangentLength;
      this.fromTangent.x = num1 + (float) (((double) num2 - (double) num1) * 0.300000011920929);
      this.toTangent.x = this.fromTangent.x;
    }

    public override bool HitTest(Vector2 point, float hitDistance) => false;
  }
}
