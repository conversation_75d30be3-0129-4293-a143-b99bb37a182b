// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmVariableEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class FsmVariableEditor
  {
    public static bool UnityInspectorMode;
    protected FsmVariable fsmVariable;
    protected bool propertyPathsInitialized;
    protected SerializedObject serializedOwner;
    protected SerializedProperty valueProp;
    protected SerializedProperty tooltipProp;
    protected SerializedProperty inspectorProp;
    protected SerializedProperty networkSyncProp;
    private VariableTypeMenu _typeMenu;

    private VariableTypeMenu typeMenu => this._typeMenu ?? (this._typeMenu = new VariableTypeMenu());

    public FsmVariableEditor(FsmVariable variable)
    {
      this.fsmVariable = variable;
      this.serializedOwner = this.fsmVariable.SerializedObject;
      if (this.serializedOwner == null)
        return;
      this.valueProp = this.FindProperty("value");
      this.tooltipProp = this.FindProperty("tooltip");
      this.inspectorProp = this.FindProperty("showInInspector");
      this.networkSyncProp = this.FindProperty("networkSync");
      this.propertyPathsInitialized = true;
    }

    protected SerializedProperty FindProperty(string field) => this.serializedOwner.FindProperty(this.fsmVariable.PropertyPath + "." + field);

    public void Update()
    {
      if (this.serializedOwner == null || !(this.serializedOwner.targetObject != (Object) null))
        return;
      this.serializedOwner.Update();
    }

    public bool IsValid() => this.propertyPathsInitialized;

    public virtual void DoValueField(GUIContent label, bool isAsset)
    {
      if (this.valueProp != null)
      {
        if (this.valueProp.serializedObject == null || !(this.valueProp.serializedObject.targetObject != (Object) null))
          return;
        EditorGUILayout.PropertyField(this.valueProp, label, true);
      }
      else
        this.fsmVariable.DoInspectorGUI(label, isAsset);
    }

    public void DoTypeField(GUIContent label)
    {
      GUILayout.BeginHorizontal();
      EditorGUILayout.PrefixLabel(label, EditorStyles.popup);
      if (GUILayout.Button(this.fsmVariable.TypeLabel, EditorStyles.popup))
        this.typeMenu.SelectVariableType(this.fsmVariable);
      GUILayout.EndHorizontal();
    }

    public void DoTypeField(params GUILayoutOption[] layoutOptions)
    {
      if (!GUILayout.Button(this.fsmVariable.TypeLabel, EditorStyles.popup, layoutOptions))
        return;
      this.typeMenu.SelectVariableType(this.fsmVariable);
    }

    public virtual void DoTypeSelector()
    {
    }

    public void DoTooltipField(GUIContent label, params GUILayoutOption[] options)
    {
      if (this.tooltipProp != null)
        FsmEditorGUILayout.TextFieldWithWordWrap(label, this.tooltipProp, options);
      else
        this.fsmVariable.Tooltip = EditorGUILayout.DelayedTextField(label, this.fsmVariable.Tooltip, options);
    }

    public void DoNetworkSyncField(GUIContent label)
    {
      if (this.networkSyncProp != null)
        EditorGUILayout.PropertyField(this.networkSyncProp, label);
      else
        this.fsmVariable.NetworkSync = EditorGUILayout.Toggle(label, this.fsmVariable.NetworkSync);
    }

    public void DoShowInInspectorField(GUIContent label)
    {
      if (this.inspectorProp != null)
        EditorGUILayout.PropertyField(this.inspectorProp, label);
      else
        this.fsmVariable.ShowInInspector = EditorGUILayout.Toggle(label, this.fsmVariable.ShowInInspector);
    }

    public void SaveChanges()
    {
      if (this.serializedOwner == null || !this.serializedOwner.ApplyModifiedProperties())
        return;
      this.fsmVariable.UpdateVariable();
    }
  }
}
