// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.ObjectPropertyDrawers
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  internal class ObjectPropertyDrawers
  {
    private static Dictionary<Type, ObjectPropertyDrawer> drawers;

    public static ObjectPropertyDrawer GetObjectPropertyDrawer(Type objType)
    {
      if (objType == null)
        return (ObjectPropertyDrawer) null;
      if (ObjectPropertyDrawers.drawers == null)
        ObjectPropertyDrawers.Rebuild();
      ObjectPropertyDrawer objectPropertyDrawer;
      ObjectPropertyDrawers.drawers.TryGetValue(objType, out objectPropertyDrawer);
      return objectPropertyDrawer;
    }

    public static void Init() => ObjectPropertyDrawers.Rebuild();

    private static void Rebuild()
    {
      ObjectPropertyDrawers.drawers = new Dictionary<Type, ObjectPropertyDrawer>();
      foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
      {
        try
        {
          foreach (Type exportedType in assembly.GetExportedTypes())
          {
            if (typeof (ObjectPropertyDrawer).IsAssignableFrom(exportedType) && exportedType.IsClass && !exportedType.IsAbstract)
            {
              Type inspectedType = CustomAttributeHelpers.GetAttribute<ObjectPropertyDrawerAttribute>(exportedType)?.InspectedType;
              if (inspectedType != null && !ObjectPropertyDrawers.drawers.ContainsKey(inspectedType))
                ObjectPropertyDrawers.drawers.Add(inspectedType, (ObjectPropertyDrawer) Activator.CreateInstance(exportedType));
            }
          }
        }
        catch (Exception ex)
        {
          NotSupportedException supportedException = ex as NotSupportedException;
        }
      }
    }
  }
}
