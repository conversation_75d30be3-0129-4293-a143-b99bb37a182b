// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.VariableTypeDefinition
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;

namespace HutongGames.PlayMakerEditor
{
  [Serializable]
  public class VariableTypeDefinition
  {
    public string Name;
    public string Type;

    public VariableTypeDefinition(string name, string type)
    {
      this.Name = name;
      this.Type = type;
    }

    public VariableTypeDefinition(string name) => this.Name = name;
  }
}
