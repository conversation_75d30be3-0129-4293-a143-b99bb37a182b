// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.ListSpacers
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using UnityEditor;
using UnityEditor.AnimatedValues;
using UnityEngine;
using UnityEngine.Events;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public class ListSpacers
  {
    private bool debug;
    private readonly EditorWindow window;
    private readonly Dictionary<int, ListSpacers.Spacer> spacers = new Dictionary<int, ListSpacers.Spacer>();

    public ListSpacers(EditorWindow _window) => this.window = _window;

    public void AddDeleteSpacer(int atIndex, float height)
    {
      if (this.debug)
        Debug.Log((object) ("Add Delete Spacer: " + (object) atIndex + " " + (object) height));
      ListSpacers.Spacer spacer;
      if (this.spacers.TryGetValue(atIndex, out spacer))
      {
        spacer.height += height;
        spacer.animFloat.value += height;
        spacer.animFloat.target = 0.0f;
      }
      else
      {
        spacer = new ListSpacers.Spacer();
        spacer.height = height;
        ref ListSpacers.Spacer local = ref spacer;
        AnimFloat animFloat = new AnimFloat(height);
        animFloat.target = 0.0f;
        animFloat.speed = 3f;
        local.animFloat = animFloat;
        spacer.animFloat.valueChanged.AddListener(new UnityAction(this.Update));
        this.spacers.Add(atIndex, spacer);
      }
    }

    public void AddInsertSpacer(int atIndex, float height)
    {
      if (this.debug)
        Debug.Log((object) ("Add Insert Spacer: " + (object) atIndex + " " + (object) height));
      ListSpacers.Spacer spacer;
      if (this.spacers.TryGetValue(atIndex, out spacer))
      {
        spacer.height += height;
        spacer.animFloat.target += height;
      }
      else
      {
        spacer = new ListSpacers.Spacer();
        spacer.isInsert = true;
        spacer.height = height;
        ref ListSpacers.Spacer local = ref spacer;
        AnimFloat animFloat = new AnimFloat(0.0f);
        animFloat.target = height;
        animFloat.speed = 3f;
        local.animFloat = animFloat;
        spacer.animFloat.valueChanged.AddListener(new UnityAction(this.Update));
        this.spacers.Add(atIndex, spacer);
      }
    }

    public void BeginSpacer(int atIndex)
    {
      ListSpacers.Spacer spacer;
      if (!this.spacers.TryGetValue(atIndex, out spacer))
        return;
      if (!spacer.isInsert)
        GUILayout.Box(GUIContent.none, GUIStyle.none, GUILayout.Height(spacer.animFloat.value));
      else
        EditorGUILayout.BeginFadeGroup(spacer.animFloat.value / spacer.height);
    }

    public void EndSpacer(int atIndex)
    {
      ListSpacers.Spacer spacer;
      if (!this.spacers.TryGetValue(atIndex, out spacer) || !spacer.isInsert)
        return;
      EditorGUILayout.EndFadeGroup();
    }

    public void Update()
    {
      this.Repaint();
      int count = this.spacers.Count;
      for (int index = count - 1; index >= 0; --index)
      {
        KeyValuePair<int, ListSpacers.Spacer> keyValuePair = this.spacers.ElementAt<KeyValuePair<int, ListSpacers.Spacer>>(index);
        AnimFloat animFloat = keyValuePair.Value.animFloat;
        if ((double) Mathf.Abs(animFloat.value - animFloat.target) < 3.0)
          this.spacers.Remove(keyValuePair.Key);
      }
      if (!this.debug || count == this.spacers.Count)
        return;
      Debug.Log((object) ("Spacers:  " + (object) this.spacers.Count));
    }

    private void Repaint()
    {
      if ((Object) this.window != (Object) null)
        this.window.Repaint();
      else
        FsmEditor.RepaintAll();
    }

    private struct Spacer
    {
      internal AnimFloat animFloat;
      internal float height;
      internal bool isInsert;
    }
  }
}
