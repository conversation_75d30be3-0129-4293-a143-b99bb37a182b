// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.EditorCommands
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class EditorCommands
  {
    public static void OpenPreferences() => FsmEditor.Inspector.SetMode(InspectorMode.Preferences);

    public static void OpenPreferences(object userdata)
    {
      int num = (int) userdata;
      FsmEditor.Inspector.SetMode(InspectorMode.Preferences);
      FsmEditorSettings.SetCategory((FsmEditorSettings.Categories) num);
    }

    public static void ToggleLogging()
    {
      FsmEditorSettings.EnableLogging = !FsmEditorSettings.EnableLogging;
      FsmLog.LoggingEnabled = FsmEditorSettings.EnableLogging;
      FsmEditorSettings.SaveSettings();
    }

    public static void Cancel()
    {
    }

    public static void SelectState(object userdata)
    {
      if (!(userdata is FsmState state))
        return;
      if (FsmEditor.IsOpen)
        FsmEditor.SelectState(state, true);
      else
        FsmEditor.Open(state);
    }

    public static void SelectFsm(object userdata) => FsmEditor.SelectFsm((Fsm) userdata);

    public static FsmState AddState(Vector2 position) => !FsmEditor.DisconnectCheck() ? (FsmState) null : FsmEditor.GraphView.AddState(position);

    public static bool RenameState(FsmState state, string newName)
    {
      if (state == null)
        return false;
      if (state.Name == newName)
        return true;
      string message = FsmEditor.SelectedFsm.ValidateNewStateName(state, newName);
      if (message != "")
      {
        EditorUtility.DisplayDialog(Strings.Error_Cannot_Rename_State, message, Strings.OK);
        return false;
      }
      FsmEditor.RecordUndo(Strings.Command_Rename_State);
      FsmEditor.SelectedFsm.RenameState(state, newName);
      FsmEditor.Selection.Update();
      FsmEditor.GraphView.UpdateStateSize(state);
      FsmEditor.SetFsmDirty(true);
      return true;
    }

    [Obsolete("Use Fsm.AddState(stateName) extension method instead.")]
    public static void SetStateName(FsmState state, string name)
    {
      FsmEditor.Builder.SetStateName(state, name);
      FsmEditor.GraphView.UpdateStateSize(state);
    }

    public static FsmStateAction AddAction(FsmState state, System.Type actionType)
    {
      if (!FsmEditor.DisconnectCheck())
        return (FsmStateAction) null;
      FsmEditor.RecordUndo(Strings.Command_Add_Action);
      FsmStateAction fsmStateAction = state.AddAction(actionType);
      fsmStateAction.InitEditor(state.Fsm);
      ActionSelector.AddActionToRecent(actionType);
      FsmEditor.UpdateActionUsage();
      return fsmStateAction;
    }

    public static FsmStateAction AddRunFsmActionToState(
      FsmState state,
      FsmTemplate template)
    {
      if (!FsmEditor.DisconnectCheck())
        return (FsmStateAction) null;
      FsmStateAction action = EditorCommands.AddAction(state, Actions.GetRunFsmAction());
      Actions.SetRunFsmActionTemplate(action, template);
      FsmEditor.SelectState(state, false);
      return action;
    }

    public static FsmStateAction InsertAction(
      FsmState state,
      System.Type actionType,
      FsmStateAction beforeAction)
    {
      if (!FsmEditor.DisconnectCheck())
        return (FsmStateAction) null;
      FsmEditor.RecordUndo(Strings.Command_Add_Action);
      FsmStateAction fsmStateAction = state.InsertAction(actionType, beforeAction);
      ActionSelector.AddActionToRecent(actionType);
      FsmEditor.UpdateActionUsage();
      return fsmStateAction;
    }

    public static void CutStateSelection()
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      FsmEditor.Clipboard.CopyStates(FsmEditor.SelectedFsm, FsmEditor.SelectedStates);
      EditorCommands.DeleteMultiSelection();
    }

    public static void CopyStateSelection() => FsmEditor.Clipboard.CopyStates(FsmEditor.SelectedFsm, FsmEditor.SelectedStates);

    public static void CopyFsm() => FsmEditor.Clipboard.CopyFsm(FsmEditor.SelectedFsm);

    public static void PasteFsm() => FsmEditor.SelectFsm(FsmEditor.Clipboard.PasteFsmToSelected());
    
    public static void ExportFsmAsLua()
    {
      FsmEditorMenu.ExportFsmToLua(FsmEditor.SelectedFsm);
    }
    
    public static void ExportAndRun()
    {
      FsmEditorMenu.ExportAndRun(FsmEditor.SelectedFsm);
    }
    
    public static void ExportAndLogin()
    {
      FsmEditorMenu.ExportAndRun(FsmEditor.SelectedFsm, true);
    }

    public static void SaveFsmAsTemplate()
    {
      FsmTemplate template = EditFsmTemplate.SaveFsmAsNewTemplate(FsmEditor.SelectedFsm);
      if (!((UnityEngine.Object) template != (UnityEngine.Object) null) || !((UnityEngine.Object) FsmEditor.SelectedFsmComponent != (UnityEngine.Object) null) || !Dialogs.YesNoDialog(Strings.Dialog_Save_Template, Strings.Dialog_Use_Saved_Template_in_this_FSM))
        return;
      FsmEditor.SelectedFsmComponent.UseTemplate(template);
      FsmEditor.Inspector.SetMode(InspectorMode.FsmInspector);
    }

    public static void SaveSelectionAsTemplate() => EditFsmTemplate.SaveStatesAsNewTemplate(FsmEditor.SelectedFsm, FsmEditor.SelectedStates);

    public static void AddTemplateToSelected(object userdata) => FsmEditor.SelectFsm(EditFsmTemplate.AddTemplateToSelected(userdata as FsmTemplate));

    public static void AddFsmAndUseTemplateWithSelected(object userdata) => FsmBuilder.AddFsmToSelected(userdata as FsmTemplate);

    public static void AddTemplate(object userdata) => FsmEditor.SelectFsm(EditFsmTemplate.AddTemplateToSelected(userdata as FsmTemplate));

    public static void SelectAll() => EditorCommands.SelectAllStates();

    public static void SelectAllStates()
    {
      FsmEditor.Selection.SelectAllStates();
      FsmEditor.Repaint();
    }

    public static void PasteStates()
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      EditorCommands.PasteStates(FsmGraphView.GetViewCenter());
    }

    public static void PasteStates(Vector2 position)
    {
      if (!FsmEditor.Clipboard.CanPaste() || !FsmEditor.DisconnectCheck())
        return;
      FsmEditor.Selection.SelectStates(FsmEditor.Clipboard.PasteStates(FsmEditor.SelectedFsm, position));
      FsmEditor.Inspector.ResetView();
      FsmEditor.GraphView.UpdateGraphBounds();
    }

    public static void PasteTemplate(Vector2 position)
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      EditorCommands.PasteTemplate(FsmEditor.SelectedTemplate, position);
    }

    public static void PasteTemplate(FsmTemplate template, Vector2 position)
    {
      if ((UnityEngine.Object) template == (UnityEngine.Object) null || !FsmEditor.DisconnectCheck())
        return;
      FsmEditor.Selection.SelectStates(FsmClipboard.PasteTemplate(FsmEditor.SelectedFsm, template, position));
      FsmEditor.UpdateViews();
    }

    public static void PasteTemplate(object userdata)
    {
      FsmTemplate template = userdata as FsmTemplate;
      Vector2 viewCenter = FsmGraphView.GetViewCenter();
      viewCenter.x -= 100f;
      viewCenter.y -= 100f;
      Vector2 position = viewCenter;
      EditorCommands.PasteTemplate(template, position);
    }

    public static bool AddTransitionToSelectedState()
    {
      if (!FsmEditor.DisconnectCheck())
        return false;
      EditorCommands.AddTransitionToState(FsmEditor.SelectedState);
      return true;
    }

    public static void AddTransitionToSelectedState(string eventName) => EditorCommands.AddTransitionToState(FsmEditor.SelectedState, eventName);

    public static void AddTransitionToState(FsmState state, string eventName = "")
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Command_Add_Transition);
      FsmTransition transition = state.AddTransition();
      transition.FsmEvent = FsmEvent.GetFsmEvent(eventName);
      FsmEditor.Selection.SelectTransition(transition);
      FsmEditor.GraphView.UpdateStateSize(FsmEditor.SelectedState);
      FsmEditor.UpdateFsmInfo();
      FsmEditor.SetFsmDirty(true);
    }

    public static void MakeEventGlobal(string eventName)
    {
      EditPlayMakerGlobals.SetEventIsGlobal(FsmEvent.GetFsmEvent(eventName), true);
      FsmEditor.UpdateFsmInfo();
      FsmEditor.EventsManager.Reset();
    }

    public static FsmTransition AddGlobalTransitionToSelectedState() => !FsmEditor.DisconnectCheck() ? (FsmTransition) null : EditorCommands.AddGlobalTransition(FsmEditor.SelectedState, (FsmEvent) null);

    public static FsmTransition AddGlobalTransition(FsmState state, FsmEvent fsmEvent)
    {
      if (!FsmEditor.DisconnectCheck())
        return (FsmTransition) null;
      FsmEditor.RecordUndo(Strings.Command_Add_Global_Transition);
      FsmTransition fsmTransition = state.AddGlobalTransition(fsmEvent);
      FsmEditor.GraphView.UpdateGraphBounds(true);
      FsmEditor.UpdateFsmInfo(state.Fsm);
      FsmEditor.SetFsmDirty(state.Fsm, true);
      return fsmTransition;
    }

    public static void DeleteGlobalTransition(FsmTransition transition)
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Command_Delete_Global_Transition);
      FsmEditor.SelectedFsm.DeleteGlobalTransition(transition);
      FsmEditor.UpdateFsmInfo();
      FsmEditor.GraphView.UpdateStateSize(transition.ToState);
      FsmEditor.GraphView.UpdateGraphBounds(true);
      FsmEditor.SetFsmDirty(true);
      Keyboard.ResetFocus();
    }

    public static void SetTransitionNewEvent(FsmTransition transition, string newEventName)
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      FsmEvent fsmEvent = FsmEvent.GetFsmEvent(newEventName);
      FsmEditor.SelectedFsm.AddEvent(fsmEvent);
      FsmEditor.EventsManager.Reset();
      EditorCommands.SetTransitionEvent(transition, fsmEvent);
    }

    public static void SetTransitionEvent(FsmTransition transition, FsmEvent fsmEvent)
    {
      if (transition == null || !FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Command_Set_Transition_Event);
      transition.SetEvent(fsmEvent);
      FsmEditor.GraphView.UpdateStateSize(FsmEditor.SelectedState);
      FsmEditor.UpdateFsmInfo();
      FsmEditor.SetFsmDirty(true);
    }

    public static void SetTransitionTarget(FsmTransition transition, string toState)
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Command_Set_Transition_Target);
      FsmEditor.SelectedFsm.SetTransitionTargetState(transition, toState);
      FsmEditor.UpdateFsmInfo();
      FsmEditor.SetFsmDirty(true);
    }

    public static void DeleteSelectedTransition()
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      EditorCommands.DeleteTransition(FsmEditor.SelectedState, FsmEditor.SelectedTransition);
    }

    public static void DeleteTransition(FsmState state, FsmTransition transition)
    {
      if (!FsmEditor.DisconnectCheck() || state == null)
        return;
      FsmEditor.RecordUndo(Strings.Menu_Delete_Transition);
      FsmEditor.SelectedState.DeleteTransition(transition);
      FsmEditor.GraphView.UpdateStateSize(state);
      FsmEditor.UpdateFsmInfo(state.Fsm);
      FsmEditor.SetFsmDirty(state.Fsm, true);
    }

    public static void DeleteSelectedState()
    {
      if (!Dialogs.AreYouSure(Strings.Command_Delete_State) || !FsmEditor.DisconnectCheck())
        return;
      FsmState selectedState = FsmEditor.SelectedState;
      FsmEditor.Selection.RemoveState(selectedState);
      FsmEditor.RecordUndo(Strings.Command_Delete_State);
      FsmEditor.SelectedFsm.DeleteState(selectedState);
      FsmEditor.GraphView.UpdateGraphSize();
      FsmEditor.UpdateActionUsage();
      FsmEditor.UpdateFsmInfo();
      FsmEditor.SetFsmDirty(true);
      FsmEditor.Selection.SelectState((FsmState) null);
    }

    public static void DeleteMultiSelection()
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Menu_GraphView_Delete_States);
      FsmEditor.SelectedFsm.DeleteStates(FsmEditor.SelectedStates);
      FsmEditor.Selection.DeselectAll();
      FsmEditor.GraphView.UpdateGraphSize();
      FsmEditor.UpdateActionUsage();
      FsmEditor.UpdateFsmInfo();
      FsmEditor.SetFsmDirty(true);
    }

    public static void SetSelectedStateAsStartState()
    {
      if (!FsmEditor.DisconnectCheck() || FsmEditor.SelectedState == null)
        return;
      FsmEditor.RecordUndo(Strings.Command_Set_Start_State);
      FsmEditor.SelectedFsm.SetStartState(FsmEditor.SelectedState.Name);
      FsmEditor.SetFsmDirty(true);
    }
    
    public static void SetSelectedStateAsPrepareState()
    {
      if (!FsmEditor.DisconnectCheck() || FsmEditor.SelectedState == null)
        return;
      FsmEditor.RecordUndo("Set Prepare State");
      FsmEditor.SelectedFsm.SetPrepareState(FsmEditor.SelectedState.Name);
      FsmEditor.SetFsmDirty(true);
    }
    
    public static void SetSelectedStateAsCleanupState()
    {
      if (!FsmEditor.DisconnectCheck() || FsmEditor.SelectedState == null)
        return;
      
      FsmEditor.RecordUndo("Set Cleanup State");
      FsmEditor.SelectedFsm.SetCleanupState(FsmEditor.SelectedState.Name);
      FsmEditor.SetFsmDirty(true);
    }

    public static void SetStateColorIndex(object userdata)
    {
      if (FsmEditor.SelectedState == null)
        return;
      FsmEditor.RecordUndo(Strings.Command_Set_State_Color);
      FsmEditor.SelectedState.SetColorIndex((int) userdata);
      FsmEditor.SetFsmDirty(false);
    }

    public static void SetSelectedStatesColorIndex(object userdata)
    {
      FsmEditor.RecordUndo(Strings.Command_Set_Selected_States_Color);
      FsmEditor.SelectedFsm.SetStatesColorIndex((IEnumerable<FsmState>) FsmEditor.SelectedStates, (int) userdata);
      FsmEditor.SetFsmDirty(false);
    }

    public static void SelectTransition(object userdata)
    {
      if (!(userdata is FsmTransition transition))
        return;
      FsmEditor.Selection.SelectTransition(transition);
      FsmEditor.RepaintAll();
    }

    public static void SetTransitionColorIndex(object userdata)
    {
      FsmEditor.RecordUndo(Strings.Command_Set_Transition_Color);
      FsmEditor.SelectedTransition.SetColorIndex((int) userdata);
      FsmEditor.SetFsmDirty(false);
    }

    public static void ToggleBreakpointOnSelectedState()
    {
      if (FsmEditor.SelectedState == null)
        return;
      EditorCommands.ToggleBreakpoint(FsmEditor.SelectedState);
    }

    public static void SetTransitionLinkStyle(object userdata) => FsmGraphView.SetLinkStyle(FsmEditor.SelectedTransition, (FsmTransition.CustomLinkStyle) userdata);

    public static void SetTransitionLinkConstraint(object userdata) => FsmGraphView.SetLinkConstraint(FsmEditor.SelectedTransition, (FsmTransition.CustomLinkConstraint) userdata);

    public static void SetTransitionLinkTarget(object userdata) => FsmGraphView.SetLinkTarget(FsmEditor.SelectedTransition, (FsmTransition.CustomLinkTarget) userdata);

    public static void MoveTransitionUp(object userdata)
    {
      if (!(userdata is FsmTransition transition) || !FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Menu_Move_Transition_Up);
      FsmEditor.SelectedState.MoveTransitionUp(transition);
      FsmEditor.SetFsmDirty(true);
    }

    public static void MoveTransitionDown(object userdata)
    {
      if (!(userdata is FsmTransition transition) || !FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Menu_Move_Transition_Down);
      FsmEditor.SelectedState.MoveTransitionDown(transition);
      FsmEditor.SetFsmDirty(true);
    }

    public static void AddExposedEvent(Fsm fsm, FsmEvent fsmEvent)
    {
      FsmEditor.RecordUndo("Set Inspector Flag");
      fsm.AddExposedEvent(fsmEvent);
      FsmEditor.SetFsmDirty(false);
    }

    public static void RemoveExposedEvent(Fsm fsm, FsmEvent fsmEvent)
    {
      FsmEditor.RecordUndo("Set Inspector Flag");
      fsm.RemoveExposedEvent(fsmEvent);
      FsmEditor.SetFsmDirty(false);
    }

    public static void AddOutputEvent(Fsm fsm, FsmEvent fsmEvent)
    {
      if (fsm == null || fsmEvent == null)
        return;
      FsmEditor.RecordUndo("Event Is Output Setting");
      fsm.OutputEvents.Add(fsmEvent);
      FsmEditor.SetFsmDirty(true);
    }

    public static void RemoveOutputEvent(Fsm fsm, FsmEvent fsmEvent)
    {
      if (fsm == null || fsmEvent == null)
        return;
      FsmEditor.RecordUndo("Event Is Output Setting");
      fsm.OutputEvents.RemoveAll((Predicate<FsmEvent>) (e => e.Name == fsmEvent.Name));
      FsmEditor.SetFsmDirty(true);
    }

    public static FsmEvent AddEvent(string eventName)
    {
      if (!FsmEditor.DisconnectCheck())
        return (FsmEvent) null;
      FsmEditor.RecordUndo(Strings.Command_Add_Event);
      FsmEvent fsmEvent = FsmEditor.SelectedFsm.AddEvent(eventName);
      FsmEditor.SetFsmDirty(true);
      GlobalEventsWindow.ResetView();
      return fsmEvent;
    }

    public static void OpenWikiHelp() => EditorCommands.OpenWikiPage(WikiPages.Home);

    [Localizable(false)]
    public static void SearchWikiHelp(FsmStateAction action) => Application.OpenURL("https://alidocs.dingtalk.com/i/nodes/YMyQA2dXW79rjRqBCwy6k1zkJzlwrZgb#" + Labels.NicifyVariableName(Labels.StripNamespace(action.ToString())));

    [Localizable(false)]
    public static void SearchWikiHelp(string topic) => Application.OpenURL("https://alidocs.dingtalk.com/i/nodes/YMyQA2dXW79rjRqBCwy6k1zkJzlwrZgb#" + topic);

    public static void OpenWikiPage(FsmStateAction action)
    {
      HelpUrlAttribute attribute = CustomAttributeHelpers.GetAttribute<HelpUrlAttribute>(action.GetType());
      if (attribute != null)
      {
        Application.OpenURL(attribute.Url);
      }
      else
      {
        if (EditorCommands.OpenWikiPage(Labels.StripNamespace(action.ToString())))
          return;
        EditorCommands.SearchWikiHelp(action);
      }
    }

    public static bool OpenWikiPage(string topic)
    {
      int wikiPageNumber = DocHelpers.GetWikiPageNumber(topic);
      if (wikiPageNumber <= 0)
        return false;
      EditorCommands.OpenWikiPage(wikiPageNumber);
      return true;
    }

    [Obsolete("Use DocHelpers.GetWikiPageNumber instead.")]
    public static int GetWikiPageNumber(string topic) => DocHelpers.GetWikiPageNumber(topic);

    [Localizable(false)]
    public static void OpenWikiPage(int page) => Application.OpenURL("https://hutonggames.fogbugz.com/default.asp?W" + (object) page);

    public static void OpenWikiPage(WikiPages page) => EditorCommands.OpenWikiPage((int) page);

    public static void OpenProductWebPage() => Application.OpenURL("http://hutonggames.com/");

    public static void OpenOnlineStore() => Application.OpenURL("http://www.hutonggames.com/store.html");

    public static void OpenAssetStorePage() => AssetStore.Open("http://u3d.as/content/hutong-games-llc/playmaker/1Az");

    public static void RemoveFsmComponent()
    {
      if ((UnityEngine.Object) FsmEditor.SelectedFsmComponent == (UnityEngine.Object) null)
        return;
      PlayMakerFSM selectedFsmComponent = FsmEditor.SelectedFsmComponent;
      FsmEditor.RemoveFromList(selectedFsmComponent);
      FsmEditor.SelectNone();
      Undo.SetCurrentGroupName("Remove FSM Component");
      Undo.DestroyObjectImmediate((UnityEngine.Object) selectedFsmComponent);
    }

    public static void DeleteTemplate()
    {
      if (!((UnityEngine.Object) FsmEditor.SelectedTemplate != (UnityEngine.Object) null) || !Dialogs.YesNoDialog(Strings.Command_DeleteTemplate))
        return;
      AssetDatabase.DeleteAsset(AssetDatabase.GetAssetPath((UnityEngine.Object) FsmEditor.SelectedTemplate));
      Templates.InitList();
    }

    public static void ToggleIsSequence(object userData) => EditorCommands.ToggleIsSequence(userData as FsmState);

    public static void ToggleIsSequence(FsmState state)
    {
      if (state == null || !FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo("Toggle Sequence");
      state.IsSequence = !state.IsSequence;
    }

    public static void ToggleBreakpoint(FsmState state)
    {
      if (state == null)
        return;
      FsmEditor.RecordUndo(Strings.Menu_GraphView_Toggle_Breakpoint);
      state.IsBreakpoint = !state.IsBreakpoint;
    }

    public static void ClearBreakpoints()
    {
      FsmEditor.RecordUndo(Strings.Command_Clear_Breakpoints);
      foreach (FsmState state in FsmEditor.SelectedFsm.States)
        state.IsBreakpoint = false;
    }

    public static void ToggleAutoRefreshFsmInfo()
    {
      FsmEditorSettings.AutoRefreshFsmInfo = !FsmEditorSettings.AutoRefreshFsmInfo;
      FsmEditorSettings.SaveSettings();
    }

    public static void ToggleSelectNewVariables()
    {
      FsmEditorSettings.SelectNewVariables = !FsmEditorSettings.SelectNewVariables;
      FsmEditorSettings.SaveSettings();
    }

    public static void ToggleDebugVariables()
    {
      FsmEditorSettings.DebugVariables = !FsmEditorSettings.DebugVariables;
      FsmEditorSettings.SaveSettings();
    }

    public static void ChooseWatermark() => FsmEditor.Inspector.SetMode(InspectorMode.Watermarks);

    public static void SaveScreenshot() => FsmEditor.GraphView.TakeScreenshot();

    public static void ToggleShowHints()
    {
      FsmEditorSettings.ShowHints = !FsmEditorSettings.ShowHints;
      FsmEditorSettings.SaveSettings();
      FsmEditor.RepaintAll();
    }

    public static void UpdateGraphView()
    {
      FsmEditor.GraphView.UpdateStateSizes(FsmEditor.SelectedFsm);
      FsmEditor.GraphView.UpdateGraphBounds();
    }

    public static void AutoNameAction(FsmStateAction action)
    {
      if (action == null || !FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo("Auto Name Action");
      action.IsAutoNamed = !action.IsAutoNamed;
      action.Name = action.IsAutoNamed ? action.AutoName() : (string) null;
      FsmEditor.SaveActions();
      FsmEditor.SetFsmDirty();
    }

    public static void OpenAction(FsmStateAction action, bool openState)
    {
      if (action == null)
        return;
      action.IsOpen = openState;
      if (Application.isPlaying)
        return;
      FsmEditor.SaveActions(action.State, false, false);
    }

    public static void OpenAllActions(FsmState state, bool openState)
    {
      foreach (FsmStateAction action in state.Actions)
        action.IsOpen = openState;
      if (Application.isPlaying)
        return;
      FsmEditor.SaveActions(state, false);
    }

    public static void EnableAction(FsmStateAction action, bool enabledState)
    {
      if (action == null)
        return;
      FsmEditor.RecordUndo(Strings.Command_Enable_Action);
      action.Enabled = enabledState;
      FsmEditor.SaveActions(action.State);
    }

    public static void EnableAllActions(FsmState state, bool enabledState)
    {
      FsmEditor.RecordUndo(Strings.Command_Enable_All_Actions);
      foreach (FsmStateAction action in state.Actions)
        action.Enabled = enabledState;
      FsmEditor.SaveActions(state);
    }

    public static void ResetAction(FsmStateAction action)
    {
      if (action == null || !FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Command_Reset_Action);
      Keyboard.ResetFocus();
      action.BaseReset();
      action.Reset();
      FsmEditor.SaveActions(action.State);
      Actions.UpdateTooltip(action);
    }

    public static void ResetActionName(FsmStateAction action)
    {
      if (action == null || !FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Command_Reset_Action_Name);
      action.Name = action.IsAutoNamed ? action.AutoName() : (string) null;
      FsmEditor.SaveActions(action.State, false);
      Actions.UpdateTooltip(action);
    }

    public static void RenameAction(FsmStateAction action, string newName)
    {
      if (action == null || !FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Command_Rename_Action);
      action.Name = newName;
      FsmEditor.SaveActions(action.State, false);
      Actions.UpdateTooltip(action);
    }

    public static void MoveActionUp(FsmState state, FsmStateAction action)
    {
      if (state == null || action == null || !FsmEditor.DisconnectCheck())
        return;
      List<FsmStateAction> fsmStateActionList = new List<FsmStateAction>((IEnumerable<FsmStateAction>) state.Actions);
      int num = fsmStateActionList.IndexOf(action);
      if (num == 0)
        return;
      FsmEditor.RecordUndo(Strings.Command_Move_Action_Up);
      fsmStateActionList.Remove(action);
      fsmStateActionList.Insert(num - 1, action);
      state.Actions = fsmStateActionList.ToArray();
      FsmEditor.SaveActions(action.State, false);
    }

    public static void MoveActionBefore(
      FsmState state,
      FsmStateAction action,
      FsmStateAction beforeAction)
    {
      if (state == null || action == null || (beforeAction == null || !FsmEditor.DisconnectCheck()))
        return;
      List<FsmStateAction> fsmStateActionList = new List<FsmStateAction>((IEnumerable<FsmStateAction>) state.Actions);
      fsmStateActionList.Remove(action);
      int index = Mathf.Clamp(fsmStateActionList.IndexOf(beforeAction), 0, fsmStateActionList.Count - 1);
      fsmStateActionList.Insert(index, action);
      state.Actions = fsmStateActionList.ToArray();
      FsmEditor.SaveActions(action.State, false);
    }

    public static void MoveActionAfter(
      FsmState state,
      FsmStateAction action,
      FsmStateAction afterAction)
    {
      if (state == null || action == null || (afterAction == null || !FsmEditor.DisconnectCheck()))
        return;
      List<FsmStateAction> fsmStateActionList = new List<FsmStateAction>((IEnumerable<FsmStateAction>) state.Actions);
      fsmStateActionList.Remove(action);
      int index = Mathf.Clamp(fsmStateActionList.IndexOf(afterAction) + 1, 0, fsmStateActionList.Count);
      fsmStateActionList.Insert(index, action);
      state.Actions = fsmStateActionList.ToArray();
      FsmEditor.SaveActions(action.State, false);
    }

    public static void MoveActionDown(FsmState state, FsmStateAction action)
    {
      if (state == null || action == null || !FsmEditor.DisconnectCheck())
        return;
      List<FsmStateAction> fsmStateActionList = new List<FsmStateAction>((IEnumerable<FsmStateAction>) state.Actions);
      int num = fsmStateActionList.IndexOf(action);
      if (num == state.Actions.Length - 1)
        return;
      FsmEditor.RecordUndo(Strings.Command_Move_Action_Down);
      fsmStateActionList.Remove(action);
      fsmStateActionList.Insert(num + 1, action);
      state.Actions = fsmStateActionList.ToArray();
      FsmEditor.SaveActions(action.State, false);
    }

    public static void MoveActionToTop(FsmState state, FsmStateAction action)
    {
      if (state == null || action == null || !FsmEditor.DisconnectCheck())
        return;
      List<FsmStateAction> fsmStateActionList = new List<FsmStateAction>((IEnumerable<FsmStateAction>) state.Actions);
      if (fsmStateActionList.IndexOf(action) == 0)
        return;
      FsmEditor.RecordUndo(Strings.Menu_Move_Action_To_Top);
      fsmStateActionList.Remove(action);
      fsmStateActionList.Insert(0, action);
      state.Actions = fsmStateActionList.ToArray();
      FsmEditor.SaveActions(action.State, false);
    }

    public static void MoveActionToBottom(FsmState state, FsmStateAction action)
    {
      if (state == null || action == null || !FsmEditor.DisconnectCheck())
        return;
      List<FsmStateAction> fsmStateActionList = new List<FsmStateAction>((IEnumerable<FsmStateAction>) state.Actions);
      if (fsmStateActionList.IndexOf(action) == state.Actions.Length - 1)
        return;
      FsmEditor.RecordUndo(Strings.Menu_Move_Action_To_Bottom);
      fsmStateActionList.Remove(action);
      fsmStateActionList.Add(action);
      state.Actions = fsmStateActionList.ToArray();
      FsmEditor.SaveActions(action.State, false);
    }

    public static void DeleteEvent(FsmEvent fsmEvent)
    {
      if (fsmEvent == null || !FsmEditor.DisconnectCheck())
        return;
      FsmEditor.RecordUndo(Strings.Command_Delete_Event);
      FsmEditor.SelectedFsm.DeleteEvent(fsmEvent);
      FsmEditor.GraphView.UpdateStateSizes(FsmEditor.SelectedFsm);
      FsmEditor.SaveActions(FsmEditor.SelectedFsm);
      GlobalEventsWindow.UpdateView(fsmEvent.Name);
      Keyboard.ResetFocus();
    }

    public static void DeleteEventFromAll(FsmEvent fsmEvent)
    {
      if (!FsmEditor.DisconnectCheck())
        return;
      if (FsmEditorSettings.LoadAllPrefabs)
        Files.LoadAllPlaymakerPrefabs();
      foreach (Fsm fsm in FsmEditor.FsmList)
      {
        if (FsmPrefabs.ShouldModify(fsm))
        {
          Undo.RecordObject(fsm.OwnerObject, Strings.Command_Delete_Event_From_All_FSMs);
          fsm.DeleteEvent(fsmEvent);
          FsmEditor.SaveActions(fsm);
        }
      }
      FsmEditor.RepaintAll();
    }

    public static NamedVariable AddVariable(VariableType type, string name)
    {
      FsmEditor.RecordUndo(Strings.Command_Add_Variable);
      NamedVariable namedVariable = FsmEditor.SelectedFsm.AddVariable(type, name);
      FsmEditor.SetFsmDirty(true);
      return namedVariable;
    }

    [Obsolete("Use FsmVariable.DeleteVariable or Fsm.DeleteVariable extension methods instead.")]
    public static void DeleteVariable(FsmVariable variable) => EditorCommands.DeleteVariable(variable.FsmVariables, variable);

    [Obsolete("Use FsmVariable.DeleteVariable or Fsm.DeleteVariable extension methods instead.")]
    public static void DeleteVariable(FsmVariables fsmVariables, FsmVariable variable)
    {
      FsmBuilder.RemoveVariableUsage(variable.NamedVar);
      FsmVariable.DeleteVariable(fsmVariables, variable);
    }

    [Obsolete("Use Fsm.DeleteVariable extension method instead.")]
    public static void DeleteVariable(
      Fsm fsm,
      FsmVariable fsmVariable,
      bool undo = true,
      bool checkDialog = true)
    {
      if (fsm == null || fsmVariable == null || checkDialog && !EditorCommands.CheckDeleteVariable(fsm, Strings.Dialog_Delete_Variable, Strings.Dialog_Delete_Variable_Are_you_sure, fsmVariable))
        return;
      if (undo)
        FsmEditor.RecordUndo(fsm, Strings.Dialog_Delete_Variable);
      fsm.SetVariableIsOutput(fsmVariable.NamedVar, false);
      EditorCommands.DeleteVariable(fsm.Variables, fsmVariable);
      FsmEditor.SetFsmDirty(fsm, true);
    }

    public static void DeleteGlobalVariable(FsmVariable fsmVariable, bool undo = true, bool checkDialog = true)
    {
      if (checkDialog && !EditorCommands.CheckDeleteGlobalVariable(Strings.Dialog_Delete_Variable))
        return;
      if (undo)
        FsmEditor.RecordGlobalsUndo(Strings.Dialog_Delete_Variable);
      FsmBuilder.RemoveGlobalVariableUsage(fsmVariable.NamedVar);
    }

    private static bool CheckDeleteVariable(
      Fsm fsm,
      string title,
      string warning,
      FsmVariable fsmVariable)
    {
      return fsm == null || FsmSearch.GetUnusedVariables(fsm).Contains(fsmVariable) || Dialogs.YesNoDialog(title, warning);
    }

    private static bool CheckDeleteGlobalVariable(string title) => Dialogs.YesNoDialog(title, Strings.Label_Check_Edit_Global_Variable);

    public static void ChangeVariableType(
      FsmVariable fsmVariable,
      VariableType newType,
      System.Type objectType)
    {
      if (FsmEditor.SelectedFsm == null)
        return;
      if (fsmVariable.IsTemp)
      {
        fsmVariable.Type = newType;
        fsmVariable.ObjectType = objectType;
      }
      else
      {
        if (!FsmEditor.DisconnectCheck() || !EditorCommands.CheckDeleteVariable(FsmEditor.SelectedFsm, Strings.Dialog_Edit_Variable_Type, Strings.Dialog_Edit_Variable_Type_Are_you_sure, fsmVariable))
          return;
        FsmEditor.RecordUndo(Strings.Label_Edit_Variable);
        string name = fsmVariable.Name;
        string tooltip = fsmVariable.Tooltip;
        bool showInInspector = fsmVariable.ShowInInspector;
        FsmEditor.SelectedFsm.DeleteVariable(fsmVariable.NamedVar);
        NamedVariable variable = FsmEditor.SelectedFsm.AddVariable(newType, name);
        variable.ObjectType = objectType;
        variable.Tooltip = tooltip;
        variable.ShowInInspector = showInInspector;
        fsmVariable.SetVariable(variable);
      }
    }

    public static void ChangeArrayType(
      FsmVariable fsmVariable,
      VariableType newType,
      System.Type objectType)
    {
      if (!(fsmVariable.NamedVar is FsmArray namedVar))
        Debug.LogWarning((object) ("Variable is not an array: " + fsmVariable.Name));
      else if (fsmVariable.IsTemp)
      {
        namedVar.SetType(newType);
        namedVar.ObjectType = objectType;
        fsmVariable.SetVariable((NamedVariable) namedVar);
      }
      else
      {
        if (!FsmEditor.DisconnectCheck() || !EditorCommands.CheckDeleteVariable(FsmEditor.SelectedFsm, Strings.Dialog_Edit_Variable_Type, Strings.Dialog_Edit_Variable_Type_Are_you_sure, fsmVariable))
          return;
        FsmEditor.RecordUndo(Strings.Label_Edit_Variable);
        if (fsmVariable.Fsm != null)
          FsmBuilder.RemoveVariableUsage(fsmVariable.NamedVar);
        else
          FsmBuilder.RemoveGlobalVariableUsage(fsmVariable.NamedVar);
        namedVar.SetType(newType);
        namedVar.ObjectType = objectType;
        fsmVariable.SetVariable((NamedVariable) namedVar);
      }
    }

    public static void ChangeVariableObjectType(
      FsmVariable fsmVariable,
      VariableType type,
      System.Type objectType)
    {
      if (fsmVariable.Type != type)
        Debug.LogWarning((object) ("Variable is not of type: " + (object) type));
      else if (fsmVariable.IsTemp)
      {
        fsmVariable.NamedVar.ObjectType = objectType;
        fsmVariable.UpdateVariable();
      }
      else
      {
        if (!FsmEditor.DisconnectCheck() || !EditorCommands.CheckDeleteVariable(FsmEditor.SelectedFsm, Strings.Dialog_Edit_Variable_Type, Strings.Dialog_Edit_Variable_Type_Are_you_sure, fsmVariable))
          return;
        FsmEditor.RecordUndo(Strings.Label_Edit_Variable);
        if (fsmVariable.Fsm != null)
          FsmBuilder.RemoveVariableUsage(fsmVariable.NamedVar);
        else
          FsmBuilder.RemoveGlobalVariableUsage(fsmVariable.NamedVar);
        fsmVariable.NamedVar.ObjectType = objectType;
        fsmVariable.UpdateVariable();
      }
    }

    public static UnityEngine.Component AddComponent(GameObject go, System.Type componentType) => (UnityEngine.Object) go == (UnityEngine.Object) null || componentType == null ? (UnityEngine.Component) null : Undo.AddComponent(go, componentType);
  }
}
