// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.PackageExporter
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public static class PackageExporter
  {
    private static string playmakerDir;
    private static string runtimeVersion;
    private static string editorVersion;
    private static string runtimeVersionWebGL;
    private static string runtimeVersionWSA;
    private static string runtimeVersionWP8;

    public static void Export(string packageName)
    {
      if (!PackageExporter.VerifyProject())
        return;
      string unitypackageFileName = PackageExporter.GetUnitypackageFileName(packageName);
      AssetDatabase.ExportPackage("Assets/PlayMaker", unitypackageFileName, ExportPackageOptions.Recurse);
      UnityEngine.Debug.Log((object) ("Exported Package: " + unitypackageFileName));
    }

    public static void ExportPlugin(string packageName)
    {
      if (!PackageExporter.VerifyProject())
        return;
      string unitypackageFileName = PackageExporter.GetUnitypackageFileName(packageName);
      AssetDatabase.ExportPackage(new string[2]
      {
        "Assets/ThirdPlugins/PlayMaker",
        "Assets/Plugins"
      }, unitypackageFileName, ExportPackageOptions.Recurse);
      UnityEngine.Debug.Log((object) ("Exported Package: " + unitypackageFileName));
    }

    public static void ExportFullProduct(string packageName)
    {
      string[] assetPathNames = new string[5]
      {
        "Assets/Gizmos",
        "Assets/iTween",
        "Assets/Photon Unity Networking",
        "Assets/ThirdPlugins/PlayMaker",
        "Assets/Plugins"
      };
      if (PackageExporter.VerifyProject())
      {
        string unitypackageFileName = PackageExporter.GetUnitypackageFileName(packageName);
        AssetDatabase.ExportPackage(assetPathNames, unitypackageFileName, ExportPackageOptions.Recurse);
        UnityEngine.Debug.Log((object) ("Exported Package: " + unitypackageFileName));
        if (!(packageName == "Playmaker") && !(packageName == "PlaymakerStudent"))
          return;
        PackageExporter.CopyBuildToFinalInstall(packageName);
      }
      else
        UnityEngine.Debug.LogError((object) "Failed to export package!");
    }

    public static string GetPlaymakerRootDir()
    {
      string dataPath = Application.dataPath;
      return dataPath.Substring(0, dataPath.LastIndexOf("Projects", StringComparison.Ordinal));
    }

    private static string GetUnitypackageFileName(string packageName)
    {
      PackageExporter.playmakerDir = PackageExporter.GetPlaymakerRootDir();
      string path = Path.Combine(PackageExporter.playmakerDir, "Builds/Unity" + PackageExporter.GetUnityVersion());
      Directory.CreateDirectory(path);
      return string.Format("{0}/{1}.{2}.unitypackage", (object) path, (object) packageName, (object) PackageExporter.runtimeVersion);
    }

    private static string GetUnityVersion()
    {
      string[] strArray = Application.unityVersion.Split('.');
      return strArray[0] + "." + strArray[1];
    }

    private static void CopyBuildToFinalInstall(string packageName)
    {
      string unitypackageFileName = PackageExporter.GetUnitypackageFileName(packageName);
      string str1 = Application.dataPath + "/../../" + packageName + ".final.unity";
      packageName = packageName + "." + PackageExporter.GetShortVersionInfo() + ".unitypackage";
      string str2 = packageName;
      string path = str1 + "/Assets/ThirdPlugins/PlayMaker/Editor/Install/" + str2;
      FileUtil.DeleteFileOrDirectory(path);
      string dest = path;
      FileUtil.CopyFileOrDirectory(unitypackageFileName, dest);
    }

    private static bool VerifyProject()
    {
      PackageExporter.FixLocalizedResources();
      return PackageExporter.CheckVersionInfo() && PackageExporter.VerifyNoPlayMakerPrefs() && PackageExporter.VerifyNoPlayMakerGlobals();
    }

    private static bool CheckVersionInfo()
    {
      PackageExporter.UpdateVersionInfo();
      if ((!(PackageExporter.runtimeVersion == PackageExporter.editorVersion) ? 0 : (PackageExporter.runtimeVersion == PackageExporter.runtimeVersionWebGL ? 1 : 0)) == 0)
        throw new InvalidDataException("DLL VersionInfo mismatch:\n" + PackageExporter.GetVersionInfo());
      return true;
    }

    private static void UpdateVersionInfo()
    {
      PackageExporter.runtimeVersion = FileVersionInfo.GetVersionInfo("Assets/Plugins/PlayMaker/PlayMaker.dll").ProductVersion;
      PackageExporter.runtimeVersionWebGL = FileVersionInfo.GetVersionInfo("Assets/Plugins/PlayMaker/WebGL/PlayMaker.dll").ProductVersion;
      PackageExporter.editorVersion = FileVersionInfo.GetVersionInfo("Assets/PlayMaker/Editor/PlayMakerEditor.dll").ProductVersion;
      UnityEngine.Debug.Log((object) PackageExporter.GetVersionInfo());
    }

    private static string GetVersionInfo() => "RuntimeVersion: " + PackageExporter.runtimeVersion + "\nRuntimeVersionWSA: " + PackageExporter.runtimeVersionWSA + "\nRuntimeVersionWP8: " + PackageExporter.runtimeVersionWP8 + "\nRuntimeVersionWebGL: " + PackageExporter.runtimeVersionWebGL + "\nEditorVersion: " + PackageExporter.editorVersion;

    private static string GetShortVersionInfo()
    {
      string[] strArray = PackageExporter.runtimeVersion.Split('.');
      return strArray[0] + "." + strArray[1] + "." + strArray[2];
    }

    private static bool VerifyNoPlayMakerGlobals()
    {
      PlayMakerGlobals[] objectsOfTypeAll = Resources.FindObjectsOfTypeAll<PlayMakerGlobals>();
      if (objectsOfTypeAll.Length != 0)
      {
        UnityEngine.Debug.LogError((object) "Project has PlayMakerGlobals which will overwrite user globals!", (UnityEngine.Object) objectsOfTypeAll[0]);
        UnityEngine.Debug.Log((object) AssetDatabase.GetAssetPath((UnityEngine.Object) objectsOfTypeAll[0]), (UnityEngine.Object) objectsOfTypeAll[0]);
      }
      return objectsOfTypeAll.Length == 0;
    }

    private static bool VerifyNoPlayMakerPrefs()
    {
      UnityEngine.Object @object = Resources.Load("PlayMakerPrefs");
      if (@object != (UnityEngine.Object) null)
      {
        UnityEngine.Debug.LogError((object) "Project has PlayMakerPrefs which will overwrite user preferences!", @object);
        UnityEngine.Debug.Log((object) AssetDatabase.GetAssetPath(@object), @object);
      }
      return @object == (UnityEngine.Object) null;
    }

    private static void FixLocalizedResources()
    {
      UnityEngine.Debug.Log((object) "Fix Localized Resources");
      foreach (string file in Directory.GetFiles(Application.dataPath, "PlayMakerEditorResources.resources.dll", SearchOption.AllDirectories))
      {
        PluginImporter pluginImporter = PackageExporter.GetPluginImporter(file.Substring(Application.dataPath.Length - 6));
        pluginImporter.SetCompatibleWithEditor(false);
        pluginImporter.SaveAndReimport();
      }
    }

    private static PluginImporter GetPluginImporter(string pluginPath)
    {
      PluginImporter atPath = (PluginImporter) AssetImporter.GetAtPath(pluginPath);
      if ((UnityEngine.Object) atPath != (UnityEngine.Object) null)
        return atPath;
      UnityEngine.Debug.LogWarning((object) ("Couldn't find plugin: " + pluginPath));
      return (PluginImporter) null;
    }
  }
}
