// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmBuilder
// Assembly: PlayMakerEditor, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using HutongGames.Utility;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class FsmBuilder
  {
    [Obsolete("Use FsmClipboard")]
    private static FsmTemplate clipboard;

    public Fsm targetFsm { get; private set; }

    public FsmBuilder()
    {
    }

    public FsmBuilder(Fsm target) => this.SetTarget(target);

    public void SetTarget(Fsm target) => this.targetFsm = target;

    public static void RemoveGlobalVariableUsage(NamedVariable variable)
    {
      FsmSearch.UpdateAll();
      FsmBuilder.RemoveVariableUsage(FsmSearch.GetGlobalVariablesUsageList(variable), variable);
    }

    public static void RemoveVariableUsage(Fsm fsm, NamedVariable variable) => FsmBuilder.RemoveVariableUsage(FsmSearch.GetVariableUsageList(fsm, variable), variable);

    public static void RemoveVariableUsage(NamedVariable variable) => FsmBuilder.RemoveVariableUsage(FsmSearch.GetVariableUsageList(FsmEditor.SelectedFsm, variable), variable);

    public static void RemoveVariableUsage(List<FsmInfo> fsmInfoList, NamedVariable variable)
    {
      foreach (FsmInfo fsmInfo in fsmInfoList)
        FsmBuilder.RemoveVariableUsageInField(fsmInfo.fieldInObject, fsmInfo.field, variable);
      foreach (Fsm fsm in FsmInfo.GetFsmList(fsmInfoList))
        FsmEditor.SaveActions(fsm);
    }

    public static void RemoveVariableUsageInObject(object obj, NamedVariable variable)
    {
      if (obj == null)
        return;
      foreach (FieldInfo serializedField in TypeHelpers.GetSerializedFields(obj.GetType()))
        FsmBuilder.RemoveVariableUsageInField(obj, serializedField, variable);
    }

    public static void RemoveVariableUsageInField(
      object obj,
      FieldInfo field,
      NamedVariable variable)
    {
      object obj1 = field.GetValue(obj);
      if (obj1 == null)
        return;
      System.Type fieldType = field.FieldType;
      if (fieldType.IsSubclassOf(typeof (NamedVariable)))
      {
        NamedVariable namedVariable = (NamedVariable) obj1;
        if (!namedVariable.UseVariable || !(namedVariable.Name == variable.Name))
          return;
        NamedVariable instance = (NamedVariable) Activator.CreateInstance(fieldType);
        instance.UseVariable = true;
        field.SetValue(obj, (object) instance);
      }
      else if (fieldType == typeof (FsmVar))
      {
        FsmVar fsmVar = (FsmVar) obj1;
        if (!(fsmVar.variableName == variable.Name))
          return;
        fsmVar.NamedVar = fsmVar.NamedVar.GetNewVariableOfSameType();
      }
      else if (fieldType.IsArray)
      {
        Array array = (Array) obj1;
        System.Type elementType = fieldType.GetElementType();
        for (int elementIndex = 0; elementIndex < array.Length; ++elementIndex)
          FsmBuilder.RemoveVariableUsageInArray(array, elementType, elementIndex, variable);
      }
      else
      {
        if (!fieldType.IsClass)
          return;
        FsmBuilder.RemoveVariableUsageInObject(obj1, variable);
      }
    }

    public static void RemoveVariableUsageInArray(
      Array array,
      System.Type type,
      int elementIndex,
      NamedVariable variable)
    {
      object obj = array.GetValue(elementIndex);
      if (obj == null)
        return;
      if (type.IsSubclassOf(typeof (NamedVariable)))
      {
        NamedVariable namedVariable = (NamedVariable) obj;
        if (!namedVariable.UseVariable || namedVariable != variable)
          return;
        array.SetValue((object) null, elementIndex);
      }
      else if (type == typeof (FsmVar))
      {
        if (!(((FsmVar) obj).variableName == variable.Name))
          return;
        array.SetValue((object) null, elementIndex);
      }
      else
      {
        if (!type.IsClass)
          return;
        FsmBuilder.RemoveVariableUsageInObject(obj, variable);
      }
    }

    [Obsolete("Use Fsm.RenameVariable() extension method instead.")]
    public static void RenameVariable(NamedVariable variable, string newName)
    {
      Debug.Log((object) string.Format("RenameVariable {0} to {1}", (object) variable.Name, (object) newName));
      FsmSearch.UpdateAll();
      List<FsmInfo> variableUsageList = FsmSearch.GetVariableUsageList(FsmEditor.SelectedFsm, variable);
      foreach (FsmInfo fsmInfo in variableUsageList)
        FsmBuilder.RenameVariableInField(fsmInfo.fieldInObject, fsmInfo.field, variable, newName, 0);
      foreach (Fsm fsm in FsmInfo.GetFsmList(variableUsageList))
      {
        Debug.Log((object) ("SaveActions: " + Labels.GetFullFsmLabel(fsm)));
        FsmEditor.SaveActions(fsm);
      }
    }

    public static void RenameVariableInObject(
      object obj,
      NamedVariable variable,
      string newName,
      int currentDepth)
    {
      if (obj == null || currentDepth >= 7)
        return;
      foreach (FieldInfo serializedField in TypeHelpers.GetSerializedFields(obj.GetType()))
        FsmBuilder.RenameVariableInField(obj, serializedField, variable, newName, currentDepth + 1);
    }

    public static void RenameVariableInField(
      object obj,
      FieldInfo field,
      NamedVariable variable,
      string newName,
      int currentDepth)
    {
      object obj1 = field.GetValue(obj);
      if (obj1 == null)
        return;
      System.Type fieldType = field.FieldType;
      if (fieldType.IsSubclassOf(typeof (NamedVariable)))
      {
        NamedVariable namedVariable = (NamedVariable) obj1;
        if (namedVariable == variable || !(namedVariable.Name == variable.Name))
          return;
        namedVariable.SetName(newName);
      }
      else if (fieldType == typeof (FsmVar))
      {
        FsmVar fsmVar = (FsmVar) obj1;
        if (!(fsmVar.NamedVar.Name == variable.Name))
          return;
        fsmVar.NamedVar = variable;
        fsmVar.variableName = newName;
      }
      else if (fieldType.IsArray)
      {
        Array array = (Array) obj1;
        System.Type elementType = fieldType.GetElementType();
        for (int elementIndex = 0; elementIndex < array.Length; ++elementIndex)
          FsmBuilder.RenameVariableInArray(array, elementType, elementIndex, variable, newName, currentDepth);
      }
      else
      {
        if (!fieldType.IsClass)
          return;
        FsmBuilder.RenameVariableInObject(obj1, variable, newName, currentDepth);
      }
    }

    public static void RenameVariableInArray(
      Array array,
      System.Type type,
      int elementIndex,
      NamedVariable variable,
      string newName,
      int currentDepth)
    {
      object obj = array.GetValue(elementIndex);
      if (obj == null)
        return;
      if (type.IsSubclassOf(typeof (NamedVariable)))
      {
        NamedVariable namedVariable = (NamedVariable) obj;
        if (!(namedVariable.Name == variable.Name))
          return;
        namedVariable.SetName(newName);
        array.SetValue((object) namedVariable, elementIndex);
      }
      else if (type == typeof (FsmVar))
      {
        FsmVar fsmVar = (FsmVar) obj;
        if (!(fsmVar.variableName == variable.Name))
          return;
        fsmVar.NamedVar = variable;
      }
      else
      {
        if (!type.IsClass)
          return;
        FsmBuilder.RenameVariableInObject(obj, variable, newName, currentDepth);
      }
    }

    public static void AddFsmToSelected() => FsmBuilder.AddFsmToSelected((FsmTemplate) null);

    public static void AddFsmToSelected(FsmTemplate useTemplate)
    {
      GameObject[] gameObjects = Selection.gameObjects;
      if (gameObjects.Length == 0)
        return;
      if (gameObjects.Length > 1)
      {
        if (!Dialogs.AreYouSure(Strings.Dialog_Add_FSM, Strings.Dialog_Add_FSM_to_multiple_objects_))
          return;
        foreach (GameObject go in gameObjects)
          FsmBuilder.AddFsmToGameObject(go, true, useTemplate);
      }
      else
      {
        PlayMakerFSM gameObject = FsmBuilder.AddFsmToGameObject(Selection.activeGameObject, true, useTemplate);
        if (!((UnityEngine.Object) gameObject != (UnityEngine.Object) null) || !FsmEditor.IsOpen)
          return;
        FsmEditor.SelectFsm(gameObject.Fsm);
        FsmEditor.SelectState(gameObject.Fsm.States[0], false);
      }
    }

    public static PlayMakerFSM AddFsmToGameObject(
      GameObject go,
      bool undo,
      FsmTemplate useTemplate = null)
    {
      if (!((UnityEngine.Object) go != (UnityEngine.Object) null))
        return (PlayMakerFSM) null;
      string uniqueFsmName = Labels.GetUniqueFsmName(go);
      PlayMakerFSM fsmComponent = FsmBuilder.DoAddFsmComponent(go, undo);
      fsmComponent.Fsm.Name = uniqueFsmName;
      if ((UnityEngine.Object) useTemplate != (UnityEngine.Object) null)
        fsmComponent.UseTemplate(useTemplate);
      return fsmComponent;
    }

    private static PlayMakerFSM DoAddFsmComponent(GameObject go, bool undo)
    {
      PlayMakerFSM fsmComponent = undo ? Undo.AddComponent<PlayMakerFSM>(go) : go.AddComponent<PlayMakerFSM>();
      FsmEditor.AddToFsmList(fsmComponent);
      return fsmComponent;
    }

    public static string MergeVariables(UnityEngine.Object source, UnityEngine.Object targetOwner)
    {
      string str = "";
      List<FsmVariable> fsmVariableList1 = FsmVariable.GetFsmVariableList(source);
      List<FsmVariable> fsmVariableList2 = FsmVariable.GetFsmVariableList(targetOwner);
      FsmVariables variables = FsmVariable.GetVariables(targetOwner);
      foreach (FsmVariable fsmVariable in fsmVariableList1)
      {
        if (FsmVariable.VariableNameUsed(fsmVariableList2, fsmVariable.Name))
        {
          if (FsmVariable.GetVariableType(fsmVariableList2, fsmVariable.Name) != fsmVariable.Type)
          {
            Debug.Log((object) string.Format(Strings.Error_Variable_name_already_exists_and_is_of_different_type, (object) fsmVariable.Name));
            str = str + string.Format(Strings.Error_Variable_name_already_exists_and_is_of_different_type, (object) fsmVariable.Name) + Environment.NewLine;
          }
        }
        else
        {
          switch (fsmVariable.Type)
          {
            case VariableType.Float:
              variables.FloatVariables = ArrayUtility.Add<FsmFloat>(variables.FloatVariables, new FsmFloat((FsmFloat) fsmVariable.NamedVar));
              continue;
            case VariableType.Int:
              variables.IntVariables = ArrayUtility.Add<FsmInt>(variables.IntVariables, new FsmInt((FsmInt) fsmVariable.NamedVar));
              continue;
            case VariableType.Bool:
              variables.BoolVariables = ArrayUtility.Add<FsmBool>(variables.BoolVariables, new FsmBool((FsmBool) fsmVariable.NamedVar));
              continue;
            case VariableType.GameObject:
              variables.GameObjectVariables = ArrayUtility.Add<FsmGameObject>(variables.GameObjectVariables, new FsmGameObject((FsmGameObject) fsmVariable.NamedVar));
              continue;
            case VariableType.String:
              variables.StringVariables = ArrayUtility.Add<FsmString>(variables.StringVariables, new FsmString((FsmString) fsmVariable.NamedVar));
              continue;
            case VariableType.Vector2:
              variables.Vector2Variables = ArrayUtility.Add<FsmVector2>(variables.Vector2Variables, new FsmVector2((FsmVector2) fsmVariable.NamedVar));
              continue;
            case VariableType.Vector3:
              variables.Vector3Variables = ArrayUtility.Add<FsmVector3>(variables.Vector3Variables, new FsmVector3((FsmVector3) fsmVariable.NamedVar));
              continue;
            case VariableType.Color:
              variables.ColorVariables = ArrayUtility.Add<FsmColor>(variables.ColorVariables, new FsmColor((FsmColor) fsmVariable.NamedVar));
              continue;
            case VariableType.Rect:
              variables.RectVariables = ArrayUtility.Add<FsmRect>(variables.RectVariables, new FsmRect((FsmRect) fsmVariable.NamedVar));
              continue;
            case VariableType.Material:
              variables.MaterialVariables = ArrayUtility.Add<FsmMaterial>(variables.MaterialVariables, new FsmMaterial((FsmObject) fsmVariable.NamedVar));
              continue;
            case VariableType.Texture:
              variables.TextureVariables = ArrayUtility.Add<FsmTexture>(variables.TextureVariables, new FsmTexture((FsmObject) fsmVariable.NamedVar));
              continue;
            case VariableType.Quaternion:
              variables.QuaternionVariables = ArrayUtility.Add<FsmQuaternion>(variables.QuaternionVariables, new FsmQuaternion((FsmQuaternion) fsmVariable.NamedVar));
              continue;
            case VariableType.Object:
              variables.ObjectVariables = ArrayUtility.Add<FsmObject>(variables.ObjectVariables, new FsmObject((FsmObject) fsmVariable.NamedVar));
              continue;
            case VariableType.Array:
              variables.ArrayVariables = ArrayUtility.Add<FsmArray>(variables.ArrayVariables, new FsmArray((FsmArray) fsmVariable.NamedVar));
              continue;
            case VariableType.Enum:
              variables.EnumVariables = ArrayUtility.Add<FsmEnum>(variables.EnumVariables, new FsmEnum((FsmEnum) fsmVariable.NamedVar));
              continue;
            default:
              Debug.Log((object) Strings.Error_Unknown_variable_type);
              continue;
          }
        }
      }
      return str;
    }

    public static string MergeGlobals(PlayMakerGlobals source, PlayMakerGlobals target)
    {
      string str = FsmBuilder.MergeVariables((UnityEngine.Object) source, (UnityEngine.Object) target);
      foreach (string eventName in source.Events)
      {
        if (!target.Events.Contains(eventName))
          target.AddEvent(eventName);
      }
      return str;
    }

    [Obsolete("Use Fsm.AddState() extension method instead.")]
    public FsmState AddState(Vector2 position) => this.targetFsm.AddState(position);

    [Obsolete("Use Fsm.SetStartState extension method instead.")]
    public void SetStartState(string state) => this.targetFsm.StartState = state;

    [Obsolete("Use Fsm.AddState(stateName) extension method instead.")]
    public void SetStateName(FsmState state, string name)
    {
      if (state == null)
        return;
      state.Name = this.StateNameExists(state, name) ? this.GenerateUniqueStateName(state, name) : name;
    }

    [Obsolete("Use Fsm.RenameState(stateName) extension method instead.")]
    public void RenameState(FsmState state, string newName) => this.targetFsm.RenameState(state, newName);

    [Obsolete("Use Fsm.DeleteStates extension method instead.")]
    public void DeleteStates(List<FsmState> states) => this.targetFsm.DeleteStates(states);

    [Obsolete("Use Fsm.DeleteState extension method instead.")]
    public void DeleteState(FsmState state) => this.targetFsm.DeleteState(state);

    [Obsolete("Use Fsm.ValidateNewStateName extension method instead.")]
    public string ValidateNewStateName(FsmState state, string newName) => this.targetFsm.ValidateNewStateName(state, newName);

    [Obsolete("Use Fsm.GetUniqueStateName extension method instead.")]
    public string GenerateUniqueStateName(FsmState state, string newNameRoot)
    {
      int num = 1;
      string stateName = newNameRoot + " " + num.ToString((IFormatProvider) CultureInfo.InvariantCulture);
      while (this.targetFsm.HasState(stateName))
        stateName = newNameRoot + " " + num++.ToString((IFormatProvider) CultureInfo.InvariantCulture);
      return stateName;
    }

    [Obsolete("Use Fsm.HasState extension method instead.")]
    private bool StateNameExists(FsmState state, string name)
    {
      foreach (FsmState state1 in this.targetFsm.States)
      {
        if (state1 != state && state1.Name == name)
          return true;
      }
      return false;
    }

    [Obsolete("Use FsmState.IsStartState() extension method instead.")]
    public bool IsStartState(FsmState fsmState) => fsmState != null && this.targetFsm.GetState(this.targetFsm.StartState) == fsmState;

    [Obsolete("Use Fsm.GetStartState() extension method instead.")]
    public FsmState GetStartState() => this.targetFsm != null ? this.targetFsm.GetState(this.targetFsm.StartState) : (FsmState) null;

    [Obsolete("Use FsmState.SetColorIndex extension method instead.")]
    public static void SetStateColorIndex(FsmState state, int colorIndex, bool undo = true)
    {
      if (state == null)
        return;
      colorIndex = Mathf.Clamp(colorIndex, 0, PlayMakerPrefs.Colors.Length - 1);
      state.ColorIndex = colorIndex;
    }

    [Obsolete("Use Fsm.SetStatesColorIndex extension method instead.")]
    public static void SetSelectedStatesColorIndex(int colorIndex)
    {
      colorIndex = Mathf.Clamp(colorIndex, 0, PlayMakerPrefs.Colors.Length - 1);
      foreach (FsmState state in FsmEditor.Selection.States)
        state.ColorIndex = colorIndex;
    }

    [Obsolete("Use State.AddTransition extension method instead.")]
    public FsmTransition AddTransition(FsmState state) => state.AddTransition();

    [Obsolete("Use State.AddTransition extension method instead.")]
    public FsmTransition AddTransition(
      FsmState state,
      string toState,
      FsmEvent fsmEvent)
    {
      return state.AddTransition(toState, fsmEvent);
    }

    [Obsolete("Use FsmTransition.SetEvent extension method instead.")]
    public void SetTransitionEvent(FsmTransition transition, FsmEvent fsmEvent) => transition.SetEvent(fsmEvent);

    [Obsolete("Use Fsm.GetTransitionTargetState extension method instead.")]
    public static FsmState GetTransitionState(Fsm fsm, FsmTransition transition) => fsm.GetTransitionTargetState(transition);

    [Obsolete("Use Fsm.SetTransitionTargetState extension method instead.")]
    public void SetTransitionTarget(FsmTransition transition, string toState) => this.targetFsm.SetTransitionTargetState(transition, toState);

    [Obsolete("Use FsmState.DeleteTransition extension method instead.")]
    public void DeleteTransition(FsmState state, FsmTransition transition) => state.DeleteTransition(transition);

    [Obsolete("Use FsmState.MoveTransitionUp extension method instead.")]
    public void MoveTransitionUp(FsmState state, FsmTransition transition) => state.MoveTransitionUp(transition);

    [Obsolete("Use FsmState.MoveTransitionDown extension method instead.")]
    public void MoveTransitionDown(FsmState state, FsmTransition transition) => state.MoveTransitionDown(transition);

    [Obsolete("Use Fsm.FindTransitionToStateFromActiveState extension method instead.")]
    public FsmTransition FindTransitionToState(FsmState toState) => this.targetFsm.FindTransitionToStateFromActiveState(toState);

    [Obsolete("Use FsmState.GetTransitionIndex extension method instead.")]
    public int GetTransitionIndex(FsmState state, FsmTransition transition) => state.GetTransitionIndex(transition);

    [Obsolete("Use FsmTransition.SetColorIndex extension method instead.")]
    public static void SetTransitionColorIndex(
      Fsm fsm,
      FsmTransition transition,
      int colorIndex,
      bool undo = true)
    {
      transition.SetColorIndex(colorIndex);
    }

    [Obsolete("Use Fsm.AddGlobalTransitionToState extension method instead.")]
    public FsmTransition AddGlobalTransition(FsmState state, FsmEvent fsmEvent) => this.targetFsm.AddGlobalTransitionToState(state, fsmEvent);

    [Obsolete("Use Fsm.DeleteGlobalTransitionToState extension method instead.")]
    public void DeleteGlobalTransition(FsmTransition transition) => this.targetFsm.DeleteGlobalTransition(transition);

    [Obsolete("Use Fsm.GetGlobalTransitionsToState extension method instead.")]
    public List<FsmTransition> GetGlobalTransitions(FsmState state) => this.targetFsm.GetGlobalTransitionsToState(state);

    [Obsolete("Use Fsm.IsGlobalTransitionToState extension method instead.")]
    public bool HasGlobalTransition(FsmState state) => this.targetFsm.IsGlobalTransitionToState(state);

    [Obsolete("Use Fsm.AddExposedEvent extension method instead.")]
    public static void AddExposedEvent(Fsm fsm, FsmEvent fsmEvent) => fsm.AddExposedEvent(fsmEvent);

    [Obsolete("Use Fsm.RemoveExposedEvent extension method instead.")]
    public static void RemoveExposedEvent(Fsm fsm, FsmEvent fsmEvent) => fsm.RemoveExposedEvent(fsmEvent);

    [Obsolete("Use Fsm.AddEvent extension method instead.")]
    public FsmEvent AddEvent(string name) => this.targetFsm.AddEvent(name);

    [Obsolete("Use Fsm.AddEvent extension method instead.")]
    public FsmEvent AddEvent(Fsm fsm, FsmEvent fsmEvent) => fsm.AddEvent(fsmEvent);

    [Obsolete("Use Fsm.DeleteEvent extension method instead.")]
    public void DeleteEvent(FsmEvent fsmEvent) => this.targetFsm.DeleteEvent(fsmEvent);

    [Obsolete("Use Fsm.DeleteEvent extension method instead.")]
    public void DeleteEvent(Fsm fsm, string fsmEventName)
    {
      if (fsm == null || string.IsNullOrEmpty(fsmEventName))
        return;
      List<FsmEvent> fsmEventList1 = new List<FsmEvent>();
      foreach (FsmEvent fsmEvent in fsm.Events)
      {
        if (fsmEvent.Name != fsmEventName)
          fsmEventList1.Add(fsmEvent);
      }
      fsm.Events = fsmEventList1.ToArray();
      List<FsmEvent> fsmEventList2 = new List<FsmEvent>();
      foreach (FsmEvent exposedEvent in fsm.ExposedEvents)
      {
        if (exposedEvent.Name != fsmEventName)
          fsmEventList2.Add(exposedEvent);
      }
      fsm.ExposedEvents = fsmEventList2;
      foreach (FsmTransition globalTransition in fsm.GlobalTransitions)
      {
        if (globalTransition.EventName == fsmEventName)
          globalTransition.FsmEvent = (FsmEvent) null;
      }
      foreach (FsmState state in fsm.States)
      {
        foreach (FsmTransition transition in state.Transitions)
        {
          if (transition.EventName == fsmEventName)
            transition.FsmEvent = (FsmEvent) null;
        }
        foreach (FsmStateAction action in state.Actions)
          this.DeleteEvent(state, action, fsmEventName);
      }
    }

    [Obsolete("Use Fsm.DeleteEvent extension method instead.")]
    public void DeleteEvent(Fsm fsm, FsmEvent fsmEvent)
    {
      if (fsm == null || FsmEvent.IsNullOrEmpty(fsmEvent))
        return;
      this.DeleteEvent(fsm, fsmEvent.Name);
    }

    [Obsolete("Use FsmState.DeleteEvent extension method instead.")]
    public void DeleteEvent(FsmState state, FsmStateAction action, string fsmEventName)
    {
      if (state == null || action == null || string.IsNullOrEmpty(fsmEventName))
        return;
      this.DeleteEvent((object) action, fsmEventName);
    }

    [Obsolete("Use FsmState.DeleteEvent extension method instead.")]
    public void DeleteEvent(object obj, string fsmEventName)
    {
      if (obj == null || string.IsNullOrEmpty(fsmEventName))
        return;
      foreach (FieldInfo field in ActionData.GetFields(obj.GetType()))
      {
        System.Type fieldType = field.FieldType;
        object obj1 = field.GetValue(obj);
        if (obj1 != null && fieldType == typeof (FsmEvent) && ((FsmEvent) obj1).Name == fsmEventName)
          field.SetValue(obj, (object) null);
      }
    }

    [Obsolete("Use Fsm.RenameEvent extension method instead.")]
    public void RenameEvent(string oldEventName, string newEventName) => Debug.LogWarning((object) "Not implemented!");

    [Obsolete("Use Fsm.RenameEvent extension method instead.")]
    public void RenameEvent(Fsm fsmTarget, string oldEventName, string newEventName)
    {
      if (fsmTarget == null || newEventName == oldEventName)
        return;
      foreach (FsmInfo eventSentBy in FsmSearch.GetSearch(fsmTarget).GetEventSentByList(oldEventName))
        eventSentBy.field.SetValue(eventSentBy.fieldInObject, (object) new FsmEvent(newEventName));
      fsmTarget.SaveActions();
      foreach (FsmInfo fsmInfo in FsmInfo.FindTransitionsUsingEvent(fsmTarget, oldEventName))
        fsmInfo.transition.FsmEvent.Name = newEventName;
      foreach (FsmEvent fsmEvent in fsmTarget.Events)
      {
        if (fsmEvent.Name == oldEventName)
          fsmEvent.Name = newEventName;
      }
      foreach (FsmEvent exposedEvent in fsmTarget.ExposedEvents)
      {
        if (exposedEvent.Name == oldEventName)
          exposedEvent.Name = newEventName;
      }
      FsmEditor.GraphView.UpdateStateSizes(fsmTarget);
      FsmEditor.SetFsmDirty(fsmTarget, true);
      FsmEditor.UpdateFsmInfo(fsmTarget);
      FsmEvent.EventList.Sort();
    }

    [Obsolete("Use EditPlayMakerGlobals.SetEventIsGlobal instead")]
    public void SetEventIsGlobal(FsmEvent fsmEvent, bool isGlobal) => EditPlayMakerGlobals.SetEventIsGlobal(fsmEvent, isGlobal);

    [Obsolete("Use EditPlayMakerGlobals.SetEventIsGlobal instead")]
    public void SetEventIsGlobal(FsmEvent fsmEvent) => EditPlayMakerGlobals.SetEventIsGlobal(fsmEvent, true);

    [Obsolete("Use FsmEventManager.ValidateRenameEvent instead.")]
    public string ValidateRenameEvent(FsmEvent fsmEvent, string newEventName) => fsmEvent == null || newEventName == null || !(fsmEvent.Name == newEventName) && newEventName.Replace(" ", "") == "" ? Strings.Error_Invalid_Name : "";

    [Obsolete("Use Fsm.ValidateAddEvent extension method instead.")]
    public string ValidateAddEvent(string newEventName)
    {
      if (string.IsNullOrEmpty(newEventName))
        return "";
      if (newEventName.Replace(" ", "") == "")
        return Strings.Error_Invalid_Name;
      foreach (FsmEvent fsmEvent in this.targetFsm.Events)
      {
        if (fsmEvent.Name == newEventName)
          return Strings.Error_Event_already_used;
      }
      return "";
    }

    [Obsolete("Use FsmSearch results instead.")]
    public static void RenameEventInField(
      object obj,
      FieldInfo field,
      string oldEventName,
      string newEventName)
    {
      System.Type fieldType = field.FieldType;
      object obj1 = field.GetValue(obj);
      if (obj1 == null)
        return;
      if (fieldType == typeof (FsmEvent))
      {
        FsmEvent fsmEvent = (FsmEvent) obj1;
        if (fsmEvent.Name == oldEventName)
        {
          fsmEvent.Name = newEventName;
          field.SetValue(obj, (object) fsmEvent);
        }
      }
      if (!fieldType.IsArray)
        return;
      Array array = (Array) obj1;
      if (fieldType.GetElementType() != typeof (FsmEvent))
        return;
      int num = 0;
      while (num < array.Length)
        ++num;
    }

    [Obsolete("Use FsmSearch results instead.")]
    public static void RenameEventInArray(
      Array array,
      int elementIndex,
      string oldEventName,
      string newEventName)
    {
      object obj = array.GetValue(elementIndex);
      if (obj == null || !(obj is FsmEvent fsmEvent) || !(fsmEvent.Name == oldEventName))
        return;
      fsmEvent.Name = newEventName;
      array.SetValue((object) fsmEvent, elementIndex);
    }

    [Obsolete("Use SetEventIsGlobal(FsmEvent, bool) instead. All FSMs are checked for the event.")]
    public void SetEventIsGlobal(Fsm fsm, FsmEvent fsmEvent, bool isGlobal) => this.SetEventIsGlobal(fsmEvent, isGlobal);

    [Obsolete("Use FsmState.AddAction extension method instead.")]
    public FsmStateAction AddAction(FsmState state, System.Type actionType)
    {
      if (state == null)
        return (FsmStateAction) null;
      FsmStateAction instance = (FsmStateAction) Activator.CreateInstance(actionType);
      state.Actions = ArrayUtility.Add<FsmStateAction>(state.Actions, instance);
      instance.Init(state);
      instance.Reset();
      FsmEditor.SaveActions(instance.State);
      return instance;
    }

    [Obsolete("Use FsmState.InsertAction extension method instead.")]
    public FsmStateAction InsertAction(
      FsmState state,
      System.Type actionType,
      FsmStateAction beforeAction)
    {
      if (state == null)
        return (FsmStateAction) null;
      if (beforeAction == null)
        return this.AddAction(state, actionType);
      FsmEditor.RecordUndo(Strings.Command_Insert_Action);
      FsmStateAction instance = (FsmStateAction) Activator.CreateInstance(actionType);
      int actionIndex = Actions.GetActionIndex(state, beforeAction);
      if (actionIndex == -1)
        return this.AddAction(state, actionType);
      List<FsmStateAction> fsmStateActionList = new List<FsmStateAction>((IEnumerable<FsmStateAction>) state.Actions);
      fsmStateActionList.Insert(actionIndex, instance);
      state.Actions = fsmStateActionList.ToArray();
      instance.Init(state);
      instance.Reset();
      FsmEditor.SaveActions(instance.State);
      FsmEditor.UpdateActionUsage();
      return instance;
    }

    [Obsolete("Use FsmState.DeleteAction extension method instead.")]
    public void DeleteAction(FsmState state, FsmStateAction action)
    {
      if (state == null || action == null)
        return;
      FsmEditor.RecordUndo(Strings.Command_Delete_Action);
      state.Actions = ArrayUtility.Remove<FsmStateAction>(state.Actions, action);
      Keyboard.ResetFocus();
      FsmEditor.SaveActions(state);
      FsmEditor.UpdateActionUsage();
      FsmEditor.UpdateFsmInfo();
    }

    [Obsolete("Use FsmState.DeleteAction extension method instead.")]
    public void DeleteActions(FsmState state, IEnumerable<FsmStateAction> actions, bool undo = true)
    {
      if (state == null || actions == null)
        return;
      if (undo)
        FsmEditor.RecordUndo(Strings.Command_Delete_Actions);
      foreach (FsmStateAction action in actions)
        state.Actions = ArrayUtility.Remove<FsmStateAction>(state.Actions, action);
      Keyboard.ResetFocus();
      FsmEditor.SaveActions(state);
      FsmEditor.UpdateActionUsage();
      FsmEditor.UpdateFsmInfo();
    }

    [Obsolete("Use Fsm.AddVariable extension method instead")]
    public NamedVariable AddVariable(VariableType varType, string varName) => FsmBuilder.AddVariable(this.targetFsm, varType, varName);

    [Obsolete("Use Fsm.AddVariable extension method instead")]
    public static NamedVariable AddVariable(Fsm fsm, VariableType type, string name)
    {
      name = FsmVariable.GetUniqueVariableName(FsmVariable.GetFsmVariableList(fsm.OwnerObject), name);
      fsm.AddVariable(type, name);
      return fsm.Variables.GetVariable(name);
    }

    [Obsolete("Use FsmClipboard")]
    public static FsmTemplate Clipboard
    {
      get => FsmBuilder.clipboard;
      set => FsmBuilder.clipboard = value;
    }

    [Obsolete("Use FsmClipboard.CanPaste instead.")]
    public static int ClipboardNumStates => (UnityEngine.Object) FsmBuilder.clipboard == (UnityEngine.Object) null || FsmBuilder.clipboard.fsm == null || FsmBuilder.clipboard.fsm.States == null ? 0 : FsmBuilder.clipboard.fsm.States.Length;

    [Obsolete("Use FsmClipboard.CanPaste instead.")]
    public bool CanPaste() => !((UnityEngine.Object) FsmBuilder.clipboard == (UnityEngine.Object) null) && FsmBuilder.clipboard.fsm != null && (uint) FsmBuilder.clipboard.fsm.States.Length > 0U;

    [Obsolete("Use EditFsmTemplate.AddTemplateToSelected instead.")]
    public static Fsm AddTemplateToSelected(FsmTemplate fsmTemplate)
    {
      if ((UnityEngine.Object) fsmTemplate == (UnityEngine.Object) null)
        return (Fsm) null;
      GameObject[] gameObjects = Selection.gameObjects;
      if (gameObjects.Length == 0)
        return (Fsm) null;
      if (gameObjects.Length > 1 && !Dialogs.AreYouSure(Strings.Dialog_Add_FSM_Template, Strings.Dialog_Add_FSM_Template_to_multiple_objects_))
        return (Fsm) null;
      Fsm fsm = (Fsm) null;
      foreach (GameObject gameObject in Selection.gameObjects)
      {
        if ((UnityEngine.Object) fsmTemplate == (UnityEngine.Object) null || (UnityEngine.Object) gameObject == (UnityEngine.Object) null)
        {
          fsm = (Fsm) null;
        }
        else
        {
          PlayMakerFSM fsmComponent = FsmBuilder.DoAddFsmComponent(gameObject, true);
          fsmComponent.Fsm.Name = fsmTemplate.fsm.Name;
          fsmComponent.Fsm.States = FsmBuilder.CopyStates((IEnumerable<FsmState>) fsmTemplate.fsm.States);
          fsmComponent.Fsm.GlobalTransitions = FsmBuilder.CopyTransitions((IEnumerable<FsmTransition>) fsmTemplate.fsm.GlobalTransitions);
          fsmComponent.Fsm.Events = FsmBuilder.CopyEvents((IEnumerable<FsmEvent>) fsmTemplate.fsm.Events);
          fsmComponent.Fsm.Variables = FsmBuilder.CopyVariables(fsmTemplate.fsm.Variables);
          fsmComponent.Fsm.Description = fsmTemplate.fsm.Description;
          fsmComponent.Fsm.Watermark = fsmTemplate.fsm.Watermark;
          fsmComponent.Fsm.StartState = fsmTemplate.fsm.StartState;
          fsmComponent.Fsm.ExposedEvents = new List<FsmEvent>((IEnumerable<FsmEvent>) FsmBuilder.CopyEvents((IEnumerable<FsmEvent>) fsmTemplate.fsm.ExposedEvents));
          fsmComponent.Fsm.OutputEvents = new List<FsmEvent>((IEnumerable<FsmEvent>) FsmBuilder.CopyEvents((IEnumerable<FsmEvent>) fsmTemplate.fsm.OutputEvents));
          bool flag = false;
          foreach (FsmState state in fsmComponent.Fsm.States)
          {
            FsmGraphView.TranslateState(state, new Vector2(100f, 100f));
            if (state.Name == fsmComponent.Fsm.StartState)
              flag = true;
          }
          if (!flag)
            fsmComponent.Fsm.StartState = fsmComponent.Fsm.States[0].Name;
          FsmEditor.AddToFsmList(fsmComponent);
          fsm = fsmComponent.Fsm;
        }
      }
      return fsm;
    }

    [Obsolete("Use EditFsmTemplate instead")]
    public static Fsm PasteFsmToSelected()
    {
      if ((UnityEngine.Object) FsmBuilder.clipboard == (UnityEngine.Object) null)
        return (Fsm) null;
      GameObject[] gameObjects = Selection.gameObjects;
      if (gameObjects.Length == 0)
        return (Fsm) null;
      if (gameObjects.Length > 1 && !Dialogs.AreYouSure(Strings.Command_Paste_FSM, Strings.Dialog_Paste_FSM_to_multiple_objects))
        return (Fsm) null;
      Fsm fsm = (Fsm) null;
      foreach (GameObject gameObject in Selection.gameObjects)
      {
        if ((UnityEngine.Object) FsmBuilder.clipboard == (UnityEngine.Object) null || (UnityEngine.Object) gameObject == (UnityEngine.Object) null)
        {
          fsm = (Fsm) null;
        }
        else
        {
          PlayMakerFSM fsmComponent = FsmBuilder.DoAddFsmComponent(gameObject, true);
          fsmComponent.Fsm.Name = FsmBuilder.clipboard.fsm.Name;
          fsmComponent.Fsm.States = FsmBuilder.CopyStates((IEnumerable<FsmState>) FsmBuilder.clipboard.fsm.States);
          fsmComponent.Fsm.GlobalTransitions = FsmBuilder.CopyTransitions((IEnumerable<FsmTransition>) FsmBuilder.clipboard.fsm.GlobalTransitions);
          fsmComponent.Fsm.Events = FsmBuilder.CopyEvents((IEnumerable<FsmEvent>) FsmBuilder.clipboard.fsm.Events);
          fsmComponent.Fsm.Variables = FsmBuilder.CopyVariables(FsmBuilder.clipboard.fsm.Variables);
          fsmComponent.Fsm.Description = FsmBuilder.clipboard.fsm.Description;
          fsmComponent.Fsm.Watermark = FsmBuilder.clipboard.fsm.Watermark;
          fsmComponent.Fsm.StartState = FsmBuilder.clipboard.fsm.StartState;
          fsmComponent.Fsm.ExposedEvents = new List<FsmEvent>((IEnumerable<FsmEvent>) FsmBuilder.CopyEvents((IEnumerable<FsmEvent>) FsmBuilder.clipboard.fsm.ExposedEvents));
          fsmComponent.Fsm.OutputEvents = new List<FsmEvent>((IEnumerable<FsmEvent>) FsmBuilder.CopyEvents((IEnumerable<FsmEvent>) FsmBuilder.clipboard.fsm.OutputEvents));
          bool flag = false;
          foreach (FsmState state in fsmComponent.Fsm.States)
          {
            FsmGraphView.TranslateState(state, new Vector2(100f, 100f));
            if (state.Name == fsmComponent.Fsm.StartState)
              flag = true;
          }
          if (!flag)
            fsmComponent.Fsm.StartState = fsmComponent.Fsm.States[0].Name;
          FsmEditor.AddToFsmList(fsmComponent);
          fsm = fsmComponent.Fsm;
        }
      }
      return fsm;
    }

    [Obsolete("Use EditFsmTemplate.AddTemplateToSelected instead.")]
    public static Fsm AddTemplate(FsmTemplate fsmTemplate)
    {
      if ((UnityEngine.Object) fsmTemplate == (UnityEngine.Object) null || (UnityEngine.Object) FsmEditor.SelectedFsmGameObject == (UnityEngine.Object) null)
        return (Fsm) null;
      PlayMakerFSM fsmComponent = FsmBuilder.DoAddFsmComponent(FsmEditor.SelectedFsmGameObject, true);
      fsmComponent.Fsm.Name = fsmTemplate.fsm.Name;
      fsmComponent.Fsm.States = FsmBuilder.CopyStates((IEnumerable<FsmState>) fsmTemplate.fsm.States);
      fsmComponent.Fsm.GlobalTransitions = FsmBuilder.CopyTransitions((IEnumerable<FsmTransition>) fsmTemplate.fsm.GlobalTransitions);
      fsmComponent.Fsm.Events = FsmBuilder.CopyEvents((IEnumerable<FsmEvent>) fsmTemplate.fsm.Events);
      fsmComponent.Fsm.Variables = FsmBuilder.CopyVariables(fsmTemplate.fsm.Variables);
      fsmComponent.Fsm.Description = fsmTemplate.fsm.Description;
      fsmComponent.Fsm.Watermark = fsmTemplate.fsm.Watermark;
      fsmComponent.Fsm.StartState = fsmTemplate.fsm.StartState;
      fsmComponent.Fsm.ExposedEvents = new List<FsmEvent>((IEnumerable<FsmEvent>) FsmBuilder.CopyEvents((IEnumerable<FsmEvent>) fsmTemplate.fsm.ExposedEvents));
      fsmComponent.Fsm.OutputEvents = new List<FsmEvent>((IEnumerable<FsmEvent>) FsmBuilder.CopyEvents((IEnumerable<FsmEvent>) fsmTemplate.fsm.OutputEvents));
      bool flag = false;
      foreach (FsmState state in fsmComponent.Fsm.States)
      {
        FsmGraphView.TranslateState(state, new Vector2(100f, 100f));
        if (state.Name == fsmComponent.Fsm.StartState)
          flag = true;
      }
      if (!flag)
        fsmComponent.Fsm.StartState = fsmComponent.Fsm.States[0].Name;
      FsmEditor.AddToFsmList(fsmComponent);
      return fsmComponent.Fsm;
    }

    [Obsolete("Use FsmClipboard instead.")]
    private static void CreateClipboard() => FsmBuilder.clipboard = (FsmTemplate) ScriptableObject.CreateInstance(typeof (FsmTemplate));

    [Obsolete("Use FsmClipboard.CopyFSM instead.")]
    public static void CopyFsmToClipboard(Fsm fsm)
    {
      if (fsm == null)
        return;
      if ((UnityEngine.Object) FsmBuilder.clipboard == (UnityEngine.Object) null)
        FsmBuilder.CreateClipboard();
      FsmBuilder.CopyFsmToTemplate(fsm, FsmBuilder.clipboard);
    }

    [Obsolete("Use FsmTemplate.Copy(Fsm) extension method.")]
    public static void CopyFsmToTemplate(Fsm fsm, FsmTemplate template) => template.fsm = new Fsm(fsm)
    {
      UsedInTemplate = template,
      Owner = (MonoBehaviour) null
    };

    [Obsolete("Use FsmClipboard.CopyStates instead.")]
    public void CopyStatesToClipboard(List<FsmState> states)
    {
      if ((UnityEngine.Object) FsmBuilder.clipboard == (UnityEngine.Object) null)
        FsmBuilder.CreateClipboard();
      this.CopyStatesToTemplate(states, FsmBuilder.clipboard);
    }

    [Obsolete("Use FsmClipboard.CopyStates instead.")]
    public void CopyStatesToTemplate(List<FsmState> states, FsmTemplate template)
    {
      if (states == null || states.Count == 0 || (UnityEngine.Object) template == (UnityEngine.Object) null)
        return;
      template.fsm = new Fsm()
      {
        DataVersion = this.targetFsm.DataVersion,
        Variables = this.CopyStateVariables(states, this.targetFsm.Variables),
        States = FsmBuilder.CopyStates((IEnumerable<FsmState>) states)
      };
      template.fsm.Init((MonoBehaviour) null);
      foreach (FsmState state in states)
      {
        if (state.Fsm.StartState == state.Name)
        {
          template.fsm.StartState = state.Name;
          template.fsm.UsedInTemplate = template;
        }
      }
      List<FsmTransition> fsmTransitionList = new List<FsmTransition>();
      foreach (FsmState state in states)
      {
        foreach (FsmTransition globalTransition in state.Fsm.GlobalTransitions)
        {
          if (globalTransition.ToState == state.Name)
            fsmTransitionList.Add(FsmBuilder.CopyTransition(globalTransition));
        }
      }
      template.fsm.GlobalTransitions = fsmTransitionList.ToArray();
      List<FsmEvent> fsmEventList = new List<FsmEvent>();
      foreach (FsmState state in states)
      {
        foreach (FsmTransition transition in state.Transitions)
        {
          if (!string.IsNullOrEmpty(transition.EventName))
          {
            FsmEvent fsmEvent = FsmEvent.GetFsmEvent(transition.EventName);
            if (!fsmEventList.Contains(fsmEvent))
              fsmEventList.Add(fsmEvent);
          }
        }
      }
      foreach (FsmTransition globalTransition in template.fsm.GlobalTransitions)
      {
        if (!string.IsNullOrEmpty(globalTransition.EventName))
        {
          FsmEvent fsmEvent = FsmEvent.GetFsmEvent(globalTransition.EventName);
          if (!fsmEventList.Contains(fsmEvent))
            fsmEventList.Add(fsmEvent);
        }
      }
      template.fsm.Events = fsmEventList.ToArray();
      Vector2 vector2 = new Vector2(float.PositiveInfinity, float.PositiveInfinity);
      Rect position;
      foreach (FsmState state in template.fsm.States)
      {
        ref Vector2 local1 = ref vector2;
        position = state.Position;
        double num1 = (double) Mathf.Min(position.x, vector2.x);
        local1.x = (float) num1;
        ref Vector2 local2 = ref vector2;
        position = state.Position;
        double num2 = (double) Mathf.Min(position.y, vector2.y);
        local2.y = (float) num2;
      }
      foreach (FsmState state in template.fsm.States)
      {
        FsmState fsmState = state;
        position = state.Position;
        double num1 = (double) position.x - (double) vector2.x;
        position = state.Position;
        double num2 = (double) position.y - (double) vector2.y;
        position = state.Position;
        double width = (double) position.width;
        position = state.Position;
        double height = (double) position.height;
        Rect rect = new Rect((float) num1, (float) num2, (float) width, (float) height);
        fsmState.Position = rect;
      }
      EditorUtility.SetDirty((UnityEngine.Object) template);
    }

    [Obsolete("Use FsmClipboard instead.")]
    private static FsmState[] CopyStates(IEnumerable<FsmState> states)
    {
      List<FsmState> fsmStateList = new List<FsmState>();
      foreach (FsmState state in states)
        fsmStateList.Add(FsmBuilder.CopyState(state));
      return fsmStateList.ToArray();
    }

    [Obsolete("Use FsmClipboard instead.")]
    private static FsmState CopyState(FsmState state) => new FsmState(state)
    {
      Fsm = (Fsm) null
    };

    [Obsolete("Use FsmClipboard.CopyActionsToClipboard instead.")]
    public void CopyActionsToClipboard(FsmState state, List<FsmStateAction> actions)
    {
      if ((UnityEngine.Object) FsmBuilder.clipboard == (UnityEngine.Object) null)
        FsmBuilder.CreateClipboard();
      this.CopyActionsToTemplate(state, actions, FsmBuilder.clipboard);
    }

    [Obsolete("Use FsmClipboard.CopyActionsToClipboard instead.")]
    public void CopyActionsToTemplate(
      FsmState state,
      List<FsmStateAction> actions,
      FsmTemplate template)
    {
      template.fsm = Fsm.NewTempFsm();
      template.fsm.States = FsmBuilder.CopyStates((IEnumerable<FsmState>) new List<FsmState>()
      {
        state
      });
      template.fsm.DataVersion = state.Fsm.DataVersion;
      template.fsm.Variables = new FsmVariables(state.Fsm.Variables);
      template.fsm.Init((MonoBehaviour) null);
      FsmState state1 = template.fsm.States[0];
      FsmState fsmState = state1;
      Rect position = state1.Position;
      double width = (double) position.width;
      position = state1.Position;
      double height = (double) position.height;
      Rect rect = new Rect(0.0f, 0.0f, (float) width, (float) height);
      fsmState.Position = rect;
      state1.Transitions = new FsmTransition[0];
      state1.Description = "";
      List<FsmStateAction> fsmStateActionList1 = new List<FsmStateAction>();
      List<FsmStateAction> fsmStateActionList2 = new List<FsmStateAction>();
      int index = 0;
      foreach (FsmStateAction action in state.Actions)
      {
        if (!actions.Contains(action))
          fsmStateActionList2.Add(state1.Actions[index]);
        else
          fsmStateActionList1.Add(action);
        ++index;
      }
      foreach (FsmStateAction fsmStateAction in fsmStateActionList2)
        state1.Actions = ArrayUtility.Remove<FsmStateAction>(state1.Actions, fsmStateAction);
      template.fsm.Variables = new FsmVariables()
      {
        Categories = ArrayUtility.Copy<string>(this.targetFsm.Variables.Categories)
      };
      Dictionary<string, string> categoryMap = new Dictionary<string, string>();
      foreach (string variablesUsedByAction in FsmSearch.FindVariablesUsedByActions(this.targetFsm, (IEnumerable<FsmStateAction>) fsmStateActionList1))
      {
        NamedVariable variable = this.targetFsm.Variables.FindVariable(variablesUsedByAction);
        template.fsm.AddVariable(variable);
        string category = this.targetFsm.Variables.GetCategory(variable);
        if (!string.IsNullOrEmpty(category))
          categoryMap.Add(variable.Name, category);
      }
      template.fsm.Variables.RebuildCategoryIDs(categoryMap);
      state1.SaveActions();
    }

    [Obsolete("Use FsmClipboard instead.")]
    private static FsmTransition[] CopyTransitions(
      IEnumerable<FsmTransition> transitions)
    {
      List<FsmTransition> fsmTransitionList = new List<FsmTransition>();
      foreach (FsmTransition transition in transitions)
        fsmTransitionList.Add(FsmBuilder.CopyTransition(transition));
      return fsmTransitionList.ToArray();
    }

    [Obsolete("Use FsmClipboard instead.")]
    private static FsmTransition CopyTransition(FsmTransition transiton) => new FsmTransition()
    {
      FsmEvent = new FsmEvent(transiton.EventName),
      ToState = transiton.ToState,
      ColorIndex = transiton.ColorIndex,
      LinkConstraint = transiton.LinkConstraint,
      LinkStyle = transiton.LinkStyle
    };

    [Obsolete("Use FsmClipboard instead.")]
    private static FsmEvent[] CopyEvents(IEnumerable<FsmEvent> events)
    {
      List<FsmEvent> fsmEventList = new List<FsmEvent>();
      foreach (FsmEvent source in events)
        fsmEventList.Add(new FsmEvent(source));
      return fsmEventList.ToArray();
    }

    [Obsolete("Use FsmClipboard instead.")]
    public static FsmVariables CopyVariables(FsmVariables variables) => new FsmVariables(variables);

    [Obsolete("Use FsmClipboard instead.")]
    private FsmVariables CopyStateVariables(FsmState state, FsmVariables variables) => this.CopyStateVariables(new List<FsmState>()
    {
      state
    }, variables);

    [Obsolete("Use FsmClipboard instead.")]
    private FsmVariables CopyStateVariables(
      List<FsmState> states,
      FsmVariables variables)
    {
      Debug.Log((object) nameof (CopyStateVariables));
      if (states == null || states.Count == 0 || variables == null)
        return new FsmVariables();
      List<string> variablesUsedByStates = FsmSearch.FindVariablesUsedByStates(this.targetFsm, (IEnumerable<FsmState>) states);
      List<FsmFloat> fsmFloatList = new List<FsmFloat>();
      List<FsmInt> fsmIntList = new List<FsmInt>();
      List<FsmBool> fsmBoolList = new List<FsmBool>();
      List<FsmGameObject> fsmGameObjectList = new List<FsmGameObject>();
      List<FsmColor> fsmColorList = new List<FsmColor>();
      List<FsmVector2> fsmVector2List = new List<FsmVector2>();
      List<FsmVector3> fsmVector3List = new List<FsmVector3>();
      List<FsmRect> fsmRectList = new List<FsmRect>();
      List<FsmQuaternion> fsmQuaternionList = new List<FsmQuaternion>();
      List<FsmObject> fsmObjectList = new List<FsmObject>();
      List<FsmMaterial> fsmMaterialList = new List<FsmMaterial>();
      List<FsmTexture> fsmTextureList = new List<FsmTexture>();
      List<FsmString> fsmStringList = new List<FsmString>();
      List<FsmEnum> fsmEnumList = new List<FsmEnum>();
      List<FsmArray> fsmArrayList = new List<FsmArray>();
      foreach (FsmFloat floatVariable in variables.FloatVariables)
      {
        if (variablesUsedByStates.Contains(floatVariable.Name))
          fsmFloatList.Add(new FsmFloat(floatVariable));
      }
      foreach (FsmInt intVariable in variables.IntVariables)
      {
        if (variablesUsedByStates.Contains(intVariable.Name))
          fsmIntList.Add(new FsmInt(intVariable));
      }
      foreach (FsmBool boolVariable in variables.BoolVariables)
      {
        if (variablesUsedByStates.Contains(boolVariable.Name))
          fsmBoolList.Add(new FsmBool(boolVariable));
      }
      foreach (FsmGameObject gameObjectVariable in variables.GameObjectVariables)
      {
        if (variablesUsedByStates.Contains(gameObjectVariable.Name))
          fsmGameObjectList.Add(new FsmGameObject(gameObjectVariable));
      }
      foreach (FsmColor colorVariable in variables.ColorVariables)
      {
        if (variablesUsedByStates.Contains(colorVariable.Name))
          fsmColorList.Add(new FsmColor(colorVariable));
      }
      foreach (FsmVector2 vector2Variable in variables.Vector2Variables)
      {
        if (variablesUsedByStates.Contains(vector2Variable.Name))
          fsmVector2List.Add(new FsmVector2(vector2Variable));
      }
      foreach (FsmVector3 vector3Variable in variables.Vector3Variables)
      {
        if (variablesUsedByStates.Contains(vector3Variable.Name))
          fsmVector3List.Add(new FsmVector3(vector3Variable));
      }
      foreach (FsmRect rectVariable in variables.RectVariables)
      {
        if (variablesUsedByStates.Contains(rectVariable.Name))
          fsmRectList.Add(new FsmRect(rectVariable));
      }
      foreach (FsmQuaternion quaternionVariable in variables.QuaternionVariables)
      {
        if (variablesUsedByStates.Contains(quaternionVariable.Name))
          fsmQuaternionList.Add(new FsmQuaternion(quaternionVariable));
      }
      foreach (FsmObject objectVariable in variables.ObjectVariables)
      {
        if (variablesUsedByStates.Contains(objectVariable.Name))
          fsmObjectList.Add(new FsmObject(objectVariable));
      }
      foreach (FsmMaterial materialVariable in variables.MaterialVariables)
      {
        if (variablesUsedByStates.Contains(materialVariable.Name))
          fsmMaterialList.Add(new FsmMaterial((FsmObject) materialVariable));
      }
      foreach (FsmTexture textureVariable in variables.TextureVariables)
      {
        if (variablesUsedByStates.Contains(textureVariable.Name))
          fsmTextureList.Add(new FsmTexture((FsmObject) textureVariable));
      }
      foreach (FsmString stringVariable in variables.StringVariables)
      {
        if (variablesUsedByStates.Contains(stringVariable.Name))
          fsmStringList.Add(new FsmString(stringVariable));
      }
      foreach (FsmEnum enumVariable in variables.EnumVariables)
      {
        if (variablesUsedByStates.Contains(enumVariable.Name))
          fsmEnumList.Add(new FsmEnum(enumVariable));
      }
      foreach (FsmArray arrayVariable in variables.ArrayVariables)
      {
        if (variablesUsedByStates.Contains(arrayVariable.Name))
          fsmArrayList.Add(new FsmArray(arrayVariable));
      }
      return new FsmVariables()
      {
        FloatVariables = fsmFloatList.ToArray(),
        IntVariables = fsmIntList.ToArray(),
        BoolVariables = fsmBoolList.ToArray(),
        GameObjectVariables = fsmGameObjectList.ToArray(),
        ColorVariables = fsmColorList.ToArray(),
        Vector2Variables = fsmVector2List.ToArray(),
        Vector3Variables = fsmVector3List.ToArray(),
        RectVariables = fsmRectList.ToArray(),
        StringVariables = fsmStringList.ToArray(),
        QuaternionVariables = fsmQuaternionList.ToArray(),
        ObjectVariables = fsmObjectList.ToArray(),
        MaterialVariables = fsmMaterialList.ToArray(),
        TextureVariables = fsmTextureList.ToArray(),
        EnumVariables = fsmEnumList.ToArray(),
        ArrayVariables = fsmArrayList.ToArray()
      };
    }

    public static bool VariableInSourceExistsInTarget(UnityEngine.Object source, Fsm target)
    {
      List<FsmVariable> fsmVariableList1 = FsmVariable.GetFsmVariableList(source);
      List<FsmVariable> fsmVariableList2 = FsmVariable.GetFsmVariableList(target.OwnerObject);
      foreach (FsmVariable fsmVariable in fsmVariableList1)
      {
        if (FsmVariable.VariableNameUsed(fsmVariableList2, fsmVariable.Name))
          return true;
      }
      return false;
    }

    [Obsolete("Use FsmClipboard instead.")]
    private string GenerateCopyName(FsmState state)
    {
      if (!this.StateNameExists(state, state.Name))
        return state.Name;
      string str = StringUtils.IncrementStringCounter(state.Name);
      while (this.StateNameExists(state, str))
        str = StringUtils.IncrementStringCounter(str);
      return str;
    }

    [Obsolete("Use FsmClipboard.PasteVariables instead.")]
    public static void PasteVariables(Fsm toFsm, UnityEngine.Object fromFsm, bool overwriteValues = false)
    {
      List<FsmVariable> fsmVariableList1 = FsmVariable.GetFsmVariableList(fromFsm);
      List<FsmVariable> fsmVariableList2 = FsmVariable.GetFsmVariableList(toFsm.OwnerObject);
      Dictionary<string, string> categoryMap = new Dictionary<string, string>();
      foreach (FsmVariable fsmVariable in fsmVariableList2)
      {
        if (!string.IsNullOrEmpty(fsmVariable.Category))
          categoryMap.Add(fsmVariable.Name, fsmVariable.Category);
      }
      foreach (FsmVariable fsmVariable in fsmVariableList1)
      {
        if (!string.IsNullOrEmpty(fsmVariable.Category) && !categoryMap.ContainsKey(fsmVariable.Name))
          categoryMap.Add(fsmVariable.Name, fsmVariable.Category);
      }
      foreach (FsmVariable fsmVariable in fsmVariableList1)
      {
        if (FsmVariable.VariableNameUsed(fsmVariableList2, fsmVariable.Name))
        {
          if (FsmVariable.GetVariableType(fsmVariableList2, fsmVariable.Name) != fsmVariable.Type)
            Debug.LogError((object) string.Format(Strings.Error_Variable_name_already_exists_and_is_of_different_type, (object) fsmVariable.Name));
          if (overwriteValues)
            toFsm.Variables.GetVariable(fsmVariable.Name).RawValue = fsmVariable.NamedVar.RawValue;
        }
        else
        {
          switch (fsmVariable.Type)
          {
            case VariableType.Float:
              toFsm.Variables.FloatVariables = ArrayUtility.Add<FsmFloat>(toFsm.Variables.FloatVariables, new FsmFloat((FsmFloat) fsmVariable.NamedVar));
              break;
            case VariableType.Int:
              toFsm.Variables.IntVariables = ArrayUtility.Add<FsmInt>(toFsm.Variables.IntVariables, new FsmInt((FsmInt) fsmVariable.NamedVar));
              break;
            case VariableType.Bool:
              toFsm.Variables.BoolVariables = ArrayUtility.Add<FsmBool>(toFsm.Variables.BoolVariables, new FsmBool((FsmBool) fsmVariable.NamedVar));
              break;
            case VariableType.GameObject:
              toFsm.Variables.GameObjectVariables = ArrayUtility.Add<FsmGameObject>(toFsm.Variables.GameObjectVariables, new FsmGameObject((FsmGameObject) fsmVariable.NamedVar));
              break;
            case VariableType.String:
              toFsm.Variables.StringVariables = ArrayUtility.Add<FsmString>(toFsm.Variables.StringVariables, new FsmString((FsmString) fsmVariable.NamedVar));
              break;
            case VariableType.Vector2:
              toFsm.Variables.Vector2Variables = ArrayUtility.Add<FsmVector2>(toFsm.Variables.Vector2Variables, new FsmVector2((FsmVector2) fsmVariable.NamedVar));
              break;
            case VariableType.Vector3:
              toFsm.Variables.Vector3Variables = ArrayUtility.Add<FsmVector3>(toFsm.Variables.Vector3Variables, new FsmVector3((FsmVector3) fsmVariable.NamedVar));
              break;
            case VariableType.Color:
              toFsm.Variables.ColorVariables = ArrayUtility.Add<FsmColor>(toFsm.Variables.ColorVariables, new FsmColor((FsmColor) fsmVariable.NamedVar));
              break;
            case VariableType.Rect:
              toFsm.Variables.RectVariables = ArrayUtility.Add<FsmRect>(toFsm.Variables.RectVariables, new FsmRect((FsmRect) fsmVariable.NamedVar));
              break;
            case VariableType.Material:
              toFsm.Variables.MaterialVariables = ArrayUtility.Add<FsmMaterial>(toFsm.Variables.MaterialVariables, new FsmMaterial((FsmObject) fsmVariable.NamedVar));
              break;
            case VariableType.Texture:
              toFsm.Variables.TextureVariables = ArrayUtility.Add<FsmTexture>(toFsm.Variables.TextureVariables, new FsmTexture((FsmObject) fsmVariable.NamedVar));
              break;
            case VariableType.Quaternion:
              toFsm.Variables.QuaternionVariables = ArrayUtility.Add<FsmQuaternion>(toFsm.Variables.QuaternionVariables, new FsmQuaternion((FsmQuaternion) fsmVariable.NamedVar));
              break;
            case VariableType.Object:
              toFsm.Variables.ObjectVariables = ArrayUtility.Add<FsmObject>(toFsm.Variables.ObjectVariables, new FsmObject((FsmObject) fsmVariable.NamedVar));
              break;
            case VariableType.Array:
              toFsm.Variables.ArrayVariables = ArrayUtility.Add<FsmArray>(toFsm.Variables.ArrayVariables, new FsmArray((FsmArray) fsmVariable.NamedVar));
              break;
            case VariableType.Enum:
              toFsm.Variables.EnumVariables = ArrayUtility.Add<FsmEnum>(toFsm.Variables.EnumVariables, new FsmEnum((FsmEnum) fsmVariable.NamedVar));
              break;
          }
          toFsm.Variables.Init();
        }
      }
      toFsm.Variables.RebuildCategoryIDs(categoryMap);
    }

    [Obsolete("Use EditFsmTemplate.CreateTemplateAsset instead.")]
    public static FsmTemplate CreateTemplate()
    {
      string templatesDirectory = PlayMakerPaths.GetTemplatesDirectory();
      string str = EditorPrefs.GetString("PlayMaker.SaveTemplatesDir", templatesDirectory);
      if (!PlayMakerPaths.IsValidTemplateSavePath(str))
        str = templatesDirectory;
      string path = EditorUtility.SaveFilePanel(Strings.Dialog_Save_Template, str, "template", "asset");
      if (path.Length == 0)
        return (FsmTemplate) null;
      if (!PlayMakerPaths.IsValidTemplateSavePath(path))
      {
        Debug.LogWarning((object) ("Invalid Fsm Template save path: " + path));
        EditorUtility.DisplayDialog(Strings.ProductName, Strings.Dialog_Templates_can_only_be_saved_in_the_Project_s_Assets_folder, Strings.OK);
        return (FsmTemplate) null;
      }
      EditorPrefs.SetString("PlayMaker.SaveTemplatesDir", Path.GetDirectoryName(path));
      FsmTemplate instance = (FsmTemplate) ScriptableObject.CreateInstance(typeof (FsmTemplate));
      instance.fsm = new Fsm();
      instance.fsm.Reset((MonoBehaviour) null);
      instance.fsm.UsedInTemplate = instance;
      AssetDatabase.CreateAsset((UnityEngine.Object) instance, path.Substring(Application.dataPath.Length - 6));
      return instance;
    }

    [Obsolete("Use EditFsmTemplate.SaveFsmInNewTemplate(fsm) instead.")]
    public static FsmTemplate CreateTemplate(Fsm fsm)
    {
      FsmTemplate template = FsmBuilder.CreateTemplate();
      if ((UnityEngine.Object) template == (UnityEngine.Object) null)
        return (FsmTemplate) null;
      FsmBuilder.CopyFsmToTemplate(fsm, template);
      EditorUtility.SetDirty((UnityEngine.Object) template);
      FsmErrorChecker.CheckFsmForErrors(template.fsm, true);
      if (FsmErrorChecker.CountFsmErrors(template.fsm) > 0 && EditorUtility.DisplayDialog(Strings.Dialog_Save_Template, Strings.Dialog_CreateTemplate_Errors, Strings.Dialog_Option_Select_Template, Strings.Dialog_Option_Continue))
        FsmEditor.SelectFsm(template.fsm);
      return template;
    }

    [Obsolete("Use EditFsmTemplate.SaveStatesInNewTemplate instead.")]
    public void CreateTemplate(List<FsmState> states)
    {
      FsmTemplate template = FsmBuilder.CreateTemplate();
      if ((UnityEngine.Object) template == (UnityEngine.Object) null)
        return;
      this.CopyStatesToTemplate(states, template);
      if (string.IsNullOrEmpty(template.fsm.StartState))
        template.fsm.StartState = template.fsm.States[0].Name;
      FsmGraphView.MoveAllStatesToOrigin(template.fsm);
      EditorUtility.SetDirty((UnityEngine.Object) template);
    }

    [Obsolete("Use FsmClipboard.PasteStates instead.")]
    public List<FsmState> PasteStatesFromClipboard(Vector2 position) => this.PasteStatesFromTemplate(FsmBuilder.clipboard, position);

    [Obsolete("Use FsmClipboard.PasteTemplate instead.")]
    public List<FsmState> PasteStatesFromTemplate(
      FsmTemplate template,
      Vector2 position,
      bool checkStartState = false)
    {
      if (template.fsm.States.Length == 0)
        return (List<FsmState>) null;
      FsmEditor.RecordUndo(Strings.Menu_GraphView_Paste_States);
      if (this.targetFsm.DataVersion != template.fsm.DataVersion)
      {
        template.fsm.DataVersion = this.targetFsm.DataVersion;
        template.fsm.SaveActions();
      }
      List<FsmState> fsmStateList1 = new List<FsmState>();
      foreach (FsmState state in template.fsm.States)
        fsmStateList1.Add(FsmBuilder.CopyState(state));
      List<FsmTransition> fsmTransitionList = new List<FsmTransition>();
      foreach (FsmTransition globalTransition in template.fsm.GlobalTransitions)
        fsmTransitionList.Add(FsmBuilder.CopyTransition(globalTransition));
      List<FsmState> fsmStateList2 = new List<FsmState>((IEnumerable<FsmState>) this.targetFsm.States);
      fsmStateList2.AddRange((IEnumerable<FsmState>) fsmStateList1);
      this.targetFsm.States = fsmStateList2.ToArray();
      foreach (FsmState state in fsmStateList1)
      {
        state.Fsm = this.targetFsm;
        string copyName = this.GenerateCopyName(state);
        if (checkStartState && template.fsm.StartState == state.Name && Dialogs.YesNoDialog(Strings.Dialog_Replace_Start_State, Strings.Dialog_Replace_Start_State_Description))
          this.targetFsm.StartState = copyName;
        foreach (FsmState fsmState in fsmStateList1)
        {
          foreach (FsmTransition transition in fsmState.Transitions)
          {
            if (!FsmBuilder.StateSelectionContainsState((IEnumerable<FsmState>) fsmStateList1, transition.ToState))
            {
              transition.ToState = "";
              transition.ToFsmState = (FsmState) null;
            }
            if (transition.ToState == state.Name)
              transition.ToState = copyName;
          }
        }
        foreach (FsmTransition fsmTransition in fsmTransitionList)
        {
          if (fsmTransition.ToState == state.Name)
            fsmTransition.ToState = copyName;
        }
        state.Name = copyName;
        FsmGraphView.TranslateState(state, position);
      }
      fsmTransitionList.AddRange((IEnumerable<FsmTransition>) this.targetFsm.GlobalTransitions);
      this.targetFsm.GlobalTransitions = fsmTransitionList.ToArray();
      foreach (FsmState state in fsmStateList1)
        FsmEditor.GraphView.UpdateStateSize(state);
      FsmBuilder.PasteVariables(this.targetFsm, (UnityEngine.Object) template);
      this.targetFsm.Reload();
      FsmEditor.SetFsmDirty(true);
      FsmEditor.UpdateActionUsage();
      FsmEditor.UpdateFsmInfo();
      Keyboard.ResetFocus();
      return fsmStateList1;
    }

    [Obsolete("Use FsmClipboard.PasteActions instead.")]
    public List<FsmStateAction> PasteActionsFromTemplate(
      FsmTemplate template,
      FsmState toState,
      int atIndex,
      bool undo = true)
    {
      if (atIndex == -1 || (UnityEngine.Object) template == (UnityEngine.Object) null || template.fsm == null)
        return (List<FsmStateAction>) null;
      FsmState state = FsmBuilder.CopyState(template.fsm.States[0]);
      if (state == null)
        return (List<FsmStateAction>) null;
      if (undo)
        FsmEditor.RecordUndo(Strings.Menu_Paste_Actions);
      state.Fsm = template.fsm;
      state.LoadActions();
      List<FsmStateAction> fsmStateActionList1 = new List<FsmStateAction>();
      int actionCount = state.ActionData.ActionCount;
      for (int actionIndex = 0; actionIndex < actionCount; ++actionIndex)
      {
        FsmStateAction action = state.ActionData.CreateAction(state, actionIndex);
        fsmStateActionList1.Add(action);
      }
      List<FsmStateAction> fsmStateActionList2 = new List<FsmStateAction>((IEnumerable<FsmStateAction>) toState.Actions);
      fsmStateActionList2.InsertRange(atIndex, (IEnumerable<FsmStateAction>) fsmStateActionList1);
      toState.Actions = fsmStateActionList2.ToArray();
      FsmEditor.SaveActions(toState);
      FsmBuilder.PasteVariables(this.targetFsm, (UnityEngine.Object) template);
      this.targetFsm.Reload();
      FsmEditor.SetFsmDirty(true);
      FsmEditor.UpdateActionUsage();
      FsmEditor.UpdateFsmInfo();
      List<FsmStateAction> fsmStateActionList3 = new List<FsmStateAction>();
      for (int index = atIndex; index < atIndex + actionCount; ++index)
        fsmStateActionList3.Add(toState.Actions[index]);
      return fsmStateActionList3;
    }

    private static bool StateSelectionContainsState(IEnumerable<FsmState> states, string stateName)
    {
      foreach (FsmState state in states)
      {
        if (state.Name == stateName)
          return true;
      }
      return false;
    }
  }
}
