// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.CustomActionEditors
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  internal class CustomActionEditors
  {
    private static Dictionary<Type, Type> editorsLookup;
    private static readonly Dictionary<FsmStateAction, CustomActionEditor> customEditors = new Dictionary<FsmStateAction, CustomActionEditor>();

    public static List<string> ActionsWithCustomEditors()
    {
      if (CustomActionEditors.editorsLookup == null)
        CustomActionEditors.Rebuild();
      List<string> stringList = new List<string>();
      if (CustomActionEditors.editorsLookup != null)
      {
        foreach (Type key in CustomActionEditors.editorsLookup.Keys)
          stringList.Add(key.Name);
      }
      return stringList;
    }

    public static int InstanceCount() => CustomActionEditors.customEditors.Count;

    public static bool HasCustomEditor(Type action)
    {
      if (CustomActionEditors.editorsLookup == null)
        CustomActionEditors.Rebuild();
      return CustomActionEditors.editorsLookup != null && CustomActionEditors.editorsLookup.ContainsKey(action);
    }

    public static Type GetCustomEditor(Type actionType)
    {
      if (CustomActionEditors.editorsLookup == null)
        CustomActionEditors.Rebuild();
      Type type = (Type) null;
      if (CustomActionEditors.editorsLookup != null)
        CustomActionEditors.editorsLookup.TryGetValue(actionType, out type);
      return type;
    }

    public static CustomActionEditor GetCustomEditor(FsmStateAction action)
    {
      CustomActionEditor instance;
      CustomActionEditors.customEditors.TryGetValue(action, out instance);
      if (instance == null)
      {
        Type customEditor = CustomActionEditors.GetCustomEditor(action.GetType());
        if (customEditor != null)
        {
          instance = (CustomActionEditor) Activator.CreateInstance(customEditor);
          instance.target = action;
          instance.OnEnable();
          CustomActionEditors.customEditors.Add(action, instance);
        }
      }
      return instance;
    }

    public static void Init() => CustomActionEditors.ClearCache();

    public static void ClearCache()
    {
      foreach (CustomActionEditor customActionEditor in CustomActionEditors.GetAllCustomActionEditors())
        customActionEditor?.OnDisable();
      CustomActionEditors.customEditors.Clear();
    }

    private static void Clear() => CustomActionEditors.editorsLookup = new Dictionary<Type, Type>();

    public static List<CustomActionEditor> GetAllCustomActionEditors()
    {
      List<CustomActionEditor> customActionEditorList = new List<CustomActionEditor>();
      foreach (KeyValuePair<FsmStateAction, CustomActionEditor> customEditor in CustomActionEditors.customEditors)
      {
        if (customEditor.Value != null && !customActionEditorList.Contains(customEditor.Value))
          customActionEditorList.Add(customEditor.Value);
      }
      return customActionEditorList;
    }

    public static void Rebuild()
    {
      CustomActionEditors.Clear();
      foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
      {
        try
        {
          foreach (Type exportedType in assembly.GetExportedTypes())
          {
            if (typeof (CustomActionEditor).IsAssignableFrom(exportedType) && exportedType.IsClass && !exportedType.IsAbstract)
            {
              CustomActionEditorAttribute attribute = CustomAttributeHelpers.GetAttribute<CustomActionEditorAttribute>(exportedType);
              if (attribute != null)
                CustomActionEditors.editorsLookup.Add(attribute.InspectedType, exportedType);
            }
          }
        }
        catch (Exception ex)
        {
          NotSupportedException supportedException = ex as NotSupportedException;
        }
      }
    }
  }
}
