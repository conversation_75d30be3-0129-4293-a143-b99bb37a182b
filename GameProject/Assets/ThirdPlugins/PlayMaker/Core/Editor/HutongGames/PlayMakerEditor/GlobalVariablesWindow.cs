// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.GlobalVariablesWindow
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class GlobalVariablesWindow : BaseEditorWindow
  {
    private static NamedVariable delayedSelect;
    private static GlobalVariablesWindow instance;
    private FsmVariablesEditor fsmVariablesEditor;

    public static void SelectAfterOpening(NamedVariable variable) => GlobalVariablesWindow.delayedSelect = variable;

    public override void InitWindowTitle() => this.SetTitle(Strings.GlobalsWindow_Title);

    public override void Initialize()
    {
      this.isToolWindow = true;
      if ((UnityEngine.Object) GlobalVariablesWindow.instance == (UnityEngine.Object) null)
        GlobalVariablesWindow.instance = this;
      this.minSize = new Vector2(200f, 200f);
      this.fsmVariablesEditor = new FsmVariablesEditor((EditorWindow) this, (UnityEngine.Object) FsmVariables.GlobalsComponent);
      this.fsmVariablesEditor.SettingsButtonClicked += new EditorApplication.CallbackFunction(this.DoSettingsMenu);
      this.fsmVariablesEditor.VariableContextClicked += new FsmVariablesEditor.ContextClickVariable(this.DoVariableContextMenu);
      this.fsmVariablesEditor.RefreshButtonClicked += new Action(FsmSearch.UpdateAll);
      if (GlobalVariablesWindow.delayedSelect == null)
        return;
      GlobalVariablesWindow.SelectVariable(GlobalVariablesWindow.delayedSelect);
      GlobalVariablesWindow.delayedSelect = (NamedVariable) null;
    }

    public override void DoGUI()
    {
      EditorGUIUtility.wideMode = FsmEditorSettings.InspectorWideMode;
      this.fsmVariablesEditor.SetTarget((UnityEngine.Object) FsmVariables.GlobalsComponent);
      this.fsmVariablesEditor.OnGUI();
      GUILayout.BeginVertical(FsmEditorStyles.TopBarBG);
      EditorGUILayout.HelpBox(Strings.GlobalVariablesWindow_Note_Asset_Location, MessageType.Info);
      GUILayout.EndVertical();
    }

    public static void ResetView()
    {
      if (!((UnityEngine.Object) GlobalVariablesWindow.instance != (UnityEngine.Object) null))
        return;
      GlobalVariablesWindow.instance.fsmVariablesEditor.Reset();
    }

    public static void UpdateView()
    {
      if (!((UnityEngine.Object) GlobalVariablesWindow.instance != (UnityEngine.Object) null))
        return;
      GlobalVariablesWindow.instance.fsmVariablesEditor.UpdateView();
    }

    private void DoSettingsMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent("Auto Refresh Used Counts"), FsmEditorSettings.AutoRefreshFsmInfo, new GenericMenu.MenuFunction(EditorCommands.ToggleAutoRefreshFsmInfo));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Debug_Variable_Values), FsmEditorSettings.DebugVariables, new GenericMenu.MenuFunction(EditorCommands.ToggleDebugVariables));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Select_Globals_Asset), false, (GenericMenu.MenuFunction) (() => Selection.activeObject = (UnityEngine.Object) FsmVariables.GlobalsComponent));
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Menu_Online_Help), false, new GenericMenu.MenuFunction(GlobalVariablesWindow.OpenOnlineHelp));
      genericMenu.ShowAsContext();
    }

    private void OnFocus()
    {
      if (!FsmEditorSettings.AutoRefreshFsmInfo || EditorApplication.isPlayingOrWillChangePlaymode)
        return;
      FsmSearch.RefreshAll();
      GlobalVariablesWindow.UpdateView();
    }

    private static void OpenOnlineHelp() => EditorCommands.OpenWikiPage(WikiPages.VariableManager);

    private void DoVariableContextMenu(FsmVariable variable)
    {
      GenericMenu genericMenu = new GenericMenu();
      List<FsmInfo> variablesUsageList = FsmSearch.GetGlobalVariablesUsageList(variable.NamedVar);
      if (variablesUsageList.Count == 0)
      {
        genericMenu.AddDisabledItem(new GUIContent(Strings.Menu_No_FSMs_use_this_variable));
      }
      else
      {
        foreach (FsmInfo fsmInfo in variablesUsageList)
          genericMenu.AddItem(new GUIContent(Labels.GetFullFsmLabel(fsmInfo.fsm)), FsmEditor.SelectedFsm == fsmInfo.fsm, new GenericMenu.MenuFunction2(EditorCommands.SelectFsm), (object) fsmInfo.fsm);
      }
      genericMenu.ShowAsContext();
    }

    public static void SelectVariable(NamedVariable variable)
    {
      if ((UnityEngine.Object) GlobalVariablesWindow.instance == (UnityEngine.Object) null)
        return;
      GlobalVariablesWindow.instance.fsmVariablesEditor.SelectVariable(variable.Name);
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("GlobalVariablesWindow: " + message));
  }
}
