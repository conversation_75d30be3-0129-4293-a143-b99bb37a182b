// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Localization
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.Diagnostics;
using System.Globalization;
using System.Resources;

namespace HutongGames.PlayMakerEditor
{
  public class Localization
  {
    [Conditional("DEBUG_LOG")]
    public static void DebugAvailableCultures()
    {
      // ResourceManager resourceManager = Strings.ResourceManager;
      // string str = "";
      // foreach (CultureInfo culture in CultureInfo.GetCultures(CultureTypes.AllCultures))
      // {
      //   if (resourceManager.GetResourceSet(culture, true, false) != null)
      //     str = str + culture.EnglishName + Environment.NewLine;
      // }
      // UnityEngine.Debug.Log((object) (Strings.Label_PlayMaker_Supported_Languages + Environment.NewLine + str));
    }
  }
}
