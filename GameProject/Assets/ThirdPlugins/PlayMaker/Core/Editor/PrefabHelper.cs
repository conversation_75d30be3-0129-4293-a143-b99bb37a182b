// Decompiled with JetBrains decompiler
// Type: PrefabHelper
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using UnityEditor;
using UnityEngine;

public static class PrefabHelper
{
  public static Object GetPrefabDefinition(this Object uo) => uo == (Object) null ? (Object) null : PrefabUtility.GetCorrespondingObjectFromSource<Object>(uo);

  public static bool IsPrefabInstance(this Object uo) => !(uo == (Object) null) && uo.GetPrefabDefinition() != (Object) null;

  public static bool IsPrefabDefinition(this Object uo) => !(uo == (Object) null) && uo.GetPrefabDefinition() == (Object) null && PrefabUtility.GetPrefabInstanceHandle(uo) != (Object) null;

  public static bool IsConnectedPrefabInstance(this Object go) => !(go == (Object) null) && go.IsPrefabInstance() && PrefabUtility.GetPrefabInstanceHandle(go) != (Object) null;

  public static bool IsDisconnectedPrefabInstance(this Object go) => !(go == (Object) null) && go.IsPrefabInstance() && PrefabUtility.GetPrefabInstanceHandle(go) == (Object) null;

  public static bool IsSceneBound(this Object uo)
  {
    if (uo == (Object) null)
      return false;
    switch (uo)
    {
      case GameObject _ when !uo.IsPrefabDefinition():
        return true;
      case Component _:
        return !((Component) uo).gameObject.IsPrefabDefinition();
      default:
        return false;
    }
  }
}
