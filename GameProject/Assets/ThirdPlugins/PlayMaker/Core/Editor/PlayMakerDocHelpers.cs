// Decompiled with JetBrains decompiler
// Type: PlayMakerDocHelpers
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Extensions;
using HutongGames.PlayMaker;
using HutongGames.PlayMakerEditor;
using HutongGames.Utility;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using UnityEditor;
using UnityEngine;

[Localizable(false)]
public class PlayMakerDocHelpers : BaseEditorWindow
{
  private string screenshotsSavePath;
  private string htmlSavePath;
  private string jsonSavePath;
  private string imagesUrl;
  private string[] categoryChoices;
  private int selectedCategory;
  private string selectedCategoryName;
  private bool capturingGUI;
  private int actionIndex;
  private FsmStateAction previewAction;
  private string previewActionName;
  private bool saveScreenshot;
  private List<string> exclude = new List<string>()
  {
    "PlayMakerWheelFrictionCurveClass"
  };
  private Stopwatch stopwatch;

  public void CaptureActionScreenshots() => this.StartCaptureActionScreenshots();

  public override void Initialize()
  {
    this.SetTitle("PlayMaker Helpers");
    this.screenshotsSavePath = EditorPrefs.GetString("PlayMaker.DocHelpers.ScreenshotsSavePath", "PlayMaker/Screenshots/");
    this.htmlSavePath = EditorPrefs.GetString("PlayMaker.DocHelpers.HtmlSavePath", "PlayMaker/Html/");
    this.jsonSavePath = EditorPrefs.GetString("PlayMaker.DocHelpers.JsonSavePath", "PlayMaker/Json/");
    this.imagesUrl = EditorPrefs.GetString("PlayMaker.DocHelpers.ImagesUrl", "http://mywebsite.com/docs/img/");
    this.selectedCategory = Math.Min(EditorPrefs.GetInt("PlayMaker.DocHelpers.SelectedCategory", 0), Actions.Categories.Count);
    this.minSize = new Vector2(350f, 400f);
    this.maxSize = new Vector2(350f, 900f);
    List<string> stringList = new List<string>((IEnumerable<string>) Actions.Categories);
    stringList.Insert(0, "All Categories");
    stringList.Remove("Tests");
    stringList.Remove("Recent");
    stringList.Remove("Favorites");
    this.categoryChoices = stringList.ToArray();
  }

  private void OnDisable() => this.SavePreferences();

  public override void DoGUI()
  {
    FsmEditorStyles.Init();
    EditorGUIUtility.wideMode = true;
    FsmEditorGUILayout.ToolWindowLargeTitle((EditorWindow) this, "Doc Helpers");
    if (FsmEditor.Instance == null)
    {
      GUILayout.Label("Please open the PlayMaker Editor...");
    }
    else
    {
      if (!this.capturingGUI)
      {
        EditorGUILayout.HelpBox("This tool generates the screenshots and html required to document actions in the online wiki.", MessageType.Info);
        GUILayout.Label("Source", EditorStyles.boldLabel);
        this.selectedCategory = EditorGUILayout.Popup("Action Category", this.selectedCategory, this.categoryChoices);
        this.selectedCategoryName = this.categoryChoices[this.selectedCategory];
        FsmEditorGUILayout.Divider();
        GUILayout.Label("Export Settings", EditorStyles.boldLabel);
        this.screenshotsSavePath = EditorGUILayout.TextField("Save Screenshots", this.screenshotsSavePath);
        this.htmlSavePath = EditorGUILayout.TextField("Save Html", this.htmlSavePath);
        this.jsonSavePath = EditorGUILayout.TextField("Save Json", this.jsonSavePath);
        this.imagesUrl = EditorGUILayout.TextField("Images Url", this.imagesUrl);
        FsmEditorGUILayout.Divider();
        if (GUILayout.Button("Capture Screenshots", GUILayout.MinHeight(30f)))
          this.StartCaptureActionScreenshots();
        if (GUILayout.Button("Generate Wiki Html", GUILayout.MinHeight(30f)))
        {
          this.GenerateActionWikiHtml();
          this.GenerateActionsEnum();
        }
        if (GUILayout.Button("Generate Doc Json", GUILayout.MinHeight(30f)))
          this.GenerateActionJson();
        FsmEditorGUILayout.Divider();
        if (GUI.changed)
          this.SavePreferences();
        GUILayout.FlexibleSpace();
        GUILayout.Label("Resize window height to fit largest action screenshot...");
      }
      else
      {
        ActionEditor.PreviewMode = true;
        FsmEditorGUILayout.AutoLabelWidth(this.position.width);
        EditorGUILayout.Space();
        EditorGUILayout.BeginVertical(FsmEditorStyles.BottomBarBG);
        if (this.previewAction != null)
          FsmEditor.ActionEditor.OnGUI(this.previewAction);
        else
          GUILayout.Space(20f);
        EditorGUILayout.EndVertical();
        if (Event.current.type == UnityEngine.EventType.Repaint)
        {
          this.SaveActionScreenshot();
          this.NextActionScreenshot();
        }
        GUILayout.FlexibleSpace();
        if (this.capturingGUI && EditorUtility.DisplayCancelableProgressBar("Saving Action Screenshots...", "", (float) this.actionIndex / (float) Actions.List.Count))
        {
          this.FinishCapture();
          GUIUtility.ExitGUI();
        }
      }
      ActionEditor.PreviewMode = false;
    }
  }

  private void SavePreferences()
  {
    EditorPrefs.SetString("PlayMaker.DocHelpers.ImagesUrl", this.imagesUrl);
    EditorPrefs.SetInt("PlayMaker.DocHelpers.SelectedCategory", this.selectedCategory);
    EditorPrefs.SetString("PlayMaker.DocHelpers.ScreenshotsSavePath", this.screenshotsSavePath);
    EditorPrefs.SetString("PlayMaker.DocHelpers.HtmlSavePath", this.htmlSavePath);
  }

  private void StartCaptureActionScreenshots()
  {
    UnityEngine.Debug.Log((object) ("StartCaptureActionScreenshots: " + this.selectedCategoryName));
    if (!Files.CreateFilePath(this.screenshotsSavePath))
    {
      UnityEngine.Debug.LogError((object) ("Could not create Screenshots folder: " + this.screenshotsSavePath));
    }
    else
    {
      this.stopwatch = new Stopwatch();
      this.stopwatch.Start();
      this.capturingGUI = true;
      this.actionIndex = 0;
      this.NextActionScreenshot();
    }
  }

  private void GenerateActionJson()
  {
    UnityEngine.Debug.Log((object) ("GenerateActionJson: " + this.selectedCategoryName));
    if (!Files.CreateFilePath(this.jsonSavePath))
    {
      UnityEngine.Debug.LogError((object) ("Could not create Json Directory: " + this.jsonSavePath));
    }
    else
    {
      string str1 = "";
      string str2 = "";
      StreamWriter text = File.CreateText(this.jsonSavePath + "/actions.json");
      text.WriteLine("[");
      this.actionIndex = -1;
      while (this.actionIndex < Actions.List.Count - 1)
      {
        ++this.actionIndex;
        if (EditorUtility.DisplayCancelableProgressBar("Saving Action Json...", "", (float) this.actionIndex / (float) Actions.List.Count))
          return;
        if (this.selectedCategory <= 0 || !(Actions.CategoryLookup[this.actionIndex] != this.selectedCategoryName))
        {
          System.Type type = Actions.List[this.actionIndex];
          string str3 = Labels.StripNamespace(type.ToString());
          string actionLabel = Labels.GetActionLabel(type);
          if (!this.exclude.Contains(str3))
          {
            string category = Actions.GetCategory(type);
            if (!this.SkipCategory(category))
            {
              str1 = str1 + actionLabel + Environment.NewLine;
              text.WriteLine("\t{");
              text.WriteLine("\t\t\"name\":\"{0}\",", (object) actionLabel);
              text.WriteLine("\t\t\"category\":\"{0}\",", (object) category);
              string str4 = Actions.GetTooltipCodedText(type);
              if (string.IsNullOrEmpty(str4))
              {
                str4 = "TODO";
                str2 = str2 + actionLabel + ": Description\n";
              }
              text.WriteLine("\t\t\"image\":\"./images/{0}.png\",", (object) Labels.StripNamespace(type.ToString()).Replace(" ", ""));
              text.WriteLine("\t\t\"description\":\"{0}\",", (object) str4);
              FieldInfo[] fields = ActionData.GetFields(type);
              text.WriteLine("\t\t\"params\": [");
              for (int index = 0; index < fields.Length; ++index)
              {
                FieldInfo field = fields[index];
                if (!field.IsNotSerialized)
                {
                  string str5 = Actions.GetTooltipCodedText(field);
                  if (string.IsNullOrEmpty(str5))
                  {
                    str5 = "TODO";
                    str2 += string.Format("{0}: {1}\n", (object) actionLabel, (object) field.Name);
                  }
                  text.WriteLine("\t\t\t{");
                  text.WriteLine("\t\t\t\t\"name\":\"{0}\",", (object) ObjectNames.NicifyVariableName(field.Name));
                  text.WriteLine("\t\t\t\t\"description\":\"{0}\"", (object) str5);
                  text.WriteLine("\t\t\t}" + (index < fields.Length - 1 ? "," : ""));
                }
              }
              text.WriteLine("\t\t]");
              text.WriteLine("\t\t,\"seeAlso\": [");
              List<SeeAlsoAttribute> list = CustomAttributeHelpers.GetAttributes<SeeAlsoAttribute>(type).ToList<SeeAlsoAttribute>();
              if (list.Count > 0)
              {
                for (int index = 0; index < list.Count; ++index)
                {
                  SeeAlsoAttribute seeAlsoAttribute = list[index];
                  text.WriteLine("\t\t\t{{\"li\":\"{0}\"}}" + (index < list.Count - 1 ? "," : ""), (object) StringUtils.Escape(seeAlsoAttribute.Text));
                }
              }
              text.WriteLine("\t\t]");
              if (this.actionIndex < Actions.List.Count - 3)
                text.WriteLine("\t},");
            }
          }
        }
      }
      text.WriteLine("\t}");
      text.WriteLine("]");
      text.Close();
      EditorUtility.ClearProgressBar();
      using (StreamWriter streamWriter = new StreamWriter(Path.Combine(this.jsonSavePath, "_allActions.txt")))
        streamWriter.Write(str1);
      using (StreamWriter streamWriter = new StreamWriter(Path.Combine(this.jsonSavePath, "_missingTooltips.txt")))
        streamWriter.Write(str2);
    }
  }

  private void GenerateActionCategoriesWikiHtml()
  {
    if (this.selectedCategory == 0)
    {
      foreach (string category in Actions.Categories)
        this.GenerateActionCategoryWikiHtml(category);
    }
    else
      this.GenerateActionCategoryWikiHtml(this.selectedCategoryName);
  }

  private void GenerateActionCategoryWikiHtml(string actionCategory)
  {
    string fullPath = Path.GetFullPath(Application.dataPath + "/../" + Path.Combine(this.htmlSavePath, actionCategory ?? "") + ".txt");
    if (!Files.CreateFilePath(fullPath))
      return;
    StreamWriter text = File.CreateText(fullPath);
    text.WriteLine("<p>TODO: Category description...</p>");
    text.WriteLine("<div id=\"linkList\"><ul>");
    List<System.Type> typeList = new List<System.Type>((IEnumerable<System.Type>) Actions.List);
    typeList.Sort((Comparison<System.Type>) ((x, y) => string.Compare(x.Name, y.Name, StringComparison.Ordinal)));
    foreach (System.Type objType in typeList)
    {
      if (Actions.GetActionCategory(objType) == actionCategory)
        text.WriteLine("<li>" + Labels.NicifyVariableName(Labels.StripNamespace(objType.ToString())) + "</li>");
    }
    text.WriteLine("</ul></div>");
    text.Close();
  }

  private void GenerateActionWikiHtml()
  {
    UnityEngine.Debug.Log((object) ("GenerateActionWikiHtml: " + this.selectedCategoryName));
    if (!Files.CreateFilePath(this.htmlSavePath))
      return;
    this.GenerateActionCategoriesWikiHtml();
    this.actionIndex = 0;
    while (this.actionIndex < Actions.List.Count)
    {
      EditorUtility.DisplayProgressBar("Saving Action Wiki Html...", "Press Escape to cancel", (float) this.actionIndex / (float) Actions.List.Count);
      if (Event.current.keyCode == KeyCode.Escape)
        return;
      if (this.selectedCategory > 0 && Actions.CategoryLookup[this.actionIndex] != this.selectedCategoryName)
      {
        ++this.actionIndex;
      }
      else
      {
        System.Type type = Actions.List[this.actionIndex];
        string str1 = Labels.StripNamespace(type.ToString());
        string fullPath = Path.GetFullPath(Application.dataPath + "/../" + this.htmlSavePath + "/" + Actions.GetCategory(type) + "/");
        if (!Files.CreateFilePath(fullPath))
        {
          UnityEngine.Debug.LogError((object) ("Could not create category path: " + fullPath));
        }
        else
        {
          StreamWriter text = File.CreateText(fullPath + str1 + ".txt");
          text.WriteLine("<div id=\"actionImg\"><p><img src=\"" + this.imagesUrl + str1 + ".png\" title=\"\" width=\"350\" /></p></div>");
          string str2 = Actions.GetTooltip(type);
          if (string.IsNullOrEmpty(str2))
            str2 = "TODO";
          text.WriteLine("<div id=\"actionDesc\">\n<p>");
          text.WriteLine(str2 + "</p>\n</div>");
          foreach (FieldInfo field in ActionData.GetFields(type))
          {
            string str3 = Actions.GetTooltip(field);
            if (string.IsNullOrEmpty(str3))
              str3 = "TODO";
            text.WriteLine("<div id=\"paramRow\">");
            text.WriteLine("\t<div id=\"paramName\">");
            text.WriteLine(ObjectNames.NicifyVariableName(field.Name) + "</div>");
            text.WriteLine("\t<div id=\"paramDesc\">");
            text.WriteLine(str3 + "</div>");
            text.WriteLine("</div>");
            text.WriteLine("");
          }
          text.Close();
          ++this.actionIndex;
        }
      }
    }
    EditorUtility.ClearProgressBar();
  }

  private void GenerateActionsEnum()
  {
    StreamWriter text = File.CreateText(Path.Combine(this.htmlSavePath, "ActionsEnum.txt"));
    for (int index = 0; index < Actions.List.Count; ++index)
    {
      if (!(Actions.CategoryLookup[index] == "Tests"))
      {
        string name = Labels.StripNamespace(Actions.List[index].ToString());
        if (Enum.IsDefined(typeof (WikiPages), (object) name))
        {
          WikiPages wikiPages = PlayMakerDocHelpers.StringToEnum<WikiPages>(name);
          text.WriteLine("\t\t\t" + name + " = " + (object) (int) wikiPages + ",");
        }
        else
          text.WriteLine("\t\t\t" + name + " = TODO,");
      }
    }
    text.Close();
  }

  private static T StringToEnum<T>(string name) => (T) Enum.Parse(typeof (T), name);

  private void NextActionScreenshot()
  {
    if (this.actionIndex >= Actions.List.Count)
    {
      this.FinishCapture();
    }
    else
    {
      this.saveScreenshot = false;
      System.Type type = Actions.List[this.actionIndex];
      this.previewActionName = Labels.StripNamespace(type.ToString());
      if (!this.SkipCategory(Actions.CategoryLookup[this.actionIndex]) && !this.exclude.Contains(this.previewActionName))
      {
        this.previewAction = (FsmStateAction) Activator.CreateInstance(type);
        this.previewAction.Reset();
        this.saveScreenshot = true;
        GUIUtility.hotControl = 0;
        GUIUtility.keyboardControl = 0;
      }
      ++this.actionIndex;
      this.Repaint();
    }
  }

  private bool SkipCategory(string categoryName)
  {
    if (this.selectedCategory > 0)
      return this.selectedCategoryName != categoryName;
    return categoryName == "Tests" || categoryName.EndsWith("Test") || categoryName == "Temp" || categoryName == "PlayMakerInternal";
  }

  private void FinishCapture()
  {
    EditorUtility.ClearProgressBar();
    this.capturingGUI = false;
    this.stopwatch.Stop();
    UnityEngine.Debug.Log((object) ("Captured action screenshots (" + (object) this.stopwatch.Elapsed.TotalSeconds + "s)"));
  }

  private void SaveActionScreenshot()
  {
    if (!this.saveScreenshot)
      return;
    this.SaveScreenshot(Labels.StripNamespace(this.previewActionName), GUILayoutUtility.GetLastRect());
  }

  private void SaveScreenshot(string actionName, Rect region)
  {
    if ((double) region.height < 1.0)
      return;
    region.y = (float) ((double) this.position.height - (double) region.height - 49.0);
    region = region.Scale(EditorGUIUtility.pixelsPerPoint);
    Texture2D tex = new Texture2D((int) region.width, (int) region.height, TextureFormat.RGB24, false);
    tex.ReadPixels(region, 0, 0);
    tex.Apply();
    string fullPath = Path.GetFullPath(Application.dataPath + "/../" + Path.Combine(this.screenshotsSavePath, actionName) + ".png");
    if (!Files.CreateFilePath(fullPath))
      return;
    byte[] png = tex.EncodeToPNG();
    UnityEngine.Object.DestroyImmediate((UnityEngine.Object) tex, true);
    File.WriteAllBytes(fullPath, png);
  }
}
