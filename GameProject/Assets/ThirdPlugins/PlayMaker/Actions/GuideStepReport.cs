using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Logic)]
    [Tooltip("引导步骤上报")]
    public class GuideStepReport : LuaFsmStateAction
    {
        [RequiredField]
        [Tooltip("引导线名称")]
        public FsmInt guideID;
        
        [RequiredField]
        [Tooltip("步骤名称")]
        public FsmInt stepID;

        [Tooltip("是否显示奖励")]
        public FsmBool showReward;
    }
}