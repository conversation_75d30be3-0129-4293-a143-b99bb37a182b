using System;
using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.GameLogic)]
    [Tooltip("选中内城中的建筑")]
    public class UIBuildSelect : LuaFsmStateAction
    {
        [Serializable]
        public enum TargetType
        {
            ID = 1,                     // 按建筑ID来查找
            
            Level = 2,                  // 按等级查找
            Troop = 3,                  // 按兵种查找
        }

        [Serializable]
        public enum LevelType
        {
            LowestLevel = 1,            // 按最低等级查找
            HighestLevel = 2,           // 按最高等级查找
        }

        [Tooltip("查找类型")]
        public TargetType target = TargetType.ID;
        
        [HideIf("HideBuildID")]
        [Tooltip("建筑配置ID")]
        public FsmInt buildID;

        [HideIf("HideLevelType")]
        [Tooltip("选择按最高或最低方式查找建筑")]
        public LevelType levelType = LevelType.LowestLevel;
        
        public bool HideBuildID() => target != TargetType.ID;
        public bool HideLevelType() => target != TargetType.Level;
        
        [Tooltip("是否打开建筑的操作面板")]
        public FsmBool openMenu;
        
        [Tooltip("重置摄像机到建筑的距离")]
        public FsmBool resetDistance = true;
        
        protected override string ToLua(object v)
        {
            if (v is TargetType)
                return ((int) target).ToString();
            else if (v is LevelType)
                return ((int) levelType).ToString();
            return base.ToLua(v);
        }
    }
}