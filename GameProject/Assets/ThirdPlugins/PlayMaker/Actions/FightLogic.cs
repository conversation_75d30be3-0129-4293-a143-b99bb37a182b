using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.GameLogic)]
    [Tooltip("副本战斗玩法")]
    public class FightLogic : LuaFsmStateAction
    {
        [RequiredField] 
        [Toolt<PERSON>("副本战斗ID")]
        public FsmInt fightID;
        
        [Control]
        [Tooltip("是否播放音效")]
        public FsmBool useBGM = false;
        
        [HideIf("HideBGM")]
        [Tooltip("BGM名称")]
        public FsmString backgroundMusic;

        [Tooltip("是否等待战斗结束")] 
        public FsmBool waitForFinish = true;
        
        public bool HideBGM() => !useBGM.Value;
    }
}