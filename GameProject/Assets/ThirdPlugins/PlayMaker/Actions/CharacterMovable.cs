using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Character)]
    [Tooltip("角色：移动设置")]
    public class CharacterMovable : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("想要更改移动设置的对象名称")]
        public FsmString gameObjectName;
        
        [RequiredField] 
        [Tooltip("是否可以操控移动该角色\n【勾】可以操控移动\n【空】不可操控移动\n（仅针对玩家操控，不影响上述Action生效）")]
        public FsmBool movable;
                
        [Tooltip("状态结束是否恢复原来的状态")]
        public FsmBool autoRecovery;
    }
}