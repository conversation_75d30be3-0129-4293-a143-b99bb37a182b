using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.GameLogic)]
    [Tooltip("复合行为")]
    public class Composition : LuaFsmStateAction
    {
        [Serializable]
        public enum RunType 
        {
            Sequence,
            Parallel,
        }
        
        [Tooltip("串行或并行执行下列动画")]
        public RunType runType = RunType.Sequence;
        
        public override void Awake() => this.Enabled = false;

        public override string ToLua()
        {
            return string.Empty;
        }
    }
}