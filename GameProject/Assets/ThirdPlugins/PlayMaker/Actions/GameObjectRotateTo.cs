using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.GameObject)]
    [Tooltip("设置对象朝向另一个对象")]
    public class GameObjectRotateTo : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("需要设置朝向的对象")]
        public FsmString gameObjectName;

        [Control] 
        [Tooltip("朝向位置")] 
        public FsmBool usePosition;
        
        [Toolt<PERSON>("要朝向的对象")]
        [HideIf("HideTargetName")]
        public FsmString targetObjectName;
        
        [Tooltip("要朝向的位置")]
        [HideIf("HideTargetPosition")]
        public FsmVector3 targetPosition;
        
        [Toolt<PERSON>("朝向的时间")]
        public FsmFloat duration = 0f;

        public bool HideTargetName() => usePosition.Value;
        public bool HideTargetPosition() => !usePosition.Value;
    }
}
