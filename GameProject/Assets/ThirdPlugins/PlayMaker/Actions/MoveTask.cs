using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("移动指引任务")]
    public class MoveTask : LuaFsmStateAction
    {
        [Control] 
        [Tooltip("修改任务信息")] 
        public FsmBool taskParam = true;

        [HideIf("HideTaskParam")]
        [Tooltip("任务名称")]
        public FsmString taskName;
        
        [HideIf("HideTaskParam")]
        [Tooltip("任务描述")]
        public FsmString taskDesc;

        [HideIf("HideTaskParam")]
        [Tooltip("角色对象名称")]
        public FsmString objectName = "host001";

        [HideIf("HideTaskParam")]
        [Tooltip("移动位置")]
        public FsmVector3 toPosition;

        [RequiredField]
        [Tooltip("等待时间")]
        public FsmFloat waitTime = 0.0f;

        [RequiredField]
        [Tooltip("是否播放完成动画")]
        public FsmBool actionFinish = false;

        public bool HideTaskParam() => !taskParam.Value;
    }
}