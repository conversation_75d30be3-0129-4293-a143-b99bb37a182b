using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory("GameObject")]
    [Tooltip("对象：加载")]
    public class GameObjectLoad : LuaFsmStateAction
    {
        public enum GameObjectType
        {
            RawPath = 0,
            NPC
        }

        public enum PositionType
        {
            Position = 0,
            Coordinate,
        }

        [Tooltip("GameObject类型")]
        public GameObjectType objectType;

        [HideIf("HideNPCID")] 
        [Tooltip("NPC配置ID")]
        public FsmInt npcID;
        
        [HideIf("HidePrefabPath")] 
        [Tooltip("设置想要加载的预制体路径")]
        public FsmString prefabPath;
        
        [Control]
        [Tooltip("是否将该对象重命名标注\n【勾】重命名标注（勾选后出现新Setting：Object Name）\n【空】不标注")]
        public FsmBool renameObject = false;
        
        [HideIf("HideObectName")] 
        [Tooltip("设置重命名的对象名称")]
        public FsmString objectName;
        
        // [RequiredField] 
        [HideIf("HidePosition")]
        [Tooltip("新对象的坐标")]
        public FsmVector3 position;
        
        // [RequiredField] 
        [HideIf("HideRotation")]
        [Tooltip("新对象的转向")]
        public FsmVector3 rotation;

        // [RequiredField] 
        [HideIf("HideScale")]
        [Tooltip("新对象的缩放")] 
        public FsmVector3 scale = Vector3.one;
        
        [HideIf("HideInnerCoordinate")]
        [Tooltip("新对象的内城逻辑坐标")]
        public FsmVector2 innerObjectCoordinate;

        [HideIf("HideInnerPosition")]
        [Tooltip("新对象的内城局部坐标")]
        public FsmVector2 innerObjectPosition;

        [HideIf("HideInnerSize")]
        [Tooltip("新对象的内城尺寸")]
        public FsmVector2 innerObjectSize;
        
        [HideIf("HideInnerRotation")]
        [Tooltip("新对象的内城转向")]
        public FsmFloat innerObjectRotation;
        
        [Control]
        [HideIf("HideInnerPositionType")]
        [Tooltip("内城坐标类型:\ncoordinate: 逻辑坐标\nposition: 局部坐标")]
        public PositionType innerPositionType = PositionType.Position;

        [Control]
        [Tooltip("是否在内城中使用")]
        public FsmBool useInInnerCity = false;
        
        public bool HideObectName() => !renameObject.Value;
        public bool HidePrefabPath() => objectType != GameObjectType.RawPath;
        public bool HideNPCID() => objectType != GameObjectType.NPC;
        public bool HideInnerPosition() => !useInInnerCity.Value || innerPositionType == PositionType.Coordinate;
        public bool HideInnerCoordinate() => !useInInnerCity.Value || innerPositionType == PositionType.Position;
        public bool HideInnerSize() => !useInInnerCity.Value;
        public bool HideInnerRotation() => !useInInnerCity.Value;
        public bool HidePosition() => useInInnerCity.Value;
        public bool HideRotation() => useInInnerCity.Value;
        public bool HideScale() => useInInnerCity.Value;
        public bool HideInnerPositionType() => !useInInnerCity.Value;
    }
}