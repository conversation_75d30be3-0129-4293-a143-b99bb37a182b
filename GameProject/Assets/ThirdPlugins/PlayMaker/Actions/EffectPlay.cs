using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Effects)]
    [Tooltip("特效：播放")]
    public class EffectPlay : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("设置想要播放的特效路径")]
        public FsmString effectPath;
        
        [RequiredField] 
        [Tooltip("播放特效的坐标")]
        public FsmVector3 position;

        [Control] 
        [Tooltip("是否修改特效缩放比例")]
        public FsmBool changeScale;
        
        [HideIf("HideScale")]
        [Tooltip("特效缩放的比例")]
        public FsmVector3 scale = Vector3.one;
        
        [Control]
        [Tooltip("是否修改旋转")]
        public FsmBool overrideRotation;
    
        [HideIf("HideRotation")]
        public FsmVector3 rotation;
        // [Control] public FsmBool finishDelay;
        // [HideIf("HideDelay")] public FsmFloat delayTime = 0f;
        
        [Control] 
        [Tooltip("是否将该特效重命名标注\n【勾】重命名标注（勾选后出现新Setting：Object Name）\n【空】不标注")]
        public FsmBool renameObject;
        
        [HideIf("HideObjectName")] 
        [Tooltip("设置重命名的特效名称")]
        public FsmString objectName;
        
        [Tooltip("在当前State执行完毕后，是否自动解除此特效\n【勾】解除此特效\n【空】保留此特效")]
        public FsmBool autoRelease;

        [Control]
        [Tooltip("是否修改特效添加的父节点\n默认添加到当场景下\n设置父节点后,位置和旋转都是基于节点下的localPosition和localRotation")]
        public FsmBool changeParent;

        [HideIf("HideChangeParent")] 
        [Tooltip("设置添加父节点")]
        public FsmString parentName;
        
        public bool HideObjectName() => !renameObject.Value;
        public bool HideScale() => !changeScale.Value;
        public bool HideRotation() => !overrideRotation.Value;
        // public bool HideDelay() => !finishDelay.Value;
        public bool HideChangeParent() => !changeParent.Value;
    }
}