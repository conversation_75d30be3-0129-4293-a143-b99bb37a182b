using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Effects)]
    [Tooltip("动作：播放")]
    public class AnimatorPlay : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("设置想要播放动作的对象名称")]
        public FsmString gameObjectName;
        [RequiredField] 
        [Tooltip("设置想要播放的动作名称")]
        public FsmString animationName;
        [Control]
        [Tooltip("是否循环播放动作\n【勾】循环播放动作（勾选后出现新Setting：LoopNum）\n【空】仅播放一次")]
        public FsmBool loop = false;
        [HideIf("HideLoopNum")] 
        [Tooltip("循环播放次数（正数，整数）")]
        public FsmInt loopNum = 1;
        
        public bool HideLoopNum()
        {
            return !loop.Value;
        }
    }
}
