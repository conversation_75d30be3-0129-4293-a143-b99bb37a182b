using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("检测UI页面")]
    public class UIExist : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("所要监测的UI页面名称")]
        public FsmString uiName;
        
        [Tooltip("是否是运行在弱引导线下\n勾选：运行弱引导下\n不勾选：运行强引导下")]
        public FsmBool softGuidance;
    }
}