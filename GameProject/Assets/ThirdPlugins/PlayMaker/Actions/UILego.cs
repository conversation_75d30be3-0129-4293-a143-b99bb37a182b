using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("打开拼接页面")]
    public class UILego : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("拼接ID")]
        public FsmInt legoID;

        [Tooltip("跳过拼接完成的展示动画")]
        public FsmBool skipShow;
        
        [Tooltip("是否显示拼接台面")]
        public FsmBool stageVisible = true;
        
        [Tooltip("是否等待拼接完成")]
        public FsmBool waitingForFinish = false;
    }
}