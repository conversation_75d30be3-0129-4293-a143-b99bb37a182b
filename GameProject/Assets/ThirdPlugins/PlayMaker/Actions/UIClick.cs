using System;
using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("UI按钮点击")]
    public class UIClick : LuaFsmStateAction
    {
        [Serializable]
        public enum ArrowDirection
        {
            Up,
            Down,
            Left,
            Right
        }
        
        [Serializable]
        public enum FingerDirection
        {
            LeftTop,
            LeftBottom,
            RightTop,
            RightBottom,
            Center,
        }
        
        [RequiredField] 
        [Tooltip("UI页面名称")]
        public FsmString uiName;
        
        [RequiredField] 
        [Tooltip("按钮的UI地址")]
        public FsmString buttonPath;

        [Tooltip("是否拦截点击事件\n勾选：拦截\n不勾选：不拦截")]
        public FsmBool interceptable;
        
        [Control]
        [Tooltip("是否偏移坐标")]
        public FsmBool useOffset;

        [HideIf("HideOffset")]
        [Tooltip("位置偏移值(UI分辨率为1334x750)")]
        public FsmVector2 offset;
        
        [Control] 
        [Tooltip("是否手动指定手指方向")]
        public FsmBool useFingerDirection;
        
        [HideIf("HideFingerDirection")]
        [Tooltip("手指方向")]
        public FingerDirection fingerDirection = FingerDirection.LeftTop;

        [Control] 
        [Tooltip("是否显示文本框提示")]
        public FsmBool useTip;
        
        [HideIf("HideTip")]
        [Tooltip("TIP文本框内容")]
        public FsmString tipContent;
        
        [HideIf("HideTip")]
        [Tooltip("TIP箭头位置")]
        public ArrowDirection arrowDirection = ArrowDirection.Up;
        
        // [Tooltip("是否实时跟踪对象位置")]
        // public FsmBool autoUpdate;

        public bool HideOffset() => !useOffset.Value;
        public bool HideTip() => !useTip.Value;
        public bool HideFingerDirection() => !useFingerDirection.Value;
        
        protected override string ToLua(object v)
        {
            if (v is ArrowDirection)
                return string.Format("\"{0}\"", v.ToString().ToLower());
            if (v is FingerDirection)
                return ((int) fingerDirection).ToString();
            return base.ToLua(v);
        }
    }
}