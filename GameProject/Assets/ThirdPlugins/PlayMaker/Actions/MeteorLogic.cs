using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.GameLogic)]
    [Tooltip("发射陨石")]
    public class MeteorLogic : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("陨石发射坐标")]
        public FsmVector3 startPosition;
        
        [RequiredField] 
        [Toolt<PERSON>("陨石投射位置")]
        public FsmVector3 targetPosition;
        
        [RequiredField] 
        [Tooltip("陨石投射尺寸")]
        public FsmVector2 targetSize;
    }
}