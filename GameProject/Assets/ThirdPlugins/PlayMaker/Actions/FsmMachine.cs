using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Logic)]
    public class FsmMachine : LuaFsmStateAction
    {
        public FsmBool useTemplate = false;
        
        [HideIf("HideSubFSM")]
        public PlayMakerFSM subFSM;
        
        [HideIf("HideTempateFSM")]
        public FsmTemplateControl fsmTemplateControl = new FsmTemplateControl();
        
        public bool HideSubFSM() => useTemplate.Value;
        public bool HideTempateFSM() => !useTemplate.Value;

        public Fsm GetFsm()
        {
            if (useTemplate.Value)
            {
                if (fsmTemplateControl.fsmTemplate)
                    return fsmTemplateControl.fsmTemplate.fsm;
                return null;
            }
            return subFSM.Fsm;
        }

        public override string ToLua()
        {
            string name;
            if (useTemplate.Value) name = fsmTemplateControl.fsmTemplate.fsm.Name;
            else name = subFSM.Fsm.Name;
            return string.Format("{{ name = \"{0}\", param = {{ subFSM = {1} }} }}", ToString(), name);
        }
    }
}