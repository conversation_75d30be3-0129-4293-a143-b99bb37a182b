using System;
using System.Reflection;
using System.Text;

namespace HutongGames.PlayMaker
{
    public abstract class LuaFsmStateAction : FsmStateAction
    {
        protected string ActionID => string.Format("{0}_{1}_{2}_{3}", this.Fsm.Name, this.State.Name, this.GetType().Name, this.Index);

        public override void OnEnter()
        {
            FsmEventCenter.Instance.OnFinishEvent += OnFinishEvent;
        }

        public override void OnExit()
        {
            FsmEventCenter.Instance.OnFinishEvent -= OnFinishEvent;
        }

        protected virtual void OnFinishEvent(string actionID)
        {
            if (actionID == this.ActionID)
            {
                Finish();
            }
        }

        public override string ToString()
        {
            return this.GetType().Name;
        }

        public virtual string ToLua()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{{ name = \"{0}\", param = {{ ", ToString());
            int index = 0;
            foreach (FieldInfo fi in GetType().GetFields())
            {
                // 控制变量不导出
                if (HasAttribute<ControlAttribute>(fi))
                    continue;

                // 没有激活的变量不导出
                var hide = GetAttribute<HideIfAttribute>(fi);
                if (hide != null)
                {
                    var method = GetType().GetMethod(hide.Test, BindingFlags.Instance | BindingFlags.Public);
                    if (method != null && method.ReturnType == typeof (bool) && method.GetParameters().Length == 0)
                    {
                        Func<object, bool> func = input => (bool)method.Invoke(input, null);
                        if (func(this))
                            continue;
                    }
                }

                if (index > 0)
                    sb.Append(", ");
                index++;

                var value = fi.GetValue(this);
                sb.Append(ToLua(value, fi.Name));
            }
            sb.Append(" } }");

            return sb.ToString();
        }
        
        static bool HasAttribute<T>(FieldInfo fi) where T : Attribute
        {
            object[] attributes = fi.GetCustomAttributes(true);
            if (attributes.Length == 0)
                return false;
            foreach (Attribute attribute in attributes)
            {
                if (attribute is T obj)
                    return true;
            }
            return false;
        }
        
        static T GetAttribute<T>(FieldInfo fi) where T : Attribute
        {
            object[] attributes = fi.GetCustomAttributes(true);
            if (attributes.Length == 0)
                return default(T);
            foreach (Attribute attribute in attributes)
            {
                if (attribute is T obj)
                    return obj;
            }
            return default(T);
        }
        
        private string ToLua(object v, string name)
        {
            var strValue = string.Empty;
            if (v is NamedVariable && ((NamedVariable)v).UseVariable)
                strValue = string.Format("{{ vn = '{0}' }}", ((NamedVariable)v).Name);
            else if (v is FsmVector3)
                strValue = ToLua((FsmVector3) v); 
            else if (v is FsmVector2)
                strValue = ToLua((FsmVector2) v);
            else if (v is FsmColor)
                strValue = ToLua((FsmColor) v);
            else if (v is FsmRect)
                strValue = ToLua((FsmRect) v);
            else if (v is FsmString)
                strValue = ToLua((FsmString) v);
            else if (v is FsmBool)
                strValue = ToLua((FsmBool) v);
            else if (v is FsmFloat)
                strValue = ToLua((FsmFloat) v);
            else if (v is FsmInt)
                strValue = ToLua((FsmInt) v);
            else if (v is FsmInt[])
                strValue = ToLua((FsmInt[]) v);
            else if (v is FsmVar[])
                strValue = ToLua((FsmVar[]) v);
            else if (v is FsmString[])
                strValue = ToLua((FsmString[]) v);
            else if (v is FsmSValue[])
                strValue = ToLua((FsmSValue[]) v);
            else
                strValue = ToLua(v);
            return string.Format("{0} = {1}", name, strValue);
        }

        protected virtual string ToLua(object v) => string.Format("\"{0}\"", v);
        
        protected static string ToLua(FsmVector3 v)
        {
            return string.Format("cs.Vector3({0}, {1}, {2})", v.Value.x, v.Value.y, v.Value.z);
        }

        protected static string ToLua(FsmVector2 v)
        {
            return string.Format("cs.Vector2({0}, {1})", v.Value.x, v.Value.y);
        }

        protected static string ToLua(FsmColor v)
        {
            return string.Format("cs.Color({0}, {1}, {2}, {3})", v.Value.r, v.Value.g, v.Value.b, v.Value.a);
        }
        
        protected static string ToLua(FsmRect v)
        {
            return string.Format("cs.Rect({0}, {1}, {2}, {3})", v.Value.x, v.Value.y, v.Value.width, v.Value.height);
        }

        protected static string ToLua(FsmString v)
        {
            return string.Format("\"{0}\"", v.Value);
        }
        
        protected static string ToLua(FsmInt[] vArray)
        {
            var index = 0;
            StringBuilder sb = new StringBuilder();
            foreach (var fsmInt in vArray)
            {
                if (index > 0)
                    sb.Append(", ");
                index++;
                sb.Append(fsmInt.Value);
            }
            return string.Format("{{ {0} }}", sb);
        }
        
        protected static string ToLua(FsmString[] vArray)
        {
            var index = 0;
            StringBuilder sb = new StringBuilder();
            foreach (var fsmString in vArray)
            {
                if (index > 0)
                    sb.Append(", ");
                index++;
                sb.AppendFormat("\"{0}\"", fsmString.Value);
            }
            return string.Format("{{ {0} }}", sb);
        }
        
        protected static string ToLua(FsmVar[] vArray)
        {
            var index = 0;
            StringBuilder sb = new StringBuilder();
            foreach (var fsmVar in vArray)
            {
                if (index > 0)
                    sb.Append(", ");
                index++;
                sb.Append(ToLua(fsmVar));
            }
            return string.Format("{{ {0} }}", sb);
        }
        
        protected static string ToLua(FsmSValue[] vArray)
        {
            var index = 0;
            StringBuilder sb = new StringBuilder();
            foreach (var fsmVar in vArray)
            {
                if (index > 0)
                    sb.Append(", ");
                index++;
                sb.AppendFormat("{0} = {1}", fsmVar.key, ToLua(fsmVar.value));
            }
            return string.Format("{{ {0} }}", sb);
        }

        protected static string ToLua(FsmBool v)
        {
            return string.Format("{0}", v.Value.ToString().ToLower());
        }

        protected static string ToLua(FsmFloat v)
        {
            return v.Value.ToString();
        }

        protected static string ToLua(FsmInt v)
        {
            return v.Value.ToString();
        }

        protected static string ToLua(FsmVar v)
        {
            switch (v.Type)
            {
                case VariableType.Vector3:
                    return string.Format("cs.Vector3({0}, {1}, {2})", v.vector3Value.x, v.vector3Value.y,
                        v.vector3Value.z);
                case VariableType.Vector2:
                    return string.Format("cs.Vector2({0}, {1})", v.vector2Value.x, v.vector2Value.y);
                case VariableType.Color:
                    return string.Format("cs.Color({0}, {1}, {2}, {3})", v.colorValue.r, v.colorValue.g, v.colorValue.b,
                        v.colorValue.a);
                case VariableType.Rect:
                    return string.Format("cs.Rect({0}, {1}, {2}, {3})", v.rectValue.x, v.rectValue.y, v.rectValue.width,
                        v.rectValue.height);
                case VariableType.Quaternion:
                    var eulerAngles = v.quaternionValue.eulerAngles;
                    return string.Format("cs.Vector3({0}, {1}, {2})", eulerAngles.x, eulerAngles.y, eulerAngles.z);
                case VariableType.String:
                    return string.Format("\"{0}\"", v.stringValue);
                case VariableType.Bool:
                    return string.Format("{0}", v.boolValue.ToString().ToLower());
            }
            return string.Format("{0}", v.GetValue());
        }

        // static string ToLua(FsmVar v, string name)
        // {
        //     switch (v.Type)
        //     {
        //         case VariableType.Vector3:
        //             return string.Format("{3} = cs.Vector3({0}, {1}, {2})", v.vector3Value.x, v.vector3Value.y,
        //                 v.vector3Value.z, name);
        //         case VariableType.Vector2:
        //             return string.Format("{2} = cs.Vector2({0}, {1})", v.vector2Value.x, v.vector2Value.y, name);
        //         case VariableType.Color:
        //             return string.Format("{4} = cs.Color({0}, {1}, {2}, {3})", v.colorValue.r, v.colorValue.g, v.colorValue.b,
        //                 v.colorValue.a, name);
        //         case VariableType.Quaternion:
        //             var eulerAngles = v.quaternionValue.eulerAngles;
        //             return string.Format("{3} = cs.Vector3({0}, {1}, {2})", eulerAngles.x, eulerAngles.y, eulerAngles.z, name);
        //         case VariableType.String:
        //             return string.Format("{1} = \"{0}\"", v.stringValue, name);
        //         case VariableType.Bool:
        //             return string.Format("{1} = {0}", v.boolValue.ToString().ToLower(), name);
        //     }
        //     return string.Format("{1} = {0}", v.GetValue(), name);
        // }
    }
}