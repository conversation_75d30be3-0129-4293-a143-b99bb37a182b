using System;
using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("UI功能介绍")]
    public class UIIntroduction : LuaFsmStateAction
    {
        [Serializable]
        public enum BoxAngleType
        {
            RightAngle = 0,     // 直角
            ObtuseAngle         // 软角
        }
        
        [Serializable]
        public enum TipType
        {
            Avatar = 0,          // 有头像
            Normal = 1,         // 没有头像
        }

        [Serializable]
        public enum ArrowDirection
        {
            Up,
            Down,
            Left,
            Right
        }

        [RequiredField] 
        [Tooltip("UI页面名称")]
        public FsmString uiName;

        [RequiredField] 
        [Tooltip("UI地址")]
        public FsmString uiPath;

        [Tooltip("选中框类型(直角或软角)")]
        public BoxAngleType angleType = BoxAngleType.RightAngle;

        [Control]
        [Tooltip("是否扩展选中框架位置和尺寸")]
        public FsmBool useExtend;
        
        [HideIf("HideBoxExtend")]
        [Tooltip("选中框位置尺寸扩展(位置和尺寸累加到初始值上)")]
        public FsmRect boxExtend;

        [Tooltip("是否显示对话框")]
        public FsmBool showTip;

        [HideIf("HideTip")]
        [Tooltip("对话框内容")]
        public FsmString content;

        [HideIf("HideTip")]
        [Tooltip("对话框类型(没有头像,有头像)")]
        public TipType tipType = TipType.Normal;

        [Tooltip("TIP箭头位置")]
        public ArrowDirection arrowDirection = ArrowDirection.Up;

        public bool HideTip() => !showTip.Value;
        public bool HideBoxExtend() => !useExtend.Value;

        protected override string ToLua(object v)
        {
            if (v is BoxAngleType)
                return ((int)v).ToString();
            if (v is TipType)
                return ((int)v).ToString();
            if (v is ArrowDirection)
                return string.Format("\"{0}\"", v.ToString().ToLower());
            return base.ToLua(v);
        }
    }
}