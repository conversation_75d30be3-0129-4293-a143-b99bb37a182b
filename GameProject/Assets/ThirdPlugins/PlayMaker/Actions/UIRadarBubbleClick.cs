using System;
using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("UI雷达事件按钮点击")]
    public class UIRadarBubbleClick : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("雷达事件ID")]
        public FsmInt eventID;

        [Tooltip("是否拦截点击事件\n勾选：拦截\n不勾选：不拦截")]
        public FsmBool interceptable;
    }
}