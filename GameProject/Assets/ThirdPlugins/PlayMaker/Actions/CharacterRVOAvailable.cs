using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Character)]
    [Tooltip("设置角色的RVO是否生效")]
    public class CharacterRVOAvailable : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("想要设置的对象名称")]
        public FsmString gameObjectName;
        
        [RequiredField] 
        [Tooltip("RVO是否生效\n【勾】生效\n【空】不生效\n")]
        public FsmBool available;
                
        // [Tooltip("状态结束是否恢复原来的状态")]
        // public FsmBool autoRecovery;
    }
}