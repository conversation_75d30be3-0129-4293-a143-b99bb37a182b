using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Input)]
    [Tooltip("UI点击3D物体引导")]
    public class UIObjectClick : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("3D物体名称")]
        public FsmString gameObjectName;
        
        [Tooltip("相对主体Object的偏移值(UI)")]
        public FsmVector2 offset;
    }
}