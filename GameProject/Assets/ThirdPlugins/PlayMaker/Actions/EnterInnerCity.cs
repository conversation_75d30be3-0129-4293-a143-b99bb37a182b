using System;
using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.GameLogic)]
    [Tooltip("进入玩家自己的内城")]
    public class EnterInnerCity : LuaFsmStateAction
    {
        [Serializable]
        public enum CityType
        {
            MyCity = 1,          // 自己的内城
            PlayerCity = 2,      // 其它玩家的内城
        }
        
        [Tooltip("搜索类型")]
        public CityType cityType = CityType.MyCity;

        [Tooltip("其它玩家外城ID")]
        [HideIf("HideCityObjID")]
        public FsmInt cityObjID = 0;

        public bool HideCityObjID() => cityType == CityType.MyCity;
        
        protected override string ToLua(object v)
        {
            if (v is CityType)
            {
                return ((int) cityType).ToString();
            }
            return base.ToLua(v);
        }
    }
}