using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("切换引导副本主页面右上角地图")]
    public class UIBarrierMapSwitch : LuaFsmStateAction
    {
        [Serializable]
        public enum Map
        {
            Hide = 0,
            Map1,
            Map2,
            Map3
        }

        [RequiredField] 
        [Toolt<PERSON>("UI页面名称")]
        public FsmString uiName;

        [Tooltip("是否可见")]
        public Map map;
        
        protected override string ToLua(object v)
        {
            if (v is Map)
                return ((int)map).ToString();
            return base.ToLua(v);
        }
    }
}