using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Logic)]
    [Tooltip("日志上报")]
    public class LogReport : LuaFsmStateAction
    {
        [Tooltip("是否是结束日志")]
        public FsmBool reportFinish = false;
        
        [HideIf("HideLogName")]
        [Tooltip("日志名称")]
        public FsmString logName;

        public bool HideLogName() => reportFinish.Value;
    }
}