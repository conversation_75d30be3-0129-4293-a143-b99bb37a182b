using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("设置某个页面上的控件是否可见")]
    public class UIControlVisible : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("UI页面名称")]
        public FsmString uiName;
        
        [RequiredField] 
        [Tooltip("UI地址")]
        public FsmString path;

        [Tooltip("是否可见")]
        public FsmBool visible;
    }
}