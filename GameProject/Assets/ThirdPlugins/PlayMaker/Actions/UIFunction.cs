using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    public class UIFunction : LuaFsmStateAction
    {
        [RequiredField] public FsmString proxyName;
        [RequiredField] public FsmString funcName;
        public FsmVar[] parameters;

        public override string ToLua()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{{ name = \"{0}\", param = {{ {1}, {2}, ", ToString(), proxyName.ToLua(),
                funcName.ToLua());
            for (int i = 0; i < parameters.Length; ++i)
            {
                sb.AppendFormat("{0}, ", parameters[i].ToLua());
            }

            sb.Append("} }");
            return sb.ToString();
        }
    }
}