using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Audio)]
    [Tooltip("音频播放")]
    public class VoicePlay : LuaFsmStateAction
    {
        [Control]
        [Tooltip("是否播放音效")]
        public FsmBool playSound = false;
        
        [Toolt<PERSON>("音效名称")]
        [HideIf("HideSound")]
        public FsmString soundName;
        
        [Tooltip("BGM名称")]
        [HideIf("HideBGM")]
        public FsmString backgroundName;
        
        public bool HideSound() => !playSound.Value;
        public bool HideBGM() => playSound.Value;
    }
}