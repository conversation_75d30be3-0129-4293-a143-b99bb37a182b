using System;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("UI区域气泡点击")]
    public class UIAreaBubbleClick : LuaFsmStateAction
    {
        [Serializable]
        public enum BubbleType
        {
            None = 0,
            Event         = 1,   // 事件类型
            UnlockArea    = 2,   // 解锁区域气泡
        }
        
        [RequiredField] 
        [Tooltip("所以查找的区域地块配置ID")]
        public FsmInt areaId;

        [RequiredField]
        [Tooltip("要点击的区域气泡类型")]
        public BubbleType bubbleType;

        // public bool HideID() => target != TargetType.ID && target != TargetType.SpecifiedResourceCollectiable;

        protected override string To<PERSON>ua(object v)
        {
            if (v is BubbleType)
                return ((int) bubbleType).ToString();

            return base.ToLua(v);
        }
    }
}