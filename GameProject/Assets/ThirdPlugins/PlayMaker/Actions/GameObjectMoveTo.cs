using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.GameObject)]
    [Tooltip("移动GameObject至坐标")]
    public class GameObjectMoveTo : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("设置想要移动的角色名称")]
        public FsmString gameObjectName;
        
        [RequiredField] 
        [Tooltip("移动前往的坐标")]
        public FsmVector3 targetPosition;

        [Tooltip("移动的时间")]
        public FsmFloat duration = 0f;
    }
}