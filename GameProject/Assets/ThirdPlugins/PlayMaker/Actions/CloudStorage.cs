using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Storage)]
    [Tooltip("存储数据到服务器")]
    public class CloudStorage : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("数据ID")]
        public FsmInt id;

        [Control]
        [Tooltip("是否使用VALUE值")]
        public FsmBool useValue;

        [HideIf("HideValue")]
        [Tooltip("数据值")]
        public FsmInt value;

        public bool HideValue() => !useValue.Value;
    }
}