using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.GameObject)]
    [Tooltip("对象: 加载新的模型替换旧的模型")]
    public class GameObjectReplace : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("设置想要被替换的旧对象名称")]
        public FsmString gameObjectName;
        [RequiredField] 
        [Tooltip("设置想要替换上的新对象路径")]
        public FsmString prefabPath;
        [Control]
        [Tooltip("是否覆写修改新对象的坐标\n【勾】修改（勾选后出现新Setting：Coordinate）\n【空】不修改")]
        public FsmBool overridePosition = false;
        [HideIf("HidePosition")] 
        [Tooltip("新对象的坐标")]
        public FsmVector3 position;
        
        [Control]
        [Tooltip("是否覆写修改新对象的缩放\n【勾】修改\n【空】不修改")]
        public FsmBool overrideScale = false;
        
        [HideIf("HideScale")] 
        [Tooltip("新对象的缩放")]
        public FsmVector3 scale;
        
        public bool HidePosition() => !overridePosition.Value;
        public bool HideScale() => !overrideScale.Value;
    }
}