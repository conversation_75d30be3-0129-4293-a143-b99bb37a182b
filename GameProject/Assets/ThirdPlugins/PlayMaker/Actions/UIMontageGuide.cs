using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("引导：拼接指引")]
    public class UIMontageGuide : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("拼接组件id(Montage表的id)")]
        public FsmInt montageId;
        
        [Tooltip("滑动表现的时长(指引快慢)")]
        public FsmFloat duration = 0.75f;

        [Tooltip("结束位置偏移")]
        public FsmVector2 offset;
    }
}
