using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("角色头顶飘字")]
    public class CharacterHeadHint : LuaFsmStateAction
    {
        [RequiredField]
        [Tooltip("角色对象名称")]
        public FsmString objectName = "host001";

        [RequiredField]
        [Tooltip("角色对象高度")]
        public FsmFloat objectHeight = 2.0f;

        [RequiredField]
        [Tooltip("图标")]
        public FsmString icon;

        [RequiredField]
        [Tooltip("文本")]
        public FsmString text;

        [Control] 
        [Tooltip("多次播放")] 
        public FsmBool repeatPlay;

        [HideIf("HideRepeatPlay")]
        [Tooltip("播放次数")]
        public FsmInt count;

        [HideIf("HideRepeatPlay")]
        [Tooltip("间隔时间")]
        public FsmFloat interval;

         public bool HideRepeatPlay() => !repeatPlay.Value;
    }
}