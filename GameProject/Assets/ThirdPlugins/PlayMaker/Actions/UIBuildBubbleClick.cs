using System;
using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("UI建筑泡泡点击")]
    public class UIBuildBubbleClick : LuaFsmStateAction
    {
        // [Serializable]
        // public enum TargetType
        // {
        //     ID = 1,                             // 按建筑ID来查找
        //     ResourceCollectiable = 2,           // 按有可收集资源来查找
        //     SpecifiedResourceCollectiable = 3,  // 按有指定的可收集资源来查找(需要传入可收集资源建筑ID)
        // }
        
        [Serializable]
        public enum BubbleType
        {
            None = 0,
            Res                = 1,   // 资源气泡(停产&&工人)
            CityWallIcon       = 2,   // 城墙气泡
            AllianceCenter     = 3,   // 联盟中心气泡
            AllianceHelp       = 4,   // 联盟帮助气泡
            AwardMonster       = 5,   // 怪物奖励气泡
            ClearUp            = 6,   // 建筑清除气泡
            Disable            = 7,   // 禁用气泡 （潮汐
            HeroBond           = 8,   // 英雄羁绊
            HeroLevelResonance = 9,   // 英雄共鸣
            Hospital           = 10,  // 医馆气泡
            Milestone          = 11,  // 里程碑
            Radar              = 12,  // 雷达
            PendingArmy        = 13,  // 待收兵气泡
            PendingHealArmy    = 14,  // 待收取伤兵气泡
            Recruit            = 15,  // 招募气泡
            Store              = 16,  // 集市气泡
            Pve                = 17,  // 探索方舟Pve气泡
            CastleCenter       = 18,  // 积木中心气泡
            UpgradeItem        = 19,  // 升级所需道具
            MultyGuide         = 40,  // 多道具引导气泡
        }
        
        // [Tooltip("建筑查找类型")]
        // public TargetType target = TargetType.ID;

        [RequiredField] 
        [Tooltip("所以查找的建筑配置ID")]
        // [HideIf("HideID")]
        public FsmInt buildID;

        [RequiredField]
        [Tooltip("要点击的建筑泡泡类型")]
        public BubbleType bubbleType;

        // public bool HideID() => target != TargetType.ID && target != TargetType.SpecifiedResourceCollectiable;

        protected override string ToLua(object v)
        {
            if (v is BubbleType)
                return ((int) bubbleType).ToString();
            // else if (v is TargetType)
            //     return ((int) target).ToString();
            return base.ToLua(v);
        }
    }
}