using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    public class UIBranch : LuaFsmStateAction
    {
        [RequiredField] public FsmString proxyName;
        [RequiredField] public FsmEvent[] events;

        public override string ToLua()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{{ name = \"{0}\", param = {{ {1}", ToString(), proxyName.ToLua());
            for (int i = 0; i < events.Length; ++i)
            {
                sb.Append(", ");
                sb.AppendFormat("\"{0}\"", events[i].Name);
            }
            sb.Append(" } }");
            return sb.ToString();
        }
    }
}