using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("检查页面是否停留在某一个页面上")]
    public class UIStay : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("所要检查的UI页面名称")]
        public FsmString uiName;
        
        [Tooltip("触发场景，不填的话则不进行场景检测")]
        public FsmString[] worldNames;

        [Tooltip("停留时间")]
        public FsmFloat stayTime;
    }
}