using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Logic)]
    [Tooltip("开启运行指定的逻辑片段")]
    public class LogicLaunch : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("逻辑名称")]
        public FsmString logicName;

        [Tooltip("是否等待逻辑运行结束")]
        public FsmBool waitingForFinish = false;
        
        [Control]
        [Tooltip("是否需要参数")]
        public FsmBool useParam;
        
        [HideIf("HideParam")]
        [Tooltip("逻辑参数")]
        public FsmSValue[] param;
        
        public bool HideParam() => !useParam.Value;
    }
}