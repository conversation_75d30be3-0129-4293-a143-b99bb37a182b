using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Character)]
    [Tooltip("角色：跟随另一个角色")]
    public class CharacterFollow : LuaFsmStateAction
    {
        [RequiredField]
        [Tooltip("跟随对象名称")]
        public FsmString gameObjectName;

        [<PERSON><PERSON><PERSON>("是否跟随对象")]
        public FsmBool followable;
        
        [Toolt<PERSON>("被跟随对象名称")]
        [HideIf("HideTargetObject")]
        public FsmString targetObjectName;

        [HasFloatSlider(1f, 50f)] 
        [Tooltip("跟随距离")]
        [HideIf("HideTargetObject")]
        public FsmFloat followDistance = 5f;

        public bool HideTargetObject() => !followable.Value;
    }
}