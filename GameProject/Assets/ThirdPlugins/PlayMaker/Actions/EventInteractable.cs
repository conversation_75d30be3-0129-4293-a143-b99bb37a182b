using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Input)]
    public class EventInteractable : LuaFsmStateAction
    {
        [RequiredField] public FsmString gameObjectName;
        [RequiredField] public FsmBool interactable;

        public override string ToLua()
        {
            return string.Format("{{ name = \"{0}\", param = {{ {1}, {2} }} }}", ToString(), gameObjectName.ToLua(),
                interactable.ToLua());
        }
    }
}