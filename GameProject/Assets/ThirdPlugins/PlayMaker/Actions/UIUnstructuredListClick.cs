using System;
using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("UI类表点击")]
    public class UIUnstructuredListClick : LuaFsmStateAction
    {
        [Serializable]
        public enum ListType
        {
            Technology,
            LordTalent,
            AllianceTechnology,
            RogueSkill,
        }

        [Tooltip("UI页面名称")]
        [RequiredField] public FsmString uiName;
        
        [Tooltip("类表类型")]
        public ListType listType = ListType.Technology;
        
        [RequiredField]
        [Tooltip("根据列表类型，配置对应参数")]
        public FsmInt paramID;

        [Control] 
        [Tooltip("是否使用参数2")]
        public FsmBool useParam2;

        [HideIf("HideParam2")]
        [Tooltip("配置参数2")]
        public FsmInt paramID2;
        
        [Control]
        [Tooltip("是否偏移坐标")]
        public FsmBool useOffset;

        [HideIf("HideOffset")]
        [Tooltip("位置偏移值(UI分辨率为1334x750)")]
        public FsmVector2 offset;
        
        public bool HideParam2() => !useParam2.Value;
        public bool HideOffset() => !useOffset.Value;
    }
}