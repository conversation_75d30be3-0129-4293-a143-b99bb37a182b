using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory("Check")]
    [Tooltip("检测：进入区域")]
    public class AreaEnter : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("设置想要检测进入区域的对象名称")]
        public FsmString gameObjectName;
        
        [RequiredField] 
        [Tooltip("区域的圆心坐标\n【X】【Y】【Z】（暂时只能设置圆形区域）")]
        public FsmVector3 targetPosition;
        
        [HasFloatSlider(1f, 60f)] 
        [Tooltip("减速时相对圆心的距离(减速度以寻路配置为准)\n（滑动条：最小值5，最大值60）")]
        public FsmFloat finishDistance = 1f;

        [Control]
        [Tooltip("是否添加提示提示特效")] 
        public FsmBool useTipEffect;

        [HideIf("HideEffect")] 
        [Tooltip("提示特效位置")]
        public FsmVector3 tipEffectPosition;

        public bool HideEffect() => !useTipEffect.Value;
    }
}