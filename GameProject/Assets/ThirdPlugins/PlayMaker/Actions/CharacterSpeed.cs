using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Character)]
    [Tooltip("角色：调整移动速度")]
    public class CharacterSpeed : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("设置想要调整移动速度的对象名称")]
        public FsmString gameObjectName;
        
        [RequiredField] 
        [Tooltip("新的移动速度参数")]
        public FsmFloat speed;

        [Tooltip("状态结束是否恢复原来的速度")]
        public FsmBool autoRecovery;
    }
}
