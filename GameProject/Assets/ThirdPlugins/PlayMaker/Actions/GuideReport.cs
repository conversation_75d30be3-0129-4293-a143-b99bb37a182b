using System;
using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Logic)]
    [Tooltip("引导步骤上报")]
    public class GuideReport : LuaFsmStateAction
    {
        [Serializable]
        public enum GuideState 
        {
            STARTING,
            COMPLETE,
        }
        
        [Tooltip("引导线名称")]
        public FsmInt guideID;

        [Toolt<PERSON>("引导线状态")] 
        public GuideState guideState = GuideState.STARTING;

        protected override string ToLua(object v)
        {
            if (v is GuideState)
            {
                return ((int) v).ToString();
            }
            return base.ToLua(v);
        }
    }
}