using System;
using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.World)]
    [Tooltip("大世界：加载")]
    public class WorldReplace : LuaFsmStateAction
    {
        [Serializable]
        public enum TargetType
        {
            Normal = 0,
            PVE,
        }

        [Tooltip("操作类型")] 
        public TargetType targetType;
        
        [HideIf("HideWorldName")]
        [Tooltip("设置想要替换当前大世界的新大世界名称")]
        public FsmString newWorldName;
        
        [Control] 
        [Tooltip("是否配置参数")]
        public FsmBool useParam;
        
        [HideIf("HideParam")]
        [Tooltip("场景参数")]
        public FsmSValue[] param;
        
        public bool HideParam() => !useParam.Value;
        public bool HideWorldName() => targetType != TargetType.Normal;

        protected override string ToLua(object v)
        {
            if (v is TargetType)
            {
                return ((int) v).ToString();
            }
            return v.ToString();
        }
    }
}