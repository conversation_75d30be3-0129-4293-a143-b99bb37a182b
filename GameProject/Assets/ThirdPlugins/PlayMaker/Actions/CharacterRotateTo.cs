using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Character)]
    [Tooltip("设置角色朝向另一个对象")]
    public class CharacterRotateTo : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("需要设置朝向的对象")]
        public FsmString gameObjectName;

        [Control] 
        [Tooltip("朝向位置")] 
        public FsmBool usePosition;
        
        [Tooltip("要朝向的对象")]
        [HideIf("HideTargetName")]
        public FsmString targetObjectName;
        
        [Tooltip("要朝向的位置")]
        [HideIf("HideTargetPosition")]
        public FsmVector3 targetPosition;
        
        [<PERSON>lt<PERSON>("朝向的时间")]
        public FsmFloat duration = 0f;

        public bool HideTargetName() => usePosition.Value;
        public bool HideTargetPosition() => !usePosition.Value;
    }
}
