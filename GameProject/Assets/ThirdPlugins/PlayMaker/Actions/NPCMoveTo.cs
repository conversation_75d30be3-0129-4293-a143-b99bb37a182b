using System;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.GameLogic)]
    [Tooltip("NPC走到内城的某一个位置")]
    public class NPCMoveTo : LuaFsmStateAction
    {
        [Tooltip("NPC的配置ID")]
        public FsmInt npcID;
        
        [Toolt<PERSON>("NPC移动的目的地（内城位置）")]
        public FsmVector2 destination;
        
        [Control] 
        [Tooltip("走到目的地NPC是否旋转方向")]
        public FsmBool useLookAt;

        [HideIf("HideLookAt")]
        [Tooltip("走到目的地NPC看到的位置")]
        public FsmVector2 lookAt;

        [Control] 
        [Tooltip("走到目的地是否播放动画")]
        public FsmBool playAnimation;

        [HideIf("HideAniName")]
        [Tooltip("播放的动画名称")]
        public FsmString clipName;

        public bool HideAniName() => !playAnimation.Value;
        public bool HideLookAt() => !useLookAt.Value;
    }
}