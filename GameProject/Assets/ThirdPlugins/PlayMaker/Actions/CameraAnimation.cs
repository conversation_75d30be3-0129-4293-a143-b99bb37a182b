using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Camera)]
    [Tooltip("摄像机动画")]
    public class CameraAnimation : LuaFsmStateAction
    {

        [Control] 
        [Tooltip("使用位置")] 
        public FsmBool usePosition;

        [HideIf("HidePosition")]
        [Tooltip("位置")]
        public FsmVector3 position;

        [Control] 
        [Tooltip("使用角度")] 
        public FsmBool useRotation;
        
        [HideIf("HideRotation")]
        [Tooltip("角度")]
        public FsmVector3 rotation;

        [RequiredField]
        [Tooltip("时间")]
        public FsmFloat duration = 0.5f;

        public bool HidePosition() => !usePosition.Value;
        public bool HideRotation() => !useRotation.Value;
    }
}