// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.HeaderEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using HutongGames.PlayMaker.ActionsInternal;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [CustomActionEditor(typeof (Composition))]
  public class CompositionEditor : CustomActionEditor
  {
    private Composition header;
    // private HtmlTextEditor htmlTextEditor;
  
    public override bool showCategoryIcon => false;
  
    // public override bool showEnabledCheckbox => false;
  
    public override void OnEnable()
    {
      this.header = (Composition) this.target;
    }
  
    public override bool OnGUI()
    {
      EditorGUI.BeginChangeCheck();
      GUILayout.BeginVertical(FsmEditorStyles.StandardMargins);
      // GUILayout.Space(10f);
      // this.header.comment = this.htmlTextEditor.OnGUI(this.header.comment, CustomActionEditor.maxEditorWidth - 20f);
      // if (!string.IsNullOrEmpty(this.header.comment))
        // GUILayout.Space(10f);
        header.runType = (Composition.RunType)EditorGUILayout.EnumPopup("Run Type", header.runType);
        GUILayout.Space(3f);
      GUILayout.EndVertical();
      return EditorGUI.EndChangeCheck();
    }
  }
}
