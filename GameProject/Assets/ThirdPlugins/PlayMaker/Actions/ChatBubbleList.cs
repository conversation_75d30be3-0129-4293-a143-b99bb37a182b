using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory("Chat")]
    public class ChatBubbleList : LuaFsmStateAction
    {
        [RequiredField] public FsmString gameObjectName;
        [RequiredField] public FsmFloat height;
        [RequiredField] public FsmString[] textList;
        [RequiredField] public FsmFloat[] timeList;

        // public override string ToLua()
        // {
        //     StringBuilder sb = new StringBuilder();
        //     sb.AppendFormat("{{ name = \"{0}\", param = {{ {1}, {2}, ", ToString(), gameObjectName.ToLua(),
        //         height.ToLua());
        //     for (int i = 0; i < textList.Length; ++i)
        //     {
        //         if (i == 0)
        //             sb.Append("{");
        //         sb.AppendFormat("{0}, ", textList[i].ToLua());
        //         if (i == textList.Length - 1)
        //             sb.Append("}, ");
        //     }
        //
        //     for (int i = 0; i < timeList.Length; ++i)
        //     {
        //         if (i == 0)
        //             sb.Append("{");
        //         sb.AppendFormat("{0}, ", timeList[i].ToLua());
        //         if (i == textList.Length - 1)
        //             sb.Append("}, ");
        //     }
        //
        //     sb.Append("} }");
        //     return sb.ToString();
        // }
    }
}