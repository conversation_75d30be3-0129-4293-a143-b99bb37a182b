using System;
using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("UI建筑菜单点击")]
    public class UIBuildMenuClick : LuaFsmStateAction
    {
        [Serializable]
        public enum BuildMenuEnum
        {
            btn_confirm = 1,
            btn_cancel  = 2,
            btn_detail  = 3,
            btn_recycle = 4
        }
        
        [RequiredField] 
        [Tooltip("菜单ID，对应BuildMenu表中的ID")]
        public BuildMenuEnum menu = BuildMenuEnum.btn_confirm;
        
        [Tooltip("是否拦截点击事件\n勾选：拦截\n不勾选：不拦截")]
        public FsmBool interceptable;

        [Control]
        [Tooltip("如果指定的菜单ID不存在，使用备选ID")]
        public FsmBool fallback;

        [Tooltip("备选菜单ID列表，按从上到下查找")]
        [HideIf("HideFallbackMenuIDs")]
        public FsmInt[] menuIDList;
        
        public bool HideFallbackMenuIDs() => !fallback.Value;
    }
}