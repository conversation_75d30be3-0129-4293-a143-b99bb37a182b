using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    public class UIDragHint : LuaFsmStateAction
    {
        [RequiredField] public FsmString uiPath;
        public FsmBool overrideStartPoint = false;
        [HideIf("HideStartPoint")] public FsmVector2 startPoint;
        public FsmVector2 endPoint;
        public FsmFloat duration = 0.75f;
        
        public bool HideStartPoint()
        {
            return !overrideStartPoint.Value;
        }
    }
}