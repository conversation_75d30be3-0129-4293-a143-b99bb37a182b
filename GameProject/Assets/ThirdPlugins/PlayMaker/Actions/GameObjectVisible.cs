using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory("GameObject")]
    [Tooltip("对象：设置对象显示是否")]
    public class GameObjectVisible : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("设置想要显示的对象名称\n（允许以数组形式同时设置多个对象）")]
        public FsmString[] gameObjectNames = new FsmString[]{ string.Empty };
        
        [RequiredField] 
        [Tooltip("设置对象显示与否")]
        public FsmBool visible;
        
        [RequiredField] 
        [Tooltip("在当前State执行完毕后，是否还原此Action的设置")]
        public FsmBool autoRecovery;
    }
}