using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Logic)]
    [Tooltip("逻辑分支检测")]
    public class ConditionalLogic : LuaFsmStateAction
    {
        [Serializable]
        public class ConditionEvent
        {
            public FsmInt id;
            public FsmEvent finishEvent;
        }
        
        [Tooltip("条件分支配置")]
        public ConditionEvent[] conditions = new ConditionEvent[1];

        [Tooltip("是否使用历史记录\n未勾选：则每次运行都检测条件\n勾选：使用上一次选择的分支路径")]
        public FsmBool useHistoricalRecord = false;
        
        protected override string ToLua(object v)
        {
            if (v is ConditionEvent[])
            {
                ConditionEvent[] vArray = (ConditionEvent[]) v;
                
                var index = 0;
                StringBuilder sb = new StringBuilder();
                foreach (var ss in vArray)
                {
                    if (index > 0)
                        sb.Append(", ");
                    index++;
                    sb.AppendFormat("{{ id = {0}, eventName = \"{1}\" }}", ss.id, ss.finishEvent.Name);
                }
                return string.Format("{{ {0} }}", sb);
            }
            return string.Empty;
        }
    }
}