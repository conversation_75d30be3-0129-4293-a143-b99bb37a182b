using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Character)]
    [Tooltip("角色：指示目标")]
    public class CharacterIndication : LuaFsmStateAction
    {
        [RequiredField]
        [Tooltip("跟随对象名称")]
        public FsmString gameObjectName;

        [Control] 
        [Tooltip("是否指示到特定目标")]
        [HideIf("HideIndication")]
        public FsmBool useObject = false;

        [Tooltip("目标位置")]
        [HideIf("HidePosition")]
        public FsmVector3 targetPosition;

        [Tooltip("目标对象")]
        [HideIf("HideObject")]
        public FsmString targetObject;

        [<PERSON><PERSON><PERSON>("是否清空引导指示线")]
        public FsmBool clearIndication = false;

        public bool HideIndication() => clearIndication.Value;
        public bool HidePosition() => useObject.Value || clearIndication.Value;
        public bool HideObject() => !useObject.Value || clearIndication.Value;
    }
}