using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("打开场景拼接页面")]
    public class SceneLegoShow : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("拼接ID")]
        public FsmInt legoID;
        
        [RequiredField] 
        [Tooltip("底座对象的名称(在场景中的)")]
        public FsmString baseObjectName;
    }
}