using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Character)]
    [Tooltip("角色：移动至坐标")]
    public class CharacterMoveTo : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("设置想要移动的角色名称")]
        public FsmString gameObjectName;
        
        [RequiredField] 
        [Tooltip("移动前往的坐标")]
        public FsmVector3 targetPosition;

        [Control] 
        [Tooltip("是否寻路过去\n【勾】开启寻路过去\n【空】直接瞬移过去")] 
        public FsmBool usePathFinding;
        
        // [HasFloatSlider(0, 3)]
        // [HideIf("HideFinishDistance")]
        // [Tooltip("减速时相对坐标的距离\n滑动条：最小值0，最大值3")]
        // public FsmFloat slowdownDistance = 0.5f;
        
        [HasFloatSlider(1, 10)]
        [HideIf("HideFinishDistance")]
        [Tooltip("停止相对坐标的距离\n滑动条：最小值1，最大值3")]
        public FsmFloat finishDistance = 1f;

        public bool HideFinishDistance() => !usePathFinding.Value;
    }
}