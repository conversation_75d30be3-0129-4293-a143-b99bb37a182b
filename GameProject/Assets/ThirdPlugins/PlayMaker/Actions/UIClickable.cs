using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("UI:设置是否屏蔽UI的点击事件")]
    public class UIClickable : LuaFsmStateAction
    {
        [Tooltip("是否屏蔽UI点击事件")]
        public FsmBool clickable;
        
        [Tooltip("在退出状态后是否恢复原状")]
        public FsmBool autoRecovery;
    }
}