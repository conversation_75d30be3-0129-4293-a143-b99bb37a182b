using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;
using System;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("内城建筑点击")]
    public class UIBuildClick : LuaFsmStateAction
    {
        [Serializable]
        public enum TargetType
        {
            ID = 1,                     // 按建筑ID来查找
            
            Level = 2,                  // 按等级查找
            // Troop = 3,                  // 按兵种查找
        }

        [Serializable]
        public enum LevelType
        {
            LowestLevel = 1,            // 按最低等级查找
            HighestLevel = 2,           // 按最高等级查找
        }

        // [Serializable]
        // public enum TroopType
        // {
        //     Infantry = 1,               // 按步兵查找
        //     Cavalry = 2,                // 按骑兵查找
        //     Archer = 3,                 // 按工兵查找
        // }

        [Tooltip("查找类型")]
        public TargetType target = TargetType.ID;
        
        [HideIf("HideBuildID")]
        [Tooltip("建筑配置ID")]
        public FsmInt buildID;

        [HideIf("HideLevelType")]
        [Tooltip("选择按最高或最低方式查找建筑")]
        public LevelType levelType = LevelType.LowestLevel;

        // [HideIf("HideTroopType")]
        // public TroopType troopType = TroopType.Infantry;

        [Tooltip("重置摄像机到建筑的距离")]
        public FsmBool resetDistance = true;
        
        public bool HideBuildID() => target != TargetType.ID;
        public bool HideLevelType() => target != TargetType.Level;
        // public bool HideTroopType() => target != TargetType.Troop;
        
        protected override string ToLua(object v)
        {
            if (v is TargetType)
                return ((int) target).ToString();
            else if (v is LevelType)
                return ((int) levelType).ToString();
            return base.ToLua(v);
        }
    }
}