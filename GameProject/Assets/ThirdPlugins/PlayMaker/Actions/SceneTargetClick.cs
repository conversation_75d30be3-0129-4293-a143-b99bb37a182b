using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Input)]
    [Tooltip("地面引导")]
    public class SceneTargetClick : LuaFsmStateAction
    {
        [Tooltip("场景某坐标")]
        public FsmVector3 targetPosition;
        
        [Tooltip("相对主体Object的偏移值")]
        public FsmVector3 offset;

        [Tooltip("是否开启UI蒙版")]
        public FsmBool maskable;
    }
}