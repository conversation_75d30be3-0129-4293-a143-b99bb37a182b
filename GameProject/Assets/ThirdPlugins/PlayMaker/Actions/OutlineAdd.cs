using System;
using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Effects)]
    [Tooltip("为对象添加外描边效果")]
    public class OutlineAdd : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("添加描边对象名称")]
        public FsmString gameObjectName;
        
        // [RequiredField] 
        // [Tooltip("描边的颜色")]
        // public FsmColor outlineColor;
        //
        // [RequiredField] 
        // [Tooltip("边的宽度")]
        // public FsmFloat outlineSize;
        
        [RequiredField] 
        [Tooltip("状态结束后是否删除描边")]
        public FsmBool autoRemove;
    }
}