using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Effects)]
    [Tooltip("3D提示牌等待中")]
    public class HintWaiting : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("3D物体名称")]
        public FsmString gameObjectName;
        
        [Control]
        [Tooltip("是否调整速率")]
        public FsmBool changeSpeed;

        [HideIf("HideSpeed")]
        [Tooltip("播放速率")]
        public FsmFloat speed = 1f;

        public bool HideSpeed() => !changeSpeed.Value;
    }
}