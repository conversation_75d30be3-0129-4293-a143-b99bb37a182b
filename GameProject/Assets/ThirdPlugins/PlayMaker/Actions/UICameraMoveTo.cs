using System;
using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Camera)]
    [Tooltip("内城摄像机移动")]
    public class UICameraMoveTo : LuaFsmStateAction
    {
        [Serializable]
        public enum TargetType
        {
            Build = 1,
            Tidal<PERSON>onster,
            InnerCity,
            
            InnerCityPosition = 98,
            InnerCityCoordinate = 99
        }

        [Tooltip("聚焦对象类型")]
        public TargetType target = TargetType.Build;

        [HideIf("HideBuild")]
        [Tooltip("建筑配置ID")]
        public FsmInt buildID;

        [HideIf("HideBuild")]
        [Tooltip("是否选中建筑")]
        public FsmBool selected = false;

        [HideIf("HideCoordinate")]
        [Tooltip("内城位置索引XY")]
        public FsmVector2 coordinate;

        [HideIf("HidePosition")]
        [Tooltip("对应内城位置XZ")]
        public FsmVector2 position;
        
        [Control]
        [HideIf("HideUseDistance")]
        [Tooltip("是否设置摄像机距离")]
        public FsmBool useDistance = false;
        
        [HideIf("HideDistance")]
        [Tooltip("内城摄像机距离")]
        public FsmFloat distance;
        
        public bool HideBuild() => target != TargetType.Build;
        public bool HideCoordinate() => target != TargetType.InnerCityCoordinate;
        public bool HideUseDistance() => !(target == TargetType.InnerCityPosition || target == TargetType.InnerCityCoordinate);
        public bool HidePosition() => target != TargetType.InnerCityPosition;
        public bool HideDistance() => !(target == TargetType.InnerCityPosition || target == TargetType.InnerCityCoordinate) || !useDistance.Value;

        protected override string ToLua(object v)
        {
            if (v is TargetType)
                return ((int) v).ToString();
            return base.ToLua(v);
        }
    }
}