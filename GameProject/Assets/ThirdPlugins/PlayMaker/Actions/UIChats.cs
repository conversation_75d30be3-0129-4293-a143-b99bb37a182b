using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    public class UIChats : LuaFsmStateAction
    {
        [Serializable]
        public enum Seat
        {
            Left,
            Right
        }

        [Serializable]
        public class FsmChat
        {
            [RequiredField] 
            [Tooltip("发言对象的角色模型路径")]
            public FsmString rolePath;
            
            [Tooltip("发言对象的左右席位控制\n【左】、【右】\n（选项互斥，有且仅有一个勾选结果，默认勾选：左）")]
            public Seat seat;
            
            [RequiredField] 
            [Tooltip("发言对象的名称文本")]
            public FsmString roleName;
            
            [RequiredField] 
            [Tooltip("发言对象的正文文本")]
            public FsmString text;

            [Tooltip("角色偏移（屏幕坐标）")]
            public FsmVector2 offset = Vector2.zero;

            [Tooltip("角色缩放值")]
            public FsmFloat scale = 1f;
            
            [Tooltip("是否水平翻转")]
            public FsmBool flipHorizontal = false;
        }
        
        [Control] 
        [Tooltip("是否自动播放")]
        public FsmBool autoPlay = true;

        [HideIf("HideAutoPlay")]
        [Tooltip("自动播放间隔时间(s)")]
        public FsmFloat autoPlayInterval = 5f;

        [Control] 
        [Tooltip("是否跳过剧情对话\n【勾】可以跳过（勾选后出现新Setting：Skip Chat Delay）\n【空】不可跳过")]
        public FsmBool skipChat;

        [HideIf("HideSkipChatDelay")]
        [Tooltip("出现跳出按钮的延迟时间(s)")]
        public FsmFloat skipChatDelay;
        
        [Control] 
        [Tooltip("是否有指定的剧情对话背景\n【勾】有背景图（勾选后出现新Setting：Background Path）\n【空】无背景图（默认将当前场景模糊处理并添加透明黑色遮罩")]
        public FsmBool background;
        
        [HideIf("HideBackground")]
        [Tooltip("设置要加载的剧情对话背景")]
        public FsmString backgroundPath;
        
        public FsmChat[] paragraphs;
        
        public bool HideAutoPlay() => !autoPlay.Value;
        public bool HideSkipChatDelay() => !skipChat.Value;
        public bool HideBackground() => !background.Value;

        protected override string ToLua(object v)
        {
            if (v is FsmChat[])
            {
                FsmChat[] vArray = (FsmChat[]) v;
                
                StringBuilder sb = new StringBuilder();

                var index = 0;
                foreach (var fsmVar in vArray)
                {
                    if (index > 0)
                        sb.Append(", ");
                    index++;
                    
                    sb.AppendFormat("{{ {0} = {1}, ", nameof(fsmVar.rolePath), ToLua(fsmVar.rolePath));
                    sb.AppendFormat("{0} = {1}, ", nameof(fsmVar.seat), ToLua(fsmVar.seat));
                    sb.AppendFormat("{0} = {1}, ", nameof(fsmVar.roleName), ToLua(fsmVar.roleName));
                    sb.AppendFormat("{0} = {1}, ", nameof(fsmVar.text), ToLua(fsmVar.text));
                    sb.AppendFormat("{0} = {1}, ", nameof(fsmVar.scale), ToLua(fsmVar.scale));
                    sb.AppendFormat("{0} = {1}, ", nameof(fsmVar.flipHorizontal), ToLua(fsmVar.flipHorizontal));
                    sb.AppendFormat("{0} = {1} }}", nameof(fsmVar.offset), ToLua(fsmVar.offset));
                }
                return string.Format("{{ {0} }}", sb);
            }

            if (v is Seat)
            {
                return ((int) v).ToString();
            }

            return v.ToString();
        }
    }
}