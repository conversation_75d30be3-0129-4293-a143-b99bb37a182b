using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class VignetteMixerBehaviour : PlayableBehaviour
{
    Color m_DefaultColor;
    Vector2 m_DefaultCenter;
    float m_DefaultIntensity;
    float m_DefaultSmoothness;
    bool m_DefaultRounded;

    Vignette m_TrackBinding;
    bool m_FirstFrameHappened;

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        ((Volume) playerData).profile.TryGet(out m_TrackBinding);
        if (m_TrackBinding == null)
            return;
        
        if(!m_FirstFrameHappened)
        {
            m_DefaultColor = m_TrackBinding.color.value;
            m_DefaultCenter = m_TrackBinding.center.value;
            m_DefaultIntensity = m_TrackBinding.intensity.value;
            m_DefaultSmoothness = m_TrackBinding.smoothness.value;
            m_DefaultRounded = m_TrackBinding.rounded.value;

            m_FirstFrameHappened = true;
        }


        int inputCount = playable.GetInputCount();
        Color blendedColor = Color.clear;
        Vector2 blendedCenter = Vector2.zero;
        float blendedIntensity = 0f;
        float blendedSmoothness = 0f;
        bool blendedRounded = false;

        float totalWeight = 0f;
        float greatestWeight = 0f;
        int currentInputs = 0;

        for(int i = 0; i < inputCount; i++)
        {
            float inputWeight = playable.GetInputWeight(i);
            ScriptPlayable<VignetteBehaviour> inputPlayable =(ScriptPlayable<VignetteBehaviour>)playable.GetInput(i);
            VignetteBehaviour input = inputPlayable.GetBehaviour();
            
            blendedColor += input.Color * inputWeight;
            blendedCenter += input.Center * inputWeight;
            blendedIntensity += input.Intensity * inputWeight;
            blendedSmoothness += input.Smoothness * inputWeight;
            blendedRounded = inputWeight > 0.5 ? input.Rounded : blendedRounded;

            totalWeight += inputWeight;

            if (inputWeight > greatestWeight)
            {
                greatestWeight = inputWeight;
            }

            if (!Mathf.Approximately (inputWeight, 0f))
                currentInputs++;
        }
        m_TrackBinding.color.value = blendedColor + m_DefaultColor * (1f-totalWeight);
        m_TrackBinding.center.value = blendedCenter + m_DefaultCenter * (1f-totalWeight);
        m_TrackBinding.intensity.value = blendedIntensity + m_DefaultIntensity * (1f-totalWeight);
        m_TrackBinding.smoothness.value = blendedSmoothness + m_DefaultSmoothness * (1f-totalWeight);
        m_TrackBinding.rounded.value = blendedRounded;

    }



    public override void OnPlayableDestroy (Playable playable)
    {
        m_FirstFrameHappened = false;

        if(m_TrackBinding == null)
            return;

        m_TrackBinding.color.value = m_DefaultColor;
        m_TrackBinding.center.value = m_DefaultCenter;
        m_TrackBinding.intensity.value = m_DefaultIntensity;
        m_TrackBinding.smoothness.value = m_DefaultSmoothness;
        m_TrackBinding.rounded.value = m_DefaultRounded;

    }
}
