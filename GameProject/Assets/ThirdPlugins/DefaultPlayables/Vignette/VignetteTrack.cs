using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

[TrackColor(0.24f, 0.38f, 0.66f)]
[TrackClipType(typeof(VignetteClip))]
[TrackBindingType(typeof(Volume))]
public class VignetteTrack : TrackAsset
{
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        return ScriptPlayable<VignetteMixerBehaviour>.Create(graph, inputCount);
    }
}
