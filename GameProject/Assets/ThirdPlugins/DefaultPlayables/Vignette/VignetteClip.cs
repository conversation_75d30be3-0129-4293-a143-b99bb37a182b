using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

[Serializable]
public class VignetteClip : PlayableAsset, ITimelineClipAsset
{
    public Color color = new Color(0f, 0f, 0f, 1f);
    public Vector2 center = new Vector2(0.5f, 0.5f);
    [Range(0f, 1f)] public float intensity = 0f;
    [Range(0.01f, 1f)] public float smoothness = 0.2f;
    public bool rounded = false;


    public ClipCaps clipCaps
    {
        get { return ClipCaps.Blending; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<VignetteBehaviour>.Create(graph);
        var behaviour = playable.GetBehaviour();

        behaviour.Color = color;
        behaviour.Center = center;
        behaviour.Intensity = intensity;
        behaviour.Smoothness = smoothness;
        behaviour.Rounded = rounded;


        return playable;
    }
}