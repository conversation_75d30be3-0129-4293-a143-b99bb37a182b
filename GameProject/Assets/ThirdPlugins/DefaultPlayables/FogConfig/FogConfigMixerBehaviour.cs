using System;
using Sultan.Game;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;

public class FogConfigMixerBehaviour : PlayableBehaviour
{
    float m_DefaultDistanceOffset;
    float m_DefaultHeightOffset;
    bool m_DefaultFogEnable;
    float m_DefaultNoiseAlpha;
    float m_DefaultHeightMax;
    float m_DefaultHeightMin;
    float m_DefaultDistanceMax;
    float m_DefaultDistanceMin;

    float m_AssignedDistanceOffset;
    float m_AssignedHeightOffset;
    bool m_AssignedFogEnable;
    float m_AssignedNoiseAlpha;
    float m_AssignedHeightMax;
    float m_AssignedHeightMin;
    float m_AssignedDistanceMax;
    float m_AssignedDistanceMin;

    FogConfig m_TrackBinding;

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        m_TrackBinding = playerData as FogConfig;

        if(m_TrackBinding == null)
            return;
        
        if (m_TrackBinding.fogAsset == null)
        {
            return;
        }

        if(m_TrackBinding.fogAsset.DistanceOffset != m_AssignedDistanceOffset)
            m_DefaultDistanceOffset = m_TrackBinding.fogAsset.DistanceOffset;
        if(m_TrackBinding.fogAsset.HeightOffset != m_AssignedHeightOffset)
            m_DefaultHeightOffset = m_TrackBinding.fogAsset.HeightOffset;
        if(m_TrackBinding.fogAsset.FogEnable != m_AssignedFogEnable)
            m_DefaultFogEnable = m_TrackBinding.fogAsset.FogEnable;
        if(!Mathf.Approximately(m_TrackBinding.fogAsset.NoiseAlpha, m_AssignedNoiseAlpha))
            m_DefaultNoiseAlpha = m_TrackBinding.fogAsset.NoiseAlpha;
        if(m_TrackBinding.fogAsset.HeightMax != m_AssignedHeightMax)
            m_DefaultHeightMax = m_TrackBinding.fogAsset.HeightMax;
        if(m_TrackBinding.fogAsset.HeightMin != m_AssignedHeightMin)
            m_DefaultHeightMin = m_TrackBinding.fogAsset.HeightMin;
        if(m_TrackBinding.fogAsset.DistanceMax != m_AssignedDistanceMax)
            m_DefaultDistanceMax = m_TrackBinding.fogAsset.DistanceMax;
        if(m_TrackBinding.fogAsset.DistanceMin != m_AssignedDistanceMin)
            m_DefaultDistanceMin = m_TrackBinding.fogAsset.DistanceMin;

        int inputCount = playable.GetInputCount();

        float blendedDistanceOffset = 0f;
        float blendedHeightOffset = 0f;
        float blendedNoiseAlpha = 0f;
        float blendedHeightMax = 0f;
        float blendedHeightMin = 0f;
        float blendedDistanceMax = 0f;
        float blendedDistanceMin = 0f;
        float totalWeight = 0f;
        float greatestWeight = 0f;
        int currentInputs = 0;

        for(int i = 0; i < inputCount; i++)
        {
            float inputWeight = playable.GetInputWeight(i);
            ScriptPlayable<FogConfigBehaviour> inputPlayable =(ScriptPlayable<FogConfigBehaviour>)playable.GetInput(i);
            FogConfigBehaviour input = inputPlayable.GetBehaviour();
            
            blendedDistanceOffset += input.DistanceOffset * inputWeight;
            blendedHeightOffset += input.HeightOffset * inputWeight;
            blendedNoiseAlpha += input.NoiseAlpha * inputWeight;
            blendedHeightMax += input.HeightMax * inputWeight;
            blendedHeightMin += input.HeightMin * inputWeight;
            blendedDistanceMax += input.DistanceMax * inputWeight;
            blendedDistanceMin += input.DistanceMin * inputWeight;
            totalWeight += inputWeight;

            if(inputWeight > greatestWeight)
            {
                m_AssignedFogEnable = input.FogEnable;
                m_TrackBinding.fogAsset.FogEnable = m_AssignedFogEnable;
                greatestWeight = inputWeight;
            }

            if(!Mathf.Approximately(inputWeight, 0f))
                currentInputs++;
        }

        m_AssignedDistanceOffset = Mathf.RoundToInt(blendedDistanceOffset + m_DefaultDistanceOffset *(1f - totalWeight));
        m_TrackBinding.fogAsset.DistanceOffset = m_AssignedDistanceOffset;
        m_AssignedHeightOffset = Mathf.RoundToInt(blendedHeightOffset + m_DefaultHeightOffset *(1f - totalWeight));
        m_TrackBinding.fogAsset.HeightOffset = m_AssignedHeightOffset;
        m_AssignedNoiseAlpha = blendedNoiseAlpha + m_DefaultNoiseAlpha *(1f - totalWeight);
        m_TrackBinding.fogAsset.NoiseAlpha = m_AssignedNoiseAlpha;
        m_AssignedHeightMax = Mathf.RoundToInt(blendedHeightMax + m_DefaultHeightMax *(1f - totalWeight));
        m_TrackBinding.fogAsset.HeightMax = m_AssignedHeightMax;
        m_AssignedHeightMin = Mathf.RoundToInt(blendedHeightMin + m_DefaultHeightMin *(1f - totalWeight));
        m_TrackBinding.fogAsset.HeightMin = m_AssignedHeightMin;
        m_AssignedDistanceMax = Mathf.RoundToInt(blendedDistanceMax + m_DefaultDistanceMax *(1f - totalWeight));
        m_TrackBinding.fogAsset.DistanceMax = m_AssignedDistanceMax;
        m_AssignedDistanceMin = Mathf.RoundToInt(blendedDistanceMin + m_DefaultDistanceMin *(1f - totalWeight));
        m_TrackBinding.fogAsset.DistanceMin = m_AssignedDistanceMin;

        if(currentInputs != 1 && 1f - totalWeight > greatestWeight)
        {
            m_TrackBinding.fogAsset.FogEnable = m_DefaultFogEnable;
        }

        m_TrackBinding.fogAsset.DataDirty = true;
    }
}
