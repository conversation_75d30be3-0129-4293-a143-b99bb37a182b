using System;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;

[Serializable]
public class FogConfigClip : PlayableAsset, ITimelineClipAsset
{
    public FogConfigBehaviour template = new FogConfigBehaviour();

    public ClipCaps clipCaps
    {
        get { return ClipCaps.Blending; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<FogConfigBehaviour>.Create(graph, template);
        return playable;
    }
}
