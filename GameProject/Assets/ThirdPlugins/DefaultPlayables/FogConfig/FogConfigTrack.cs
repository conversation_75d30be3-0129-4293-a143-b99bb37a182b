using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using System.Collections.Generic;
using Sultan.Game;

[TrackColor(1f, 1f, 1f)]
[TrackClipType(typeof(FogConfigClip))]
[TrackBindingType(typeof(FogConfig))]
public class FogConfigTrack : TrackAsset
{
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        return ScriptPlayable<FogConfigMixerBehaviour>.Create(graph, inputCount);
    }

    // Please note this assumes only one component of type FogConfig on the same gameobject.
    public override void GatherProperties(PlayableDirector director, IPropertyCollector driver)
    {
#if UNITY_EDITOR
        FogConfig trackBinding = director.GetGenericBinding(this) as FogConfig;
        if(trackBinding == null)
            return;

        // These field names are procedurally generated estimations based on the associated property names.
        // If any of the names are incorrect you will get a DrivenPropertyManager error saying it has failed to register the name.
        // In this case you will need to find the correct backing field name.
        // The suggested way of finding the field name is to:
        // 1. Make sure your scene is serialized to text.
        // 2. Search the text for the track binding component type.
        // 3. Look through the field names until you see one that looks correct.
        driver.AddFromName<FogConfig>(trackBinding.gameObject, "DistanceOffset");
        driver.AddFromName<FogConfig>(trackBinding.gameObject, "HeightOffset");
        driver.AddFromName<FogConfig>(trackBinding.gameObject, "FogEnable");
        driver.AddFromName<FogConfig>(trackBinding.gameObject, "NoiseAlpha");
        driver.AddFromName<FogConfig>(trackBinding.gameObject, "HeightMax");
        driver.AddFromName<FogConfig>(trackBinding.gameObject, "HeightMin");
        driver.AddFromName<FogConfig>(trackBinding.gameObject, "DistanceMax");
        driver.AddFromName<FogConfig>(trackBinding.gameObject, "DistanceMin");
#endif
        base.GatherProperties(director, driver);
    }
}
