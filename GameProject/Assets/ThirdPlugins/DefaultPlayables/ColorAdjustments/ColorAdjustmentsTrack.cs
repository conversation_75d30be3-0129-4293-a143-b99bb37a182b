using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

[TrackColor(0.7f, 0.5f, 1f)]
[TrackClipType(typeof(ColorAdjustmentsClip))]
[TrackBindingType(typeof(Volume))]
public class ColorAdjustmentsTrack : TrackAsset
{
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        return ScriptPlayable<ColorAdjustmentsMixerBehaviour>.Create(graph, inputCount);
    }
}
