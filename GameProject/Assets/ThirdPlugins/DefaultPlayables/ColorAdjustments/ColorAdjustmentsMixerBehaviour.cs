using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class ColorAdjustmentsMixerBehaviour : PlayableBehaviour
{
    float m_DefaultPostExposure;
    float m_DefaultContrast;
    Color m_DefaultColorFilter;
    float m_DefaultHueShift;
    float m_DefaultSaturation;

    ColorAdjustments m_TrackBinding;
    bool m_FirstFrameHappened;

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        ((Volume) playerData).profile.TryGet(out m_TrackBinding);
        if (m_TrackBinding == null)
            return;
        
        if(!m_FirstFrameHappened)
        {
            m_DefaultPostExposure = m_TrackBinding.postExposure.value;
            m_DefaultContrast = m_TrackBinding.contrast.value;
            m_DefaultColorFilter = m_TrackBinding.colorFilter.value;
            m_DefaultHueShift = m_TrackBinding.hueShift.value;
            m_DefaultSaturation = m_TrackBinding.saturation.value;

            m_FirstFrameHappened = true;
        }


        int inputCount = playable.GetInputCount();
        float blendedPostExposure = 0f;
        float blendedContrast = 0f;
        Color blendedColorFilter = Color.clear;
        float blendedHueShift = 0f;
        float blendedSaturation = 0f;

        float totalWeight = 0f;
        float greatestWeight = 0f;
        int currentInputs = 0;

        for(int i = 0; i < inputCount; i++)
        {
            float inputWeight = playable.GetInputWeight(i);
            ScriptPlayable<ColorAdjustmentsBehaviour> inputPlayable =(ScriptPlayable<ColorAdjustmentsBehaviour>)playable.GetInput(i);
            ColorAdjustmentsBehaviour input = inputPlayable.GetBehaviour();
            
            blendedPostExposure += input.PostExposure * inputWeight;
            blendedContrast += input.Contrast * inputWeight;
            blendedColorFilter += input.ColorFilter * inputWeight;
            blendedHueShift += input.HueShift * inputWeight;
            blendedSaturation += input.Saturation * inputWeight;

            totalWeight += inputWeight;

            if (inputWeight > greatestWeight)
            {
                greatestWeight = inputWeight;
            }

            if (!Mathf.Approximately (inputWeight, 0f))
                currentInputs++;
        }
        m_TrackBinding.postExposure.value = blendedPostExposure + m_DefaultPostExposure * (1f-totalWeight);
        m_TrackBinding.contrast.value = blendedContrast + m_DefaultContrast * (1f-totalWeight);
        m_TrackBinding.colorFilter.value = blendedColorFilter + m_DefaultColorFilter * (1f-totalWeight);
        m_TrackBinding.hueShift.value = blendedHueShift + m_DefaultHueShift * (1f-totalWeight);
        m_TrackBinding.saturation.value = blendedSaturation + m_DefaultSaturation * (1f-totalWeight);

    }



    public override void OnPlayableDestroy (Playable playable)
    {
        m_FirstFrameHappened = false;

        if(m_TrackBinding == null)
            return;

        m_TrackBinding.postExposure.value = m_DefaultPostExposure;
        m_TrackBinding.contrast.value = m_DefaultContrast;
        m_TrackBinding.colorFilter.value = m_DefaultColorFilter;
        m_TrackBinding.hueShift.value = m_DefaultHueShift;
        m_TrackBinding.saturation.value = m_DefaultSaturation;

    }
}
