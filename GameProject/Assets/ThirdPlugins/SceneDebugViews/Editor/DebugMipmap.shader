Shader "DebugView/Debug Mipmap"
{
    Properties
    {
        //[MainTexture]_BaseMap("", 2D) = "white" {}
    }
    
    SubShader
    {
        Tags
        {
            "Queue"="Transparent" "RenderType"="Transparent"
        }

        Pass
        {
            Name "DebugMipmap"

            ZWrite On

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Debug.hlsl"
            // #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            
            CBUFFER_START(UnityPerMaterial)
            CBUFFER_END
            
            float4 _BaseMap_ST;
            float4 _BaseMap_TexelSize;
            float4 _BaseMap_MipInfo;

            TEXTURE2D(_BaseMap);
            SAMPLER(sampler_BaseMap);
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float2 texcoord0    : TEXCOORD0;
            };
            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
            };
            Varyings vert(Attributes input)
            {
                Varyings o = (Varyings)0;
                o.positionCS = TransformObjectToHClip(input.positionOS.xyz);
                o.uv = TRANSFORM_TEX(input.texcoord0,_BaseMap);
                return o;
            }
            half4 frag(Varyings input) : SV_TARGET
            {
                half4 c;
                half4 albedo = SAMPLE_TEXTURE2D(_BaseMap,sampler_BaseMap,input.uv);
                float3 debugColor = GetDebugMipColor(albedo.rgb,_BaseMap_TexelSize,input.uv);
                // float4 debugStream = GetStreamingMipColor(?,_BaseMap_MipInfo);
                c.rgb = debugColor;
                c.a = 1;
                return c;
            }
            ENDHLSL
        }
    }

}