// Each #kernel tells which function to compile; you can have many kernels
//#pragma kernel FaceVisibility
#pragma kernel K1


#pragma kernel K3
//#pragma kernel EdgeDetection
#pragma kernel K2
#pragma kernel K4


// Create a RenderTexture with enableRandomWrite flag and set it
// with cs.SetTexture
//Texture
RWTexture2D<float4> T1;

//InTex
Texture2D<float4> v11;

//Vertices
StructuredBuffer<float3> v7;

//Faces
StructuredBuffer<int> v8;

//Result
RWStructuredBuffer<int> v13;

//MVP
uniform float4x4 v5;

//VisRenderSize
uniform int v9;

//MeshSize
uniform float v12;

uniform int sf;


//Hash
float F1(uint s)
{
    s = s ^ 2747636419u;
    s = s * 2654435769u;
    s = s ^ (s >> 16);
    s = s * 2654435769u;
    s = s ^ (s >> 16);
    s = s * 2654435769u;
    return float(s) / 4294967295.0f;
}

//InitRandom
float2 F2(float2 a)
{
    float2 r;
    r.x = abs(F1(uint(a.x * 4294967295.0f)));
    r.y = abs(F1(uint(a.y * 4294967295.0f)));

    return r;
}

//GetRandomBarycentric
float3 F3(float2 a) {
    float3 b;
    b.xy = F2(a);
    float c = b.x + b.y;
    if (c > 1.0f) {
        b.xy /= c;
        b.z = 0.0f;
    }
    else {
        b.z = 1.0f - c;
    }
    return b;
}

//FaceVisibility
[numthreads(64, 1, 1)]
void K1(uint3 id : SV_DispatchThreadID)
{
    int v1 = id.x;

    float4 v2 = float4(v7[v8[v1 * 3]], 1);
    float4 v3 = float4(v7[v8[v1 * 3 + 1]], 1);
    float4 v4 = float4(v7[v8[v1 * 3 + 2]], 1);
    v2 = mul(v5, v2);
    v3 = mul(v5, v3);
    v4 = mul(v5, v4);
    v2 /= v2.w;
    v3 /= v3.w;
    v4 /= v4.w;
    float v10 = length(cross(float3(v2.xy - v3.xy, 0), float3(v2.xy - v4.xy, 0)));

    uint t = sqrt(0.1 * v10 * v9 * v9);
    t = max(t, 2);
    float tt = 1.0f / t;

    for (float i = 0; i < 1.0f + 1e-4; i += tt)
    {
        for (float j = 0; j < 1.0f + 1e-4 - i; j += tt)
        {
            float3 ttt = i * v2.xyz + j * v3.xyz + (1.0f - i - j) * v4.xyz;
            uint2 l = (ttt.xy * 0.5f + float2(0.5f, 0.5f)) * v9;
            float v12 = v11[l].r;
            if ((ttt.z - v12) > -1e-4 * v12)
            {
                v13[v1] = 1;
                break;
            }
        }
    }
}


//EdgeDetection
// ref: http://recreationstudios.blogspot.com/2010/04/sobel-filter-compute-shader.html
[numthreads(8, 8, 1)]
void K2(uint3 id : SV_DispatchThreadID)
{
    float l13 = 0.95f;
    // Sample neighbor pixels
    // 00 01 02
    // 10 __ 12
    // 20 21 22
    float3 l1 = v11[id.xy + float2(-1, -1)].xyz;
    float3 l2 = v11[id.xy + float2(0, -1)].xyz;
    float3 l3 = v11[id.xy + float2(1, -1)].xyz;
    float3 l4 = v11[id.xy + float2(-1, 0)].xyz;
    float3 l5 = v11[id.xy + float2(1, 0)].xyz;
    float3 l6 = v11[id.xy + float2(-1, 1)].xyz;
    float3 l7 = v11[id.xy + float2(0, 1)].xyz;
    float3 l8 = v11[id.xy + float2(1, 1)].xyz;
    float3 l9 = l1 + 2 * l4 + l6 - l3 - 2 * l5 - l8;
    float3 l10 = l1 + 2 * l2 + l3 - l6 - 2 * l7 - l8;
    float3 l11 = (l9 * l9 + l10 * l10);
    float3 l12 = (l11 > l13 * l13);
    // l14 is where is normal edge but not uv edge.
    float l14 = saturate(saturate(l12.x + l12.y) - l12.z);
    float4 l15 = float4(l14+ l12.z, l12.z, l12.z, 1);
    if (l14 > 0.5f) {
        v13.IncrementCounter();

    }
    T1[id.xy] = l15;

}

//CheckPadding
[numthreads(8, 8, 1)]
void K4(uint3 id : SV_DispatchThreadID)
{
    float4 l12 = float4(0, 0, 0, 1);
    if (v11[id.xy].y > 0.5f) {
        l12 = float4(0.5, 0.5, 0.5, 1);
    }
    if (v11[id.xy].x > 0.5f)
    {
        v13.IncrementCounter();
        l12 = float4(1, 1, 0, 1);
    }
    T1[id.xy] = l12;
}

//AnalysePadding
[numthreads(8, 8, 1)]
void K3(uint3 id : SV_DispatchThreadID)
{
    float s11 = v11[id.xy].x * sf;
    float l0 = v11[id.xy + float2(-1, -1)].x * sf;
    float l2 = v11[id.xy + float2(0, -1)].x * sf;
    float l3 = v11[id.xy + float2(1, -1)].x * sf;
    float l4 = v11[id.xy + float2(-1, 0)].x * sf;
    float l5 = v11[id.xy + float2(1, 0)].x * sf;
    float l6 = v11[id.xy + float2(-1, 1)].x * sf;
    float l7 = v11[id.xy + float2(0, 1)].x * sf;
    float l8 = v11[id.xy + float2(1, 1)].x * sf;
    float k3l1 = 0;
    float k3l2 = 0;
    float k3l3 = 0;
    if (l0 > 1e-4)
    {
        k3l1 = l0;
        k3l2 += l0;
        k3l3 += 1.0f;
    }
    if (l2 > 1e-4)
    {
        k3l1 = l2;
        k3l2 += l2;
        k3l3 += 1.0f;
    }
    if (l3 > 1e-4)
    {
        k3l1 = l3;
        k3l2 += l3;
        k3l3 += 1.0f;
    }
    if (l4 > 1e-4)
    {
        k3l1 = l4;
        k3l2 += l4;
        k3l3 += 1.0f;
    }
    if (l5 > 1e-4)
    {
        k3l1 = l5;
        k3l2 += l5;
        k3l3 += 1.0f;
    }
    if (l6 > 1e-4)
    {
        k3l1 = l6;
        k3l2 += l6;
        k3l3 += 1.0f;
    }
    if (l7 > 1e-4)
    {
        k3l1 = l7;
        k3l2 += l7;
        k3l3 += 1.0f;
    }
    if (l8 > 1e-4)
    {
        k3l1 = l8;
        k3l2 += l8;
        k3l3 += 1.0f;
    }
    float l9 = l0 + 2 * l4 + l6 - l3 - 2 * l5 - l8;
    float l10 = l0 + 2 * l2 + l3 - l6 - 2 * l7 - l8;
    float l13 = 0.9f;
    float l11 = (l9 * l9 + l10 * l10);
    float k3l4 = (l11 > l13 * l13) ? 1.0f : 0.0f;
    float4 l12 = float4(0, k3l4, 0, 0);
    if (k3l3 > 1e-4)
    {
        float k3l5 = k3l2 / k3l3;
        if (abs(k3l5 - k3l1) > 1e-2)
        {
            l12.x += 1;
        }
    }
    T1[id.xy] = l12;

}