
========= The A* Pathfinding Project =========

The A* Pathfinding Project is an out-of-the-box pathfinding system
which along with ease of use has a large amount of features and blazing fast pathfinding.

The system has a Free version and a Pro version, both can found on my website (see below) and the Pro version can also be found in the Unity Asset Store
	
Documentation for the system can be found at:
	http://www.arongranberg.com/astar/docs/

A Get Started Guide can be found here:
	http://www.arongranberg.com/astar/docs/getstarted.php

If you have a question you can search for earlier posts in the forum or start a new thread:
	http://forum.arongranberg.com

The A* Pathfinding Project was made by <PERSON><PERSON>
	http://www.arongranberg.com
	
The license is the AssetStore Free License and the AssetStore Commercial License respectively for the Free and Pro versions of the project.

Using:
	DotNetZip - For creating zip files from serialized data (also modified to get rid of System.File calls which are not allowed in the webplayer)
