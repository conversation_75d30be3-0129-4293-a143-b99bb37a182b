using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Remoting;
using System.Text;
using System.Text.RegularExpressions;
using MeshCombineStudio;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

//Author:Aoicocoon
//Date:20240226
//副本场景合并工具

public class AOWMeshCombineStudioEditor : Editor
{
    [UnityEditor.MenuItem("Tools/副本场景合并")]
    public static void BakeModel()
    {
        AOWMeshCombineStudioEditorWindow window =
            (AOWMeshCombineStudioEditorWindow)EditorWindow.GetWindow<AOWMeshCombineStudioEditorWindow>();
        window.minSize = new Vector2(400, 600);
        window.maxSize = window.minSize;
        window.ShowUtility();
    }
}

public class AOWMeshCombineStudioEditorWindow : EditorWindow
{
    private static string MESH_COMBINED_RESULT_PATH = "Assets/ArtTmp/Scenes/CombinedSceneData";
    private static string MESH_COMBINE_STUDIO_PATH = "Assets/ThirdPlugins/MeshCombineStudio/MeshCombineStudio.prefab";
    private static string WORK_PATH = "Assets/Res/Scenes/Levels";
    
    //策划可能会通过脚本控制场景物件显示隐藏，合并工具要跳过这些物件，通过lua解析
    private static string LUA_PATH = "../LuaScripts/scommon/fsm";
    private static string LUA_FILTER_PREFIX = "Barrier";
    private static HashSet<string> CombineSkipHashSet;

    private static string[] LEVEL_NAME =
    {
        "Cookie",
        "Island01_1",
        "Island02_2",
        "Island03_2",
        "Island04_2",
        
        "Island01",
        "Island02",
        "Island03",
        "Island04",
        
        "Island_battle01",
        "Island_battle02",
        
        "Roguelike_01",
        
        //pve
        "PveScene",
        "PveScene02",
        "PveScene03",
        "PveScene04",
        "PveScene05",
        
        //pve with profile
        "PveBoard",
        "PveRainforest",
        "PveVillage",
        "PveForest"
    };
    
    public const string SCENE_WITH_PROFILE = @"^[a-zA-Z]+[0-9]+_(Advanced|Intermediate)$";
    public const string SCENE_ONY = @"^[a-zA-Z]+[0-9]";
    public const string FIND_ORIGIN = @"^[^_]+";

    //20250314
    //新增部分场景，只有雾效和灯光配置不同，但网格相同的情况，只需要导出一份压缩网格
    private static string[] PROFILE_CHANGED_ONLY =
    {
        "_Advanced",
        "_Intermediate"
    };

    private static string[] PVE_GROUND_NAME =
    {
        "terrain_pve_ground01",
        "terrain_pve_ground02",
        "terrain_pve_ground03"
    };

    private bool m_perLevelCtrl = false;
    
    public void OnGUI()
    {
        EditorGUILayout.LabelField(
            "当前默认修改下列场景预制体， 后续需要手动添加"
        );

        foreach (var l in LEVEL_NAME)
        {
            EditorGUILayout.LabelField(
                l
            );
        }
        EditorGUILayout.LabelField("工作目录:" + WORK_PATH);

        m_perLevelCtrl = GUILayout.Toggle(m_perLevelCtrl, "单独场景导出");

        // if (GUILayout.Button("测试Lua过滤物件"))
        // {
        //     FuckLua();
        // }
        
        if(m_perLevelCtrl)
        {
            for(int i = 0; i < LEVEL_NAME.Length; i++)
            {
                string l = LEVEL_NAME[i];
                if (GUILayout.Button(l + "合并导出"))
                {
                    Export(i);
                }

                if (GUILayout.Button(l + "还原到未合并状态"))
                {
                    Recover(i);
                }
                GUILayout.Space(10);
            }
        }
        else
        {
            if (GUILayout.Button("合并导出"))
            {
                Export();
            }

            if (GUILayout.Button("还原到未合并状态"))
            {
                Recover();
            }
        }
    }

    static void SearchSkipItem(GameObject root)
    {
        if (null == CombineSkipHashSet)
        {
            CombineSkipHashSet = new HashSet<string>();
        }
        CombineSkipHashSet.Clear();
        
        SkipLogicComponent(root);
        //Collect lua filter
        FuckLua();
    }

    static void SkipLogicComponent(GameObject root)
    {
        MonoBehaviour[] components = root.GetComponentsInChildren<MonoBehaviour>();
        foreach (var VARIABLE in components)
        {
            //skip self
            if(VARIABLE.gameObject == root) continue;
            
            CombineSkipHashSet.Add(VARIABLE.gameObject.name);
        }
    }

    static void FuckLua()
    {
        string workFolder = Path.Combine(Application.dataPath, LUA_PATH);
        if (Directory.Exists(workFolder))
        {
            Debug.Log("hasFolder:" + workFolder);

            var files = Directory.GetFiles(workFolder);
            foreach (var file in files)
            {
                string fileName = Path.GetFileName(file);
                if (fileName.ToLower().StartsWith(LUA_FILTER_PREFIX.ToLower()))
                {
                    //捞出所有 gameObjectName 指向物件
                    var contentLines = System.IO.File.ReadAllLines(file);
                    for(int i = 0; i < contentLines.Length; i++)
                    {
                        string line = contentLines[i];
                        string[] filters = {"gameObjectName", "targetObjectName" };

                        foreach (var filter in filters)
                        {
                            int idx = line.IndexOf(filter);
                            if (idx != -1)
                            {
                                var data = line.Substring(idx);
                                //var tempMatch = Regex.Match(data, "gameObjectName = \"?(<FUCK>[a-z]*[A-Z]*[0-9]*)?\"");
                                var tempMatch = Regex.Match(data, filter + "\\s*=\\s*\"(.*?)\"\\s*");
                                if (tempMatch.Success)
                                {
                                    var ret = Regex.Match(tempMatch.Value, "\"(.*?)\"");
                                    string f = ret.Value.Replace("\"", "");
                                    
                                    //如果有多级父级，取最后一级
                                    if(f.IndexOf("/") != -1)
                                    {
                                        string[] subF = f.Split("/");
                                        if (subF.Length > 0)
                                        {
                                            string lastNode = subF[subF.Length - 1];
                                            if (!CombineSkipHashSet.Contains(lastNode))
                                            {
                                                CombineSkipHashSet.Add(lastNode);
                                            }
                                        }
                                        
                                    }
                                    else
                                    {
                                        if (!CombineSkipHashSet.Contains(f))
                                        {
                                            CombineSkipHashSet.Add(f);
                                        }
                                    }
                                }
                            }
                        }
                    }
                   
                    Debug.Log(fileName);
                }
            }

            Debug.Log("Full Skip Item");
            foreach (var f in CombineSkipHashSet)
            {
                Debug.Log(f);
            }
        }
    }

    void Recover(int selectIndex = -1)
    {
        for(int i = 0; i < LEVEL_NAME.Length;i++)
        {
            if (selectIndex != -1 && i != selectIndex) continue;
            
            string l = LEVEL_NAME[i];
            // string fullPath = System.IO.Path.Combine(WORK_PATH, l);
            // fullPath += ".unity";
            //
            // Debug.Log(fullPath);
            // var scene = UnityEditor.SceneManagement.EditorSceneManager.OpenScene(fullPath);
            // if (scene.isLoaded)
            // {
            //     HandleScenePrefab(scene, l, true);
            // }
            
            string fullPath = System.IO.Path.Combine(WORK_PATH, l);
            fullPath += "/" + l;
            fullPath += ".prefab";
            GameObject scenePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(fullPath);
            if (scenePrefab)
            {
                UnityEngine.Object instancedPrefab = PrefabUtility.InstantiatePrefab(scenePrefab);
                HandleMeshCombine(scenePrefab, instancedPrefab as GameObject, l, true);
                GameObject.DestroyImmediate(instancedPrefab);
            }
        }
        EditorUtility.DisplayDialog("Done", "Done", "Done");
    }

    static void BuildProjectCallExport(System.Object windowInstance)
    {
        ObjectHandle handle = (ObjectHandle)windowInstance;
        AOWMeshCombineStudioEditorWindow realInstance = (AOWMeshCombineStudioEditorWindow)handle.Unwrap();
        
        for(int i = 0; i < LEVEL_NAME.Length; i++)
        {
            string l = LEVEL_NAME[i];
            
            string fullPath = System.IO.Path.Combine(WORK_PATH, l);
            fullPath += "/" + l;
            fullPath += ".prefab";
            GameObject scenePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(fullPath);
            if (scenePrefab)
            {
                UnityEngine.Object instancedPrefab = PrefabUtility.InstantiatePrefab(scenePrefab);
                HandleMeshCombine(scenePrefab, instancedPrefab as GameObject, l, false);
                GameObject.DestroyImmediate(instancedPrefab);
            }
        }
    }

    static string CheckPrefabIsProfileOnly(string sceneName)
    {
        string profilerOriginName = null;
        
        Regex regex = new Regex(SCENE_WITH_PROFILE);
        Match match = regex.Match(sceneName);
        if (match.Success)
        {
            Regex regex2 = new Regex(FIND_ORIGIN);
            Match match2 = regex2.Match(match.Value);
            if (match2.Success)
            {
                profilerOriginName = match2.Value;
            }
        }

        return profilerOriginName;
    }

    static void BuildScenePrefab(string sceneName, GameObject rootPrefab, List<Vector3> inputBackFaceDir = null, bool recoverOnly = false)
    {
        bool isValidScene = false;
        for (int l = 0; l < LEVEL_NAME.Length; l++)
        {
            string validSceneName = LEVEL_NAME[l].ToUpper();
            if (validSceneName == sceneName.ToUpper())
            {
                isValidScene = true;
                break;
            }
        }

        if (!isValidScene) return;

        var originPrefab = PrefabUtility.GetOutermostPrefabInstanceRoot(rootPrefab);
        var prefabPath = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(originPrefab);
        
        GameObject scenePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
        if (scenePrefab)
        {
            HandleMeshCombine(rootPrefab, rootPrefab as GameObject, sceneName, true);

            if (!recoverOnly)
            {
                UnityEngine.Object instancedPrefab = PrefabUtility.InstantiatePrefab(scenePrefab);
                HandleMeshCombine(originPrefab, rootPrefab as GameObject, sceneName, false, inputBackFaceDir);
                GameObject.DestroyImmediate(instancedPrefab);
            }
        }
    }

    static void BuildSinglePrefab(string fullPath, string sceneName)
    {
        bool isValidScene = false;
        for (int l = 0; l < LEVEL_NAME.Length; l++)
        {
            string validSceneName = LEVEL_NAME[l].ToUpper();
            if (validSceneName == sceneName.ToUpper())
            {
                isValidScene = true;
                break;
            }
        }
        
        //20250314 增加不同场景，禁用灯光和雾效不同的情况，当作一份资源导出
        if (!isValidScene)
        {
            for (int l = 0; l < LEVEL_NAME.Length; l++)
            {
                string validSceneName = LEVEL_NAME[l].ToUpper();
                if (sceneName.ToUpper().StartsWith(validSceneName) )
                {
                    isValidScene = true;
                    break;
                }
            }
        }
        
        if(!isValidScene) return;
        
        bool isProfileScene = false;
        string profileOnlyOriginName = CheckPrefabIsProfileOnly(sceneName);
        if (!string.IsNullOrEmpty(profileOnlyOriginName))
        {
            isProfileScene = true;
        }

        if (isProfileScene)
        {
            string[] allScene = new string[PROFILE_CHANGED_ONLY.Length + 1];
            allScene[0] = profileOnlyOriginName;
            for (int i = 0; i < PROFILE_CHANGED_ONLY.Length; i++)
            {
                allScene[i + 1] = profileOnlyOriginName + PROFILE_CHANGED_ONLY[i];
            }
            
            for (int i = 0; i < allScene.Length; i++)
            {
                string newPath = fullPath.Replace(sceneName, allScene[i]);

                GameObject scenePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(newPath);
                if (scenePrefab)
                {
                    UnityEngine.Object instancedPrefab = PrefabUtility.InstantiatePrefab(scenePrefab);
                    HandleMeshCombine(scenePrefab, instancedPrefab as GameObject, sceneName, false);
                    GameObject.DestroyImmediate(instancedPrefab);
                }
            }
        }
        else
        {
            GameObject scenePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(fullPath);
            if (scenePrefab)
            {
                UnityEngine.Object instancedPrefab = PrefabUtility.InstantiatePrefab(scenePrefab);
                HandleMeshCombine(scenePrefab, instancedPrefab as GameObject, sceneName, false);
                GameObject.DestroyImmediate(instancedPrefab);
            }
        }
    }

    void Export(int selectIndex = -1)
    {
        for(int i = 0; i < LEVEL_NAME.Length; i++)
        {
            if (selectIndex != -1 && i != selectIndex) continue;
            string l = LEVEL_NAME[i];
            
            // string fullPath = System.IO.Path.Combine(WORK_PATH, l);
            // fullPath += ".unity";
            //
            // Debug.Log(fullPath);
            // var scene = UnityEditor.SceneManagement.EditorSceneManager.OpenScene(fullPath);
            // if (scene.isLoaded)
            // {
            //     HandleScenePrefab(scene, l);
            // }
            string fullPath = System.IO.Path.Combine(WORK_PATH, l);
            fullPath += "/" + l;
            fullPath += ".prefab";
            GameObject scenePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(fullPath);
            if (scenePrefab)
            {
                UnityEngine.Object instancedPrefab = PrefabUtility.InstantiatePrefab(scenePrefab);
                HandleMeshCombine( scenePrefab, instancedPrefab as GameObject, l, false);
                GameObject.DestroyImmediate(instancedPrefab);
            }
        }
        EditorUtility.DisplayDialog("Done", "Done", "Done");
    }

    GameObject[] CollectChildren(GameObject target)
    {
        int childCount = target.transform.childCount;
        GameObject[] children = new GameObject[childCount];
        for (int i = 0; i < childCount; i++)
        {
            children[i] = target.transform.GetChild(i).gameObject;
        }

        return children;
    }

    static void FindMainLightComponent(Transform target, ref bool ret, ref Vector3 mainLightDirection)
    {
        int childCount = target.childCount;
        for (int i = 0; i < childCount; i++)
        {
            Transform child = target.GetChild(i);

            Light com = child.GetComponent<Light>();
            if (com && com.type == LightType.Directional)
            {
                ret = true;
                mainLightDirection = child.forward;
                return;
            }
            
            FindMainLightComponent(child, ref ret, ref mainLightDirection);
        }
    }
    
    static void FindMultiCameraComponent(Transform target, ref bool ret, List<Vector3> multiCameraDirection)
    {
        int childCount = target.childCount;
        for (int i = 0; i < childCount; i++)
        {
            Transform child = target.GetChild(i);
            if(child.gameObject.CompareTag("MainCamera"))
            {
                ret = true;
                multiCameraDirection.Add(child.transform.forward);
            }

            Camera com = child.GetComponent<Camera>();
            if (com)
            {
                ret = true;
                multiCameraDirection.Add(child.transform.forward);
            }
            
            FindMultiCameraComponent(child, ref ret, multiCameraDirection);
        }
    }
    

    static void FindCameraComponent(Transform target, ref bool ret, ref Vector3 cameraDirection)
    {
        int childCount = target.childCount;
        for (int i = 0; i < childCount; i++)
        {
            Transform child = target.GetChild(i);
            if(child.gameObject.CompareTag("MainCamera"))
            {
                ret = true;
                cameraDirection = child.transform.forward;
                return;
            }

            Camera com = child.GetComponent<Camera>();
            if (com)
            {
                ret = true;
                cameraDirection = child.transform.forward;
                return;
            }
            
            FindCameraComponent(child, ref ret, ref cameraDirection);
        }
    }

    static void DoRecover(GameObject prefabSource, GameObject prefabSceneInstance, string sceneName, string prefabPath)
    {
        PrefabUtility.UnpackPrefabInstance(prefabSceneInstance, PrefabUnpackMode.OutermostRoot,
            InteractionMode.AutomatedAction);
        
        MeshCombiner meshCombiner = null;
        GameObject meshCombinePrefab = null;
        MeshCombiner[] studioCom = prefabSceneInstance.GetComponentsInChildren<MeshCombiner>();

        if (studioCom.Length > 0)
        {
            meshCombiner = studioCom[0];
            try
            {
                meshCombiner.Reset();
                meshCombiner.enabled = false;
            }
            catch (Exception e)
            {

            }
            finally
            {
                GameObject.DestroyImmediate(meshCombiner.gameObject);
            }
        }
        else
        {
            //doNothing
        }

        //save back to prefab
        PrefabUtility.SaveAsPrefabAssetAndConnect(prefabSceneInstance, prefabPath, InteractionMode.AutomatedAction);
            
        //delete combinedMesh savefolder
        string combinedMeshSavePath = Path.Combine(MESH_COMBINED_RESULT_PATH, sceneName);
        combinedMeshSavePath = Path.Combine(Application.dataPath.Replace("Assets",""), combinedMeshSavePath);
        if (System.IO.Directory.Exists(combinedMeshSavePath))
        {
            System.IO.Directory.Delete(combinedMeshSavePath, true);

            string metaFile = combinedMeshSavePath + ".meta";

            if (System.IO.File.Exists(metaFile))
            {
                System.IO.File.Delete(metaFile);
            }
        }
       
    }

    // static void DoCopyProfiler(GameObject prefabSource, GameObject prefabSceneInstance, GameObject prefabOriginProfile, string currentScenePath,
    //     string originProfileScenePath)
    // {
    //     PrefabUtility.UnpackPrefabInstance(prefabOriginProfile, PrefabUnpackMode.Completely, InteractionMode.AutomatedAction);
    //     
    //     MeshCombiner meshCombiner = null;
    //     GameObject meshCombinePrefab = null;
    //     MeshCombiner[] studioCom = prefabSceneInstance.GetComponentsInChildren<MeshCombiner>();
    //
    //     if (studioCom.Length > 0)
    //     {
    //         meshCombiner = studioCom[0];
    //     }
    //
    //     if (!meshCombiner)
    //     {
    //         EditorUtility.DisplayDialog("错误", "需要按顺序先导出原始场景"+ prefabOriginProfile.name, "Done");
    //         return;
    //     }
    //
    //
    //     PrefabUtility.SaveAsPrefabAssetAndConnect(prefabOriginProfile, originProfileScenePath, InteractionMode.AutomatedAction);
    //     
    //     PrefabUtility.UnpackPrefabInstance(prefabSource, PrefabUnpackMode.Completely, InteractionMode.AutomatedAction);
    //     
    // }

    static void DoCombine(GameObject prefabSource, GameObject prefabSceneInstance, string sceneName, string prefabPath, List<Vector3> inputBackFaceCullingDir = null)
    {
        List<Vector3> backFaceCullingDir = new List<Vector3>();
        //skip camera prefab
        bool hasCamera = false;
        Vector3 cameraDirection = Vector3.zero;
        FindCameraComponent(prefabSceneInstance.transform, ref hasCamera, ref cameraDirection);

        if (hasCamera)
        {
            backFaceCullingDir.Add(cameraDirection);
        }

        if (null != inputBackFaceCullingDir)
        {
            backFaceCullingDir.AddRange(inputBackFaceCullingDir);
        }

        // bool hasMainLight = false;
        // Vector3 mainLightDirection = Vector3.zero;
        // FindMainLightComponent(prefabSceneInstance.transform, ref hasMainLight, ref mainLightDirection);
        //
        // if (hasMainLight)
        // {
        //     backFaceCullingDir.Add(mainLightDirection);
        // }
        
        bool isRoot = PrefabUtility.IsAnyPrefabInstanceRoot(prefabSceneInstance);
        if (isRoot)
        {
            PrefabUtility.UnpackPrefabInstance(prefabSceneInstance, PrefabUnpackMode.OutermostRoot,
                InteractionMode.AutomatedAction);
        }

        //mark shadowCache tag
        prefabSceneInstance.tag = "ShadowStatic";
        
        SearchSkipItem(prefabSceneInstance);
        
         //load meshcombinestudio
            MeshCombiner meshCombiner = null;
            GameObject meshCombinePrefab = null;
            MeshCombiner[] studioCom = prefabSceneInstance.GetComponentsInChildren<MeshCombiner>();

            if (studioCom.Length > 0)
            {
                meshCombiner = studioCom[0];
                
                if (meshCombiner.data)
                {
                    meshCombiner.Reset();
                }
                
                //delete combinedMesh savefolder
                string deletePath = Path.Combine(MESH_COMBINED_RESULT_PATH, sceneName);
                deletePath = Path.Combine(Application.dataPath.Replace("Assets",""), deletePath);

                if (System.IO.Directory.Exists(deletePath))
                {
                    System.IO.Directory.Delete(deletePath, true);
                }
            }
            else
            {
                meshCombinePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(MESH_COMBINE_STUDIO_PATH);
                if (meshCombinePrefab)
                {
                    var go = GameObject.Instantiate(meshCombinePrefab);
                    go.transform.parent = prefabSceneInstance.transform;
                    meshCombiner = go.GetComponent<MeshCombiner>();

                    //GameObject.DestroyImmediate(meshCombinePrefab);
                }
            } 

            ConfigureMeshCombiner(meshCombiner, prefabSceneInstance, backFaceCullingDir);

            if (null == MeshCombineJobManager.instance)
            {
                meshCombiner.InitMeshCombineJobManager();
            }

            MeshCombineJobManager.ResetMeshCache();
            meshCombiner.aow_prepareCombineState = true;
            meshCombiner.CombineAll();
            
            if (meshCombiner.unreadableMeshes.Count > 0)
            {
                MakeMeshesReadableInImportSettings(meshCombiner);
                meshCombiner.aow_prepareCombineState = false;
                meshCombiner.CombineAll();
            }
       
            //20250314, 如果当前处理的是仅仅光照表现不同的场景，则将Mesh存储位置修改为原始场景位置，这样网格可复用
            bool isProfileScene = false;
            string profileOnlyOriginName = CheckPrefabIsProfileOnly(sceneName);
            if (!string.IsNullOrEmpty(profileOnlyOriginName))
            {
                isProfileScene = true;
            }

            //isProfileScene = false;
            string combinedMeshSavePath = Path.Combine(MESH_COMBINED_RESULT_PATH, isProfileScene ? profileOnlyOriginName : sceneName);
            
            SaveCombinedMeshes(meshCombiner, combinedMeshSavePath);
       
            if (meshCombiner.readableEditedByThisPlguinMeshes.Count > 0)
            {
                MakeMeshesUnReadableInImportSettings(meshCombiner);
            }
            
            //postProcess
            var reverObjs = FindObjectsByType<MeshFilterRevert>(FindObjectsSortMode.None);
            if (reverObjs != null)
            {
                foreach (var comp in reverObjs)
                {
                    if (meshCombiner.removeOriginalMeshReference)
                    {
                        MeshRenderer mr = comp.gameObject.GetComponent<MeshRenderer>();
                        MeshFilter mf = comp.gameObject.GetComponent<MeshFilter>();
                        comp.DestroyAndReferenceMeshFilter(mf);
                        mr.enabled = false;
                    }
                }
            }
            
            //save back to prefab
            PrefabUtility.SaveAsPrefabAssetAndConnect(prefabSceneInstance, prefabPath, InteractionMode.AutomatedAction);
    }
    
    static void DoSubPrefabCombine(GameObject prefabSource, GameObject[] prefabSceneInstance, string sceneName,
        string prefabPath)
    {
        PrefabUtility.UnpackPrefabInstance(prefabSource, PrefabUnpackMode.OutermostRoot,
            InteractionMode.AutomatedAction);
        
        //skip camera prefab
        bool hasCamera = false;
        List<Vector3> multiCameraDirection = new List<Vector3>();
        FindMultiCameraComponent(prefabSource.transform, ref hasCamera, multiCameraDirection);
        
        // bool hasMainLight = false;
        // Vector3 mainLightDirection = Vector3.zero;
        // FindMainLightComponent(prefabSource.transform, ref hasMainLight, ref mainLightDirection);
        //
        // if (hasMainLight)
        // {
        //     multiCameraDirection.Add(mainLightDirection);
        // }
        
        MeshCombiner meshCombiner = null;
        GameObject meshCombinePrefab = null;
        MeshCombiner[] studioCom = prefabSource.GetComponentsInChildren<MeshCombiner>();

        if (studioCom.Length > 0)
        {
            meshCombiner = studioCom[0];
                
            if (meshCombiner.data)
            {
                meshCombiner.Reset();
            }
                
            //delete combinedMesh savefolder
            string deletePath = Path.Combine(MESH_COMBINED_RESULT_PATH, sceneName);
            deletePath = Path.Combine(Application.dataPath.Replace("Assets",""), deletePath);
            System.IO.Directory.Delete(deletePath, true);

            foreach (var oldCombiner in studioCom)
            {
                GameObject.Destroy(oldCombiner);
            }
        }

        DoSubPrefab(prefabSource,prefabSceneInstance, multiCameraDirection, sceneName,
            () =>
            {
                PrefabUtility.SaveAsPrefabAssetAndConnect(prefabSource, prefabPath, InteractionMode.AutomatedAction);
            }
        );
    }

    static void DoSubPrefab(GameObject prefabSource, GameObject[] prefabSceneInstance, List<Vector3> cameraDirection, string sceneName, Action endCall)
    {
        List<string> allSubNewFile = new List<string>();
        
        foreach (var ground in prefabSceneInstance)
        {
            MeshCombiner meshCombiner = null;
            GameObject meshCombinePrefab = null;
            PrefabUtility.UnpackPrefabInstance(ground, PrefabUnpackMode.Completely, InteractionMode.AutomatedAction);
            //mark shadowCache tag
            ground.tag = "ShadowStatic";
            
            meshCombinePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(MESH_COMBINE_STUDIO_PATH);
            if (meshCombinePrefab)
            {
                var go = GameObject.Instantiate(meshCombinePrefab);
                go.transform.parent = ground.transform;
                meshCombiner = go.GetComponent<MeshCombiner>();

                //GameObject.DestroyImmediate(meshCombinePrefab);
            }
            
            ConfigureMeshCombiner(meshCombiner, ground, cameraDirection);

            //if (null == MeshCombineJobManager.instance)
            {
                meshCombiner.InitMeshCombineJobManager();
            }
            
            MeshCombineJobManager.ResetMeshCache();
            meshCombiner.aow_prepareCombineState = true;
            meshCombiner.CombineAll();
        
            string combinedMeshSavePath = Path.Combine(MESH_COMBINED_RESULT_PATH, sceneName);
        
            if (meshCombiner.unreadableMeshes.Count > 0)
            {
                MakeMeshesReadableInImportSettings(meshCombiner);
                meshCombiner.aow_prepareCombineState = false;
                meshCombiner.CombineAll();
            }

           
            SaveCombinedMeshes(meshCombiner, combinedMeshSavePath, allSubNewFile, ground.name + "_");
       
            if (meshCombiner.readableEditedByThisPlguinMeshes.Count > 0)
            {
                MakeMeshesUnReadableInImportSettings(meshCombiner);
            }
        }
        
        endCall?.Invoke();
    }

    static GameObject[] IsHasPveRoot(GameObject prefabSceneInstance)
    {
        List<GameObject> outputList = new List<GameObject>();
        var childCount = prefabSceneInstance.transform.childCount;
        for (int i = 0; i < childCount; i++)
        {
            var child = prefabSceneInstance.transform.GetChild(i);
            var childName = child.name;
            foreach (var pveGroundName in PVE_GROUND_NAME)
            {
                if (pveGroundName == childName)
                {
                    outputList.Add(child.gameObject);
                }
            }
        }

        return outputList.ToArray();
    }

    static void HandleMeshCombine(GameObject prefabSource, GameObject prefabSceneInstance, string sceneName, bool isRecover, List<Vector3> inputBackFaceCullingDir = null)
    {
        var prefabPath = AssetDatabase.GetAssetPath(prefabSource);

        if (string.IsNullOrEmpty(prefabPath))
        {
            prefabPath = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(prefabSource);
        }
        
        if (string.IsNullOrEmpty(prefabPath))
        {
            Debug.Log("AOWMeshCombineStudioEditorWindow: Prefab Path is Missing" + prefabSource.name);
            return;
        }
        
        if (isRecover)
        {
            DoRecover(prefabSource, prefabSceneInstance, sceneName, prefabPath);
        }
        else
        {
            GameObject[] pveGroundRoot = IsHasPveRoot(prefabSceneInstance);
            if (null == pveGroundRoot || pveGroundRoot.Length == 0)
            {
                DoCombine(prefabSource, prefabSceneInstance, sceneName, prefabPath, inputBackFaceCullingDir);
            }
            else
            {
                DoSubPrefabCombine(prefabSceneInstance, pveGroundRoot, sceneName, prefabPath);
            }
        }
    }

    static void SaveCombinedMeshes(MeshCombiner meshCombiner, string savePath, List<string> customNewFileList = null, string customPrefix = null)
    {
        if (meshCombiner.saveMeshesFolder == "") meshCombiner.saveMeshesFolder = Application.dataPath;

        if (!Directory.Exists(savePath))
        {
            Directory.CreateDirectory(savePath);
        }
        // else
        // {
        //     System.IO.Directory.Delete(savePath, true);
        //     Directory.CreateDirectory(savePath);
        // }
        
        // string savePath = EditorUtility.SaveFolderPanel("Save Combined Meshes", meshCombiner.saveMeshesFolder, "");
        // if (savePath == "") return;
        // else if (!savePath.Contains(Application.dataPath))
        // {
        //     Debug.Log("(MeshCombineStudio) => Meshes need to be saved in one of this project folders.");
        //     return;
        // }

        meshCombiner.saveMeshesFolder = savePath;

        CachedComponents[] cachedComponents = meshCombiner.transform.GetComponentsInChildren<CachedComponents>();

        for (int j = 0; j < cachedComponents.Length; j++)
        {
            if (cachedComponents[j].garbageCollectMesh == null)
            {
                Debug.Log(
                    "Mesh Combine Studio => Meshes are already saved. You need to recombine first before you can save again.");
                return;
            }
        }

        if (cachedComponents == null || cachedComponents.Length == 0)
        {
            Debug.Log("Mesh Combine Studio => No meshes are found for saving");
            return;
        }

        string relavtivePath = savePath.Replace(Application.dataPath, "Assets");

        int length = cachedComponents.Length;
        int cachedMeshes = 0;
        int updatedMeshes = 0;

        string[] preExistingFiles = null;

        //if (deleteFilesFromSaveFolder.boolValue)
        if(true)
        {
            DirectoryInfo d = new DirectoryInfo(meshCombiner.saveMeshesFolder);
            FileInfo[] files = d.GetFiles("*.asset");

            preExistingFiles = new string[files.Length];

            for (int j = 0; j < files.Length; j++)
            {
                preExistingFiles[j] = files[j].Name;
            }
        }

        List<string> newFiles = null == customNewFileList ? new List<string>() : customNewFileList;

        AssetDatabase.StartAssetEditing();
        int i = 0;

        StringBuilder nameBuilder = new StringBuilder();

        try
        {
            for (i = 0; i < length; i++)
            {
                CachedComponents cachedComponent = cachedComponents[i];

                nameBuilder.Length = 0;
                Mesh mesh = cachedComponent.mf.sharedMesh;

                string assetPath = AssetDatabase.GetAssetPath(mesh);
                if (assetPath != null && assetPath.Length > 0) continue;

                if (!string.IsNullOrEmpty(customPrefix))
                {
                    nameBuilder.Append(customPrefix);
                }
                nameBuilder.Append(mesh.name);
                ComputeFileName(cachedComponent.t, ref nameBuilder);
                SanitizeFileName(ref nameBuilder);
                UniquifyMeshName(cachedComponent, ref nameBuilder);
                nameBuilder.Append(".asset");

                var meshFileName = nameBuilder.ToString();
                assetPath = Path.Combine(relavtivePath, meshFileName);
                var shouldCancel = EditorUtility.DisplayCancelableProgressBar("Saving meshes to disk " + (i / length),
                    assetPath, ((float)i) / length);
                if (shouldCancel) break;
                //newFiles[i] = meshFileName;
                newFiles.Add(meshFileName);

                GarbageCollectMesh garbageCollectMesh =
                    cachedComponent.garbageCollectMesh.GetComponent<GarbageCollectMesh>();
                if (garbageCollectMesh != null)
                {
                    garbageCollectMesh.mesh = null;
                    DestroyImmediate(garbageCollectMesh);
                }

                var existingMesh = AssetDatabase.LoadAssetAtPath<Mesh>(assetPath);
                if (existingMesh != null)
                {
                    if (CompareMeshes(existingMesh, mesh))
                    {
                        cachedComponent.mf.sharedMesh = existingMesh;
                        cachedMeshes++;
                        continue;
                    }

                    updatedMeshes++;
                }

                AssetDatabase.CreateAsset(mesh, assetPath);
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            AssetDatabase.StopAssetEditing();
        }

        Debug.Log("Mesh Combine Studio => Done saving. Cached Meshes: " + cachedMeshes + " Updated Meshes: " +
                  updatedMeshes + " New Meshes: " + (i - cachedMeshes - updatedMeshes) + " Skipped Meshes: " +
                  (cachedComponents.Length - i));

        // We didn't abort
        //if (i == length && deleteFilesFromSaveFolder.boolValue)
        if (i == length && true)
        {
            var filesToRemove = preExistingFiles.Except(newFiles).ToArray();
            foreach (var file in filesToRemove)
            {
                File.Delete(Path.Combine(meshCombiner.saveMeshesFolder, file));
            }

            Debug.Log("Mesh Combine Studio => Deleted " + filesToRemove.Count() + " outdated, unused files.");
        }

        AssetDatabase.SaveAssets();

        UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty((meshCombiner).gameObject.scene);

        AssetDatabase.Refresh();
    }
    
    private static ulong uniquifier = 0;
    private static void UniquifyMeshName(CachedComponents cached_components, ref StringBuilder nameBuilder)
    {
        nameBuilder.Append("_");
        nameBuilder.Append(++uniquifier);
        nameBuilder.Append("_Shadow_");
        nameBuilder.Append(Enum.GetName(typeof(ShadowCastingMode), cached_components.mr.shadowCastingMode));
        nameBuilder.Append("_");
        nameBuilder.Append(cached_components.mf.sharedMesh.vertices.Length);
    }
    
    private static void SanitizeFileName(ref StringBuilder nameBuilder)
    {
        nameBuilder.Replace('\\', '_');
        nameBuilder.Replace('/', '_');
        nameBuilder.Replace('.', ';');
    }
    
    private static void ComputeFileName(Transform t, ref StringBuilder meshNameBuilder)
    {
        // check the direct parent. It can be LODGroup X, or LODX, or Cell XXX, or when not using groups, Combined Objects
        var parentT = t.parent;
        if (parentT.GetComponent<MeshCombiner>())
        {
            return;
        }
        meshNameBuilder.Append("_");
        if (parentT.name.StartsWith("Cell"))
        {
            meshNameBuilder.Append(parentT.name.Substring(5));
        }
        else if (parentT.name.StartsWith("LOD"))
        {
            meshNameBuilder.Append(parentT.name);
        }
        else if (parentT.name.StartsWith("Combined Objects"))
        {
            meshNameBuilder.Append(parentT.name);
        }
        else
        {
            Debug.LogError("Unexpected parent with name " + parentT.name);
            return;
        }
        ComputeFileName(parentT, ref meshNameBuilder);
    }
    
    static bool CompareMeshes(Mesh oldMesh, Mesh newMesh)
    {
        if (oldMesh.vertices.Length != newMesh.vertices.Length) return false;

        return Enumerable.SequenceEqual(oldMesh.vertices, newMesh.vertices);
    }

    public static void MakeMeshesUnReadableInImportSettings(MeshCombiner meshCombiner)
    {
        HashSet<Mesh> readableMeshes = meshCombiner.readableEditedByThisPlguinMeshes;

        foreach (Mesh mesh in readableMeshes)
        {
            string path = AssetDatabase.GetAssetPath(mesh);
            if (path.Length > 0)
            {
                var modelImporter = ModelImporter.GetAtPath(path) as ModelImporter;
                if (modelImporter == null)
                {
                    Debug.LogError("Mesh " + mesh.name +
                                   " doesn't have a ModelImporter and is a mesh.asset, it's tricky to make readable. If you really need this contact me for support.");
                }
                else
                {
                    modelImporter.isReadable = false;
                    EditorUtility.SetDirty(modelImporter);
                    modelImporter.SaveAndReimport();
                }
            }
            else
            {
                Debug.LogError("Mesh " + mesh.name + " is not an Asset and cannot be mader readable");
            }
        }

        Debug.Log("(MeshCombineStudio) => Read/Write Enabled on " + readableMeshes.Count + " meshes");

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        readableMeshes.Clear();
    }

    public static void MakeMeshesReadableInImportSettings(MeshCombiner meshCombiner)
    {
        HashSet<Mesh> unreadableMeshes = meshCombiner.unreadableMeshes;
        meshCombiner.readableEditedByThisPlguinMeshes.Clear();

        foreach (Mesh mesh in unreadableMeshes)
        {
            string path = AssetDatabase.GetAssetPath(mesh);
            if (path.Length > 0)
            {
                var modelImporter = ModelImporter.GetAtPath(path) as ModelImporter;
                if (modelImporter == null)
                {
                    Debug.LogError("Mesh " + mesh.name +
                                   " doesn't have a ModelImporter and is a mesh.asset, it's tricky to make readable. If you really need this contact me for support.");
                }
                else
                {
                    try
                    {
                        modelImporter.isReadable = true;
                        EditorUtility.SetDirty(modelImporter);
                        modelImporter.SaveAndReimport();
                        meshCombiner.readableEditedByThisPlguinMeshes.Add(mesh);
                    }
                    catch (Exception e)
                    {
                        EditorUtility.DisplayDialog("Error", "导出错误，查看信息" + e.Message, "ok");
                    }
                    
                }
            }
            else
            {
                Debug.LogError("Mesh " + mesh.name + " is not an Asset and cannot be mader readable");
            }
        }

        Debug.Log("(MeshCombineStudio) => Read/Write Enabled on " + unreadableMeshes.Count + " meshes");

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        unreadableMeshes.Clear();
    }


    static void ConfigureMeshCombiner(MeshCombiner combiner, GameObject searchRoot, List<Vector3> cameraDirection)
    {
        if (!combiner) return;
        combiner.searchOptions.parentGOs = new GameObject[1] { searchRoot };

        if (null != CombineSkipHashSet && CombineSkipHashSet.Count > 0)
        {
            if (null == combiner.searchOptions.gameObjectNameSkipList)
            {
                combiner.searchOptions.gameObjectNameSkipList = new List<string>();
            }
            combiner.searchOptions.gameObjectNameSkipList.Clear();
            
            foreach(var filterStr in CombineSkipHashSet)
            {
                combiner.searchOptions.gameObjectNameSkipList.Add(filterStr);
            }
        }

        combiner.searchOptions.objectCenter = MeshCombiner.ObjectCenter.TransformPosition;
        combiner.searchOptions.onlyActive = true;
        combiner.searchOptions.onlyStatic = false;
        combiner.searchOptions.onlyActiveMeshRenderers = true;
        combiner.searchOptions.useSearchBox = false;
        combiner.searchOptions.useMaxBoundsFactor = false;
        //combiner.searchOptions.maxBoundsFactor = 1.5f;
        combiner.searchOptions.useVertexInputLimit = false;
        combiner.searchOptions.useLayerMask = false;
        combiner.searchOptions.useTag = false;
        combiner.searchOptions.useComponentsFilter = false;
        combiner.searchOptions.useNameContains = false;
        combiner.searchOptions.usePassEnabled = false;
        combiner.searchOptions.ignorePlaneShadow = true;
        combiner.searchOptions.ignoreLEGOWater = true;

        combiner.combineConditionSettings.sameShadowCastingMode = true;
        combiner.combineConditionSettings.sameReceiveShadows = true;
        combiner.combineConditionSettings.sameReceiveGI = true;
        combiner.combineConditionSettings.sameMaterial = true;
        combiner.combineConditionSettings.sameLightmapScale = true;
        combiner.combineConditionSettings.sameLightProbeUsage = true;
        combiner.combineConditionSettings.sameReflectionProbeUsage = true;
        combiner.combineConditionSettings.sameProbeAnchor = true;
        combiner.combineConditionSettings.sameMotionVectorGenerationMode = true;
        combiner.combineConditionSettings.sameStaticEditorFlags = true;
        combiner.combineConditionSettings.sameLayer = true;

        combiner.combineMode = CombineMode.StaticObjects;
        combiner.removeBackFaceTriangles = null != cameraDirection;
        if (combiner.removeBackFaceTriangles)
        {
            combiner.backFaceTriangleMode = MeshCombiner.BackFaceTriangleMode.Direction;

            if (cameraDirection.Count == 1)
            {
                combiner.backFaceDirection = cameraDirection[0];
            }
            else
            {
                combiner.MultiBackFaceDirection = true;
                combiner.backFaceDirections = cameraDirection.ToArray();
            }
        }
        combiner.cellSize = 24;
        combiner.cellOffset = Vector3.one * (combiner.cellSize / 2.0f);
        combiner.removeOriginalMeshReference = true;
        combiner.weldVertices = false;
        combiner.addMeshColliders = false;
        combiner.makeMeshesUnreadable = true;
        combiner.excludeSingleMeshes = true;
        combiner.useVertexOutputLimit = false;
        combiner.copyBakedLighting = false;
        combiner.rebakeLighting = false;
        combiner.useCustomInstantiatePrefab = false;

        combiner.jobSettings.combineJobMode = MeshCombineJobManager.CombineJobMode.CombineAtOnce;
        combiner.jobSettings.useMainThread = true;
        combiner.jobSettings.combineMeshesPerFrame = 128;

        uniquifier = 0;
    }
}