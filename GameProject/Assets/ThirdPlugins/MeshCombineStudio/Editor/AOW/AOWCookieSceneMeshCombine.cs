// FileName:AOWCookieSceneMeshCombine.cs
// Author:Aoicocoon
// Date:2025-05-13 10::05::00

using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using Object = System.Object;

namespace MeshCombineStudio.AOW
{
    public class AOWCookieSceneMeshCombine
    {
        [MenuItem("GameObject/CookieScene减面还原")]
        public static void RecoverCookieSceneCombine()
        {
            var scene = EditorSceneManager.GetActiveScene();
            if (!scene.IsValid()) return;
            var allgos = scene.GetRootGameObjects();

            GameObject mainCamsGo = null;
            GameObject cookieTerrainGo = null;
            foreach (var go in allgos)
            {
                if (PrefabUtility.GetPrefabInstanceStatus(go) == PrefabInstanceStatus.Connected)
                {
                    if (go.name.ToLower().Equals("com_camera"))
                    {
                        mainCamsGo = go;
                    }
                    else if(go.name.ToLower().Equals("terrain_cookie_01"))
                    {
                        cookieTerrainGo = go;
                    }
                }
            }
            
            Debug.Log(mainCamsGo?.name);
            Debug.Log(cookieTerrainGo?.name);

            if (mainCamsGo && cookieTerrainGo)
            {
                FindAndDoCompress(scene.name, cookieTerrainGo, new List<Vector3>(){mainCamsGo.transform.forward}, true);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "找不到相机或场景预制", "ok");
            }
        }
        
        [MenuItem("GameObject/CookieScene减面")]
        public static void CookieSceneCombine()
		{
            var scene = EditorSceneManager.GetActiveScene();
            if (!scene.IsValid()) return;
            var allgos = scene.GetRootGameObjects();

            GameObject mainCamsGo = null;
            GameObject cookieTerrainGo = null;
            foreach (var go in allgos)
            {
                if (PrefabUtility.GetPrefabInstanceStatus(go) == PrefabInstanceStatus.Connected)
                {
                    if (go.name.ToLower().Equals("com_camera"))
                    {
                        mainCamsGo = go;
                    }
                    else if(go.name.ToLower().Equals("terrain_cookie_01"))
                    {
                        cookieTerrainGo = go;
                    }
                }
            }
            
            Debug.Log(mainCamsGo?.name);
            Debug.Log(cookieTerrainGo?.name);

            if (mainCamsGo && cookieTerrainGo)
            {
                FindAndDoCompress(scene.name, cookieTerrainGo, new List<Vector3>(){mainCamsGo.transform.forward});
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "找不到相机或场景预制", "ok");
            }
        }
        
        static void DoCompress(System.Type curType, Assembly curAssembly, string sceneName, GameObject root, List<Vector3> backFaceDir, bool recoverOnly = false)
        {
            System.Object combiner = Activator.CreateInstance(System.AppDomain.CurrentDomain, curAssembly.FullName, curType.Name);
            if (null == combiner)
            {
                Debug.LogError("Create AOWMeshCombineStudioEditorWindow Failed");
            }
            else
            {
                var method = curType.GetMethod("BuildScenePrefab", BindingFlags.Static | BindingFlags.Instance | BindingFlags.NonPublic);
                method.Invoke(null, new Object[] { sceneName, root, backFaceDir, recoverOnly });
            }
        }
        
        static void FindAndDoCompress(string sceneName, GameObject root, List<Vector3> backFaceDir, bool recoverOnly = false)
        {
            bool finded = false;
            Assembly[] allAssembly = System.AppDomain.CurrentDomain.GetAssemblies();
            if (null != allAssembly)
            {
                foreach (Assembly singleAssembly in allAssembly)
                {
                    var types = singleAssembly.GetTypes();
                    foreach (var type in types)
                    {
                        if (type.FullName.Contains("AOWMeshCombineStudioEditorWindow"))
                        {
                            DoCompress(type, singleAssembly, sceneName, root, backFaceDir, recoverOnly);
                            finded = true;
                            break;
                        }
                    }
                }
            }
        }

        [MenuItem("GameObject/CookieScene减面", true)]
        public static bool ValidateCookieSceneCombine()
        {
            var scene = EditorSceneManager.GetActiveScene();
            if (scene.IsValid() && scene.name.Equals("Cookie", StringComparison.Ordinal))
            {
                return true;
            }

            return false;
        }
        
        [MenuItem("GameObject/CookieScene减面还原", true)]
        public static bool ValidateRecoverCookieSceneCombine()
        {
            var scene = EditorSceneManager.GetActiveScene();
            if (scene.IsValid() && scene.name.Equals("Cookie", StringComparison.Ordinal))
            {
                return true;
            }

            return false;
        }
        
    }	
}