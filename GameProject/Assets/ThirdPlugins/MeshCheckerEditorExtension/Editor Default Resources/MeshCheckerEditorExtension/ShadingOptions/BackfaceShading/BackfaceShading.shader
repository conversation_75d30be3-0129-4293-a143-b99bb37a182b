Shader "Hidden/MeshChecker/BackfaceShading" 
{
	Properties 
	{
		_Color ("Color", Color) = (1,1,1,1)
		_MainTex ("Albedo (RGB)", 2D) = "white" {}
		_Glossiness ("Smoothness", Range(0,1)) = 0.5
		_Metallic ("Metallic", Range(0,1)) = 0.5

	}
	
	SubShader
    {
        Tags { "Queue"="Geometry" "RenderType" = "Opaque" "IgnoreProjector" = "True" "RenderPipeline" = "UniversalPipeline" }
        LOD 200
    	Cull Off
        Pass
        {
            Name "BackfaceShading"
            HLSLPROGRAM
            // Required to compile gles 2.0 with standard srp library
            #pragma prefer_hlslcc gles
            #pragma exclude_renderers d3d11_9x
            //#pragma only_renderers gles gles3 glcore vulkan metal 手机
            //#pragma target 2.0
            #pragma vertex vert
            #pragma fragment frag
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/SpaceTransforms.hlsl"
            struct Attributes
            {
                float4 positionOS       : POSITION;
                float2 uv               : TEXCOORD0;
            	float3 normalOS			: NORMAL;
            };
            struct Varyings
            {
                float4 positionCS       : SV_POSITION;
                float2 uv           : TEXCOORD0;
            	float3 normalWS		: TEXCOORD1;
            	half3 viewWS		: TEXCOORD2;
            };
            
            CBUFFER_START(UnityPerMaterial)
            float4 _MainTex_ST;
            half4 _Color;
            half _Glossiness;
            half _Metallic;
            CBUFFER_END
            
            TEXTURE2D (_MainTex);SAMPLER(sampler_MainTex);
            // #define smp _linear_clampU_mirrorV
            // SAMPLER(smp);
            Varyings vert(Attributes v)
            {
                Varyings o = (Varyings)0;
            	float3 positionWS = TransformObjectToWorld(v.positionOS.xyz);
                o.positionCS = TransformObjectToHClip(v.positionOS.xyz);
            	o.normalWS = TransformObjectToWorldNormal(v.normalOS,true);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
            	o.viewWS = GetWorldSpaceViewDir(positionWS);
                return o;
            }
            half4 frag(Varyings i,bool facing:SV_IsFrontFace) : SV_Target
            {
                half4 c;
                half4 baseMap = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv);
                c = baseMap * _Color;
            	Light mainLight = GetMainLight();
				half3 L = normalize(mainLight.direction);
				half3 lightColor = mainLight.color;
				half3 N = i.normalWS;
				half lambert = saturate(dot(N , L)) * 0.5 +0.5;
            	half3 V = normalize(i.viewWS);
				half3 H = normalize(L + V);
			    half NdotH = max(0, dot(H, N));
			    half3 spec = lightColor  * pow(NdotH, exp2((_Glossiness+_Metallic)*6));
            	c = lerp(c,0.04,_Metallic);
            	c *= lambert;
            	c.rgb += spec;
            	
            	half4 backColor = half4(1, 0, 1, 1);
            		
            	c = IS_FRONT_VFACE(facing,c,backColor);
            	
                return c;
            }
            ENDHLSL
        }
    }
	/*
	SubShader 
	{
		Tags { "RenderType"="Opaque" }
		LOD 200
		
		CGPROGRAM
		// Physically based Standard lighting model, and enable shadows on all light types
		#pragma surface surf Standard fullforwardshadows

		// Use shader model 3.0 target, to get nicer looking lighting
		#pragma target 3.0

		sampler2D _MainTex;

		struct Input 
		{
			float2 uv_MainTex;
		};

		half _Glossiness;
		half _Metallic;
		fixed4 _Color;

		void surf (Input IN, inout SurfaceOutputStandard o) 
		{
			// Albedo comes from a texture tinted by color
			fixed4 c = tex2D (_MainTex, IN.uv_MainTex) * _Color;
			o.Albedo = c.rgb;
			// Metallic and smoothness come from slider variables
			o.Metallic = _Metallic;
			o.Smoothness = _Glossiness;
			o.Alpha = c.a;
		}
		ENDCG

		Pass
		{
			Cull Front
			Color(1, 0, 1, 1)
		}
	} 
	*/

	FallBack Off
}



    
