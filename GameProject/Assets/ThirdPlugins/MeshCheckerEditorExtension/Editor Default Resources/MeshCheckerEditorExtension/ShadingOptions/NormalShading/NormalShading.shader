Shader "Hidden/MeshChecker/NormalShading"
{
	Properties
	{
		_BumpMap("NormalMap", 2D) = "bump" {}
		_BumpScale("Scale", Float) = 1.0
	}

		SubShader
		{
			Pass
			{
				CGPROGRAM

				#pragma vertex vert
				#pragma fragment frag
				#include "UnityCG.cginc"

				struct v2f
				{
					float4 pos : SV_POSITION;
					fixed3 normal : NORMAL;
					float2 uv: TEXCOORD0;
				};

				sampler2D _BumpMap;
				float _BumpScale;

				v2f vert(appdata_base v)
				{
					v2f o;
	#if UNITY_VERSION >= 560
					o.pos = UnityObjectToClipPos(v.vertex);
	#else
					o.pos = UnityObjectToClipPos(v.vertex);
	#endif
					o.uv = v.texcoord;
					o.normal = v.normal;
					return o;
				}

				fixed4 frag(v2f i) : SV_Target
				{
					float3 n = i.normal;
					float3 tn = UnpackNormal(tex2D(_BumpMap, i.uv));
					float3 normal = normalize(float3(n.xy + tn.xy*_BumpScale, n.z)) * 0.5 + 0.5;

					return fixed4(normal, 1);
				}

				ENDCG
			}
		}
}
