Shader "Hidden/MeshChecker/VertexColorShading"
{
	SubShader
	{
		Pass
		{
			CGPROGRAM

			#pragma vertex vert
			#pragma fragment frag
			#include "UnityCG.cginc"

			struct appdata
			{
				float4 vertex : POSITION;
				float4 color : COLOR;
			};

			struct v2f
			{
				float4 pos : SV_POSITION;
				fixed3 color : COLOR0;
			};

			v2f vert(appdata v)
			{
				v2f o;
#if UNITY_VERSION >= 560
				o.pos = UnityObjectToClipPos(v.vertex);
#else
				o.pos = UnityObjectToClipPos(v.vertex);
#endif
				o.color = v.color;
				return o;
			}

			fixed4 frag(v2f i) : SV_Target
			{
				return fixed4(i.color, 1);
			}

			ENDCG
		}
	}
}
