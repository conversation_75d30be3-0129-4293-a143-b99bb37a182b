using JetBrains.Annotations;
using UnityEngine;

// ReSharper disable StyleCop.SA1300
namespace HightlanderSolutions.MeshCheckerEditorExtension
{
    /// <summary>
    ///     Represents a <see cref="SkinnedMeshRenderer"/> wrapper for abstract usage of the <see cref="AbstractMeshFilter"/>.
    /// </summary>
    [PublicAPI]
    public sealed class MCSkinnedMeshRenderer : AbstractMeshFilter, IMeshRenderer
    {
        /// <summary>
        ///     Gets an associated <see cref="SkinnedMeshRenderer"/>
        /// </summary>
        [PublicAPI]
        public SkinnedMeshRenderer SkinnedMeshRenderer { get; private set; }

        /// <summary>
        ///     Initializes a new instance of the <see cref="MCSkinnedMeshRenderer"/> class.
        /// </summary>
        /// <param name="skinnedMeshRenderer">The skinned mesh renderer.</param>
        [PublicAPI]
        public MCSkinnedMeshRenderer(SkinnedMeshRenderer skinnedMeshRenderer)
        {
            SkinnedMeshRenderer = skinnedMeshRenderer;
        }

        #region Overrides of AbstractMeshFilter

        /// <summary>
        ///     <para>Gets a shared mesh.</para>
        ///     <para>
        ///         This is a dummy property which is proxy for the <see cref="sharedMesh"/> property. 
        ///         You should not use it.
        ///     </para>
        /// </summary>
        [PublicAPI]
        public override Mesh mesh
        {
            get
            {
                return sharedMesh;
            }

            set
            {
                sharedMesh = value;
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override Mesh sharedMesh
        {
            get
            {
                return SkinnedMeshRenderer.sharedMesh;
            }

            set
            {
                SkinnedMeshRenderer.sharedMesh = value;
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        [NotNull]
        public override GameObject gameObject
        {
            get
            {
                return SkinnedMeshRenderer.gameObject;
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override string tag
        {
            get
            {
                return SkinnedMeshRenderer.tag;
            }

            set
            {
                SkinnedMeshRenderer.tag = value;
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override Transform transform
        {
            get
            {
                return SkinnedMeshRenderer.transform;
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override HideFlags hideFlags
        {
            get
            {
                return SkinnedMeshRenderer.hideFlags;
            }

            set
            {
                SkinnedMeshRenderer.hideFlags = value;
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override string name
        {
            get
            {
                return SkinnedMeshRenderer.name;
            }

            set
            {
                SkinnedMeshRenderer.name = value;
            }
        }

        #endregion

        #region Implementation of IMeshRenderer

        /// <inheritdoc />
        public Material[] sharedMaterials
        {
            get
            {
                return SkinnedMeshRenderer.sharedMaterials;
            }
        }

        #endregion
    }
}