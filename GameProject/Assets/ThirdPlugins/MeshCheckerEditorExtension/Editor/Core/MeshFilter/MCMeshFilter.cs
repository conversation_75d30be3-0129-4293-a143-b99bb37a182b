using System;
using JetBrains.Annotations;
using UnityEngine;

// ReSharper disable StyleCop.SA1300
namespace HightlanderSolutions.MeshCheckerEditorExtension
{
    /// <summary>
    ///     Represents a <see cref="MeshFilter"/> wrapper for abstract usage of the <see cref="AbstractMeshFilter"/>.
    /// </summary>
    [PublicAPI]
    public sealed class MCMeshFilter : AbstractMeshFilter
    {
        /// <summary>
        ///     Initializes a new instance of the <see cref="MCMeshFilter"/> class.
        /// </summary>
        /// <param name="meshFilter">The target mesh filter.</param>
        /// <exception cref="ArgumentNullException">The meshFilter argument is null.</exception>
        [PublicAPI]
        public MCMeshFilter([NotNull] MeshFilter meshFilter)
        {
            if (meshFilter == null)
            {
                throw new ArgumentNullException("meshFilter");
            }

            MeshFilter = meshFilter;
        }

        /// <summary>
        ///     Gets a target <see cref="MeshFilter"/>.
        /// </summary>
        [PublicAPI]
        public MeshFilter MeshFilter { get; private set; }

        #region Overrides of AbstractMeshFilter


        /// <inheritdoc />
        [PublicAPI]
        public override Mesh mesh
        {
            get
            {
                return MeshFilter.mesh;
            }

            set
            {
                MeshFilter.mesh = value;
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override Mesh sharedMesh
        {
            get
            {
                return MeshFilter.sharedMesh;
            }

            set
            {
                MeshFilter.sharedMesh = value;
            }
        }

        /// <inheritdoc />
        [NotNull]
        [PublicAPI]
        public override GameObject gameObject
        {
            get
            {
                return MeshFilter.gameObject;
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override string tag
        {
            get
            {
                return MeshFilter.tag;
            }

            set
            {
                MeshFilter.tag = value;
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override Transform transform
        {
            get
            {
                return MeshFilter.transform;
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override HideFlags hideFlags
        {
            get
            {
                return MeshFilter.hideFlags;
            }

            set
            {
                MeshFilter.hideFlags = value;
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override string name
        {
            get
            {
                return MeshFilter.name;
            }

            set
            {
                MeshFilter.name = value;
            }
        }

        #endregion
    }
}