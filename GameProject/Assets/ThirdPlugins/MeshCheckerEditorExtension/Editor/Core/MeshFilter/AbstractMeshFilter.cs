using JetBrains.Annotations;

using UnityEngine;

// <PERSON><PERSON><PERSON><PERSON> disable InconsistentNaming
// <PERSON><PERSON><PERSON><PERSON> disable StyleCop.SA1300
namespace HightlanderSolutions.MeshCheckerEditorExtension
{
    /// <summary>
    ///     Represents an abstraction over <see cref="MeshFilter"/> and <see cref="SkinnedMeshRenderer"/>.
    /// </summary>
    [PublicAPI]
    public abstract class AbstractMeshFilter
    {
        /// <summary>
        ///     Gets or sets the instantiated Mesh assigned to the mesh filter.
        /// </summary>
        [PublicAPI]
        public abstract Mesh mesh { get; set; }

        /// <summary>
        ///     Gets or sets the shared mesh of the mesh filter.
        /// </summary>
        [PublicAPI]
        public abstract Mesh sharedMesh { get; set; }

        /// <summary>
        ///     Gets the game object this component is attached to. A component is always attached to a game object.
        /// </summary>
        [PublicAPI]
        public abstract GameObject gameObject { get; }

        /// <summary>
        ///     Gets or sets the tag of this game object.
        /// </summary>
        [PublicAPI]
        public abstract string tag { get; set; }

        /// <summary>
        ///     Gets the Transform attached to this GameObject.
        /// </summary>
        [PublicAPI]
        public abstract Transform transform { get; }

        /// <summary>
        ///     Gets or sets a value indicating that should the object be hidden, saved with the scene or modifiable by the user?
        /// </summary>
        [PublicAPI]
        public abstract HideFlags hideFlags { get; set; }

        /// <summary>
        ///     Gets or sets the name of the object.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public abstract string name { get; set; }
    }
}