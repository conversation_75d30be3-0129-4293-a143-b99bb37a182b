using System;
using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension
{
    /// <summary>
    ///     Represents a helper class which control adding of the scripting define symbol.
    /// </summary>
    internal static class ScriptingDefineSymbolsInstaller
    {
        /// <summary>
        ///     The scripting define symbol which when defined indicates that mesh checker is present in the project.
        /// </summary>
        private const string DefineSymbolValue = "MESH_CHECKER";

        /// <summary>
        ///     The proxy which used to get or set scripting define symbols.
        /// </summary>
        // ReSharper disable once MemberCanBePrivate.Global
        // ReSharper disable once FieldCanBeMadeReadOnly.Global
        // Mocked in tests.
        internal static IScriptingDefineSymbolsProxy ScriptingDefineSymbolsProxy = new ScriptingDefineSymbolsProxy();

        /// <summary>
        ///     The array of build target groups which should be used to add the scripting define symbol.
        /// </summary>
        private static readonly BuildTargetGroup[] BuildTargetGroups =
            {
#if !UNITY_5_4_OR_NEWER
                BuildTargetGroup.WebPlayer,
                BuildTargetGroup.WP8,
                BuildTargetGroup.BlackBerry,
                BuildTargetGroup.PSM,
#endif
#if !UNITY_5_6_OR_NEWER
                BuildTargetGroup.PS3,
                BuildTargetGroup.XBOX360,
#endif
#if !UNITY_2018_1_OR_NEWER
                BuildTargetGroup.Tizen,
                BuildTargetGroup.SamsungTV,
#endif
#if !UNITY_2018_1_OR_NEWER && UNITY_5_6_OR_NEWER
                BuildTargetGroup.WiiU,
                BuildTargetGroup.N3DS,
#endif
#if !UNITY_2019_4_OR_NEWER && UNITY_5_6_OR_NEWER
                BuildTargetGroup.Facebook,
#endif
#if !UNITY_2018_3_OR_NEWER
                BuildTargetGroup.PSP2,
#endif
#if UNITY_5_6_OR_NEWER
                BuildTargetGroup.Switch,
                BuildTargetGroup.tvOS,
#endif
                BuildTargetGroup.Standalone,
                BuildTargetGroup.iOS,
                BuildTargetGroup.Android,
                BuildTargetGroup.WebGL, 
                BuildTargetGroup.WSA,
                BuildTargetGroup.PS4,
                BuildTargetGroup.XboxOne
            };

        /// <summary>
        ///     Deletes a 'MESH_CHECKER' from scripting define symbols of all platforms.
        /// </summary>
        /// <param name="recompile">The value indicating whether unity should recompile the project if define symbols were deleted.</param>
        internal static void DeleteDefinedSymbol(bool recompile = true)
        {
            bool RefreshRequired = false;

            foreach (BuildTargetGroup BuildTargetGroup in BuildTargetGroups)
            {
                bool Deleted = TryDeleteDefinedSymbolForBuildTarget(BuildTargetGroup);

#if !UNITY_4_6 && !UNITY_4_7 && !UNITY_5_0 // Analog for UNITY_5_1_OR_NEWER
                // We need to refresh only when changed defines of the current active build target.
                bool CurrentlySelectedPlatform = (BuildPipeline.GetBuildTargetGroup(EditorUserBuildSettings.activeBuildTarget) == BuildTargetGroup);
#else
                bool CurrentlySelectedPlatform = true;
                // ReSharper disable once ConditionIsAlwaysTrueOrFalse
#endif
                RefreshRequired = (CurrentlySelectedPlatform && Deleted) || RefreshRequired;
            }

            if (recompile && RefreshRequired)
            {
                AssetDatabase.Refresh();
            }
        }

        /// <summary>
        ///     Raised when Unity Editor is loaded.
        /// </summary>
        [InitializeOnLoadMethod]
        [UsedImplicitly]
        private static void OnProjectLoaded()
        {
            MCPreferences.Subscribe(PreferenceType.DisableDefines, OnDisableDefinesSettingChanged);

            TryDefineSymbol();
        }

        /// <summary>
        ///     Tries to define the symbol.
        /// </summary>
        private static void TryDefineSymbol()
        {
            if (MCPreferences.DisableDefinesSetting)
            {
                return;
            }

            try
            {
                DefineSymbol();
            }
            catch (Exception Ex)
            {
                Debug.LogError("[MeshChecker] Something went wrong when trying to define 'MESH_CHECKER' symbol in project. " +
                               "This feature was disabled, you can turn it on again in Preferences... -> Mesh Checker \n" + Ex);
                MCPreferences.DisableDefinesSetting = true;
            }
        }

        /// <summary>
        ///     Defines the symbol to scripting define symbols of all platforms.
        /// </summary>
        private static void DefineSymbol()
        {
#if !UNITY_4_6 && !UNITY_4_7 && !UNITY_5_0 // Analog for UNITY_5_1_OR_NEWER
            BuildTargetGroup CurrentBuildTarget = BuildPipeline.GetBuildTargetGroup(EditorUserBuildSettings.activeBuildTarget);
            bool RefreshRequired = TryDefineSymbolForBuildTarget(CurrentBuildTarget);
#else
            bool RefreshRequired = false;

            foreach (BuildTargetGroup BuildTargetGroup in BuildTargetGroups)
            {
                bool Deleted = TryDeleteDefinedSymbolForBuildTarget(BuildTargetGroup);
                RefreshRequired = Deleted || RefreshRequired;
            }
#endif

            if (RefreshRequired)
            {
                AssetDatabase.Refresh();
            }
        }

        /// <summary>
        ///     Checks that the define symbol added to list for specified build target.
        /// </summary>
        /// <param name="buildTargetGroup">The target platform.</param>
        /// <returns><c>true</c> - if define symbol exists for build target; otherwise - <c>false</c>.</returns>
        private static bool CheckDefineSymbolForBuildTarget(BuildTargetGroup buildTargetGroup)
        {
            using (var DefinedSymbols = new DefinedSymbols(ScriptingDefineSymbolsProxy.GetSymbols(buildTargetGroup)))
            {
                return DefinedSymbols.Contains(DefineSymbolValue);
            }
        }

        /// <summary>
        ///     Tries to define the symbol to the specified platform.
        /// </summary>
        /// <param name="buildTargetGroup">The target platform.</param>
        /// <returns><c>true</c> - if the symbol was added; otherwise - <c>false</c>.</returns>
        private static bool TryDefineSymbolForBuildTarget(BuildTargetGroup buildTargetGroup)
        {
            using (var DefinedSymbols = new DefinedSymbols(ScriptingDefineSymbolsProxy.GetSymbols(buildTargetGroup)))
            {
                if (DefinedSymbols.Contains(DefineSymbolValue))
                {
                    return false;
                }

                if (!DefinedSymbols.TryAdd(DefineSymbolValue))
                {
                    return false;
                }

                ScriptingDefineSymbolsProxy.SetSymbols(buildTargetGroup, DefinedSymbols.ToString());
            }

            return true;
        }

        /// <summary>
        ///     Tries to delete defined symbol from the specified platform.
        /// </summary>
        /// <param name="buildTargetGroup">The target platform.</param>
        /// <returns><c>true</c> - if the symbol was deleted; otherwise - <c>false</c>.</returns>
        private static bool TryDeleteDefinedSymbolForBuildTarget(BuildTargetGroup buildTargetGroup)
        {
            using (var DefinedSymbols = new DefinedSymbols(ScriptingDefineSymbolsProxy.GetSymbols(buildTargetGroup)))
            {
                if (!DefinedSymbols.Contains(DefineSymbolValue))
                {
                    return false;
                }

                if (!DefinedSymbols.TryRemove(DefineSymbolValue))
                {
                    return false;
                }

                ScriptingDefineSymbolsProxy.SetSymbols(buildTargetGroup, DefinedSymbols.ToString());
            }

            return true;
        }

        /// <summary>
        ///     Raised when <see cref="MCPreferences.DisableDefinesSetting"/> was changed.
        /// </summary>
        private static void OnDisableDefinesSettingChanged()
        {
            if (!MCPreferences.DisableDefinesSetting)
            {
                TryDefineSymbol();
            }
        }
    }
}