using System;
using System.Collections.Generic;
using System.Linq;
using JetBrains.Annotations;

namespace HightlanderSolutions.MeshCheckerEditorExtension
{
    /// <summary>
    ///     Represents an utility class which helps to parse, add and remove globally defined symbols.
    /// </summary>
    internal sealed class DefinedSymbols : IDisposable
    {
        /// <summary>
        ///     The collection of globally defined symbols.
        /// </summary>
        private List<string> DefinesArray;

        /// <summary>
        ///     Initializes a new instance of the <see cref="DefinedSymbols"/> class.
        /// </summary>
        /// <param name="defines">Joined globally defined symbols.</param>
        [PublicAPI]
        public DefinedSymbols([NotNull] string defines)
        {
            if (defines == null)
            {
                throw new ArgumentNullException("defines");
            }

            DefinesArray = defines.Split(new[] { ";" }, StringSplitOptions.RemoveEmptyEntries).ToList();
        }

        /// <summary>
        ///     Checks that specified define symbol is registered.
        /// </summary>
        /// <param name="defineSymbol">The symbol to check.</param>
        /// <returns><c>true</c> - if symbol registered; otherwise - <c>false</c>.</returns>
        [PublicAPI]
        public bool Contains([NotNull] string defineSymbol)
        {
            if (defineSymbol == null)
            {
                throw new ArgumentNullException("defineSymbol");
            }

            return DefinesArray.Contains(defineSymbol, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        ///     Tries to remove a globally defined symbol.
        /// </summary>
        /// <param name="defineSymbol">The symbol to remove.</param>
        /// <returns><c>true</c> - if the symbol was removed; otherwise - <c>false</c>.</returns>
        [PublicAPI]
        public bool TryRemove([NotNull] string defineSymbol)
        {
            if (defineSymbol == null)
            {
                throw new ArgumentNullException("defineSymbol");
            }

            if (Contains(defineSymbol))
            {
                DefinesArray.Remove(defineSymbol);

                return true;
            }

            return false;
        }

        /// <summary>
        ///     Tries to add a globally defined symbol.
        /// </summary>
        /// <param name="defineSymbol">The symbol to add.</param>
        /// <returns><c>true</c> - if the symbol was added; otherwise - <c>false</c>.</returns>
        [PublicAPI]
        public bool TryAdd([NotNull] string defineSymbol)
        {
            if (defineSymbol == null)
            {
                throw new ArgumentNullException("defineSymbol");
            }

            if (!Contains(defineSymbol))
            {
                DefinesArray.Add(defineSymbol);

                return true;
            }

            return false;
        }

        #region Overrides of Object

        /// <inheritdoc />
        [PublicAPI]
        public override string ToString()
        {
            return string.Join(";", DefinesArray.ToArray());
        }

        #endregion

        #region IDisposable

        /// <inheritdoc />
        [PublicAPI]
        public void Dispose()
        {
            DefinesArray = null;
        }

        #endregion
    }
}