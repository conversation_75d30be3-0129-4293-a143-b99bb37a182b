using JetBrains.Annotations;

using UnityEngine;

// <PERSON><PERSON><PERSON><PERSON> disable InconsistentNaming
// Re<PERSON><PERSON><PERSON> disable StyleCop.SA1300
namespace HightlanderSolutions.MeshCheckerEditorExtension
{
    /// <summary>
    ///     The MeshRenderer and SkinnedMeshRenderer interface.
    /// </summary>
    [PublicAPI]
    public interface IMeshRenderer
    {

        /// <summary>
        ///     Gets the game object this component is attached to. A component is always attached to a game object.
        /// </summary>
        [PublicAPI]
        GameObject gameObject { get; }

        /// <summary>
        ///     Gets or sets the tag of this game object.
        /// </summary>
        [PublicAPI]
        string tag { get; set; }

        /// <summary>
        ///     Gets the Transform attached to this GameObject.
        /// </summary>
        [PublicAPI]
        Transform transform { get; }

        /// <summary>
        ///     Gets or sets a value indicating that should the object be hidden, saved with the scene or modifiable by the user?
        /// </summary>
        [PublicAPI]
        HideFlags hideFlags { get; set; }

        /// <summary>
        ///     Gets or sets the name of the object.
        /// </summary>
        [PublicAPI]
        [NotNull]
        string name { get; set; }

        /// <summary>
        ///     Gets all the shared materials of this object.
        /// </summary>
        [PublicAPI]
        Material[] sharedMaterials { get; }


    }
}