using System;
using System.Collections.Generic;
using System.Linq;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;

using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.PolygonsCount
{
    /// <summary>
    ///     Represents the manager that manage measures of polygons and vertices.
    /// </summary>
    internal sealed class PolygonsMeasurer
    {
        /// <summary>
        ///     Sorting type enumerator, used in measures sorting.
        /// </summary>
        public enum SortingType
        {
            /// <summary>
            ///     Do not sort.
            /// </summary>
            None,

            /// <summary>
            ///     Sort by object name.
            /// </summary>
            Name,

            /// <summary>
            ///     Sort by polygons count.
            /// </summary>
            Polygons,

            /// <summary>
            ///     Sort by vertices count.
            /// </summary>
            Vertices
        }

        /// <summary>
        ///     The list of available measures.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly List<PolygonsMeasure> PolygonsMeasures = new List<PolygonsMeasure>();

        /// <summary>
        ///     The total sum of all measures.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly PolygonsMeasure TotalMeasure = new PolygonsMeasure();

        /// <summary>
        ///     The width of «Polygons» and «Vertices» columns.
        /// </summary>
#if UNITY_2019_1_OR_NEWER
        private const int InfoWidth = 75;
#else
        private const int InfoWidth = 70;
#endif

        /// <summary>
        ///     The width of left space.
        /// </summary>
        private const float LeftSpaceWidth = 5;

        /// <summary>
        ///     The state of a scroll view.
        /// </summary>
        [NotNull]
        private readonly ScrollViewState ScrollViewState;

        /// <summary>
        ///     The current sorting type
        /// </summary>
        private SortingType Sorting;

        /// <summary>
        ///     The value indicating whether the sorting is ascending.
        /// </summary>
        private bool SortingAscending;

        /// <summary>
        ///     Initializes a new instance of the <see cref="PolygonsMeasurer"/> class.
        /// </summary>
        /// <param name="repaintCallback">The repaint container callback.</param>
        [PublicAPI]
        public PolygonsMeasurer([CanBeNull]Action repaintCallback = null)
        {
            ScrollViewState = new ScrollViewState(repaintCallback);
        }

        /// <summary>
        ///     Measures a polygons and vertices count of objects and theirs <see cref="MeshFilter"/> from specified dictionary.
        /// </summary>
        /// <param name="objectsDictionary">
        ///     Dictionary of GameObjects and theirs corresponding mesh filters.
        ///     <para>
        ///       Often this dictionary is result of selection processing. (<see cref="SelectionManager{T,TC}"/>.<see cref="SelectionManager{T,TC}.SelectedComponentsDictionary"/>)
        ///     </para>
        /// </param>
        /// <exception cref="ArgumentNullException"><paramref name="objectsDictionary"/> is <see langword="null"/></exception>
        [PublicAPI]
        public void AddObjectsToMeasure([NotNull]Dictionary<GameObject, AbstractMeshFilter> objectsDictionary)
        {
            if (objectsDictionary == null)
            {
                throw new ArgumentNullException("objectsDictionary");
            }

            foreach (KeyValuePair<GameObject, AbstractMeshFilter> Pair in objectsDictionary)
            {
                PolygonsMeasure NewMeasure = new PolygonsMeasure(Pair.Key, Pair.Value);
                PolygonsMeasures.Add(NewMeasure);
                TotalMeasure.Add(NewMeasure.VertexCount, NewMeasure.PolygonsCount);
            }

            SortPolygonsMeasures();
        }

        /// <summary>
        ///     Removes measures, that corresponds to game objects in <paramref name="keysList"/>.
        /// </summary>
        /// <param name="keysList">The collection of GameObjects, which measure must be removed.</param>
        [PublicAPI]
        public void RemoveObjectsFromMeasures([NotNull]IEnumerable<GameObject> keysList)
        {
            foreach (GameObject KeyItem in keysList)
            {
                PolygonsMeasure FindedMeasure = PolygonsMeasures.Find(measure => measure != null
                                                                                 && measure.Target == KeyItem);
                if (FindedMeasure != null)
                {
                    PolygonsMeasures.Remove(FindedMeasure);
                    TotalMeasure.Subtract(FindedMeasure.VertexCount, FindedMeasure.PolygonsCount);
                }
            }

            SortPolygonsMeasures();
        }

        /// <summary>
        ///     Removes all measures.
        /// </summary>
        [PublicAPI]
        public void Clear()
        {
            PolygonsMeasures.Clear();
        }

        /// <summary>
        ///     Draws the measurer at the specified position.
        /// </summary>
        /// <param name="position">The position, where a measurer must be drawn.</param>
        [PublicAPI]
        public void Draw(Rect position)
        {
            if (PolygonsMeasures.Count == 0)
            {
                Rect InfoPosition = new RectOffset(10, 10, 5, 1).Remove(position);
                string Caption;

                if (Selection.gameObjects.Length > 0)
                {
                    Caption = "No objects with a MeshFilter or a SkinnedMeshRenderer component selected.\n\n"
                        + "<size=14>(Component can be attached to any child)</size>";
                }
                else
                {
                    Caption = "To start using Polygons count tool simply select some objects on scene or prefabs in the project window!";
                }

                using (new MCGUI.ColorScope(0.65f))
                {
                    GUI.Label(InfoPosition, Caption, MCGUI.Styles.ToolEmptyLabel);
                }

                return;
            }

            float Width = position.width;
            float Height = position.height;
#if !UNITY_4_6 && !UNITY_4_7 // Analog for UNITY_5_0_OR_NEWER
            GUI.BeginClip(position);
#else
            GUI.BeginGroup(position);
#endif
            {
                Rect ToolbarRect = new Rect(0, 0, Width, MCGUI.ToolbarHeight);
                
                float ScrollViewHeight = Height - MCGUI.ToolbarHeight;
                float InnerRectHeight = (PolygonsMeasures.Count * MCGUI.LineHeight) + 1;
                bool VerticalScrollbarExists = InnerRectHeight > ScrollViewHeight;

                Rect LeftSpaceRect = new Rect(0, ToolbarRect.yMin, LeftSpaceWidth, MCGUI.ToolbarHeight);
                
#if UNITY_2019_1_OR_NEWER
                float ObjectColumnWidth = ToolbarRect.width + 1 - LeftSpaceWidth - (InfoWidth * 2) - (VerticalScrollbarExists ? MCGUI.ScrollbarSize - 1 : 4);
#else
                float ObjectColumnWidth = ToolbarRect.width - LeftSpaceWidth - (InfoWidth * 2) - (VerticalScrollbarExists ? MCGUI.ScrollbarSize - 1 : 4);
#endif
                Rect ObjectsButtonRect = new Rect(LeftSpaceWidth, ToolbarRect.yMin, ObjectColumnWidth, MCGUI.ToolbarHeight);
                Rect PolygonsRect = new Rect(ObjectsButtonRect.xMax, ToolbarRect.yMin, InfoWidth, MCGUI.ToolbarHeight);
                Rect VerticesRect = new Rect(PolygonsRect.xMax, ToolbarRect.yMin, InfoWidth, MCGUI.ToolbarHeight);
                float RightSpaceWidth = ToolbarRect.width - VerticesRect.xMax;
                Rect RightSpaceRect = new Rect(VerticesRect.xMax, ToolbarRect.yMin, RightSpaceWidth, MCGUI.ToolbarHeight);

                DrawTitle(LeftSpaceRect, ObjectsButtonRect, PolygonsRect, VerticesRect, RightSpaceRect);

                // Draw total polygons count
                LeftSpaceRect.y += MCGUI.ToolbarHeight;
                ObjectsButtonRect.y += MCGUI.ToolbarHeight;
                PolygonsRect.y += MCGUI.ToolbarHeight;
                VerticesRect.y += MCGUI.ToolbarHeight;
                RightSpaceRect.y += MCGUI.ToolbarHeight;

                if (PolygonsMeasures.Count > 0)
                {
                
#if UNITY_2019_1_OR_NEWER
                    DrawTotalMeasure(MCGUI.ToolbarHeight, LeftSpaceWidth, ObjectColumnWidth - 1, InfoWidth, RightSpaceWidth);
#else
                    DrawTotalMeasure(MCGUI.ToolbarHeight, LeftSpaceWidth, ObjectColumnWidth, InfoWidth, RightSpaceWidth);
#endif
                }

                // Draw scroll view data
                Rect ScrollViewRect = new Rect(0, ToolbarRect.yMax + MCGUI.ToolbarHeight, Width, ScrollViewHeight);
                Rect ScrollbarRect = new Rect(Width - MCGUI.ScrollbarSize, ToolbarRect.yMax, MCGUI.ScrollbarSize, Height - MCGUI.ToolbarHeight);
                Rect ScrollViewInnerRect = new Rect(0, 0, VerticalScrollbarExists ? Width - MCGUI.ScrollbarSize : Width, InnerRectHeight);

                // Calculate first and last drawed elements indices
                int FirstIndex = Mathf.FloorToInt(ScrollViewState.ScrollPosition.y / MCGUI.LineHeight);
                int LastIndex = Mathf.CeilToInt((ScrollViewState.ScrollPosition.y + ScrollViewHeight) / MCGUI.LineHeight);

                LastIndex = Mathf.Clamp(LastIndex, 0, PolygonsMeasures.Count - 1);

#if UNITY_2019_1_OR_NEWER
                GUI.Box(ScrollViewRect, GUIContent.none, "OL box flat");
#endif
                MCGUI.BeginScrollView(ScrollViewRect, ScrollViewState, ScrollViewInnerRect, VerticalScrollbarExists ? ScrollbarRect : (Rect?)null);
                {
                    for (int Index = FirstIndex; Index <= LastIndex; Index++)
                    {
                        PolygonsMeasure Measure = PolygonsMeasures[Index];
                        if (Measure == null)
                        {
                            continue;
                        }

                        DrawMeasureItem(Index, Width, VerticalScrollbarExists);
                    }
                }

                Rect SeparatorRect = new Rect(0, PolygonsMeasures.Count * MCGUI.LineHeight, ScrollViewInnerRect.width, 1);
                MCGUI.DrawSeparatorLine(SeparatorRect);

                MCGUI.EndScrollView();
            }
#if !UNITY_4_6 && !UNITY_4_7 // Analog for UNITY_5_0_OR_NEWER
            GUI.EndClip();
#else
            GUI.EndGroup();
#endif
        }

        /// <summary>
        ///     Draws the total measure.
        /// </summary>
        /// <param name="topOffset">The top offset.</param>
        /// <param name="leftOffset">The left offset.</param>
        /// <param name="titleWidth">The title column width.</param>
        /// <param name="countsWidth">The count columns width.</param>
        /// <param name="lastSpaceWidth">The last space width</param>
        private void DrawTotalMeasure(
            float topOffset,
            float leftOffset,
            float titleWidth,
            float countsWidth,
            float lastSpaceWidth)
        {
            float TotalWidth = leftOffset + titleWidth + (countsWidth * 2) + lastSpaceWidth;

            Rect SpaceRect = new Rect(0, topOffset, leftOffset, MCGUI.ToolbarHeight);
            Rect ObjectsButtonRect = new Rect(SpaceRect.xMax, topOffset, titleWidth, MCGUI.ToolbarHeight);
            Rect PolygonsRect = new Rect(ObjectsButtonRect.xMax + 1, topOffset, countsWidth - 1, MCGUI.ToolbarHeight);
            Rect VerticesRect = new Rect(PolygonsRect.xMax + 1, topOffset, countsWidth + lastSpaceWidth - 1, MCGUI.ToolbarHeight);

            EditorGUI.LabelField(SpaceRect, string.Empty, MCGUI.Styles.ListLabelEven);
            EditorGUI.LabelField(ObjectsButtonRect, MCUtilities.WrapBold("Total"), MCGUI.Styles.ListLabelEven);

            EditorGUI.LabelField(
                PolygonsRect,
                new GUIContent(MCUtilities.WrapBold(TotalMeasure.PolygonsCountText), TotalMeasure.PolygonsCount.ToString()),
                MCGUI.Styles.ListLabelEven);

            EditorGUI.LabelField(
                VerticesRect,
                new GUIContent(MCUtilities.WrapBold(TotalMeasure.VertexCountText), TotalMeasure.VertexCount.ToString()),
                MCGUI.Styles.ListLabelEven);

            Rect SeparatorRect = new Rect(0, SpaceRect.yMax - 1, TotalWidth, 1);
            MCGUI.DrawSeparatorLine(SeparatorRect);
        }

        /// <summary>
        ///     Gets a column title with special symbols based on the current sorting.
        /// </summary>
        /// <param name="columnTitle">The title of a column.</param>
        /// <param name="sortingType">The sorting type, corresponding to the column.</param>
        /// <returns>
        ///     <para>
        ///         Returns total column title with special symbols «▼» or «▲»;
        ///     </para>
        ///     <para>
        ///         Example: «Polygons ▲» when columnTitle are «Polygons», current sorting type equals to sortingType argument and sorting is ascending.
        ///     </para>
        /// </returns>
        [PublicAPI]
        [NotNull]
        public string GetColumnTitle([CanBeNull]string columnTitle, SortingType sortingType)
        {
            return string.Concat(columnTitle, Sorting != sortingType ? string.Empty : SortingAscending ? " ▲" : " ▼");
        }

        #region Sorting

        /// <summary>
        ///     Sets sorting type or toggle ascending/descending flag.
        /// </summary>
        /// <param name="type">The sorting type.</param>
        [PublicAPI]
        public void SetSorting(SortingType type)
        {
            if (Sorting == type)
            {
                // If the sorting type not changed - then just switch the order flag.
                SortingAscending = !SortingAscending;
            }
            else
            {
                // If a sorting type are new - then change the type and set the order flag to descending state.
                Sorting = type;
                SortingAscending = false;
            }

            SortPolygonsMeasures();
        }

        /// <summary>
        ///     Sorts polygons measures list and orders it ascending or descending, based on SortingAscending flag.
        /// </summary>
        private void SortPolygonsMeasures()
        {
            List<PolygonsMeasure> OrderedList;
            switch (Sorting)
            {
                case SortingType.None:
                    return;

                case SortingType.Name:
                    OrderedList = SortingAscending
                        ? PolygonsMeasures.OrderByDescending(e => e.TargetName).ToList()
                        : PolygonsMeasures.OrderBy(e => e.TargetName).ToList();
                    break;

                case SortingType.Polygons:
                    OrderedList = SortingAscending
                        ? PolygonsMeasures.OrderBy(e => e.PolygonsCount).ToList()
                        : PolygonsMeasures.OrderByDescending(e => e.PolygonsCount).ToList();
                    break;

                case SortingType.Vertices:
                    OrderedList = SortingAscending
                        ? PolygonsMeasures.OrderBy(e => e.VertexCount).ToList()
                        : PolygonsMeasures.OrderByDescending(e => e.VertexCount).ToList();
                    break;

                default:
                    return;
            }

            PolygonsMeasures.Clear();
            PolygonsMeasures.AddRange(OrderedList);
        }

        #endregion

        /// <summary>
        ///     Draws a measure item.
        /// </summary>
        /// <param name="index">The index of an item.</param>
        /// <param name="width">The width of a line.</param>
        /// <param name="verticalScrollbarExists">Specifies whether a scrollbar exists.</param>
        /// <exception cref="ArgumentOutOfRangeException">
        ///     <paramref name="index" /> is less than 0.-or-<paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.Generic.List`1.Count" />. 
        /// </exception>
        private void DrawMeasureItem(int index, float width, bool verticalScrollbarExists)
        {
            PolygonsMeasure Measure = PolygonsMeasures[index];
            if (Measure == null)
            {
                return;
            }

            int ItemYMin = index * MCGUI.LineHeight;
            Rect ItemSpaceRect = new Rect(0, ItemYMin, LeftSpaceWidth, MCGUI.LineHeight);
            float ItemTitleWidth = width - LeftSpaceWidth - (InfoWidth * 2)
                                   - (verticalScrollbarExists ? MCGUI.ScrollbarSize - 1 : 4);
            Rect ItemTitleRect = new Rect(ItemSpaceRect.xMax, ItemYMin, ItemTitleWidth, MCGUI.LineHeight);
            Rect ItemPolygonsRect = new Rect(ItemTitleRect.xMax + 1, ItemYMin, InfoWidth - 1, MCGUI.LineHeight);
            Rect ItemVerticesRect = new Rect(ItemPolygonsRect.xMax + 1, ItemYMin, width - ItemPolygonsRect.xMax - 1, MCGUI.LineHeight);

            bool Odd = index % 2 != 0;
            GUIStyle LineStyle = Odd ? MCGUI.Styles.ListLabelOdd : MCGUI.Styles.ListLabelEven;

            EditorGUI.LabelField(ItemSpaceRect, string.Empty, LineStyle);

            if (GUI.Button(ItemTitleRect, new GUIContent(Measure.TargetName, Measure.TargetName), LineStyle))
            {
                EditorGUIUtility.PingObject(Measure.Target);
            }

            EditorGUI.LabelField(
                ItemPolygonsRect,
                new GUIContent(Measure.PolygonsCountText, Measure.PolygonsCount.ToString()),
                LineStyle);
            EditorGUI.LabelField(
                ItemVerticesRect,
                new GUIContent(Measure.VertexCountText, Measure.VertexCount.ToString()),
                LineStyle);
        }

        /// <summary>
        ///     Draws the title.
        /// </summary>
        /// <param name="spaceRect">The rect of left space.</param>
        /// <param name="objectsButtonRect">The objects button rect.</param>
        /// <param name="polygonsRect">The polygons button rect.</param>
        /// <param name="verticesRect">The vertices button rect.</param>
        /// <param name="lastSpaceRect">The rect of right space.</param>
        private void DrawTitle(Rect spaceRect, Rect objectsButtonRect, Rect polygonsRect, Rect verticesRect, Rect lastSpaceRect)
        {
#if UNITY_2019_1_OR_NEWER
            GUI.Box(new Rect(spaceRect.x, spaceRect.y, lastSpaceRect.xMax - spaceRect.xMin, spaceRect.yMax - spaceRect.yMin + 1), GUIContent.none, EditorStyles.toolbar);
#endif
            EditorGUI.LabelField(spaceRect, string.Empty, EditorStyles.toolbar);

            if (MCGUI.ToolbarButton(objectsButtonRect, GetColumnTitle("Objects", SortingType.Name)))
            {
                SetSorting(SortingType.Name);
            }

            if (MCGUI.ToolbarButton(polygonsRect, GetColumnTitle("Polygons", SortingType.Polygons)))
            {
                SetSorting(SortingType.Polygons);
            }

            if (MCGUI.ToolbarButton(verticesRect, GetColumnTitle("Vertices", SortingType.Vertices)))
            {
                SetSorting(SortingType.Vertices);
            }

            EditorGUI.LabelField(lastSpaceRect, string.Empty, EditorStyles.toolbar);
        }
    }
}
