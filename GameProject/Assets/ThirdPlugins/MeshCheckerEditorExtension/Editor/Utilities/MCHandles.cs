using JetBrains.Annotations;

using UnityEditor;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Utilities
{
    /// <summary>
    ///     Represents a helper class which used to draw handles in MeshAnalysis window.
    /// </summary>
    public static class MCHandles
    {
        /// <summary>
        ///     The value indicating whether handle meshes already initialized.
        /// </summary>
        private static bool MeshesInitialized;

        /// <summary>
        ///     The backing field for the <see cref="CubeMesh"/> property.
        /// </summary>
        private static Mesh InnerCubeMesh;

        /// <summary>
        ///     The backing field for the <see cref="SphereMesh"/> property.
        /// </summary>
        private static Mesh InnerSphereMesh;

        /// <summary>
        ///     The backing field for the <see cref="ConeMesh"/> property.
        /// </summary>
        private static Mesh InnerConeMesh;

        /// <summary>
        ///     The backing field for the <see cref="CylinderMesh"/> property.
        /// </summary>
        private static Mesh InnerCylinderMesh;

        /// <summary>
        ///     The backing field for the <see cref="QuadMesh"/> property.
        /// </summary>
        private static Mesh InnerQuadMesh;

        /// <summary>
        ///     Gets a cube mesh for handles.
        /// </summary>
        [PublicAPI]
        public static Mesh CubeMesh
        {
            get
            {
                InitializeMeshes();

                return InnerCubeMesh;
            }
        }

        /// <summary>
        ///     Gets a sphere mesh for handles.
        /// </summary>
        [PublicAPI]
        public static Mesh SphereMesh
        {
            get
            {
                InitializeMeshes();

                return InnerSphereMesh;
            }
        }

        /// <summary>
        ///     Gets a cone mesh for handles.
        /// </summary>
        [PublicAPI]
        public static Mesh ConeMesh
        {
            get
            {
                InitializeMeshes();

                return InnerConeMesh;
            }
        }

        /// <summary>
        ///     Gets a cylinder mesh for handles.
        /// </summary>
        [PublicAPI]
        public static Mesh CylinderMesh
        {
            get
            {
                InitializeMeshes();

                return InnerCylinderMesh;
            }
        }

        /// <summary>
        ///     Gets a quad mesh for handles.
        /// </summary>
        [PublicAPI]
        public static Mesh QuadMesh
        {
            get
            {
                InitializeMeshes();

                return InnerQuadMesh;
            }
        }

        /// <summary>
        ///     Gets the size of a point which should use handles or other instruments to visualize the point.
        /// </summary>
        /// <param name="position">The position of the point.</param>
        /// <param name="camera">The camera which used to draw specified point.</param>
        /// <returns>The size or scale of the handle.</returns>
        [PublicAPI]
        public static float GetHandleSize(Vector3 position, Camera camera)
        {
            const float HandleSize = 5.0f;

            if (camera)
            {
                Transform Transform = camera.transform;
                Vector3 CameraPosition = Transform.position;
                float Distance = Vector3.Dot(position - CameraPosition, Transform.TransformDirection(new Vector3(0, 0, 1)));
                Vector3 ScreenPoint = camera.WorldToScreenPoint(CameraPosition + Transform.TransformDirection(new Vector3(0, 0, Distance)));
                Vector3 ScreenPoint2 = camera.WorldToScreenPoint(CameraPosition + Transform.TransformDirection(new Vector3(1, 0, Distance)));
                float ScreenDistance = (ScreenPoint - ScreenPoint2).magnitude;
                return HandleSize / Mathf.Max(ScreenDistance, 0.0001f);
            }
            else
            {
                return 20.0f;
            }
        }

        /// <summary>
        ///     Loads internal Unity meshes for handles.
        /// </summary>
        private static void InitializeMeshes()
        {
            if (MeshesInitialized)
            {
                return;
            }

            GameObject HandlesGameObject = (GameObject)EditorGUIUtility.Load("SceneView/HandlesGO.fbx");
            if (!HandlesGameObject)
            {
                Debug.Log("Couldn't find SceneView/HandlesGO.fbx");
            }
            HandlesGameObject.SetActive(false);

            const string AssertMessage = "mesh is null. A problem has occurred with `SceneView/HandlesGO.fbx`";

            foreach (Transform Transform in HandlesGameObject.transform)
            {
                var MeshFilter = Transform.GetComponent<MeshFilter>();
                switch (Transform.name)
                {
                    case "Cube":
                        InnerCubeMesh = MeshFilter.sharedMesh;
                        if (InnerCubeMesh == null)
                        {
                            Debug.LogWarning(AssertMessage);
                        }
                        break;

                    case "Sphere":
                        InnerSphereMesh = MeshFilter.sharedMesh;
                        if (InnerSphereMesh == null)
                        {
                            Debug.LogWarning(AssertMessage);
                        }
                        break;

                    case "Cone":
                        InnerConeMesh = MeshFilter.sharedMesh;
                        if (InnerConeMesh == null)
                        {
                            Debug.LogWarning(AssertMessage);
                        }
                        break;

                    case "Cylinder":
                        InnerCylinderMesh = MeshFilter.sharedMesh;
                        if (InnerCylinderMesh == null)
                        {
                            Debug.LogWarning(AssertMessage);
                        }
                        break;

                    case "Quad":
                        InnerQuadMesh = MeshFilter.sharedMesh;
                        if (InnerQuadMesh == null)
                        {
                            Debug.LogWarning(AssertMessage);
                        }
                        break;
                }
            }

            MeshesInitialized = true;
        }
    }
}