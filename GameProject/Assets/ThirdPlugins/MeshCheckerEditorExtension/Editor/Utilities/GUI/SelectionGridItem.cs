using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    /// <summary>
    ///     Represents an item in selection grid.
    ///     <para>
    ///         Used in <see cref="MCGUI.DrawSelectionGrid{T}"/>
    ///     </para>
    /// </summary>
    /// <typeparam name="T">Type of referenced item.</typeparam>
    [PublicAPI]
    public struct SelectionGridItem<T>
    {
        /// <summary>
        ///     Object, that linked with SelectionGridItem
        /// </summary>
        [PublicAPI]
        public readonly T ReferencedItem;

        /// <summary>
        ///     Text, image and tooltip for selection grid item.
        /// </summary>
        internal readonly GUIContent Content;

        /// <summary>
        ///     Width of GUI element
        /// </summary>
        internal readonly float Width;

        /// <summary>
        ///     Height of GUI element
        /// </summary>
        internal readonly float Height;

        /// <summary>
        ///     Style of GUI element
        /// </summary>
        internal readonly GUIStyle Style;

        /// <summary>
        ///     The cached width layout option, which used to draw a GUI element in the layout mode with needed width.
        /// </summary>
        internal readonly GUILayoutOption WidthLayoutOption;

        /// <summary>
        ///     The cached height layout option, which used to draw a GUI element in the layout mode with needed height.
        /// </summary>
        internal readonly GUILayoutOption HeightLayoutOption;

        /// <summary>
        ///     Enabled grid items are drawn as enabled GUI element. Disabled - as disabled.
        /// </summary>
        internal readonly bool Enabled;

        /// <summary>
        ///     Specifies whether the <see cref="Color"/> not equals to <see cref="UnityEngine.Color.white"/>.
        /// </summary>
        internal readonly bool HasColor;

        /// <summary>
        ///     Color of GUI element.
        ///     <para>
        ///         Default: <see cref="UnityEngine.Color.white"/>
        ///     </para>
        /// </summary>
        internal Color Color;

        /// <summary>
        ///     Initializes a new instance of the <see cref="SelectionGridItem{T}"/> struct.
        /// </summary>
        /// <param name="content">Text, image and tooltip for selection grid item.</param>
        /// <param name="referencedItem">Object, that linked with SelectionGridItem</param>
        /// <param name="width">Width of GUI element</param>
        /// <param name="height">Height of GUI element</param>
        /// <param name="style">Style of GUI element</param>
        /// <param name="enabled">Enabled grid items are drawn as enabled GUI element. Disabled - as disabled.</param>
        [PublicAPI]
        public SelectionGridItem([NotNull]GUIContent content, T referencedItem, float width, float height, [NotNull]GUIStyle style, bool enabled = true)
            : this(content, referencedItem, width, height, style, Color.white, enabled)
        {
        }

        /// <summary>
        ///     Initializes a new instance of the <see cref="SelectionGridItem{T}"/> struct.
        /// </summary>
        /// <param name="content">Text, image and tooltip for selection grid item.</param>
        /// <param name="referencedItem">Object, that linked with SelectionGridItem</param>
        /// <param name="width">Width of GUI element</param>
        /// <param name="height">Height of GUI element</param>
        /// <param name="style">Style of GUI element</param>
        /// <param name="color">
        ///     Color of GUI element.
        ///     <para>
        ///         Default: <see cref="UnityEngine.Color.white"/>
        ///     </para>
        /// </param>
        /// <param name="enabled">Enabled grid items are drawn as enabled GUI element. Disabled - as disabled.</param>
        [PublicAPI]
        public SelectionGridItem([NotNull]GUIContent content, T referencedItem, float width, float height, [NotNull]GUIStyle style, Color color, bool enabled = true)
        {
            Content = content;
            ReferencedItem = referencedItem;
            Width = width;
            Height = height;
            Style = style;
            Enabled = enabled;
            Color = color;
            HasColor = Color != Color.white;

            WidthLayoutOption = GUILayout.Width(Width);
            HeightLayoutOption = GUILayout.Height(Height);
        }
    }
}
