using JetBrains.Annotations;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    public static partial class MCGUI
    {
        /// <summary>
        ///     The animation delegate.
        /// </summary>
        /// <param name="from">The start value.</param>
        /// <param name="to">The end value.</param>
        /// <param name="t">The animation time. Should be between 0 and 1 (both inclusive).</param>
        [PublicAPI]
        public delegate float AnimationDelegate(float from, float to, float t);

        /// <summary>
        ///     Recommended by Google duration of entering animation.
        /// </summary>
        [PublicAPI]
        public const float EnteringDuration = 0.225f;

        /// <summary>
        ///     Recommended by Google duration of exiting animation.
        /// </summary>
        [PublicAPI]
        public const float ExitingDuration = 0.195f;

        /// <summary>
        ///     <para>Slow at the beginning, fast/abrupt at the end</para>
        ///     <para>Use this curve when things are moving out.</para>
        /// </summary>
        [PublicAPI]
        public static readonly AnimationDelegate EaseInQuad = (from, to, t) =>
        {
            float Change = to - from;
            return (Change * t * t) + from;
        };

        /// <summary>
        ///     <para>Fast/abrupt at the beginning, slow at the end</para>
        ///     <para>Use this curve when things are moving in.</para>
        /// </summary>
        [PublicAPI]
        public static readonly AnimationDelegate EaseOutQuad = (from, to, t) =>
        {
            float Change = to - from;
            return (-Change * t * (t - 2)) + from;
        };

        /// <summary>
        ///     <para>Slow at the beginning and slow at the end</para>
        ///     <para>Use this curve when things are moving in or out.</para>
        /// </summary>
        [PublicAPI]
        public static readonly AnimationDelegate EaseInOutQuad = (from, to, t) =>
        {
            float Change = to - from;

            t *= 2f;
            if (t < 1f)
            {
                return (Change / 2f * t * t) + from;
            }

            t--;
            return (-Change / 2f * ((t * (t - 2f)) - 1f)) + from;
        };

        /// <summary>
        ///     Linear easing.
        /// </summary>
        [PublicAPI]
        public static readonly AnimationDelegate Linear = (from, to, t) => Mathf.Lerp(from, to, t);
    }
}