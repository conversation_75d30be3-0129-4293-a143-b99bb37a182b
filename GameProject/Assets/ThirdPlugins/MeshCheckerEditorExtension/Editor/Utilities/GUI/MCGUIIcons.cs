using JetBrains.Annotations;

using UnityEditor;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    /// <summary>
    ///     Represents a storage of icons used in UI.
    /// </summary>
    [PublicAPI]
    public sealed class MCGUIIcons
    {
        /// <summary>
        ///     The texture of a wireframe triangle icon.
        /// </summary>
        public readonly SkinDependentItem<Texture2D> WireframeTriangleIcon =
            new SkinDependentItem<Texture2D>(MCGUI.LoadIcon("Wireframe16.png"), MCGUI.LoadIcon("Wireframe16_darktheme.png"));

        /// <summary>
        ///     The texture of a solid triangle icon.
        /// </summary>
        public readonly SkinDependentItem<Texture2D> SolidTriangleIcon =
            new SkinDependentItem<Texture2D>(MCGUI.LoadIcon("Solid16.png"), MCGUI.LoadIcon("Solid16_darktheme.png"));

        /// <summary>
        ///     A small settings icon.
        /// </summary>
        public readonly SkinDependentItem<Texture2D> SettingsSmallIcon =
            new SkinDependentItem<Texture2D>(MCGUI.LoadIcon("Settings_small.png"), MCGUI.LoadIcon("Settings_small_darktheme.png"));

        /// <summary>
        ///     A small circle which can be used to indicate a status of something.
        /// </summary>
        public readonly Texture2D StatusPointIcon = MCGUI.LoadIcon("state-point.png");

        /// <summary>
        ///     A small logo icon.
        /// </summary>
        internal readonly SkinDependentItem<Texture2D> LogoIconSmall =
            new SkinDependentItem<Texture2D>(MCGUI.LoadIcon("Icon_small.png"), MCGUI.LoadIcon("Icon_small_darktheme.png"));

        /// <summary>
        ///     The texture of a bounds icon.
        /// </summary>
        internal readonly SkinDependentItem<Texture2D> BoundsIcon =
            new SkinDependentItem<Texture2D>(MCGUI.LoadIcon("Bounds32.png"), MCGUI.LoadIcon("Bounds32_darktheme.png"));

        /// <summary>
        ///     The texture of a polygons icon.
        /// </summary>
        internal readonly SkinDependentItem<Texture2D> PolygonIcon =
            new SkinDependentItem<Texture2D>(MCGUI.LoadIcon("Polygon32.png"), MCGUI.LoadIcon("Polygon32_darktheme.png"));

        /// <summary>
        ///     The texture of a checker icon.
        /// </summary>
        internal readonly SkinDependentItem<Texture2D> CheckerIcon =
            new SkinDependentItem<Texture2D>(MCGUI.LoadIcon("Checker32.png"), MCGUI.LoadIcon("Checker32_darktheme.png"));

        /// <summary>
        ///     Gets the texture of an analysis icon.
        /// </summary>
        internal readonly SkinDependentItem<Texture2D> AnalysisIcon =
            new SkinDependentItem<Texture2D>(MCGUI.LoadIcon("Analysis.png"), MCGUI.LoadIcon("Analysis_darktheme.png"));

        /// <summary>
        ///     Gets the texture of an analysis icon.
        /// </summary>
        internal readonly SkinDependentItem<Texture2D> MaximizeIcon =
            new SkinDependentItem<Texture2D>(MCGUI.LoadIcon("maximize.png"), MCGUI.LoadIcon("maximize_darktheme.png"));

        /// <summary>
        ///     Gets the texture of a lighting icon.
        /// </summary>
        [CanBeNull]
        internal Texture2D SceneViewLightingIcon
        {
            get
            {
                if (InnerSceneViewLightingIcon == null)
                {
                    InnerSceneViewLightingIcon = EditorGUIUtility.FindTexture("SceneViewLighting");
                }

                return InnerSceneViewLightingIcon;
            }
        }

        /// <summary>
        ///     Gets the texture of a state line.
        /// </summary>
        [CanBeNull]
        internal Texture2D StateTexture
        {
            get
            {
                if (InnerStateTexture == null)
                {
                    InnerStateTexture = MCGUI.LoadIcon("mc-vertical-line_gradient.png");
                }

                return InnerStateTexture;
            }
        }

        /// <summary>
        ///     Gets the texture of a state line.
        /// </summary>
        [CanBeNull]
        internal Texture2D VerticalLineTexture
        {
            get
            {
                if (InnerVerticalLineTexture == null)
                {
                    InnerVerticalLineTexture = MCGUI.LoadIcon("mc-vertical-line_solid.png");
                }

                return InnerVerticalLineTexture;
            }
        }

        /// <summary>
        ///     Gets the texture of a collapse icon.
        /// </summary>
        [CanBeNull]
        internal Texture2D CollapseIcon
        {
            get
            {
                if (InnerCollapseIcon == null)
                {
                    InnerCollapseIcon = MCGUI.LoadIcon("Collapse16.png");
                }

                return InnerCollapseIcon;
            }
        }

        /// <summary>
        ///     Gets the texture of an expand icon.
        /// </summary>
        [CanBeNull]
        internal Texture2D ExpandIcon
        {
            get
            {
                if (InnerExpandIcon == null)
                {
                    InnerExpandIcon = MCGUI.LoadIcon("Expand16.png");
                }

                return InnerExpandIcon;
            }
        }

        /// <summary>
        ///     The backing field for the <see cref="SceneViewLightingIcon"/> property.
        /// </summary>
        private Texture2D InnerSceneViewLightingIcon;

        /// <summary>
        ///     The backing field for the <see cref="StateTexture"/> property.
        /// </summary>
        private Texture2D InnerStateTexture;

        /// <summary>
        ///     The backing field for the <see cref="VerticalLineTexture"/> property.
        /// </summary>
        private Texture2D InnerVerticalLineTexture;

        /// <summary>
        ///     The backing field for the <see cref="CollapseIcon"/> property.
        /// </summary>
        private Texture2D InnerCollapseIcon;

        /// <summary>
        ///     The backing field for the <see cref="ExpandIcon"/> property.
        /// </summary>
        private Texture2D InnerExpandIcon;
    }
}