using JetBrains.Annotations;
using UnityEditor;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    /// <summary>
    ///     Represents a skin dependent item.
    /// </summary>
    /// <typeparam name="TItem">The type of an item.</typeparam>
    public struct SkinDependentItem<TItem>
    {
        /// <summary>
        ///     Gets an item for the light skin.
        /// </summary>
        [NotNull]
        public TItem Light { get; private set; }

        /// <summary>
        ///     Gets an item for the dark (pro) skin.
        /// </summary>
        [NotNull]
        public TItem Dark { get; private set; }

        /// <summary>
        ///     Gets an item for the current skin.
        /// </summary>
        [NotNull]
        public TItem Current
        {
            get
            {
                return Get(EditorGUIUtility.isProSkin);
            }
        }

        /// <summary>
        ///     Initializes a new instance of the <see cref="SkinDependentItem{TItem}"/> struct.
        /// </summary>
        /// <param name="light">The item for the light skin.</param>
        /// <param name="dark">The item for the dark (pro) skin.</param>
        public SkinDependentItem([NotNull]TItem light, [NotNull]TItem dark)
            : this()
        {
            Light = light;
            Dark = dark;
        }

        /// <summary>
        ///     Gets an item for the specified skin.
        /// </summary>
        /// <param name="isProSkin">Specifies whether current skin are dark (pro).</param>
        /// <returns>An item for the specified skin.</returns>
        [NotNull]
        public TItem Get(bool isProSkin)
        {
            return isProSkin ? Dark : Light;
        }
    }
}
