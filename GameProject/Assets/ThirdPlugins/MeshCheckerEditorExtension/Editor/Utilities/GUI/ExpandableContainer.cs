using System;

using JetBrains.Annotations;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    /// <summary>
    ///     Represents an expandable container which changes their height smoothly.
    /// </summary>
    public sealed class ExpandableContainer
    {
        /// <summary>
        ///     The content drawing method delegate.
        /// </summary>
        /// <param name="position">The position of a content.</param>
        public delegate void DrawContentDelegate(Rect position);

        /// <summary>
        ///     The repaint callback which raised when container needs to repaint.
        /// </summary>
        [NotNull]
        private readonly Action RepaintCallback;

        /// <summary>
        ///     A style of the container.
        /// </summary>
        [CanBeNull]
        private readonly GUIStyle ContainerStyle;

        /// <summary>
        ///     Gets a currently drawn height.
        /// </summary>
        public float CurrentHeight { get; private set; }

        /// <summary>
        ///     The animation initial height.
        /// </summary>
        private float InitialHeight;

        /// <summary>
        ///     The backing field for the <see cref="TargetHeight"/> property.
        /// </summary>
        private float InnerTargetHeight;

        /// <summary>
        ///     Gets or sets the animation target height.
        /// </summary>
        private float TargetHeight
        {
            get
            {
                return InnerTargetHeight;
            }

            set
            {
                if (InnerTargetHeight != value)
                {
                    InnerTargetHeight = value;

                    if (value > 0)
                    {
                        LastSignificantHeight = value;
                    }
                }
            }
        }

        /// <summary>
        ///     The last target height that was greater than 0;
        /// </summary>
        private float LastSignificantHeight;

        /// <summary>
        ///     The value indicating whether container currently animating.
        /// </summary>
        private bool Animating;

        /// <summary>
        ///     The value indicating whether container is expanding.
        /// </summary>
        private bool IsExpanding;

        /// <summary>
        ///     The time when the animation begins.
        /// </summary>
        private DateTime AnimationStartTime;

        /// <summary>
        ///     A cached rect. Updates on Layout event.
        /// </summary>
        private Rect CachedRect;

        /// <summary>
        ///     Initializes a new instance of the <see cref="ExpandableContainer"/> class.
        /// </summary>
        /// <param name="repaintCallback">The repaint callback which used to repaint owner window when animating.</param>
        /// <param name="style">The container style. (Optional)</param>
        public ExpandableContainer([NotNull]Action repaintCallback, [CanBeNull] GUIStyle style = null)
        {
            RepaintCallback = repaintCallback;
            ContainerStyle = style;
        }

        /// <summary>
        ///     Draws the container with content.
        /// </summary>
        /// <param name="position">The position where expanded container should be drawn.</param>
        /// <param name="contentDrawer">The content drawer callback.</param>
        public void Draw(Rect position, [CanBeNull] DrawContentDelegate contentDrawer)
        {
            bool Animated = position.height != CurrentHeight;

            if (Event.current.type == EventType.Layout)
            {
                CachedRect = Animate(position);
            }

            Rect UsedPosition = CachedRect;

            if (Animated)
            {
                RepaintCallback();
            }

            if (UsedPosition.height < 1)
            {
                return;
            }

            if (ContainerStyle != null)
            {
                GUI.Box(UsedPosition, GUIContent.none, ContainerStyle);
            }

            using (new GUI.ClipScope(UsedPosition))
            {
                Rect ContentRect = new Rect(0, 0, position.width, LastSignificantHeight);

                if (contentDrawer != null)
                {
                    contentDrawer(ContentRect);
                }
            }
        }

        /// <summary>
        ///     Process an input rect.
        /// </summary>
        /// <param name="position">The input position.</param>
        /// <returns>Animated position of container.</returns>
        private Rect Animate(Rect position)
        {
            bool Equal = Mathf.Abs(CurrentHeight - position.height) <= Mathf.Epsilon;
            if (Equal)
            {
                Animating = false;
                CurrentHeight = position.height;
                return position;
            }
            else
            {
                if (!Animating || Mathf.Abs(TargetHeight - position.height) > Mathf.Epsilon)
                {
                    Animating = true;
                    IsExpanding = position.height > CurrentHeight;
                    AnimationStartTime = DateTime.Now;
                    InitialHeight = CurrentHeight;
                    TargetHeight = position.height;

                    return new Rect(position.x, position.y, position.width, CurrentHeight);
                }
                else
                {
                    float T = Mathf.Clamp01((float)(DateTime.Now - AnimationStartTime).TotalSeconds / (IsExpanding ? MCGUI.EnteringDuration : MCGUI.ExitingDuration));

                    MCGUI.AnimationDelegate EasingFunction = MCGUI.EaseInOutQuad;

                    if (position.height == 0 || InitialHeight == 0)
                    {
                        EasingFunction = IsExpanding ? MCGUI.EaseOutQuad : MCGUI.EaseInQuad;
                    }

                    CurrentHeight = EasingFunction(InitialHeight, position.height, T);

                    return new Rect(position.x, position.y, position.width, CurrentHeight);
                }
            }
        }
    }
}