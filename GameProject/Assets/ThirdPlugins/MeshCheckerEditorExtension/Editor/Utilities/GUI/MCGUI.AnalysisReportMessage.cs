using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;

using JetBrains.Annotations;

using UnityEditor;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    public static partial class MCGUI
    {
        /// <summary>
        ///     Represents a set of methods to draw an analysis report message box.
        /// </summary>
        [PublicAPI]
        public static class AnalysisReportMessage
        {
            /// <summary>
            ///     The padding of a status icon.
            /// </summary>
            private const int IconPadding = 5;

            /// <summary>
            ///     The padding of a message.
            /// </summary>
            private const int MessagePadding = 2;

            /// <summary>
            ///     A cached value of <see cref="MessagePadding"/> * 2.
            /// </summary>
            private const int DoubledMessagePadding = MessagePadding * 2;

            /// <summary>
            ///     Gets a height needed to draw a specified message content.
            /// </summary>
            /// <param name="width">Available width which will be used to draw a report message.</param>
            /// <param name="messageContent">The message which should be drawn.</param>
            /// <returns>The height needed to draw a report message.</returns>
            [PublicAPI]
            public static float GetHeight(float width, [CanBeNull] GUIContent messageContent)
            {
                float IconZoneSize = (IconPadding * 2f) + MCGUI.Icons.StatusPointIcon.width;

                if (messageContent == null)
                {
                    return IconZoneSize;
                }

                float NeededHeight = MCGUI.Styles.LabelWordWrap.CalcHeight(messageContent, width - IconZoneSize - DoubledMessagePadding) + DoubledMessagePadding;
                return Mathf.Max(NeededHeight, IconZoneSize);
            }

            /// <summary>
            ///     Draws a report message in specified position.
            /// </summary>
            /// <param name="position">The position where to draw the report message.</param>
            /// <param name="resultType">The type of an analysis result.</param>
            /// <param name="messageContent">The message which should be drawn.</param>
            [PublicAPI]
            public static void Draw(Rect position, MeshAnalysisResultType resultType, [CanBeNull] GUIContent messageContent)
            {
                Color StatusColor = GetColorForResultType(resultType);

                Draw(position, StatusColor, messageContent);
            }

            /// <summary>
            ///     Draws a report message in specified position.
            /// </summary>
            /// <param name="position">The position where to draw the report message.</param>
            /// <param name="statusColor">The color which should be used to display status.</param>
            /// <param name="messageContent">The message which should be drawn.</param>
            [PublicAPI]
            public static void Draw(Rect position, Color statusColor, [CanBeNull] GUIContent messageContent)
            {
                // Draw border
                GUI.Box(position, GUIContent.none, EditorStyles.helpBox);

                // Draw icon
                Texture2D Icon = MCGUI.Icons.StatusPointIcon;
                
#if STATUS_ICON_MIDDLE
                float IconOffsetY = (position.height - Icon.height) / 2f;
#else
                const float IconOffsetY = IconPadding;
#endif
                Rect IconRect = new Rect(position.x + IconPadding, position.y + IconOffsetY, Icon.width, Icon.height);

                MCGUI.DrawStatusPoint(IconRect, statusColor);

                if (messageContent != null)
                {
                    // Draw message label
                    Rect MessageRect = new Rect(
                        IconRect.xMax + MessagePadding,
                        position.y + MessagePadding,
                        position.width - Icon.width - (IconPadding * 2) - DoubledMessagePadding,
                        position.height);
                    GUI.Label(MessageRect, messageContent, Styles.LabelWordWrap);
                }
            }

            /// <summary>
            ///     Gets a status color for a specified result type.
            /// </summary>
            /// <param name="resultType">The analysis result type.</param>
            /// <returns>A color which should be used for status indication.</returns>
            private static Color GetColorForResultType(MeshAnalysisResultType resultType)
            {
                if (resultType == MeshAnalysisResultType.Warning)
                {
                    return MCGUI.WarningColor.Current;
                }

                if (resultType == MeshAnalysisResultType.Error)
                {
                    return MCGUI.ErrorColor.Current;
                }

                if (resultType == MeshAnalysisResultType.Success)
                {
                    return MCGUI.OkColor.Current;
                }

                return Color.gray;
            }
        }
    }
}