using System;
using System.Collections.Generic;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework;
using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    /// <summary>
    ///     Gives access to MeshChecker GUI.
    /// </summary>
    [PublicAPI]
    public static partial class MCGUI
    {
        /// <summary>
        ///     Gets the color of GUI element in «error» state.
        /// </summary>
        [Obsolete("Use ErrorColor instead.")]
        public static SkinDependentItem<Color> ErrorGUIColor
        {
            get
            {
                return ErrorColor;
            }
        }

        /// <summary>
        ///     The standard height of toolbars.
        /// </summary>
        [PublicAPI]
#if UNITY_2019_1_OR_NEWER
        public const int ToolbarHeight = 22;
#else
        public const int ToolbarHeight = 18;
#endif

        /// <summary>
        ///     The standard height of line.
        /// </summary>
        internal const int LineHeight = 18;

        /// <summary>
        ///     The size of scrollbar.
        /// </summary>
        internal const int ScrollbarSize = 15;

        /// <summary>
        ///     The duration of smooth scroll.
        /// </summary>
        internal const float ScrollSmoothDuration = 0.1f;

        /// <summary>
        ///     Directory of icons.
        /// </summary>
        private const string RecourcesFolder = "Assets/ThirdPlugins/MeshCheckerEditorExtension/Editor Default Resources/MeshCheckerEditorExtension/";

        /// <summary>
        ///     Raised when styles should be reloaded.
        /// </summary>
        internal static event Action ReloadStylesEvent = delegate { };

        /// <summary>
        ///     The width GUI layout option cache for 70 px. 
        /// </summary>
        private static readonly GUILayoutOption WidthLayoutOption70 = GUILayout.Width(70);

        /// <summary>
        ///     The width GUI layout option cache for 18 px. 
        /// </summary>
        private static readonly GUILayoutOption WidthLayoutOption18 = GUILayout.Width(18);

        /// <summary>
        ///     Indicates whether importing message was already showed.
        /// </summary>
        private static bool ImportingMessageShowed;

        /// <summary>
        ///     The backing field for the <see cref="Styles"/> property.
        /// </summary>
        private static MCGUIStyles InnerStyles;

        /// <summary>
        ///     Gets a styles instance.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public static MCGUIStyles Styles
        {
            get
            {
                if (InnerStyles == null)
                {
                    InnerStyles = new MCGUIStyles();
                }

                return InnerStyles;
            }

            internal set
            {
                InnerStyles = value;
            }
        }

        /// <summary>
        ///     The backing field for the <see cref="Icons"/> property.
        /// </summary>
        private static MCGUIIcons InnerIcons;

        /// <summary>
        ///     Gets a styles instance.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public static MCGUIIcons Icons
        {
            get
            {
                if (InnerIcons == null)
                {
                    InnerIcons = new MCGUIIcons();
                }

                return InnerIcons;
            }

            internal set
            {
                InnerIcons = value;
            }
        }

        /// <summary>
        ///     Gets the separator line color.
        /// </summary>
        public static SkinDependentItem<Color> SeparatorLineColor
        {
            get
            {
                return new SkinDependentItem<Color>(
                    new Color(0.5f, 0.5f, 0.5f),
                    new Color(0.341f, 0.341f, 0.341f));
            }
        }

        /// <summary>
        ///     Gets the color used to display selection boxes. This is standard Unity's blue color.
        /// </summary>
        [PublicAPI]
        public static SkinDependentItem<Color> SelectionColor
        {
            get
            {
                return new SkinDependentItem<Color>(new Color(0.243f, 0.49f, 0.905f), new Color(0.243f, 0.372f, 0.588f));
            }
        }

        /// <summary>
        ///     Gets the color of GUI element in «error» state.
        /// </summary>
        public static SkinDependentItem<Color> ErrorColor
        {
            get
            {
                return new SkinDependentItem<Color>(
                    new Color(0.937f, 0.086f, 0.086f),
                    new Color(0.678f, 0.105f, 0.086f));
            }
        }

        /// <summary>
        ///     Gets the color of GUI element in «warning» state.
        /// </summary>
        public static SkinDependentItem<Color> WarningColor
        {
            get
            {
                return new SkinDependentItem<Color>(
                    new Color(1f, 0.715f, 0.129f),
                    new Color(0.8f, 0.56f, 0.04f));
            }
        }

        /// <summary>
        ///     Gets the color of GUI element in «ok» state.
        /// </summary>
        public static SkinDependentItem<Color> OkColor
        {
            get
            {
                return new SkinDependentItem<Color>(
                    new Color(0.137f, 0.827f, 0.137f),
                    new Color(0.082f, 0.549f, 0.031f));
            }
        }

        /// <summary>
        ///     Gets the color of button GUI element.
        /// </summary>
        public static SkinDependentItem<Color> ButtonColor
        {
            get
            {
                return new SkinDependentItem<Color>(
                    new Color(0.302f, 0.302f, 0.302f, 1),
                    new Color(0.831f, 0.831f, 0.831f, 1));
            }
        }

        /// <summary>
        ///     Cleans used resources.
        /// </summary>
        public static void Cleanup()
        {
            if (InnerStyles != null)
            {
                InnerStyles.Dispose();
                InnerStyles = null;
            }
            Icons = null;
        }

        /// <summary>
        ///     Reloads styles and raises <see cref="ReloadStylesEvent"/>.
        /// </summary>
        internal static void ReloadStyles()
        {
            Cleanup();
            ReloadStylesEvent();
        }

        /// <summary>
        ///     Make a bunch of toggles. Only none or one can be selected.
        ///     <para>
        ///         Selection grid is GUILayout element and must be drawn in GUILayout scope.
        ///     </para>
        /// </summary>
        /// <typeparam name="T">Type of object, which referenced to <see cref="SelectionGridItem{T}"/></typeparam>
        /// <param name="items">List of <see cref="SelectionGridItem{T}"/> that will be drawn</param>
        /// <param name="selectedValue">Object, that referenced by currently selected <see cref="SelectionGridItem{T}"/></param>
        /// <param name="emptyValue">Object, that will be returned when none of grid items are selected.</param>
        /// <returns>
        ///     Returns <see cref="SelectionGridItem{T}.ReferencedItem"/> of selected grid item when one of them are selected 
        ///     and <paramref name="emptyValue"/> when neither are selected.
        /// </returns>
        [PublicAPI]
        public static T DrawSelectionGrid<T>([NotNull]IList<SelectionGridItem<T>> items, T selectedValue, T emptyValue)
        {
            if (items == null)
            {
                GUILayout.Label(new GUIContent("Failed to draw the selection grid.", "NullReferenceException: Selection grid items are null."), Styles.ErrorLabel);
                return emptyValue;
            }

            bool InitialGUIEnabledState = GUI.enabled;

            // A «for» cycle used for memory allocations optimization.
            for (int Index = 0; Index < items.Count; Index++)
            {
                SelectionGridItem<T> Item = items[Index];

                bool ItemSelected = selectedValue.Equals(Item.ReferencedItem);
                if (!Item.Enabled)
                {
                    GUI.enabled = false;
                }

                if (Item.HasColor)
                {
                    GUI.backgroundColor = Item.Color;
                }

                if (GUILayout.Toggle(
                        ItemSelected,
                        Item.Content,
                        Item.Style,
                        Item.WidthLayoutOption,
                        Item.HeightLayoutOption) != ItemSelected)
                {
                    selectedValue = ItemSelected ? emptyValue : Item.ReferencedItem;
                }

                GUI.enabled = InitialGUIEnabledState;
                GUI.backgroundColor = Color.white;
            }

            return selectedValue;
        }

        /// <summary>
        ///     Draws <see cref="IndexLoop"/> toolbar GUI in Layout zone.
        /// </summary>
        /// <param name="loop">Referenced <see cref="IndexLoop"/></param>
        /// <param name="enabled">When <b>enabled</b> - LoopToolbar are drawn as enabled GUI element. When <b>disabled</b> - as disabled.</param>
        /// <exception cref="ArgumentNullException"><paramref name="loop"/> is <see langword="null"/></exception>
        /// <exception cref="InvalidOperationException">DrawLoopToolbar should be invoked in OnGUI scope.</exception>
        public static void DrawLoopToolbar([NotNull]IndexLoop loop, bool enabled)
        {
            if (loop == null)
            {
                throw new ArgumentNullException("loop");
            }

            if (Event.current == null)
            {
                throw new InvalidOperationException("DrawLoopToolbar should be invoked in OnGUI scope.");
            }

            GUI.enabled = enabled;

            if (GUILayout.Button(new GUIContent("\u25C0\u200A", "Select previous (Left Arrow key)"), EditorStyles.toolbarButton, WidthLayoutOption18))
            {
                loop.Previous();
            }

            string CountZoneCaption;
            if (loop.Selected)
            {
                CountZoneCaption = loop.ToString(IndexLoop.LoopStringFormat.IndexToCount);
            }
            else
            {
                CountZoneCaption = (loop.Max + 1).ToString();
            }

            if (GUILayout.Button(new GUIContent(CountZoneCaption, "Remove selection"), EditorStyles.toolbarButton, WidthLayoutOption70))
            {
                loop.Deselect();
            }

            if (GUILayout.Button(new GUIContent("\u25B6", "Select next (Right Arrow key)"), EditorStyles.toolbarButton, WidthLayoutOption18))
            {
                loop.Next();
            }

            GUI.enabled = true;

            if (enabled)
            {
                if (Event.current.type == EventType.KeyDown)
                {
                    switch (Event.current.keyCode)
                    {
                        case KeyCode.RightArrow:
                            loop.Next();
                            Event.current.Use();
                            break;

                        case KeyCode.LeftArrow:
                            loop.Previous();
                            Event.current.Use();
                            break;
                    }
                }
            }
        }

        /// <summary>
        ///     Make a single press button with <see cref="EditorStyles.toolbarButton"/> style.
        /// </summary>
        /// <param name="position">Position of the button</param>
        /// <param name="caption">Text to display on the button</param>
        /// <returns><c>true</c> if a user clicks the button; otherwise, <c>false</c>.</returns>
        [PublicAPI]
        public static bool ToolbarButton(Rect position, [CanBeNull]string caption)
        {
            return GUI.Button(position, caption, EditorStyles.toolbarButton);
        }

        /// <summary>
        ///     Make a single press button with <see cref="EditorStyles.toolbarButton"/> style.
        /// </summary>
        /// <param name="position">Position of the button</param>
        /// <param name="content">Text, image and tooltip for this button.</param>
        /// <returns><c>true</c> if a user clicks the button; otherwise, <c>false</c>.</returns>
        [PublicAPI]
        public static bool ToolbarButton(Rect position, [NotNull]GUIContent content)
        {
            return GUI.Button(position, content, EditorStyles.toolbarButton);
        }

        /// <summary>
        ///     Draws a circle which can be used to indicate a status of something.
        /// </summary>
        /// <param name="position">The position where to draw.</param>
        /// <param name="color">A color of the point.</param>
        public static void DrawStatusPoint(Rect position, Color color)
        {
            using (new ColorScope(color))
            {
                GUI.DrawTexture(position, Icons.StatusPointIcon);
            }
        }

        /// <summary>
        ///     Draws a circle which can be used to indicate a status of something.
        /// </summary>
        /// <param name="position">The position where to draw.</param>
        /// <param name="color">A color of the point.</param>
        /// <param name="alpha">The color alpha.</param>
        public static void DrawStatusPoint(Rect position, Color color, float alpha)
        {
            using (new ColorScope(color, alpha))
            {
                GUI.DrawTexture(position, Icons.StatusPointIcon);
            }
        }

        /// <summary>
        ///     Draws a selection box with specified color.
        /// </summary>
        /// <param name="position">The position where to draw.</param>
        [PublicAPI]
        public static void DrawSelectionBox(Rect position)
        {
            DrawSelectionBox(position, SelectionColor.Current);
        }

        /// <summary>
        ///     Draws a selection box with specified color.
        /// </summary>
        /// <param name="position">The position where to draw.</param>
        /// <param name="color">The color of borders.</param>
        [PublicAPI]
        public static void DrawSelectionBox(Rect position, Color color)
        {
            using (new ColorScope(color))
            {
                GUI.Label(position, GUIContent.none, Styles.SelectionBox);
            }
        }

        /// <summary>
        ///     Make a separator line in the specified position.
        /// </summary>
        /// <param name="position">The position.</param>
        internal static void DrawSeparatorLine(Rect position)
        {
            using (new ColorScope(SeparatorLineColor.Current))
            {
                GUI.Box(position, string.Empty, Styles.SeparatorLine);
            }
        }

        /// <summary>
        ///     Make a scroll view.
        ///     <para>
        ///         A method <see cref="EndScrollView"/> must be invoked after content.
        ///     </para>
        /// </summary>
        /// <param name="position">The position of a scroll view.</param>
        /// <param name="scrollState">The state of a scroll view.</param>
        /// <param name="viewRect">The inner view position.</param>
        /// <param name="verticalScrollbarRect">The vertical scrollbar position.</param>
        /// <param name="horizontalScrollbarRect">The horizontal scrollbar position</param>
        /// <exception cref="ArgumentNullException"><paramref name="scrollState"/> is <see langword="null"/></exception>
        /// <exception cref="InvalidOperationException">Method should be invoked in OnGUI scope.</exception>
        internal static void BeginScrollView(
            Rect position,
            [NotNull]ScrollViewState scrollState,
            Rect viewRect,
            [CanBeNull]Rect? verticalScrollbarRect = null,
            [CanBeNull]Rect? horizontalScrollbarRect = null)
        {
            if (scrollState == null)
            {
                throw new ArgumentNullException("scrollState");
            }

            if (Event.current == null)
            {
                throw new InvalidOperationException("Method should be invoked in OnGUI scope.");
            }

            scrollState.MaxValue = new Vector2(viewRect.width - position.width, viewRect.height - position.height);
            scrollState.MinValue = Vector2.zero;

            bool HasVerticalScrollbar = verticalScrollbarRect != null;
            bool HasHorizontalScrollbar = horizontalScrollbarRect != null;

            if (Event.current.type == EventType.ScrollWheel
                && HasVerticalScrollbar
                && (position.Contains(Event.current.mousePosition)
                    || ((Rect)verticalScrollbarRect).Contains(Event.current.mousePosition)))
            {
                scrollState.DoSmoothScroll(Event.current.delta * 20);
                Event.current.Use();
            }

            if (HasVerticalScrollbar)
            {
                float NewScrollPositionY = GUI.VerticalScrollbar((Rect)verticalScrollbarRect, scrollState.ScrollPosition.y, position.height, 0, viewRect.height);
                if (NewScrollPositionY != scrollState.ScrollPosition.y)
                {
                    scrollState.SetScrollPositionY(NewScrollPositionY);
                }
            }
            else
            {
                scrollState.SetScrollPositionY(0, false);
            }

            if (HasHorizontalScrollbar)
            {
                float NewScrollPositionX = GUI.HorizontalScrollbar((Rect)horizontalScrollbarRect, scrollState.ScrollPosition.x, position.width, 0, viewRect.width);
                if (NewScrollPositionX != scrollState.ScrollPosition.x)
                {
                    scrollState.SetScrollPositionX(NewScrollPositionX);
                }
            }
            else
            {
                scrollState.SetScrollPositionX(0, false);
            }

            Vector2 ScrollPosition = scrollState.GetSmoothScrollPosition();
            GUI.BeginGroup(position);

            viewRect.x = -ScrollPosition.x;
            viewRect.y = -ScrollPosition.y;

            GUI.BeginGroup(viewRect);
        }

        /// <summary>
        ///     Loads the icon texture from an icons directory.
        /// </summary>
        /// <param name="filename">The name of icon file.</param>
        /// <returns>The icon texture.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="filename"/> is <see langword="null"/></exception>
        /// <exception cref="ArgumentException">Filename are empty.</exception>
        [NotNull]
        internal static Texture2D LoadIcon([NotNull]string filename)
        {
            if (filename == null)
            {
                throw new ArgumentNullException("filename");
            }

            if (string.IsNullOrEmpty(filename))
            {
                throw new ArgumentException("Filename are empty.", "filename");
            }

            Texture2D LoadedTexture = EditorGUIUtility.Load(string.Format("{0}{1}", RecourcesFolder, filename)) as Texture2D;

            if (LoadedTexture == null)
            {
                if (!ImportingMessageShowed)
                {
                    Debug.LogWarning("[MeshChecker] Failed to find icon in the 'Assets/Editor Default Resources/'. \nIf you have just imported this asset then, please, move the 'Assets/MeshCheckerEditorExtension/Editor Default Resources' folder into the 'Assets/'; otherwise, MeshChecker will not be able to access icons and shaders.\nThank you for buying the MeshChecker extension!");
                    ImportingMessageShowed = true;
                }

                return Texture2D.blackTexture;
            }

            return LoadedTexture;
        }

        /// <summary>
        ///     Loads the font from an recources directory.
        /// </summary>
        /// <param name="filename">The font path.</param>
        /// <param name="silent">The value indicating whether error message should not be showed.</param>
        /// <returns>The font or <c>null</c> if specified file does not exist..</returns>
        /// <exception cref="ArgumentNullException"><paramref name="filename"/> is <see langword="null"/></exception>
        /// <exception cref="ArgumentException">Filename are empty.</exception>
        [CanBeNull]
        internal static Font LoadFont([NotNull]string filename, bool silent = false)
        {
            if (filename == null)
            {
                throw new ArgumentNullException("filename");
            }

            if (string.IsNullOrEmpty(filename))
            {
                throw new ArgumentException("Filename are empty.", "filename");
            }

            Font LoadedFont = EditorGUIUtility.Load(string.Format("{0}{1}", RecourcesFolder, filename)) as Font;

            if (LoadedFont == null)
            {
                if (!ImportingMessageShowed && !silent)
                {
                    Debug.LogWarningFormat("[MeshChecker] Failed to find font in the 'Assets/Editor Default Resources/{0}'. \nIf you have just imported this asset then, please, move the 'Assets/MeshCheckerEditorExtension/Editor Default Resources' folder into the 'Assets/'; otherwise, MeshChecker will not be able to access icons and shaders.\nThank you for buying the MeshChecker extension!", filename);
                    ImportingMessageShowed = true;
                }

                return null;
            }

            return LoadedFont;
        }

        /// <summary>
        ///     Ends a scroll view.
        /// </summary>
        internal static void EndScrollView()
        {
            GUI.EndGroup();
            GUI.EndGroup();
        }
    }
}
