using System.Reflection;
using UnityEditor;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    /// <summary>
    ///     Represents extensions for the EditorWindow class.
    /// </summary>
    public static class EditorWindowExtensions
    {
        /// <summary>
        ///     Gets the docked state of specified <see cref="EditorWindow"/>.
        /// </summary>
        /// <param name="window">The editor window which docked state needed.</param>
        /// <returns><c>true</c> - if window is docked; otherwise - <c>false</c>.</returns>
        public static bool GetDocked(this EditorWindow window)
        {
            const BindingFlags FullBinding = BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static;

            PropertyInfo DockedProperty = typeof(EditorWindow).GetProperty("docked", FullBinding);

            if (DockedProperty == null)
            {
                return false;
            }

            MethodInfo IsDockedMethod = DockedProperty.GetGetMethod(true);

            return (bool)IsDockedMethod.Invoke(window, null);
        }
    }
}