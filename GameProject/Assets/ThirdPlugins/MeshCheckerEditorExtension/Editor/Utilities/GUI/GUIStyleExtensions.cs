using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    /// <summary>
    ///     Extensions class for <see cref="GUIStyle"/>
    /// </summary>
    internal static class GUIStyleExtensions
    {
        /// <summary>
        ///     Sets padding for the style.
        /// </summary>
        /// <param name="style">The style.</param>
        /// <param name="padding">The padding value.</param>
        /// <returns>The specified style with changed padding.</returns>
        [NotNull]
        public static GUIStyle SetPadding([NotNull]this GUIStyle style, [NotNull]RectOffset padding)
        {
            style.padding = padding;
            return style;
        }

        /// <summary>
        ///     Sets image position for the style.
        /// </summary>
        /// <param name="style">The style.</param>
        /// <param name="imagePosition">The new image position.</param>
        /// <returns>The specified style with changed image position.</returns>
        [NotNull]
        public static GUIStyle SetImagePosition([NotNull]this GUIStyle style, ImagePosition imagePosition)
        {
            style.imagePosition = imagePosition;
            return style;
        }

        /// <summary>
        ///     Sets overflow for the style.
        /// </summary>
        /// <param name="style">The style.</param>
        /// <param name="overflow">The overflow value.</param>
        /// <returns>The specified style with changed overflow.</returns>
        [NotNull]
        public static GUIStyle SetOverflow([NotNull]this GUIStyle style, [NotNull]RectOffset overflow)
        {
            style.overflow = overflow;
            return style;
        }

        /// <summary>
        ///     Swaps a normal state to the onNormal state and onNormal -> normal of specified style.
        /// </summary>
        /// <param name="style">The style.</param>
        /// <returns>The speficied style with swapped states.</returns>
        [NotNull]
        public static GUIStyle SwapNormalStates([NotNull]this GUIStyle style)
        {
            GUIStyleState Temp = style.normal;
            style.normal = style.onNormal;
            style.onNormal = Temp;

            return style;
        }

        /// <summary>
        ///     Changes a font and font-size of the specified style.
        /// </summary>
        /// <param name="style">The style.</param>
        /// <param name="font">The font.</param>
        /// <param name="fontSize">The size of the font.</param>
        /// <returns>The speficied style with changed font.</returns>
        [NotNull]
        public static GUIStyle SetFont([NotNull] this GUIStyle style, [NotNull] Font font, [NotNull] int fontSize)
        {
            style.font = font;
            style.fontSize = fontSize;

            return style;
        }

        /// <summary>
        ///     Changes the fixed height of the specified style.
        /// </summary>
        /// <param name="style">Target style.</param>
        /// <param name="height">The height which should be assigned.</param>
        /// <returns>The speficied style with changed <see cref="GUIStyle.fixedHeight"/>.</returns>
        [NotNull]
        public static GUIStyle SetFixedHeight([NotNull] this GUIStyle style, float height)
        {
            style.fixedHeight = height;
            return style;
        }
    }
}
