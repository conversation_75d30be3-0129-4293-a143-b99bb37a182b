using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.Utilities
{
    /// <summary>
    ///     Provides utilities for simplifying reflection usage.
    /// </summary>
    internal static class ReflectionUtilities
    {
        /// <summary>
        ///     Gets instances of the <typeparamref name="TClass"/> type that have <typeparamref name="TAttribute"/> attribute.
        /// </summary>
        /// <typeparam name="TClass">The type of the needed class.</typeparam>
        /// <typeparam name="TAttribute">The type of the attribute.</typeparam>
        /// <returns>A collection of <typeparamref name="TClass"/> instances.</returns>
        [NotNull]
        public static IEnumerable<TClass> GetClassWithAttribute<TClass, TAttribute>()
        {
            IEnumerable<Type> Types = GetTypesWithAttribute<TAttribute>();
            return CreateInstancesFromTypes<TClass>(Types);
        }

        /// <summary>
        ///     Creates instances of specified types and cast them to <typeparamref name="TClass"/>.
        /// </summary>
        /// <typeparam name="TClass">The type of result.</typeparam>
        /// <param name="types">The collection of types which should be instanced.</param>
        /// <returns>The collection of created instances.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="types"/> is <see langword="null"/></exception>
        [NotNull]
        internal static List<TClass> CreateInstancesFromTypes<TClass>([NotNull]IEnumerable<Type> types)
        {
            if (types == null)
            {
                throw new ArgumentNullException("types");
            }

            List<TClass> Result = new List<TClass>();
            foreach (Type IterationType in types)
            {
                if (IterationType == null)
                {
                    continue;
                }

                object NewInstance;

                try
                {
                    NewInstance = Activator.CreateInstance(IterationType);
                }
                catch (TypeLoadException TypeLoadException)
                {
                    Debug.LogError(string.Format(
                            "[MeshChecker editor extension] Failed to create instance of {0}.\n{1} is not a valid type.\n{2}",
                            IterationType.Name,
                            IterationType.FullName,
                            TypeLoadException));
                    continue;
                }
                catch (MissingMethodException MissingMethodException)
                {
                    Debug.LogError(string.Format(
                        "[MeshChecker editor extension] Failed to create instance of {0}.\nNo matching public constructor was found.\n{1}",
                        IterationType.Name,
                        MissingMethodException));
                    continue;
                }
                catch (TargetInvocationException TargetInvocationException)
                {
                    Debug.LogError(string.Format(
                        "[MeshChecker editor extension] Failed to create instance of {0}.\nThe constructor being called throws an exception.\n{1}",
                        IterationType.Name,
                        TargetInvocationException));
                    continue;
                }
                catch (Exception E)
                {
                    Debug.LogError(string.Format(
                        "[MeshChecker editor extension] Failed to create instance of {0}.\n{1}",
                        IterationType.Name,
                        E));
                    continue;
                }

                TClass TypedInstance;
                try
                {
                    TypedInstance = (TClass)NewInstance;
                }
                catch (Exception E)
                {
                    Debug.LogError(string.Format(
                        "[MeshChecker editor extension] Failed to create instance of {0}.\n Type mismatch.\n{1}",
                        IterationType.Name,
                        E));
                    continue;
                }

                Result.Add(TypedInstance);
            }

            return Result;
        }

        /// <summary>
        ///     Gets types that have an attribute with specified type.
        /// </summary>
        /// <typeparam name="TAttribute">The attribute type.</typeparam>
        /// <returns>A collection of types.</returns>
        /// <exception cref="AppDomainUnloadedException">The operation is attempted on an unloaded application domain. </exception>
        [NotNull]
        internal static IEnumerable<Type> GetTypesWithAttribute<TAttribute>()
        {
            List<Type> Result = new List<Type>();
            foreach (Assembly Assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                if (Assembly == null)
                {
                    continue;
                }

                Result.AddRange(GetTypesWithAttribute<TAttribute>(Assembly));
            }

            return Result;
        }

        /// <summary>
        ///     Gets an attribute that have <typeparamref name="TAttribute"/> type from a specified type.
        /// </summary>
        /// <typeparam name="TAttribute">The attribute type.</typeparam>
        /// <param name="classInstanceType">The type of class that should have an attribute you are looking for.</param>
        /// <returns>The instance of an attribute. <see langword="null" /> if attribute doesn't exists.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="classInstanceType"/> is <see langword="null"/></exception>
        /// <exception cref="TypeLoadException">A custom attribute type could not be loaded. </exception>
        [CanBeNull]
        internal static TAttribute GetAttribute<TAttribute>([NotNull]Type classInstanceType)
        {
            if (classInstanceType == null)
            {
                throw new ArgumentNullException("classInstanceType");
            }

            object[] Attributes = classInstanceType.GetCustomAttributes(typeof(TAttribute), false);
            return Attributes.OfType<TAttribute>().FirstOrDefault();
        }

        /// <summary>
        ///     Gets types from a specified assembly that have an attribute with <typeparamref name="TAttribute"/> type.
        /// </summary>
        /// <typeparam name="TAttribute">The attribute type.</typeparam>
        /// <param name="assembly">The target assembly.</param>
        /// <returns>A collection of types.</returns>
        /// <exception cref="ReflectionTypeLoadException">The assembly contains one or more types that cannot be loaded. The array returned by the <see cref="P:System.Reflection.ReflectionTypeLoadException.Types" /> property of this exception contains a <see cref="T:System.Type" /> object for each type that was loaded and null for each type that could not be loaded, while the <see cref="P:System.Reflection.ReflectionTypeLoadException.LoaderExceptions" /> property contains an exception for each type that could not be loaded.</exception>
        [LinqTunnel]
        [NotNull]
        private static IEnumerable<Type> GetTypesWithAttribute<TAttribute>([NotNull]Assembly assembly)
        {
            return assembly.GetTypes().Where(type => type != null && type.GetCustomAttributes(typeof(TAttribute), false).Length > 0);
        }
    }
}