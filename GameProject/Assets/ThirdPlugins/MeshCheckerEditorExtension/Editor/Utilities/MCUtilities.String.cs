using JetBrains.Annotations;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Utilities
{
    public static partial class MCUtilities
    {
        /// <summary>
        ///     Wraps an input string with 'b' (bold) tags.
        /// </summary>
        /// <param name="input">The input string.</param>
        /// <returns>A string wrapped with 'b' (bold) tags.</returns>
        [Pure]
        [NotNull]
        internal static string WrapBold(string input)
        {
            return string.Concat("<b>", input, "</b>");
        }
    }
}