using System;

using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;

using JetBrains.Annotations;

using UnityEditor;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Utilities.IssuesHandlers
{
    /// <summary>
    ///     Represents a helper class for handling package import issues.
    /// </summary>
    internal static class PackageImportIssues
    {
        /// <summary>
        ///     The path where resources folder should be.
        /// </summary>
        private const string CorrectResourcesPath = "Assets/ThirdPlugins/MeshCheckerEditorExtension/Editor Default Resources";

        /// <summary>
        ///     The initial path (after importing) to the resources folder.
        /// </summary>
        private const string InitialResourcesPath = "Assets/ThirdPlugins";

        /// <summary>
        ///     The resources subfolder name.
        /// </summary>
        private const string ResourcesSubfolder = "MeshCheckerEditorExtension";

        /// <summary>
        ///     Gets or sets a value indicating whether resources folder can not be located.
        /// </summary>
        private static bool ResourcesFolderNotLocated { get; set; }

        /// <summary>
        ///     Gets or sets a value indicating whether resources folder not moved after import.
        /// </summary>
        private static bool ResourcesFolderNotMoved { get; set; }

        /// <summary>
        ///     Performs the issues check.
        /// </summary>
        public static void PerformCheck()
        {
            // TODO: handle situation where user imports a new version over previous one and has two resources folder.
            ResourcesFolderNotLocated = !AssetDatabase.IsValidFolder(string.Concat(CorrectResourcesPath, "/", ResourcesSubfolder));
            ResourcesFolderNotMoved = ! AssetDatabase.IsValidFolder(string.Concat(InitialResourcesPath, "/", ResourcesSubfolder));
        }

        /// <summary>
        ///     Draws an issue messages.
        /// </summary>
        public static void DrawMessagesLayout()
        {
            if (ResourcesFolderNotMoved)
            {
                DrawMessageLayout(DrawNotMovedResourcesIssueMessageLayoutContent);
            }
            else if (ResourcesFolderNotLocated)
            {
                DrawMessageLayout(DrawNotExistResourcesIssueMessageLayoutContent);
            }
        }

        /// <summary>
        ///     Fixes the resources folder position issue.
        /// </summary>
        private static void FixResourcesFolder()
        {
            if (ResourcesFolderNotMoved)
            {
                if (!AssetDatabase.IsValidFolder(CorrectResourcesPath))
                {
                    CreateDirectory(CorrectResourcesPath);
                }
                string MoveResult = AssetDatabase.MoveAsset(
                    string.Concat(InitialResourcesPath, "/", ResourcesSubfolder),
                    string.Concat(CorrectResourcesPath, "/", ResourcesSubfolder));

                if (!string.IsNullOrEmpty(MoveResult))
                {
                    Debug.LogError("[MeshChecker] Failed to move resources folder. \n" + MoveResult + "\n\n");
                }
                else
                {
                    AssetDatabase.DeleteAsset(InitialResourcesPath);
                }

                PerformCheck();

                MCGUI.ReloadStyles();
            }
        }

        /// <summary>
        ///     Draw the resources not moved issue message content.
        /// </summary>
        private static void DrawNotMovedResourcesIssueMessageLayoutContent()
        {
            GUILayout.Label(
                "If you have just imported this asset then, please, move the 'Assets/MeshCheckerEditorExtension/Editor Default Resources' folder into the 'Assets/'; otherwise, MeshChecker will not be able to access icons and shaders.",
                MCGUI.Styles.LabelWordWrap);

            using (new MCGUI.HorizontalLayoutScope())
            {
                if (GUILayout.Button("Move automatically"))
                {
                    FixResourcesFolder();
                }
#if UNITY_2019_1_OR_NEWER
                const int IconSize = 16;
#elif UNITY_2018_4_OR_NEWER
                const int IconSize = 18;
#else
                const int IconSize = 20;
#endif
                if (GUILayout.Button(GUIContent.none, MCGUI.Styles.RefreshButton, GUILayout.Width(IconSize), GUILayout.Height(IconSize)))
                {
                    PerformCheck();

                    MCGUI.ReloadStyles();
                }
            }
        }

        /// <summary>
        ///     Draw the resources not exist issue message content.
        /// </summary>
        private static void DrawNotExistResourcesIssueMessageLayoutContent()
        {
            GUILayout.Label(
                "<b>Failed to find directory 'Assets/Editor Default Resources/MeshCheckerEditorExtension/'.</b>\n\n"
                + "MeshChecker will not be able to access icons and shaders.",
                MCGUI.Styles.LabelWordWrap);

            MCGUI.DrawSeparatorLine(GUILayoutUtility.GetRect(0f, float.MaxValue, 1f, 1f));

            GUILayout.Space(3);

            using (new MCGUI.HorizontalLayoutScope())
            {
                GUILayout.Label("<b>How to fix:</b> please, reimport package.", MCGUI.Styles.LabelWordWrap);

                if (GUILayout.Button(GUIContent.none, MCGUI.Styles.RefreshButton, GUILayout.Width(20), GUILayout.Height(20)))
                {
                    PerformCheck();

                    MCGUI.ReloadStyles();
                }
            }
        }

        /// <summary>
        ///     Draw a message layout.
        /// </summary>
        /// <param name="contentDrawer">The content drawing method.</param>
        private static void DrawMessageLayout([NotNull]Action contentDrawer)
        {
            using (new MCGUI.VerticalLayoutScope("IN ThumbnailShadow"))
            {
                using (new MCGUI.VerticalLayoutScope(MCGUI.Styles.NotificationMessage, new Color(1, 0.98f, 0.6f)))
                {
                    contentDrawer();
                }
            }
        }

        /// <summary>
        ///     Creates directories of specified full path relative to project folder.
        /// </summary>
        /// <param name="path">The needed path.</param>
        private static void CreateDirectory([NotNull] string path)
        {
            string[] Folders = path.Split(char.Parse("/"));
            string IterationPath = Folders[0];
            for (int I = 1; I < Folders.Length; I++)
            {
                string NewPath = string.Concat(IterationPath, "/", Folders[I]);
                if (!AssetDatabase.IsValidFolder(NewPath))
                {
                    AssetDatabase.CreateFolder(IterationPath, Folders[I]);
                }

                IterationPath = NewPath;
            }
        }
    }
}