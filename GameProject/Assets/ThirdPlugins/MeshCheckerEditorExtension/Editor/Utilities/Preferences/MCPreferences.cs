using System;
using System.Collections.Generic;
using HightlanderSolutions.MeshCheckerEditorExtension.Checker;
using HightlanderSolutions.MeshCheckerEditorExtension.ObjectBounds;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;

using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension
{
    /// <summary>
    ///     Provides an access to preferences of the editor extension.
    /// </summary>
    internal static class MCPreferences
    {
        /// <summary>
        ///     The upper limit of a sizes precision.
        /// </summary>
        public const int SizesPrecisionUpperLimit = 6;

        #region Preferences keys

        /// <summary>
        ///     The root part of preferences.
        /// </summary>
        private const string PreferencesRoot = "HightlanderSolutions.MeshCheckerWindow";

        /// <summary>
        ///     The dictionary of editor preferences keys.
        /// </summary>
        [NotNull]
        private static readonly Dictionary<PreferenceType, string> EditorPreferencesKeys = new Dictionary<PreferenceType, string>
            {
                { PreferenceType.SizesPrecision, PreferencesRoot + ".SizesPrecision" },
                { PreferenceType.IsDrawAxis, PreferencesRoot + ".IsDrawAxis" },
                { PreferenceType.IsDrawBoundingBox, PreferencesRoot + ".IsDrawBoundingBox" },
                { PreferenceType.BoundingBoxColorR, PreferencesRoot + ".BoundingBoxColorR" },
                { PreferenceType.BoundingBoxColorG, PreferencesRoot + ".BoundingBoxColorG" },
                { PreferenceType.BoundingBoxColorB, PreferencesRoot + ".BoundingBoxColorB" },
                { PreferenceType.BoundingBoxColorA, PreferencesRoot + ".BoundingBoxColorA" },
                { PreferenceType.AutoSetChecker, PreferencesRoot + ".AutoSetCheckerTexture" },
                { PreferenceType.CheckerSize, PreferencesRoot + ".CheckerTextureSize" },
                { PreferenceType.CheckerProceduralTiling, PreferencesRoot + ".CheckerProceduralTiling" },
                { PreferenceType.UnitsType, PreferencesRoot + ".UnitsType" },
                { PreferenceType.UnitCoefficient, PreferencesRoot + ".UnitCoefficient" },
                { PreferenceType.DrawWireframe, PreferencesRoot + ".DrawWireframe" },
                { PreferenceType.CheckerTextureType, PreferencesRoot + ".CheckerTextureType" },
                { PreferenceType.CustomCheckerTexturePath, PreferencesRoot + ".CustomCheckerTexturePath" },
                { PreferenceType.CheckerTextureUVTilingX, PreferencesRoot + ".CheckerTextureUVTilingX" },
                { PreferenceType.CheckerTextureUVTilingY, PreferencesRoot + ".CheckerTextureUVTilingY" },
                { PreferenceType.CheckerTextureUVOffsetX, PreferencesRoot + ".CheckerTextureUVOffsetX" },
                { PreferenceType.CheckerTextureUVOffsetY, PreferencesRoot + ".CheckerTextureUVOffsetY" },
                { PreferenceType.BoundsType, PreferencesRoot + ".BoundsType" },
                { PreferenceType.IsDrawBoundingBoxCenter, PreferencesRoot + ".IsDrawBoundingBoxCenter" },
                { PreferenceType.Version, PreferencesRoot + ".Version" },
                { PreferenceType.DisableDefines, PreferencesRoot + ".DisableDefines" },
            };

        #endregion

        /// <summary>
        ///     Default bounding box color constant.
        /// </summary>
        private static readonly Color DefaultBoundingBoxColor = Color.yellow;

        /// <summary>
        ///     Basic unit convertation coefficients.
        /// </summary>
        [NotNull]
        private static readonly Dictionary<UnitType, float> UnitBasicCoefficients = new Dictionary<UnitType, float>
        {
            { UnitType.Meters, 1f },
            { UnitType.Inches, 39.3700787f },
            { UnitType.Feet, 3.280839895f },
            { UnitType.Centimeters, 100f }
        };

        /// <summary>
        ///     A collection of subscribers on a specific event that occurs when specified preference has been changed.
        /// </summary>
        [NotNull]
        private static readonly Dictionary<PreferenceType, Action> PreferenceChangedEventSubscribers = new Dictionary<PreferenceType, Action>();

        /// <summary>
        ///     Occurs when a preference has been changed.
        /// </summary>
        internal static event Action PreferencesChangedEvent;

        #region Version
        private static string InnerVersion;

        /// <summary>
        ///     Gets or sets a version of the editor extension.
        /// </summary>
        [CanBeNull]
        internal static string Version
        {
            get
            {
                if (InnerVersion == null
                    && EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.Version]))
                {
                    InnerVersion = EditorPrefs.GetString(EditorPreferencesKeys[PreferenceType.Version]);
                }

                return InnerVersion;
            }

            set
            {
                if (InnerVersion != value)
                {
                    InnerVersion = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.Version);

                    EditorPrefs.SetString(EditorPreferencesKeys[PreferenceType.Version], InnerVersion);
                }
            }
        }
        #endregion

        #region Current Unit Type
        private static UnitType? InnerCurrentUnitType;

        /// <summary>
        ///     Gets or sets the current units type.
        /// </summary>
        internal static UnitType CurrentUnitType
        {
            get
            {
                if (InnerCurrentUnitType == null)
                {
                    // If not initialized - try load from EditorPrefs or set default value
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.UnitsType]))
                    {
                        InnerCurrentUnitType =
                            (UnitType)EditorPrefs.GetInt(EditorPreferencesKeys[PreferenceType.UnitsType]);
                    }
                    else
                    {
                        InnerCurrentUnitType = UnitType.Meters;
                    }
                }

                return (UnitType)InnerCurrentUnitType;
            }

            set
            {
                if (CurrentUnitType != value)
                {
                    UnitType LastUnitType = CurrentUnitType;
                    InnerCurrentUnitType = value;

                    // Change units coefficient
                    float UnitCefficientCopy = UnitCoefficient;
                    UnitCefficientCopy /= UnitBasicCoefficients[LastUnitType];
                    UnitCefficientCopy *= UnitBasicCoefficients[CurrentUnitType];
                    UnitCoefficient = UnitCefficientCopy;

                    // Invoke preference changed event
                    InvokePreferenceChangedEventHandlers(PreferenceType.UnitsType);

                    EditorPrefs.SetInt(EditorPreferencesKeys[PreferenceType.UnitsType], (int)InnerCurrentUnitType);
                }
            }
        }

        #endregion

        #region Unit Coefficient

        private static float? InnerUnitCoefficient;

        /// <summary>
        ///     Gets or sets a unit coefficient.
        /// </summary>
        internal static float UnitCoefficient
        {
            get
            {
                if (InnerUnitCoefficient == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.UnitCoefficient]))
                    {
                        InnerUnitCoefficient = EditorPrefs.GetFloat(EditorPreferencesKeys[PreferenceType.UnitCoefficient]);
                    }
                    else
                    {
                        InnerUnitCoefficient = 1f;
                    }
                }

                return (float)InnerUnitCoefficient;
            }

            set
            {
                if (InnerUnitCoefficient != value)
                {
                    InnerUnitCoefficient = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.UnitCoefficient);

                    EditorPrefs.SetFloat(EditorPreferencesKeys[PreferenceType.UnitCoefficient], (float)InnerUnitCoefficient);
                }
            }
        }

        #endregion

        #region Draw Wireframe
        private static bool? InnerDrawWireframe;

        /// <summary>
        ///     Gets or sets a value indicating whether wireframe should be drawn.
        /// </summary>
        internal static bool DrawWireframe
        {
            get
            {
                if (InnerDrawWireframe == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.DrawWireframe]))
                    {
                        InnerDrawWireframe = EditorPrefs.GetBool(EditorPreferencesKeys[PreferenceType.DrawWireframe]);
                    }
                    else
                    {
                        InnerDrawWireframe = false;
                    }
                }

                return (bool)InnerDrawWireframe;
            }

            set
            {
                if (InnerDrawWireframe != value)
                {
                    InnerDrawWireframe = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.DrawWireframe);

                    EditorPrefs.SetBool(EditorPreferencesKeys[PreferenceType.DrawWireframe], (bool)InnerDrawWireframe);
                }
            }
        }
        #endregion

        #region Sizes Precision

        private static int? InnerSizesPrecisionSetting;

        /// <summary>
        ///     Gets or sets a sizes precision (count of digits after point). Saves and loads automatically.
        /// </summary>
        internal static int SizesPrecisionSetting
        {
            get
            {
                // If the backing field not initialized - try to load preference or set a default value.
                if (InnerSizesPrecisionSetting == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.SizesPrecision]))
                    {
                        InnerSizesPrecisionSetting = EditorPrefs.GetInt(EditorPreferencesKeys[PreferenceType.SizesPrecision]);
                    }
                    else
                    {
                        InnerSizesPrecisionSetting = 2;
                    }
                }

                return (int)InnerSizesPrecisionSetting;
            }

            set
            {
                // If value are changed.
                if (InnerSizesPrecisionSetting != value)
                {
                    // Update the backing field.
                    InnerSizesPrecisionSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.SizesPrecision);

                    // Save the preference.
                    EditorPrefs.SetInt(EditorPreferencesKeys[PreferenceType.SizesPrecision], (int)InnerSizesPrecisionSetting);
                }
            }
        }

        #endregion

        #region Draw Axis

        private static bool? InnerDrawAxisSetting;

        /// <summary>
        ///     Gets or sets a value indicating whether axis should be drawn.
        /// </summary>
        internal static bool DrawAxisSetting
        {
            get
            {
                if (InnerDrawAxisSetting == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.IsDrawAxis]))
                    {
                        InnerDrawAxisSetting = EditorPrefs.GetBool(EditorPreferencesKeys[PreferenceType.IsDrawAxis]);
                    }
                    else
                    {
                        InnerDrawAxisSetting = true;
                    }
                }

                return (bool)InnerDrawAxisSetting;
            }

            set
            {
                if (InnerDrawAxisSetting != value)
                {
                    InnerDrawAxisSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.IsDrawAxis);

                    EditorPrefs.SetBool(EditorPreferencesKeys[PreferenceType.IsDrawAxis], (bool)InnerDrawAxisSetting);
                }
            }
        }

        #endregion

        #region Draw Bounding Box

        private static bool? InnerDrawBoundingBoxSetting;

        /// <summary>
        ///     Gets or sets a value indicating whether bounding box should be drawn.
        /// </summary>
        internal static bool DrawBoundingBoxSetting
        {
            get
            {
                if (InnerDrawBoundingBoxSetting == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.IsDrawBoundingBox]))
                    {
                        InnerDrawBoundingBoxSetting = EditorPrefs.GetBool(EditorPreferencesKeys[PreferenceType.IsDrawBoundingBox]);
                    }
                    else
                    {
                        InnerDrawBoundingBoxSetting = true;
                    }
                }

                return (bool)InnerDrawBoundingBoxSetting;
            }

            set
            {
                if (InnerDrawBoundingBoxSetting != value)
                {
                    InnerDrawBoundingBoxSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.IsDrawBoundingBox);

                    EditorPrefs.SetBool(EditorPreferencesKeys[PreferenceType.IsDrawBoundingBox], (bool)InnerDrawBoundingBoxSetting);
                }
            }
        }

        #endregion

        #region Draw Bounding Box Center

        private static bool? InnerDrawBoundingBoxCenterSetting;

        /// <summary>
        ///     Gets or sets a value indicating whether a bounding center should be drawn.
        /// </summary>
        internal static bool DrawBoundingBoxCenterSetting
        {
            get
            {
                if (InnerDrawBoundingBoxCenterSetting == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.IsDrawBoundingBoxCenter]))
                    {
                        InnerDrawBoundingBoxCenterSetting = EditorPrefs.GetBool(EditorPreferencesKeys[PreferenceType.IsDrawBoundingBoxCenter]);
                    }
                    else
                    {
                        InnerDrawBoundingBoxCenterSetting = true;
                    }
                }

                return (bool)InnerDrawBoundingBoxCenterSetting;
            }

            set
            {
                if (InnerDrawBoundingBoxCenterSetting != value)
                {
                    InnerDrawBoundingBoxCenterSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.IsDrawBoundingBoxCenter);

                    EditorPrefs.SetBool(EditorPreferencesKeys[PreferenceType.IsDrawBoundingBoxCenter], (bool)InnerDrawBoundingBoxCenterSetting);
                }
            }
        }

        #endregion

        #region Bounding Box Color

        private static Color? InnerBoundingBoxColorSetting;

        /// <summary>
        ///     Gets or sets a color of bounding box lines in the scene view. Saves and loads automatically.
        /// </summary>
        internal static Color BoundingBoxColorSetting
        {
            get
            {
                // If backing field not initialized -> load preference or set default.
                if (InnerBoundingBoxColorSetting == null)
                {
                    InnerBoundingBoxColorSetting = LoadBoundingBoxColor();
                }

                return (Color)InnerBoundingBoxColorSetting;
            }

            set
            {
                // If value changed
                if (InnerBoundingBoxColorSetting != value)
                {
                    // Set new value
                    InnerBoundingBoxColorSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.BoundingBoxColor);

                    // Save preference
                    SaveBoundingBoxColor((Color)InnerBoundingBoxColorSetting);
                }
            }
        }

        #endregion

        #region Auto Set Checker

        private static bool? InnerAutoSetCheckerSetting;

        /// <summary>
        ///     Gets or sets a value indicating whether a checker material should apply automatically.
        /// </summary>
        internal static bool AutoSetCheckerSetting
        {
            get
            {
                if (InnerAutoSetCheckerSetting == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.AutoSetChecker]))
                    {
                        InnerAutoSetCheckerSetting = EditorPrefs.GetBool(EditorPreferencesKeys[PreferenceType.AutoSetChecker]);
                    }
                    else
                    {
                        InnerAutoSetCheckerSetting = false;
                    }
                }

                return (bool)InnerAutoSetCheckerSetting;
            }

            set
            {
                if (InnerAutoSetCheckerSetting != value)
                {
                    InnerAutoSetCheckerSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.AutoSetChecker);

                    EditorPrefs.SetBool(EditorPreferencesKeys[PreferenceType.AutoSetChecker], (bool)InnerAutoSetCheckerSetting);
                }
            }
        }

        #endregion

        #region Checker Size

        private static int? InnerCheckerSizeSetting;

        /// <summary>
        ///     Gets or sets the checker size. The value is a power of two.
        ///     <para>
        ///         Default: 10. (2^10 is 1024)
        ///     </para>
        /// </summary>
        internal static int CheckerSizeSetting
        {
            get
            {
                if (InnerCheckerSizeSetting == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.CheckerSize]))
                    {
                        InnerCheckerSizeSetting = EditorPrefs.GetInt(EditorPreferencesKeys[PreferenceType.CheckerSize]);
                    }
                    else
                    {
                        InnerCheckerSizeSetting = 10;
                    }
                }

                return (int)InnerCheckerSizeSetting;
            }

            set
            {
                if (InnerCheckerSizeSetting != value)
                {
                    InnerCheckerSizeSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.CheckerSize);

                    EditorPrefs.SetInt(EditorPreferencesKeys[PreferenceType.CheckerSize], (int)InnerCheckerSizeSetting);
                }
            }
        }

        #endregion

        #region Checker Tiling

        private static float? InnerCheckerTilingSetting;

        /// <summary>
        ///     Gets or sets tiling of the checker texture.
        /// </summary>
        internal static float CheckerTilingSetting
        {
            get
            {
                if (InnerCheckerTilingSetting == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.CheckerProceduralTiling]))
                    {
                        InnerCheckerTilingSetting = EditorPrefs.GetFloat(EditorPreferencesKeys[PreferenceType.CheckerProceduralTiling]);
                    }
                    else
                    {
                        InnerCheckerTilingSetting = 2f;
                    }
                }

                return (float)InnerCheckerTilingSetting;
            }

            set
            {
                if (InnerCheckerTilingSetting != value)
                {
                    InnerCheckerTilingSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.CheckerProceduralTiling);

                    EditorPrefs.SetFloat(EditorPreferencesKeys[PreferenceType.CheckerProceduralTiling], (float)InnerCheckerTilingSetting);
                }
            }
        }

        #endregion

        #region Checker Texture Type

        private static CheckerTextureType? InnerCheckerTextureTypeSetting;

        /// <summary>
        ///     Gets or sets a type of the checker texture.
        /// </summary>
        internal static CheckerTextureType CheckerTextureTypeSetting
        {
            get
            {
                if (InnerCheckerTextureTypeSetting == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.CheckerTextureType]))
                    {
                        InnerCheckerTextureTypeSetting = (CheckerTextureType)EditorPrefs.GetInt(EditorPreferencesKeys[PreferenceType.CheckerTextureType]);
                    }
                    else
                    {
                        InnerCheckerTextureTypeSetting = CheckerTextureType.Procedural;
                    }
                }

                return (CheckerTextureType)InnerCheckerTextureTypeSetting;
            }

            set
            {
                if (InnerCheckerTextureTypeSetting != value)
                {
                    InnerCheckerTextureTypeSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.CheckerTextureType);

                    EditorPrefs.SetInt(EditorPreferencesKeys[PreferenceType.CheckerTextureType], (int)InnerCheckerTextureTypeSetting);
                }
            }
        }

        #endregion

        #region Custom Checker Texture

        private static Texture2D InnerCustomCheckerTextureSetting;

        /// <summary>
        ///     Gets or sets a texture of checker.
        /// </summary>
        [CanBeNull]
        internal static Texture2D CustomCheckerTextureSetting
        {
            get
            {
                if (InnerCustomCheckerTextureSetting == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.CustomCheckerTexturePath]))
                    {
                        string CustomCheckerTexturePath = EditorPrefs.GetString(EditorPreferencesKeys[PreferenceType.CustomCheckerTexturePath]);
                        if (!string.IsNullOrEmpty(CustomCheckerTexturePath))
                        {
#if UNITY_4_6 || UNITY_5_0
                            CustomCheckerTextureSetting = AssetDatabase.LoadAssetAtPath(CustomCheckerTexturePath, typeof(Texture2D)) as Texture2D;
#else
                            CustomCheckerTextureSetting = AssetDatabase.LoadAssetAtPath<Texture2D>(CustomCheckerTexturePath);
#endif
                        }
                    }
                }

                return InnerCustomCheckerTextureSetting;
            }

            set
            {
                if (InnerCustomCheckerTextureSetting != value)
                {
                    InnerCustomCheckerTextureSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.CustomCheckerTexturePath);

                    // Save to EditorPrefs
                    string CustomCheckerTexturePath = string.Empty;
                    if (InnerCustomCheckerTextureSetting != null)
                    {
                        CustomCheckerTexturePath = AssetDatabase.GetAssetPath(InnerCustomCheckerTextureSetting);
                    }

                    EditorPrefs.SetString(EditorPreferencesKeys[PreferenceType.CustomCheckerTexturePath], CustomCheckerTexturePath);
                }
            }
        }

        #endregion

        #region Checker Texture UV Tiling

        private static Vector2? InnerCheckerTextureUVTilingSetting;

        /// <summary>
        ///     Gets or sets UV tiling of a checker texture.
        /// </summary>
        internal static Vector2 CheckerTextureUVTilingSetting
        {
            get
            {
                if (InnerCheckerTextureUVTilingSetting == null)
                {
                    float CheckerTextureUVTilingX = 1f;
                    float CheckerTextureUVTilingY = 1f;
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.CheckerTextureUVTilingX]))
                    {
                        CheckerTextureUVTilingX = EditorPrefs.GetFloat(EditorPreferencesKeys[PreferenceType.CheckerTextureUVTilingX]);
                    }

                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.CheckerTextureUVTilingY]))
                    {
                        CheckerTextureUVTilingY = EditorPrefs.GetFloat(EditorPreferencesKeys[PreferenceType.CheckerTextureUVTilingY]);
                    }

                    InnerCheckerTextureUVTilingSetting = new Vector2(CheckerTextureUVTilingX, CheckerTextureUVTilingY);
                }

                return (Vector2)InnerCheckerTextureUVTilingSetting;
            }

            set
            {
                if (InnerCheckerTextureUVTilingSetting != value)
                {
                    InnerCheckerTextureUVTilingSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.CheckerTextureUVTiling);

                    // Save to EditorPrefs
                    Vector2 CheckerTextureUVTiling = (Vector2)InnerCheckerTextureUVTilingSetting;
                    EditorPrefs.SetFloat(EditorPreferencesKeys[PreferenceType.CheckerTextureUVTilingX], CheckerTextureUVTiling.x);
                    EditorPrefs.SetFloat(EditorPreferencesKeys[PreferenceType.CheckerTextureUVTilingY], CheckerTextureUVTiling.y);
                }
            }
        }

        #endregion

        #region Checker Texture UV Offset

        private static Vector2? InnerCheckerTextureUVOffsetSetting;

        /// <summary>
        ///     Gets or sets a UV offset of the checker texture.
        /// </summary>
        internal static Vector2 CheckerTextureUVOffsetSetting
        {
            get
            {
                if (InnerCheckerTextureUVOffsetSetting == null)
                {
                    float CheckerTextureUVOffsetX = 0f;
                    float CheckerTextureUVOffsetY = 0f;
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.CheckerTextureUVOffsetX]))
                    {
                        CheckerTextureUVOffsetX = EditorPrefs.GetFloat(EditorPreferencesKeys[PreferenceType.CheckerTextureUVOffsetX]);
                    }

                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.CheckerTextureUVOffsetY]))
                    {
                        CheckerTextureUVOffsetY = EditorPrefs.GetFloat(EditorPreferencesKeys[PreferenceType.CheckerTextureUVOffsetY]);
                    }

                    InnerCheckerTextureUVOffsetSetting = new Vector2(CheckerTextureUVOffsetX, CheckerTextureUVOffsetY);
                }

                return (Vector2)InnerCheckerTextureUVOffsetSetting;
            }

            set
            {
                if (InnerCheckerTextureUVOffsetSetting != value)
                {
                    InnerCheckerTextureUVOffsetSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.CheckerTextureUVOffset);

                    // Save to EditorPrefs
                    Vector2 CheckerTextureUVOffset = (Vector2)InnerCheckerTextureUVOffsetSetting;
                    EditorPrefs.SetFloat(EditorPreferencesKeys[PreferenceType.CheckerTextureUVOffsetX], CheckerTextureUVOffset.x);
                    EditorPrefs.SetFloat(EditorPreferencesKeys[PreferenceType.CheckerTextureUVOffsetY], CheckerTextureUVOffset.y);
                }
            }
        }

        #endregion

        #region Bounds Type

        private static BoundsType? InnerBoundsTypeSetting;

        /// <summary>
        ///     Gets or sets a bounds type.
        /// </summary>
        internal static BoundsType BoundsTypeSetting
        {
            get
            {
                if (InnerBoundsTypeSetting == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.BoundsType]))
                    {
                        InnerBoundsTypeSetting = (BoundsType)EditorPrefs.GetInt(EditorPreferencesKeys[PreferenceType.BoundsType]);
                    }
                    else
                    {
                        InnerBoundsTypeSetting = BoundsType.World;
                    }
                }

                return (BoundsType)InnerBoundsTypeSetting;
            }

            set
            {
                if (InnerBoundsTypeSetting != value)
                {
                    InnerBoundsTypeSetting = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.BoundsType);

                    EditorPrefs.SetInt(EditorPreferencesKeys[PreferenceType.BoundsType], (int)InnerBoundsTypeSetting);
                }
            }
        }

        #endregion

        #region Disable Defines

        private static bool? InnerDisableDefines;

        /// <summary>
        ///     Gets or sets a value indicating whether axis should be drawn.
        /// </summary>
        internal static bool DisableDefinesSetting
        {
            get
            {
                if (InnerDisableDefines == null)
                {
                    if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.DisableDefines]))
                    {
                        InnerDisableDefines = EditorPrefs.GetBool(EditorPreferencesKeys[PreferenceType.DisableDefines]);
                    }
                    else
                    {
                        InnerDisableDefines = false;
                    }
                }

                return (bool)InnerDisableDefines;
            }

            set
            {
                if (InnerDisableDefines != value)
                {
                    InnerDisableDefines = value;

                    InvokePreferenceChangedEventHandlers(PreferenceType.DisableDefines);

                    EditorPrefs.SetBool(EditorPreferencesKeys[PreferenceType.DisableDefines], (bool)InnerDisableDefines);
                }
            }
        }

        #endregion

        /// <summary>
        ///     Subscribes to a preference changed event.
        /// </summary>
        /// <param name="preferenceType">The preference type.</param>
        /// <param name="callback">The callback method which will be invoked when a preference with a specified type changed.</param>
        internal static void Subscribe(PreferenceType preferenceType, [NotNull]Action callback)
        {
            if (PreferenceChangedEventSubscribers.ContainsKey(preferenceType))
            {
                PreferenceChangedEventSubscribers[preferenceType] += callback;
            }
            else
            {
                PreferenceChangedEventSubscribers.Add(preferenceType, callback);
            }
        }

        /// <summary>
        ///     Unsubscribes from a preference changed event.
        /// </summary>
        /// <param name="preferenceType">The preference type.</param>
        /// <param name="callback">The callback that should be unsubscribed.</param>
        internal static void Unsubscribe(PreferenceType preferenceType, [NotNull]Action callback)
        {
            if (PreferenceChangedEventSubscribers.ContainsKey(preferenceType))
            {
                PreferenceChangedEventSubscribers[preferenceType] -= callback;
            }
        }

#if UNITY_2018_3_OR_NEWER
        internal class MeshCheckerSettingsProvider : SettingsProvider
        {
            public MeshCheckerSettingsProvider(string path, SettingsScope scopes = SettingsScope.User)
                : base(path, scopes)
            { }
 
            public override void OnGUI(string searchContext)
            {
                DrawWindow();
            }
        }
 
        [SettingsProvider]
        internal static SettingsProvider MeshCheckerSettings()
        {
            return new MeshCheckerSettingsProvider("Preferences/Mesh Checker");
        }
#else
        /// <summary>
        ///     Draws preferences window zone.
        /// </summary>
        [PreferenceItem("Mesh Checker"), UsedImplicitly]
#endif
        internal static void DrawWindow()
        {
            using (new GUILayout.VerticalScope(EditorStyles.helpBox))
            {
                GUILayout.Label("Sizes and Bounds", MCGUI.Styles.RobotoLabelBold);
                BoundsSettingsDrawer.DrawUnitSettingsLayout();
                BoundsSettingsDrawer.DrawSizesPrecisionSettingsLayout();
                BoundsSettingsDrawer.DrawDisplayBoundsCenterSettingsLayout();
                BoundsSettingsDrawer.DrawDisplayAxisSettingsLayout();
                BoundsSettingsDrawer.DrawBoundingBoxColorSettingsLayout();
            }

            using (new GUILayout.VerticalScope(EditorStyles.helpBox))
            {
                GUILayout.Label("UV Checker", MCGUI.Styles.RobotoLabelBold);
                DrawWireframe = EditorGUILayout.ToggleLeft("Display wireframe while a checker texture is applied", DrawWireframe);
            }

            using (new GUILayout.VerticalScope(EditorStyles.helpBox))
            {
                GUILayout.Label("External integration with Mesh Checker", MCGUI.Styles.RobotoLabelBold);
                DrawDisableDefinesLine();
            }


            GUILayout.FlexibleSpace();

            GUILayout.BeginHorizontal();

            if (GUILayout.Button("Clear MeshChecker editor preferences records"))
            {
                ClearPreferencesRecords();
            }

            GUILayout.EndHorizontal();
        }

        private static void DrawDisableDefinesLine()
        {
            GUIContent DisableDefinesToggleContent = new GUIContent(
                "Disable defines",
                "Disable auto adding of the 'MESH_CHECKER' to scripting define symbols which can be used by third-party assets and extensions.");
            DisableDefinesSetting = EditorGUILayout.ToggleLeft(DisableDefinesToggleContent, DisableDefinesSetting);

            if (GUILayout.Button(new GUIContent("Remove 'MESH_CHECKER' scripting define symbol", "Remove the 'MESH_CHECKER' defined symbol from all platforms.")))
            {
                DisableDefinesSetting = true;
                ScriptingDefineSymbolsInstaller.DeleteDefinedSymbol();
            }
        }

        /// <summary>
        ///     Replaces unit coefficient to default value for selected UnitType.
        /// </summary>
        internal static void DropUnitCoefficientToDefault()
        {
            UnitCoefficient = UnitBasicCoefficients[CurrentUnitType];
        }

        /// <summary>
        ///     Invokes all handlers for specified preference type.
        /// </summary>
        /// <param name="preferenceType">The preference type.</param>
        private static void InvokePreferenceChangedEventHandlers(PreferenceType preferenceType)
        {
            if (PreferenceChangedEventSubscribers.ContainsKey(preferenceType)
                && PreferenceChangedEventSubscribers[preferenceType] != null)
            {
                PreferenceChangedEventSubscribers[preferenceType]();
            }

            if (PreferencesChangedEvent != null)
            {
                try
                {
                    PreferencesChangedEvent();
                }
                catch (Exception E)
                {
                    Debug.LogException(E);
                }
            }
        }

        /// <summary>
        ///     Clears all MeshChecker preferences records.
        /// </summary>
        private static void ClearPreferencesRecords()
        {
            foreach (KeyValuePair<PreferenceType, string> KeyValuePair in EditorPreferencesKeys)
            {
                if (EditorPrefs.HasKey(KeyValuePair.Value))
                {
                    EditorPrefs.DeleteKey(KeyValuePair.Value);
                }
            }
        }

        #region Load\Save BoundingBoxColor

        /// <summary>
        ///     Saves the BoundingBoxColor preference as four float records.
        /// </summary>
        /// <param name="color">The color of bounding boxes.</param>
        private static void SaveBoundingBoxColor(Color color)
        {
            EditorPrefs.SetFloat(EditorPreferencesKeys[PreferenceType.BoundingBoxColorR], color.r);
            EditorPrefs.SetFloat(EditorPreferencesKeys[PreferenceType.BoundingBoxColorG], color.g);
            EditorPrefs.SetFloat(EditorPreferencesKeys[PreferenceType.BoundingBoxColorB], color.b);
            EditorPrefs.SetFloat(EditorPreferencesKeys[PreferenceType.BoundingBoxColorA], color.a);
        }


        /// <summary>
        ///     Loads and creates color from BoundigBoxColor preference records or returns default color of bounding box.
        /// </summary>
        /// <returns>Saved bounding box color if exist; otherwise, the default value.</returns>
        private static Color LoadBoundingBoxColor()
        {
            if (EditorPrefs.HasKey(EditorPreferencesKeys[PreferenceType.BoundingBoxColorR]))
            {
                float R = EditorPrefs.GetFloat(EditorPreferencesKeys[PreferenceType.BoundingBoxColorR]);
                float G = EditorPrefs.GetFloat(EditorPreferencesKeys[PreferenceType.BoundingBoxColorG]);
                float B = EditorPrefs.GetFloat(EditorPreferencesKeys[PreferenceType.BoundingBoxColorB]);
                float A = EditorPrefs.GetFloat(EditorPreferencesKeys[PreferenceType.BoundingBoxColorA]);
                return new Color(R, G, B, A);
            }

            return DefaultBoundingBoxColor;
        }

        #endregion
    }
}