using System;

using JetBrains.Annotations;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Utilities
{
    public static partial class MCUtilities
    {
        /// <summary>
        ///     Represents a set of bezier functions.
        /// </summary>
        public static class Bezier
        {
            /// <summary>
            ///     Calculates a point for the quadratic bezier curve.
            /// </summary>
            /// <param name="p0">The first basis point.</param>
            /// <param name="p1">The second basis point.</param>
            /// <param name="p2">The third basis point.</param>
            /// <param name="t">The parameter t ∊ [0, 1]</param>
            /// <returns>Coordinates of the calculated point.</returns>
            [Pure]
            [PublicAPI]
            public static Vector3 Quadratic(Vector3 p0, Vector3 p1, Vector3 p2, float t)
            {
                if (t < 0 || t > 1)
                {
                    throw new ArgumentOutOfRangeException("t");
                }

                float OneMinusT = 1f - t;

                return (OneMinusT * OneMinusT * p0) + (2 * t * OneMinusT * p1) + (t * t * p2);
            }

            /// <summary>
            ///     Calculates a point for the cubic bezier curve.
            /// </summary>
            /// <param name="p0">The first basis point.</param>
            /// <param name="a0">The second basis point.</param>
            /// <param name="a1">The third basis point.</param>
            /// <param name="p1">The fourth basis point.</param>
            /// <param name="t">The parameter t ∊ [0, 1]</param>
            /// <returns>Coordinates of the calculated point.</returns>
            [Pure]
            [PublicAPI]
            public static Vector3 Cubic(Vector3 p0, Vector3 a0, Vector3 a1, Vector3 p1, float t)
            {
                if (t < 0 || t > 1)
                {
                    throw new ArgumentOutOfRangeException("t");
                }

                float OneMinusT = 1f - t;

                return (OneMinusT * OneMinusT * OneMinusT * p0) + (3 * t * OneMinusT * OneMinusT * a0) + (3 * t * t * OneMinusT * a1) + (t * t * t * p1);
            }
        }
    }
}