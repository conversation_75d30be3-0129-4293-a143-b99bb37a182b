using System;
using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Utilities
{
    /// <summary>
    ///     Provides utilities and extensions.
    /// </summary>
    public static partial class MCUtilities
    {
        /// <summary>
        ///     Draws a wire cube.
        /// </summary>
        /// <param name="position">The bounds position.</param>
        /// <param name="rotation">The bounds rotation.</param>
        /// <param name="size">The bounds size.</param>
        /// <param name="color">The bounds color.</param>
        public static void DrawHandleWireCube(Vector3 position, Quaternion rotation, Vector3 size, Color color)
        {
            Color InitialColor = Handles.color;
            Handles.color = color;

            Vector3 Half = size / 2;

            // draw front
            Handles.DrawLine(position + (rotation * new Vector3(-Half.x, -Half.y, Half.z)), position + (rotation * new Vector3(Half.x, -Half.y, Half.z)));
            Handles.DrawLine(position + (rotation * new Vector3(-Half.x, -Half.y, Half.z)), position + (rotation * new Vector3(-Half.x, Half.y, Half.z)));
            Handles.DrawLine(position + (rotation * new Vector3(Half.x, Half.y, Half.z)), position + (rotation * new Vector3(Half.x, -Half.y, Half.z)));
            Handles.DrawLine(position + (rotation * new Vector3(Half.x, Half.y, Half.z)), position + (rotation * new Vector3(-Half.x, Half.y, Half.z)));

            // draw back
            Handles.DrawLine(position + (rotation * new Vector3(-Half.x, -Half.y, -Half.z)), position + (rotation * new Vector3(Half.x, -Half.y, -Half.z)));
            Handles.DrawLine(position + (rotation * new Vector3(-Half.x, -Half.y, -Half.z)), position + (rotation * new Vector3(-Half.x, Half.y, -Half.z)));
            Handles.DrawLine(position + (rotation * new Vector3(Half.x, Half.y, -Half.z)), position + (rotation * new Vector3(Half.x, -Half.y, -Half.z)));
            Handles.DrawLine(position + (rotation * new Vector3(Half.x, Half.y, -Half.z)), position + (rotation * new Vector3(-Half.x, Half.y, -Half.z)));

            // draw corners
            Handles.DrawLine(position + (rotation * new Vector3(-Half.x, -Half.y, -Half.z)), position + (rotation * new Vector3(-Half.x, -Half.y, Half.z)));
            Handles.DrawLine(position + (rotation * new Vector3(Half.x, -Half.y, -Half.z)), position + (rotation * new Vector3(Half.x, -Half.y, Half.z)));
            Handles.DrawLine(position + (rotation * new Vector3(-Half.x, Half.y, -Half.z)), position + (rotation * new Vector3(-Half.x, Half.y, Half.z)));
            Handles.DrawLine(position + (rotation * new Vector3(Half.x, Half.y, -Half.z)), position + (rotation * new Vector3(Half.x, Half.y, Half.z)));

            Handles.color = InitialColor;
        }

        /// <summary>
        ///     Gets end points of x, y and z axis.
        /// </summary>
        /// <param name="size">The axis size.</param>
        /// <param name="center">The center position of axis.</param>
        /// <param name="rotation">The axis rotation.</param>
        /// <returns>Vector3[] array which contains x, y and z points.</returns>
        [NotNull]
        public static Vector3[] GetAxisEndPoints(Vector3 size, Vector3 center, Quaternion rotation)
        {
            float X = size.x / 2f;
            float Y = size.y / 2f;
            float Z = size.z / 2f;

            float MaxSize = Mathf.Max(size.x, size.y, size.z);
            float AxisAdditionalLength = MaxSize * 0.3f;

            Vector3[] Result =
                            {
                                center + (rotation * new Vector3(X + AxisAdditionalLength, -Y, -Z)),
                                center + (rotation * new Vector3(-X, Y + AxisAdditionalLength, -Z)),
                                center + (rotation * new Vector3(-X, -Y, Z + AxisAdditionalLength))
                            };

            return Result;
        }

        /// <summary>
        ///     Draws all axis.
        /// </summary>
        /// <param name="size">The axis size.</param>
        /// <param name="center">The center position of axis.</param>
        /// <param name="rotation">The axis rotation.</param>
        public static void DrawHandleAllAxis(Vector3 size, Vector3 center, Quaternion rotation)
        {
            float MaxSize = Mathf.Max(size.x, size.y, size.z);
            float ArrowSize = MaxSize * 0.05f;

            Vector3 StartPoint = center + (rotation * (-size / 2f));

            Vector3[] EndPoints = GetAxisEndPoints(size, center, rotation);

            Handles.color = Handles.xAxisColor;
            DrawHandleAxis(StartPoint, EndPoints[0], ArrowSize, 0);

            Handles.color = Handles.yAxisColor;
            DrawHandleAxis(StartPoint, EndPoints[1], ArrowSize, 1);

            Handles.color = Handles.zAxisColor;
            DrawHandleAxis(StartPoint, EndPoints[2], ArrowSize, 2);
        }

        /// <summary>
        ///     Draws an axis.
        /// </summary>
        /// <param name="startPoint">The start position.</param>
        /// <param name="endPoint">The end position.</param>
        /// <param name="arrowSize">The arrow size.</param>
        /// <param name="axisHack">0 for X, 1 for Y, 2 for Z</param>
        public static void DrawHandleAxis(Vector3 startPoint, Vector3 endPoint, float arrowSize, int axisHack)
        {
            const float ArrowInclination = 30;
            Vector3[] Points = new Vector3[4];
            Points[0] = startPoint;
            Points[1] = endPoint;

            Vector3 Align = (endPoint - startPoint).normalized;

            Vector3 Backward = -Align;

            if (axisHack == 0)
            {
                // x
                Points[2] = Quaternion.Euler(0, ArrowInclination, 0) * Backward * arrowSize;
                Points[3] = Quaternion.Euler(0, -ArrowInclination, 0) * Backward * arrowSize;
            }
            else if (axisHack == 1)
            {
                // y
                Points[2] = Quaternion.Euler(0, 0, ArrowInclination) * Backward * arrowSize;
                Points[3] = Quaternion.Euler(0, 0, -ArrowInclination) * Backward * arrowSize;
            }
            else if (axisHack == 2)
            {
                // z
                Points[2] = Quaternion.Euler(ArrowInclination, 0, 0) * Backward * arrowSize;
                Points[3] = Quaternion.Euler(-ArrowInclination, 0, 0) * Backward * arrowSize;
            }

            Handles.DrawLine(Points[0], Points[1]);
            Handles.DrawLine(Points[1], Points[1] + Points[2]);
            Handles.DrawLine(Points[1], Points[1] + Points[3]);
        }

        /// <summary>
        ///     Draws a size handle label.
        /// </summary>
        /// <param name="point">The origin of label.</param>
        /// <param name="color">The text color.</param>
        /// <param name="size">The size data.</param>
        /// <param name="letter">Vector component letter. (X, Y or Z)</param>
        /// <param name="textStyle">The style for labels in the scene view.</param>
        /// <exception cref="InvalidOperationException">No one scene view are drawn now.</exception>
        public static void DrawSizeHandleLabel(Vector3 point, Color color, float size, string letter, GUIStyle textStyle)
        {
            if (SceneView.currentDrawingSceneView == null)
            {
                throw new InvalidOperationException("No one scene view are drawn now.");
            }

            if (SceneView.currentDrawingSceneView.camera.WorldToScreenPoint(point).z > 0)
            {
                Handles.Label(
                    point,
                    string.Format("{0}: {1} {2}", letter, UnitsUtilities.TranslateToUnitsAndRound(size), MCPreferences.CurrentUnitType.GetUnitString()),
                    textStyle);
            }
        }

        /// <summary>
        ///     Extension method, which inverts quaternion.
        /// </summary>
        /// <param name="rotation">The initial quaternion.</param>
        /// <returns>Inverted quaternion.</returns>
        public static Quaternion Inverse(this Quaternion rotation)
        {
            return Quaternion.Inverse(rotation);
        }

        /// <summary>
        ///     The extension method, which gets corner points of a bounds.
        /// </summary>
        /// <param name="bounds">The bounds.</param>
        /// <returns>Corner points of bounds.</returns>
        [NotNull]
        public static Vector3[] CornerPoints(this Bounds bounds)
        {
            return bounds.CornerPoints(Quaternion.identity);
        }

        /// <summary>
        ///     Gets corner points of the bounds with specified rotation.
        /// </summary>
        /// <param name="bounds">The bounds.</param>
        /// <param name="rotation">The rotation of bounds.</param>
        /// <returns>Corner points of rotated bounds</returns>
        [NotNull]
        public static Vector3[] CornerPoints(this Bounds bounds, Quaternion rotation)
        {
            Vector3[] Result = new Vector3[8];
            Result[0] = bounds.center - (rotation * bounds.extents);
            Result[1] = bounds.center + (rotation * new Vector3(-bounds.extents.x, -bounds.extents.y, bounds.extents.z));
            Result[2] = bounds.center + (rotation * new Vector3(-bounds.extents.x, bounds.extents.y, -bounds.extents.z));
            Result[3] = bounds.center + (rotation * new Vector3(-bounds.extents.x, bounds.extents.y, bounds.extents.z));
            Result[4] = bounds.center + (rotation * new Vector3(bounds.extents.x, -bounds.extents.y, -bounds.extents.z));
            Result[5] = bounds.center + (rotation * new Vector3(bounds.extents.x, -bounds.extents.y, bounds.extents.z));
            Result[6] = bounds.center + (rotation * new Vector3(bounds.extents.x, bounds.extents.y, -bounds.extents.z));
            Result[7] = bounds.center + (rotation * bounds.extents);

            return Result;
        }

        /// <summary>
        ///     Rounds input value, based on precision preference.
        /// </summary>
        /// <param name="inputValue">The value which should be rounded.</param>
        /// <returns>Rounded value.</returns>
        public static float RoundValueWithPrecision(float inputValue)
        {
            float Result = inputValue;
            if (MCPreferences.SizesPrecisionSetting < MCPreferences.SizesPrecisionUpperLimit)
            {
                float PrecisionCoefficient = Mathf.Pow(10, MCPreferences.SizesPrecisionSetting);
                Result *= PrecisionCoefficient;
                Result = Mathf.Round(Result);
                Result /= PrecisionCoefficient;
            }

            return Result;
        }

        /// <summary>
        ///     Converts an integer value to the short string representation.
        /// </summary>
        /// <param name="inputValue">The value that should be converted.</param>
        /// <returns>The string that represents converted value.</returns>
        [NotNull]
        public static string ConvertIntToShortStringRepresentation(int inputValue)
        {
            int Rest;
            string Multiplier = string.Empty;

            if (inputValue < 10000)
            {
                Rest = inputValue;
            }
            else if (inputValue < 10000000)
            {
                Rest = inputValue / 1000;
                Multiplier = "K";
            }
            else
            {
                Rest = inputValue / 1000000;
                Multiplier = "M";
            }

            return string.Concat(inputValue >= 0 ? string.Empty : "-", Rest, Multiplier);
        }

        /// <summary>
        ///     Transforms selection to <see cref="GameObject"/>
        /// </summary>
        /// <param name="selection">The selection array.</param>
        /// <returns>Array of GameObjects.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="selection"/> is <see langword="null"/></exception>
        [NotNull]
        public static GameObject[] TransformSelectionToGameObjects([NotNull]UnityEngine.Object[] selection)
        {
            if (selection == null)
            {
                throw new ArgumentNullException("selection");
            }

            GameObject[] Result = new GameObject[selection.Length];
            for (int Index = 0; Index < selection.Length; Index++)
            {
                Result[Index] = selection[Index] as GameObject;
            }

            return Result;
        }

        /// <summary>
        ///     Checks that GameObject are prefab.
        /// </summary>
        /// <param name="gameObject">Checked game object.</param>
        /// <returns><c>true</c> if <paramref name="gameObject"/> is prefab; otherwise, <c>false</c>.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="gameObject"/> is <see langword="null"/></exception>
        public static bool IsPrefab([NotNull]GameObject gameObject)
        {
            if (gameObject == null)
            {
                throw new ArgumentNullException("gameObject");
            }

#if UNITY_2018_3_OR_NEWER
            PrefabAssetType GameObjectPrefabType = PrefabUtility.GetPrefabAssetType(gameObject);
            PrefabInstanceStatus PrefabInstanceStatus = PrefabUtility.GetPrefabInstanceStatus(gameObject);
            return PrefabInstanceStatus == PrefabInstanceStatus.NotAPrefab // Prefabs in scene have status Connected or Disconnected
                   && (GameObjectPrefabType == PrefabAssetType.Model 
                   || GameObjectPrefabType == PrefabAssetType.Regular
                   || GameObjectPrefabType == PrefabAssetType.Variant);
#else
            PrefabType GameObjectPrefabType = PrefabUtility.GetPrefabType(gameObject);
            return GameObjectPrefabType == PrefabType.Prefab || GameObjectPrefabType == PrefabType.ModelPrefab;
#endif
        }

        /// <summary>
        ///     Gets the average of the three vectors.
        /// </summary>
        /// <param name="p1">The first point of triangle.</param>
        /// <param name="p2">The second point of triangle.</param>
        /// <param name="p3">The third point of triangle.</param>
        /// <returns>The center position of triangle.</returns>
        public static Vector3 GetAverage(Vector3 p1, Vector3 p2, Vector3 p3)
        {
            return (p1 + p2 + p3) / 3f;
        }

        /// <summary>
        ///     Calculates the checksum of an objects array.
        /// </summary>
        /// <param name="objects">The objects array.</param>
        /// <returns>The checksum of objects.</returns>
        [Pure]
        public static int GetChecksum([NotNull]UnityEngine.Object[] objects)
        {
            int Checksum = 0;

            for (int Index = 0; Index < objects.Length; Index++)
            {
                if (objects[Index] == null)
                {
                    continue;
                }

                Checksum += objects[Index].GetInstanceID();
            }

            return Checksum;
        }

        public static Shader GetBasicShader()
        {
#if UNITY_2019_1_OR_NEWER
            return UnityEngine.Rendering.GraphicsSettings.renderPipelineAsset.defaultShader;
#else
            return Shader.Find("Standard");
#endif
        }

        /// <summary>
        ///     Draws handles with specified offset, rotation, scale and color.
        /// </summary>
        /// <param name="drawHandlesDelegate">The draw action.</param>
        /// <param name="offset">The handles offset.</param>
        /// <param name="rotation">The handles rotation.</param>
        /// <param name="scale">The handles scale.</param>
        /// <param name="color">The handles color.</param>
        /// <exception cref="ArgumentNullException"><paramref name="drawHandlesDelegate"/> is <see langword="null"/></exception>
        private static void DrawHandle([NotNull]Action drawHandlesDelegate, Vector3 offset, Quaternion rotation, Vector3 scale, Color color)
        {
            if (drawHandlesDelegate == null)
            {
                throw new ArgumentNullException("drawHandlesDelegate");
            }

            Matrix4x4 InitialMatrix4X4 = Handles.matrix;
            Handles.matrix = Matrix4x4.TRS(offset, rotation, scale);

            Color InitialColor = Handles.color;
            Handles.color = color;

            try
            {
                drawHandlesDelegate();
            }
            catch (Exception E)
            {
                Debug.LogException(E);
            }

            Handles.color = InitialColor;
            Handles.matrix = InitialMatrix4X4;
        }
    }
}
