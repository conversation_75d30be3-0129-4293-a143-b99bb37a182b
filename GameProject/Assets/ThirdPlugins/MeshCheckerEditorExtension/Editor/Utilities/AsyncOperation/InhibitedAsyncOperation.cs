using System;
using JetBrains.Annotations;

using UnityEditor;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Async
{
    /// <summary>
    ///     Represents the async operation wrapper, that updates manually from the main thread.
    /// </summary>
    /// <typeparam name="T">The type of a result.</typeparam>
    public class InhibitedAsyncOperation<T>
    {
        /// <summary>
        ///     The delegate to use for handlers of the <see cref="InhibitedAsyncOperation{T}.OperationEndedEvent"/>.
        /// </summary>
        /// <param name="result">The result of the async operation.</param>
        /// <param name="state">The result state of the async operation.</param>
        public delegate void OperationEndedDelegate(T result, AsyncOperationResultState state);

        /// <summary>
        ///     The delegate to use for handlers of the <see cref="InhibitedAsyncOperation{T}.ProgressChangedEvent"/>.
        /// </summary>
        /// <param name="progress">The new progress of the async operation. The value stays between 0f and 1f, where 1f means that operation ended.</param>
        public delegate void ProgressChangedDelegate(float progress);

        /// <summary>
        ///     The inhibited async operation scope (references the normal async operation, that can be modified from the another threads)
        /// </summary>
        private readonly AsyncOperation<T> Scope;

        /// <summary>
        ///     The backing field of the <see cref="IsEnded"/> property.
        /// </summary>
        private bool InnerIsEnded;

        /// <summary>
        ///     The end state of an asynchronous operation.
        ///     <para>
        ///         Can be modified from another thread.
        ///     </para>
        /// </summary>
        private AsyncOperationResultState AsyncOperationResultState;

        /// <summary>
        ///     Occurs when the async operation ends.
        /// </summary>
        [PublicAPI]
        public event OperationEndedDelegate OperationEndedEvent;

        /// <summary>
        ///     Occurs when the progress of async operation changed.
        /// </summary>
        [PublicAPI]
        public event ProgressChangedDelegate ProgressChangedEvent;

        /// <summary>
        ///     Gets a value indicating whether the asynchronous operation ended.
        /// </summary>
        [PublicAPI]
        public bool IsEnded
        {
            get
            {
                return InnerIsEnded;
            }

            private set
            {
                if (InnerIsEnded != value)
                {
                    InnerIsEnded = value;

                    if (InnerIsEnded)
                    {
                        Result = InnerResult;
                        if (OperationEndedEvent != null)
                        {
                            OperationEndedEvent(Result, AsyncOperationResultState);
                        }
                    }

                    if (InnerIsEnded)
                    {
                        EditorApplication.update -= UpdateFromMainThread;
                    }
                }
            }
        }

        /// <summary>
        ///     Gets a value indicating whether the asynchronous operation was aborted.
        /// </summary>
        [PublicAPI]
        public bool IsAborted { get; private set; }

        /// <summary>
        ///     Gets the current progress. The value stays between 0f and 1f, where 1f = ended.
        /// </summary>
        [PublicAPI]
        public float Progress { get; private set; }

        /// <summary>
        ///     Gets the result of asynchronous operation.
        ///     <para>
        ///         The value modified from the main thread only.
        ///     </para>
        /// </summary>
        [PublicAPI]
        public T Result { get; private set; }

        /// <summary>
        ///     Gets or sets the result of the asynchronous operation.
        ///     <para>
        ///         Can be modified from another thread.
        ///     </para>
        /// Result of async operation (Can be modified from another thread)
        /// </summary>
        private T InnerResult { get; set; }

        /// <summary>
        ///     Initializes a new instance of the <see cref="InhibitedAsyncOperation{T}"/> class.
        /// </summary>
        /// <param name="asyncOperation">The async operation.</param>
        /// <exception cref="ArgumentNullException"><paramref name="asyncOperation"/> are <see langword="null" /></exception>
        [PublicAPI]
        public InhibitedAsyncOperation([NotNull]AsyncOperation<T> asyncOperation)
        {
            if (asyncOperation == null)
            {
                throw new ArgumentNullException("asyncOperation");
            }

            Scope = asyncOperation;
            Scope.OperationEndedCallback += ScopeOperationEndedCallback;

            EditorApplication.update += UpdateFromMainThread;

            Progress = Scope.Progress;
        }

        /// <summary>
        ///     Aborts the async operation.
        /// </summary>
        public void Abort()
        {
            if (IsEnded)
            {
                throw new InvalidOperationException("Cannot abort ended operation.");
            }

            Scope.Abort();

            AsyncOperationResultState = AsyncOperationResultState.Aborted;
            IsAborted = true;
            IsEnded = true;
        }

        /// <summary>
        ///     Updates inhibited async operation values from the main thread.
        /// </summary>
        private void UpdateFromMainThread()
        {
            if (Scope != null)
            {
                if (Progress != Scope.Progress)
                {
                    Progress = Scope.Progress;
                    if (ProgressChangedEvent != null)
                    {
                        ProgressChangedEvent(Progress);
                    }
                }

                IsEnded = Scope.IsEnded;
            }
        }

        /// <summary>
        ///     Handles the <see cref="AsyncOperation{T}.OperationEndedCallback"/> event.
        /// </summary>
        /// <param name="param">The result of the async operation.</param>
        /// <param name="resultState">The result state.</param>
        private void ScopeOperationEndedCallback(T param, AsyncOperationResultState resultState)
        {
            InnerResult = param;
            AsyncOperationResultState = resultState;
        }
    }
}