using JetBrains.Annotations;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension
{
    /// <summary>
    ///     Represents a selection manager for <see cref="AbstractMeshFilter"/> instances.
    /// </summary>
    /// <typeparam name="TObject">The type of selection objects.</typeparam>
    internal sealed class AbstractMeshFilterSelectionManager<TObject> : SelectionManager<TObject, AbstractMeshFilter>
        where TObject : class
    {
        #region Overrides of SelectionManager<TObject,AbstractMeshFilter>

        /// <inheritdoc />
        protected override void TryAddComponentToSelection(TObject selectedObject)
        {
            if (TryFindMeshFilter(selectedObject))
            {
                return;
            }

            TryFindSkinnedMeshRenderer(selectedObject);
        }

        /// <summary>
        ///     Tries to find a <see cref="MeshFilter"/> and add it to the SelectedComponentsDictionary.
        /// </summary>
        /// <param name="selectedObject">The newly selected object.</param>
        private bool TryFindMeshFilter([NotNull] TObject selectedObject)
        {
            MeshFilter FindedMeshFilter;

            if (SelectionManager<TObject, MeshFilter>.TryGetComponentFromGameObject(selectedObject, out FindedMeshFilter)
                || SelectionManager<TObject, MeshFilter>.TryGetComponentFromComponent(selectedObject, out FindedMeshFilter)
                || SelectionManager<TObject, MeshFilter>.TryGetTargetAsComponent(selectedObject, out FindedMeshFilter))
            {
                if (FindedMeshFilter != null)
                {
                    // Add to the SelectedComponentsDictionary dictionary
                    SelectedComponentsDictionary.Add(selectedObject, new MCMeshFilter(FindedMeshFilter));
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        ///     Tries to find a <see cref="MeshFilter"/> and add it to the SelectedComponentsDictionary.
        /// </summary>
        /// <param name="selectedObject">The newly selected object.</param>
        private bool TryFindSkinnedMeshRenderer([NotNull] TObject selectedObject)
        {
            SkinnedMeshRenderer FindedComponent;

            if (SelectionManager<TObject, SkinnedMeshRenderer>.TryGetComponentFromGameObject(selectedObject, out FindedComponent)
                || SelectionManager<TObject, SkinnedMeshRenderer>.TryGetComponentFromComponent(selectedObject, out FindedComponent)
                || SelectionManager<TObject, SkinnedMeshRenderer>.TryGetTargetAsComponent(selectedObject, out FindedComponent))
            {
                if (FindedComponent != null)
                {
                    // Add to the SelectedComponentsDictionary dictionary
                    SelectedComponentsDictionary.Add(selectedObject, new MCSkinnedMeshRenderer(FindedComponent));

                    return true;
                }
            }

            return false;
        }

        #endregion
    }
}