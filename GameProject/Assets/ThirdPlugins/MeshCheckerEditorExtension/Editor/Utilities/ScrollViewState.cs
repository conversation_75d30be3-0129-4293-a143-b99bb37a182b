using System;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    /// <summary>
    ///     Represents a state of a scroll view.
    /// </summary>
    internal sealed class ScrollViewState
    {
        /// <summary>
        ///     Gets the scroll position.
        /// </summary>
        public Vector2 ScrollPosition { get; private set; }

        /// <summary>
        ///     Gets or sets a minimal scroll position.
        /// </summary>
        public Vector2 MinValue { get; set; }

        /// <summary>
        ///     Gets or sets a maximal scroll position.
        /// </summary>
        public Vector2 MaxValue { get; set; }

        /// <summary>
        ///     Indicates whether the scroll action performed.
        /// </summary>
        private bool ScrollPerformed;

        /// <summary>
        ///     The start timestamp of a scroll action.
        /// </summary>
        private DateTime ScrollStartDateTime;

        /// <summary>
        ///     The target position.
        /// </summary>
        private Vector2 TargetPosition;

        /// <summary>
        ///     The initial position of scroll action.
        /// </summary>
        private Vector2 InitialPosition;

        /// <summary>
        ///     Rised when the scoll position changed.
        /// </summary>
        public event Action InertialScrollEvent;

        /// <summary>
        ///     Initializes a new instance of the <see cref="ScrollViewState"/> class.
        /// </summary>
        /// <param name="inertialScrollCallback">The inertial scroll callback. See <see cref="InertialScrollEvent"/></param>
        public ScrollViewState([CanBeNull]Action inertialScrollCallback = null)
        {
            InertialScrollEvent = inertialScrollCallback;
        }

        /// <summary>
        ///     Changes the target position of a scroll animation.
        /// </summary>
        /// <param name="scrollIncrement">The scroll position delta.</param>
        public void DoSmoothScroll(Vector2 scrollIncrement)
        {
            if (scrollIncrement == Vector2.zero)
            {
                return;
            }

            if (!ScrollPerformed)
            {
                TargetPosition = ScrollPosition;
            }

            TargetPosition += scrollIncrement;
            TargetPosition = ClampPosition(TargetPosition);

            InitialPosition = ScrollPosition;
            ScrollPerformed = true;
            ScrollStartDateTime = DateTime.Now;
        }

        /// <summary>
        ///     Gets the scroll position for current frame.
        /// </summary>
        /// <returns>The scroll positoin for current frame.</returns>
        public Vector2 GetSmoothScrollPosition()
        {
            if (!ScrollPerformed)
            {
                return ScrollPosition;
            }

            float SecondsSinceScrollStarted = (float)(DateTime.Now - ScrollStartDateTime).TotalSeconds;
            float LerpK = SecondsSinceScrollStarted / MCGUI.ScrollSmoothDuration;
            if (LerpK >= 1f)
            {
                ScrollPerformed = false;
                ScrollPosition = TargetPosition;
                return ScrollPosition;
            }

            ScrollPosition = Vector2.Lerp(InitialPosition, TargetPosition, LerpK);

            if (InertialScrollEvent != null)
            {
                try
                {
                    InertialScrollEvent();
                }
                catch (Exception E)
                {
                    Debug.LogException(E);
                }
            }

            return ScrollPosition;
        }

        /// <summary>
        ///     Sets the X coordinate of the scroll position.
        /// </summary>
        /// <param name="x">The new value.</param>
        /// <param name="stopAnimation">Specifies whether the animation should be stopped.</param>
        public void SetScrollPositionX(float x, bool stopAnimation = true)
        {
            ScrollPosition = ScrollPosition.ChangeX(x);
            if (stopAnimation)
            {
                ScrollPerformed = false;
            }
        }

        /// <summary>
        ///     Sets the Y coordinate of the scroll position.
        /// </summary>
        /// <param name="y">The new value.</param>
        /// <param name="stopAnimation">Specifies whether the animation should be stopped.</param>
        public void SetScrollPositionY(float y, bool stopAnimation = true)
        {
            ScrollPosition = ScrollPosition.ChangeY(y);
            if (stopAnimation)
            {
                ScrollPerformed = false;
            }
        }

        /// <summary>
        ///     Clamps the position by the <see cref="MinValue"/> and <see cref="MaxValue"/>.
        /// </summary>
        /// <param name="position">The position that should be clamped.</param>
        /// <returns>The clamped position.</returns>
        private Vector2 ClampPosition(Vector2 position)
        {
            if (position.x > MaxValue.x)
            {
                position.x = MaxValue.x;
            }

            if (position.x < MinValue.x)
            {
                position.x = MinValue.x;
            }

            if (position.y > MaxValue.y)
            {
                position.y = MaxValue.y;
            }

            if (position.y < MinValue.y)
            {
                position.y = MinValue.y;
            }

            return position;
        }
    }
}
