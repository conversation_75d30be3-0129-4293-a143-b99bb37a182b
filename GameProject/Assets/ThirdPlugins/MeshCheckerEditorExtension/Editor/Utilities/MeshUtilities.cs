using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Utilities
{
    /// <summary>
    ///     Provides utilities for a mesh.
    /// </summary>
    [PublicAPI]
    public static class MeshUtilities
    {
        /// <summary>
        ///     Gets an array of UV sets which stored in specified <paramref name="mesh"/>.
        /// </summary>
        /// <param name="mesh">The mesh.</param>
        /// <returns>The array of UV sets.</returns>
        [PublicAPI]
        [NotNull]
        public static Vector2[][] GetUVSets([NotNull]this Mesh mesh)
        {
            return new[]
            {
#if !UNITY_4_6 && !UNITY_4_7 // Analog for UNITY_5_0_OR_NEWER
                mesh.uv,
                mesh.uv2,
                mesh.uv3,
                mesh.uv4
#else
                mesh.uv,
                mesh.uv2
#endif
            };
        }
    }
}