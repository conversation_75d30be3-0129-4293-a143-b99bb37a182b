using System;
using System.Collections.Generic;
using System.Linq;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension
{
    /// <summary>
    ///     Represents an asset modification processor.
    /// </summary>
    internal class MeshCheckerAssetModificationProcessor : UnityEditor.AssetModificationProcessor
    {
        /// <summary>
        ///     Occurs before a scene is saved.
        /// </summary>
        public static event Action SceneSaveEvent = delegate { };

        /// <summary>
        ///     Occurs when a new prefab is about to be created.
        /// </summary>
        public static event Action WillCreatePrefabEvent = delegate { };

        /// <summary>
        ///     This is called by Unity when it is about to write serialized assets or scene files to disk.
        /// </summary>
        /// <param name="paths">Assets paths.</param>
        /// <returns>Processed assets paths.</returns>
        public static string[] OnWillSaveAssets(string[] paths)
        {
            if (PathsContains(".unity", paths))
            {
                try
                {
                    SceneSaveEvent();
                }
                catch (Exception E)
                {
                    Debug.LogException(E);
                }
            }

            return paths;
        }

        /// <summary>
        ///     This is called by Unity when it is about to create an asset not imported by the user, eg. ".meta" files.
        /// </summary>
        /// <param name="path">The asset path.</param>
        public static void OnWillCreateAsset([NotNull]string path)
        {
            if (path.Contains(".prefab"))
            {
                try
                {
                    WillCreatePrefabEvent();
                }
                catch (Exception E)
                {
                    Debug.LogException(E);
                }
            }
        }

        /// <summary>
        ///     Checks the paths for a fragment.
        /// </summary>
        /// <param name="fragment">The fragment.</param>
        /// <param name="paths">The collection of paths.</param>
        /// <returns><c>true</c> if one of paths contains the fragment; otherwise, <c>false</c>.</returns>
        private static bool PathsContains([NotNull] string fragment, [NotNull] IEnumerable<string> paths)
        {
            return paths.Any(iterationPath => iterationPath.Contains(fragment));
        }
    }
}