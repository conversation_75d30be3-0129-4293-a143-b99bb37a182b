using System;
using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    /// <summary>
    ///     Represents a popup windows with tool configuration GUI elements.
    /// </summary>
    internal sealed class ToolConfigurationPopup : PopupWindowContent
    {
        /// <summary>
        ///     A delegate which will be invoked when GUI elements should be drawn.
        /// </summary>
        private readonly Action<Rect> DrawDelegate;

        /// <summary>
        ///    Indicates whether the size is non constant. 
        /// </summary>
        private readonly bool SizeNonConstant;

        /// <summary>
        ///     The function for getting width of a window.
        /// </summary>
        private readonly Func<float> Width;

        /// <summary>
        ///     The function for getting height of a window.
        /// </summary>
        private readonly Func<float> Height;

        /// <summary>
        ///     The size of a window.
        /// </summary>
        private readonly Vector2 Size;

        /// <summary>
        ///     Initializes a new instance of the <see cref="ToolConfigurationPopup"/> class.
        /// </summary>
        /// <param name="size">The constant size</param>
        /// <param name="drawDelegate">The draw delegate.</param>
        /// <exception cref="ArgumentNullException"><paramref name="drawDelegate"/> is <see langword="null"/></exception>
        public ToolConfigurationPopup(Vector2 size, [NotNull]Action<Rect> drawDelegate)
        {
            if (drawDelegate == null)
            {
                throw new ArgumentNullException("drawDelegate");
            }

            DrawDelegate = drawDelegate;
            SizeNonConstant = false;
            Size = size;
        }

        /// <summary>
        ///     Initializes a new instance of the <see cref="ToolConfigurationPopup"/> class.
        /// </summary>
        /// <param name="drawDelegate">The draw delegate.</param>
        /// <param name="width">The function for getting width of a window.</param>
        /// <param name="height">The function for getting height of a window.</param>
        /// <exception cref="ArgumentNullException"><paramref name="drawDelegate"/>, <paramref name="width"/> or <paramref name="height"/> is <see langword="null"/></exception>
        public ToolConfigurationPopup([NotNull]Action<Rect> drawDelegate, [NotNull]Func<float> width, [NotNull]Func<float> height)
        {
            if (drawDelegate == null)
            {
                throw new ArgumentNullException("drawDelegate");
            }

            if (width == null)
            {
                throw new ArgumentNullException("width");
            }

            if (height == null)
            {
                throw new ArgumentNullException("height");
            }

            DrawDelegate = drawDelegate;

            SizeNonConstant = true;
            Width = width;
            Height = height;
        }

        /// <summary>
        ///     Callback for drawing GUI controls for the popup window.
        /// </summary>
        /// <param name="rect">The rectangle to draw the GUI inside.</param>
        /// <exception cref="ExitGUIException">Throwed when GUI should be ended.</exception>
        public override void OnGUI(Rect rect)
        {
            try
            {
                if (SizeNonConstant)
                {
                    float ResultWidth = Width == null ? 1 : Width();
                    float ResultHeight = Height == null ? 1 : Height();
                    editorWindow.minSize = new Vector2(ResultWidth, ResultHeight);
                    editorWindow.maxSize = new Vector2(ResultWidth, ResultHeight);
                }
                else
                {
                    if (Size != Vector2.zero)
                    {
                        editorWindow.maxSize = Size;
                        editorWindow.minSize = Size;
                    }
                }

                if (DrawDelegate != null)
                {
                    DrawDelegate(rect);
                }
            }
            catch (ExitGUIException)
            {
                throw;
            }
            catch (Exception E)
            {
                Debug.LogException(E);

                GUI.color = MCGUI.ErrorColor.Current;
                GUILayout.Label("Failed to draw.", EditorStyles.boldLabel);
                GUI.color = Color.white;
            }
        }
    }
}
