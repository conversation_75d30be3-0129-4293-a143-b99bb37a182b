using System;
using System.Collections.Generic;
using System.Linq;

using HightlanderSolutions.MeshCheckerEditorExtension.Checker;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework;
using HightlanderSolutions.MeshCheckerEditorExtension.ObjectBounds;
using HightlanderSolutions.MeshCheckerEditorExtension.PolygonsCount;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities.IssuesHandlers;

using JetBrains.Annotations;

using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension
{
    /// <summary>
    ///     Represents a main window of the MeshChecker editor extension.
    /// </summary>
    internal sealed class MeshCheckerWindow : EditorWindow
    {
        /// <summary>
        ///     Represents a type of tools.
        /// </summary>
        private enum ToolType
        {
            /// <summary>
            ///     Literally nothing.
            /// </summary>
            Nothing,

            /// <summary>
            ///     The tool that helps to get sizes of selection and draw their bounds.
            /// </summary>
            SizeAndBounds,

            /// <summary>
            ///     The tool that applies a procedural or custom checker texture to the selection.
            /// </summary>
            Checker,

            /// <summary>
            ///     The tool that measure a count of polygons and vertices.
            /// </summary>
            PolygonsCount,

            /// <summary>
            ///     The tool that provides analysis of selected meshes.
            /// </summary>
            Analysis
        }

        #region Implementation of static members

        /// <summary>
        ///     The title of a window.
        /// </summary>
        private const string WindowTitle = "MeshChecker";

        /// <summary>
        ///     Indicates whether the window is disabled.
        /// </summary>
        private static bool IsDisabled = true;

        /// <summary>
        ///     The backing field of the <see cref="Instance"/> property.
        /// </summary>
        private static MeshCheckerWindow InnerInstance;

        /// <summary>
        ///     Gets or sets an instance of <see cref="MeshCheckerWindow"/>.
        /// </summary>
        [NotNull]
        private static MeshCheckerWindow Instance
        {
            get
            {
                if (InnerInstance == null && !IsDisabled)
                {
                    InnerInstance = GetWindow<MeshCheckerWindow>();
                }

                return InnerInstance;
            }

            set
            {
                InnerInstance = value;
            }
        }

        /// <summary>
        ///     Creates new instance of window.
        /// </summary>
        [MenuItem("Window/Mesh Checker")]
        public static void InitializeWindow()
        {
            Instance = GetWindow<MeshCheckerWindow>();
            IsDisabled = false;
        }

        #endregion

        /// <summary>
        ///     The backing field of the <see cref="CurrentTool"/> property.
        /// </summary>
        private ToolType InnerCurrentTool;

        /// <summary>
        ///     Gets or sets a current tool.
        /// </summary>
        private ToolType CurrentTool
        {
            get
            {
                return InnerCurrentTool;
            }

            set
            {
                if (InnerCurrentTool != value)
                {
                    TransformsSelectionManager.ClearSelection();
                    ObjectsSelectionManager.ClearSelection();
                    ScrollViewStateInstance = new ScrollViewState(Repaint);
                    InnerCurrentTool = value;
                    LastSelectionChecksum = 0;
                }
            }
        }

        /// <summary>
        ///     The selection grid items of toolbar.
        /// </summary>
        private List<SelectionGridItem<ToolType>> SelectionGridItems;

        /// <summary>
        ///     Indicates whether window waits for scene view repaint.
        /// </summary>
        private bool WaitForRepaintSceneView;

        /// <summary>
        ///     The backing field of the <see cref="CheckerManager"/> property.
        /// </summary>
        private CheckerManager InnerCheckerManager;

        /// <summary>
        ///     Gets an instance of <see cref="Checker.CheckerManager"/>.
        /// </summary>
        [NotNull]
        private CheckerManager CheckerManager
        {
            get
            {
                return InnerCheckerManager ?? (InnerCheckerManager = new CheckerManager());
            }
        }

        /// <summary>
        ///     The backing field of the <see cref="TransformsSelectionManager"/> property.
        /// </summary>
        private SelectionManager<Transform, Renderer> InnerTransformsSelectionManager;

        /// <summary>
        ///     Gets a manager of <see cref="Transform"/>-<see cref="Renderer"/> selection pair.
        /// </summary>
        [NotNull]
        private SelectionManager<Transform, Renderer> TransformsSelectionManager
        {
            get
            {
                if (InnerTransformsSelectionManager == null)
                {
                    InnerTransformsSelectionManager = new SelectionManager<Transform, Renderer>();
                    InnerTransformsSelectionManager.ObjectsSelectedEvent += TransformsSelectedEventHandler;
                    InnerTransformsSelectionManager.ObjectsDeselectedEvent += TransformsDeselectedEventHandler;
                }

                return InnerTransformsSelectionManager;
            }
        }

        /// <summary>
        ///     The backing field of the <see cref="ObjectsSelectionManager"/> property.
        /// </summary>
        private AbstractMeshFilterSelectionManager<GameObject> InnerObjectsSelectionManager;

        /// <summary>
        ///     Gets a manager of <see cref="GameObject"/>-<see cref="AbstractMeshFilter"/> selection pair.
        /// </summary>
        [NotNull]
        private AbstractMeshFilterSelectionManager<GameObject> ObjectsSelectionManager
        {
            get
            {
                if (InnerObjectsSelectionManager == null)
                {
                    InnerObjectsSelectionManager = new AbstractMeshFilterSelectionManager<GameObject>();
                    InnerObjectsSelectionManager.ObjectsSelectedEvent += ObjectsSelectedEventHandler;
                    InnerObjectsSelectionManager.ObjectsDeselectedEvent += ObjectsDeselectedEventHandler;
                }

                return InnerObjectsSelectionManager;
            }
        }

        /// <summary>
        ///     The scroll view state.
        /// </summary>
        [NotNull]
        private ScrollViewState ScrollViewStateInstance = new ScrollViewState();

        /// <summary>
        ///     The backing field of the <see cref="BoundsManager"/> property.
        /// </summary>
        private BoundsManager InnerBoundsManager;

        /// <summary>
        ///     Gets or sets the manager of bounds.
        /// </summary>
        [NotNull]
        private BoundsManager BoundsManager
        {
            get
            {
                if (InnerBoundsManager == null)
                {
                    ReinitializeBoundsManager();
                }

                return InnerBoundsManager;
            }

            set
            {
                InnerBoundsManager = value;
            }
        }

        private BoundsPanelDrawer BoundsPanelDrawer;

        /// <summary>
        ///     The expandable container which contains utilities for selected tool.
        /// </summary>
        private ExpandableContainer ToolUtilitiesContainer;

        /// <summary>
        ///     The polygon measures manager.
        /// </summary>
        private PolygonsMeasurer PolygonsMeasurer;

        /// <summary>
        ///     The manager of mesh analyses.
        /// </summary>
        private MeshAnalysesManager MeshAnalysesManager;

        /// <summary>
        ///     Indicates whether the window should ignore an update invocation.
        /// </summary>
        private bool IgnoreUpdate;

        /// <summary>
        ///     The previous checksum of selected <see cref="GameObject"/>s. Used in detection of selection changing.
        /// </summary>
        private int LastSelectionChecksum;

        #region Implementation of EditorWindow methods

        /// <summary>
        ///     Updates the window.
        /// </summary>
        [UsedImplicitly]
        private void Update()
        {
            if (IgnoreUpdate)
            {
                return;
            }

            bool SelectionChanged = false;

            switch (CurrentTool)
            {
                case ToolType.Checker:
                    if (CheckSelectionChanged())
                    {
                        SelectionChanged = TransformsSelectionManager.CheckAndChangeSelection(Selection.GetTransforms(SelectionMode.Deep));
                    }

                    break;

                case ToolType.SizeAndBounds:
                    if (CheckSelectionChanged())
                    {
                        const SelectionMode SelectionMask = SelectionMode.Deep | SelectionMode.ExcludePrefab;
                        UnityEngine.Object[] FilteredSelection = Selection.GetFiltered(typeof(GameObject), SelectionMask);
                        SelectionChanged = ObjectsSelectionManager.CheckAndChangeSelection(MCUtilities.TransformSelectionToGameObjects(FilteredSelection));
                    }

                    break;

                case ToolType.PolygonsCount:
                case ToolType.Analysis:
                    if (CheckSelectionChanged())
                    {
                        UnityEngine.Object[] FilteredSelection = Selection.GetFiltered(typeof(GameObject), SelectionMode.Deep);
                        SelectionChanged = ObjectsSelectionManager.CheckAndChangeSelection(MCUtilities.TransformSelectionToGameObjects(FilteredSelection));
                    }

                    break;
            }

            if (SelectionChanged)
            {
                Repaint();
            }
        }

        /// <summary>
        ///     Checks the selection changed.
        ///     <para>
        ///         Calculate checksum for selected <see cref="GameObject"/>s and compare with previous.
        ///     </para>
        /// </summary>
        /// <param name="updateChecksum">Specifies whether <see cref="LastSelectionChecksum"/> should be updated.</param>
        /// <returns>
        ///     <c>true</c> if selection changed; otherwise, <c>false</c>.
        /// </returns>
        private bool CheckSelectionChanged(bool updateChecksum = true)
        {
            int SelectionChecksum = MCUtilities.GetChecksum(Selection.gameObjects);
            bool Changed = LastSelectionChecksum != SelectionChecksum;
            if (Changed && updateChecksum)
            {
                LastSelectionChecksum = SelectionChecksum;
            }

            return Changed;
        }

        /// <summary>
        ///     Destroys the window.
        /// </summary>
        [UsedImplicitly]
        private void OnDestroy()
        {
            IsDisabled = true;
        }

        /// <summary>
        ///     Enables the window.
        /// </summary>
        [UsedImplicitly]
        private void OnEnable()
        {
            PackageImportIssues.PerformCheck();

            minSize = new Vector2(280, 128);
#if !UNITY_4_6 && !UNITY_4_7 && !UNITY_5_0 // Analog for UNITY_5_1_OR_NEWER
            titleContent = new GUIContent(WindowTitle, MCGUI.Icons.LogoIconSmall.Current);
#else
            title = WindowTitle;
#endif
            wantsMouseMove = true;

            ScrollViewStateInstance = new ScrollViewState(Repaint);
            ToolUtilitiesContainer = new ExpandableContainer(Repaint);

            IsDisabled = false;
            MeshCheckerAssetModificationProcessor.SceneSaveEvent += SceneSaveEventHandler;
            MeshCheckerAssetModificationProcessor.WillCreatePrefabEvent += WillCreatePrefabEventHandler;
#if UNITY_2018_1_OR_NEWER
            EditorApplication.playModeStateChanged += PlaymodeStateChangedEventHandler;
#else
            EditorApplication.playmodeStateChanged += PlaymodeStateChangedEventHandler;
#endif
#if UNITY_2019_1_OR_NEWER
            SceneView.duringSceneGui += OnSceneGUIEventHandler;
#else
            SceneView.onSceneGUIDelegate += OnSceneGUIEventHandler;
#endif

            ReinitializeBoundsManager();
            PolygonsMeasurer = new PolygonsMeasurer(Repaint);
            MeshAnalysesManager = new MeshAnalysesManager();

            // Subscribe to preference changed event
            MCPreferences.PreferencesChangedEvent += PreferencesChangedEventHandler;
            MCPreferences.Subscribe(PreferenceType.BoundsType, BoundsTypeChangedEventHandler);

            MCGUI.ReloadStylesEvent += ReloadStylesEventHandler;

            IgnoreUpdate = false;
        }

        /// <summary>
        ///     Generates selection grid items for tabs if not generated yet.
        /// </summary>
        private void GenerateSelectionGridItemsIfNeeded()
        {
            if (SelectionGridItems == null)
            {
                const int ToolButtonWidth = 40;
                const int ToolButtonHeight = 40;

                SelectionGridItem<ToolType> BoundsItem = new SelectionGridItem<ToolType>(
                    new GUIContent(MCGUI.Icons.BoundsIcon.Current, "Sizes and bounds"),
                    ToolType.SizeAndBounds,
                    ToolButtonWidth,
                    ToolButtonHeight,
                    MCGUI.Styles.LargeButtonLeft);

                SelectionGridItem<ToolType> PolygonsCountItem = new SelectionGridItem<ToolType>(
                    new GUIContent(MCGUI.Icons.PolygonIcon.Current, "Polygons count"),
                    ToolType.PolygonsCount,
                    ToolButtonWidth + 4,
                    ToolButtonHeight,
                    MCGUI.Styles.LargeButtonMiddle);

                SelectionGridItem<ToolType> CheckerItem = new SelectionGridItem<ToolType>(
                    new GUIContent(MCGUI.Icons.CheckerIcon.Current, "UV Checker"),
                    ToolType.Checker,
                    ToolButtonWidth,
                    ToolButtonHeight,
                    MCGUI.Styles.LargeButtonMiddle);

                SelectionGridItem<ToolType> AnalysisItem = new SelectionGridItem<ToolType>(
                    new GUIContent(MCGUI.Icons.AnalysisIcon.Current, "Analyses"),
                    ToolType.Analysis,
                    ToolButtonWidth,
                    ToolButtonHeight,
                    MCGUI.Styles.LargeButtonRight);

                SelectionGridItems = new List<SelectionGridItem<ToolType>>
                {
                    BoundsItem,
                    PolygonsCountItem,
                    CheckerItem,
                    AnalysisItem
                };
            }
        }

        /// <summary>
        ///     Disables the window.
        /// </summary>
        [UsedImplicitly]
        private void OnDisable()
        {
            IgnoreUpdate = true;

            IsDisabled = true;

            ScrollViewStateInstance = null;
            ToolUtilitiesContainer = null;
            SelectionGridItems = null;
            LastSelectionChecksum = 0;
            TransformsSelectionManager.ClearSelection();
            ObjectsSelectionManager.ClearSelection();
            CheckerManager.Cleanup();
            MeshCheckerAssetModificationProcessor.SceneSaveEvent -= SceneSaveEventHandler;
            MeshCheckerAssetModificationProcessor.WillCreatePrefabEvent -= WillCreatePrefabEventHandler;
#if UNITY_2018_1_OR_NEWER
            EditorApplication.playModeStateChanged -= PlaymodeStateChangedEventHandler;
#else
            EditorApplication.playmodeStateChanged -= PlaymodeStateChangedEventHandler;
#endif
#if UNITY_2019_1_OR_NEWER
            SceneView.duringSceneGui -= OnSceneGUIEventHandler;
#else
            SceneView.onSceneGUIDelegate -= OnSceneGUIEventHandler;
#endif
            BoundsManager.Release();

            if (PolygonsMeasurer != null)
            {
                PolygonsMeasurer.Clear();
            }

            MeshAnalysesManager = null;

            // Unsubscribe from preference changed event
            MCPreferences.PreferencesChangedEvent -= PreferencesChangedEventHandler;
            MCPreferences.Unsubscribe(PreferenceType.BoundsType, BoundsTypeChangedEventHandler);

            MCGUI.ReloadStylesEvent -= ReloadStylesEventHandler;

            MCGUI.Cleanup();
        }

        /// <summary>
        ///     Updates the inspector.
        /// </summary>
        [UsedImplicitly]
        private void OnInspectorUpdate()
        {
            if (MCPreferences.BoundsTypeSetting == BoundsType.Local)
            {
                if (Selection.activeTransform != null)
                {
                    BoundsManager.UsedRotation = Selection.activeTransform.rotation;
                }
            }

            // Update inhibited async operation from main thread
            BoundsManager.Update();
        }

        private Rect ToolbarRect = new Rect(0, 0, 0, 0);

        /// <summary>
        ///     Draws a GUI of the window.
        /// </summary>
        /// <exception cref="InvalidOperationException">Method should be invoked in OnGUI scope.</exception>
        [UsedImplicitly]
        private void OnGUI()
        {
            if (Event.current == null)
            {
                throw new InvalidOperationException("Method should be invoked in OnGUI scope.");
            }

            GUI.Box(new Rect(0, 0, position.width, position.height), string.Empty, "AppToolbar");

            PackageImportIssues.DrawMessagesLayout();

            GenerateSelectionGridItemsIfNeeded();

            using (new GUILayout.HorizontalScope(GUILayout.Width(position.width + 1)))
            {
                CurrentTool = MCGUI.DrawSelectionGrid(SelectionGridItems, CurrentTool, ToolType.Nothing);
            }

            if (Event.current.type == EventType.Repaint)
            {
                Rect PreviousToolbarRect = ToolbarRect;
                ToolbarRect = GUILayoutUtility.GetLastRect();

                if (PreviousToolbarRect != ToolbarRect)
                {
                    Repaint();
                }
            }

            if (CurrentTool == ToolType.SizeAndBounds)
            {
                Rect TestOptionsRect = new Rect(position.width - 80, ToolbarRect.yMax - 23, 80, 20);
                using (new GUILayout.AreaScope(TestOptionsRect))
                {
                    using (new GUILayout.HorizontalScope())
                    {
                        BoundsPanelDrawer.DrawUtilitiesGridLayout();
                    }
                }
            }

            float VerticalOffset = 44 + ToolUtilitiesContainer.CurrentHeight;

            Rect OtherZoneRect = new Rect(
                0,
                ToolbarRect.yMax + ToolUtilitiesContainer.CurrentHeight,
                position.width,
                position.height - VerticalOffset);

            Rect ToolUtilitiesContainerRect = new Rect(0, ToolbarRect.yMax - 1, position.width, 0);

            if (CurrentTool == ToolType.SizeAndBounds && BoundsPanelDrawer != null)
            {
                ToolUtilitiesContainerRect.height = BoundsPanelDrawer.GetUtilitiesZoneHeight();
                ToolUtilitiesContainer.Draw(ToolUtilitiesContainerRect, BoundsPanelDrawer.DrawUtilitiesZoneContent);
            }
            else
            {
                ToolUtilitiesContainer.Draw(ToolUtilitiesContainerRect, null);
            }

            Rect BackgroundBoxRect = new Rect(
                OtherZoneRect.x - 1,
                OtherZoneRect.y,
                OtherZoneRect.width + 2,
                OtherZoneRect.height + 2);

            GUI.Box(BackgroundBoxRect, string.Empty, MCGUI.Styles.WindowBackground);

            if (CurrentTool == ToolType.SizeAndBounds && BoundsPanelDrawer != null)
            {
                using (new GUI.ClipScope(OtherZoneRect))
                {
                    BoundsPanelDrawer.Draw(new Rect(0, 0, OtherZoneRect.width, OtherZoneRect.height));
                }
            }

            if (CurrentTool == ToolType.PolygonsCount && PolygonsMeasurer != null)
            {
                using (new GUI.ClipScope(OtherZoneRect))
                {
                    PolygonsMeasurer.Draw(new Rect(0, 0, OtherZoneRect.width, OtherZoneRect.height));
                }
            }

            if (CurrentTool == ToolType.Checker)
            {
                DrawUVCheckerPanel(OtherZoneRect);
            }

            if (CurrentTool == ToolType.Analysis)
            {
                DrawAnalysisPanel(OtherZoneRect);
            }

            if (Event.current.type == EventType.Repaint
                && WaitForRepaintSceneView)
            {
                RepaintSceneView();
            }
        }

        /// <summary>
        ///     Repaints a scene view.
        /// </summary>
        private void RepaintSceneView()
        {
            SceneView.RepaintAll();

            WaitForRepaintSceneView = false;
        }

        #endregion

        #region Handlers

        /// <summary>
        ///     The handler of an OnSceneViewGUI event.
        /// </summary>
        /// <param name="sceneView">The repainted scene view.</param>
        /// <exception cref="ArgumentNullException"><paramref name="sceneView"/> is <see langword="null"/></exception>
        private void OnSceneGUIEventHandler([NotNull]SceneView sceneView)
        {
            if (sceneView == null)
            {
                throw new ArgumentNullException("sceneView");
            }

            if (BoundsManager.IsBoundsAreActual)
            {
                Bounds TotalBounds = BoundsManager.GetAlignedBounds();

                if (MCPreferences.DrawBoundingBoxSetting)
                {
                    MCUtilities.DrawHandleWireCube(
                        TotalBounds.center,
                        BoundsManager.UsedRotation,
                        TotalBounds.size,
                        MCPreferences.BoundingBoxColorSetting);
                }

                if (MCPreferences.DrawAxisSetting)
                {
                    // Draw axis on scene
                    MCUtilities.DrawHandleAllAxis(TotalBounds.size, TotalBounds.center, BoundsManager.UsedRotation);

                    // Draw sizes on scene
                    Vector3[] EndPoints = MCUtilities.GetAxisEndPoints(
                        TotalBounds.size,
                        TotalBounds.center,
                        BoundsManager.UsedRotation);

                    if (SceneView.currentDrawingSceneView != null)
                    {
                        MCUtilities.DrawSizeHandleLabel(EndPoints[0], Handles.xAxisColor, TotalBounds.size.x, "X", MCGUI.Styles.SceneAxisLabelX);
                        MCUtilities.DrawSizeHandleLabel(EndPoints[1], Handles.yAxisColor, TotalBounds.size.y, "Y", MCGUI.Styles.SceneAxisLabelY);
                        MCUtilities.DrawSizeHandleLabel(EndPoints[2], Handles.zAxisColor, TotalBounds.size.z, "Z", MCGUI.Styles.SceneAxisLabelZ);
                    }
                }

                if (MCPreferences.DrawBoundingBoxCenterSetting)
                {
                    Handles.color = MCPreferences.BoundingBoxColorSetting;

#if UNITY_5_6_OR_NEWER
                    Handles.SphereHandleCap(0, TotalBounds.center, Quaternion.identity, HandleUtility.GetHandleSize(TotalBounds.center) * 0.2f, Event.current.type);
#else
                    Handles.SphereCap(0, TotalBounds.center, Quaternion.identity, HandleUtility.GetHandleSize(TotalBounds.center) * 0.2f);
#endif
                }
            }
        }

        /// <summary>
        ///     Handles a scene save event.
        /// </summary>
        private void SceneSaveEventHandler()
        {
            CleanupTools();
        }

        /// <summary>
        ///     Handles a prefab creation event.
        /// </summary>
        private void WillCreatePrefabEventHandler()
        {
            CleanupTools();
        }

        /// <summary>
        ///     Handles a preference change event.
        /// </summary>
        private void PreferencesChangedEventHandler()
        {
            WaitForRepaintSceneView = true;
        }

        /// <summary>
        ///     Handles a bounds type change event.
        /// </summary>
        private void BoundsTypeChangedEventHandler()
        {
            ReinitializeBoundsManager();
        }

#if UNITY_2018_1_OR_NEWER
        /// <summary>
        ///     Handles a playmode state change event.
        /// </summary>
        /// <param name="stateChange">Enumeration specifying a change in the Editor's play mode state.</param>
        private void PlaymodeStateChangedEventHandler(PlayModeStateChange stateChange)
#else
        /// <summary>
        ///     Handles a playmode state change event.
        /// </summary>
        private void PlaymodeStateChangedEventHandler()
#endif
        {
            // If playmode starting begin
            if (EditorApplication.isPlayingOrWillChangePlaymode && !EditorApplication.isPlaying)
            {
                IgnoreUpdate = true;
                CleanupTools();
            }

            // If playmode starting ends
            if (EditorApplication.isPlayingOrWillChangePlaymode && EditorApplication.isPlaying && !EditorApplication.isPaused)
            {
                IgnoreUpdate = false;
            }

            // If playmode stopping begin
            if (!EditorApplication.isPlayingOrWillChangePlaymode && EditorApplication.isPlaying)
            {
                IgnoreUpdate = true;
                CleanupTools();
            }

            // If playmode stopping ends
            if (!EditorApplication.isPlayingOrWillChangePlaymode && !EditorApplication.isPlaying)
            {
                IgnoreUpdate = false;
            }
        }

        /// <summary>
        ///     Handles a transforms selection event.
        /// </summary>
        /// <param name="transforms">The selected transforms list.</param>
        /// <exception cref="ArgumentNullException"><paramref name="transforms"/> is <see langword="null"/></exception>
        private void TransformsSelectedEventHandler([NotNull]List<Transform> transforms)
        {
            if (transforms == null)
            {
                throw new ArgumentNullException("transforms");
            }

            if (CurrentTool == ToolType.Checker)
            {
                // If need to set checker texture automaticaly
                if (MCPreferences.AutoSetCheckerSetting)
                {
                    SetCheckerMaterialTo(transforms);
                }
            }
        }

        /// <summary>
        ///     Handles a transforms deselection event.
        /// </summary>
        /// <param name="transforms">The deselected transforms list.</param>
        /// <exception cref="ArgumentNullException"><paramref name="transforms"/> is <see langword="null"/></exception>
        private void TransformsDeselectedEventHandler([NotNull] List<Transform> transforms)
        {
            if (transforms == null)
            {
                throw new ArgumentNullException("transforms");
            }

            if (CurrentTool == ToolType.Checker)
            {
                List<Renderer> SelectedRenderers = SelectionManager<Transform, Renderer>.GetComponentsFromObjects(transforms);

                if (SelectedRenderers.Count > 0)
                {
                    // Revert materials on deselected objects with renderers
                    CheckerManager.RevertMaterialsFromSnapshot(SelectedRenderers);
                }
            }
        }

        /// <summary>
        ///     Handles an objects selection event.
        /// </summary>
        /// <param name="objects">The selected GameObjects list.</param>
        /// <exception cref="ArgumentNullException"><paramref name="objects"/> is <see langword="null"/></exception>
        private void ObjectsSelectedEventHandler([NotNull] List<GameObject> objects)
        {
            if (objects == null)
            {
                throw new ArgumentNullException("objects");
            }

            switch (CurrentTool)
            {
                case ToolType.SizeAndBounds:
                    TryUpdateLocalBoundsManagerRotation();
                    BoundsManager.ChangeObservedMeshes(ObjectsSelectionManager.SelectedComponents);
                    break;

                case ToolType.PolygonsCount:
                    PolygonsMeasurer.AddObjectsToMeasure(ObjectsSelectionManager.GetPairsByKeys(objects));
                    break;

                case ToolType.Analysis:
                    MeshAnalysesManager.ChangeObservedMeshFilters(ObjectsSelectionManager.SelectedComponents, Repaint);
                    break;
            }

            Repaint();
        }

        /// <summary>
        ///     Handles an object deselection event.
        /// </summary>
        /// <param name="objects">The deselected GameObjects list.</param>
        /// <exception cref="ArgumentNullException"><paramref name="objects"/> is <see langword="null"/></exception>
        private void ObjectsDeselectedEventHandler([NotNull] List<GameObject> objects)
        {
            if (objects == null)
            {
                throw new ArgumentNullException("objects");
            }

            switch (CurrentTool)
            {
                case ToolType.SizeAndBounds:
                    TryUpdateLocalBoundsManagerRotation();
                    BoundsManager.ChangeObservedMeshes(ObjectsSelectionManager.SelectedComponents);
                    break;

                case ToolType.PolygonsCount:
                    PolygonsMeasurer.RemoveObjectsFromMeasures(objects);
                    break;

                case ToolType.Analysis:
                    MeshAnalysesManager.ChangeObservedMeshFilters(ObjectsSelectionManager.SelectedComponents, Repaint);
                    break;
            }

            Repaint();
        }

        /// <summary>
        ///     <para>Reload used styles</para>
        ///     <para>Handler of the <see cref="MCGUI.ReloadStylesEvent"/>.</para>
        /// </summary>
        private void ReloadStylesEventHandler()
        {
            SelectionGridItems = null;
            Repaint();
        }

        #endregion

        /// <summary>
        ///     Cleans all tools data
        /// </summary>
        private void CleanupTools()
        {
            TransformsSelectionManager.ClearSelection();
            ObjectsSelectionManager.ClearSelection();
            CheckerManager.Cleanup();
            MeshAnalysesManager.Cleanup();
            LastSelectionChecksum = 0;
        }

        /// <summary>
        ///     Sets a checker material to targets.
        /// </summary>
        /// <param name="targets">The collection of targets.</param>
        /// <exception cref="ArgumentNullException"><paramref name="targets"/> is <see langword="null"/></exception>
        private void SetCheckerMaterialTo([NotNull] List<Transform> targets)
        {
            if (targets == null)
            {
                throw new ArgumentNullException("targets", "[MeshChecker editor extension] Called SetCheckerMaterialTo method with null list. Please, send info about this <NAME_EMAIL>");
            }

            List<Renderer> SelectedRenderers = SelectionManager<Transform, Renderer>.GetComponentsFromObjects(targets);
            SelectedRenderers.RemoveAll(renderer => MCUtilities.IsPrefab(renderer.gameObject));

            if (SelectedRenderers.Count > 0)
            {
                // Set checker material to newly selected objects with renderers
                CheckerManager.SetCheckerMaterial(SelectedRenderers);
            }
        }

        /// <summary>
        ///     Tries to update the rotation of a <see cref="LocalBoundsManager"/>.
        /// </summary>
        private void TryUpdateLocalBoundsManagerRotation()
        {
            if (MCPreferences.BoundsTypeSetting == BoundsType.Local)
            {
                if (Selection.activeTransform != null)
                {
                    BoundsManager.UsedRotation = Selection.activeTransform.rotation;
                }
            }
        }

        #region GUI methods

        /// <summary>
        ///     Draws the UV checker panel.
        /// </summary>
        /// <param name="panelPosition">The position where panel should be drawn.</param>
        private void DrawUVCheckerPanel(Rect panelPosition)
        {
            float CheckerMaterialSettingsHeight = CheckerMaterial.GetHeight();
            Rect CheckerMaterialSettingsRect = new Rect(panelPosition.x, panelPosition.y, panelPosition.width, CheckerMaterialSettingsHeight);

            // Draw CheckerMaterial settings
            CheckerMaterial.DrawSettings(CheckerMaterialSettingsRect);

            // Create Rect for second (Layout) zone.
            Rect LayoutRect = new Rect(
                5,
                CheckerMaterialSettingsRect.yMax + 5,
                CheckerMaterialSettingsRect.width - 10,
                panelPosition.height - CheckerMaterialSettingsHeight - 10);

            // Draw second (Layout) zone.
            using (new GUILayout.AreaScope(LayoutRect))
            {
                // "Set Checker Auto" Toggle
                MCPreferences.AutoSetCheckerSetting = GUILayout.Toggle(MCPreferences.AutoSetCheckerSetting, "Set checker auto");

                // "Set Checker" Button
                GUI.enabled = TransformsSelectionManager.IsComponentExistInSelection;
                if (GUILayout.Button("Set Checker"))
                {
                    // Apply checker material to selection
                    SetCheckerMaterialTo(TransformsSelectionManager.SelectedObjects.ToList());
                }

                // "Remove Checker" Button
                GUI.enabled = TransformsSelectionManager.IsComponentExistInSelection && CheckerManager.CheckerTextureApplied;

                if (GUILayout.Button("Remove Checker"))
                {
                    CheckerManager.RevertMaterialsFromSnapshot(TransformsSelectionManager.SelectedComponents);
                }

                GUI.enabled = true;

                if (!TransformsSelectionManager.IsComponentExistInSelection)
                {
                    GUILayout.FlexibleSpace();

                    string Caption;

                    if (Selection.gameObjects.Length > 0)
                    {
                        Caption = "No objects with a Renderer component selected.\n\n"
                            + "<size=14>(Renderer can be attached to any child)</size>";
                    }
                    else
                    {
                        Caption = "To start using Checker tool simply select some objects on scene and press 'Set Checker' button!";
                    }

                    using (new MCGUI.ColorScope(0.65f))
                    {
                        GUILayout.Label(Caption, MCGUI.Styles.ToolEmptyLabel);
                    }

                    GUILayout.FlexibleSpace();
                }
            }
        }

        /// <summary>
        ///     Draws the analysis panel.
        /// </summary>
        /// <param name="panelPosition">The position where panel should be drawn.</param>
        private void DrawAnalysisPanel(Rect panelPosition)
        {
            if (MeshAnalysesManager.CurrentMeshAnalysesGroupsList.Count == 0)
            {
                Rect InfoPosition = new RectOffset(10, 10, 5, 1).Remove(panelPosition);
                string Caption;

                if (Selection.gameObjects.Length > 0)
                {
                    Caption = "No objects with a MeshFilter or a SkinnedMeshRenderer component selected.\n\n"
                        + "<size=14>(Component can be attached to any child)</size>";
                }
                else
                {
                    Caption = "To start using Mesh Analysis tool simply select some objects on scene or prefabs in the project window!";
                }

                using (new MCGUI.ColorScope(0.65f))
                {
                    GUI.Label(InfoPosition, Caption, MCGUI.Styles.ToolEmptyLabel);
                }

                return;
            }

            // Calculate InnerZoneHeight without vertical scrollbar
            float InnerZoneHeight = MeshAnalysesManager.CurrentMeshAnalysesGroupsList.Sum(check => check.GetHeight(panelPosition.width)) +
                                       (MeshAnalysesManager.CurrentMeshAnalysesGroupsList.Count * 3);

            bool ScrollbarExists = InnerZoneHeight > panelPosition.height;

            float ElementWidth = ScrollbarExists ? panelPosition.width - 15 : panelPosition.width;

            // Recalculcate InnerZoneHeight if vertical scrollbar exists, because width changed
            InnerZoneHeight = MeshAnalysesManager.CurrentMeshAnalysesGroupsList.Sum(check => check.GetHeight(ElementWidth)) +
                                       (MeshAnalysesManager.CurrentMeshAnalysesGroupsList.Count * 3);

            Rect ScrollViewInnerRect = new Rect(0, 0, ElementWidth, InnerZoneHeight);
            Rect ScrollbarRect = new Rect(position.width - MCGUI.ScrollbarSize, panelPosition.y, MCGUI.ScrollbarSize, panelPosition.height);

            MCGUI.BeginScrollView(panelPosition, ScrollViewStateInstance, ScrollViewInnerRect, ScrollbarExists ? ScrollbarRect : (Rect?)null);
            float YMin = 1;
            float VisibleYMin = ScrollViewStateInstance.ScrollPosition.y;
            float VisibleYMax = VisibleYMin + panelPosition.height;

            foreach (MeshAnalysesGroup AnalysesGroup in MeshAnalysesManager.CurrentMeshAnalysesGroupsList)
            {
                float Height = AnalysesGroup.GetHeight(ElementWidth);

                // Do not draw if not visible.
                float YMax = YMin + Height + 3;
                if (YMax < VisibleYMin
                    || YMin >= VisibleYMax)
                {
                    YMin = YMax;
                    continue;
                }

                Rect ElementRect = new Rect(0, YMin, ElementWidth, Height);
                AnalysesGroup.Draw(ElementRect);

                YMin += Height + 1;

                Rect SeparatorRect = new Rect(0, YMin, ElementWidth, 1);

                MCGUI.DrawSeparatorLine(SeparatorRect);

                YMin += 2;
            }

            MCGUI.EndScrollView();
        }

        #endregion

        /// <summary>
        ///     Reinitializes a bounds manager.
        /// </summary>
        private void ReinitializeBoundsManager()
        {
            if (InnerBoundsManager != null)
            {
                BoundsManager.Release();
            }

            if (MCPreferences.BoundsTypeSetting == BoundsType.World)
            {
                BoundsManager = new WorldBoundsManager();
            }
            else
            {
                BoundsManager = new LocalBoundsManager();
                TryUpdateLocalBoundsManagerRotation();
            }

            BoundsManager.BoundsCalculcationCompletedMainThreadEvent += Repaint;
            BoundsManager.ProgressChangedEvent += progress => Repaint();

            BoundsManager.ChangeObservedMeshes(ObjectsSelectionManager.SelectedComponents);
            BoundsPanelDrawer = new BoundsPanelDrawer(BoundsManager, Repaint);
        }
    }
}