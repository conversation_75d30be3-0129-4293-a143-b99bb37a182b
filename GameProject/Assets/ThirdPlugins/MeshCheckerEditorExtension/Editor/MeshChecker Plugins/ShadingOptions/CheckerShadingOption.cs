using System;
using HightlanderSolutions.MeshCheckerEditorExtension.Checker;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.ShadingOptions
{
    /// <summary>
    ///     Represents a shading option which helps to check the UV mapping.
    /// </summary>
    [Alias("ShadingOptions.Checker")]
    [MeshAnalysisShading]
    internal sealed class CheckerShadingOption : MeshAnalysisShadingOption
    {
        /// <summary>
        ///     Id of shading option
        /// </summary>
        private readonly Guid Id = new Guid();

        /// <summary>
        ///     The description drawer.
        /// </summary>
        private readonly TextShadingOptionDescription<CheckerShadingOption> Description =
            new TextShadingOptionDescription<CheckerShadingOption>(
                new Vector2(400, 80),
                "This mode applies a checker texture to a mesh. It helps detecting flaws in texture maps.");

        /// <summary>
        ///     The instance of checker material.
        /// </summary>
        private Material CheckerMaterialInstance;

        /// <summary>
        ///     The array of materials applied to submeshes.
        /// </summary>
        private Material[] Materials;

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get { return "Checker"; }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get { return 0; }
        }

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
            CheckerMaterialInstance = CheckerMaterial.Get(Id);
            Materials = new Material[mesh.subMeshCount];
            for (int Index = 0; Index < mesh.subMeshCount; Index++)
            {
                Materials[Index] = CheckerMaterialInstance;
            }
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
            Materials = null;

            CheckerMaterialInstance = null;
            CheckerMaterial.Release(Id);
        }

        /// <inheritdoc />
        public override Material[] GetMaterials()
        {
            return Materials;
        }

        /// <inheritdoc />
        public override bool GetHaveSettings()
        {
            return true;
        }

        /// <inheritdoc />
        public override float GetSettingsWidth()
        {
            return 300;
        }

        /// <inheritdoc />
        public override float GetSettingsHeight()
        {
            return CheckerMaterial.GetHeight();
        }

        /// <inheritdoc />
        public override void DrawSettings(Rect position)
        {
            CheckerMaterial.DrawSettings(position, MeshAnalysisWindow.RepaintWindow);
        }

        /// <inheritdoc />
        public override bool HasDescription()
        {
            return true;
        }

        /// <inheritdoc />
        public override Vector2 GetDescriptionWindowSize()
        {
            return Description.Size;
        }

        /// <inheritdoc />
        public override void DrawDescription(Rect position)
        {
            Description.Draw(position);
        }

        #endregion
    }
}
