using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface;

using UnityEditor;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.ShadingOptions
{
    /// <summary>
    ///     Represents a shading option which displays normals.
    /// </summary>
    [Alias(Alias)]
    [MeshAnalysisShading]
    public sealed class NormalsShadingOption : BasicShaderShadingOption
    {
        /// <summary>
        ///     The height of a space between two settings lines.
        /// </summary>
        private const float Space = 3;

        /// <summary>
        ///     The alias of the shading option.
        /// </summary>
        private const string Alias = "ShadingOptions.Normals";

        /// <summary>
        ///     The key of the wireframe toggle state.
        /// </summary>
        private const string WirframePropertyKey = "MC.ShadingOptions.Normals.Wireframe";

        /// <summary>
        ///     The padding of a settings window.
        /// </summary>
        private readonly RectOffset SettingsPadding = new RectOffset(5, 5, 5, 5);

        /// <summary>
        ///     The description drawer.
        /// </summary>
        private readonly TextShadingOptionDescription<NormalsShadingOption> Description =
            new TextShadingOptionDescription<NormalsShadingOption>(
                new Vector2(400, 120),
                "This option displays normals of a surface and is suitable to detect edge hardness errors and wrong normals directions. These normals are utilized in a various lighting procedures and other effects.");

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get
            {
                return "Normal";
            }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get
            {
                return 1000;
            }
        }

        /// <inheritdoc />
        public override bool Wireframe
        {
            get
            {
                return EditorPrefs.GetBool(WirframePropertyKey, false);
            }

            protected set
            {
                EditorPrefs.SetBool(WirframePropertyKey, value);
            }
        }

        /// <inheritdoc />
        public override bool GetHaveSettings()
        {
            return true;
        }

        /// <inheritdoc />
        public override float GetSettingsHeight()
        {
            return (EditorGUIUtility.singleLineHeight * 3) + 10 + (Space * 2);
        }

        /// <inheritdoc />
        public override void DrawSettings(Rect position)
        {
            position = SettingsPadding.Remove(position);
            Rect WireframeTogglePosition = new Rect(position)
            {
                height = EditorGUIUtility.singleLineHeight
            };

            Rect NormalMapPosition = new Rect(WireframeTogglePosition);
            NormalMapPosition.y += EditorGUIUtility.singleLineHeight + Space;

            Rect NormalScalePosition = new Rect(NormalMapPosition);
            NormalScalePosition.y += EditorGUIUtility.singleLineHeight + Space;

            EditorGUI.BeginChangeCheck();

            // Wireframe flag
            Wireframe = EditorGUI.Toggle(WireframeTogglePosition, "Wireframe", Wireframe);

            // Normal Map
            Texture NormalMap = (Texture)EditorGUI.ObjectField(
                NormalMapPosition,
                new GUIContent("Normal Map"),
                ShadingMaterial.GetTexture("_BumpMap"),
                typeof(Texture),
                false);
            ShadingMaterial.SetTexture("_BumpMap", NormalMap);

            if (NormalMap == null)
            {
                GUI.enabled = false;
            }

            // Normal Scale
            float NormalScale = EditorGUI.Slider(
                NormalScalePosition,
                new GUIContent("Normal Scale"),
                ShadingMaterial.GetFloat("_BumpScale"),
                0,
                1);
            ShadingMaterial.SetFloat("_BumpScale", NormalScale);

            GUI.enabled = true;

            if (EditorGUI.EndChangeCheck())
            {
                MeshAnalysisWindow.RepaintWindow();
            }
        }

        /// <inheritdoc />
        public override bool HasDescription()
        {
            return true;
        }

        /// <inheritdoc />
        public override Vector2 GetDescriptionWindowSize()
        {
            return Description.Size;
        }

        /// <inheritdoc />
        public override void DrawDescription(Rect position)
        {
            Description.Draw(position);
        }

        #endregion

        #region Overrides of BasicShaderShadingOption

        /// <inheritdoc />
        protected override string ShaderName
        {
            get
            {
                return "Hidden/MeshChecker/NormalShading";
            }
        }

        #endregion
    }
}
