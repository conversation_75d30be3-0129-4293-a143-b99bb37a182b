using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.ShadingOptions
{
    /// <summary>
    ///     Represents a shading option which provides a possibility to apply custom materials to submeshes.
    /// </summary>
    [MeshAnalysisShading]
    public sealed class CustomMaterialsShadingOption : MeshAnalysisShadingOption
    {
        /// <summary>
        ///     The description drawer.
        /// </summary>
        private readonly TextShadingOptionDescription<CustomMaterialsShadingOption> Description =
            new TextShadingOptionDescription<CustomMaterialsShadingOption>(
                new Vector2(400, 80),
                "In this mode custom materials are used to display the model.");

        /// <summary>
        ///     The array of materials applied to submeshes.
        /// </summary>
        private Material[] Materials;

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get { return "Custom Materials"; }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get { return 1100; }
        }

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
            Materials = new Material[mesh.subMeshCount];
            Shader FoundShader = MCUtilities.GetBasicShader();

            for (int Index = 0; Index < Materials.Length; Index++)
            {
                // Materials[Index] = AssetDatabase.GetBuiltinExtraResource<Material>("Default-Diffuse.mat");
                Materials[Index] = new Material(FoundShader);
            }
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
            Materials = null;
        }

        /// <inheritdoc />
        public override Material[] GetMaterials()
        {
            return Materials;
        }

        /// <inheritdoc />
        public override bool GetHaveSettings()
        {
            return true;
        }

        /// <inheritdoc />
        public override float GetSettingsWidth()
        {
            return 300;
        }

        /// <inheritdoc />
        public override float GetSettingsHeight()
        {
            if (Materials != null)
            {
                return (Materials.Length * 18) + 10;
            }

            return 0;
        }

        /// <inheritdoc />
        public override void DrawSettings(Rect position)
        {
            position = new RectOffset(5, 5, 5, 5).Remove(position);

            bool MaterialsChanged = false;
            GUILayout.BeginArea(position);

            for (int Index = 0; Index < Materials.Length; Index++)
            {
                EditorGUI.BeginChangeCheck();
                string FieldCaption = string.Concat("Submesh ", Index);
                Materials[Index] = EditorGUILayout.ObjectField(FieldCaption, Materials[Index], typeof(Material), false) as Material;
                if (EditorGUI.EndChangeCheck())
                {
                    MaterialsChanged = true;
                }
            }

            GUILayout.EndArea();

            if (MaterialsChanged)
            {
                MeshAnalysisWindow.RepaintWindow();
            }
        }

        /// <inheritdoc />
        public override bool HasDescription()
        {
            return true;
        }

        /// <inheritdoc />
        public override Vector2 GetDescriptionWindowSize()
        {
            return Description.Size;
        }

        /// <inheritdoc />
        public override void DrawDescription(Rect position)
        {
            Description.Draw(position);
        }

        #endregion
    }
}
