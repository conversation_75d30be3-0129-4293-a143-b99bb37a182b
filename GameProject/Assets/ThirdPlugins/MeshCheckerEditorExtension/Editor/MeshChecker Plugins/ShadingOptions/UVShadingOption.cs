using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.ShadingOptions
{
    /// <summary>
    ///     Represents a shading option which displays UV coordinates.
    /// </summary>
    [Alias("ShadingOptions.UVCoordinates")]
    [MeshAnalysisShading]
    public sealed class UVShadingOption : BasicShaderShadingOption
    {
        /// <summary>
        ///     The description drawer.
        /// </summary>
        private CustomShadingOptionDescription<UVShadingOption> Description;

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get
            {
                return "UV";
            }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get
            {
                return 1003;
            }
        }

        /// <inheritdoc />
        public override bool HasDescription()
        {
            return true;
        }

        /// <inheritdoc />
        public override Vector2 GetDescriptionWindowSize()
        {
            return Description.Size;
        }

        /// <inheritdoc />
        public override void DrawDescription(Rect position)
        {
            Description.Draw(position);
        }

        #endregion

        #region Overrides of BasicShaderShadingOption

        /// <inheritdoc />
        protected override string ShaderName
        {
            get
            {
                return "Hidden/MeshChecker/UVShading";
            }
        }

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
            base.Initialize(mesh);

            Description = new CustomShadingOptionDescription<UVShadingOption>(new Vector2(400, 240), DescriptionDrawer);
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
            base.Deinitialize();

            Description = null;
        }

        #endregion

        /// <summary>
        ///     The drawer method for description content drawing.
        /// </summary>
        /// <param name="position">The description content position.</param>
        private void DescriptionDrawer(Rect position)
        {
            using (new GUILayout.AreaScope(position))
            {
                const string DescriptionString = "This shading option colors each point of a mesh depending on its UV coordinates. Black – (0, 0); Red – (1, 0); Green – (0, 1); Yellow – (1, 1) \n It helps to identify errors in UV sets.";
                GUILayout.Label(DescriptionString, MCGUI.Styles.RobotoLabel);

                GUILayout.Space(10);

                Graphics.DrawTexture(GUILayoutUtility.GetRect(100, 100, 100, 100), Texture2D.whiteTexture, ShadingMaterial);

                GUILayout.FlexibleSpace();
            }
        }
    }
}
