using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.ShadingOptions
{
    /// <summary>
    ///     Represents a shading option which displays binormals.
    /// </summary>
    [Alias("ShadingOptions.Binormal")]
    [MeshAnalysisShading]
    public sealed class BinormalShadingOption : BasicShaderShadingOption
    {
        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get
            {
                return "Binormal";
            }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get
            {
                return 1001;
            }
        }

        #endregion

        #region Overrides of BasicShaderShadingOption

        /// <inheritdoc />
        protected override string ShaderName
        {
            get
            {
                return "Hidden/MeshChecker/BinormalShading";
            }
        }

        /// <inheritdoc />
        public override bool HasDescription()
        {
            return true;
        }

        /// <inheritdoc />
        public override Vector2 GetDescriptionWindowSize()
        {
            return Description.Size;
        }

        /// <inheritdoc />
        public override void DrawDescription(Rect position)
        {
            Description.Draw(position);
        }

        #endregion

        /// <summary>
        ///     The description drawer.
        /// </summary>
        private readonly TextShadingOptionDescription<BinormalShadingOption> Description =
            new TextShadingOptionDescription<BinormalShadingOption>(
                new Vector2(400, 120), 
                "This option displays surface tangents and suitable to detect edge hardness errors and wrong tangents directions. Tangents are used in lighting shaders and other effects.");
    }
}
