using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.ShadingOptions
{
    /// <summary>
    ///     Represents a shading option which displays tangents.
    /// </summary>
    [Alias("ShadingOptions.Tangent")]
    [MeshAnalysisShading]
    public sealed class TangentShadingOption : BasicShaderShadingOption
    {
        /// <summary>
        ///     The description drawer.
        /// </summary>
        private readonly TextShadingOptionDescription<TangentShadingOption> Description =
            new TextShadingOptionDescription<TangentShadingOption>(
                new Vector2(400, 120),
                "This option displays surface tangents. Tangents are used in lighting shaders and other effects. This option is suitable to detect edge hardness errors and wrong tangents directions.");

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get
            {
                return "Tangent";
            }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get
            {
                return 1002;
            }
        }

        /// <inheritdoc />
        public override bool HasDescription()
        {
            return true;
        }

        /// <inheritdoc />
        public override Vector2 GetDescriptionWindowSize()
        {
            return Description.Size;
        }

        /// <inheritdoc />
        public override void DrawDescription(Rect position)
        {
            Description.Draw(position);
        }

        #endregion

        #region Overrides of BasicShaderShadingOption

        /// <inheritdoc />
        protected override string ShaderName
        {
            get
            {
                return "Hidden/MeshChecker/TangentShading";
            }
        }

        #endregion
    }
}
