using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;
using UnityEditor;
using UnityEngine;
using Random = UnityEngine.Random;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.ShadingOptions
{
    /// <summary>
    ///     Represents a shading option which displays submeshes with custom colors.
    /// </summary>
    [Alias("ShadingOptions.SubmeshColor")]
    [MeshAnalysisShading]
    public sealed class SubmeshColorShadingOption : MeshAnalysisShadingOption
    {
        /// <summary>
        ///     The description drawer.
        /// </summary>
        private readonly TextShadingOptionDescription<SubmeshColorShadingOption> Description =
            new TextShadingOptionDescription<SubmeshColorShadingOption>(
                new Vector2(400, 140),
                "This shading option makes every submesh to have its own color. Typically submeshes are grouped based on their materials. Colors are pregenerated but can be changed in the Settings. \nThis mode is useful for detecting mesh grouping errors.");

        /// <summary>
        ///     The array of materials applied to submeshes.
        /// </summary>
        private Material[] Materials;

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get
            {
                return "Random submesh color";
            }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get
            {
                return 1102;
            }
        }

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
            Materials = new Material[mesh.subMeshCount];

            for (int Index = 0; Index < Materials.Length; Index++)
            {
                Materials[Index] = new Material(MCUtilities.GetBasicShader())
                {
                    color = new Color(Random.Range(0f, 1f), Random.Range(0f, 1f), Random.Range(0f, 1f))
                };
            }
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
            foreach (Material Material in Materials)
            {
                Object.DestroyImmediate(Material);
            }

            Materials = null;
        }

        /// <inheritdoc />
        public override Material[] GetMaterials()
        {
            return Materials;
        }

        /// <inheritdoc />
        public override bool GetHaveSettings()
        {
            return true;
        }

        /// <inheritdoc />
        public override float GetSettingsWidth()
        {
            return 300;
        }

        /// <inheritdoc />
        public override float GetSettingsHeight()
        {
            if (Materials != null)
            {
                return (Materials.Length * 18) + 10;
            }

            return 0;
        }

        /// <inheritdoc />
        public override void DrawSettings(Rect position)
        {
            position = new RectOffset(5, 5, 5, 5).Remove(position);

            bool ColorChanged = false;
            GUILayout.BeginArea(position);

            for (int Index = 0; Index < Materials.Length; Index++)
            {
                EditorGUI.BeginChangeCheck();

                string FieldName = string.Concat("Submesh ", Index);
                Materials[Index].color = EditorGUILayout.ColorField(FieldName, Materials[Index].color);

                if (EditorGUI.EndChangeCheck())
                {
                    ColorChanged = true;
                }
            }

            GUILayout.EndArea();

            if (ColorChanged)
            {
                MeshAnalysisWindow.RepaintWindow();
            }
        }

        /// <inheritdoc />
        public override bool HasDescription()
        {
            return true;
        }

        /// <inheritdoc />
        public override Vector2 GetDescriptionWindowSize()
        {
            return Description.Size;
        }

        /// <inheritdoc />
        public override void DrawDescription(Rect position)
        {
            Description.Draw(position);
        }

        #endregion
    }
}
