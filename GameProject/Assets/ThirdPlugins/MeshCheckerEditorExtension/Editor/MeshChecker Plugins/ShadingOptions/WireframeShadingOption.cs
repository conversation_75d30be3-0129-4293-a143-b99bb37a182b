using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface;
using UnityEditor;
using UnityEngine;

namespace MeshCheckerEditorExtension.Editor.Plugins.ShadingOptions
{
    /// <summary>
    ///     Represents a shading option which displays mesh as wireframe.
    /// </summary>
    /// <remarks>
    ///     This shading option uses internal Unity's shader "Hidden/Internal-Colored".
    /// </remarks>
    [Alias("ShadingOptions.Wireframe")]
    [MeshAnalysisShading]
    public class WireframeShadingOption : BasicShaderShadingOption
    {
        /// <summary>
        ///     The description drawer.
        /// </summary>
        private readonly TextShadingOptionDescription<WireframeShadingOption> Description =
            new TextShadingOptionDescription<WireframeShadingOption>(
                new Vector2(400, 80),
                "Wireframe is a simple shading option that lets you view objects as a wire mesh");

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get
            {
                return "Wireframe";
            }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get
            {
                return 1;
            }
        }

        /// <inheritdoc />
        public override bool HasDescription()
        {
            return true;
        }

        /// <inheritdoc />
        public override Vector2 GetDescriptionWindowSize()
        {
            return Description.Size;
        }

        /// <inheritdoc />
        public override void DrawDescription(Rect position)
        {
            Description.Draw(position);
        }

        #endregion

        #region Overrides of BasicShaderShadingOption

        /// <inheritdoc />
        protected override string ShaderName
        {
            get
            {
#if UNITY_5_0
                return "Hidden/MeshChecker/Colored";
#else
                return "Hidden/Internal-Colored";
#endif
            }
        }

        /// <inheritdoc />
        protected override void ConfigureMaterial(Material material)
        {
            material.hideFlags = HideFlags.HideAndDontSave;
            material.SetColor("_Color", EditorGUIUtility.isProSkin ? new Color(0.9f, 0.9f, 0.9f, 0.9f) : new Color(0, 0, 0, 0.9f));
            material.SetInt("_ZWrite", 0);
            material.SetFloat("_ZBias", -1.0f);
            material.SetInt("_SrcBlend", 5);
            material.SetInt("_DstBlend", 10);

            // Mesh should be drawn in wireframe mode.
            Wireframe = true;
        }

        #endregion
    }
}