using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.ShadingOptions
{
    /// <summary>
    ///     Represents a shading option which displays vertices color.
    /// </summary>
    [Alias("ShadingOptions.VertexColor")]
    [MeshAnalysisShading]
    public sealed class VertexColorShadingOption : BasicShaderShadingOption
    {
        /// <summary>
        ///     The description drawer.
        /// </summary>
        private readonly TextShadingOptionDescription<VertexColorShadingOption> Description =
            new TextShadingOptionDescription<VertexColorShadingOption>(
                new Vector2(400, 100),
                "This option makes color of each vertex prominent. It might be useful to detect if a model has incorrect vertex colors.");

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get
            {
                return "Vertex Color";
            }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get
            {
                return 1006;
            }
        }

        /// <inheritdoc />
        public override bool HasDescription()
        {
            return true;
        }

        /// <inheritdoc />
        public override Vector2 GetDescriptionWindowSize()
        {
            return Description.Size;
        }

        /// <inheritdoc />
        public override void DrawDescription(Rect position)
        {
            Description.Draw(position);
        }

        #endregion

        #region Overrides of BasicShaderShadingOption

        /// <inheritdoc />
        protected override string ShaderName
        {
            get
            {
                return "Hidden/MeshChecker/VertexColorShading";
            }
        }

        #endregion
    }
}
