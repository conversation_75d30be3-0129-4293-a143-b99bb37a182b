using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.ShadingOptions
{
    /// <summary>
    ///     Represents a shading option which displays inverted normals.
    /// </summary>
    [Alias("ShadingOptions.InvertedNormals")]
    [MeshAnalysisShading]
    public sealed class InvertedNormalsShadingOption : BasicShaderShadingOption
    {
        /// <summary>
        ///     The description drawer.
        /// </summary>
        private readonly CustomShadingOptionDescription<InvertedNormalsShadingOption> Description =
            new CustomShadingOptionDescription<InvertedNormalsShadingOption>(
                new Vector2(400, 250),
                DescriptionDrawer);

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get { return "Inverted Normals"; }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get { return 1005; }
        }

        /// <inheritdoc />
        public override bool HasDescription()
        {
            return true;
        }

        /// <inheritdoc />
        public override Vector2 GetDescriptionWindowSize()
        {
            return Description.Size;
        }

        /// <inheritdoc />
        public override void DrawDescription(Rect position)
        {
            Description.Draw(position);
        }

        #endregion

        #region Overrides of BasicShaderShadingOption

        /// <inheritdoc />
        protected override string ShaderName
        {
            get
            {
                return "Hidden/MeshChecker/InvertedNormals";
            }
        }

        #endregion

        /// <summary>
        ///     The drawer method for description content drawing.
        /// </summary>
        /// <param name="position">The description content position.</param>
        private static void DescriptionDrawer(Rect position)
        {
            using (new GUILayout.AreaScope(position))
            {
                const string DescriptionString = "This option highlights surfaces which normals are approximately codirectional to a view direction. This may indicate that an error in vertex normals, triangle normals or an edge hardness error takes place.";
                GUILayout.Label(DescriptionString, MCGUI.Styles.RobotoLabel);

                GUILayout.Space(10);

                using (new MCGUI.HorizontalLayoutScope())
                {
                    using (new MCGUI.ColorScope(MCGUI.ErrorColor.Current))
                    {
                        GUILayout.Label(string.Empty, MCGUI.Styles.ColoredVerticalLine, GUILayout.Width(6));
                    }

                    GUIContent RedCaption = new GUIContent("If you see the red color - then an angle between normal and view vector are less than 45°.\n"
                                                           + "Probably the normals are inverted.");
                    GUILayout.Label(RedCaption, MCGUI.Styles.RobotoLabel);
                }

                GUILayout.Space(10);

                using (new MCGUI.HorizontalLayoutScope())
                {
                    using (new MCGUI.ColorScope(new Color(0.943f, 0.943f, 0.943f)))
                    {
                        GUILayout.Label(string.Empty, MCGUI.Styles.ColoredVerticalLine, GUILayout.Width(6));
                    }

                    GUIContent WhiteCaption = new GUIContent("If you see the white color - then an angle between normal and view vector are greater than 45°.");
                    GUILayout.Label(WhiteCaption, MCGUI.Styles.RobotoLabel);
                }

                GUILayout.FlexibleSpace();
            }
        }
    }
}
