using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.Plugins.MeshAnalysis;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;

using JetBrains.Annotations;

using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.Tools
{
    /// <summary>
    ///     Represents a tool which helps to find triangles with coincident UV mapping.
    /// </summary>
    [Alias("Tools.CoincidentUVs")]
    [MeshAnalysisTool("Coincident UVs", 100, KeyCode.Q)]
    public sealed class CoincidentUVTool : MeshAnalysisTool
    {
        /// <summary>
        ///     Represents a UV sets enumeration.
        /// </summary>
        private enum UVSet
        {
            /// <summary>
            ///     First UV set.
            /// </summary>
            UV,

            /// <summary>
            ///     Second UV set.
            /// </summary>
            UV2,

            /// <summary>
            ///     Third UV set.
            /// </summary>
            UV3,

            /// <summary>
            ///     Fourth UV set.
            /// </summary>
            UV4
        }

        /// <summary>
        ///     The used analysis for target mesh.
        /// </summary>
        private CoincidentUVAnalysis CoincidentUVAnalysis;

        /// <summary>
        ///     The polygon drawing type selection grid.
        /// </summary>
        private PolygonDrawingTypeToolbar PolygonDrawingTypeToolbar;

        /// <summary>
        ///     The collection of polygons that have coincident UVs in vertices.
        /// </summary>
        private List<CoincidentUVAnalysis.CoincidentUVPolygon> CoincidentUVPolygons;

        /// <summary>
        ///     Indices of polygon vertices that have coincident UVs.
        /// </summary>
        private int[] BadVertices;

        /// <summary>
        ///     The positions on the screen of polygons vertices that have coincident UVs.
        /// </summary>
        private Vector3[] BadPolygonsScreenPosition;

        /// <summary>
        ///     The count of polygons that have coincident UVs.
        /// </summary>
        private int BadPolygonsCount;

        /// <summary>
        ///     The count of vertices that have coincident UVs.
        /// </summary>
        private int BadVerticesCount;

        /// <summary>
        ///     Failed triangles loop for cycling from UI.
        /// </summary>
        private IndexLoop Loop = new IndexLoop();

        /// <summary>
        ///     Indicates whether all triangles should be shown.
        /// </summary>
        private bool ShowAllTriangles = true;

        /// <summary>
        ///     Vertices of a target mesh.
        /// </summary>
        private Vector3[] MeshVertices;

        /// <summary>
        ///     Normals of a target mesh.
        /// </summary>
        private Vector3[] MeshNormals;

        /// <summary>
        ///     UV sets of a target mesh.
        /// </summary>
        private Vector2[][] MeshUV;

        /// <summary>
        ///     The collection of selection grid items for changing observed UV set.
        /// </summary>
        private List<SelectionGridItem<UVSet>> UVSelectionGridItems;

        /// <summary>
        ///     The currently observed UV set.
        /// </summary>
        private UVSet ObservedUVSet;

        /// <summary>
        ///     The index of the currently hovered vertex.
        /// </summary>
        private int HoveredVertex = -1;

        /// <summary>
        ///     Gets or sets the threshold. 
        ///     Only UV coordinates with a distance between them greater than the threshold successfully pass the analysis.
        /// </summary>
        [PublicAPI]
        public float UVThreshold { get; set; }

        /// <summary>
        ///     Gets a value indicating whether the tool are ready to draw failed polygons.
        /// </summary>
        private bool ReadyToDrawBads
        {
            get
            {
                return CoincidentUVAnalysis != null
                       && CoincidentUVAnalysis.Ended
                       && CoincidentUVAnalysis.Result == MeshAnalysisResultType.Error;
            }
        }

        /// <summary>
        ///     The target mesh.
        /// </summary>
        private Mesh Mesh;

        /// <summary>
        ///     The collection of points which used to draw a polyline from the infobox to hovered vertex.
        /// </summary>
        private readonly OverlayLine PointerLine = new OverlayLine();

        /// <summary>
        ///     The color of unselected triangles.
        /// </summary>
        private readonly Color UnselectedTrianglesColor = new Color(0.94f, 0.75f, 0.1f);

        #region Overrides of MeshAnalysisTool

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
            Mesh = mesh;
            MeshVertices = mesh.vertices;
            MeshNormals = mesh.normals;
            MeshUV = mesh.GetUVSets();

            StartNewCoincidentUVAnalysis(UVThreshold);

            PolygonDrawingTypeToolbar = new PolygonDrawingTypeToolbar(PolygonsDrawingType.Wireframe)
            {
                AllowDeselect = true
            };
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
            BadVertices = null;
            BadPolygonsScreenPosition = null;
            BadPolygonsCount = 0;
            BadVerticesCount = 0;

            PolygonDrawingTypeToolbar = null;

            Loop.Release();
            Loop = null;
        }

        /// <inheritdoc />
        public override void DrawToolbar()
        {
            bool BadTrianglesExists = BadPolygonsCount > 0;
            GUI.enabled = BadTrianglesExists;

            PolygonDrawingTypeToolbar.Draw();

            GUILayout.Space(5);

            ShowAllTriangles = GUILayout.Toggle(ShowAllTriangles, "Show all", EditorStyles.toolbarButton);

            MCGUI.DrawLoopToolbar(Loop, BadTrianglesExists);
        }

        /// <inheritdoc />
        public override void DrawOverlay(Rect position)
        {
            if (PolygonDrawingTypeToolbar.Current != PolygonsDrawingType.DontDraw
                && ReadyToDrawBads)
            {
                for (int Index = 0; Index < BadVerticesCount; Index++)
                {
                    BadPolygonsScreenPosition[Index] = MeshAnalysisWindow.PreviewToOverlayPoint(MeshVertices[BadVertices[Index]]);
                }

                Handles.color = OverlayUtilities.RedHandleColor.Dark;

                // Draw all triangles except selected one if needed
                for (int PolygonIndex = 0; PolygonIndex < BadPolygonsCount; PolygonIndex++)
                {
                    if (!ShowAllTriangles
                        && Loop.Selected
                        && Loop.Current != PolygonIndex)
                    {
                        continue;
                    }

                    int FirstVertex = PolygonIndex * 3;
                    switch (PolygonDrawingTypeToolbar.Current)
                    {
                        case PolygonsDrawingType.Solid:
                            OverlayUtilities.DrawAATriangleSolid(
                                BadPolygonsScreenPosition[FirstVertex + 0],
                                BadPolygonsScreenPosition[FirstVertex + 1],
                                BadPolygonsScreenPosition[FirstVertex + 2]);
                            break;

                        case PolygonsDrawingType.Wireframe:
                            if (Loop.Current != PolygonIndex)
                            {
                                OverlayUtilities.DrawTriangleWireframe(
                                    BadPolygonsScreenPosition[FirstVertex + 0],
                                    BadPolygonsScreenPosition[FirstVertex + 1],
                                    BadPolygonsScreenPosition[FirstVertex + 2],
                                    2);
                            }

                            break;

                        case PolygonsDrawingType.DontDraw:
                            break;
                    }
                }

                // Draw selected triangle
                if (Loop.Selected)
                {
                    int FirstVertex = Loop.Current * 3;
                    switch (PolygonDrawingTypeToolbar.Current)
                    {
                        case PolygonsDrawingType.Solid:
                            Handles.color = UnselectedTrianglesColor;
                            OverlayUtilities.DrawTriangleWireframe(
                                BadPolygonsScreenPosition[FirstVertex + 0],
                                BadPolygonsScreenPosition[FirstVertex + 1],
                                BadPolygonsScreenPosition[FirstVertex + 2],
                                3);
                            break;

                        case PolygonsDrawingType.Wireframe:
                            Handles.color = UnselectedTrianglesColor;
                            OverlayUtilities.DrawTriangleWireframe(
                                BadPolygonsScreenPosition[FirstVertex + 0],
                                BadPolygonsScreenPosition[FirstVertex + 1],
                                BadPolygonsScreenPosition[FirstVertex + 2],
                                3,
                                true);
                            break;
                    }
                }
            }

            if (PointerLine.HasPoints)
            {
                OverlayUtilities.DrawPointerLine(PointerLine);
            }
        }

        /// <inheritdoc />
        public override void DrawOverlayGUI(Rect position)
        {
            if (Loop.NotSelected)
            {
                return;
            }

            DrawPolygonInfoWindow(position);
        }

        #endregion

        /// <summary>
        ///     Draws a polygon info window which displays vertex UV coordinates in UV sets.
        /// </summary>
        /// <param name="position">The position of the window.</param>
        private void DrawPolygonInfoWindow(Rect position)
        {
            float WindowAlpha = EditorGUIUtility.isProSkin ? 0.9f : 0.7f;
            const float Width = 200;

            using (new MCGUI.ColorScope(WindowAlpha))
            {
                Rect HeaderBackgroundRect = new Rect(position.x + 4, position.y + 5, Width, 22);
                Rect HeaderLabelRect = new RectOffset(5, 5, 1, 0).Remove(HeaderBackgroundRect);
                GUI.Label(HeaderBackgroundRect, string.Empty, "RL Header");
                GUI.Label(HeaderLabelRect, "Polygon UV Info", "BoldLabel");

#if UNITY_2019_1_OR_NEWER
                Rect PolygonInfoRect = new Rect(HeaderBackgroundRect.xMin, HeaderBackgroundRect.yMax - 3, Width, 156);
#else
                Rect PolygonInfoRect = new Rect(HeaderBackgroundRect.xMin, HeaderBackgroundRect.yMax - 5, Width, 152);
#endif

                bool VertexZoneHovered = false;

                using (new GUILayout.AreaScope(PolygonInfoRect, string.Empty, "RL Background"))
                {
                    using (new GUILayout.HorizontalScope())
                    {
                        ObservedUVSet = MCGUI.DrawSelectionGrid(UVSelectionGridItems, ObservedUVSet, UVSet.UV);
                    }

                    if (MeshUV[(int)ObservedUVSet].Length > 0)
                    {
                        EditorGUIUtility.labelWidth = 40;
                        int FirstVertex = Loop.Current * 3;

                        for (int Index = 0; Index < 3; Index++)
                        {
                            Vector2 UV = MeshUV[(int)ObservedUVSet][BadVertices[FirstVertex + Index]];
                            bool BadVertex = CoincidentUVPolygons[Loop.Current].FailedVertexInUVSet[(int)ObservedUVSet][Index];

                            using (new MCGUI.ColorScope(MCGUI.ErrorColor.Dark, WindowAlpha, BadVertex))
                            using (new GUILayout.VerticalScope(MCGUI.Styles.HelpBox))
                            {
                                const float CoordinatesLineHeight = 15;
                                EditorGUILayout.LabelField(new GUIContent("X"), new GUIContent(UV.x.ToString(CultureInfo.InvariantCulture)), GUILayout.Height(CoordinatesLineHeight));
                                EditorGUILayout.LabelField(new GUIContent("Y"), new GUIContent(UV.y.ToString(CultureInfo.InvariantCulture)), GUILayout.Height(CoordinatesLineHeight));
                            }

                            Rect BoxRect = GUILayoutUtility.GetLastRect();
                            Rect MarkRect = new Rect(BoxRect.xMax - 14, BoxRect.y + (BoxRect.height / 2) - 4.5f, 9, 9);
                            bool CursorOverVertexZone = BoxRect.Contains(Event.current.mousePosition);
                            MCGUI.DrawStatusPoint(MarkRect, BadVertex ? MCGUI.ErrorColor.Current : MCGUI.OkColor.Current, WindowAlpha);

                            if (Event.current.type == EventType.MouseMove)
                            {
                                if (CursorOverVertexZone)
                                {
                                    if (HoveredVertex != Index)
                                    {
                                        MeshAnalysisWindow.RepaintWindow();
                                    }

                                    VertexZoneHovered = true;
                                    HoveredVertex = Index;

                                    Vector3 PinPoint = new Vector3(
                                        PolygonInfoRect.xMin + MarkRect.xMax,
                                        PolygonInfoRect.yMin + BoxRect.yMin + (BoxRect.height / 2),
                                        0);

                                    PointerLine.ClearInput();
                                    PointerLine.AddInputPoint(PinPoint);
                                    const int SecondPointOffset = 20;
                                    PointerLine.AddInputPoint(PinPoint + (Vector3.right * SecondPointOffset));
                                    PointerLine.AddInputPoint(BadPolygonsScreenPosition[FirstVertex + Index].ChangeZ(0));
                                    bool IsError = CoincidentUVPolygons[Loop.Current].FailedVertexInUVSet[(int)ObservedUVSet][HoveredVertex];
                                    PointerLine.SetColor(IsError);
                                }
                            }
                        }

                        if (Event.current.type == EventType.MouseMove)
                        {
                            if (!VertexZoneHovered && HoveredVertex >= 0)
                            {
                                PointerLine.ClearInput();
                                HoveredVertex = -1;
                                MeshAnalysisWindow.RepaintWindow();
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        ///     Updates the selection grid items and its colors.
        /// </summary>
        /// <param name="polygonIndex">The index of failed polygon that selection grid updates for.</param>
        private void UpdateUVSelectionGridItems(int polygonIndex)
        {
            bool[] UVErrorState = new bool[4];
            if (ReadyToDrawBads && polygonIndex >= 0)
            {
                CoincidentUVAnalysis.CoincidentUVPolygon PolygonInfo = CoincidentUVAnalysis.CoincidentUVPolygons.ElementAt(polygonIndex).Value;
                if (PolygonInfo != null)
                {
                    PolygonInfo.FailedUVSets.CopyTo(UVErrorState, 0);
                    Color[] Colors = new Color[4];
                    for (int UVSet = 0; UVSet < 4; UVSet++)
                    {
                        Colors[UVSet] = UVErrorState[UVSet] ? MCGUI.ErrorColor.Dark : Color.white;
                    }

                    UpdateSelectionGridItems(Colors);
                }
                else
                {
                    UpdateSelectionGridItems(new[] { Color.magenta, Color.magenta, Color.magenta, Color.magenta });
                }
            }
        }

        /// <summary>
        ///     Updates the color of a selection grid items.
        /// </summary>
        /// <param name="colors">The collection of new colors.</param>
        /// <exception cref="ArgumentNullException"><paramref name="colors"/> is <see langword="null"/></exception>
        /// <exception cref="ArgumentException">Array must have 4 colors.</exception>
        private void UpdateSelectionGridItems([NotNull] IList<Color> colors)
        {
            if (colors == null)
            {
                throw new ArgumentNullException("colors");
            }

            if (colors.Count != 4)
            {
                throw new ArgumentException("The array must have 4 colors.", "colors");
            }

            UVSelectionGridItems = new List<SelectionGridItem<UVSet>>
            {
                new SelectionGridItem<UVSet>(new GUIContent("UV "), UVSet.UV,  35, 18, MCGUI.Styles.SmallButtonLeft,   colors[0], MeshUV[0].Length > 0),
                new SelectionGridItem<UVSet>(new GUIContent("UV2"), UVSet.UV2, 35, 18, MCGUI.Styles.SmallButtonMiddle, colors[1], MeshUV[1].Length > 0),
                new SelectionGridItem<UVSet>(new GUIContent("UV3"), UVSet.UV3, 35, 18, MCGUI.Styles.SmallButtonMiddle, colors[2], MeshUV[2].Length > 0),
                new SelectionGridItem<UVSet>(new GUIContent("UV4"), UVSet.UV4, 35, 18, MCGUI.Styles.SmallButtonRight,  colors[3], MeshUV[3].Length > 0)
            };
        }

        /// <summary>
        ///     Starts a new <see cref="T:HightlanderSolutions.MeshCheckerEditorExtension.Plugins.MeshAnalysis.CoincidentUVAnalysis"/>.
        /// </summary>
        /// <param name="threshold">The threshold of the analysis.</param>
        private void StartNewCoincidentUVAnalysis(float threshold)
        {
            CoincidentUVAnalysis = new CoincidentUVAnalysis
            {
                Threshold = threshold
            };
            CoincidentUVAnalysis.AnalysisEndedEvent += CoincidentUVAnalysisEndedCallback;
            CoincidentUVAnalysis.Initialize(Mesh);
            CoincidentUVAnalysis.RunAnalysisInThread();
        }

        /// <summary>
        ///     The callback method which occurs when analysis ended.
        /// </summary>
        /// <param name="result">Specifies the result of analysis.</param>
        private void CoincidentUVAnalysisEndedCallback(MeshAnalysisResultType result)
        {
            if (result == MeshAnalysisResultType.Error)
            {
                CoincidentUVPolygons = CoincidentUVAnalysis.GetCoincidentUVPolygons();

                BadPolygonsCount = CoincidentUVPolygons.Count;
                BadVerticesCount = BadPolygonsCount * 3;

                // Initialize array for bad vertices projection
                BadPolygonsScreenPosition = new Vector3[BadPolygonsCount * 3];
                BadVertices = new int[BadVerticesCount];

                for (int PolygonIndex = 0; PolygonIndex < BadPolygonsCount; PolygonIndex++)
                {
                    CoincidentUVPolygons[PolygonIndex].VertexIndices.CopyTo(BadVertices, PolygonIndex * 3);
                }

                Loop = new IndexLoop(CoincidentUVPolygons.Count - 1);
                Loop.SelectionChangedEvent += TriangleSelectionChangedEventHandler;
                Loop.SelectionRemovedEvent += TriangleSelectionRemovedEventHandler;
            }
        }

        /// <summary>
        ///     Invoked when the triangle selection changed.
        /// </summary>
        /// <param name="newIndex">The new loop index of a selected triangle.</param>
        private void TriangleSelectionChangedEventHandler(int newIndex)
        {
            UpdateUVSelectionGridItems(newIndex);
            FocusOnTriangle(newIndex);

            HoveredVertex = -1;
            PointerLine.ClearInput();
        }

        /// <summary>
        ///     Invoked when the triangle selection removed.
        /// </summary>
        private void TriangleSelectionRemovedEventHandler()
        {
            MeshAnalysisWindow.RemoveFocus();
        }

        /// <summary>
        ///     Focuses on the triangle which has specified index.
        /// </summary>
        /// <param name="triangle">The index of the triangle</param>
        private void FocusOnTriangle(int triangle)
        {
            int FirstIndex = triangle * 3;
            Vector3 TriangleNormal = MCUtilities.GetAverage(
                MeshNormals[BadVertices[FirstIndex + 0]],
                MeshNormals[BadVertices[FirstIndex + 1]],
                MeshNormals[BadVertices[FirstIndex + 2]]).normalized;

            MeshAnalysisWindow.FocusOnTriangle(
                MeshVertices[BadVertices[FirstIndex + 0]],
                MeshVertices[BadVertices[FirstIndex + 1]],
                MeshVertices[BadVertices[FirstIndex + 2]],
                TriangleNormal);
        }
    }
}
