using System.Collections.Generic;
using System.Globalization;
using System.Linq;

using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.Plugins.MeshAnalysis;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;

using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.Tools
{
    /// <summary>
    ///     Represents a tool which helps to find triangles with coincident vertices.
    /// </summary>
    [Alias("Tools.StuckPolygons")]
    [MeshAnalysisTool("Stuck Polygons", 101, KeyCode.W)]
    internal sealed class StuckPolygonsTool : MeshAnalysisTool
    {
        /// <summary>
        ///     The used analysis for target mesh.
        /// </summary>
        private readonly CoincidentVerticesAnalysis Analysis = new CoincidentVerticesAnalysis();

        /// <summary>
        ///     The collection of points which used to draw a polyline from the infobox to hovered vertex.
        /// </summary>
        private readonly OverlayLine PointerLine = new OverlayLine();

        /// <summary>
        ///     The polygon drawing type selection grid.
        /// </summary>
        private PolygonDrawingTypeToolbar PolygonDrawingTypeToolbar;

        /// <summary>
        ///     The collection of polygons that have coincident vertices.
        /// </summary>
        private List<FailedPolygon> FailedPolygons;

        /// <summary>
        ///     Indices of vertices contained in triangles with coincident vertices.
        /// </summary>
        private int[] BadVertices;

        /// <summary>
        ///     The states array of vertices contained in triangles with coincident vertices.
        /// </summary>
        private bool[] VertexBadFlags;

        /// <summary>
        ///     Failed triangles loop for cycling from UI.
        /// </summary>
        private IndexLoop Loop = new IndexLoop();

        /// <summary>
        ///     Indicates whether all triangles should be shown.
        /// </summary>
        private bool ShowAllTriangles = true;

        /// <summary>
        ///     The index of hovered vertex. -1 if no vertex had hovered.
        /// </summary>
        private int HoveredVertex = -1;

        /// <summary>
        ///     Vertices of a target mesh.
        /// </summary>
        private Vector3[] MeshVertices;

        /// <summary>
        ///     Normals of a target mesh.
        /// </summary>
        private Vector3[] MeshNormals;

        /// <summary>
        ///     Gets or sets the threshold. 
        ///     Only polygon sides with a distance between vertices greater than the threshold successfully pass the analysis.
        /// </summary>
        private float Threshold = CoincidentVerticesAnalysis.DefaultThreshold;

        /// <summary>
        ///     Gets a value indicating whether the analysis ended.
        /// </summary>
        private bool AnalysisEnded
        {
            get
            {
                return Analysis != null
                       && Analysis.Ended;
            }
        }

        #region Overrides of MeshAnalysisTool

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
            Analysis.AnalysisEndedEvent += AnalysisEndedEventHandler;
            Analysis.Initialize(mesh);
            Analysis.RunAnalysisInThread();

            MeshVertices = mesh.vertices;
            MeshNormals = mesh.normals;

            PolygonDrawingTypeToolbar = new PolygonDrawingTypeToolbar(PolygonsDrawingType.Wireframe);
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
            Analysis.Deinitialize();

            BadVertices = null;
            FailedPolygons = null;

            Loop.Release();
            Loop = null;

            PolygonDrawingTypeToolbar = null;
        }

        /// <inheritdoc />
        public override void DrawToolbar()
        {
            PolygonDrawingTypeToolbar.Draw();
            GUILayout.Space(5);
            ShowAllTriangles = GUILayout.Toggle(ShowAllTriangles, "Show all", EditorStyles.toolbarButton);
            Loop.Draw(AnalysisEnded && Analysis.NotSuccess);
            GUILayout.Space(5);
            GUILayout.Label("Threshold");
            EditorGUI.BeginChangeCheck();
            Threshold = EditorGUILayout.Slider(GUIContent.none, Threshold, 0f, 0.001f, GUILayout.Width(200));
            if (EditorGUI.EndChangeCheck())
            {
                Analysis.Threshold = Threshold;
                Analysis.RunAnalysisInThread();
            }
        }

        /// <inheritdoc />
        public override void DrawOverlay(Rect position)
        {
            if (AnalysisEnded
                && Analysis.NotSuccess
                && FailedPolygons.Any())
            {
                Handles.color = OverlayUtilities.RedHandleColor.Dark;

                // Draw all triangles except selected one if needed
                for (int PolygonIndex = 0; PolygonIndex < FailedPolygons.Count; PolygonIndex++)
                {
                    if (!ShowAllTriangles
                        && Loop.Selected
                        && Loop.Current != PolygonIndex)
                    {
                        continue;
                    }

                    int FirstIndex = PolygonIndex * 3;

                    switch (PolygonDrawingTypeToolbar.Current)
                    {
                        case PolygonsDrawingType.Solid:
                            OverlayUtilities.DrawAATriangleSolid(
                                MeshAnalysisWindow.PreviewToOverlayPoint(MeshVertices[BadVertices[FirstIndex + 0]]),
                                MeshAnalysisWindow.PreviewToOverlayPoint(MeshVertices[BadVertices[FirstIndex + 1]]),
                                MeshAnalysisWindow.PreviewToOverlayPoint(MeshVertices[BadVertices[FirstIndex + 2]]));
                            break;

                        case PolygonsDrawingType.Wireframe:
                            if (Loop.Current != PolygonIndex)
                            {
                                OverlayUtilities.DrawTriangleWireframe(
                                    MeshAnalysisWindow.PreviewToOverlayPoint(MeshVertices[BadVertices[FirstIndex + 0]]),
                                    MeshAnalysisWindow.PreviewToOverlayPoint(MeshVertices[BadVertices[FirstIndex + 1]]),
                                    MeshAnalysisWindow.PreviewToOverlayPoint(MeshVertices[BadVertices[FirstIndex + 2]]),
                                    3);
                            }

                            break;

                        case PolygonsDrawingType.DontDraw:
                            break;
                    }
                }

                // Draw selected triangle
                if (Loop.Selected)
                {
                    Handles.color = Color.yellow;
                    int FirstIndex = Loop.Current * 3;
                    OverlayUtilities.DrawTriangleWireframe(
                        MeshAnalysisWindow.PreviewToOverlayPoint(MeshVertices[BadVertices[FirstIndex + 0]]),
                        MeshAnalysisWindow.PreviewToOverlayPoint(MeshVertices[BadVertices[FirstIndex + 1]]),
                        MeshAnalysisWindow.PreviewToOverlayPoint(MeshVertices[BadVertices[FirstIndex + 2]]),
                        3,
                        true);
                }
            }

            if (PointerLine.HasPoints)
            {
                OverlayUtilities.DrawPointerLine(PointerLine);
            }
        }


        /// <inheritdoc />
        public override void DrawOverlayGUI(Rect position)
        {
            if (Loop.NotSelected)
            {
                return;
            }

            DrawPolygonInfoWindow(position);
        }

        #endregion

        /// <summary>
        ///     Draws a polygon info window which displays vertex UV coordinates in UV sets.
        /// </summary>
        /// <param name="position">The position of the window.</param>
        private void DrawPolygonInfoWindow(Rect position)
        {
            float WindowAlpha = EditorGUIUtility.isProSkin ? 0.9f : 0.7f;
            const float Width = 200;

            using (new MCGUI.ColorScope(WindowAlpha))
            {
                Rect HeaderBackgroundRect = new Rect(position.x + 4, position.y + 5, Width, 22);
                Rect HeaderLabelRect = new RectOffset(5, 5, 1, 0).Remove(HeaderBackgroundRect);
                GUI.Label(HeaderBackgroundRect, string.Empty, "RL Header");
                GUI.Label(HeaderLabelRect, "Polygon vertices Info", "BoldLabel");

#if UNITY_2019_1_OR_NEWER
                Rect PolygonInfoRect = new Rect(HeaderBackgroundRect.xMin, HeaderBackgroundRect.yMax - 3, Width, 189);
#else
                Rect PolygonInfoRect = new Rect(HeaderBackgroundRect.xMin, HeaderBackgroundRect.yMax - 5, Width, 182);
#endif

                bool VertexZoneHovered = false;

                using (new GUILayout.AreaScope(PolygonInfoRect, string.Empty, "RL Background"))
                {
                    EditorGUIUtility.labelWidth = 40;
                    for (int Index = 0; Index < 3; Index++)
                    {
                        int FirstVertex = Loop.Current * 3;

                        Vector3 Position = MeshVertices[BadVertices[FirstVertex + Index]];
                        bool FailedVertex = VertexBadFlags[FirstVertex + Index];

                        using (new MCGUI.ColorScope(MCGUI.ErrorColor.Dark, FailedVertex))
                        using (new GUILayout.VerticalScope(MCGUI.Styles.HelpBox))
                        {
                            const float CoordinatesLineHeight = 15;
                            EditorGUILayout.LabelField(new GUIContent("X"), new GUIContent(Position.x.ToString(CultureInfo.InvariantCulture)), GUILayout.Height(CoordinatesLineHeight));
                            EditorGUILayout.LabelField(new GUIContent("Y"), new GUIContent(Position.y.ToString(CultureInfo.InvariantCulture)), GUILayout.Height(CoordinatesLineHeight));
                            EditorGUILayout.LabelField(new GUIContent("Z"), new GUIContent(Position.z.ToString(CultureInfo.InvariantCulture)), GUILayout.Height(CoordinatesLineHeight));
                        }

                        Rect BoxRect = GUILayoutUtility.GetLastRect();
                        Rect MarkRect = new Rect(BoxRect.xMax - 14, BoxRect.y + (BoxRect.height / 2) - 4.5f, 9, 9);
                        bool CursorOverVertexZone = BoxRect.Contains(Event.current.mousePosition);
                        MCGUI.DrawStatusPoint(MarkRect, FailedVertex ? MCGUI.ErrorColor.Current : MCGUI.OkColor.Current, WindowAlpha);

                        if (Event.current.type == EventType.MouseMove)
                        {
                            if (CursorOverVertexZone)
                            {
                                if (HoveredVertex != Index)
                                {
                                    MeshAnalysisWindow.RepaintWindow();
                                }

                                VertexZoneHovered = true;
                                HoveredVertex = Index;

                                Vector3 PinPoint = new Vector3(
                                    PolygonInfoRect.xMin + MarkRect.xMax,
                                    PolygonInfoRect.yMin + BoxRect.yMin + (BoxRect.height / 2),
                                    0);

                                PointerLine.ClearInput();
                                PointerLine.AddInputPoint(PinPoint);
                                const int SecondPointOffset = 20;
                                PointerLine.AddInputPoint(PinPoint + (Vector3.right * SecondPointOffset));
                                PointerLine.AddInputPoint(MeshAnalysisWindow.PreviewToOverlayPoint(MeshVertices[BadVertices[FirstVertex + Index]]).ChangeZ(0));
                                PointerLine.SetColor(FailedVertex);
                            }
                        }
                    }

                    if (Event.current.type == EventType.MouseMove)
                    {
                        if (!VertexZoneHovered && HoveredVertex >= 0)
                        {
                            RemovePointerLine();
                        }
                    }
                }
            }
        }

        /// <summary>
        ///     Focuses on the triangle which has specified index.
        /// </summary>
        /// <param name="triangle">The index of the triangle</param>
        private void FocusOnTriangle(int triangle)
        {
            int FirstIndex = triangle * 3;

            Vector3 TriangleNormal = MCUtilities.GetAverage(
                MeshNormals[BadVertices[FirstIndex + 0]],
                MeshNormals[BadVertices[FirstIndex + 1]],
                MeshNormals[BadVertices[FirstIndex + 2]]).normalized;

            MeshAnalysisWindow.FocusOnTriangle(
                MeshVertices[BadVertices[FirstIndex + 0]],
                MeshVertices[BadVertices[FirstIndex + 1]],
                MeshVertices[BadVertices[FirstIndex + 2]],
                TriangleNormal);
        }

        /// <summary>
        ///     The callback method which occurs when analysis ended.
        /// </summary>
        /// <param name="result">Specifies the result of analysis.</param>
        private void AnalysisEndedEventHandler(MeshAnalysisResultType result)
        {
            FailedPolygons = new List<FailedPolygon>(Analysis.FailedPolygons);

            int BadPolygonsCount = FailedPolygons.Count;
            int BadVerticesCount = BadPolygonsCount * 3;
            
            BadVertices = new int[BadVerticesCount];
            VertexBadFlags = new bool[BadVerticesCount];

            for (int PolygonIndex = 0; PolygonIndex < BadPolygonsCount; PolygonIndex++)
            {
                if (FailedPolygons[PolygonIndex] == null)
                {
                    break;
                }

                FailedPolygons[PolygonIndex].VertexIndices.CopyTo(BadVertices, PolygonIndex * 3);
                FailedPolygons[PolygonIndex].FailedVertex.CopyTo(VertexBadFlags, PolygonIndex * 3);
            }

            Loop = new IndexLoop(Analysis.FailedPolygons.Count - 1);
            Loop.SelectionChangedEvent += TriangleSelectionChangedEventHandler;
            Loop.SelectionRemovedEvent += MeshAnalysisWindow.RemoveFocus;
        }

        /// <summary>
        ///     Invoked when the triangle selection changed.
        /// </summary>
        /// <param name="newIndex">The new loop index of a selected triangle.</param>
        private void TriangleSelectionChangedEventHandler(int newIndex)
        {
            FocusOnTriangle(newIndex);

            HoveredVertex = -1;
            PointerLine.ClearInput();
        }

        /// <summary>
        ///     Clears line points, hovered vertex data and repaints the window.
        /// </summary>
        private void RemovePointerLine()
        {
            PointerLine.ClearInput();
            HoveredVertex = -1;
            MeshAnalysisWindow.RepaintWindow();
        }
    }
}
