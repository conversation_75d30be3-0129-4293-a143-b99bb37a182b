using System;
using System.Collections.Generic;
using System.Linq;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.MeshAnalysis
{
    /// <summary>
    ///     Represents an analysis which checks mesh triangles on the coincident UVs. 
    ///     If 2 or 3 of vertices in the triangle shares same UV coordinates then triangle has errors.
    /// </summary>
    [MeshAnalysis]
    public sealed class CoincidentUVAnalysis : MessageMeshAnalysis
    {
        /// <summary>
        ///     Represents a failed polygon with info about failed UV sets.
        /// </summary>
        public sealed class CoincidentUVPolygon : FailedPolygon
        {
            /// <summary>
            ///     The array of a values indicating whether a UV set have errors.
            /// </summary>
            [NotNull]
            public readonly bool[] FailedUVSets = new bool[4];

            /// <summary>
            ///     The array of a values indicating whether a vertex of the triangle shares same UV coordinates with another.
            /// </summary>
            public readonly bool[][] FailedVertexInUVSet;

            /// <summary>
            ///     Initializes a new instance of the <see cref="CoincidentUVPolygon"/> class.
            /// </summary>
            /// <param name="polygonIndex">The polygon index.</param>
            /// <param name="index1">The index of a first vertex.</param>
            /// <param name="index2">The index of a second vertex.</param>
            /// <param name="index3">The index of a third vertex.</param>
            public CoincidentUVPolygon(int polygonIndex, int index1, int index2, int index3) : base(polygonIndex, index1, index2, index3)
            {
                FailedVertexInUVSet = new bool[4][];
                for (int I = 0; I < 4; I++)
                {
                    FailedVertexInUVSet[I] = new bool[3];
                }
            }

            /// <summary>
            ///     Initializes a new instance of the <see cref="CoincidentUVPolygon"/> class.
            /// </summary>
            /// <param name="polygonIndex">The polygon index.</param>
            /// <param name="triangles">The collection of mesh triangles.</param>
            public CoincidentUVPolygon(int polygonIndex, [NotNull] IList<int> triangles)
                : this(polygonIndex, triangles[polygonIndex], triangles[polygonIndex + 1], triangles[polygonIndex + 2])
            {
            }

            #region Overrides of Object

            /// <inheritdoc />
            public override string ToString()
            {
                return string.Format(
                    "[CoincidentUVPolygon] Index={0}; FailedUVSets={1}, {2}, {3}, {4};",
                    Index,
                    FailedUVSets[0],
                    FailedUVSets[1],
                    FailedUVSets[2],
                    FailedUVSets[3]);
            }

            #endregion
        }

        /// <summary>
        ///     Gets the dictionary of failed polygons where keys are polygons indices.
        /// </summary>
        [PublicAPI]
        public Dictionary<int, CoincidentUVPolygon> CoincidentUVPolygons { get; private set; }

        /// <summary>
        ///     Gets the triangles of a target mesh.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        [PublicAPI]
        public int[] Triangles { get; private set; }

        /// <summary>
        ///     The array of values that indicates whether some of triangles have errors in the UV set.
        /// </summary>
        [PublicAPI]
        public readonly bool[] FailedUVSets = new bool[4];

        /// <summary>
        ///     Gets or sets the threshold. 
        ///     Only UV coordinates with a distance between them greater than the threshold successfully pass the analysis.
        /// </summary>
        [PublicAPI]
        public float Threshold
        {
            get
            {
                return InnerThreshold;
            }

            set
            {
                InnerThreshold = value;
                SqrThreshold = InnerThreshold * InnerThreshold;
            }
        }

        /// <summary>
        ///     The default threshold.
        /// </summary>
        private const float DefaultThreshold = 0f;

        /// <summary>
        ///     UV sets of the target mesh.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private Vector2[][] UV;

        /// <summary>
        ///     The array of values that indicates whether an UV set exists.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private bool[] HasUV = new bool[4];

        /// <summary>
        ///     Gets or sets the squared value of the threshold.
        /// </summary>
        private float SqrThreshold { get; set; }

        /// <summary>
        ///     The backing field of the <see cref="Threshold"/> property.
        /// </summary>
        private float InnerThreshold;

        /// <summary>
        ///     Initializes a new instance of the <see cref="CoincidentUVAnalysis"/> class.
        /// </summary>
        public CoincidentUVAnalysis()
        {
            Threshold = DefaultThreshold;
            Interactable = true;
        }

        /// <summary>
        ///     Gets the list of polygons that have errors.
        /// </summary>
        /// <returns>The list of polygons that have errors.</returns>
        [NotNull]
        public List<CoincidentUVPolygon> GetCoincidentUVPolygons()
        {
            if (CoincidentUVPolygons == null)
            {
                return new List<CoincidentUVPolygon>();
            }

            return CoincidentUVPolygons.Values.ToList();
        }

        #region Overrides of MeshAnalysis

        /// <inheritdoc />
        public override void OnClick()
        {
            MeshAnalysisWindow.Open(Mesh, "ShadingOptions.Checker", "Tools.CoincidentUVs");
        }

        /// <inheritdoc />
        public override void RunAnalysis()
        {
            bool[] TempFailedVertices = new bool[3];

            for (int PolygonIndex = 0; PolygonIndex < Triangles.Length; PolygonIndex += 3)
            {
                int[] PolygonVertexIndices = { Triangles[PolygonIndex], Triangles[PolygonIndex + 1], Triangles[PolygonIndex + 2] };
                for (int UVSetIndex = 0; UVSetIndex < 4; UVSetIndex++)
                {
                    if (HasUV[UVSetIndex])
                    {
                        for (int i = 0; i < 3; i++)
                        {
                            TempFailedVertices[i] = false;
                        }

                        if (CheckPair(0, 1, UVSetIndex, PolygonVertexIndices))
                        {
                            TempFailedVertices[0] = true;
                            TempFailedVertices[1] = true;
                        }

                        if (CheckPair(0, 2, UVSetIndex, PolygonVertexIndices))
                        {
                            TempFailedVertices[0] = true;
                            TempFailedVertices[2] = true;
                        }

                        if (CheckPair(1, 2, UVSetIndex, PolygonVertexIndices))
                        {
                            TempFailedVertices[1] = true;
                            TempFailedVertices[2] = true;
                        }

                        // Check for Coincident UV in vertices
                        if (TempFailedVertices.Any(e => e))
                        {
                            // Add to FailedUVSets array to know what UV sets are failed
                            FailedUVSets[UVSetIndex] = true;

                            if (!CoincidentUVPolygons.ContainsKey(PolygonIndex))
                            {
                                // If this polygon are new - add to dictionary
                                CoincidentUVPolygon NewCoincidentUVPolygon = new CoincidentUVPolygon(PolygonIndex, Triangles);

                                // Set bad vertices flag in current UV set
                                TempFailedVertices.CopyTo(NewCoincidentUVPolygon.FailedVertexInUVSet[UVSetIndex], 0);

                                // Set failed UV set flag
                                NewCoincidentUVPolygon.FailedUVSets[UVSetIndex] = true;
                                CoincidentUVPolygons.Add(PolygonIndex, NewCoincidentUVPolygon);
                            }
                            else
                            {
                                // If polygon already exists in dictionary - update polygon failed uv sets
                                CoincidentUVPolygons[PolygonIndex].FailedUVSets[UVSetIndex] = true;

                                TempFailedVertices.CopyTo(CoincidentUVPolygons[PolygonIndex].FailedVertexInUVSet[UVSetIndex], 0);
                            }
                        }
                    }
                }
            }

            bool HasErrors = CoincidentUVPolygons.Count > 0;
            MeshAnalysisResultType EndType = HasErrors ? MeshAnalysisResultType.Error : MeshAnalysisResultType.Success;
            End(EndType, GetUVDoublesExistsCaption());
        }

        /// <inheritdoc />
        protected override void OnInitialize(Mesh mesh)
        {
            Triangles = mesh.triangles;
            UV = mesh.GetUVSets();

            CoincidentUVPolygons = new Dictionary<int, CoincidentUVPolygon>();

            for (int UVSetIndex = 0; UVSetIndex < 4; UVSetIndex++)
            {
                HasUV[UVSetIndex] = UV[UVSetIndex].Length > 0;
            }
        }

        /// <inheritdoc />
        protected override void OnDeinitialize()
        {
            Triangles = null;
            UV = null;
            HasUV = null;
            CoincidentUVPolygons = null;
        }

        #endregion

        /// <summary>
        ///     Checks the pair on coincident UVs.
        /// </summary>
        /// <param name="vertex0">The first vertex index in a triangle.</param>
        /// <param name="vertex1">The second vertex index in a triangle.</param>
        /// <param name="uvSetIndex">The index of the checked UV set.</param>
        /// <param name="triangleIndices">The array of vertex indices in triangle.</param>
        /// <returns><c>true</c> if the distance between UV coordinates greater than a threshold; otherwise, <c>false</c>.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="triangleIndices"/> is <see langword="null"/></exception>
        private bool CheckPair(short vertex0, short vertex1, int uvSetIndex, [NotNull] int[] triangleIndices)
        {
            if (triangleIndices == null)
            {
                throw new ArgumentNullException("triangleIndices");
            }

            return (UV[uvSetIndex][triangleIndices[vertex0]] - UV[uvSetIndex][triangleIndices[vertex1]]).sqrMagnitude <= SqrThreshold;
        }

        /// <summary>
        ///     Gets the text of an error.
        /// </summary>
        /// <returns>The text of the error.</returns>
        [CanBeNull]
        private string GetUVDoublesExistsCaption()
        {
            string UVDoublesExistCaption = null;

            int CoincidentUVPolygonsCount = CoincidentUVPolygons.Count;
            int FailedUVSetsCount = FailedUVSets.Count(e => e);

            if (CoincidentUVPolygonsCount > 0)
            {
                UVDoublesExistCaption = string.Concat("Mesh has UV coord doubles in ", CoincidentUVPolygonsCount, " polygons.");
                if (FailedUVSetsCount > 0)
                {
                    UVDoublesExistCaption += " (";
                    int AddedUVsCount = 0;
                    for (int Index = 0; Index < 4; Index++)
                    {
                        if (FailedUVSets[Index])
                        {
                            if (AddedUVsCount > 0)
                            {
                                UVDoublesExistCaption += ", ";
                            }

                            UVDoublesExistCaption += "UV" + (Index == 0 ? string.Empty : (Index + 1).ToString());
                            AddedUVsCount++;
                        }
                    }

                    UVDoublesExistCaption += FailedUVSetsCount == 1 ? " set)" : " sets)";
                }
            }

            return UVDoublesExistCaption;
        }
    }
}
