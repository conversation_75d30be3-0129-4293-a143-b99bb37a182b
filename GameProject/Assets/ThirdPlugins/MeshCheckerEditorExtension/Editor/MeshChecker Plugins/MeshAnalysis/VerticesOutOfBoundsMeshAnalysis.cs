using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.MeshAnalysis
{
    /// <summary>
    ///     Represents an analysis which checks for vertices that stay out of bounds.
    /// </summary>
    [MeshAnalysis]
    public sealed class VerticesOutOfBoundsMeshAnalysis : MessageMeshAnalysis
    {
        /// <summary>
        ///     The threshold. 
        ///     Only vertices with a distance between them and bounds that are greater than the threshold can successfully pass the analysis.
        /// </summary>
        private const float Threshold = 0.0001f;

        /// <summary>
        ///     The mesh bounds.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private Bounds MeshBounds;

        /// <summary>
        ///     The vertices array of the target mesh.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private Vector3[] Vertices;

        #region Overrides of MeshAnalysis

        /// <inheritdoc />
        public override void RunAnalysis()
        {
            bool VertexOutOfBounds = false;

            for (int Index = 0; Index < Vertices.Length && !VertexOutOfBounds; Index++)
            {
                VertexOutOfBounds = !MeshBounds.Contains(Vertices[Index]);
            }

            if (VertexOutOfBounds)
            {
                End(MeshAnalysisResultType.Error, "Mesh partially out of bounds.");
            }
            else
            {
                End(MeshAnalysisResultType.Success, string.Empty);
            }
        }

        /// <inheritdoc />
        public override void OnClick()
        {
            MeshAnalysisWindow.Open(Mesh, null, "Tools.ViewBounds");
        }

        /// <inheritdoc />
        protected override void OnInitialize(Mesh mesh)
        {
            MeshBounds = mesh.bounds;
            MeshBounds.size += Vector3.one * Threshold;
            Vertices = mesh.vertices;

            Interactable = true;
        }

        /// <inheritdoc />
        protected override void OnDeinitialize()
        {
            Vertices = null;
        }

        #endregion
    }
}