using System.Collections.Generic;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.MeshAnalysis
{
    /// <summary>
    ///     Represents an analysis which checks mesh triangles on the coincident vertices.
    ///     If 2 or 3 of vertices in the triangle shares same or have similar positions then triangle has errors.
    /// </summary>
    [MeshAnalysis]
    internal sealed class CoincidentVerticesAnalysis : MessageMeshAnalysis
    {
        /// <summary>
        ///     The default threshold.
        /// </summary>
        [PublicAPI]
        public const float DefaultThreshold = 0.00001f;

        /// <summary>
        ///     The list of failed polygons.
        /// </summary>
        public readonly List<FailedPolygon> FailedPolygons = new List<FailedPolygon>();

        /// <summary>
        ///     Gets or sets the threshold. 
        ///     Only UV coordinates with a distance between them greater than the threshold successfully pass the analysis.
        /// </summary>
        [PublicAPI]
        public float Threshold
        {
            get
            {
                return InnerThreshold;
            }

            set
            {
                InnerThreshold = value;
                SqrThreshold = InnerThreshold * InnerThreshold;
            }
        }

        /// <summary>
        ///     The vertices array of the target mesh.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private Vector3[] Vertices;

        /// <summary>
        ///     The triangles of the target mesh.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private int[] Triangles;

        /// <summary>
        ///     The triangles count.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private int TrianglesCount;

        /// <summary>
        ///     The backing field of the <see cref="Threshold"/> property.
        /// </summary>
        private float InnerThreshold;

        /// <summary>
        ///     Gets or sets the squared value of the threshold.
        /// </summary>
        private float SqrThreshold { get; set; }

        /// <summary>
        ///     Initializes a new instance of the <see cref="CoincidentVerticesAnalysis"/> class.
        /// </summary>
        public CoincidentVerticesAnalysis()
        {
            Threshold = DefaultThreshold;
        }

        #region Overrides of MeshAnalysis

        /// <inheritdoc />
        public override void RunAnalysis()
        {
            FailedPolygons.Clear();
            for (int Index = 0; Index < TrianglesCount; Index++)
            {
                int TriangleFirstIndex = Index * 3;
                bool[] SideFailed =
                            {
                                CheckPair(TriangleFirstIndex, 0, 1),
                                CheckPair(TriangleFirstIndex, 0, 2),
                                CheckPair(TriangleFirstIndex, 1, 2)
                            };

                if (SideFailed[0]
                    || SideFailed[1]
                    || SideFailed[2])
                {
                    FailedPolygon FailedPolygon = new FailedPolygon(Index * 3, Triangles);
                    FailedPolygon.FailedVertex[0] = SideFailed[0] || SideFailed[1];
                    FailedPolygon.FailedVertex[1] = SideFailed[0] || SideFailed[2];
                    FailedPolygon.FailedVertex[2] = SideFailed[1] || SideFailed[2];
                    FailedPolygons.Add(FailedPolygon);
                }
            }

            if (FailedPolygons.Count == 0)
            {
                End(MeshAnalysisResultType.Success);
            }
            else
            {
                End(MeshAnalysisResultType.Error, string.Format("Mesh has vertex position doubles in {0} polygons.", FailedPolygons.Count));
            }
        }

        /// <inheritdoc />
        public override void OnClick()
        {
            MeshAnalysisWindow.Open(Mesh, "ShadingOptions.Checker", "Tools.StuckPolygons");
        }

        /// <inheritdoc />
        protected override void OnInitialize(Mesh mesh)
        {
            Interactable = true;

            Vertices = mesh.vertices;
            Triangles = mesh.triangles;
            TrianglesCount = Triangles.Length / 3;
        }

        /// <inheritdoc />
        protected override void OnDeinitialize()
        {
            FailedPolygons.Clear();
        }

        #endregion

        /// <summary>
        ///     Checks the pair on coincident vertices.
        /// </summary>
        /// <param name="triangleFirstIndex">The index of the first vertex in a triangle.</param>
        /// <param name="vertex0">The index of first vertex in triangle.</param>
        /// <param name="vertex1">The index of second vertex in triangle.</param>
        /// <returns><c>true</c> if the distance between vertices less than a threshold; otherwise, <c>false</c>.</returns>
        private bool CheckPair(int triangleFirstIndex, int vertex0, int vertex1)
        {
            return (Vertices[Triangles[triangleFirstIndex + vertex0]] - Vertices[Triangles[triangleFirstIndex + vertex1]]).sqrMagnitude < SqrThreshold;
        }
    }
}
