using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.MeshAnalysis
{
    /// <summary>
    ///     Represents an analysis which checks for the pivot that out of the mesh bounds.
    /// </summary>
    [MeshAnalysis]
    public sealed class PivotOutOfBoundsMeshAnalysis : MessageMeshAnalysis
    {
        /// <summary>
        ///     The mesh bounds.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private Bounds MeshBounds;

        #region Overrides of MeshAnalysis

        /// <inheritdoc />
        public override void RunAnalysis()
        {
            if (MeshBounds.SqrDistance(Vector3.zero) < Mathf.Max(MeshBounds.extents.x, MeshBounds.extents.y, MeshBounds.extents.z) / 10f)
            {
                End(MeshAnalysisResultType.Success, string.Empty);
            }
            else
            {
                End(MeshAnalysisResultType.Warning, "Pivot point too far from mesh bounds.");
            }
        }

        /// <inheritdoc />
        public override void OnClick()
        {
            MeshAnalysisWindow.Open(Mesh, null, "Tools.ViewPivot");
        }

        /// <inheritdoc />
        protected override void OnInitialize(Mesh mesh)
        {
            Interactable = true;
            MeshBounds = mesh.bounds;
        }

        /// <inheritdoc />
        protected override void OnDeinitialize()
        {
        }

        #endregion
    }
}
