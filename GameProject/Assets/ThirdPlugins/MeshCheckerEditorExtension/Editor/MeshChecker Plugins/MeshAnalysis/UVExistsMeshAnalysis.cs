using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.MeshAnalysis
{
    /// <summary>
    ///     Represents an analysis which checks on a UV0 set existed.
    /// </summary>
    [MeshAnalysis]
    public sealed class UVExistsMeshAnalysis : MessageMeshAnalysis
    {
        /// <summary>
        ///     The UV sets.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private Vector2[] UVs;

        #region Overrides of MeshAnalysis

        /// <inheritdoc />
        public override void RunAnalysis()
        {
            if (UVs != null && UVs.Length > 0)
            {
                End(MeshAnalysisResultType.Success, string.Empty);
            }
            else
            {
                End(MeshAnalysisResultType.Error, "Mesh doesn't have UV0 data.\nUV0 needed for lightmap baking.");
            }
        }

        /// <inheritdoc />
        protected override void OnInitialize(Mesh mesh)
        {
            UVs = Mesh.uv;
        }

        /// <inheritdoc />
        protected override void OnDeinitialize()
        {
            UVs = null;
        }

        #endregion
    }
}
