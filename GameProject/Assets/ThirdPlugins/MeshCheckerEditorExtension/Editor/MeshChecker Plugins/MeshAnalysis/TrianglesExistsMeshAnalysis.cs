using System.Collections.Generic;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.MeshAnalysis
{
    [MeshAnalysis]
    public sealed class TrianglesExistsMeshAnalysis : MessageMeshAnalysis
    {
        /// <summary>
        ///     The array of indices for each analysed submesh.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private int[][] Indices;

        #region Overrides of MeshAnalysis

        /// <inheritdoc />
        public override void RunAnalysis()
        {
            List<string> EmptySubmeshes = new List<string>();

            if (Indices.Length == 0)
            {
                End(MeshAnalysisResultType.Error, "The mesh hasn't any triangles.");
            }
            else
            {
                for (int S = 0; S < Indices.Length; S++)
                {
                    if (Indices[S].Length < 3)
                    {
                        EmptySubmeshes.Add(S.ToString());
                    }
                }

                if (EmptySubmeshes.Count > 0)
                {
                    End(MeshAnalysisResultType.Error, string.Format("The mesh have empty submeshes: {0}.", string.Join(",", EmptySubmeshes.ToArray())));
                }
                else
                {
                    End(MeshAnalysisResultType.Success);
                }
            }
        }

        /// <inheritdoc />
        protected override void OnInitialize(Mesh mesh)
        {
            Indices = new int[mesh.subMeshCount][];
            for (int SubmeshIndex = 0; SubmeshIndex < mesh.subMeshCount; SubmeshIndex++)
            {
                Indices[SubmeshIndex] = mesh.GetIndices(SubmeshIndex);
            }
        }

        /// <inheritdoc />
        protected override void OnDeinitialize()
        {
            Indices = null;
        }

        #endregion
    }
}