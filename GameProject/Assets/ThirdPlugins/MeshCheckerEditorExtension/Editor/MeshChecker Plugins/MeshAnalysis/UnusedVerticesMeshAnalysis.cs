using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.MeshAnalysis
{
    [MeshAnalysis]
    public sealed class UnusedVerticesMeshAnalysis : MessageMeshAnalysis
    {
        /// <summary>
        ///     The array of indices for each analysed submesh.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private int[][] Indices;

        /// <summary>
        ///     The count of vertices in the analysed mesh.
        /// </summary>
        /// <remarks>
        ///     Filled on initialization.
        /// </remarks>
        private int VerticesCount;

        #region Overrides of MeshAnalysis

        /// <inheritdoc />
        public override void RunAnalysis()
        {
            List<int> UnicalIndices = new List<int>(Indices.SelectMany(indices => indices).Distinct());

            int UnusedVerticesCount = VerticesCount - UnicalIndices.Count;

            if (UnusedVerticesCount > 0)
            {
                End(MeshAnalysisResultType.Error, string.Format("Mesh have {0} unused vertices.", UnusedVerticesCount));
            }
            else if (UnusedVerticesCount == 0)
            {
                End(MeshAnalysisResultType.Success);
            }
            else
            {
                End(MeshAnalysisResultType.Failed, "The count of used indices are greater than vertices exist.");
            }
        }

        /// <inheritdoc />
        protected override void OnInitialize(Mesh mesh)
        {
            VerticesCount = mesh.vertices.Length;

            Indices = new int[mesh.subMeshCount][];
            for (int SubmeshIndex = 0; SubmeshIndex < mesh.subMeshCount; SubmeshIndex++)
            {
                Indices[SubmeshIndex] = mesh.GetIndices(SubmeshIndex);
            }
        }

        /// <inheritdoc />
        protected override void OnDeinitialize()
        {
            VerticesCount = -1;
            Indices = null;
        }

        #endregion
    }
}