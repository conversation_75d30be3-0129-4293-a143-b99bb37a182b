using HightlanderSolutions.MeshCheckerEditorExtension.Async;

using JetBrains.Annotations;

namespace HightlanderSolutions.MeshCheckerEditorExtension.ObjectBounds
{
    /// <summary>
    ///     Asynchronous operation object returned from 
    ///     <see cref="Utilities.MCUtilities.CalculateBoundsAsync"/>.
    /// </summary>
    public class BoundsCalculationAsyncOperation : InhibitedAsyncOperation<BoundsCalculationResult>
    {
        /// <inheritdoc />
        public BoundsCalculationAsyncOperation([NotNull] AsyncOperation<BoundsCalculationResult> asyncOperation)
            : base(asyncOperation)
        {
        }
    }
}