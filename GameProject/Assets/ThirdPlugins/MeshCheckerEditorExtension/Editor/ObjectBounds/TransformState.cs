using System;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.ObjectBounds
{
    /// <summary>
    ///     Represents a state of the transform.
    /// </summary>
    internal class TransformState
    {
        /// <summary>
        ///     Gets the position of the transform.
        /// </summary>
        public Vector3 Position { get; private set; }

        /// <summary>
        ///     Gets the rotation of the transform.
        /// </summary>
        public Quaternion Rotation { get; private set; }

        /// <summary>
        ///     Gets the lossy scale of the transform.
        /// </summary>
        public Vector3 LossyScale { get; private set; }

        /// <summary>
        ///     Initializes a new instance of the <see cref="TransformState"/> class.
        /// </summary>
        /// <param name="transform">The transform.</param>
        /// <exception cref="ArgumentNullException"><paramref name="transform"/> is <see langword="null"/></exception>
        public TransformState([NotNull]Transform transform)
        {
            if (transform == null)
            {
                throw new ArgumentNullException("transform");
            }

            Set(transform);
        }

        /// <summary>
        ///     Sets the transform.
        /// </summary>
        /// <param name="transform">The transform which must be referenced by the TransformState.</param>
        /// <exception cref="ArgumentNullException"><paramref name="transform"/> is <see langword="null"/></exception>
        public void Set([NotNull] Transform transform)
        {
            if (transform == null)
            {
                throw new ArgumentNullException("transform");
            }

            Position = transform.position;
            Rotation = transform.rotation;
            LossyScale = transform.lossyScale;
        }

        /// <summary>
        ///     Checks for equality.
        /// </summary>
        /// <param name="transform">The transform which compared to the TransformState.</param>
        /// <param name="positionChanged">Specifies whether the position changed.</param>
        /// <param name="rotationChanged">Specifies whether the rotation changed.</param>
        /// <param name="lossyScaleChanged">Specifies whether the lossyScale changed.</param>
        /// <returns><c>true</c> - if equal; otherwise, <c>false</c>.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="transform"/> is <see langword="null"/></exception>
        public bool Equal([NotNull]Transform transform, out bool positionChanged, out bool rotationChanged, out bool lossyScaleChanged)
        {
            if (transform == null)
            {
                throw new ArgumentNullException("transform");
            }

            return Equal(transform.position, transform.rotation, transform.lossyScale, out positionChanged, out rotationChanged, out lossyScaleChanged);
        }

        /// <summary>
        ///     Checks for equality.
        /// </summary>
        /// <param name="transform">The transform which compared to the TransformState.</param>
        /// <returns><c>true</c> - if equal; otherwise, <c>false</c>.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="transform"/> is <see langword="null"/></exception>
        public bool Equal([NotNull]Transform transform)
        {
            if (transform == null)
            {
                throw new ArgumentNullException("transform");
            }

            bool Dummy1, Dummy2, Dummy3;

            return Equal(transform, out Dummy1, out Dummy2, out Dummy3);
        }

        /// <summary>
        ///     Checks for equality.
        /// </summary>
        /// <param name="transformState">A TransformState which compared to this TransformState.</param>
        /// <param name="positionChanged">Specifies whether the position changed.</param>
        /// <param name="rotationChanged">Specifies whether the rotation changed.</param>
        /// <param name="lossyScaleChanged">Specifies whether the lossyScale changed.</param>
        /// <returns><c>true</c> - if equal; otherwise, <c>false</c>.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="transformState"/> is <see langword="null"/></exception>
        public bool Equal([NotNull]TransformState transformState, out bool positionChanged, out bool rotationChanged, out bool lossyScaleChanged)
        {
            if (transformState == null)
            {
                throw new ArgumentNullException("transformState");
            }

            return Equal(transformState.Position, transformState.Rotation, transformState.LossyScale, out positionChanged, out rotationChanged, out lossyScaleChanged);
        }

        /// <summary>
        ///     Checks for equality.
        /// </summary>
        /// <param name="transformState">A TransformState which compared to this TransformState.</param>
        /// <returns><c>true</c> - if equal; otherwise, <c>false</c>.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="transformState"/> is <see langword="null"/></exception>
        public bool Equal([NotNull]TransformState transformState)
        {
            if (transformState == null)
            {
                throw new ArgumentNullException("transformState");
            }

            bool Dummy1, Dummy2, Dummy3;

            return Equal(transformState, out Dummy1, out Dummy2, out Dummy3);
        }

        /// <summary>
        ///     Checks for equality. 
        /// </summary>
        /// <param name="position">The compared position.</param>
        /// <param name="rotation">The compared rotation.</param>
        /// <param name="lossyScale">The compared scale.</param>
        /// <param name="positionChanged">Specifies whether the position changed.</param>
        /// <param name="rotationChanged">Specifies whether the rotation changed.</param>
        /// <param name="lossyScaleChanged">Specifies whether the lossyScale changed.</param>
        /// <returns><c>true</c> - if equal; otherwise, <c>false</c>.</returns>
        public bool Equal(Vector3 position, Quaternion rotation, Vector3 lossyScale, out bool positionChanged, out bool rotationChanged, out bool lossyScaleChanged)
        {
            positionChanged = Position != position;
            rotationChanged = Rotation != rotation;
            lossyScaleChanged = LossyScale != lossyScale;

            return !positionChanged && !rotationChanged && !lossyScaleChanged;
        }
    }
}