using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using HightlanderSolutions.MeshCheckerEditorExtension.Async;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.ObjectBounds
{
    /// <summary>
    ///     Represents a manager of a local-aligned bounds.
    /// </summary>
    internal sealed class LocalBoundsManager : BoundsManager
    {
        /// <summary>
        ///     A backing field of the <see cref="UsedRotation"/> property.
        /// </summary>
        private Quaternion InnerUsedRotation = Quaternion.identity;

        /// <inheritdoc />
        public override Quaternion UsedRotation
        {
            get
            {
                return InnerUsedRotation;
            }

            set
            {
                // If rotation changed
                if (InnerUsedRotation != value)
                {
                    // Update new rotation
                    InnerUsedRotation = value;

                    // Mark all ObjectBounds as changed
                    List<ObjectBounds> CalculatedBoundsCopy = new List<ObjectBounds>(CalculatedBounds);
                    List<ObjectBounds> ChangedBoundsCopy = new List<ObjectBounds>(ChangedBounds);
                    List<ObjectBounds> UnchangedBounds = CalculatedBoundsCopy.Except(ChangedBoundsCopy).ToList();
                    ChangedBounds.AddRange(UnchangedBounds);
                }
            }
        }

        /// <inheritdoc />
        public override Bounds GetAlignedBounds()
        {
            if (IsBoundsAreActual)
            {
                return GetAlignedBounds(TotalBounds);
            }

            return new Bounds();
        }

        /// <inheritdoc />
        public override Bounds GetAlignedBounds(Bounds input)
        {
            return new Bounds(UsedRotation * input.center, input.size);
        }

        /// <inheritdoc />
        [NotNull]
        protected override AsyncOperation<BoundsCalculationResult> CalculateBoundsAsync(
            [CanBeNull] Action<BoundsCalculationResult, AsyncOperationResultState> callback)
        {
            // Create async operation instance.
            AsyncOperation<BoundsCalculationResult> AsyncOperation = new AsyncOperation<BoundsCalculationResult>(1, callback);

            // End operation if objects storage are empty.
            if (CalculatedBounds.Count <= 0 && ChangedBounds.Count <= 0)
            {
                AsyncOperation.End(AsyncOperationResultState.Success, new BoundsCalculationResult());
                return AsyncOperation;
            }

            List<ObjectBounds> CalculatedBoundsCopy = new List<ObjectBounds>(CalculatedBounds);
            List<ObjectBounds> ChangedBoundsCopy = new List<ObjectBounds>(ChangedBounds);

            // Calculate iterations count.
            int IterationsCount = GetChangedObjectsVerticesCount(ChangedBoundsCopy);
            IterationsCount += CalculatedBoundsCopy.Count;

            AsyncOperation.SetTargetProgress(IterationsCount);

            // Start new thread.
            ThreadPool.QueueUserWorkItem(arg =>
            {
                BoundsCalculationResult CalculationResult = new BoundsCalculationResult();
                try
                {
                    Quaternion UsedRotationCopy = UsedRotation;
                    foreach (ObjectBounds Item in ChangedBoundsCopy)
                    {
                        if (AsyncOperation.Aborted)
                        {
                            return;
                        }

                        if (Item == null)
                        {
                            ChangedBounds.Remove(null);
                            continue;
                        }

                        if (Item.MeshData.Broken)
                        {
                            continue;
                        }

                        Bounds ResultBounds = CalculateObjectBounds(UsedRotationCopy, Item, AsyncOperation);

                        if (!AsyncOperation.Aborted)
                        {
                            if (ChangedBounds.Remove(Item))
                            {
                                Item.SetBounds(ResultBounds);
                            }
                        }
                        else
                        {
                            // if async operation was aborted, need to stop thread work.
                            AsyncOperation.End(AsyncOperationResultState.Aborted, CalculationResult);
                            return;
                        }
                    }

                    // Update total bounds.
                    foreach (ObjectBounds Item in CalculatedBoundsCopy)
                    {
                        if (AsyncOperation.Aborted)
                        {
                            return;
                        }

                        AsyncOperation.IncrementProgress();

                        if (Item.MeshData.Broken
                            || !Item.BoundsExists)
                        {
                            continue;
                        }

                        if (!CalculationResult.IsTotalBoundsExists)
                        {
                            CalculationResult.TotalBounds = Item.Bounds;
                            continue;
                        }

                        CalculationResult.Encapsulate(Item.Bounds);
                    }

                    AsyncOperation.End(AsyncOperationResultState.Success, CalculationResult);
                }
                catch (Exception E)
                {
                    Debug.LogError("[MeshChecker editor extension] Sorry, but MeshChecker catch an exception in another thread. Please, send info to the developer. \n" + E);
                    AsyncOperation.End(AsyncOperationResultState.Error, CalculationResult);
                }
            });

            return AsyncOperation;
        }
    }
}