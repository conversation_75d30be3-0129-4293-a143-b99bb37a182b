using System;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.ObjectBounds
{
    /// <summary>
    ///     Represents an input data of the target Mesh for calculations.
    /// </summary>
    internal sealed class MeshData
    {
        /// <summary>
        ///     Gets a name of the mesh.
        /// </summary>
        [PublicAPI]
        public string Name { get; private set; }

        /// <summary>
        ///     The transform of the target object.
        /// </summary>
        [PublicAPI]
        public readonly Transform Transform;

        /// <summary>
        ///     The MeshFilter component of the target object.
        /// </summary>
        [PublicAPI]
        public readonly AbstractMeshFilter MeshFilter;

        /// <summary>
        ///     Gets the mesh that referenced by the target object.
        /// </summary>
        [PublicAPI]
        public Mesh Mesh { get; private set; }

        /// <summary>
        ///     Gets the position of the target object.
        /// </summary>
        [PublicAPI]
        public Vector3 Position
        {
            get
            {
                return TransformState.Position;
            }
        }

        /// <summary>
        ///     Gets the rotation of the target object.
        /// </summary>
        [PublicAPI]
        public Quaternion Rotation
        {
            get
            {
                return TransformState.Rotation;
            }
        }

        /// <summary>
        ///     Gets the scale of the target object.
        /// </summary>
        [PublicAPI]
        public Vector3 Scale
        {
            get
            {
                return TransformState.LossyScale;
            }
        }

        /// <summary>
        ///     Gets a vertices array of the <see cref="Mesh"/>.
        /// </summary>
        [PublicAPI]
        [CanBeNull]
        public Vector3[] Vertices { get; private set; }

        /// <summary>
        ///     The state of the object transform.
        /// </summary>
        [NotNull]
        private TransformState TransformState;

        /// <summary>
        ///     Gets a value, that represents the unique state of MeshData.
        ///     <para>
        ///         Changes if some of data changed.
        ///     </para>
        /// </summary>
        [PublicAPI]
        public Guid StateId { get; private set; }

        /// <summary>
        ///     Gets a value indicating whether a position, rotation or scale changed.
        /// </summary>
        [PublicAPI]
        public bool Changed
        {
            get
            {
                if (Transform == null)
                {
                    return true;
                }

                // Position, rotation or scale changed
                if (!TransformState.Equal(Transform))
                {
                    return true;
                }

                return false;
            }
        }

        /// <summary>
        ///     Gets a value indicating whether a Transform, MeshFilter, Mesh or Vertices are missed.
        /// </summary>
        [PublicAPI]
        public bool Broken
        {
            get
            {
                if (Transform == null)
                {
                    return true;
                }

                if (MeshFilter == null)
                {
                    return true;
                }

                if (Mesh == null)
                {
                    return true;
                }

                if (Vertices == null || Vertices.Length == 0)
                {
                    return true;
                }

                return false;
            }
        }

        /// <summary>
        ///     Initializes a new instance of the <see cref="MeshData"/> class.
        /// </summary>
        /// <param name="transform">The transform.</param>
        /// <param name="meshFilter">The mesh filter.</param>
        /// <exception cref="ArgumentNullException"><paramref name="transform"/> or <paramref name="meshFilter"/> is <see langword="null"/></exception>
        [PublicAPI]
        public MeshData([NotNull] Transform transform, [NotNull] AbstractMeshFilter meshFilter)
        {
            if (transform == null)
            {
                throw new ArgumentNullException("transform");
            }

            if (meshFilter == null)
            {
                throw new ArgumentNullException("meshFilter");
            }

            Name = transform.name;
            Transform = transform;
            MeshFilter = meshFilter;
            TransformState = new TransformState(transform);

            UpdateMesh();
        }

        /// <summary>
        ///     Initializes a new instance of the <see cref="MeshData"/> class.
        /// </summary>
        /// <param name="meshFilter">The mesh filter.</param>
        /// <exception cref="ArgumentNullException"><paramref name="meshFilter" /> or transform is <see langword="null" /></exception>
        [PublicAPI]
        public MeshData([NotNull] AbstractMeshFilter meshFilter) : this(meshFilter.transform, meshFilter)
        {
        }

        /// <summary>
        ///     Updates the transform state.
        /// </summary>
        [PublicAPI]
        public void UpdateTransformState()
        {
            if (Transform == null)
            {
                return;
            }

            TransformState OldTransformState = TransformState;

            TransformState = new TransformState(Transform);

            if (!OldTransformState.Equal(TransformState))
            {
                UpdateStateHash();

                if (MeshFilter is MCSkinnedMeshRenderer)
                {
                    BakeAndPushSkinnedMeshData();
                }
            }
        }

        /// <summary>
        ///     Updates the mesh.
        /// </summary>
        [PublicAPI]
        public void UpdateMesh()
        {
            if (MeshFilter == null || MeshFilter.sharedMesh == null)
            {
                Mesh = null;
                Vertices = new Vector3[0];

                UpdateStateHash();
            }
            else
            {
                if (MeshFilter is MCMeshFilter)
                {
                    PushMeshFilterData();
                }
                else if (MeshFilter is MCSkinnedMeshRenderer)
                {
                    BakeAndPushSkinnedMeshData();
                }
            }
        }

        /// <summary>
        ///     Pushes <see cref="MCMeshFilter"/> data to the MeshData instance if <see cref="MeshFilter"/> is <see cref="MCMeshFilter"/>.
        /// </summary>
        private void PushMeshFilterData()
        {
            MCMeshFilter MCMeshFilter = MeshFilter as MCMeshFilter;
            if (MCMeshFilter == null || MCMeshFilter.MeshFilter == null)
            {
                return;
            }

            if (Mesh != MCMeshFilter.sharedMesh)
            {
                Mesh = MCMeshFilter.sharedMesh;
                Vertices = Mesh.vertices;

                UpdateStateHash();
            }
        }

        /// <summary>
        ///     Bakes a skinned mesh if the <see cref="MeshFilter"/> is <see cref="MCSkinnedMeshRenderer"/> and pushes their data to the MeshData instance.
        /// </summary>
        private void BakeAndPushSkinnedMeshData()
        {
            MCSkinnedMeshRenderer Filter = MeshFilter as MCSkinnedMeshRenderer;
            if (Filter == null || Filter.SkinnedMeshRenderer == null)
            {
                return;
            }

            Mesh OldMesh = Mesh;
            Mesh NewMesh = new Mesh();
            Filter.SkinnedMeshRenderer.BakeMesh(NewMesh);
            Mesh = NewMesh;
            Vertices = Mesh.vertices;

            if (OldMesh != Filter.sharedMesh)
            {
                UnityEngine.Object.DestroyImmediate(OldMesh);
            }
        }

        /// <summary>
        ///     Updates the state hash.
        /// </summary>
        private void UpdateStateHash()
        {
            StateId = new Guid();
        }
    }
}
