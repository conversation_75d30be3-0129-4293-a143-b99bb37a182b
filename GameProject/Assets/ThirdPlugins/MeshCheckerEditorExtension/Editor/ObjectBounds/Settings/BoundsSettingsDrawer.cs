using System;

using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;

using UnityEditor;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.ObjectBounds
{
    internal static class BoundsSettingsDrawer
    {
        /// <summary>
        ///     Draws a unit settings line in layout mode.
        /// </summary>
        internal static void DrawUnitSettingsLayout()
        {
            using (new MCGUI.HorizontalLayoutScope())
            {
                GUILayout.Label("1 Unit", GUILayout.ExpandWidth(false));
                GUILayout.FlexibleSpace();
                GUILayout.Label("=", GUILayout.ExpandWidth(false));
                GUILayout.FlexibleSpace();

                MCPreferences.UnitCoefficient = EditorGUILayout.FloatField(MCPreferences.UnitCoefficient);
                Enum EnumPopup = EditorGUILayout.EnumPopup(
                    string.Empty,
                    MCPreferences.CurrentUnitType,
                    GUILayout.Width(90f));
                if (EnumPopup != null)
                {
                    MCPreferences.CurrentUnitType = (UnitType)EnumPopup;
                }

                if (GUILayout.Button(
                    "Default",
                    EditorStyles.miniButton,
                    GUILayout.ExpandWidth(false)))
                {
                    MCPreferences.DropUnitCoefficientToDefault();
                }
            }
        }

        /// <summary>
        ///     Draws a <see cref="MCPreferences.SizesPrecisionSetting"/> line in layout mode.
        /// </summary>
        internal static void DrawSizesPrecisionSettingsLayout()
        {
            GUIContent SizesPrecisionContent = new GUIContent(string.Format("Sizes precision: {0} digits", MCPreferences.SizesPrecisionSetting));
            MCPreferences.SizesPrecisionSetting = EditorGUILayout.IntSlider(SizesPrecisionContent, MCPreferences.SizesPrecisionSetting, 0, 6);
        }

        /// <summary>
        ///     Draws a <see cref="MCPreferences.DrawBoundingBoxCenterSetting"/> line in layout mode.
        /// </summary>
        internal static void DrawDisplayBoundsCenterSettingsLayout()
        {
            MCPreferences.DrawBoundingBoxCenterSetting = EditorGUILayout.ToggleLeft("Display bounds center", MCPreferences.DrawBoundingBoxCenterSetting);
        }

        /// <summary>
        ///     Draws a <see cref="MCPreferences.DrawAxisSetting"/> line in layout mode.
        /// </summary>
        internal static void DrawDisplayAxisSettingsLayout()
        {
            MCPreferences.DrawAxisSetting = EditorGUILayout.ToggleLeft("Draw axis", MCPreferences.DrawAxisSetting);
        }

        /// <summary>
        ///     Draws a <see cref="MCPreferences.BoundingBoxColorSetting"/> line in layout mode.
        /// </summary>
        internal static void DrawBoundingBoxColorSettingsLayout()
        {
            MCPreferences.BoundingBoxColorSetting = EditorGUILayout.ColorField(new GUIContent("Bounding box color"), MCPreferences.BoundingBoxColorSetting);
        }
    }
}