using System;
using System.Collections.Generic;
using System.Linq;
using HightlanderSolutions.MeshCheckerEditorExtension.Async;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.ObjectBounds
{
    /// <summary>
    ///     Represents a manager of calculated bounds.
    /// </summary>
    internal abstract class BoundsManager
    {
        /// <summary>
        ///     The sorting type enumerator, used in sorting of calculation results.
        /// </summary>
        public enum SortingType
        {
            /// <summary>
            ///     Do not sort.
            /// </summary>
            None,

            /// <summary>
            ///     Sort by the name.
            /// </summary>
            Name,

            /// <summary>
            ///     Sort by the x-axis sizes.
            /// </summary>
            SizeX,

            /// <summary>
            ///     Sort by the y-axis sizes.
            /// </summary>
            SizeY,

            /// <summary>
            ///     Sort by the z-axis sizes.
            /// </summary>
            SizeZ
        }

        /// <summary>
        ///     Represents a process state of the manager.
        /// </summary>
        private enum State
        {
            /// <summary>
            ///     The manager is initialized.
            /// </summary>
            Initialized,

            /// <summary>
            ///     The manager performs calculations.
            /// </summary>
            PerformCalculations,

            /// <summary>
            ///     The manager idle.
            /// </summary>
            Idle
        }

        /// <summary>
        ///     A current sorting type.
        /// </summary>
        private SortingType Sorting;

        /// <summary>
        ///     A value indicating whether a sorting result ordered ascending.
        /// </summary>
        private bool SortingAscending;

        #region Events

        /// <summary>
        ///     Occurs when a bounds calculation completed.
        ///     <para>
        ///         The event raises not in the main thread.
        ///     </para>
        /// </summary>
        public event Action BoundsCalculcationCompletedEvent;

        /// <summary>
        ///     Occurs when a bounds calculation completed.
        ///     <para>
        ///         The event raises in the main thread.
        ///     </para>
        /// </summary>
        public event Action BoundsCalculcationCompletedMainThreadEvent;

        /// <summary>
        ///     Occurs when a progress changed.
        /// </summary>
        public event InhibitedAsyncOperation<BoundsCalculationResult>.ProgressChangedDelegate ProgressChangedEvent;

        /// <summary>
        ///     A value indicating whether a calculation completed 
        ///     and the manager waits for raising <see cref="BoundsCalculcationCompletedMainThreadEvent"/> in the main thread.
        /// </summary>
        private bool WaitForBoundsCalculcationCompletedMainThreadEvent;

        #endregion

        /// <summary>
        ///     A backing field of the <see cref="TotalBounds"/> property.
        /// </summary>
        private Bounds? InnerTotalBounds;

        /// <summary>
        ///     Gets the bounds of all selected objects.
        /// </summary>
        public Bounds TotalBounds
        {
            get
            {
                if (InnerTotalBounds != null)
                {
                    return (Bounds)InnerTotalBounds;
                }
                else
                {
                    return new Bounds();
                }
            }
        }

        /// <summary>
        ///     Gets an inhibited async operation instance of a world bounds calculation.
        /// </summary>
        public BoundsCalculationAsyncOperation InhibitedCalculationAsyncOperation
        {
            get;
            private set;
        }

        /// <summary>
        ///     Gets a value indicating whether a calculation ended and the total bounds exists.
        /// </summary>
        public bool IsBoundsAreActual
        {
            get
            {
                return (CurrentState == State.Idle) && (InnerTotalBounds != null);
            }
        }

        /// <summary>
        ///     Gets or sets the rotation which used in calculations.
        /// </summary>
        public abstract Quaternion UsedRotation { get; set; }

        /// <summary>
        ///     The list of bounds that already calculated.
        /// </summary>
        [NotNull]
        protected readonly List<ObjectBounds> CalculatedBounds = new List<ObjectBounds>();

        /// <summary>
        ///     The list of bounds that was changed since last update.
        /// </summary>
        [NotNull]
        protected readonly List<ObjectBounds> ChangedBounds = new List<ObjectBounds>();

        /// <summary>
        ///     A backing of the <see cref="CalculateWorldBoundsAsyncOperation"/> property.
        /// </summary>
        private AsyncOperation<BoundsCalculationResult> InnerCalculateWorldBoundsAsyncOperation;

        /// <summary>
        ///     Gets or sets an async operation instance of a world bounds calculation.
        /// </summary>
        [CanBeNull]
        private AsyncOperation<BoundsCalculationResult> CalculateWorldBoundsAsyncOperation
        {
            get
            {
                return InnerCalculateWorldBoundsAsyncOperation;
            }

            set
            {
                // Abort inhibited async operation.
                if (InhibitedCalculationAsyncOperation != null)
                {
                    if (!InhibitedCalculationAsyncOperation.IsEnded)
                    {
                        InhibitedCalculationAsyncOperation.Abort();
                    }

                    InhibitedCalculationAsyncOperation = null;
                }

                // Abort current calculation async operation if exist and not ended.
                if (InnerCalculateWorldBoundsAsyncOperation != null && !InnerCalculateWorldBoundsAsyncOperation.IsEnded)
                {
                    InnerCalculateWorldBoundsAsyncOperation.Abort();
                }

                InnerCalculateWorldBoundsAsyncOperation = value;

                // Create new inhibited async operation.
                if (InnerCalculateWorldBoundsAsyncOperation != null)
                {
                    InhibitedCalculationAsyncOperation = new BoundsCalculationAsyncOperation(InnerCalculateWorldBoundsAsyncOperation);
                    InhibitedCalculationAsyncOperation.ProgressChangedEvent += ProgressChangedEvent;
                }
            }
        }

        /// <summary>
        ///     Initializes a new instance of the <see cref="BoundsManager"/> class.
        /// </summary>
        protected BoundsManager()
        {
            StateInitialized();
        }

        /// <summary>
        ///     Releases the bounds manager.
        /// </summary>
        public void Release()
        {
            StateInitialized();
        }

        /// <summary>
        ///     Gets aligned bounds of selection.
        /// </summary>
        /// <returns>The aligned bounds of selection.</returns>
        public abstract Bounds GetAlignedBounds();

        /// <summary>
        ///     Align specified bounds and return.
        /// </summary>
        /// <param name="input">The input bounds.</param>
        /// <returns>The aligned bounds of selection.</returns>
        public abstract Bounds GetAlignedBounds(Bounds input);

        /// <summary>
        ///     Update the bounds manager. Must be called from the main thread.
        /// </summary>
        public void Update()
        {
            if (WaitForBoundsCalculcationCompletedMainThreadEvent
                && BoundsCalculcationCompletedMainThreadEvent != null)
            {
                try
                {
                    BoundsCalculcationCompletedMainThreadEvent();
                }
                catch (Exception E)
                {
                    Debug.LogException(E);
                }

                WaitForBoundsCalculcationCompletedMainThreadEvent = false;
            }

            // Check changed objects
            bool MeshDataChanged = false;
            List<ObjectBounds> CalculatedBoundsCopy = new List<ObjectBounds>(CalculatedBounds);

            foreach (ObjectBounds Item in CalculatedBoundsCopy)
            {
                if (Item.MeshData.Broken)
                {
                    Item.MeshData.UpdateMesh();

                    if (ChangedBounds.Contains(Item))
                    {
                        ChangedBounds.Remove(Item);
                        MeshDataChanged = true;
                    }

                    if (Item.BoundsExists)
                    {
                        Item.Reset();
                        MeshDataChanged = true;
                    }
                }
                else
                {
                    if (Item.MeshData.Changed)
                    {
                        Item.MeshData.UpdateMesh();
                        Item.MeshData.UpdateTransformState();

                        if (!ChangedBounds.Contains(Item))
                        {
                            ChangedBounds.Add(Item);
                            Item.Reset();
                        }

                        MeshDataChanged = true;
                    }
                }
            }

            if (MeshDataChanged)
            {
                StatePerformCalculations();
            }
        }

        /// <summary>
        ///     Updates the list of observed meshes.
        /// </summary>
        /// <param name="meshFiltersList">The list of MeshFilters.</param>
        public void ChangeObservedMeshes([NotNull] IEnumerable<AbstractMeshFilter> meshFiltersList)
        {
            List<AbstractMeshFilter> MeshFiltersListCopy = new List<AbstractMeshFilter>(meshFiltersList);

            if (MeshFiltersListCopy.Count <= 1)
            {
                InnerTotalBounds = null;
            }

            List<ObjectBounds> CalculatedBoundsCopy = new List<ObjectBounds>(CalculatedBounds);
            bool SomeObservedObjectsLost = false;
            bool ReceiveNewObservedObjects = false;

            // Remove old MeshFilters, that are not actual anymore.
            foreach (ObjectBounds Item in CalculatedBoundsCopy)
            {
                if (!MeshFiltersListCopy.Contains(Item.MeshData.MeshFilter))
                {
                    // Remove from the storage.
                    CalculatedBounds.Remove(Item);

                    // Remove from the list of changed elements.
                    ChangedBounds.Remove(Item);

                    SomeObservedObjectsLost = true;
                }
            }

            // Update copy
            if (SomeObservedObjectsLost)
            {
                CalculatedBoundsCopy = new List<ObjectBounds>(CalculatedBounds);
            }

            // Add new observed meshes
            foreach (AbstractMeshFilter MeshFilterItem in MeshFiltersListCopy)
            {
                if (CalculatedBoundsCopy.All(e => e.MeshData.MeshFilter != MeshFilterItem))
                {
                    MeshData NewMeshData = new MeshData(MeshFilterItem);

                    if (NewMeshData.Broken)
                    {
                        continue;
                    }

                    ObjectBounds NewObjectBounds = new ObjectBounds(NewMeshData);

                    CalculatedBounds.Add(NewObjectBounds);

                    if (!NewObjectBounds.MeshData.Broken)
                    {
                        ChangedBounds.Add(NewObjectBounds);
                        ReceiveNewObservedObjects = true;
                    }
                }
            }

            if (SomeObservedObjectsLost || ReceiveNewObservedObjects)
            {
                StatePerformCalculations();
            }
        }

        /// <summary>
        ///     Gets pairs of calculated bounds and owner transforms.
        /// </summary>
        /// <returns>The list of pairs which represents transforms and their calculated bounds.</returns>
        [NotNull]
        public List<KeyValuePair<Transform, Bounds>> GetTransformBoundsPairs()
        {
            List<KeyValuePair<Transform, Bounds>> ResultList = new List<KeyValuePair<Transform, Bounds>>();

            foreach (ObjectBounds Item in CalculatedBounds)
            {
                if (Item == null)
                {
                    continue;
                }

                if (Item.BoundsExists)
                {
                    ResultList.Add(new KeyValuePair<Transform, Bounds>(Item.MeshData.Transform, Item.Bounds));
                }
            }

            return ResultList;
        }

        #region Sorting

        /// <summary>
        ///     Sets the sorting type or toggles ascending/descending flag.
        /// </summary>
        /// <param name="type">The sorting type.</param>
        public void SetSorting(SortingType type)
        {
            // If the sorting type are not changed - then just switch the order flag.
            if (Sorting == type)
            {
                SortingAscending = !SortingAscending;
            }
            else
            {
                // If the sorting type are new - then change the type and drop an order flag to descending.
                Sorting = type;
                SortingAscending = false;
            }
        }

        /// <summary>
        ///     Gets sorted transform and bounds pairs and sorts the calculation result if needed.
        /// </summary>
        /// <returns>Returns sorted list of transform-bounds pairs. Or unsorted if sorting type are none.</returns>
        [NotNull]
        public List<KeyValuePair<Transform, Bounds>> GetSortedTransformBoundsPairs()
        {
            if (Sorting == SortingType.None)
            {
                // If sorting not needed - create the list and return as is.
                return (from Item in CalculatedBounds
                        where Item.BoundsExists
                        select new KeyValuePair<Transform, Bounds>(Item.MeshData.Transform, Item.Bounds)).ToList();
            }

            // Filter storage by BoundsExists flag
            List<ObjectBounds> FilteredObjectBoundsStorage = CalculatedBounds.Where(e => e != null && e.BoundsExists).ToList();

            // Sort by current sorting type
            switch (Sorting)
            {
                case SortingType.Name:
                    FilteredObjectBoundsStorage = SortingAscending
                        ? FilteredObjectBoundsStorage.OrderByDescending(e => e.Name).ToList()
                        : FilteredObjectBoundsStorage.OrderBy(e => e.Name).ToList();
                    break;

                case SortingType.SizeX:
                    FilteredObjectBoundsStorage = SortingAscending
                        ? FilteredObjectBoundsStorage.OrderBy(e => e.Bounds.size.x).ToList()
                        : FilteredObjectBoundsStorage.OrderByDescending(e => e.Bounds.size.x).ToList();
                    break;

                case SortingType.SizeY:
                    FilteredObjectBoundsStorage = SortingAscending
                        ? FilteredObjectBoundsStorage.OrderBy(e => e.Bounds.size.y).ToList()
                        : FilteredObjectBoundsStorage.OrderByDescending(e => e.Bounds.size.y).ToList();
                    break;

                case SortingType.SizeZ:
                    FilteredObjectBoundsStorage = SortingAscending
                        ? FilteredObjectBoundsStorage.OrderBy(e => e.Bounds.size.z).ToList()
                        : FilteredObjectBoundsStorage.OrderByDescending(e => e.Bounds.size.z).ToList();
                    break;
            }

            // Create pairs list from sorted list and return
            return (from Item in FilteredObjectBoundsStorage
                    select new KeyValuePair<Transform, Bounds>(Item.MeshData.Transform, Item.Bounds)).ToList();
        }

        /// <summary>
        ///     Gets a column title with special symbols based on the current sorting.
        /// </summary>
        /// <param name="columnTitle">The title of a column.</param>
        /// <param name="sortingType">The sorting type, corresponding to the column.</param>
        /// <returns>
        ///     <para>
        ///         Returns total column title with special symbols «▼» or «▲»;
        ///     </para>
        ///     <para>
        ///         Example: «Objects ▲» when columnTitle are «Polygons», current sorting type equals to sortingType argument and sorting is ascending.
        ///     </para>
        /// </returns>
        [PublicAPI]
        [NotNull]
        public string GetColumnTitle([CanBeNull]string columnTitle, SortingType sortingType)
        {
            return string.Concat(columnTitle, Sorting != sortingType ? string.Empty : SortingAscending ? " ▲" : " ▼");
        }

        /// <summary>
        ///     <para>
        ///         Gets the total bounds of target.
        ///     </para>
        ///     <para>
        ///         Method encapsulates all bounds that are the children of the target or target self. Bounds should be already calculated.
        ///     </para>
        /// </summary>
        /// <param name="target">The target.</param>
        /// <returns>Total bounds of target.</returns>
        public Bounds GetTotalObjectBounds(Transform target)
        {
            var ChildBounds = CalculatedBounds.Where(bounds => bounds.MeshData.Transform.IsChildOf(target));

            Bounds? ResultBounds = null;

            foreach (ObjectBounds Bounds in ChildBounds)
            {
                if (ResultBounds == null)
                {
                    ResultBounds = Bounds.Bounds;
                }
                else
                {
                    Bounds TempBounds = ResultBounds.Value;
                    TempBounds.Encapsulate(Bounds.Bounds);
                    ResultBounds = TempBounds;
                }
            }

            return ResultBounds == null ? new Bounds() : GetAlignedBounds(ResultBounds.Value);
        }

        #endregion

        /// <summary>
        ///     Gets the vertices count of changed objects.
        /// </summary>
        /// <param name="changedObjectBounds">The collection of changed object bounds.</param>
        /// <returns>Count of vertices in changed objects.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="changedObjectBounds"/> is <see langword="null"/></exception>
        protected static int GetChangedObjectsVerticesCount([NotNull]List<ObjectBounds> changedObjectBounds)
        {
            if (changedObjectBounds == null)
            {
                throw new ArgumentNullException("changedObjectBounds");
            }

            int IterationsCount = 0;
            foreach (ObjectBounds Item in changedObjectBounds)
            {
                // If MeshData not changed or lost Transform\MeshFilter - ignore it (doesn't affect to calculation time)
                if (Item == null
                    || Item.MeshData.Broken)
                {
                    // Bounds already calculated and doesn't changed since last calculation.
                    continue;
                }

                IterationsCount += Item.MeshData.Vertices.Length;
            }

            return IterationsCount;
        }

        /// <summary>
        ///     Calculates bounds of an object.
        /// </summary>
        /// <param name="target">The target object.</param>
        /// <param name="asyncOperation">The instance of an async operation.</param>
        /// <returns>Calculated bounds of the target object.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="target"/> or <paramref name="asyncOperation"/> is <see langword="null"/></exception>
        /// <exception cref="InvalidOperationException">Invalid mesh data.</exception>
        protected static Bounds CalculateObjectBounds([NotNull]ObjectBounds target, [NotNull]AsyncOperation<BoundsCalculationResult> asyncOperation)
        {
            return CalculateObjectBounds(Quaternion.identity, target, asyncOperation);
        }

        /// <summary>
        ///     Calculates bounds of an object.
        /// </summary>
        /// <param name="rotation">The rotation of bounds.</param>
        /// <param name="target">The target object.</param>
        /// <param name="asyncOperation">The instance of an async operation.</param>
        /// <returns>Calculated bounds of the target object.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="target"/> or <paramref name="asyncOperation"/> is <see langword="null"/></exception>
        /// <exception cref="InvalidOperationException">Invalid mesh data.</exception>
        protected static Bounds CalculateObjectBounds(Quaternion rotation, [NotNull]ObjectBounds target, [NotNull]AsyncOperation<BoundsCalculationResult> asyncOperation)
        {
            if (target == null)
            {
                throw new ArgumentNullException("target");
            }

            if (asyncOperation == null)
            {
                throw new ArgumentNullException("asyncOperation");
            }

            if (target.MeshData.Broken)
            {
                throw new InvalidOperationException("Invalid mesh data.");
            }

            bool Rotated = rotation != Quaternion.identity;
            Quaternion InvertedRotation = rotation.Inverse();

            // Initialize result bounds.
            Bounds ResultBounds;
            Vector3 VertexPosition = GetVertexPosition(target.MeshData, 0);

            if (Rotated)
            {
                ResultBounds = new Bounds(InvertedRotation * VertexPosition, Vector3.zero);
            }
            else
            {
                ResultBounds = new Bounds(VertexPosition, Vector3.zero);
            }

            // All vertices cycle.
            for (int Index = 1; Index < target.MeshData.Vertices.Length; Index++)
            {
                if (asyncOperation.Aborted)
                {
                    return default(Bounds);
                }

                VertexPosition = GetVertexPosition(target.MeshData, Index);

                if (Rotated)
                {
                    // Update result bounds with iteration vertex.
                    ResultBounds.Encapsulate(InvertedRotation * VertexPosition);
                }
                else
                {
                    ResultBounds.Encapsulate(VertexPosition);
                }

                // Increment async operation progress counter.
                asyncOperation.IncrementProgress();
            }

            return ResultBounds;
        }

        /// <summary>
        ///     Gets the vertex position that has the specified index.
        /// </summary>
        /// <param name="meshData">The mesh data.</param>
        /// <param name="index">The vertex index.</param>
        /// <returns>
        ///     The position of vertex in scene.
        /// </returns>
        /// <exception cref="ArgumentNullException"><paramref name="meshData"/> is <see langword="null"/></exception>
        /// <exception cref="ArgumentException">The mesh data must not be broken.</exception>
        protected static Vector3 GetVertexPosition([NotNull] MeshData meshData, int index)
        {
            if (meshData == null)
            {
                throw new ArgumentNullException("meshData");
            }

            if (meshData.Broken)
            {
                throw new ArgumentException("The mesh data must not be broken.", "meshData");
            }

            Vector3 LocalVertexPosition = meshData.Rotation * meshData.Vertices[index].ApplyScale(meshData.Scale);
            Vector3 VertexPosition = meshData.Position + LocalVertexPosition;
            return VertexPosition;
        }

        /// <summary>
        ///     Calculates bounds asynchronously.
        /// </summary>
        /// <param name="callback">The callback which invokes when the operation ends.</param>
        /// <returns>The async operation instance.</returns>
        protected abstract AsyncOperation<BoundsCalculationResult> CalculateBoundsAsync(Action<BoundsCalculationResult, AsyncOperationResultState> callback);

        #region FSM realisation

        /// <summary>
        ///     A current state of a manager.
        /// </summary>
        private State CurrentState;

        /// <summary>
        ///     Sets a new state of a manager.
        /// </summary>
        /// <param name="newState">The new state.</param>
        private void SetState(State newState)
        {
            // Handle state ending here.
            ////switch (CurrentState)
            ////{
            ////}

            CurrentState = newState;
        }

        /// <summary>
        ///     Sets an <see cref="State.Initialized"/> state.
        /// </summary>
        private void StateInitialized()
        {
            SetState(State.Initialized);

            InnerTotalBounds = null;
            CalculatedBounds.Clear();
            ChangedBounds.Clear();
            BoundsCalculcationCompletedEvent = null;
            ProgressChangedEvent = null;
        }

        /// <summary>
        ///     Sets a <see cref="State.PerformCalculations"/> state.
        /// </summary>
        private void StatePerformCalculations()
        {
            SetState(State.PerformCalculations);

            AddBoundlessObjectsToChanged();

            CalculateWorldBoundsAsyncOperation = CalculateBoundsAsync(CalculateBoundsAsyncOperationCallback);
        }

        /// <summary>
        ///     Sets a <see cref="State.Idle"/> state.
        /// </summary>
        private void StateIdle()
        {
            if (CurrentState != State.PerformCalculations)
            {
                return;
            }

            SetState(State.Idle);

            if (BoundsCalculcationCompletedEvent != null)
            {
                try
                {
                    BoundsCalculcationCompletedEvent();
                }
                catch (Exception E)
                {
                    Debug.LogException(E);
                }

                WaitForBoundsCalculcationCompletedMainThreadEvent = true;
            }
        }

        #endregion

        /// <summary>
        ///     Process the asynchronous operation result of a bounds calculation.
        /// </summary>
        /// <param name="result">The result of async operation.</param>
        /// <param name="resultState">The operation result state.</param>
        private void CalculateBoundsAsyncOperationCallback([NotNull]BoundsCalculationResult result, AsyncOperationResultState resultState)
        {
            if (resultState == AsyncOperationResultState.Error)
            {
                Debug.LogError("[MeshChecker editor extension] Failed to calculate world bounds asynchronously.");
                return;
            }

            if (resultState == AsyncOperationResultState.Aborted)
            {
                return;
            }

            if (resultState == AsyncOperationResultState.Success)
            {
                if (result.IsTotalBoundsExists)
                {
                    InnerTotalBounds = result.TotalBounds;
                }
                else
                {
                    InnerTotalBounds = null;
                }

                StateIdle();
            }
        }

        /// <summary>
        ///     Adds all object that doesn't have bounds and not broken to the <see cref="ChangedBounds"/>.
        /// </summary>
        /// <seealso cref="ObjectBounds.BoundsExists"/>
        /// <seealso cref="MeshData.Broken"/>
        private void AddBoundlessObjectsToChanged()
        {
            List<ObjectBounds> CalculatedBoundsCopy = new List<ObjectBounds>(CalculatedBounds);
            List<ObjectBounds> ChangedBoundsCopy = new List<ObjectBounds>(ChangedBounds);

            List<ObjectBounds> UncalculatedBounds = CalculatedBoundsCopy
                .Where(item => !item.MeshData.Broken && !item.BoundsExists)
                .Except(ChangedBoundsCopy).ToList();
            ChangedBounds.AddRange(UncalculatedBounds);
        }
    }
}