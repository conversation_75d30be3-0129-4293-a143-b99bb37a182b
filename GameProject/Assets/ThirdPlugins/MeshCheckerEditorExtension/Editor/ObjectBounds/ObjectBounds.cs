using System;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.ObjectBounds
{
    /// <summary>
    ///     Represents a bounds of an object.
    /// </summary>
    internal sealed class ObjectBounds
    {
        /// <summary>
        ///     The mesh data.
        /// </summary>
        [NotNull]
        public readonly MeshData MeshData;

        /// <summary>
        ///     The object name.
        /// </summary>
        public readonly string Name;

        /// <summary>
        ///     Gets a value indicating whether bounds exists.
        /// </summary>
        public bool BoundsExists { get; private set; }

        /// <summary>
        ///     Gets a bounds of the mesh.
        /// </summary>
        public Bounds Bounds { get; private set; }

        /// <summary>
        ///     Initializes a new instance of the <see cref="ObjectBounds"/> class.
        /// </summary>
        /// <param name="meshData">The mesh data.</param>
        /// <exception cref="ArgumentNullException"><paramref name="meshData"/> is <see langword="null"/></exception>
        /// <exception cref="ArgumentException">MeshData broken.</exception>
        public ObjectBounds([NotNull] MeshData meshData)
        {
            if (meshData == null)
            {
                throw new ArgumentNullException("meshData");
            }

            if (meshData.Broken)
            {
                throw new ArgumentException("MeshData broken.");
            }

            MeshData = meshData;
            BoundsExists = false;
            Bounds = default(Bounds);
            Name = MeshData.Transform.name;
        }

        /// <summary>
        ///     Sets new bounds.
        /// </summary>
        /// <param name="newBounds">The new bounds.</param>
        public void SetBounds(Bounds newBounds)
        {
            Bounds = newBounds;
            BoundsExists = true;
        }

        /// <summary>
        ///     Resets the bounds data.
        /// </summary>
        public void Reset()
        {
            BoundsExists = false;
            Bounds = default(Bounds);
        }
    }
}
