using System;
using System.Collections.Generic;
using System.Linq;
using HightlanderSolutions.MeshCheckerEditorExtension.Checker;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;
using HightlanderSolutions.Utilities;
using JetBrains.Annotations;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Represents a window that visualizes mesh data using shading option and if a tool selected using the tool.
    /// </summary>
    /// <seealso cref="MeshAnalysisShadingOption"/>
    /// <seealso cref="MeshAnalysisTool"/>
    [PublicAPI]
    public class MeshAnalysisWindow : EditorWindow
    {
        /// <summary>
        ///     The instance of a mesh analysis window.
        /// </summary>
        private static MeshAnalysisWindow Instance;

        /// <summary>
        ///     Raised when the window repaints.
        /// </summary>
        public static event Action RepaintEvent = delegate { };

        /// <summary>
        ///     The window Id.
        /// </summary>
        private readonly Guid Id = Guid.NewGuid();

        /// <summary>
        ///     Gets a camera which used to draw meshes in Mesh Analysis window.
        /// </summary>
        public static Camera CurrentCamera
        {
            get { return Instance.PreviewRenderUtilityCamera; }
        }

        /// <summary>
        ///     Gets the position of a camera orbit center.
        /// </summary>
        /// <exception cref="InvalidOperationException" accessor="get">MeshAnalysisWindow not initialized properly.</exception>
        [PublicAPI]
        public Vector3 FocusPosition
        {
            get
            {
                if (CameraController == null)
                {
                    throw new InvalidOperationException("MeshAnalysisWindow not initialized properly.");
                }

                return CameraController.OrbitCenter;
            }
        }

        /// <summary>
        ///     The backing field of the <see cref="Mesh"/> property.
        /// </summary>
        [CanBeNull]
        private Mesh InnerMesh;

        /// <summary>
        ///     Gets or sets a visualized mesh.
        /// </summary>
        [CanBeNull]
        private Mesh Mesh
        {
            get
            {
                return InnerMesh;
            }

            set
            {
                if (value != null)
                {
                    InnerMesh = value;
                    MeshVertices = InnerMesh.vertices;
                }
            }
        }

        /// <summary>
        ///     Vertices of a visualized mesh.
        /// </summary>
        [CanBeNull]
        private Vector3[] MeshVertices;

        /// <summary>
        ///     The render utility which used to draw mesh on window.
        /// </summary>
        [CanBeNull]
        private PreviewRenderUtility PreviewRenderUtility;

        /// <summary>
        ///     The cached scale factor of the <see cref="PreviewRenderUtility"/>.
        /// </summary>
        private float ScaleFactor;

        /// <summary>
        ///     The controller of a camera.
        /// </summary>
        [CanBeNull]
        private OrbitCameraController CameraController;

        /// <summary>
        ///     The current mouse scroll value.
        /// </summary>
        private float MouseScroll;

        /// <summary>
        ///     The mesh bounds.
        /// </summary>
        private Bounds MeshBounds;

        /// <summary>
        ///     The index of a currently selected shading option.
        /// </summary>
        private int SelectedShadingOption;

        /// <summary>
        ///     The backing field of the <see cref="ShadingOptions"/> property.
        /// </summary>
        [CanBeNull]
        private MeshAnalysisShadingOption[] InnerShadingOptions;

        /// <summary>
        ///     Gets or sets an array of shading options.
        /// </summary>
        [NotNull]
        private MeshAnalysisShadingOption[] ShadingOptions
        {
            get
            {
                if (InnerShadingOptions == null)
                {
                    if (Mesh == null)
                    {
                        throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
                    }

                    List<MeshAnalysisShadingOption> FindedShadingOptions =
                        ReflectionUtilities.GetClassWithAttribute<MeshAnalysisShadingOption, MeshAnalysisShadingAttribute>().ToList();

                    IEnumerable<MeshAnalysisShadingGroup> FindedShadingGroups =
                        ReflectionUtilities.GetClassWithAttribute<MeshAnalysisShadingGroup, MeshAnalysisShadingGroupAttribute>();

                    foreach (MeshAnalysisShadingGroup ShadingGroup in FindedShadingGroups)
                    {
                        if (ShadingGroup == null)
                        {
                            continue;
                        }

                        FindedShadingOptions.AddRange(ShadingGroup.GetOptions(Mesh));
                    }

                    FindedShadingOptions = FindedShadingOptions.Where(o => o != null)
                                                               .OrderBy(o => o.Priority).ToList();

                    List<MeshAnalysisShadingOption> FindedShadingOptionsCopy = new List<MeshAnalysisShadingOption>(FindedShadingOptions);
                    foreach (MeshAnalysisShadingOption ShadingOption in FindedShadingOptionsCopy)
                    {
                        try
                        {
                            ShadingOption.Initialize(Mesh);
                        }
                        catch (Exception E)
                        {
                            Debug.LogError(string.Concat("[MeshAnalysis] '", ShadingOption.Title, "' throws exception on initialize. Excluded from shading options list.\n", E.ToString()));
                            FindedShadingOptions.Remove(ShadingOption);
                        }
                    }

                    // If no shading options available then add default.
                    if (FindedShadingOptions.Count == 0)
                    {
                        DefaultShadingOption ShadingOption = new DefaultShadingOption();
                        ShadingOption.Initialize(Mesh);
                        FindedShadingOptions.Add(ShadingOption);
                    }

                    InnerShadingOptions = FindedShadingOptions.ToArray();
                }

                return InnerShadingOptions;
            }

            set
            {
                InnerShadingOptions = value;
            }
        }

        /// <summary>
        ///     The menu that contains shading options.
        /// </summary>
        [CanBeNull]
        private GenericMenu ShadingMenu;

        /// <summary>
        ///     The index of a currently selected tool.
        /// </summary>
        private int SelectedTool;

        /// <summary>
        ///     The backing field of <see cref="Tools"/> property.
        /// </summary>
        [CanBeNull]
        private MeshAnalysisTool[] InnerTools;

        /// <summary>
        ///     Gets or sets an array of available tools.
        /// </summary>
        [NotNull]
        private MeshAnalysisTool[] Tools
        {
            get
            {
                if (InnerTools == null)
                {
                    if (Mesh == null)
                    {
                        throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
                    }

                    List<MeshAnalysisTool> FoundTools = new List<MeshAnalysisTool>
                    {
                        new EmptyMeshAnalysisTool("None", -1000)
                    };
                    FoundTools.AddRange(ReflectionUtilities.GetClassWithAttribute<MeshAnalysisTool, MeshAnalysisToolAttribute>()
                        .Where(e => e != null)
                        .OrderBy(e => e.Priority));

                    List<MeshAnalysisTool> FoundToolsCopy = new List<MeshAnalysisTool>(FoundTools);
                    foreach (MeshAnalysisTool Tool in FoundToolsCopy)
                    {
                        try
                        {
                            Tool.Initialize(Mesh);
                        }
                        catch (Exception E)
                        {
                            Debug.LogError(string.Concat("[MeshAnalysis] '", Tool.Name, "' throws exception on initialize. Excluded from tools list.\n", E.ToString()));
                            FoundTools.Remove(Tool);
                        }
                    }

                    InnerTools = FoundTools.ToArray();
                }

                return InnerTools;
            }

            set
            {
                InnerTools = value;
            }
        }

        /// <summary>
        ///     The menu that contains an available tools.
        /// </summary>
        [CanBeNull]
        private GenericMenu ToolsMenu;

        /// <summary>
        ///     The value indicating whether should be used basic lighting.
        /// </summary>
        private bool UseBasicLighting = true;

        /// <summary>
        ///     The value indicating whether render utility currently draws.
        /// </summary>
        private bool PreviewRenderScope;

        /// <summary>
        ///     The value indicating whether the window are initialized.
        /// </summary>
        private bool IsInitialized;

        /// <summary>
        ///     Gets the camera of a PreviewRenderUtility. This is a wrapper property.
        /// </summary>
        private Camera PreviewRenderUtilityCamera
        {
            get
            {
#if UNITY_2017_1_OR_NEWER
                return PreviewRenderUtility.camera;
#else
                return PreviewRenderUtility.m_Camera;
#endif
            }
        }

        /// <summary>
        ///     Gets the lights of a PreviewRenderUtility. This is a wrapper property.
        /// </summary>
        private Light[] PreviewRenderUtilityLight
        {
            get
            {
#if UNITY_2017_1_OR_NEWER
                return PreviewRenderUtility.lights;
#else
                return PreviewRenderUtility.m_Light;
#endif
            }
        }

        #region GUI fields

        /// <summary>
        ///     The position of a material settings button.
        /// </summary>
        private Rect MaterialSettingsButtonRect;

        /// <summary>
        ///     The position of a shading options menu button.
        /// </summary>
        private Rect ShadingOptionsButtonRect;

        /// <summary>
        ///     The position of a tools menu button.
        /// </summary>
        private Rect ToolsButtonRect;

        #endregion

        /// <summary>
        ///     Opens the window for a specified mesh.
        /// </summary>
        /// <param name="targetMesh">The mesh that should be visualized.</param>
        /// <exception cref="InvalidOperationException">Unity failed to get an instance of the window.</exception>
        [PublicAPI]
        public static void Open([NotNull]Mesh targetMesh)
        {
            Open(targetMesh, null, null);
        }

        /// <summary>
        ///     Opens the window for a specified mesh.
        /// </summary>
        /// <param name="targetMesh">The mesh that should be visualized.</param>
        /// <param name="shadingOptionAlias">The alias of a shading option that should be selected.</param>
        /// <param name="toolAlias">The alias of a tool that should be selected.</param>
        /// <exception cref="InvalidOperationException">Unity failed to get an instance of the window.</exception>
        [PublicAPI]
        public static void Open([NotNull]Mesh targetMesh, [CanBeNull]string shadingOptionAlias, [CanBeNull]string toolAlias)
        {
            MeshAnalysisWindow NewWindow = GetWindow<MeshAnalysisWindow>(false, "Mesh Analysis", true);

            if (NewWindow == null)
            {
                throw new InvalidOperationException("Unity failed to get an instance of the window.");
            }

            NewWindow.Mesh = targetMesh;
            NewWindow.Initialize(shadingOptionAlias, toolAlias);
        }

        /// <summary>
        ///     Focuses the camera on the triangle.
        /// </summary>
        /// <param name="p1">The first vertex of the triangle.</param>
        /// <param name="p2">The second vertex of the triangle.</param>
        /// <param name="p3">The third vertex of the triangle.</param>
        /// <param name="normal">The normal of the triangle.</param>
        [PublicAPI]
        public static void FocusOnTriangle(Vector3 p1, Vector3 p2, Vector3 p3, Vector3 normal)
        {
            Vector3 CenterPoint = MCUtilities.GetAverage(p1, p2, p3);

            ChangeFocus(CenterPoint, -normal);
        }

        /// <summary>
        ///     Changes the focus position of a camera.
        /// </summary>
        /// <param name="position">The new focus position.</param>
        /// <exception cref="InvalidOperationException">The window not initialized properly.</exception>
        [PublicAPI]
        public static void ChangeFocus(Vector3 position)
        {
            if (Instance != null
                && Instance.CameraController != null)
            {
                if (Instance.CameraController.SetOrbitCenter(position))
                {
                    RepaintWindow();
                }
            }
            else
            {
                throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
            }
        }

        /// <summary>
        ///     Changes the focus position of a camera.
        /// </summary>
        /// <param name="position">The new focus position.</param>
        /// <param name="forward">The forward direction.</param>        
        /// <exception cref="InvalidOperationException">The window not initialized properly.</exception>
        [PublicAPI]
        public static void ChangeFocus(Vector3 position, Vector3 forward)
        {
            if (Instance != null
                && Instance.CameraController != null)
            {
                Instance.CameraController.SetOrbitCenter(position);
                Instance.CameraController.SetRotation(forward);
            }
            else
            {
                throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
            }
        }

        /// <summary>
        ///     Removes a custom focus.
        /// </summary>
        /// <exception cref="InvalidOperationException">The window not initialized properly.</exception>
        [PublicAPI]
        public static void RemoveFocus()
        {
            if (Instance != null)
            {
                ChangeFocus(Instance.MeshBounds.center);
            }
            else
            {
                throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
            }
        }

        /// <summary>
        /// Repaint <see cref="MeshAnalysisWindow"/>
        /// </summary>
        [PublicAPI]
        public static void RepaintWindow()
        {
            if (Instance != null)
            {
                Instance.Repaint();
            }
        }

        /// <summary>
        ///     Transforms position from preview world space into overlay screen space.
        /// </summary>
        /// <param name="previewWorldPosition">The position in preview world.</param>
        /// <returns>
        ///     The position in an overlay space.
        /// </returns>
        /// <exception cref="InvalidOperationException">The window not initialized properly.</exception>
        [PublicAPI]
        public static Vector3 PreviewToOverlayPoint(Vector3 previewWorldPosition)
        {
            if (Instance != null
                && Instance.IsInitialized)
            {
                Vector3 ScreenPoint = Instance.PreviewRenderUtilityCamera.WorldToScreenPoint(previewWorldPosition);

                if (!Instance.PreviewRenderScope)
                {
                    float OneToScaleFactor = 1f / Instance.ScaleFactor;
                    ScreenPoint = ScreenPoint.ApplyScale(new Vector3(OneToScaleFactor, OneToScaleFactor, 1));
                }

                ScreenPoint.y = Instance.position.height - ScreenPoint.y;

                ScreenPoint.z = Mathf.Sign(ScreenPoint.z) * 3f;
                return ScreenPoint;
            }
            else
            {
                throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
            }
        }

        public static Vector3 GetObjectCenter()
        {
            if (Instance != null)
            {
                return Instance.MeshBounds.center;
            }

            return Vector3.zero;
        }

        /// <summary>
        ///     Calculates the local bounds of a specified world bounds.
        /// </summary>
        /// <param name="worldBounds">The bounds in world.</param>
        /// <param name="cameraRotation">The rotation of a camera.</param>
        /// <returns>The local bounds of a world bounds.</returns>
        private static Bounds CalcLocalBounds(Bounds worldBounds, Quaternion cameraRotation)
        {
            Quaternion InversedRotation = cameraRotation.Inverse();

            Vector3[] Points =
            {
                new Vector3(worldBounds.extents.x, worldBounds.extents.y, worldBounds.extents.z),
                new Vector3(worldBounds.extents.x, worldBounds.extents.y, -worldBounds.extents.z),
                new Vector3(worldBounds.extents.x, -worldBounds.extents.y, worldBounds.extents.z),
                new Vector3(worldBounds.extents.x, -worldBounds.extents.y, -worldBounds.extents.z),
                new Vector3(-worldBounds.extents.x, worldBounds.extents.y, worldBounds.extents.z),
                new Vector3(-worldBounds.extents.x, worldBounds.extents.y, -worldBounds.extents.z),
                new Vector3(-worldBounds.extents.x, -worldBounds.extents.y, worldBounds.extents.z),
                new Vector3(-worldBounds.extents.x, -worldBounds.extents.y, -worldBounds.extents.z)
            };

            for (int Index = 0; Index < 8; Index++)
            {
                Points[Index] = worldBounds.center + (InversedRotation * Points[Index]);
            }

            Bounds ResultBounds = new Bounds(Points[0], Vector3.zero);

            for (int I = 1; I < 8; I++)
            {
                ResultBounds.Encapsulate(Points[I]);
            }

            return ResultBounds;
        }

        /// <summary>
        ///     Gets distance to bounds.
        /// </summary>
        /// <param name="cameraPosition">The position of a camera.</param>
        /// <param name="cameraRotation">The rotation of a camera.</param>
        /// <param name="worldBounds">The bounds of a mesh in world.</param>
        /// <param name="minDistance">The min distance.</param>
        /// <param name="maxDistance">The max distance.</param>
        private static void GetDistanceToBounds(Vector3 cameraPosition, Quaternion cameraRotation, Bounds worldBounds, out float minDistance, out float maxDistance)
        {
            Bounds LocalBounds = CalcLocalBounds(worldBounds, cameraRotation);
            float MinDistanceLimit = 0.1f * LocalBounds.extents.z;

            Vector3 PlanesNormal = cameraRotation * Vector3.back;
            Plane NearPlane = new Plane(PlanesNormal, LocalBounds.center + (PlanesNormal * LocalBounds.extents.z));
            Plane FarPlane = new Plane(PlanesNormal, LocalBounds.center - (PlanesNormal * LocalBounds.extents.z));
            minDistance = NearPlane.GetDistanceToPoint(cameraPosition) - 0.0001f;
            maxDistance = FarPlane.GetDistanceToPoint(cameraPosition) + 0.0001f;

            if (worldBounds.Contains(cameraPosition))
            {
                minDistance = MinDistanceLimit;
            }
            else
            {
                minDistance = Mathf.Clamp(minDistance, MinDistanceLimit, float.MaxValue);
            }
        }

        /// <summary>
        ///     Updates an editor window.
        /// </summary>
        /// <exception cref="InvalidOperationException">The window not initialized properly.</exception>
        private void Update()
        {
            if (!IsInitialized)
            {
                return;
            }

            if (CameraController == null)
            {
                throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
            }

            if (CameraController.Update(MouseScroll))
            {
                Repaint();
            }

            MouseScroll = 0;
        }

        /// <summary>
        ///     Raised when an editor window becomes enabled.
        /// </summary>
        private void OnEnable()
        {
            Initialize();
            wantsMouseMove = true;
        }

        /// <summary>
        ///     Raised when an editor window becomes disabled.
        /// </summary>
        private void OnDisable()
        {
            Deinitialize();
        }

        /// <summary>
        ///     Initializes the window.
        /// </summary>
        /// <param name="shadingOptionAlias">The alias of a shading option that should be selected.</param>
        /// <param name="toolAlias">The alias of a tool that should be selected.</param>
        private void Initialize([CanBeNull]string shadingOptionAlias = null, [CanBeNull]string toolAlias = null)
        {
            if (Mesh == null)
            {
                return;
            }

            if (IsInitialized)
            {
                Deinitialize();
            }

            Instance = this;

            PreviewRenderUtility = new PreviewRenderUtility(false);
            CameraController = new OrbitCameraController(PreviewRenderUtilityCamera);

            MeshBounds = CalcBounds();
            RemoveFocus();

            if (CameraController != null)
            {
                CameraController.TargetDistance = MeshBounds.extents.magnitude * 8f;
                CameraController.SetDistanceRange(
                    MeshBounds.extents.magnitude * 0.01f,
                    MeshBounds.extents.magnitude * 40f);
            }

            MeshCheckerAssetModificationProcessor.SceneSaveEvent += SceneSaveEventHandler;
            MCGUI.ReloadStylesEvent += ReloadStylesEventHandler;

            if (!string.IsNullOrEmpty(shadingOptionAlias))
            {
                SelectedShadingOption = 0;

                // Cache Alias variable to reduce garbage
                // ReSharper disable once TooWideLocalVariableScope
                string Alias;
                for (int Index = 0; Index < ShadingOptions.Length; Index++)
                {
                    Alias = ShadingOptions[Index].GetAlias();
                    if (Alias == shadingOptionAlias)
                    {
                        SelectedShadingOption = Index;
                        break;
                    }
                }
            }
            else
            {
                if (SelectedShadingOption < 0
                || SelectedShadingOption >= ShadingOptions.Length)
                {
                    SelectedShadingOption = 0;
                }
            }

            if (!string.IsNullOrEmpty(toolAlias))
            {
                // Cache Alias variable to reduce garbage
                // ReSharper disable once TooWideLocalVariableScope
                string Alias;
                for (int Index = 0; Index < Tools.Length; Index++)
                {
                    Alias = Tools[Index].GetAlias();
                    if (Alias == toolAlias)
                    {
                        SelectedTool = Index;
                        break;
                    }
                }
            }
            else
            {
                if (SelectedTool < 0 || SelectedTool >= Tools.Length)
                {
                    SelectedTool = 0;
                }
            }

            IsInitialized = true;
            RemoveNotification();
        }

        /// <summary>
        ///     Deinitializes the window.
        /// </summary>
        private void Deinitialize()
        {
            if (!IsInitialized)
            {
                return;
            }

            Instance = null;

            if (PreviewRenderUtility != null)
            {
                PreviewRenderUtility.Cleanup();
                PreviewRenderUtility = null;
            }

            Cleanup();

            if (CameraController != null)
            {
                CameraController.Cleanup();
                CameraController = null;
            }

            MeshCheckerAssetModificationProcessor.SceneSaveEvent -= SceneSaveEventHandler;
            MCGUI.ReloadStylesEvent -= ReloadStylesEventHandler;

            IsInitialized = false;
        }

        /// <summary>
        ///     Cleans up loaded and generated resources.
        /// </summary>
        private void Cleanup()
        {
            CheckerMaterial.Release(Id);

            foreach (MeshAnalysisShadingOption ShadingOption in ShadingOptions)
            {
                try
                {
                    ShadingOption.Deinitialize();
                }
                catch (Exception E)
                {
                    Debug.LogError(string.Concat("[MeshAnalysis] '", ShadingOption.Title, "' throws exception on Deinitialize() call.\n", E.ToString()));
                }
            }

            // ReSharper disable once AssignNullToNotNullAttribute
            ShadingOptions = null;

            foreach (MeshAnalysisTool Tool in Tools)
            {
                try
                {
                    Tool.Deinitialize();
                }
                catch (Exception E)
                {
                    Debug.LogError(string.Concat("[MeshAnalysis] '", Tool.Name, "' throws exception on Deinitialize() call.\n", E.ToString()));
                }
            }

            // ReSharper disable once AssignNullToNotNullAttribute
            Tools = null;
        }

        /// <summary>
        ///     Draws GUI.
        /// </summary>
        [UsedImplicitly]
        private void OnGUI()
        {
            if (!IsInitialized || Mesh == null)
            {
                ShowNotification(new GUIContent("Nothing to draw"));
                return;
            }

            if (Event.current == null)
            {
                return;
            }

            HandleUserInputToolSelection();

            Rect MeshDrawZoneRect = new Rect(0, MCGUI.ToolbarHeight - 1, position.width, position.height - (MCGUI.ToolbarHeight - 1));

            if (Event.current.type == EventType.Repaint)
            {
                float MinDistance, MaxDistance;
                GetDistanceToBounds(out MinDistance, out MaxDistance);
                SetClippingPlanes(MinDistance, MaxDistance);

                UpdateLighting();

                RepaintEvent();

                DrawVisualizedMesh(MeshDrawZoneRect);
            }

            DrawOverlays(MeshDrawZoneRect);
            DrawToolbar();

            HandleUserInputCameraControl();

            DrawNavigationHints(MeshDrawZoneRect);
        }

        /// <summary>
        ///     Draws an overlay and an overlay GUI.
        /// </summary>
        /// <param name="overlayPosition">The position where an overlay should be drawn.</param>
        /// <exception cref="InvalidOperationException">Method invoked outside of an OnGUI scope.</exception>
        /// <exception cref="ExitGUIException">Throwed when GUI should be ended.</exception>
        private void DrawOverlays(Rect overlayPosition)
        {
            if (Event.current == null)
            {
                throw new InvalidOperationException("The method should be invoked inside of an OnGUI scope.");
            }

            if (Tools[SelectedTool] != null)
            {
                if (Event.current.type == EventType.Repaint)
                {
                    try
                    {
                        Tools[SelectedTool].DrawOverlay(overlayPosition);
                    }
                    catch (ExitGUIException)
                    {
                        throw;
                    }
                    catch (Exception E)
                    {
                        Debug.LogException(E);
                        GUI.Label(overlayPosition, "Failed to draw overlay for selected tool", MCGUI.Styles.ErrorLabel);
                    }
                }

                try
                {
                    Tools[SelectedTool].DrawOverlayGUI(overlayPosition);
                }
                catch (ExitGUIException)
                {
                    throw;
                }
                catch (Exception E)
                {
                    Debug.LogException(E);
                    GUI.Label(overlayPosition, "Failed to draw overlay GUI for selected tool", MCGUI.Styles.ErrorLabel);
                }
            }
        }

        /// <summary>
        ///     Handles the user input to control the camera.
        /// </summary>
        /// <exception cref="InvalidOperationException">The window not initialized properly.</exception>
        /// <exception cref="InvalidOperationException">Method invoked outside of an OnGUI scope.</exception>
        private void HandleUserInputCameraControl()
        {
            if (Event.current == null)
            {
                throw new InvalidOperationException("The method should be invoked inside of an OnGUI scope.");
            }

            if (CameraController == null)
            {
                throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
            }

            if (Event.current.type == EventType.ScrollWheel)
            {
                MouseScroll = Mathf.Sign(Event.current.delta.y);
                Event.current.Use();
            }

            if (Event.current.type == EventType.MouseDrag && Event.current.button == 0)
            {
                CameraController.Rotate(Event.current.delta);
                Event.current.Use();
                Repaint();
            }

            if (Event.current.type == EventType.MouseDrag && Event.current.button == 1)
            {
                CameraController.Move(Event.current.delta);
                Event.current.Use();
                Repaint();
            }

            if (Event.current.type == EventType.KeyDown && Event.current.keyCode == KeyCode.F)
            {
                RemoveFocus();
                Event.current.Use();
                Repaint();
            }
        }

        /// <summary>
        ///     Handles the user input to select a tool.
        /// </summary>
        /// <exception cref="InvalidOperationException">Method invoked outside of an OnGUI scope.</exception>
        private void HandleUserInputToolSelection()
        {
            if (Event.current == null)
            {
                throw new InvalidOperationException("The method should be invoked inside of an OnGUI scope.");
            }

            if (Event.current.type == EventType.KeyDown && Event.current.keyCode != KeyCode.None)
            {
                for (int ToolIndex = 0; ToolIndex < Tools.Length; ToolIndex++)
                {
                    if (Tools[ToolIndex] == null)
                    {
                        continue;
                    }

                    if (Event.current.keyCode == Tools[ToolIndex].Keybinding)
                    {
                        SelectedTool = ToolIndex;
                        Event.current.Use();
                    }
                }
            }
        }

        /// <summary>
        ///     Draws the visualized mesh in a specified position.
        /// </summary>
        /// <param name="drawZonePosition">The position where mesh layer should be drawn.</param>
        /// <exception cref="InvalidOperationException">The window not initialized properly.</exception>
        /// <exception cref="ExitGUIException">Throwed when GUI should be ended.</exception>
        private void DrawVisualizedMesh(Rect drawZonePosition)
        {
            if (PreviewRenderUtility == null
                || PreviewRenderUtilityCamera == null
                || Mesh == null)
            {
                throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
            }

            if (ShadingOptions[SelectedShadingOption] == null)
            {
                if (ShadingOptions.Length > 0)
                {
                    SelectedShadingOption = 0;
                }
                else
                {
                    throw new InvalidOperationException("[MeshAnalysis] Shading options missed.");
                }
            }
            
            PreviewRenderScope = true;
            PreviewRenderUtility.BeginPreview(drawZonePosition, MCGUI.Styles.FlowBackground);
#if !UNITY_4_6 && !UNITY_4_7 // Analog for UNITY_5_0_OR_NEWER
            ScaleFactor = PreviewRenderUtility.GetScaleFactor(drawZonePosition.width, drawZonePosition.height);
#else
            ScaleFactor = MCUtilities.GetScaleFactor(drawZonePosition.width, drawZonePosition.height);
#endif
            // Save fog
            bool Fog = RenderSettings.fog;

            GL.wireframe = ShadingOptions[SelectedShadingOption].Wireframe;

            Material[] Materials = ShadingOptions[SelectedShadingOption].GetMaterials();
            for (int Submesh = 0; Submesh < Mesh.subMeshCount; Submesh++)
            {
                PreviewRenderUtility.DrawMesh(Mesh, Vector3.zero, Quaternion.identity, Materials[Submesh], Submesh);

                // Set custom lighting
                InternalEditorUtility.SetCustomLighting(PreviewRenderUtilityLight, Color.white);

                // Save fog and remove it
                Unsupported.SetRenderSettingsUseFogNoDirty(false);
            }

            // Render observed mesh
            PreviewRenderUtilityCamera.Render();
            GL.wireframe = false;

            // Draw meshes in selected tool
            try
            {
                if (Tools[SelectedTool] != null)
                {
                    // TODO: Update near and far clipping planes (Update bounds)
                    Tools[SelectedTool].DrawMeshesInternal(PreviewRenderUtility);
                }
            }
            catch (ExitGUIException)
            {
                throw;
            }
            catch (Exception E)
            {
                Debug.LogException(E);
            }

            // Set custom lighting
            InternalEditorUtility.SetCustomLighting(PreviewRenderUtilityLight, Color.white);

            // Remove fog
            Unsupported.SetRenderSettingsUseFogNoDirty(false);

            // Render additional meshes
            PreviewRenderUtilityCamera.clearFlags = CameraClearFlags.Nothing;
            PreviewRenderUtilityCamera.Render();

            // Return fog
            Unsupported.SetRenderSettingsUseFogNoDirty(Fog);

            // Remove custom lighting
            InternalEditorUtility.RemoveCustomLighting();
#if !UNITY_4_6 && !UNITY_4_7 && !UNITY_5_0 // Analog for UNITY_5_1_OR_NEWER
            PreviewRenderUtility.EndAndDrawPreview(drawZonePosition);
#else
            GUI.DrawTexture(drawZonePosition, PreviewRenderUtility.EndPreview());
#endif
            PreviewRenderScope = false;
        }

        private void DrawNavigationHints(Rect zone)
        {
            zone = new RectOffset(10, 10, 10, 10).Remove(zone);

            using (new GUILayout.AreaScope(zone))
            {
                GUILayout.FlexibleSpace();

                using (new MCGUI.ColorScope(new Color(1f, 1f, 1f, 0.2f)))
                {
                    GUILayout.Label("Hold LMB to rotate, RMB to move", MCGUI.Styles.RobotoLabelBoldWhite);
                    GUILayout.Label("Press F to reposition", MCGUI.Styles.RobotoLabelBoldWhite);
                }
            }
        }

        /// <summary>
        ///     Updates lighting.
        /// </summary>
        /// <exception cref="InvalidOperationException">The window not initialized properly.</exception>
        private void UpdateLighting()
        {
            if (PreviewRenderUtility == null
                || PreviewRenderUtilityLight == null
                || PreviewRenderUtilityLight[0] == null
                || PreviewRenderUtilityLight[0].transform == null
                || PreviewRenderUtilityLight[1] == null
                || PreviewRenderUtilityLight[1].transform == null
                || PreviewRenderUtilityCamera == null
                || PreviewRenderUtilityCamera.transform == null)
            {
                throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
            }

            if (UseBasicLighting)
            {
                PreviewRenderUtilityLight[0].intensity = 1;
                PreviewRenderUtilityLight[0].transform.rotation = Quaternion.Euler(30f, 30f, 0f);
                PreviewRenderUtilityLight[1].transform.rotation = Quaternion.Euler(30f, 210f, 0f);
                PreviewRenderUtilityLight[1].intensity = 1;
            }
            else
            {
                PreviewRenderUtilityLight[0].intensity = 1f;
                PreviewRenderUtilityLight[0].transform.rotation =
                    PreviewRenderUtilityCamera.transform.rotation;
                PreviewRenderUtilityLight[1].intensity = 0f;
            }
        }

        /// <summary>
        ///     Draws a toolbar.
        /// </summary>
        private void DrawToolbar()
        {
            using (new MCGUI.EnabledScope(Mesh != null))
            {
                using (new MCGUI.HorizontalLayoutScope(EditorStyles.toolbar, GUILayout.Height(MCGUI.ToolbarHeight), GUILayout.Width(position.width)))
                {
                    DrawToolbarShadingOptions();

                    GUI.enabled = true;

                    GUILayout.Label(string.Empty, EditorStyles.toolbarButton, GUILayout.Width(15));

                    DrawToolbarTools();

                    GUILayout.FlexibleSpace();
                    if (Mesh != null)
                    {
                        GUILayout.Label(Mesh.name, EditorStyles.toolbarButton);
                        GUILayout.Space(10);
                    }

                    UseBasicLighting = GUILayout.Toggle(
                        UseBasicLighting,
                        new GUIContent(MCGUI.Icons.SceneViewLightingIcon, "When toggled off a light attached to camera is used."),
                        EditorStyles.toolbarButton);

                    if (this.GetDocked())
                    {
                        GUILayout.Space(3);
                        maximized = GUILayout.Toggle(
                            maximized, 
                            new GUIContent(MCGUI.Icons.MaximizeIcon.Current, "Toggle maximized (Shift + Space)"),
                            EditorStyles.toolbarButton);
                    }
                }
            }
        }

        /// <summary>
        ///     Draws a shading options zone in the toolbar.
        /// </summary>
        /// <exception cref="InvalidOperationException">Method invoked outside of an OnGUI scope.</exception>
        private void DrawToolbarShadingOptions()
        {
            if (Event.current == null)
            {
                throw new InvalidOperationException("The method should be invoked inside of an OnGUI scope.");
            }

            if (GUILayout.Button(ShadingOptions[SelectedShadingOption].Title, EditorStyles.toolbarDropDown, GUILayout.Width(150)))
            {
                ShadingMenu = new GenericMenu();
                int PreviousGroup = ShadingOptions[0].Priority;
                for (int Index = 0; Index < ShadingOptions.Length; Index++)
                {
                    int Group = Mathf.FloorToInt(ShadingOptions[Index].Priority / 100f);
                    if (Group > PreviousGroup)
                    {
                        ShadingMenu.AddSeparator(string.Empty);
                    }

                    PreviousGroup = Group;

                    int ItemIndex = Index;
                    ShadingMenu.AddItem(new GUIContent(ShadingOptions[Index].Title), SelectedShadingOption == Index, () => SelectedShadingOption = ItemIndex);
                }

                ShadingMenu.DropDown(ShadingOptionsButtonRect);
            }

            // Popup window needs this rect for correct positioning. 
            // This rect possible get only in repaint GUI call.
            if (Event.current.type == EventType.Repaint)
            {
                ShadingOptionsButtonRect = GUILayoutUtility.GetLastRect();
            }

            if (!ShadingOptions[SelectedShadingOption].GetHaveSettings())
            {
                GUI.enabled = false;
            }

            if (GUILayout.Button(new GUIContent(MCGUI.Icons.SettingsSmallIcon.Current), EditorStyles.toolbarButton, GUILayout.Width(21)))
            {
                PopupWindow.Show(
                    MaterialSettingsButtonRect,
                    new ToolConfigurationPopup(
                        rect => ShadingOptions[SelectedShadingOption].DrawSettings(rect),
                        ShadingOptions[SelectedShadingOption].GetSettingsWidth,
                        ShadingOptions[SelectedShadingOption].GetSettingsHeight));
            }

            GUI.enabled = true;
            if (!ShadingOptions[SelectedShadingOption].HasDescription())
            {
                GUI.enabled = false;
            }

            if (GUILayout.Button(new GUIContent("?"), EditorStyles.toolbarButton, GUILayout.Width(21)))
            {
                PopupWindow.Show(
                    MaterialSettingsButtonRect,
                    new ToolConfigurationPopup(
                        ShadingOptions[SelectedShadingOption].GetDescriptionWindowSize(),
                        rect => ShadingOptions[SelectedShadingOption].DrawDescription(rect)));
            }

            // Popup window needs this rect for correct positioning. 
            // This rect possible get only in repaint GUI call.
            if (Event.current.type == EventType.Repaint)
            {
                MaterialSettingsButtonRect = GUILayoutUtility.GetLastRect();
            }
        }

        /// <summary>
        ///     Draws a tools zone in the toolbar.
        /// </summary>
        /// <exception cref="InvalidOperationException">Method invoked outside of an OnGUI scope.</exception>
        /// <exception cref="ExitGUIException">Throwed when GUI should be ended.</exception>
        private void DrawToolbarTools()
        {
            if (Event.current == null)
            {
                throw new InvalidOperationException("The method should be invoked inside of an OnGUI scope.");
            }

            bool ToolsAvailable = Tools.Length > 0;
            string ToolDropDownCaption = ToolsAvailable ? Tools[SelectedTool].Name : "No tools available";
            GUI.enabled = ToolsAvailable;
            if (GUILayout.Button(ToolDropDownCaption, EditorStyles.toolbarDropDown, GUILayout.Width(150)))
            {
                ToolsMenu = new GenericMenu();
                int PreviousGroup = Mathf.FloorToInt(Tools[0].Priority / 100f);
                for (int Index = 0; Index < Tools.Length; Index++)
                {
                    int Group = Mathf.FloorToInt(Tools[Index].Priority / 100f);
                    if (Group > PreviousGroup)
                    {
                        ToolsMenu.AddSeparator(string.Empty);
                    }

                    PreviousGroup = Group;

                    int ItemIndex = Index;
                    ToolsMenu.AddItem(
                        new GUIContent(string.Concat(Tools[Index].Name, Tools[Index].Keybinding != KeyCode.None ? " _" + Tools[Index].Keybinding : string.Empty)),
                        SelectedTool == Index,
                        () => SelectedTool = ItemIndex);
                }

                ToolsMenu.DropDown(ToolsButtonRect);
            }

            GUI.enabled = true;

            // Popup window needs this rect for correct positioning. 
            // This rect possible get only in repaint GUI call.
            if (Event.current.type == EventType.Repaint)
            {
                ToolsButtonRect = GUILayoutUtility.GetLastRect();
            }

            GUILayout.BeginHorizontal();

            try
            {
                Tools[SelectedTool].DrawToolbar();
            }
            catch (ExitGUIException)
            {
                throw;
            }
            catch (Exception E)
            {
                Debug.LogException(E);
                GUILayout.Label("Failed to draw toolbar for selected tool", MCGUI.Styles.ErrorLabel);
            }

            GUILayout.EndHorizontal();
        }

        /// <summary>
        ///     Calculates bounds of a visualized mesh.
        /// </summary>
        /// <exception cref="InvalidOperationException">The window not initialized properly.</exception>
        /// <returns>The bounds of a visualized mesh.</returns>
        private Bounds CalcBounds()
        {
            if (MeshVertices == null)
            {
                throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
            }

            Bounds ResultBounds = new Bounds(MeshVertices[0], Vector3.zero);

            // All vertices cycle
            for (int Index = 1; Index < MeshVertices.Length; Index++)
            {
                // Update result bounds with iteration vertex
                ResultBounds.Encapsulate(MeshVertices[Index]);
            }

            return ResultBounds;
        }

        /// <summary>
        ///     Gets distance to bounds.
        /// </summary>
        /// <param name="minDistance">The min distance.</param>
        /// <param name="maxDistance">The max distance.</param>
        /// <exception cref="InvalidOperationException">The window not initialized properly.</exception>
        private void GetDistanceToBounds(out float minDistance, out float maxDistance)
        {
            if (PreviewRenderUtility == null
                || PreviewRenderUtilityCamera == null
                || PreviewRenderUtilityCamera.transform == null)
            {
                throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
            }

            GetDistanceToBounds(
                PreviewRenderUtilityCamera.transform.position,
                PreviewRenderUtilityCamera.transform.rotation,
                MeshBounds,
                out minDistance,
                out maxDistance);
        }

        /// <summary>
        ///     Sets clipping planes of the render utility.
        /// </summary>
        /// <param name="minDistance">The min distance.</param>
        /// <param name="maxDistance">The max distance.</param>
        private void SetClippingPlanes(float minDistance, float maxDistance)
        {
            if (PreviewRenderUtility == null
                || PreviewRenderUtilityCamera == null)
            {
                throw new InvalidOperationException("[MeshAnalysis] The window not initialized properly.");
            }

            PreviewRenderUtilityCamera.nearClipPlane = minDistance;
            PreviewRenderUtilityCamera.farClipPlane = maxDistance;
        }

        #region Event Handlers

        /// <summary>
        ///     Handles a scene save event.
        /// </summary>
        private void SceneSaveEventHandler()
        {
            Cleanup();
        }

        /// <summary>
        ///     <para>Reload used styles</para>
        ///     <para>Handler of the <see cref="MCGUI.ReloadStylesEvent"/>.</para>
        /// </summary>
        private void ReloadStylesEventHandler()
        {
            Initialize();
            Repaint();
        }

        #endregion
    }
}
