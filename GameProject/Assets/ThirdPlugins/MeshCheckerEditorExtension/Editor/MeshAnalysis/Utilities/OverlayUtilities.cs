using System;

using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;
using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework
{
    /// <summary>
    ///     Represents utilities which help to draw overlay in MeshAnalysis window.
    /// </summary>
    [PublicAPI]
    public static class OverlayUtilities
    {
        /// <summary>
        ///     Gets the green color of handles.
        /// </summary>
        public static readonly SkinDependentItem<Color> GreenHandleColor = new SkinDependentItem<Color>(new Color(0.1f, 0.5f, 0.1f), new Color(0.2f, 0.6f, 0.2f));

        /// <summary>
        ///     Gets the red color of handles.
        /// </summary>
        public static readonly SkinDependentItem<Color> RedHandleColor = new SkinDependentItem<Color>(new Color(0.678f, 0.035f, 0.059f, 0.8f), new Color(0.847f, 0.102f, 0.102f));

        /// <summary>
        ///     The cached triangle points array. Created to eliminate memory allocation.
        ///     <para>
        ///         The length is 4.
        ///     </para>
        /// </summary>
        [NotNull]
        private static readonly Vector3[] WireframeTrianglePointsArray = new Vector3[4];

        /// <summary>
        ///     The cached triangle points array. Created to eliminate memory allocation.
        ///     <para>
        ///         The length is 3.
        ///     </para>
        /// </summary>
        [NotNull]
        private static readonly Vector3[] SolidTrianglePointsArray = new Vector3[3];

        /// <summary>
        ///     The cached poly line points array. Created to eliminate memory allocation.
        ///     <para>
        ///         The length is 5.
        ///     </para>
        /// </summary>
        [NotNull]
        private static readonly Vector3[] PolyLinePointsArray = new Vector3[5];

        /// <summary>
        ///     The handle lines material.
        /// </summary>
        private static Material InnerHandleLinesMaterial;

        /// <summary>
        ///     Gets the handle lines material.
        /// </summary>
        /// <exception cref="InvalidOperationException" accessor="get">Failed to load required resource at path "SceneView/2DHandleLines.mat"</exception>
        [PublicAPI]
        [NotNull]
        public static Material HandleLinesMaterial
        {
            get
            {
                if (InnerHandleLinesMaterial == null)
                {
                    InnerHandleLinesMaterial = (Material)EditorGUIUtility.LoadRequired("SceneView/2DHandleLines.mat");
                    if (InnerHandleLinesMaterial == null)
                    {
                        throw new InvalidOperationException("Failed to load required resource at path \"SceneView/2DHandleLines.mat\"");
                    }
                }

                return InnerHandleLinesMaterial;
            }
        }

        /// <summary>
        ///     Draws a wireframe triangle in a MeshAnalysis Tool overlay.
        /// </summary>
        /// <param name="p0">The first point of triangle</param>
        /// <param name="p1">The second point of triangle</param>
        /// <param name="p2">The third point of triangle</param>
        /// <param name="width">The line width</param>
        /// <param name="showVertices">Specifies whether vertices points should be drawn.</param>
        /// <param name="verticesSize">The size of vertices points.</param>
        [PublicAPI]
        public static void DrawTriangleWireframe(Vector3 p0, Vector3 p1, Vector3 p2, float width, bool showVertices = false, float verticesSize = 4)
        {
            bool Front = (p0.z > 0)
                         && (p1.z > 0)
                         && (p2.z > 0);

            if (!Front)
            {
                return;
            }

            p0.z = 0;
            p1.z = 0;
            p2.z = 0;

            SetHandleMaterialPass();

            const int K = 5;
            if (Vector2.Distance(p0, p1) > K
                && Vector2.Distance(p1, p2) > K
                && Vector2.Distance(p2, p0) > K)
            {
                DrawAATriangleWireframe(width, p0, p1, p2);
            }
            else
            {
                DrawTriangleWireframe(p0, p1, p2);
            }

            if (showVertices)
            {
                DrawPoint(p0, verticesSize);
                DrawPoint(p1, verticesSize);
                DrawPoint(p2, verticesSize);
            }
        }

        /// <summary>
        ///     Draws a line between <paramref name="p1"/> and <paramref name="p2"/>.
        /// </summary>
        /// <param name="p1">The first point.</param>
        /// <param name="p2">The second point.</param>
        /// <param name="width">The width of a line.</param>
        [PublicAPI]
        public static void DrawLine(Vector3 p1, Vector3 p2, float width)
        {
            bool Front = (p1.z > 0) && (p2.z > 0);

            if (!Front)
            {
                return;
            }

            p1.z = 0;
            p2.z = 0;

            SetHandleMaterialPass();

            const int K = 25;
            if ((p2 - p1).sqrMagnitude > K)
            {
                DrawAALine(width, p1, p2);
            }
            else
            {
                DrawLine(p1, p2);
            }
        }

        /// <summary>
        ///     Draws an anti-aliased solid triangle.
        /// </summary>
        /// <param name="p1">The first point of triangle.</param>
        /// <param name="p2">The second point of triangle.</param>
        /// <param name="p3">The third point of triangle.</param>
        /// <param name="showVertices">Specifies whether vertices should be drawn.</param>
        /// <param name="verticesSize">Size of vertices.</param>
        [PublicAPI]
        public static void DrawAATriangleSolid(Vector3 p1, Vector3 p2, Vector3 p3, bool showVertices = false, float verticesSize = 4)
        {
            SolidTrianglePointsArray[0] = p1;
            SolidTrianglePointsArray[1] = p2;
            SolidTrianglePointsArray[2] = p3;
            DrawAATriangleSolid(SolidTrianglePointsArray, showVertices, verticesSize);
        }

        /// <summary>
        ///     Draws an anti-aliased solid triangle.
        /// </summary>
        /// <param name="points">The array of points.</param>
        /// <param name="showVertices">Specifies whether vertices should be drawn.</param>
        /// <param name="verticesSize">Size of vertices.</param>
        /// <exception cref="ArgumentNullException"><paramref name="points"/> is <see langword="null"/></exception>
        /// <exception cref="ArgumentException">Points count must be equals or greater than 3.</exception>
        [PublicAPI]
        public static void DrawAATriangleSolid([NotNull]Vector3[] points, bool showVertices = false, float verticesSize = 4)
        {
#if !UNITY_4_6 && !UNITY_4_7 // Analog for UNITY_5_0_OR_NEWER
            if (points == null)
            {
                throw new ArgumentNullException("points");
            }

            if (points.Length < 3)
            {
                throw new ArgumentException("Points count must be equals or greater than 3.", "points");
            }

            SetHandleMaterialPass();

            bool Front = (points[0].z > 0) && (points[1].z > 0) && (points[2].z > 0);

            if (!Front)
            {
                return;
            }

            points[0].z = 0;
            points[1].z = 0;
            points[2].z = 0;

            SetHandleMaterialPass();
            Handles.DrawAAConvexPolygon(points);

            if (showVertices)
            {
                DrawPoint(points[0], verticesSize);
                DrawPoint(points[1], verticesSize);
                DrawPoint(points[2], verticesSize);
            }
#else
            DrawTriangleWireframe(points[0], points[1], points[2], 3, showVertices, verticesSize);
#endif
        }

        /// <summary>
        ///     Draws a point.
        /// </summary>
        /// <param name="position">The position of a point.</param>
        /// <param name="size">The size of a point.</param>
        [PublicAPI]
        public static void DrawPoint(Vector3 position, float size)
        {
            if (position.z < 0)
            {
                return;
            }

            SetHandleMaterialPass();

            position.z = -5;

#if UNITY_5_6_OR_NEWER
            Handles.SphereHandleCap(0, position, Quaternion.identity, size, Event.current.type);
#else
            Handles.SphereCap(0, position, Quaternion.identity, size);
#endif
        }

        /// <summary>
        ///     Sets a pass for <see cref="HandleLinesMaterial"/>.
        /// </summary>
        [PublicAPI]
        public static void SetHandleMaterialPass()
        {
            HandleLinesMaterial.SetPass(0);
        }

        /// <summary>
        ///     Draws an anti-aliased cube which position will be projected from world to screen coordinates.
        /// </summary>
        /// <param name="bounds">The bounds.</param>
        /// <param name="rotation">The rotation.</param>
        /// <param name="color">The color.</param>
        /// <param name="width">The width of anti-aliased lines.</param>
        [PublicAPI]
        public static void DrawAAWireCubeProjected(Bounds bounds, Quaternion rotation, Color color, float width = 3f)
        {
            Color InitialColor = Handles.color;
            Handles.color = color;

            Vector3[] Points = bounds.CornerPoints(rotation);

            for (int Index = 0; Index <= 7; Index++)
            {
                Points[Index] = MeshAnalysisWindow.PreviewToOverlayPoint(Points[Index]).ChangeZ(0);
            }

            DrawAAWireRectangle(width, Points[6], Points[2], Points[0], Points[4]);
            DrawAAWireRectangle(width, Points[7], Points[3], Points[1], Points[5]);
            DrawAALine(width, Points[7], Points[6]);
            DrawAALine(width, Points[5], Points[4]);
            DrawAALine(width, Points[3], Points[2]);
            DrawAALine(width, Points[1], Points[0]);

            Handles.color = InitialColor;
        }

        /// <summary>
        ///     Draws an anti-aliased rectangle.
        /// </summary>
        /// <param name="width">The width of anti-aliased lines.</param>
        /// <param name="p0">The first point.</param>
        /// <param name="p1">The second point.</param>
        /// <param name="p2">The third point.</param>
        /// <param name="p3">The fourth point.</param>
        /// <remarks>
        ///     Method invocation doesn't allocate additional memory by parameters array creation 
        ///     in <see cref="M:UnityEngine.Handles.DrawAAPolyLine(UnityEngine.Color[],UnityEngine.Vector3[])"/>.
        /// </remarks>
        [PublicAPI]
        public static void DrawAAWireRectangle(float width, Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3)
        {
            PolyLinePointsArray[0] = p0;
            PolyLinePointsArray[1] = p1;
            PolyLinePointsArray[2] = p2;
            PolyLinePointsArray[3] = p3;
            PolyLinePointsArray[4] = p0;

            Handles.DrawAAPolyLine(width, PolyLinePointsArray);
        }

        /// <summary>
        ///  Draws a rough rectangle.
        /// </summary>
        /// <param name="p0">The first point.</param>
        /// <param name="p1">The second point.</param>
        /// <param name="p2">The third point.</param>
        /// <param name="p3">The fourth point.</param>
        /// <remarks>
        ///     Method invocation doesn't allocate additional memory by parameters array creation 
        ///     in <see cref="M:UnityEngine.Handles.DrawPolyLine(UnityEngine.Vector3[])"/>.
        /// </remarks>
        [PublicAPI]
        public static void DrawWireRect(Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3)
        {
            PolyLinePointsArray[0] = p0;
            PolyLinePointsArray[1] = p1;
            PolyLinePointsArray[2] = p2;
            PolyLinePointsArray[3] = p3;
            PolyLinePointsArray[4] = p0;

            Handles.DrawPolyLine(PolyLinePointsArray);
        }

        /// <summary>
        ///     Draws an anti-aliased line between <paramref name="p0"/> and <paramref name="p1"/>.
        /// </summary>
        /// <param name="width">The width of anti-aliased lines.</param>
        /// <param name="p0">The first point.</param>
        /// <param name="p1">The second point.</param>
        /// <remarks>
        ///     Method invocation doesn't allocate additional memory by parameters array creation 
        ///     in <see cref="M:UnityEngine.Handles.DrawAAPolyLine(UnityEngine.Color[],UnityEngine.Vector3[])"/>.
        /// </remarks>
        [PublicAPI]
        public static void DrawAALine(float width, Vector3 p0, Vector3 p1)
        {
            PolyLinePointsArray[0] = p0;
            PolyLinePointsArray[1] = p1;

            Handles.DrawAAPolyLine(width, 2, PolyLinePointsArray);
        }

        /// <summary>
        ///     Draws a rough line between <paramref name="p0"/> and <paramref name="p1"/>.
        /// </summary>
        /// <param name="p0">The first point.</param>
        /// <param name="p1">The second point.</param>
        /// <remarks>
        ///     Method invocation doesn't allocate additional memory by parameters array creation 
        ///     in <see cref="Handles.DrawPolyLine(UnityEngine.Vector3[])"/>.
        /// </remarks>
        [PublicAPI]
        public static void DrawLine(Vector3 p0, Vector3 p1)
        {
            Handles.DrawLine(p0, p1);
        }

        /// <summary>
        ///     Draws an anti-aliased triangle.
        /// </summary>
        /// <param name="width">The width of anti-aliased lines.</param>
        /// <param name="p0">The first point.</param>
        /// <param name="p1">The second point.</param>
        /// <param name="p2">The third point.</param>
        /// <remarks>
        ///     Method invocation doesn't allocate additional memory by parameters array creation 
        ///     in <see cref="M:UnityEngine.Handles.DrawAAPolyLine(UnityEngine.Color[],UnityEngine.Vector3[])"/>.
        /// </remarks>
        [PublicAPI]
        public static void DrawAATriangleWireframe(float width, Vector3 p0, Vector3 p1, Vector3 p2)
        {
            PolyLinePointsArray[0] = p0;
            PolyLinePointsArray[1] = p1;
            PolyLinePointsArray[2] = p2;
            PolyLinePointsArray[3] = p0;

            Handles.DrawAAPolyLine(width, 4, PolyLinePointsArray);
        }

        /// <summary>
        ///     Draws a rough triangle.
        /// </summary>
        /// <param name="p0">The first point.</param>
        /// <param name="p1">The second point.</param>
        /// <param name="p2">The third point.</param>
        /// <remarks>
        ///     Method invocation doesn't allocate additional memory by parameters array creation 
        ///     in <see cref="Handles.DrawPolyLine(UnityEngine.Vector3[])"/>.
        /// </remarks>
        [PublicAPI]
        public static void DrawTriangleWireframe(Vector3 p0, Vector3 p1, Vector3 p2)
        {
            WireframeTrianglePointsArray[0] = p0;
            WireframeTrianglePointsArray[1] = p1;
            WireframeTrianglePointsArray[2] = p2;
            WireframeTrianglePointsArray[3] = p0;

            Handles.DrawPolyLine(WireframeTrianglePointsArray);
        }

        /// <summary>
        ///     Draws a pointer GUI line using specified points.
        /// </summary>
        /// <param name="points">The waypoints of line.</param>
        /// <param name="error">Specifies whether the line points to error target.</param>
        /// <exception cref="ArgumentNullException"><paramref name="points"/> is <see langword="null"/></exception>
        [PublicAPI]
        public static void DrawPointerLine([NotNull] Vector3[] points, bool error)
        {
            Color LineColor = error ? RedHandleColor.Dark : GreenHandleColor.Dark;
            DrawPointerLine(points, LineColor);
        }

        /// <summary>
        ///     Draws a pointer GUI line using specified points.
        /// </summary>
        /// <param name="overlayLine">The info about pointer line.</param>
        /// <exception cref="ArgumentNullException"><paramref name="overlayLine"/> is <see langword="null"/></exception>
        public static void DrawPointerLine([NotNull] OverlayLine overlayLine)
        {
            if (overlayLine == null)
            {
                throw new ArgumentNullException("overlayLine");
            }

            if (!overlayLine.HasPoints)
            {
                return;
            }

            DrawPointerLine(overlayLine.GetOutputPoints(), overlayLine.Color);
        }

        /// <summary>
        ///     Draws a pointer GUI line using specified points.
        /// </summary>
        /// <param name="points">The waypoints of line.</param>
        /// <param name="lineColor">The line color.</param>
        /// <exception cref="ArgumentNullException"><paramref name="points"/> is <see langword="null"/></exception>
        [PublicAPI]
        public static void DrawPointerLine([NotNull] Vector3[] points, Color lineColor)
        {
            if (points == null)
            {
                throw new ArgumentNullException("points");
            }

            GUI.color = Color.white;

            if (Event.current.type == EventType.Repaint)
            {
                // Draw line
                Handles.color = lineColor;
                Handles.DrawAAPolyLine(4, points);
            }
        }
    }
}
