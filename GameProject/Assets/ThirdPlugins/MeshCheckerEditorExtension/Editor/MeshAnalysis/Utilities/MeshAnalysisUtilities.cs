using System;
using System.Collections.Generic;
using System.Linq;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.Utilities;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework
{
    /// <summary>
    ///     Represents utilities for a mesh analysis.
    /// </summary>
    [PublicAPI]
    public static class MeshAnalysisUtilities
    {
        /// <summary>
        ///     <para>
        ///         Finds all mesh usages on scene.
        ///     </para>
        ///     <para>
        ///         This method finds only <see cref="MeshRenderer"/> components.
        ///         If you need <see cref="SkinnedMeshRenderer"/> components in result collection then use <see cref="FindAllMeshUsages"/> method.
        ///     </para>
        /// </summary>
        /// <param name="mesh">Mesh which usages need to find.</param>
        /// <returns>A collection of found mesh renderers that use specified mesh.</returns>
        /// <exception cref="ArgumentNullException">
        ///     <paramref name="mesh"/> is <see langword="null"/>
        /// </exception>
        [NotNull]
        [PublicAPI]
        public static IEnumerable<MeshRenderer> FindMeshUsages([NotNull]Mesh mesh)
        {
            if (mesh == null)
            {
                throw new ArgumentNullException("mesh");
            }

            List<MeshRenderer> Result = new List<MeshRenderer>();
            MeshFilter[] MeshFilters = UnityEngine.Object.FindObjectsOfType<MeshFilter>();

            if (MeshFilters == null || MeshFilters.Length == 0)
            {
                return Result;
            }

            foreach (MeshFilter Filter in MeshFilters)
            {
                if (Filter == null || Filter.sharedMesh != mesh)
                {
                    continue;
                }

                MeshRenderer FindedMeshRenderer = Filter.GetComponent<MeshRenderer>();
                if (FindedMeshRenderer != null)
                {
                    if (Result.Any(e => e != null && e.sharedMaterials == FindedMeshRenderer.sharedMaterials))
                    {
                        continue;
                    }

                    Result.Add(FindedMeshRenderer);
                }
            }

            // ReSharper disable once PossibleNullReferenceException
            return Result.OrderBy(e => e.name);
        }

        /// <summary>
        ///     Finds all mesh usages on scene (in MeshFilters and SkinnedMeshRenderers too)
        /// </summary>
        /// <param name="mesh">Mesh which usages need to find.</param>
        /// <returns>A collection of finded <see cref="MeshRenderer"/> and <see cref="SkinnedMeshRenderer"/> components that use specified mesh.</returns>
        /// <exception cref="ArgumentNullException">
        ///     <paramref name="mesh"/> is <see langword="null"/>
        /// </exception>
        [NotNull]
        [PublicAPI]
        public static IEnumerable<IMeshRenderer> FindAllMeshUsages([NotNull]Mesh mesh)
        {
            if (mesh == null)
            {
                throw new ArgumentNullException("mesh");
            }

            List<IMeshRenderer> Result = new List<IMeshRenderer>();
            Result.AddRange(FindMeshUsages(mesh).Select(usage => new MCMeshRenderer(usage) as IMeshRenderer));

            SkinnedMeshRenderer[] SkinnedMeshRenderers = UnityEngine.Object.FindObjectsOfType<SkinnedMeshRenderer>();

            if (SkinnedMeshRenderers != null && SkinnedMeshRenderers.Length != 0)
            {
                foreach (SkinnedMeshRenderer Renderer in SkinnedMeshRenderers)
                {
                    if (Renderer == null || Renderer.sharedMesh != mesh)
                    {
                        continue;
                    }

                    Result.Add(new MCSkinnedMeshRenderer(Renderer));
                }
            }

            return Result.OrderBy(e => e.name);
        }

        /// <summary>
        ///     Gets the alias of a <see cref="MeshAnalysisTool"/> or a <see cref="MeshAnalysisShadingOption"/>, which stored in <see cref="AliasAttribute"/>
        /// </summary>
        /// <typeparam name="T">Type of the tool or ShadingOption class. 
        ///     <para>
        ///         Must be derived from <see cref="MeshAnalysisTool"/> or <see cref="MeshAnalysisShadingOption"/>.
        ///     </para>
        /// </typeparam>
        /// <returns>
        ///     Alias if exists; otherwise, <c>null</c>.
        /// </returns>
        /// <exception cref="ArgumentException"><typeparamref name="T"/> are not implementing IHaveAlias interface.</exception>
        [CanBeNull]
        [PublicAPI]
        public static string GetAliasSafe<T>() where T : IHaveAlias
        {
            return GetAliasSafe(typeof(T));
        }

        /// <summary>
        ///     Gets the alias of a <see cref="MeshAnalysisTool"/> or a <see cref="MeshAnalysisShadingOption"/>, which stored in <see cref="AliasAttribute"/>
        /// </summary>
        /// <param name="type">The type of a tool or shadingOption class.
        ///     <para>
        ///         Must be derived from <see cref="MeshAnalysisTool"/>, <see cref="MeshAnalysisShadingOption"/> or implement <see cref="IHaveAlias"/> interface.
        ///     </para>
        /// </param>
        /// <returns>
        ///     Alias if exists; otherwise, <c>null</c>.
        /// </returns>
        /// <exception cref="ArgumentException"><paramref name="type"/> are not implementing IHaveAlias interface.</exception>
        [CanBeNull]
        [PublicAPI]
        public static string GetAliasSafe(Type type)
        {
            string FindedAlias = null;

            if (typeof(IHaveAlias).IsAssignableFrom(type))
            {
                AliasAttribute Attribute = ReflectionUtilities.GetAttribute<AliasAttribute>(type);
                if (Attribute != null)
                {
                    FindedAlias = Attribute.Alias;
                }
            }
            else
            {
                throw new ArgumentException("Type are not implementing IHaveAlias interface.", "type");
            }

            return FindedAlias;
        }

        /// <summary>
        ///     Gets the alias of a type.
        /// </summary>
        /// <param name="type">The type that has <see cref="AliasAttribute"/></param>
        /// <returns>
        ///     Alias if exists; otherwise, <c>null</c>.
        /// </returns>
        [CanBeNull]
        [PublicAPI]
        public static string GetAlias(Type type)
        {
            string FindedAlias = null;

            AliasAttribute Attribute = ReflectionUtilities.GetAttribute<AliasAttribute>(type);
            if (Attribute != null)
            {
                FindedAlias = Attribute.Alias;
            }

            return FindedAlias;
        }

        /// <summary>
        ///     Get the alias of a type.
        /// </summary>
        /// <typeparam name="T">The type that has <see cref="AliasAttribute"/> and implements an <see cref="IHaveAlias"/> interface.</typeparam>
        /// <param name="target">Instance which alias we are looking for.</param>
        /// <returns>
        ///     Alias if exists; otherwise, <c>null</c>.
        /// </returns>
        [CanBeNull]
        internal static string GetAlias<T>(this T target) where T : IHaveAlias
        {
            string FindedAlias = null;

            if (target != null)
            {
                AliasAttribute Attribute = ReflectionUtilities.GetAttribute<AliasAttribute>(target.GetType());
                if (Attribute != null)
                {
                    FindedAlias = Attribute.Alias;
                }
            }

            return FindedAlias;
        }
    }
}
