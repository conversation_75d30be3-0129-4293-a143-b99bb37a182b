using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface
{
    /// <summary>
    ///     Represents a simple description of a shading option with custom content.
    /// </summary>
    /// <typeparam name="TShadingOption">The type of target shading option.</typeparam>
    public class CustomShadingOptionDescription<TShadingOption> : ShadingOptionDescription<TShadingOption>
        where TShadingOption : MeshAnalysisShadingOption
    {
        /// <summary>
        ///     The content drawer delegate.
        /// </summary>
        /// <param name="position">The content position.</param>
        public delegate void ContentDrawerDelegate(Rect position);

        /// <summary>
        ///     Initializes a new instance of the <see cref="CustomShadingOptionDescription{TShadingOption}"/> class.
        /// </summary>
        /// <param name="size">The size of a description popup.</param>
        /// <param name="contentDrawer">The content drawer method.</param>
        /// <example>
        ///     <para>
        ///         This example shows how to use drawer with a content drawer method.
        ///         <code>
        ///             /// <summary>
        ///             ///     The description drawer.
        ///             /// </summary>
        ///             private readonly CustomShadingOptionDescription&lt;NormalsShadingOption&gt; Description =
        ///                new CustomShadingOptionDescription&lt;NormalsShadingOption&gt;(new Vector2(800, 80), DescriptionDrawer);
        ///             
        ///             private void DescriptionDrawer(Rect position) 
        ///             {
        ///                 // Draw some description...
        ///             }
        ///         </code>
        ///     </para>
        ///     <para>
        ///         Anonymous method also can be used.
        ///         <code>
        ///             /// <summary>
        ///             ///     The description drawer.
        ///             /// </summary>
        ///             private readonly CustomShadingOptionDescription&lt;NormalsShadingOption&gt; Description =
        ///                new CustomShadingOptionDescription&lt;NormalsShadingOption&gt;(
        ///                    new Vector2(800, 80), position => 
        ///                    {
        ///                        GUI.Label(position, "Some description");
        ///                    });
        ///         </code>
        ///     </para>
        /// </example>
        public CustomShadingOptionDescription(Vector2 size, ContentDrawerDelegate contentDrawer)
        {
            InnerSize = size;
            ContentDrawer = contentDrawer;
        }

        #region Overrides of ShadingOptionDescription

        /// <inheritdoc />
        public override Vector2 Size
        {
            get
            {
                return InnerSize;
            }
        }
        #endregion

        /// <summary>
        ///     A backing field for the <see cref="Size"/> property.
        /// </summary>
        private readonly Vector2 InnerSize;

        /// <summary>
        ///     The description of a shading option.
        /// </summary>
        private readonly ContentDrawerDelegate ContentDrawer;

        #region Overrides of ShadingOptionDescription
        /// <inheritdoc />
        protected override void DrawContent(Rect position)
        {
            if (ContentDrawer == null)
            {
                DrawContentError(position);
                return;
            }

            ContentDrawer(position);
        }

        #endregion
    }
}