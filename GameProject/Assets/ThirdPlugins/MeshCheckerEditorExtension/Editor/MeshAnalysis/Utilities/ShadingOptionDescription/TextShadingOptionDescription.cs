using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.UserInterface
{
    /// <summary>
    ///     Represents a simple text description of a shading option.
    /// </summary>
    /// <typeparam name="TShadingOption">The type of target shading option.</typeparam>
    public sealed class TextShadingOptionDescription<TShadingOption> : ShadingOptionDescription<TShadingOption> 
        where TShadingOption : MeshAnalysisShadingOption
    {
        /// <summary>
        ///     Initializes a new instance of the <see cref="TextShadingOptionDescription{TShadingOption}"/> class.
        /// </summary>
        /// <param name="size">The size of a description popup.</param>
        /// <param name="text">The description.</param>
        public TextShadingOptionDescription(Vector2 size, string text)
        {
            InnerSize = size;
            Text = text;
        }

        #region Overrides of ShadingOptionDescription

        /// <inheritdoc />
        public override Vector2 Size
        {
            get
            {
                return InnerSize;
            }
        }
        #endregion

        /// <summary>
        ///     A backing field for the <see cref="Size"/> property.
        /// </summary>
        private readonly Vector2 InnerSize;

        /// <summary>
        ///     The description of a shading option.
        /// </summary>
        private readonly string Text;

        #region Overrides of ShadingOptionDescription
        /// <inheritdoc />
        protected override void DrawContent(Rect position)
        {
            GUI.Label(position, Text, MCGUI.Styles.RobotoLabel);
        }

        #endregion
    }
}