using System;
using System.Collections.Generic;

using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;

using JetBrains.Annotations;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework
{
    /// <summary>
    ///     Represents a line which can be drawn by <see cref="OverlayUtilities.DrawPointerLine(OverlayLine)"/>.
    /// </summary>
    [PublicAPI]
    public sealed class OverlayLine
    {
        /// <summary>
        ///     Represents a collection of <see cref="Vector3"/> points.
        /// </summary>
        private sealed class PointsCollection
        {
            /// <summary>
            ///     The default capacity of internal arrays.
            /// </summary>
            private const int DefaultCapacity = 3;

            /// <summary>
            ///     The maximum array length.
            /// </summary>
            private const int MaxArrayLength = 0X7FEFFFFF;

            /// <summary>
            ///     The empty array.
            /// </summary>
            private static readonly Vector3[] EmptyArray = new Vector3[0];

            /// <summary>Gets a point at specified index.</summary>
            /// <param name="index">The index of needed point.</param>
            /// <returns> The <see cref="Vector3"/> point at specified index.</returns>
            [PublicAPI]
            public Vector3 this[int index]
            {
                get
                {
                    if (index < 0 || index >= Count)
                    {
                        throw new ArgumentOutOfRangeException("index");
                    }

                    return Points[index];
                }
            }

            /// <summary>
            ///     Gets the array of input points.
            /// </summary>
            [PublicAPI]
            public Vector3[] Points { get; private set; }

            /// <summary>
            ///     Gets the count of stored input points.
            /// </summary>
            [PublicAPI]
            public int Count { get; private set; }

            /// <summary>
            ///     Gets a version of the collection.
            /// </summary>
            [PublicAPI]
            public int Version { get; private set; }

            /// <summary>
            ///     Initializes a new instance of the <see cref="PointsCollection"/> class.
            /// </summary>
            [PublicAPI]
            public PointsCollection()
            {
                Points = new Vector3[DefaultCapacity];
            }

            /// <summary>
            ///     Clears an input points collection.
            /// </summary>
            [PublicAPI]
            public void Clear()
            {
                if (Count > 0)
                {
                    Count = 0;
                    Version++;
                }
            }

            /// <summary>
            ///     Adds an input point to the collection.
            /// </summary>
            /// <param name="point">A position of the input point.</param>
            [PublicAPI]
            public void Add(Vector3 point)
            {
                if (Count == Points.Length)
                {
                    EnsureCapacity(Count + 1);
                }

                Points[Count++] = point;
                Version++;
            }

            /// <summary>
            ///     Adds input points to the collection.
            /// </summary>
            /// <param name="points">A collection of points which should be added.</param>
            [PublicAPI]
            public void AddRange([NotNull] IEnumerable<Vector3> points)
            {
                if (points == null)
                {
                    throw new ArgumentNullException("points");
                }

                foreach (Vector3 Point in points)
                {
                    Add(Point);
                }

                Version++;
            }

            /// <summary>
            ///     Gets or sets a capacity of the <see cref="Points"/> array.
            /// </summary>
            private int Capacity
            {
                get
                {
                    return Points.Length;
                }

                set
                {
                    if (value < Count)
                    {
                        throw new ArgumentOutOfRangeException("value");
                    }

                    if (value != Points.Length)
                    {
                        if (value > 0)
                        {
                            Vector3[] NewItems = new Vector3[value];
                            if (Count > 0)
                            {
                                Array.Copy(Points, 0, NewItems, 0, Count);
                            }

                            Points = NewItems;
                        }
                        else
                        {
                            Points = EmptyArray;
                        }
                    }
                }
            }

            /// <summary>
            ///     Ensures that the capacity of this list is at least the given minimum
            ///     value. If the currect capacity of the list is less than min, the
            ///     capacity is increased to twice the current capacity or to min,
            ///     whichever is larger.
            /// </summary>
            /// <param name="min">Minimum needed capacity.</param>
            private void EnsureCapacity(int min)
            {
                if (Points.Length < min)
                {
                    int NewCapacity = Points.Length == 0 ? DefaultCapacity : Points.Length * 2;

                    // Allow the list to grow to maximum possible capacity (~2G elements) before encountering overflow.
                    if ((uint)NewCapacity > MaxArrayLength)
                    {
                        NewCapacity = MaxArrayLength;
                    }

                    if (NewCapacity < min)
                    {
                        NewCapacity = min;
                    }

                    Capacity = NewCapacity;
                }
            }
        }

        /// <summary>
        ///     Gets a color of the line.
        /// </summary>
        [PublicAPI]
        public Color Color { get; private set; }

        /// <summary>
        ///     Gets a value indicating whether line data has points.
        /// </summary>
        [PublicAPI]
        public bool HasPoints
        {
            get
            {
                return InputPoints.Count > 0;
            }
        }

        /// <summary>
        ///     The collection of input points.
        /// </summary>
        private readonly PointsCollection InputPoints = new PointsCollection();

        /// <summary>
        ///     The collection of output points.
        /// </summary>
        private readonly PointsCollection OutputPoints = new PointsCollection();

        /// <summary>
        ///     The input collection version which used to generate output last time.
        /// </summary>
        private int TargetInputVersion = -1;

        /// <summary>
        ///     Initializes a new instance of the <see cref="OverlayLine"/> class.
        /// </summary>
        [PublicAPI]
        public OverlayLine()
        {
            Color = OverlayUtilities.GreenHandleColor.Dark;
        }

        /// <summary>
        ///     Sets a color of the line.
        /// </summary>
        /// <param name="color">The new color.</param>
        [PublicAPI]
        public void SetColor(Color color)
        {
            Color = color;
        }

        /// <summary>
        ///     Sets a color of the line
        /// </summary>
        /// <param name="isError">Indicates whether line displays an error of something.</param>
        [PublicAPI]
        public void SetColor(bool isError)
        {
            Color = isError ? OverlayUtilities.RedHandleColor.Dark : OverlayUtilities.GreenHandleColor.Dark;
        }

        /// <summary>
        ///     Clears an input points collection.
        /// </summary>
        [PublicAPI]
        public void ClearInput()
        {
            InputPoints.Clear();
        }

        /// <summary>
        ///     Adds an input point to the collection.
        /// </summary>
        /// <param name="point">A position of the input point.</param>
        [PublicAPI]
        public void AddInputPoint(Vector3 point)
        {
            InputPoints.Add(point);
        }

        /// <summary>
        ///     Adds input points to the collection.
        /// </summary>
        /// <param name="points">A collection of points which should be added.</param>
        [PublicAPI]
        public void AddInputPoints([NotNull] IEnumerable<Vector3> points)
        {
            InputPoints.AddRange(points);
        }

        /// <summary>
        ///     Gets an array of output points which should be drawn.
        /// </summary>
        /// <returns>An array of points.</returns>
        [PublicAPI]
        [NotNull]
        public Vector3[] GetOutputPoints()
        {
            ActualizeOutput();

            Vector3[] Result = new Vector3[OutputPoints.Count];
            GetOutputPointsNonAlloc(Result);
            return Result;
        }

        /// <summary>
        ///     Gets an array of output points which should be drawn without memory allocations.
        /// </summary>
        /// <param name="outputArray">An array of points where to write.</param>
        /// <returns>The count of output points.</returns>
        [PublicAPI]
        public int GetOutputPointsNonAlloc([NotNull] Vector3[] outputArray)
        {
            if (outputArray == null)
            {
                throw new ArgumentNullException("outputArray");
            }

            ActualizeOutput();

            int OutputCount = OutputPoints.Count;
            if (OutputCount > outputArray.Length)
            {
                OutputCount = outputArray.Length;
            }

            Array.Copy(OutputPoints.Points, outputArray, OutputCount);
            return OutputPoints.Count;
        }

        /// <summary>
        ///     Regenerates output points if needed.
        /// </summary>
        private void ActualizeOutput()
        {
            if (TargetInputVersion != InputPoints.Version)
            {
                OutputPoints.Clear();

                for (int Index = 0; Index < InputPoints.Count; Index++)
                {
                    if (Index == 0 || Index == InputPoints.Count - 1)
                    {
                        OutputPoints.Add(InputPoints[Index]);
                    }
                    else
                    {
                        const int Radius = 10;

                        Vector3 Input0 = InputPoints[Index - 1];
                        Vector3 Input1 = InputPoints[Index];
                        Vector3 Input2 = InputPoints[Index + 1];

                        Vector3 P0 = Input1 - ((Input1 - Input0).normalized * Radius);
                        Vector3 P1 = Input1 - ((Input1 - Input2).normalized * Radius);

                        Vector3 BDirection = P1 - P0;
                        float BDistance = BDirection.magnitude;
                        if (BDistance < Radius)
                        {
                            // Move points from center to enlarge a distance between them.
                            float Offset = (2f * Radius) - BDistance;
                            Vector3 PushDistance = BDirection.normalized * Offset;
                            P1 = P1 + PushDistance;

                            Vector3 A0 = P0 + ((P0 - Input0).normalized * Radius);
                            Vector3 A1 = P1 + ((P1 - Input2).normalized * Radius);

                            GenerateCurvePoints(OutputPoints, t => MCUtilities.Bezier.Cubic(P0, A0, A1, P1, t));
                        }
                        else
                        {
                            GenerateCurvePoints(OutputPoints, t => MCUtilities.Bezier.Quadratic(P0, Input1, P1, t));
                        }

                    }
                }

                TargetInputVersion = InputPoints.Version;
            }
        }

        /// <summary>
        ///     Generates a curve points using a specified bezier function.
        /// </summary>
        /// <param name="points">Target points collection.</param>
        /// <param name="bezierFunc">The bezier function with assigned parameters.</param>
        /// <example>
        ///     <code>
        ///         GenerateCurvePoints(OutputPoints, t => MCUtilities.Bezier.Cubic(P0, A0, A1, P1, t));
        ///     </code>
        /// </example>
        private void GenerateCurvePoints([NotNull] PointsCollection points, [NotNull] Func<float, Vector3> bezierFunc)
        {
            if (points == null)
            {
                throw new ArgumentNullException("points");
            }

            if (bezierFunc == null)
            {
                throw new ArgumentNullException("bezierFunc");
            }

            const int SectionsCount = 10;
            const float StepT = 1f / SectionsCount;

            points.Add(bezierFunc(0f));
            for (float T = StepT; T < 1f; T += StepT)
            {
                points.Add(bezierFunc(T));
            }
            points.Add(bezierFunc(1f));
        }
    }
}