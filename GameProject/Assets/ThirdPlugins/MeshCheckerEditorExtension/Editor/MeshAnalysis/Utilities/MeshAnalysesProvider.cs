using System;
using System.Collections.Generic;
using System.Linq;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.Utilities;
using JetBrains.Annotations;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework
{
    /// <summary>
    ///     Represents the provider of mesh analysis types.
    /// </summary>
    internal static class MeshAnalysesProvider
    {
        /// <summary>
        ///     Lifetime of a cache.
        /// </summary>
        private const float CacheLifetime = 5f;

        /// <summary>
        ///     Time of the last cache.
        /// </summary>
        private static DateTime CacheTime;

        /// <summary>
        ///     Cached mesh analysis types.
        /// </summary>
        [CanBeNull]
        private static List<Type> Cache;

        /// <summary>
        ///     Get all types which derived from the <see cref="MeshAnalysis"/> with the <see cref="MeshAnalysisAttribute"/> on them and create new instances.
        /// </summary>
        /// <returns>
        ///     The list of mesh analyses instances.
        /// </returns>
        [NotNull]
        public static List<MeshAnalysis> GetMeshAnalyses()
        {
            IEnumerable<Type> MeshAnalysisTypes = GetMeshAnalysisTypes();
            return ReflectionUtilities.CreateInstancesFromTypes<MeshAnalysis>(MeshAnalysisTypes);
        }

        /// <summary>
        ///     Cleans up a memory.
        /// </summary>
        internal static void Cleanup()
        {
            Cache = null;
        }

        /// <summary>
        ///     Get all types derived from the <see cref="MeshAnalysis"/> with the <see cref="MeshAnalysisAttribute"/> on them.
        /// </summary>
        /// <returns>
        ///     Collection of mesh analysis types.
        /// </returns>
        [NotNull]
        private static IEnumerable<Type> GetMeshAnalysisTypes()
        {
            // If cache not exists or cache are out of date
            if (Cache == null
                || (DateTime.Now - CacheTime).TotalSeconds > CacheLifetime)
            {
                Cache = ReflectionUtilities.GetTypesWithAttribute<MeshAnalysisAttribute>().ToList();
                CacheTime = DateTime.Now;
            }

            return Cache;
        }
    }
}