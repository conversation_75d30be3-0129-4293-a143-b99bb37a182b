using System;

using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;

using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework
{
    /// <summary>
    ///     The <see cref="IndexLoop"/> helps to cycle through loop of indexes
    /// </summary>
    [PublicAPI]
    public class IndexLoop
    {
        /// <summary>
        ///     Format of <see cref="IndexLoop"/> string representation.
        /// </summary>
        public enum LoopStringFormat
        {
            /// <summary>
            ///     The format at which the current index (<see cref="Current"/>) and the maximum (<see cref="Max"/>) are displayed.
            ///     <para>
            ///         The current index belongs to the interval from <see cref="Min"/> to <see cref="Max"/>.
            ///     </para>
            ///     <remarks>
            ///         <para>
            ///             When Min=3, Max=10 and Current=6 then result string will be «6/10»
            ///         </para>
            ///         <para>
            ///             When Min=0, Max=10 and Current=10 then result string will be «10/10»
            ///         </para>
            ///         <para>
            ///             When Min=0, Max=10 and Current=0 then result string will be «0/10»
            ///         </para>
            ///     </remarks>
            /// </summary>
            CurrentToMax,

            /// <summary>
            ///     The format at which the index in loop and the count of indices are displayed.
            ///     <para>
            ///         The index in loop belongs to the interval from 1 to <see cref="Max"/>-<see cref="Min"/>+1.
            ///     </para>
            ///     <remarks>
            ///         <para>
            ///             When Min=3, Max=10 and Current=6 then result string will be «4/8»
            ///         </para>
            ///         <para>
            ///             When Min=0, Max=9 and Current=9 then result string will be «10/10»
            ///         </para>
            ///         <para>
            ///             When Min=0, Max=9 and Current=0 then result string will be «1/10»
            ///         </para>
            ///     </remarks>
            /// </summary>
            IndexToCount
        }

        /// <summary>
        ///     Rised when <see cref="Current"/> index change.
        /// </summary>
        [PublicAPI]
        public event Action<int> SelectionChangedEvent = delegate { };

        /// <summary>
        ///     Rised when selection is removed.
        /// </summary>
        [PublicAPI]
        public event Action SelectionRemovedEvent = delegate { };

        /// <summary>
        ///     Gets currently selected index value.
        ///     <para>
        ///         In interval from <see cref="Min"/> (inclusive) to <see cref="Max"/> (inclusive).
        ///     </para>
        /// </summary>
        [PublicAPI]
        public int Current { get; private set; }

        /// <summary>
        ///     Gets a value indicating whether one of indices is selected.
        ///     <para>
        ///         Opposite to <see cref="NotSelected"/>
        ///     </para>
        /// </summary>
        [PublicAPI]
        public bool Selected { get; private set; }

        /// <summary>
        ///     Gets a value indicating whether nothing selected.
        ///     <para>
        ///         Opposite to <see cref="Selected"/>
        ///     </para>
        /// </summary>
        [PublicAPI]
        public bool NotSelected
        {
            get
            {
                return !Selected;
            }
        }

        /// <summary>
        ///     Gets minimal index in loop.
        /// </summary>
        [PublicAPI]
        public int Min { get; private set; }

        /// <summary>
        ///     Gets maximal index in loop.
        /// </summary>
        [PublicAPI]
        public int Max { get; private set; }

        /// <summary>
        ///     Initializes a new instance of the <see cref="IndexLoop"/> class. 
        ///     New instance is empty.
        /// </summary>
        public IndexLoop() : this(-1, -1)
        {
        }

        /// <summary>
        ///     Initializes a new instance of the <see cref="IndexLoop"/> class. 
        ///     Indices will be in range from 0 to specified value.
        /// </summary>
        /// <param name="max">
        /// Maximal index value.
        /// </param>
        public IndexLoop(int max) : this(0, max)
        {
        }

        /// <summary>
        ///     Initializes a new instance of the <see cref="IndexLoop"/> class. 
        ///     Indices will be in the specified range.
        /// </summary>
        /// <param name="min">Minimal index value.</param>
        /// <param name="max">Maximal index value.</param>
        public IndexLoop(int min, int max)
        {
            Current = -1;
            Selected = false;
            Min = min;
            Max = max;
        }

        /// <summary>
        ///     Selects next index in loop.
        ///     <para>
        ///         If next index greater then <see cref="Max"/> value then <see cref="Min"/> value will be selected.
        ///     </para>
        /// </summary>
        [PublicAPI]
        public void Next()
        {
            Current++;

            if (Current > Max)
            {
                Current = Min;
            }

            Selected = true;
            try
            {
                SelectionChangedEvent(Current);
            }
            catch (Exception E)
            {
                Debug.LogException(E);
            }
        }

        /// <summary>
        ///     Selects previous index in loop.
        ///     <para>
        ///         If previous index lesser then <see cref="Min"/> value then <see cref="Max"/> value will be selected.
        ///     </para>
        /// </summary>
        [PublicAPI]
        public void Previous()
        {
            Current--;

            if (Current < Min)
            {
                Current = Max;
            }

            Selected = true;
            try
            {
                SelectionChangedEvent(Current);
            }
            catch (Exception E)
            {
                Debug.LogException(E);
            }
        }

        /// <summary>
        ///     Selects the specified index.
        /// </summary>
        /// <param name="index">Value that should be assigned to <see cref="Current"/></param>
        /// <exception cref="ArgumentOutOfRangeException">The index is out of loop ranges. Index must be within <see cref="Min"/> and <see cref="Max"/> values.</exception>
        [PublicAPI]
        public void Set(int index)
        {
            if (index < Min || index > Max)
            {
                throw new ArgumentOutOfRangeException("index", index.ToString(), "Index must be within range.");
            }

            Current = index;
            Selected = true;
            try
            {
                SelectionChangedEvent(Current);
            }
            catch (Exception E)
            {
                Debug.LogException(E);
            }
        }

        /// <summary>
        ///     Deselect current index.
        /// </summary>
        /// <remarks>
        ///     <see cref="SelectionRemovedEvent"/> will be rised.
        /// </remarks>
        [PublicAPI]
        public void Deselect()
        {
            Current = -1;
            Selected = false;
            try
            {
                SelectionRemovedEvent();
            }
            catch (Exception E)
            {
                Debug.LogException(E);
            }
        }

        /// <summary>
        ///     Converts the loop value to its string representation.
        /// </summary>
        /// <param name="format">
        ///     A custom format string.
        /// </param>
        /// <returns>
        ///     The string representation of the value of this instance.
        /// </returns>
        /// <remarks>
        ///     <para>
        ///         Use «%min%» for <see cref="Min"/> value.
        ///     </para>
        ///     <para>
        ///         Use «%max%» for <see cref="Max"/> value.
        ///     </para>
        ///     <para>
        ///         Use «%index%» for current index in loop.
        ///     </para>
        ///     <para>
        ///         Use «%current%» for <see cref="Current"/> value.
        ///     </para>
        ///     <para>
        ///         Use «%count%» for indices count value.
        ///     </para>
        /// </remarks>
        /// <example>
        ///     <code>
        ///         public string GetIndexToCountRepresentation(IndexLoop loop)
        ///         {
        ///             return loop.ToString("%index%/%count%");
        ///         }
        ///     </code>
        /// </example>
        /// <exception cref="ArgumentException">Format are null or empty.</exception>
        [PublicAPI]
        public string ToString([NotNull]string format)
        {
            if (string.IsNullOrEmpty(format))
            {
                throw new ArgumentException("Format must not be null or empty.", "format");
            }

            return format.Replace("%min%", Min.ToString())
                         .Replace("%max%", Max.ToString())
                         .Replace("%index%", (Current + 1 - Min).ToString())
                         .Replace("%current%", Current.ToString())
                         .Replace("%count%", Current.ToString());
        }

        /// <summary>
        ///     Converts the loop value to its string representation.
        /// </summary>
        /// <param name="format">
        ///     Format of string representation.
        ///     <para>
        ///         See <see cref="LoopStringFormat"/> for more details.
        ///     </para>
        /// </param>
        /// <returns>
        ///     The string representation of the value of this instance.
        /// </returns>
        [PublicAPI]
        public string ToString(LoopStringFormat format)
        {
            switch (format)
            {
                case LoopStringFormat.CurrentToMax:
                    return string.Concat(Current.ToString(), "/", Max.ToString());

                case LoopStringFormat.IndexToCount:
                default:
                    return string.Concat((Current + 1 - Min).ToString(), "/", (Max + 1 - Min).ToString());
            }
        }

        /// <summary>
        ///     Remove subscribers from events and prepare for GC.
        /// </summary>
        public void Release()
        {
            SelectionChangedEvent = null;
            SelectionRemovedEvent = null;
        }

        /// <summary>
        ///     Draws <see cref="IndexLoop"/> toolbar GUI in Layout zone.
        /// </summary>
        /// <param name="enabled">When <b>enabled</b> - LoopToolbar are drawn as enabled GUI element. When <b>disabled</b> - as disabled.</param>
        /// <seealso cref="MCGUI.DrawLoopToolbar"/>
        /// <exception cref="InvalidOperationException">DrawLoopToolbar should be invoked in OnGUI scope.</exception>
        public void Draw(bool enabled)
        {
            MCGUI.DrawLoopToolbar(this, enabled);
        }
    }
}