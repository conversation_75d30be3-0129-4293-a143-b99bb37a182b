using System.Collections.Generic;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;
using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework
{
    /// <summary>
    ///     Represents a selection grid for toolbar which allow to select a polygon drawing type.
    /// </summary>
    public class PolygonDrawingTypeToolbar
    {
        /// <summary>
        ///     Gets or sets a value indicating whether deselection allowed.
        ///     <para>
        ///         If <c>true</c> then the <see cref="PolygonsDrawingType.DontDraw"/> value is allowed; otherwise, not allowed.
        ///     </para>
        /// </summary>
        [PublicAPI]
        public bool AllowDeselect { get; set; }

        /// <summary>
        ///     Gets a current drawing type.
        /// </summary>
        [PublicAPI]
        public PolygonsDrawingType Current { get; private set; }

        private List<SelectionGridItem<PolygonsDrawingType>> InnerPolygonsDrawingTypeSelectionGridItems;

        /// <summary>
        ///     The collection of selection grid items for changing the polygon drawing type.
        /// </summary>
        private List<SelectionGridItem<PolygonsDrawingType>> PolygonsDrawingTypeSelectionGridItems
        {
            get
            {
                if (InnerPolygonsDrawingTypeSelectionGridItems == null)
                {
                    InnerPolygonsDrawingTypeSelectionGridItems = new List<SelectionGridItem<PolygonsDrawingType>>
                    {
                        new SelectionGridItem<PolygonsDrawingType>(
                            new GUIContent(MCGUI.Icons.WireframeTriangleIcon.Current, "Wireframe"),
                            PolygonsDrawingType.Wireframe,
                            22,
                            MCGUI.ToolbarHeight,
                            EditorStyles.toolbarButton),

                        new SelectionGridItem<PolygonsDrawingType>(
                            new GUIContent(MCGUI.Icons.SolidTriangleIcon.Current, "Solid"),
                            PolygonsDrawingType.Solid,
                            22,
                            MCGUI.ToolbarHeight,
                            EditorStyles.toolbarButton),
                    };
                }

                return InnerPolygonsDrawingTypeSelectionGridItems;
            }
        }

        /// <summary>
        ///     Initializes a new instance of the <see cref="PolygonDrawingTypeToolbar"/> class.
        /// </summary>
        /// <param name="initialType">The initial drawing type.</param>
        [PublicAPI]
        public PolygonDrawingTypeToolbar(PolygonsDrawingType initialType)
        {
            Current = initialType;
        }

        /// <summary>
        ///     Draws the selection grid.
        /// </summary>
        /// <returns>The current drawing type.</returns>
        [PublicAPI]
        public PolygonsDrawingType Draw()
        {
            PolygonsDrawingType EmptyValue = AllowDeselect ? PolygonsDrawingType.DontDraw : PolygonsDrawingType.Wireframe;

            GUILayout.BeginHorizontal();
            Current = MCGUI.DrawSelectionGrid(PolygonsDrawingTypeSelectionGridItems, Current, EmptyValue);
            GUILayout.EndHorizontal();

            return Current;
        }
    }
}