using System;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Represents a shading option which creates material from shader with specified <see cref="ShaderName"/>.
    /// </summary>
    [PublicAPI]
    public abstract class BasicShaderShadingOption : MeshAnalysisShadingOption
    {
        /// <summary>
        ///     Gets the name of shader that should be used.
        /// </summary>
        /// <remarks>
        ///     The name should contain all path, e.g. 'Hidden/MeshChecker/BinormalShading'
        /// </remarks>
        [NotNull]
        protected abstract string ShaderName { get; }

        /// <summary>
        ///     The array of materials applied to submeshes.
        /// </summary>
        private Material[] Materials;

        /// <summary>
        ///     The backing field for the <see cref="ShadingMaterial"/> property.
        /// </summary>
        private Material InnerShadingMaterial;

        /// <summary>
        ///     Gets the material that colors a mesh using a specific shader.
        /// </summary>
        /// <exception cref="InvalidOperationException" accessor="get">ShaderName must not be null or empty.</exception>
        protected Material ShadingMaterial
        {
            get
            {
                if (InnerShadingMaterial == null)
                {
                    if (string.IsNullOrEmpty(ShaderName))
                    {
                        throw new InvalidOperationException("ShaderName must not be null or empty.");    
                    }

                    Shader FoundShader = Shader.Find(ShaderName);

                    if (FoundShader == null)
                    {
                        Debug.LogError(string.Format("[MeshAnalysis] Failed to find shader in '{0}' shading option.", Title));
                    }

                    InnerShadingMaterial = new Material(FoundShader);
                    ConfigureMaterial(InnerShadingMaterial);
                }

                return InnerShadingMaterial;
            }
        }

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
            Materials = new Material[mesh.subMeshCount];
            for (int Index = 0; Index < Materials.Length; Index++)
            {
                Materials[Index] = ShadingMaterial;
            }
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
            if (InnerShadingMaterial != null)
            {
                UnityEngine.Object.DestroyImmediate(InnerShadingMaterial);
            }

            Materials = null;
        }

        /// <inheritdoc />
        public override Material[] GetMaterials()
        {
            return Materials;
        }

        #endregion

        /// <summary>
        ///     Override this method if you need to configure created material.
        /// </summary>
        /// <param name="material">The newly created material used to display mesh.</param>
        protected virtual void ConfigureMaterial([NotNull]Material material)
        {
        }
    }
}
