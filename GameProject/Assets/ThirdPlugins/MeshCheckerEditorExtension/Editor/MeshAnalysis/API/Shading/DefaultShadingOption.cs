using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework
{ 
    /// <summary>
    ///     An internal shading option which used to display object when no shading options available.
    /// </summary>
    internal sealed class DefaultShadingOption : BasicShaderShadingOption
    {
        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get
            {
                return "Not available";
            }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get
            {
                return 0;
            }
        }

        #endregion

        #region Overrides of BasicShaderShadingOption

        /// <inheritdoc />
        protected override string ShaderName
        {
            get
            {
                return "Standard";
            }
        }

        #endregion
    }
}
