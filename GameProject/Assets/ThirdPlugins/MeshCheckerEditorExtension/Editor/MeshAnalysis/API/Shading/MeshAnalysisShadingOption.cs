using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Basic shading option class.
    ///     <para>
    ///         Create derived class and mark it with the attribute <see cref="MeshAnalysisShadingAttribute"/> to 
    ///         add a new shading option to the shading options menu of a <see cref="MeshAnalysisWindow"/>.
    ///     </para>
    /// </summary>
    [PublicAPI]
    public abstract class MeshAnalysisShadingOption : IHaveAlias
    {
        /// <summary>
        ///     Gets the name of shading option.
        /// </summary>
        [NotNull]
        [PublicAPI]
        public abstract string Title { get; }

        /// <summary>
        ///     Gets the priority of the shading option in an options list.
        /// </summary>
        /// <remarks>
        ///     <para>
        ///         Shading options group by their priority. Every 100 will merge into one group.
        ///     </para>
        ///     <para>
        ///         <para>_______________</para>
        ///         <para>Option -   0</para>
        ///         <para>Option -   1</para>
        ///         <para>Option -   2</para>
        ///         <para>_______________</para>
        ///         <para>Option - 100</para>
        ///         <para>Option - 120</para>
        ///         <para>Option - 121</para>
        ///         <para>Option - 199</para>
        ///         <para>_______________</para>
        ///         <para>Option - 460</para>
        ///         <para>Option - 461</para>
        ///         <para>_______________</para>
        ///     </para>
        /// </remarks>
        [PublicAPI]
        public abstract int Priority { get; }

        /// <summary>
        ///     <para>Gets or sets a value indicating whether a mesh should be drawn in wireframe mode.</para>
        /// </summary>
        /// <remarks>
        ///     <para>This option is extremly useful for wireframe shaders.</para>
        ///     <para>If Wireframe is <c>true</c> - <see cref="GL.wireframe"/> will be <c>true</c> when observed mesh is drawn.</para>
        /// </remarks>
        public virtual bool Wireframe { get; protected set; }

        /// <summary>
        ///     Initialize the shading option for a specified mesh.
        /// </summary>
        /// <param name="mesh">A mesh which will be shaded.</param>
        [PublicAPI]
        public abstract void Initialize([NotNull]Mesh mesh);

        /// <summary>
        ///     Deinitialize the shading option and release used resources.
        /// </summary>
        [PublicAPI]
        public abstract void Deinitialize();

        /// <summary>
        ///     Get materials which will be applied to the shaded mesh.
        /// </summary>
        /// <returns>
        ///     The array of materials. The length of the array must be equal to the count of submeshes.
        ///     <seealso cref="Mesh.subMeshCount"/>
        /// </returns>
        [PublicAPI]
        [NotNull]
        public abstract Material[] GetMaterials();

        /// <summary>
        ///     Draw settings in the target position.
        /// </summary>
        /// <param name="position">The position where settings should be drawn.</param>
        [PublicAPI]
        public virtual void DrawSettings(Rect position)
        {
            EditorGUILayout.HelpBox("Settings not implemented", MessageType.Info);
        }

        /// <summary>
        ///     Returns a value indicating whether the shading option has settings.
        /// </summary>
        /// <returns>
        ///     A value indicating whether the shading option has settings.
        /// </returns>
        /// <remarks>
        ///     Default: false.
        /// </remarks>
        [PublicAPI]
        public virtual bool GetHaveSettings()
        {
            return false;
        }

        /// <summary>
        ///     Returns the width of a settings popup.
        /// </summary>
        /// <returns>
        ///     The width of a settings popup.
        /// </returns>
        /// <remarks>
        ///     Default: 300px.
        /// </remarks>
        [PublicAPI]
        public virtual float GetSettingsWidth()
        {
            return 300;
        }

        /// <summary>
        ///     Returns the height of a settings popup.
        /// </summary>
        /// <returns>
        ///     The height of a settings popup.
        /// </returns>
        /// <remarks>
        ///     Default: 100px.
        /// </remarks>
        [PublicAPI]
        public virtual float GetSettingsHeight()
        {
            return 100;
        }

        /// <summary>
        ///     Draw the description in the target position.
        /// </summary>
        /// <param name="position">The position where the description should be drawn.</param>
        [PublicAPI]
        public virtual void DrawDescription(Rect position)
        {
        }

        /// <summary>
        ///     Returns a value indicating whether the shading option has a description.
        /// </summary>
        /// <returns>
        ///     <c>true</c> if the shading option has a description; otherwise, <c>false</c>.
        /// </returns>
        /// <remarks>
        ///     Default: false.
        /// </remarks>
        [PublicAPI]
        public virtual bool HasDescription()
        {
            return false;
        }

        /// <summary>
        ///     Returns the size of the description window.
        /// </summary>
        /// <returns>
        ///     Width and height of the description window.
        /// </returns>
        /// <remarks>
        ///     Default: 800 x 600.
        /// </remarks>
        [PublicAPI]
        public virtual Vector2 GetDescriptionWindowSize()
        {
            return new Vector2(800, 600);
        }
    }
}
