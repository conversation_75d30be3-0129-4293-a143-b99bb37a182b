using System.Collections.Generic;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Represents a bunch of shading options for an observed <see cref="Mesh"/> in a <see cref="MeshAnalysisWindow"/>
    /// </summary>
    [PublicAPI]
    public abstract class MeshAnalysisShadingGroup
    {
        /// <summary>
        ///     Returns a collection of <see cref="MeshAnalysisShadingOption"/> for the specified <see cref="Mesh"/>.
        /// </summary>
        /// <param name="mesh">A mesh which will be shaded.</param>
        /// <returns>
        ///     Collection of shading options.
        /// </returns>
        [NotNull]
        [PublicAPI]
        public abstract IEnumerable<MeshAnalysisShadingOption> GetOptions([NotNull]Mesh mesh);
    }
}
