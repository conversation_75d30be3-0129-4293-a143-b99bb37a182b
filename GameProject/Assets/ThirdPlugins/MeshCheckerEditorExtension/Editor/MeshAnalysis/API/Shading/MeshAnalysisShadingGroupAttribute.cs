using System;
using JetBrains.Annotations;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Should be used on <see cref="MeshAnalysisShadingOption"/> 
    ///     and causes <PERSON><PERSON><PERSON><PERSON><PERSON> to add generated by group options to the shading options menu of a <see cref="MeshAnalysisWindow"/>.
    /// </summary>
    [PublicAPI]
    [AttributeUsage(AttributeTargets.Class, Inherited = false)]
    [BaseTypeRequired(typeof(MeshAnalysisShadingGroup))]
    public sealed class MeshAnalysisShadingGroupAttribute : Attribute
    {
    }
}
