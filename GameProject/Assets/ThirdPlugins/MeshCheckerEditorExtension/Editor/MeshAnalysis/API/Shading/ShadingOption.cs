using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Represents a shading option which creates from code e.g. from a <see cref="MeshAnalysisShadingGroup"/>.
    /// </summary>
    [PublicAPI]
    public sealed class ShadingOption : MeshAnalysisShadingOption
    {
        /// <summary>
        ///     Shading materials.
        /// </summary>
        private readonly Material[] Materials;

        /// <summary>
        ///     Inner value of the <see cref="Title"/>
        /// </summary>
        private readonly string InnerTitle;

        /// <summary>
        ///     Inner value of the <see cref="Priority"/>
        /// </summary>
        private readonly int InnerPriority;

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        [PublicAPI]
        public override string Title
        {
            get
            {
                if (InnerTitle != null)
                {
                    return InnerTitle;
                }
                else
                {
                    return string.Empty;
                }
            }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override int Priority
        {
            get { return InnerPriority; }
        }

        /// <inheritdoc />
        [PublicAPI]
        public override void Initialize(Mesh mesh)
        {
        }

        /// <inheritdoc />
        [PublicAPI]
        public override void Deinitialize()
        {
        }

        /// <inheritdoc />
        [PublicAPI]
        public override Material[] GetMaterials()
        {
            return Materials;
        }

        #endregion

        /// <summary>
        ///     Initializes a new instance of the <see cref="ShadingOption"/> class.
        /// </summary>
        /// <param name="title">The title of a shading option.</param>
        /// <param name="priority">
        ///     The priority of a shading option.
        /// </param>
        /// <param name="materials">Materials which will apply to the mesh.</param>
        /// <seealso cref="MeshAnalysisShadingOption.Priority"/>
        [PublicAPI]
        public ShadingOption(string title, int priority, Material[] materials)
        {
            InnerTitle = title;
            InnerPriority = priority;
            Materials = materials;
        }
    }
}
