using System;
using System.Threading;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Basic MeshAnalysis class.
    ///     <para>
    ///         Create derived class, mark it with the attribute <see cref="MeshAnalysisAttribute"/> to add new analysis.
    ///     </para>
    /// </summary>
    [PublicAPI]
    public abstract class MeshAnalysis
    {
        /// <summary>
        ///     Occurs when the analysis has been ended.
        /// </summary>
        [PublicAPI]
        public event Action<MeshAnalysisResultType> AnalysisEndedEvent;

        /// <summary>
        ///     Gets or sets a value indicating whether the analysis is interactable.
        /// </summary>
        /// <remarks>
        ///     Method <see cref="OnClick"/> invokes depends on the Interactable flag.
        /// </remarks>
        [PublicAPI]
        public bool Interactable { get; protected set; }

        /// <summary>
        ///     Gets a value indicating whether the analysis ended.
        /// </summary>
        [PublicAPI]
        public bool Ended { get; private set; }

        /// <summary>
        ///     Gets the result of the analysis.
        /// </summary>
        [PublicAPI]
        public MeshAnalysisResultType Result { get; private set; }

        /// <summary>
        ///     Gets a value indicating whether the result is equal to <see cref="MeshAnalysisResultType.Warning"/> or <see cref="MeshAnalysisResultType.Error"/>
        /// </summary>
        public bool NotSuccess
        {
            get
            {
                return Result == MeshAnalysisResultType.Warning
                  || Result == MeshAnalysisResultType.Error;
            }
        }

        /// <summary>
        ///     Gets the mesh to be analysed.
        /// </summary>
        [PublicAPI]
        protected Mesh Mesh { get; private set; }

        /// <summary>
        ///     Indicates whether the analysis is initialized.
        /// </summary>
        private bool Initialized;

        /// <summary>
        ///     Run analysis for <see cref="Mesh"/>.
        ///     <para>
        ///         This method can be invoked in second thread. You can't access Unity resources from it, 
        ///         instead cache resources in <see cref="OnInitialize"/> method.
        ///     </para>
        /// </summary>
        public abstract void RunAnalysis();

        /// <summary>
        ///     Draw result of the analysis in target position.
        /// </summary>
        /// <param name="position">Position where the result should be drawed.</param>
        [PublicAPI]
        public abstract void Draw(Rect position);

        /// <summary>
        ///     Get needed height for specified width to draw the result of the analysis.
        /// </summary>
        /// <param name="width">
        ///     Width of the drawing zone.
        /// </param>
        /// <returns>
        ///     Height of the drawing zone with specified width.
        /// </returns>
        [PublicAPI]
        public abstract float GetHeight(float width);

        /// <summary>
        ///     Occurs when user perform click on the drawed result of this analysis if <see cref="Interactable"/> flag is true.
        /// </summary>
        [PublicAPI]
        public virtual void OnClick()
        {
        }

        /// <summary>
        ///     Initializes the analysis for a specified mesh.
        /// </summary>
        /// <param name="mesh">
        ///     A mesh that should be analysed.
        /// </param>
        /// <exception cref="ArgumentNullException">MeshAnalysis initialization failed: the mesh argument are null. <paramref name="mesh"/></exception>
        [PublicAPI]
        public void Initialize([NotNull]Mesh mesh)
        {
            if (mesh == null)
            {
                throw new ArgumentNullException("mesh", "[MeshChecker editor extension] MeshAnalysis initialization failed: Mesh argument are null.");
            }

            if (Initialized)
            {
                return;
            }

            Mesh = mesh;
            OnInitialize(Mesh);

            Initialized = true;
        }

        /// <summary>
        ///     Deinitializes the analysis.
        /// </summary>
        [PublicAPI]
        public void Deinitialize()
        {
            if (!Initialized)
            {
                return;
            }

            AnalysisEndedEvent = null;

            OnDeinitialize();

            Initialized = false;
        }

        /// <summary>
        ///     Runs the analysis in a new separate thread.
        /// </summary>
        /// <exception cref="NotSupportedException">The common language runtime (CLR) is hosted, and the host does not support this action.</exception>
        [PublicAPI]
        public void RunAnalysisInThread()
        {
            ThreadPool.QueueUserWorkItem(arg =>
            {
                try
                {
                    RunAnalysis();
                }
                catch (Exception E)
                {
                    Debug.LogError(string.Format("[MeshAnalysis] Failed to run analysis in another thread.\n{0}", E));
                    End(MeshAnalysisResultType.Failed);
                }
            });
        }

        /// <summary>
        ///     Occurs when a manager prepares to run analyses.
        ///     <para>
        ///         This method invoked in main thread. Feel free to use Unity resources.
        ///     </para>
        /// </summary>
        /// <param name="mesh">Mesh for which analysis will be runned.</param>
        /// <remarks>
        ///     This method are good point to cache Unity resources. 
        ///     <see cref="RunAnalysis"/> can be invoked in separate thread and inside it you don't have access to resources like <see cref="UnityEngine.Mesh.vertices"/>.
        /// </remarks>
        [PublicAPI]
        protected abstract void OnInitialize([NotNull]Mesh mesh);

        /// <summary>
        ///     Occurs when a manager clean its data for releasing the memory.
        /// </summary>
        [PublicAPI]
        protected abstract void OnDeinitialize();

        /// <summary>
        ///     Ends the analysis with specified result.
        /// </summary>
        /// <param name="result">
        ///     Specifies the result of analysis.
        /// </param>
        [PublicAPI]
        protected void End(MeshAnalysisResultType result)
        {
            Result = result;
            Ended = true;

            if (AnalysisEndedEvent != null)
            {
                try
                {
                    AnalysisEndedEvent(result);
                }
                catch (Exception E)
                {
                    Debug.LogException(E);
                }
            }
        }
    }
}
