using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;

using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Represents a simple <see cref="MeshAnalysis"/> which drawed as <see cref="EditorGUI.HelpBox"/> 
    ///     with the specified caption when the result are <see cref="MeshAnalysisResultType.Error"/> or <see cref="MeshAnalysisResultType.Warning"/>
    /// </summary>
    [PublicAPI]
    public abstract class MessageMeshAnalysis : MeshAnalysis
    {
        /// <summary>
        ///     The inner value of the <see cref="Message"/> property.
        /// </summary>
        private string InnerMessage;

        /// <summary>
        ///     Gets or sets the message that explains result of the analysis.
        ///     Showed if the analysis has <see cref="MeshAnalysisResultType.Error"/> or <see cref="MeshAnalysisResultType.Warning"/> result.
        /// </summary>
        [PublicAPI]
        [CanBeNull]
        public string Message
        {
            get
            {
                return InnerMessage;
            }

            protected set
            {
                InnerMessage = value;
                MessageContent = new GUIContent(value);
            }
        }

        /// <summary>
        ///     Gets the <see cref="GUIContent"/> instance of the message.
        /// </summary>
        [PublicAPI]
        [CanBeNull]
        public GUIContent MessageContent { get; private set; }

        #region Overrides of MeshAnalysis

        /// <inheritdoc />
        public override float GetHeight(float width)
        {
            return MCGUI.AnalysisReportMessage.GetHeight(width, MessageContent);
        }

        /// <inheritdoc />
        public override void Draw(Rect position)
        {
            MCGUI.AnalysisReportMessage.Draw(position, Result, MessageContent);
        }

        #endregion

        /// <summary>
        ///     Ends the analysis with a specified result and message.
        /// </summary>
        /// <param name="result">Specifies the result type of the analysis.</param>
        /// <param name="message">The message that explains result of the analysis.</param>
        /// <seealso cref="Message"/>
        [PublicAPI]
        protected void End(MeshAnalysisResultType result, [CanBeNull]string message)
        {
            Message = message;
            End(result);
        }
    }
}
