using System;
using JetBrains.Annotations;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Should be used on <see cref="MeshAnalysis"/> and causes MeshChecker to run analyses marked with such attributes for an analyzed meshes.
    /// </summary>
    [PublicAPI]
    [AttributeUsage(AttributeTargets.Class, Inherited = false)]
    [BaseTypeRequired(typeof(MeshAnalysis))]
    public sealed class MeshAnalysisAttribute : Attribute
    {
    }
}
