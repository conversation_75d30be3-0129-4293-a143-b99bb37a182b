using System;
using JetBrains.Annotations;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Represents attribute used to add alias to the <see cref="ShadingOption"/> and the <see cref="MeshAnalysisTool"/>.
    /// </summary>
    /// <remarks>
    ///     The alias of a tool or a shading option can be received by invoking <see cref="MeshAnalysisUtilities.GetAliasSafe"/>,
    ///     <see cref="MeshAnalysisUtilities.GetAliasSafe{T}"/> or <see cref="MeshAnalysisUtilities.GetAlias"/>.
    /// </remarks>
    [AttributeUsage(AttributeTargets.Class, Inherited = false)]
    [PublicAPI]
    public sealed class AliasAttribute : Attribute
    {
        /// <summary>
        ///     Alias of the class that has attribute <see cref="AliasAttribute"/>.
        /// </summary>
        [NotNull]
        internal readonly string Alias;

        /// <summary>
        ///     Initializes a new instance of the <see cref="AliasAttribute"/> class.
        /// </summary>
        /// <param name="alias">The class alias.</param>
        public AliasAttribute([NotNull]string alias)
        {
            Alias = alias;
        }
    }
}
