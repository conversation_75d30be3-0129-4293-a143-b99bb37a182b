using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;

using JetBrains.Annotations;

using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework
{
    /// <summary>
    ///     An internal tool which used when no other tools was found.
    /// </summary>
    internal sealed class EmptyMeshAnalysisTool : MeshAnalysisTool
    {
        #region Overrides of MeshAnalysisTool

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
        }

        #endregion

        /// <summary>
        ///     Initializes a new instance of the <see cref="EmptyMeshAnalysisTool"/> class.
        /// </summary>
        /// <param name="name">The tool name.</param>
        /// <param name="priority">A priority of the tool in menu. <see cref="MeshAnalysisTool.Priority"/></param>
        public EmptyMeshAnalysisTool([<PERSON>BeNull] string name = null, int? priority = null)
        {
            SetToolName(name);
            if (priority.HasValue)
            {
                Priority = priority.Value;
            }
        }
    }
}