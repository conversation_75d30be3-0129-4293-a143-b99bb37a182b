using System;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Should be used on <see cref="MeshAnalysisTool"/> 
    ///     and causes <PERSON><PERSON><PERSON><PERSON><PERSON> to add the tool to the tools menu of a <see cref="MeshAnalysisWindow"/>.
    /// </summary>
    [PublicAPI]
    [AttributeUsage(AttributeTargets.Class, Inherited = false)]
    [BaseTypeRequired(typeof(MeshAnalysisTool))]
    public sealed class MeshAnalysisToolAttribute : Attribute
    {
        /// <summary>
        ///     Name of a tool.
        /// </summary>
        internal readonly string Name;

        /// <summary>
        ///     Priority of a tool.
        /// </summary>
        internal readonly int Priority;

        /// <summary>
        ///     Keybinding of a tool.
        /// </summary>
        internal readonly KeyCode Keybinding;

        /// <summary>
        ///     Initializes a new instance of the <see cref="MeshAnalysisToolAttribute"/> class.
        /// </summary>
        /// <param name="name">The tool name.</param>
        /// <param name="priority">The tool priority.</param>
        /// <param name="keybinding">Specifies the hotkey binded to the tool.</param>
        public MeshAnalysisToolAttribute(string name, int priority, KeyCode keybinding = KeyCode.None)
        {
            Name = name;
            Priority = priority;
            Keybinding = keybinding;
        }
    }
}
