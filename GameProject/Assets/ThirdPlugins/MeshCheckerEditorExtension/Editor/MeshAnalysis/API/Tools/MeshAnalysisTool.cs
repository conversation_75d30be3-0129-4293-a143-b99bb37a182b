using System;
using HightlanderSolutions.Utilities;
using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API
{
    /// <summary>
    ///     Basic MeshAnalysisTool class.
    ///     <para>
    ///         Create derived class and mark it with the attribute <see cref="MeshAnalysisToolAttribute"/> to 
    ///         add a new tool to the tools menu of a <see cref="MeshAnalysisWindow"/>.
    ///     </para>
    /// </summary>
    [PublicAPI]
    public abstract class MeshAnalysisTool : IHaveAlias
    {
        #region Internal fields from MeshAnalysisToolAttribute

        /// <summary>
        ///     Inner value of the <see cref="Name"/> property.
        /// </summary>
        private string InnerName;

        /// <summary>
        ///     Gets the name of a tool.
        /// </summary>
        internal string Name
        {
            get
            {
                if (InnerName == null)
                {
                    MeshAnalysisToolAttribute Attribute = ReflectionUtilities.GetAttribute<MeshAnalysisToolAttribute>(GetType());
                    if (Attribute != null)
                    {
                        InnerName = Attribute.Name;
                    }
                }

                return InnerName;
            }

            private set
            {
                InnerName = value;
            }
        }

        /// <summary>
        ///     Inner value of the <see cref="Priority"/> property.
        /// </summary>
        private int? InnerPriority;

        /// <summary>
        ///     Gets the priority of a tool in a tools list.
        /// </summary>
        /// <remarks>
        ///     <para>
        ///         Tools group by their priority. Every 100 will merge into one group.
        ///     </para>
        ///     <para>
        ///         <para>_____________</para>
        ///         <para>Tool -   0</para>
        ///         <para>Tool -   1</para>
        ///         <para>Tool -   2</para>
        ///         <para>_____________</para>
        ///         <para>Tool - 100</para>
        ///         <para>Tool - 120</para>
        ///         <para>Tool - 121</para>
        ///         <para>Tool - 199</para>
        ///         <para>_____________</para>
        ///         <para>Tool - 460</para>
        ///         <para>Tool - 461</para>
        ///         <para>_____________</para>
        ///     </para>
        /// </remarks>
        internal int Priority
        {
            get
            {
                if (InnerPriority == null)
                {
                    MeshAnalysisToolAttribute Attribute = ReflectionUtilities.GetAttribute<MeshAnalysisToolAttribute>(GetType());
                    if (Attribute != null)
                    {
                        InnerPriority = Attribute.Priority;
                    }
                }

                if (InnerPriority != null)
                {
                    return (int)InnerPriority;
                }
                else
                {
                    return int.MaxValue;
                }
            }

            set
            {
                InnerPriority = value;
            }
        }

        /// <summary>
        ///     Inner value of the <see cref="Keybinding"/> property.
        /// </summary>
        private KeyCode? InnerKeybinding;

        /// <summary>
        ///     Gets the key binding of a tool.
        /// </summary>
        internal KeyCode Keybinding
        {
            get
            {
                if (InnerKeybinding == null)
                {
                    MeshAnalysisToolAttribute Attribute = ReflectionUtilities.GetAttribute<MeshAnalysisToolAttribute>(GetType());
                    if (Attribute != null)
                    {
                        InnerKeybinding = Attribute.Keybinding;
                    }
                }

                if (InnerKeybinding != null)
                {
                    return (KeyCode)InnerKeybinding;
                }
                else
                {
                    return KeyCode.None;
                }
            }
        }

        #endregion

        /// <summary>
        ///     Instance of the <see cref="PreviewRenderUtility"/> which used to render meshes.
        /// </summary>
        [CanBeNull]
        private PreviewRenderUtility PreviewRenderUtilityInstance;

        /// <summary>
        ///     Initializes the tool for the specified <see cref="Mesh"/>.
        /// </summary>
        /// <param name="mesh">Specifies a mesh used in the tool.</param>
        [PublicAPI]
        public abstract void Initialize([NotNull]Mesh mesh);

        /// <summary>
        ///     Deinitializes the tool.
        /// </summary>
        [PublicAPI]
        public abstract void Deinitialize();

        /// <summary>
        ///     Draws the toolbar GUI.
        ///     <para>
        ///         The method invoked in a <see cref="GUILayout.BeginHorizontal(UnityEngine.GUILayoutOption[])"/> scope.
        ///     </para>
        /// </summary>
        [PublicAPI]
        public virtual void DrawToolbar()
        {
        }

        /// <summary>
        ///     Draws a new GUI layer over the main layer in the mesh analysis window.
        /// </summary>
        /// <param name="position">Specifies where an overlay will be drawn.</param>
        [PublicAPI]
        public virtual void DrawOverlay(Rect position)
        {
        }

        /// <summary>
        ///     Draws a last GUI layer over the main and the first layer in the mesh analysis window.
        /// </summary>
        /// <param name="position">Specifies where an overlay will be drawn.</param>
        [PublicAPI]
        public virtual void DrawOverlayGUI(Rect position)
        {
        }

        /// <summary>
        ///     Draws meshes using a specified <see cref="PreviewRenderUtility"/>.
        /// </summary>
        /// <param name="previewRenderUtility">Specifies a utility used to draw meshes.</param>
        internal void DrawMeshesInternal(PreviewRenderUtility previewRenderUtility)
        {
            PreviewRenderUtilityInstance = previewRenderUtility;

            DrawMeshes();

            PreviewRenderUtilityInstance = null;
        }

        /// <summary>
        ///     Draws additional meshes in a mesh analysis window.
        /// </summary>
        /// <remarks>
        ///     In this method you can use <see cref="DrawMesh(Mesh,Vector3,Quaternion,Material,int)"/> to draw meshes in window.
        /// </remarks>
        [PublicAPI]
        protected virtual void DrawMeshes()
        {
        }

        /// <summary>
        ///     Changes a name of the tool.
        /// </summary>
        /// <param name="name">The new name.</param>
        [PublicAPI]
        protected void SetToolName(string name)
        {
            Name = name;
        }

        /// <summary>
        ///     Draws the submesh in mesh analysis window.
        /// </summary>
        /// <param name="mesh">Specifies a mesh which submesh will be drawn.</param>
        /// <param name="position">Specifies a position where submesh will be drawn.</param>
        /// <param name="rotation">Specifies a rotation of a mesh.</param>
        /// <param name="material">Specifies a material used to draw submesh.</param>
        /// <param name="submesh">Specifies an index of the submesh.</param>
        /// <exception cref="InvalidOperationException">
        ///     A render utility could not be found.
        ///     <para>
        ///         The method invoked not in a DrawMeshes scope or the mesh analysis window is not initialized.
        ///     </para>
        /// </exception>
        [PublicAPI]
        protected void DrawMesh(Mesh mesh, Vector3 position, Quaternion rotation, Material material, int submesh)
        {
            if (PreviewRenderUtilityInstance == null)
            {
                throw new InvalidOperationException("[MeshChecker editor extension] Failed to draw mesh.\n"
                    + "Method DrawMesh(...) called not in DrawMeshes scope or MeshAnalysisWindow not initialized.");
            }

            PreviewRenderUtilityInstance.DrawMesh(mesh, position, rotation, material, submesh);
        }

        /// <summary>
        ///     Draws the submesh in mesh analysis window.
        /// </summary>
        /// <param name="mesh">Specifies a mesh which submesh will be drawn.</param>
        /// <param name="position">Specifies a position where submesh will be drawn.</param>
        /// <param name="rotation">Specifies a rotation of a mesh.</param>
        /// <param name="scale">Specifies a scale of a mesh.</param>
        /// <param name="material">Specifies a material used to draw submesh.</param>
        /// <param name="submesh">Specifies an index of the submesh.</param>
        /// <exception cref="InvalidOperationException">
        ///     A render utility could not be found.
        ///     <para>
        ///         The method invoked not in a DrawMeshes scope or the mesh analysis window is not initialized.
        ///     </para>
        /// </exception>
        [PublicAPI]
        protected void DrawMesh(Mesh mesh, Vector3 position, Quaternion rotation, Vector3 scale, Material material, int submesh)
        {
            if (PreviewRenderUtilityInstance == null)
            {
                throw new InvalidOperationException("[MeshChecker editor extension] Failed to draw mesh.\n"
                                                    + "Method DrawMesh(...) called not in DrawMeshes scope or MeshAnalysisWindow not initialized.");
            }

            PreviewRenderUtilityInstance.DrawMesh(mesh, Matrix4x4.TRS(position, rotation, scale), material, submesh);
        }
    }
}
