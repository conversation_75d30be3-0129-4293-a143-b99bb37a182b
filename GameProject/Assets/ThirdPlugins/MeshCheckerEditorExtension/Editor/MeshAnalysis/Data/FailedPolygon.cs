using System.Collections.Generic;
using JetBrains.Annotations;

namespace HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework
{
    /// <summary>
    ///     Represents a data about the failed triangle.
    /// </summary>
    [PublicAPI]
    public class FailedPolygon
    {
        /// <summary>
        ///     Indices of the triangle.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly int[] VertexIndices = new int[3];

        /// <summary>
        ///     Array of values indicating whether the vertex is invalid.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly bool[] FailedVertex = new bool[3];

        /// <summary>
        ///     Gets the index of the failed polygon.
        /// </summary>
        [PublicAPI]
        public int Index { get; private set; }

        /// <summary>
        ///     Initializes a new instance of the <see cref="FailedPolygon"/> class.
        /// </summary>
        /// <param name="polygonIndex">The polygon index.</param>
        /// <param name="index0">The index of the first vertex.</param>
        /// <param name="index1">The index of the second vertex.</param>
        /// <param name="index2">The index of the third vertex.</param>
        public FailedPolygon(int polygonIndex, int index0, int index1, int index2)
        {
            Index = polygonIndex;
            VertexIndices[0] = index0;
            VertexIndices[1] = index1;
            VertexIndices[2] = index2;
        }

        /// <summary>
        ///     Initializes a new instance of the <see cref="FailedPolygon"/> class.
        /// </summary>
        /// <param name="polygonIndex">The polygon index.</param>
        /// <param name="meshTriangles">The mesh triangles. (<see cref="UnityEngine.Mesh.triangles"/>)</param>
        /// <exception cref="System.ArgumentOutOfRangeException">
        ///     <paramref name="meshTriangles"/> doesn't contain a triangle that starts from <paramref name="polygonIndex"/>.
        /// </exception>
        /// <exception cref="System.NotSupportedException">
        ///     The property is set and the <see cref="T:System.Collections.Generic.IList`1" /> is read-only.
        /// </exception>
        public FailedPolygon(int polygonIndex, [NotNull]IList<int> meshTriangles)
            : this(polygonIndex, meshTriangles[polygonIndex], meshTriangles[polygonIndex + 1], meshTriangles[polygonIndex + 2])
        {
        }
    }
}