using System;
using System.Collections.Generic;
using System.Linq;

using JetBrains.Annotations;

using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Checker
{
    /// <summary>
    ///     Represents a manager of the checker tool.
    ///     <para>
    ///         The manager controls renderers that a checker material applied to.
    ///     </para>
    /// </summary>
    internal sealed class CheckerManager
    {
        /// <summary>
        ///     The manager Id which used to control a checker material usage.
        /// </summary>
        private readonly Guid Id = Guid.NewGuid();

        /// <summary>
        ///     Collection of renderers initial materials.
        /// </summary>
        [NotNull]
        private readonly List<InitialMaterialsStorageItem> InitialMaterialsStorage = new List<InitialMaterialsStorageItem>();

        /// <summary>
        ///     The instance of a checker material which requested from <see cref="CheckerMaterial"/>.
        /// </summary>
        private Material CheckerMaterialInstance;

        /// <summary>
        ///     Gets a value indicating whether the <see cref="CheckerManager"/> sets a checker texture to someone.
        /// </summary>
        public bool CheckerTextureApplied
        {
            get
            {
                return InitialMaterialsStorage.Count > 0;
            }
        }

        /// <summary>
        ///     Releases resources.
        /// </summary>
        public void Cleanup()
        {
            if (CheckerMaterialInstance != null)
            {
                CheckerMaterial.Release(Id);
                CheckerMaterialInstance = null;
            }
        }

        #region Apply materials to renderer

        /// <summary>
        /// Set checker material to all sharedMaterials slots in all renderers
        /// </summary>
        /// <param name="renderers">List of renderer components</param>
        /// <exception cref="ArgumentNullException"><paramref name="renderers"/> is <see langword="null"/></exception>
        /// <exception cref="ArgumentException"><paramref name="renderers"/> is empty.</exception>
        public void SetCheckerMaterial([NotNull]IEnumerable<Renderer> renderers)
        {
            if (renderers == null)
            {
                throw new ArgumentNullException("renderers", "[MeshChecker editor extension] Try to set the checker material to a null renders array. Please, send info about this <NAME_EMAIL>");
            }

            IEnumerable<Renderer> Renderers = renderers as IList<Renderer> ?? renderers.ToList();
            if (!Renderers.Any())
            {
                throw new ArgumentException("[MeshChecker editor extension] Try to set the checker material to an empty renders array. Please, send info about this <NAME_EMAIL>", "renderers");
            }

            // Check for checker material in renderer
            if (Renderers.Any(element => element.sharedMaterials.Contains(CheckerMaterialInstance) && InitialMaterialsStorage.All(init => init.Renderer != element)))
            {
                Debug.LogWarning("[MeshChecker editor extension] The selected object already has checker material on it. Please, send info about this <NAME_EMAIL>");
            }

            // Perform Undo record
            Undo.RecordObjects(Renderers.ToArray(), "Setup checker material");

            // Save the initial materials snapshot
            SaveInitialMaterialsSnapshot(Renderers);

            // Get the checker material instance if needed.
            if (CheckerMaterialInstance == null)
            {
                CheckerMaterialInstance = CheckerMaterial.Get(Id);
            }

            // Set checker material to all sharedMaterials of all renderers
            foreach (Renderer Renderer in Renderers)
            {
                Material[] NewMaterials = new Material[Renderer.sharedMaterials.Length];

                for (int Index = 0; Index < NewMaterials.Length; Index++)
                {
                    NewMaterials[Index] = CheckerMaterialInstance;
                }

                Renderer.sharedMaterials = NewMaterials;

                if (!MCPreferences.DrawWireframe)
                {
                    // Hide wireframe of selected renderers

#if UNITY_5_6_OR_NEWER
                    EditorUtility.SetSelectedRenderState(Renderer, EditorSelectedRenderState.Hidden);
#else
                    EditorUtility.SetSelectedWireframeHidden(Renderer, true);
#endif
                }
            }
        }

        /// <summary>
        ///     Reverts materials in all Renderers to the initial state.
        /// </summary>
        /// <param name="renderers">Target renderers.</param>
        /// <exception cref="ArgumentNullException"><paramref name="renderers"/> is <see langword="null"/></exception>
        public void RevertMaterialsFromSnapshot([NotNull]IEnumerable<Renderer> renderers)
        {
            if (renderers == null)
            {
                throw new ArgumentNullException("renderers");
            }

            // If storage empty then nothing to revert
            if (InitialMaterialsStorage.Count == 0)
            {
                return;
            }

            foreach (Renderer Renderer in renderers)
            {
                if (Renderer == null)
                {
                    continue;
                }

                RevertMaterialsFromSnapshot(Renderer);
            }
        }

        /// <summary>
        ///     Saves the initial snapshot of materials on renderers
        /// </summary>
        /// <param name="renderers">Target renderers.</param>
        /// <exception cref="ArgumentNullException"><paramref name="renderers"/> is <see langword="null"/></exception>
        private void SaveInitialMaterialsSnapshot([NotNull]IEnumerable<Renderer> renderers)
        {
            if (renderers == null)
            {
                throw new ArgumentNullException("renderers");
            }

            foreach (Renderer Renderer in renderers)
            {
                InitialMaterialsStorage.Add(new InitialMaterialsStorageItem(Renderer, Renderer.sharedMaterials));
            }
        }

        /// <summary>
        ///     Reverts materials in the Renderer to initial state.
        /// </summary>
        /// <param name="renderer">Target renderer.</param>
        /// <exception cref="ArgumentNullException"><paramref name="renderer"/> is <see langword="null"/></exception>
        private void RevertMaterialsFromSnapshot([NotNull]Renderer renderer)
        {
            if (renderer == null)
            {
                throw new ArgumentNullException("renderer");
            }

            // Find item in storage with initial materials info
            InitialMaterialsStorageItem FindedItem = InitialMaterialsStorage.Find(item => item.Renderer == renderer);

            if (FindedItem != null)
            {
                // revert to initial materials
                renderer.sharedMaterials = FindedItem.Materials;

                // Turn back the wireframe
                // TODO: If wireframe doesn't turned off - then no need to turn it on here.
#if UNITY_5_6_OR_NEWER
                EditorUtility.SetSelectedRenderState(renderer, EditorSelectedRenderState.Highlight);
#else
                EditorUtility.SetSelectedWireframeHidden(renderer, false);
#endif

                // Remove references from the storage
                InitialMaterialsStorage.RemoveAll(item => item.Renderer == renderer);
            }
        }

        #endregion
    }
}