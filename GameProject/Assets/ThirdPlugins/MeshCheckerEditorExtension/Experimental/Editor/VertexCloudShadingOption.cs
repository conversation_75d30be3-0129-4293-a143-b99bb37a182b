using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;

using UnityEditor;

using UnityEngine;

namespace MeshCheckerEditorExtension.Editor.Plugins.ShadingOptions
{
    [Alias("ShadingOptions.VertexCloud")]
    [MeshAnalysisShading]
    public sealed class VertexCloudShadingOption : BasicShaderShadingOption
    {
        private float PointSize = 0.1f;

        #region Overrides of MeshAnalysisShadingOption

        /// <inheritdoc />
        public override string Title
        {
            get
            {
                return "Vertex Cloud";
            }
        }

        /// <inheritdoc />
        public override int Priority
        {
            get
            {
                return 1008;
            }
        }

        /// <inheritdoc />
        public override bool GetHaveSettings()
        {
            return true;
        }

        /// <inheritdoc />
        public override void DrawSettings(Rect position)
        {
            EditorGUI.BeginChangeCheck();
            PointSize = EditorGUI.Slider(new Rect(position.position.x, position.position.y, position.width, 20f), PointSize, 0, 1);
            if (EditorGUI.EndChangeCheck())
            {
                ShadingMaterial.SetFloat("_PointRadius", MCHandles.GetHandleSize(Vector3.zero, MeshAnalysisWindow.CurrentCamera) * PointSize);
                ShadingMaterial.SetColor("_Color", OverlayUtilities.GreenHandleColor.Dark);
                ShadingMaterial.SetFloat("_CheckDepth", 0f);
                ShadingMaterial.SetInt("_ZTest", (int)UnityEngine.Rendering.CompareFunction.Less);
            }
        }

        #endregion

        #region Overrides of BasicShaderShadingOption

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
            base.Initialize(mesh);

            MeshAnalysisWindow.RepaintEvent += RepaintEventHandler;
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
            base.Deinitialize();

            MeshAnalysisWindow.RepaintEvent -= RepaintEventHandler;
        }

        /// <inheritdoc />
        protected override string ShaderName
        {
            get
            {
                return "Hidden/MeshChecker/PointsVisualization";
            }
        }

        /// <inheritdoc />
        protected override void ConfigureMaterial(Material material)
        {
            material.SetFloat("_PointRadius", MCHandles.GetHandleSize(Vector3.zero, MeshAnalysisWindow.CurrentCamera));
            material.SetColor("_Color", OverlayUtilities.GreenHandleColor.Dark);
            material.SetFloat("_CheckDepth", 0f);
            material.SetInt("_ZTest", (int)UnityEngine.Rendering.CompareFunction.Less);
        }

        #endregion

        /// <summary>
        ///     The handler of the <see cref="MeshAnalysisWindow.RepaintEvent"/>.
        /// </summary>
        private void RepaintEventHandler()
        {
            ShadingMaterial.SetFloat("_PointRadius", MCHandles.GetHandleSize(MeshAnalysisWindow.GetObjectCenter(), MeshAnalysisWindow.CurrentCamera));
        }
    }
}