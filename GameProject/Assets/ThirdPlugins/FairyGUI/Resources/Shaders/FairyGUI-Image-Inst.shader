// Upgrade NOTE: replaced '_Object2World' with 'unity_ObjectToWorld'
// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "FairyGUI/ImageInst"
{
    Properties
    {
        _MainTex ("Base (RGB), Alpha (A)", 2D) = "black" {}

        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255

        _ColorMask ("Color Mask", Float) = 15

        [HideInInspector]_ClipBox ("Clip Box", Vector) = (-2, -2, 0, 0)

        _BlendSrcFactor ("Blend SrcFactor", Float) = 5
        _BlendDstFactor ("Blend DstFactor", Float) = 10
    }

    SubShader
    {
        LOD 100

        Tags
        {
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
            "RenderPipeline"="UniversalPipeline"
            "LightMode" = "UniversalForward"
        }

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        ZTest Always  // 关闭深度测试
        Fog
        {
            Mode Off
        }
        Blend [_BlendSrcFactor] [_BlendDstFactor], One One
        ColorMask [_ColorMask]

        Pass
        {
            HLSLPROGRAM
            //移除etc支持减少变体
            // #pragma multi_compile NOT_COMBINED COMBINED
            #pragma multi_compile NOT_GRAYED GRAYED COLOR_FILTER
            #pragma multi_compile NOT_CLIPPED CLIPPED SOFT_CLIPPED ALPHA_MASK
            #pragma multi_compile_local _ ENABLE_WORLD_HUD_SCALE
            #pragma vertex vert
            #pragma fragment frag

            #pragma multi_compile_instancing
            #pragma instancing_options assumeuniformscaling
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Input.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/UnityInstancing.hlsl"

            struct appdata_t
            {
                float4 vertex : POSITION;
                half4 color : COLOR;
                float4 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                half4 color : COLOR;
                float4 texcoord : TEXCOORD0;

                #ifdef CLIPPED
                    float2 clipPos : TEXCOORD1;
                #endif

                #ifdef SOFT_CLIPPED
                    float2 clipPos : TEXCOORD1;
                #endif
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);

            #ifdef COMBINED
                sampler2D _AlphaTex;
            #endif

            int LOWQuality_LinearMode;

            UNITY_INSTANCING_BUFFER_START(UnityPerMaterial)
                // half _BaseColor;
                // half4 _ClipBox;
                UNITY_DEFINE_INSTANCED_PROP(float4, _VertexOffset)
                UNITY_DEFINE_INSTANCED_PROP(float4, _VertexUV)
            UNITY_INSTANCING_BUFFER_END(UnityPerMaterial)
                half4 _ClipSoftness;

            #ifdef COLOR_FILTER
                float4x4 _ColorMatrix;
                float4 _ColorOffset;
                float _ColorOption = 0;
            #endif

            //#if !defined(UNITY_COLORSPACE_GAMMA) && (UNITY_VERSION >= 550)
            //#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
            //#endif

            v2f vert(appdata_t v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v)
                UNITY_TRANSFER_INSTANCE_ID(v,o);
                float4 VertexOffset = UNITY_ACCESS_INSTANCED_PROP(UnityPerMaterial, _VertexOffset);
                // float3 posOS = v.vertex.xyz;
                // posOS.xy = posOS.xy * VertexOffset.zw + VertexOffset.xy;
                #if ENABLE_WORLD_HUD_SCALE
                    // half distance = GetCameraPositionWS().y;
                    // half min = 211;
                    // half max = 658;
                    // half n = 2;
                    // half scaleFactor = saturate((distance - min) / (max - min)) * n + 1;
                    // v.vertex.xy = v.vertex.xy * VertexOffset.zw * scaleFactor + VertexOffset.xy;
                    v.vertex.xy = v.vertex.xy * VertexOffset.zw + VertexOffset.xy;
                #else
                    v.vertex.xy = v.vertex.xy * VertexOffset.zw + VertexOffset.xy;
                #endif
                VertexPositionInputs vertexInput = GetVertexPositionInputs(v.vertex.xyz);
                // o.vertex = TransformObjectToHClip(v.vertex.xyz);
                o.vertex = vertexInput.positionCS;
                float4 Vertex_UV = UNITY_ACCESS_INSTANCED_PROP(UnityPerMaterial, _VertexUV);
                o.texcoord.xy = Vertex_UV.xy + v.texcoord.xy * Vertex_UV.zw;
                o.texcoord.w = 1;
                // o.texcoord.xy = Vertex_UV.xy + v.texcoord.xy * Vertex_UV.zw;
                //#if !defined(UNITY_COLORSPACE_GAMMA) && (UNITY_VERSION >= 550)
                //o.color.rgb = Gamma22ToLinear(v.color.rgb);
                //o.color.a = v.color.a;
                //#else
                o.color = v.color;
                //#endif

                #ifdef CLIPPED
                    // o.clipPos = mul(unity_ObjectToWorld, v.vertex).xy * _ClipBox.zw + _ClipBox.xy;
                    o.clipPos = vertexInput.positionWS.xy * _ClipBox.zw + _ClipBox.xy;
                #endif

                #ifdef SOFT_CLIPPED
                    // o.clipPos = mul(unity_ObjectToWorld, v.vertex).xy * _ClipBox.zw + _ClipBox.xy;
                    o.clipPos = vertexInput.positionWS.xy * _ClipBox.zw + _ClipBox.xy;
                #endif

                return o;
            }

            half4 frag(v2f i) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(i);
                half4 col = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.texcoord.xy / i.texcoord.w);
                // half4 col = tex2D(_MainTex, i.texcoord.xy / i.texcoord.w) * i.color;
                // col.xyz += _BaseColor;
                // col *=i.color;

                #ifdef COMBINED
                    col.a *= tex2D(_AlphaTex, i.texcoord.xy / i.texcoord.w).g;
                #endif

                #ifdef GRAYED
                    half grey = dot(col.rgb, half3(0.299, 0.587, 0.114) * 0.8);
                    col.rgb = half3(grey, grey, grey);
                #endif

                #ifdef SOFT_CLIPPED
                    float2 factor = float2(0,0);
                    if(i.clipPos.x<0)
                        factor.x = (1.0-abs(i.clipPos.x)) * _ClipSoftness.x;
                    else
                        factor.x = (1.0-i.clipPos.x) * _ClipSoftness.z;
                    if(i.clipPos.y<0)
                        factor.y = (1.0-abs(i.clipPos.y)) * _ClipSoftness.w;
                    else
                        factor.y = (1.0-i.clipPos.y) * _ClipSoftness.y;
                    col.a *= clamp(min(factor.x, factor.y), 0.0, 1.0);
                #endif

                #ifdef CLIPPED
                    float2 factor = abs(i.clipPos);
                    col.a *= step(max(factor.x, factor.y), 1);
                #endif

                #ifdef COLOR_FILTER
                    if (_ColorOption == 0)
                    {
                        half4 col2 = col;
                        col2.r = dot(col, _ColorMatrix[0]) + _ColorOffset.x;
                        col2.g = dot(col, _ColorMatrix[1]) + _ColorOffset.y;
                        col2.b = dot(col, _ColorMatrix[2]) + _ColorOffset.z;
                        col2.a = dot(col, _ColorMatrix[3]) + _ColorOffset.w;
                        col = col2;
                    }
                    else //premultiply alpha
                        col.rgb *= col.a;
                #endif

                #ifdef ALPHA_MASK
                    clip(col.a - 0.001);
                #endif

                //save shader variant
    	        col.rgb = lerp(col.rgb, col.rgb * col.rgb, LOWQuality_LinearMode);

                return col;
            }
            ENDHLSL
        }
    }
}