Shader "CocosShader/worldBorder"
{
    Properties
    {
        [NoScaleOffset]_MainTex ("Base (RGB), Alpha (A)", 2D) = "black" {}
        _BorderColor ("BorderColor", Vector) = (1, 1, 1, 1)
        _BorderHeight ("BorderHeight", Float) = 1.0
        _BorderRatio ("BorderRatio", Float) = 1.0

        [Enum(Off,0,On,1)]_ZWrite ("ZWrite", Float) = 0.0
        [Enum(UnityEngine.Rendering.CompareFunction)] _ZTest("ZTest", Float) = 0.0 //"Disable"
        [Enum(UnityEngine.Rendering.CullMode)] _Culling ("Culling", Float) = 0.0

        _SrcBlend ("Blend SrcFactor", Float) = 5
        _DstBlend ("Blend DstFactor", Float) = 10
    }

    SubShader
    {
        LOD 100

        Tags
        {
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
            "RenderPipeline"="UniversalPipeline"
            "LightMode" = "UniversalForward"
        }

        ZWrite [_ZWrite]
        ZTest [_ZTest]
        Cull [_Culling]
        Blend [_SrcBlend] [_DstBlend], One One

        Pass
        {

            HLSLPROGRAM
                #pragma vertex vert
                #pragma fragment frag
                #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

                struct appdata_t
                {
                    float4 vertex : POSITION;
                    float3 normal : NORMAL;
                    float2 uv : TEXCOORD0;
                };

                struct v2f
                {
                    float4 vertex : SV_POSITION;
                    float2 uv : TEXCOORD0;
                };

                sampler2D _MainTex;
                float4x4 _AddMatrix;

                CBUFFER_START(UnityPerMaterial)
                    float4 _BorderColor = float4(1, 1, 1, 1);
                    float _BorderHeight = 1.0;
                    float _BorderRatio = 1.0;
                CBUFFER_END


                v2f vert (appdata_t v)
                {
                    v2f o;
                    float3 tempPos = v.vertex.xyz;

                    tempPos.x = tempPos.x + v.normal.x * _BorderHeight;		
                    tempPos.y = tempPos.y + v.normal.y * _BorderHeight;

                    float3 pos = mul(_AddMatrix,float4(tempPos,1)).xyz;
                    o.vertex = TransformObjectToHClip(pos);

                    o.uv = float2(v.uv.x * _BorderRatio,v.uv.y);

                    return o;
                }

                half4 frag (v2f i) : SV_Target
                {
                    half4 col = tex2D(_MainTex, i.uv) * _BorderColor;
                    return col;
                }
            ENDHLSL
        }
    }
}
