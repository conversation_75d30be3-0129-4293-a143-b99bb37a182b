Shader "CocosShader/allianceTerritoryLineShader" {
    Properties
    {
        [NoScaleOffset]_MainTex ("MainTex", 2D) = "black" {}
        [NoScaleOffset]_Texture ("Texture", 2D) = "black" {}
        _TexturePos ("Texture_Pos", Vector) = (1, 1, 0, 0)
        _Resolution ("Resolution", Vector) = (1024, 1024, 0, 0)
        _TileInfo ("TileInfo", Vector) = (1201, 1201, 0, 0)
        _LodLevel ("LodLevel", Float) = 0
    }
    
    SubShader
    {
        LOD 100

        Tags
        {
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
            "RenderPipeline"="UniversalPipeline"
            "LightMode" = "UniversalForward"
        }
        
        Blend SrcAlpha OneMinusSrcAlpha, One One
        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Common.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Input.hlsl"

            struct appdata_t {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
            };

            sampler2D _MainTex;
            sampler2D _Texture;
            CBUFFER_START(UnityPerMaterial)
                float4 _TexturePos;
                float4 _Resolution;
                float4 _TileInfo;
                float _LodLevel;
            CBUFFER_END
            
            float newCorlorArray[45];

            v2f vert (appdata_t v)
            {
                v2f o;
                o.vertex = TransformObjectToHClip(v.vertex.xyz);
                o.uv = v.uv;
                return o;
            }
            
            #define divTileWidth  0.00390625f // 1/256
            #define divTileHeight  0.0078125f // 1/128
            #define step_tile  0.000832639467111f // 1/1201

            float2 worldPosToUV(float2 pos)
            {
                return frac(pos.xy/_Resolution.xy);
            }

            float2 getSampleUVForWorldPos(float2 texCoord)
            {
                float2 tmp = texCoord.xy;
                float2 uv = _TexturePos.zw + (tmp.xy + _Resolution.zw) * _Resolution.xy * _TexturePos.xy;
                return uv;
            }

            float2 getTilePos(float2 wp)
            {
                float halfMapSize = _TileInfo.x * 0.5;
                float2 divPos = float2(wp.x*divTileWidth, wp.y*divTileHeight);
                float invY = _TileInfo.y - divPos.y;
                return float2(invY + divPos.x - halfMapSize, invY - divPos.x + halfMapSize);
            }

            float2 getTileUV(float2 wp)
            {
                return frac((floor(wp) + 0.5) * step_tile);
            }

            float3 getTileSpalt(float2 tp)
            {
                tp = getTileUV(tp);

                float4 sp = tex2D(_Texture, tp);

                return sp.rgb;
            }
            
            float getShowColor(float a)
            {
                float w = 0.9 - _LodLevel*0.1;
                float lineW = 0.01 * _LodLevel;
                return max(0.0, a - w) * (1.0/(1.0 - w))*step(a, 1.0-lineW);
            }

            float getShowColor2(float a, float isDottedLine)
            {
                return lerp(getShowColor(a), step(0.9, a), isDottedLine);
            }

            float getDottedLineColor(float a)
            {
                return step(fmod(floor(a*4.0+0.5), 2.0)+0.5, 1.0);
            }

            #define div_t 0.0078125f // 1/128 #t(-1,0)
            #define div_b 0.015625f // 1/64 #b(1,0)
            #define div_l 0.03125f // 1/32 #l(0,1)
            #define div_r 0.0625f // 1/16 #r(0,-1)

            #define div_lt 0.0078125f // 1/128 #lt(-1,1)
            #define div_rt 0.015625f // 1/64 #rt(-1,-1)
            #define div_lb 0.03125f // 1/32 #lb(1,1)
            #define div_rb 0.0625f // 1/16 #rb(1,-1)

            float countAllAlpha(float2 tp, float3 sp, float state)
            {
                tp = frac(tp);

                float v1 = .0;
                float a = .0;
                float isDottedLine = step(_LodLevel, 2.0) * state;

                float r = sp.g * 256.;

                v1 = floor(r * div_t);
                a = max(a, getShowColor2(1.0-tp.x, isDottedLine) * v1 );
                a = lerp(a, min(a, getDottedLineColor(tp.y)), isDottedLine);
                r -= v1 * 128.;
                v1 = floor(r * div_b);
                a = max(a, getShowColor2(tp.x, isDottedLine) * v1 );
                a = lerp(a, min(a, getDottedLineColor(tp.y)), isDottedLine);
                r -= v1 * 64.;
                v1 = floor(r * div_l);
                a = max(a, getShowColor2(tp.y, isDottedLine) * v1 );
                a = lerp(a, min(a, getDottedLineColor(tp.x)), isDottedLine);
                r -= v1 * 32.;
                v1 = floor(r * div_r);
                a = max(a, getShowColor2(1.0-tp.y, isDottedLine) * v1 );
                a = lerp(a, min(a, getDottedLineColor(tp.x)), isDottedLine);

                //虚线不考虑其他四个方向
                r = sp.b * 256. * step(isDottedLine, 0.5);
                float lineW = 0.01 * _LodLevel;

                v1 = floor(r * div_lt);
                //a = max(a, getShowColor(tp.y-tp.x) * v1 );
                a = max(a, getShowColor(1.0-tp.x-lineW) * getShowColor(tp.y-lineW) * v1 );
                r -= v1 * 128.;
                v1 = floor(r * div_rt);
                //a = max(a, getShowColor(1.0-tp.x-tp.y) * v1 );
                a = max(a, getShowColor(1.0-tp.x-lineW) * getShowColor(1.0-tp.y-lineW) * v1 );
                r -= v1 * 64.;
                v1 = floor(r * div_lb);
                //a = max(a, getShowColor(tp.x+tp.y-1.0) * v1 );
                a = max(a, getShowColor(tp.x-lineW) * getShowColor(tp.y-lineW) * v1 );
                r -= v1 * 32.;
                v1 = floor(r * div_rb);
                //a = max(a, getShowColor(tp.x-tp.y) * v1 );
                a = max(a, getShowColor(tp.x-lineW) * getShowColor(1.0-tp.y-lineW) * v1 );

                return a; //
            }

            half4 frag (v2f i) : SV_Target
            {
                float2 wp = getSampleUVForWorldPos(i.uv);
                float2 tp = getTilePos(wp);
                if (tp.x < 0.0 || tp.y < 0.0 || tp.x > _TileInfo.x || tp.y > _TileInfo.y)
                {
                    discard;
                }

                float3 sp = getTileSpalt(tp);
                if (sp.r < 0.0001)
                {
                    discard;
                }

                float r = sp.r * 255.0;
                float state = step(20.0, r);
                float rIndex = r - state * 20.0;

                //float a = countAllAlpha(wp, sp, state);
                float a = min(1.0, countAllAlpha(tp, sp, state) + step(1.5, _LodLevel)*0.1);
                if (a < 0.0001)
                {
                    discard;
                }

                float3 color;

                int index = int(rIndex-0.5)*3;
                color.r = newCorlorArray[index];
                color.g = newCorlorArray[index + 1];
                color.b = newCorlorArray[index + 2];
                
                half4 col = float4(color,1.0) * a;
                return col;
            }
            ENDHLSL
        }
    }
    Fallback Off
}
