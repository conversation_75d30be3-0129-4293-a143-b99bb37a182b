// Upgrade NOTE: replaced '_Object2World' with 'unity_ObjectToWorld'
// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "CocosShader/terrain_shader_low"
{
    Properties
    {
        [NoScaleOffset]_MainTex ("MainTex", 2D) = "white" {}
        [NoScaleOffset]_Texture ("Texture", 2D) = "white" {}
        [NoScaleOffset]_TileTexture ("TileTexture", 2D) = "white" {}
        _TexturePos ("Texture_Pos", Vector) = (1, 1, 0, 0)
        _Resolution ("Resolution", Vector) = (1024, 1024, 0, 0)
        _TileInfo ("TileInfo", Vector) = (1201, 1201, 0, 0)
        _LodLevel ("LodLevel", Float) = 0
        _Fade ("Fade", Float) = 0
    }

    SubShader
    {
        LOD 100

        Tags
        {
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
            "RenderPipeline"="UniversalPipeline"
            "LightMode" = "UniversalForward"
        }

        Pass
        {

            HLSLPROGRAM
                #pragma vertex vert
                #pragma fragment frag
                #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

                struct appdata
                {
                    float4 vertex : POSITION;
                    float2 uv : TEXCOORD0;
                };

                struct v2f
                {
                    float2 uv : TEXCOORD0;
                    float4 vertex : SV_POSITION;
                };

                sampler2D _Texture;
                sampler2D _TileTexture;

                CBUFFER_START(UnityPerMaterial)
                    float4 _TexturePos;
                    float4 _Resolution;
                    float4 _TileInfo;

                    float _LodLevel;
                    float _Fade;
                CBUFFER_END
                //单个像素对应的uv是0.00048828125
                #define fixPix 0.00048828125f
                #define fixPix2 0.0009765625f
                // 1/256
                #define divTileWidth 0.00390625f
                // 1/128
                #define divTileHeight 0.0078125f

                v2f vert (appdata v)
                {
                    v2f o;
                    o.vertex = TransformObjectToHClip(v.vertex.xyz);
                    o.uv = v.uv;
                    return o;
                }

                float2 worldPosToUV( float2 pos, float tiling)
                {
                    float2 uv = frac(pos.xy/(_Resolution.xy * tiling));
                    return uv;
                }

                float2 getSampleUVForWorldPos(float2 texCoord)
                {
                    float2 tmp = texCoord.xy;
                    float2 uv = _TexturePos.zw + (tmp.xy + _Resolution.zw) * _Resolution.xy * _TexturePos.xy;
                    return uv;
                }

                float2 getTilePos(float2 wp)
                {
                    float halfMapSize = _TileInfo.x * 0.5;
                    float2 divPos = float2(wp.x * divTileWidth, wp.y * divTileHeight);
                    float invY = _TileInfo.y - divPos.y;
                    return float2(invY + divPos.x - halfMapSize, invY - divPos.x + halfMapSize);
                }

                float2 getTileUV(float2 wp)
                {
                    wp.x = frac(wp.x / _TileInfo.x );
                    wp.y = 1.0 - frac(wp.y / _TileInfo.y );
                    return wp;
                }

                //根据mipmap级别和图块索引进行采样
                float4 sampleColor(float2 wp, float mipLevel, float index)
                {
                    mipLevel = floor(clamp(mipLevel, 0., 3.));

                    //转换uv到对应的mipmap级别
                    float2 uv = worldPosToUV(wp, pow(2., mipLevel));

                    mipLevel = 0.0;
                    float size = 1.;
                    uv = uv * 0.5;

                    //防止黑线，计算挤出2个像素后的uv坐标
                    uv = uv * (1. - fixPix2 );

                    //转换uv到图集纹理块
                    float halfSize = 0.5;
                    float flIndex = floor(index * 0.5);
                    float offsetX = fmod(index,2) * 0.5;
                    float offsetY = abs(flIndex * halfSize - 0.5);

                    uv.x += offsetX;
                    uv.y += offsetY;

                    //uv调整 防止黑线
                    uv += fixPix;
                    uv.x = clamp(offsetX + fixPix, offsetX + halfSize - fixPix, uv.x);
                    uv.y = clamp(offsetY + fixPix, offsetY + halfSize - fixPix, uv.y);

                    float4 terrainColor = tex2D(_Texture, uv);
                    return terrainColor;
                }

                float getTileSpalt(float2 wp,sampler2D tileTexture)
                {
                    float2 tp = getTilePos(wp);
                    tp = getTileUV(tp);

                    float4 sp = tex2D(tileTexture, tp);

                    float index = sp.r * 4.0; // 255 / 64
                    float indexR = floor(index);

                    return indexR;
                }

                float3 blendAndFadeTwoLayer(float3 lowColor, float3 highColor, float mipLevel)
                {
                    return lerp(lowColor, highColor, frac(mipLevel));
                }

                half4 frag (v2f i) : SV_Target
                {
                    float level = _LodLevel;
                    float2 wp = getSampleUVForWorldPos(i.uv);

                    float sp = getTileSpalt(wp,_TileTexture);
                    float3 color = sampleColor(wp, level, sp).rgb;
                    // sample the texture
                    // half4 col = tex2D(_Texture, i.uv);
                    half4 col = float4(color,1.0);
                    return col;
                }
            ENDHLSL
        }
    }
}
