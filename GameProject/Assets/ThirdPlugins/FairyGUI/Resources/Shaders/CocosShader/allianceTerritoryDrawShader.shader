Shader "CocosShader/allianceTerritoryDrawShader" {
    Properties
    {
         [NoScaleOffset]_MainTex ("Base (RGB), Alpha (A)", 2D) = "black" {}
    }
    
    SubShader
    {
        LOD 100

        Tags
        {
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
            "RenderPipeline"="UniversalPipeline"
            "LightMode" = "UniversalForward"
        }
        
        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Common.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Input.hlsl"

            struct appdata_t {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
            };

            sampler2D _MainTex;
            
            float4 sampleColor(float2 uv)
            {
                float4 color1 = tex2D(_MainTex, uv);
                return color1;
            }

            #define mul_t 0.50196f // 128/255 #t(-1,0)
            #define mul_t 0.50196f // 128/255 #t(-1,0)
            #define mul_b 0.25098f // 64/255 #b(1,0)
            #define mul_l 0.12549f // 32/255 #l(0,1)
            #define mul_r 0.062745f // 16/255 #r(0,-1)

            #define mul_lt 0.50196f // 128/255 #lt(-1,1)
            #define mul_rt 0.25098f // 64/255 #rt(-1,-1)
            #define mul_lb 0.12549f // 32/255 #lb(1,1)
            #define mul_rb 0.062745f // 16/255 #rb(1,-1)

            #define step_tile 0.000832639467111f // 1/1201

            float getLeagueID(float4 color)
            {
                return color.g * 256.0 + color.b;
            }

            float getActivityState(float r)
            {
                return step(10.0, r*256.0);
            }

            float getDirColor(float2 uv,float leagueID, float2 dir, float weight)
            {
                float4 color = sampleColor(uv + dir*step_tile);
                float newLeagueID = getLeagueID(color);

                return step(0.0001, abs(newLeagueID - leagueID)) * weight;
            }
            
            v2f vert (appdata_t v)
            {
                v2f o;
                o.vertex = TransformObjectToHClip(v.vertex.xyz);
                o.uv = v.uv;
                return o;
            }

            half4 frag (v2f i) : SV_Target
            {
                float2 uv = i.uv;
                float4 color = sampleColor(uv);
                float leagueID = getLeagueID(color);
                if (leagueID < 0.0001)
                {
                    discard;
                }

                //记录一下联盟关系
                float3 ret = float3(color.r,0.,0.);

                ret.g += getDirColor(uv,leagueID, float2(-1, 0), mul_t);
                ret.g += getDirColor(uv,leagueID, float2(1, 0), mul_b);
                ret.g += getDirColor(uv,leagueID, float2(0, 1), mul_l);
                ret.g += getDirColor(uv,leagueID, float2(0, -1), mul_r);

                ret.b += getDirColor(uv,leagueID, float2(-1, 1), mul_lt);
                ret.b += getDirColor(uv,leagueID, float2(-1, -1), mul_rt);
                ret.b += getDirColor(uv,leagueID, float2(1, 1), mul_lb);
                ret.b += getDirColor(uv,leagueID, float2(1, -1), mul_rb);
                
                half4 col = float4(ret,1.0);
                return col;
            }
            ENDHLSL
        }
    }
    Fallback Off
}