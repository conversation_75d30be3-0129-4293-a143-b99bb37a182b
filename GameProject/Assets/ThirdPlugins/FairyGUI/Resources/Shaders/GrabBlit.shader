Shader "Hidden/Universal Render Pipeline/GrabBlit"
{
    
    HLSLINCLUDE
        half4 _BlurColor;
    ENDHLSL
    
    
    Properties {
	_BlurColor     ("Blur Color", Color) = (0,0,0,0)
    }
    
    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline"
        }
        LOD 100

        Pass
        {
            Name "Blit"
            ZTest Always
            ZWrite Off
            Cull Off

            HLSLPROGRAM
            #pragma vertex FullscreenVert
            #pragma fragment Fragment

            // #pragma multi_compile_fragment _ _LINEAR_TO_SRGB_CONVERSION
            #pragma multi_compile _ _USE_DRAW_PROCEDURAL

            #include "Packages/com.unity.render-pipelines.universal/Shaders/Utils/Fullscreen.hlsl"

            TEXTURE2D_X(_SourceTex);
            SAMPLER(sampler_SourceTex);

            int LOWQuality_LinearMode;

            half4 Fragment(Varyings input) : SV_Target
            {
                UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

                half4 col = SAMPLE_TEXTURE2D_X(_SourceTex, sampler_SourceTex, input.uv);

                col.rgb = (_BlurColor.rgb * _BlurColor.a) + col.rgb * (1.0-_BlurColor.a);
                
                // #ifdef _LINEAR_TO_SRGB_CONVERSION
                // col = SRGBToLinear(col);
                // #endif

                //save shader variant
                //may not need srgb2linear, beacuse is capture's content is scene not ui
    	        col.rgb = lerp(col.rgb, col.rgb * col.rgb, LOWQuality_LinearMode);

                // col.r = col.r + 1.0;
                return col;
            }
            ENDHLSL
        }
    }
}