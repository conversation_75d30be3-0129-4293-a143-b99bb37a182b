using System.Collections.Generic;
using Sultan.ECSModule;
using Unity.Entities;
using UnityEngine;

namespace XLua.Editor
{
    public static class BlockListCfg
    {
        [BlackList] 
        public static List<List<string>> BlackList = new List<List<string>>()
        {
            new List<string>(){ typeof(EntityManager).FullName, "SetName", typeof(Entity).FullName,typeof(string).FullName},
            new List<string>(){ typeof(EntityManager).FullName, "GetName", typeof(Entity).FullName},
            new List<string>(){ typeof(UnityEngine.Input).FullName, "IsJoystickPreconfigured", typeof(string).FullName},
            new List<string>(){ typeof(UnityEngineVector2Extention).FullName, "unityTypes"},
            new List<string>(){ "FairyGUI.GoWrapper.RendererInfo", "RendererInfo"},
            new List<string>(){ "Unity.Mathematics.float2"},
            new List<string>(){ "Unity.Mathematics.float3"},
            new List<string>(){ "Sultan.ECSModule.MoveLineDrawSystem"},
            new List<string>(){ "UniWebViewInterface"},
            new List<string>(){"Sultan.Manager.SceneCFGDataNavigator", "CreateInstance"}, 
            new List<string>(){"Sultan.Localization", "CreateAsset"},
            new List<string>(){"Sultan.Localization", "CreateConfigAsset"},
            new List<string>(){"Sultan.ECSModule.HUDRenderResource", "HudSpriteInfo"},
            new List<string>(){"Sultan.ECSModule.HUDRenderResource.HudSpriteInfo"},
            new List<string>(){typeof(MeshRenderer).FullName, "scaleInLightmap"},
            new List<string>(){typeof(MeshRenderer).FullName, "receiveGI"},
            new List<string>(){typeof(MeshRenderer).FullName, "stitchLightmapSeams"},
            //new List<string>(){"Sultan.Init.AndroidQulityCfg","Sultan.Init.IOSQulityCfg"},
            //new List<string>(){"Sultan.Init.DeviceQulity","SerializeQulityCfg"},

        };
    }
}