using System.IO;
using System.Text;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

public static class ComponentDataExport
{
    public static string common_path = Application.dataPath + "/Scripts/AppFacade/XLua/GenECS/";
    public static List<System.Type> m_ExportList = new List<System.Type>
    {
        // typeof(Sultan.ECSModule.Rotation),
        //typeof(Unity.Transforms.NonUniformScale),
        // typeof(Sultan.ECSModule.Translation)
    };
    
    

    [MenuItem("XLua/GenComponentData", false, 1)]
    public static void GenComponentData()
    {
        var types = XLuaConfig.GCOptimize;
        
        string filePath = common_path + "IComponentDataWrap.cs";
        if (!Directory.Exists(common_path))
            Directory.CreateDirectory(common_path);

        if (File.Exists(filePath))
        {
            File.Delete(filePath);
            AssetDatabase.DeleteAsset(filePath);
        }

        StreamWriter textWriter = new StreamWriter(filePath, false, Encoding.UTF8);

        textWriter.Write(@"
using System; 
using System.Collections;
using System.Collections.Generic;

namespace Sultan.ECSModule
{
    public static partial class ECSHelper
    {
        public static void initBind(){
            var dict = GameObjectConvert.s_ConvertDataCall;
        ");
        foreach (var type in types)
        {
            //var info = string.Format(@"
            //s_bindFuncAdd[typeof({0})] = (AddComponentDataToEntiyDelegate)(AddComponentData<{0}>);
            //s_bindFuncGet[typeof({0})] = (GetComponentDataToEntiyDelegate)(GetComponentData<{0}>);
            //s_bindFuncSet[typeof({0})] = (SetComponentDataToEntiyDelegate)(SetComponentData<{0}>);
            //", type.FullName);
            //textWriter.Write(info);
            WriteComponent(ref textWriter, type);
        }

        foreach(var type in m_ExportList)
        {
            WriteComponent(ref textWriter, type);
        }
        

        textWriter.Write(@"
        }
    }
}
        ");
        textWriter.Close();
        AssetDatabase.Refresh();
    }

    public static void  WriteComponent(ref StreamWriter textWriter, System.Type type)
    {
        var info = string.Format(@"
            s_bindFuncAdd[typeof({0})] = (AddComponentDataToEntiyDelegate)(AddComponentData<{0}>);
            s_bindFuncGet[typeof({0})] = (GetComponentDataToEntiyDelegate)(GetComponentData<{0}>);
            s_bindFuncSet[typeof({0})] = (SetComponentDataToEntiyDelegate)(SetComponentData<{0}>);
            ", type.FullName);
        textWriter.Write(info);
    }
}
