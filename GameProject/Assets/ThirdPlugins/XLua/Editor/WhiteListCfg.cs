using XLua;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Unity.Entities;
using Sultan.ECSModule;

public static class WhiteListCfg
{

    [LuaCallCSharp]
    public static List<Type> CommonList = new List<Type>()
    {
        typeof(Action<UnityEngine.GameObject>),
        typeof(Action<UnityEngine.AsyncOperation>),
        typeof(UnityEngine.AsyncOperation)
    };

    [LuaCallCSharp]
    public static List<System.Type> DotweenList = new List<System.Type>()
    {
        // typeof(DG.Tweening.DOTween),
        // typeof(DG.Tweening.DOVirtual),
        // typeof(DG.Tweening.EaseFactory),
        // typeof(DG.Tweening.Tweener),
        // typeof(DG.Tweening.Tween),
        // typeof(DG.Tweening.Sequence),
        // typeof(DG.Tweening.TweenParams),
        // typeof(DG.Tweening.Core.ABSSequentiable),
        // typeof(DG.Tweening.Core.TweenerCore<Vector3, Vector3, DG.Tweening.Plugins.Options.VectorOptions>),
        // typeof(DG.Tweening.TweenCallback),
        // typeof(DG.Tweening.TweenExtensions),
        // typeof(DG.Tweening.TweenSettingsExtensions),
        // typeof(DG.Tweening.ShortcutExtensions),
    };

    [LuaCallCSharp]
    public static List<System.Type> ECSList = new List<System.Type>()
    {
        //typeof(Unity.Entities.ConvertToEntity),
        typeof(Unity.Entities.Entity),
        typeof(Unity.Entities.EntityManager),
        typeof(Unity.Entities.World),
        typeof(Unity.Entities.ComponentType),
        typeof(Unity.Entities.EntityArchetype),
        typeof(WeakAssetReference),
        
        typeof(ArmyInfo),
        typeof(ArmyTroopInfo),

        // typeof(Sultan.ECSModule.Rotation),
        // typeof(Sultan.ECSModule.NonUniformScale),
        // typeof(Sultan.ECSModule.Translation),

        typeof(Sultan.ECSModule.GameObjectConvert),
        typeof(Sultan.ECSModule.GameWorld),
        typeof(Sultan.ECSModule.EntityFactory),
        typeof(Sultan.ECSModule.ECSHelper),
        typeof(Sultan.ECSModule.WayPoint),
        typeof(Sultan.GPUSkin.Runtime.VATAnimator),
    };
}