using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using Luban.Editor;
using Sultan.BuildTools;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using XLua;
using BindType = System.Type;
using Debug = UnityEngine.Debug;

namespace Emmy
{

    public class EmmyluaAPIMaker
    {
        [MenuItem("Tools/Gen config preview")]
        public static void GenConfigPreview()
        {
            
            var ins = LubanExportConfig.Instance;
            var path = ins.input_data_dir;
            if (!LubanTools.ConfigSvnUpdate(path))
            {
                return;
            }
            
            var cmd = RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? "dotnet.exe" : "/usr/local/share/dotnet/dotnet";
            var which_dll = LubanExportConfig.Instance.which_dll;
            var input_data_dir = LubanExportConfig.Instance.input_data_dir;
            var define_xml = LubanExportConfig.Instance.define_xml;
            var dll_dir = Path.GetDirectoryName(which_dll);
            var template = Path.Combine(dll_dir, "TempalteServer");
            var gen_types = "data_lua";
            var output_data_dir = "../../AOW_CODE/LuaScripts/emmylua_api/configs_data";
            var param = $"\"{which_dll}\" -t \"{template}\" -j cfg --  -d \"{define_xml}\" --input_data_dir \"{input_data_dir}\" --gen_types data_lua -s client --output_data_dir \"{output_data_dir}\"";
            Debug.Log($"{cmd} {param}");
            CLIUtil.Execute(cmd, param);
        }
        
        [MenuItem("Tools/Gen Proto API")]
        public static void GenProtoAPI()
        {
            
            var cmd = "lua";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                cmd = Path.GetFullPath("../CITools/Lua/lua-core/lua.exe");
            }
            var param = "z_genAPI.lua";
            // var workingDiectory = Path.Combine(Application.dataPath, "../../AOW_CODE/Tools/sprotoconvert");
            var workingDiectory = "../../AOW_CODE/Tools/sprotoconvert";

            using (Process tmpProcess = new Process())
            {
                tmpProcess.StartInfo.FileName = $"\"{cmd}\"";
                tmpProcess.StartInfo.Arguments = param;
                tmpProcess.StartInfo.WindowStyle = ProcessWindowStyle.Normal;
                tmpProcess.StartInfo.CreateNoWindow = false;
                tmpProcess.StartInfo.WorkingDirectory = workingDiectory;

                tmpProcess.StartInfo.UseShellExecute = false;
                tmpProcess.StartInfo.RedirectStandardOutput = true;
                tmpProcess.StartInfo.RedirectStandardError = true;
                // tmpProcess.StartInfo.RedirectStandarcollectorNamedError = true;
                tmpProcess.Start();

                
                // tmpProcess.WaitForExit();
                var output = tmpProcess.StandardOutput.ReadToEnd();
                var err = tmpProcess.StandardError.ReadToEnd();
                var code = tmpProcess.ExitCode;
                tmpProcess.Close();
                tmpProcess.Dispose();
                Debug.Log(output);
                if (code == 0)
                {
                    Debug.Log("导出proto成功");
                }
                else
                {
                    Debug.LogError($"CLI Execute return code:{tmpProcess.ExitCode}\n{cmd} {param}");
                    
                    Debug.LogError(err);
                    EditorUtility.DisplayDialog("导出失败", err, "确定");
                }
            }
        }
        
        private static List<string> ExportNameSpace = new List<string>()
        {
            "Sultan",
            "FairyGUI",
            "UnityEngine",
        };

        private static bool NeedExport(Type type)
        {
            if(string.IsNullOrEmpty(type.Namespace))
                return true;
            foreach (var item in ExportNameSpace) {
                if (type.Namespace.StartsWith(item)) {
                    return true;
                }
            }
            return false;
        }
        
        // private static string m_apiDir = Application.dataPath + "/../../UnityAPI";
        private const string outDir = "../../AOW_CODE/LuaScripts/emmylua_api/xlua";

        [MenuItem("Tools/Gen Emmylua C# API")]
        static void DoIt() {
            if (Directory.Exists(outDir)) {
                Directory.Delete(outDir, true);
            }
            if (!Directory.Exists(outDir))
            {
                Directory.CreateDirectory(outDir);
            }
            var customTypes = (from assembly in XLuaConfig.customAssemblys.Select(s => Assembly.Load(s))
                from type in assembly.GetExportedTypes()
                where 
                    // type.Namespace == null || !type.Namespace.StartsWith("XLua")
                    // type.Namespace != null && NeedExport(type)
                    NeedExport(type)
                    // && type.BaseType != typeof(MulticastDelegate) && !type.IsInterface && !type.IsEnum
                select type);
            var bindTypes = customTypes.Concat(XLuaConfig.unityTypes);
            foreach (var bindType in bindTypes)
            {
                var generator = new LuaAPIGenerator();
                generator.Gen(bindType, outDir);
            }

            Debug.LogFormat("API 生成完毕. {0}",  Path.GetFullPath(outDir));
        }
    }

    abstract class Filter<T>
    {
        public delegate void EachProcessor(T value);

        public abstract bool Contains(T type);

        public Filter<T> Exclude(params Filter<T>[] others)
        {
            Filter<T> v = this;
            for (var i = 0; i < others.Length; i++)
            {
                v = new ExcludeFilter<T>(v, others[i]);
            }
            return v;
        }

        public Filter<T> And(params Filter<T>[] others)
        {
            Filter<T> v = this;
            for (var i = 0; i < others.Length; i++)
            {
                v = new AndFilter<T>(v, others[i]);
            }
            return v;
        }

        public Filter<T> Or(params Filter<T>[] others)
        {
            Filter<T> v = this;
            for (var i = 0; i < others.Length; i++)
            {
                v = new OrFilter<T>(v, others[i]);
            }
            return v;
        }

        public virtual void Each(EachProcessor processor)
        {

        }
    }

    class ExcludeFilter<T> : Filter<T>
    {
        private readonly Filter<T> _baseFilter;
        private readonly Filter<T> _excludeFilter;

        public ExcludeFilter(Filter<T> baseFilter, Filter<T> excludeFilter)
        {
            _baseFilter = baseFilter;
            _excludeFilter = excludeFilter;
        }

        public override bool Contains(T type)
        {
            return _baseFilter.Contains(type) && !_excludeFilter.Contains(type);
        }

        public override void Each(EachProcessor processor)
        {
            _baseFilter.Each(v =>
            {
                if (!_excludeFilter.Contains(v))
                {
                    processor(v);
                }
            });
        }
    }

    class OrFilter<T> : Filter<T>
    {
        private readonly Filter<T> _baseFilter;
        private readonly Filter<T> _orFilter;

        public OrFilter(Filter<T> baseFilter, Filter<T> orFilter)
        {
            _baseFilter = baseFilter;
            _orFilter = orFilter;
        }

        public override bool Contains(T type)
        {
            return _baseFilter.Contains(type) || _orFilter.Contains(type);
        }

        public override void Each(EachProcessor processor)
        {
            _baseFilter.Each(processor);
            _orFilter.Each(processor);
        }
    }

    class AndFilter<T> : Filter<T>
    {
        private readonly Filter<T> _baseFilter;
        private readonly Filter<T> _andFilter;

        public AndFilter(Filter<T> baseFilter, Filter<T> andFilter)
        {
            _baseFilter = baseFilter;
            _andFilter = andFilter;
        }

        public override bool Contains(T type)
        {
            return _baseFilter.Contains(type) && _andFilter.Contains(type);
        }

        public override void Each(EachProcessor processor)
        {
            _baseFilter.Each(v =>
            {
                if (_andFilter.Contains(v))
                {
                    processor(v);
                }
            });
        }
    }

    class GeneralFilter<T> : Filter<T>
    {
        private readonly ICollection<T> _arr;

        public GeneralFilter(ICollection<T> arr)
        {
            _arr = arr;
        }

        public override bool Contains(T type) { return _arr.Contains(type); }

        public override void Each(EachProcessor processor)
        {
            foreach (T x1 in _arr)
            {
                processor(x1);
            }
        }
    }

    class BindTypeCollection : Filter<BindType>
    {
        private readonly Queue<BindType> _typeQueue;
        private List<BindType> _typeList;

        public BindTypeCollection(BindType[] typeArr)
        {
            var count = typeArr.Length;
            _typeQueue = new Queue<BindType>(count);
            foreach (var bindType in typeArr)
            {
                _typeQueue.Enqueue(bindType);
            }
        }

        public BindType[] CollectBindType(Filter<Type> baseFilter, Filter<Type> excludeFilter)
        {
            List<Type> processed = new List<Type>();
            excludeFilter = excludeFilter.Or(new GeneralFilter<Type>(processed));
            _typeList = new List<BindType>();

            baseFilter.Each(t => _typeQueue.Enqueue(t));
            while (_typeQueue.Count > 0)
            {
                var bind = _typeQueue.Dequeue();
                if (!excludeFilter.Contains(bind))
                {
                    _typeList.Add(bind);
                    processed.Add(bind);
                    CreateBaseBindType(bind.BaseType, excludeFilter);
                }
            }
            return _typeList.ToArray();
        }

        void CreateBaseBindType(Type baseType, Filter<Type> excludeFilter)
        {
            if (baseType != null && !excludeFilter.Contains(baseType))
            {
                var bind = baseType;
                _typeQueue.Enqueue(bind);
                CreateBaseBindType(bind.BaseType, excludeFilter);
            }
        }

        public override bool Contains(BindType type)
        {
            return false;
        }

        public override void Each(EachProcessor processor)
        {
            foreach (var bindType in _typeList)
            {
                processor(bindType);
            }
        }
    }

    /// <summary>
    /// 黑名单过滤
    /// </summary>
    /// <typeparam name="T"></typeparam>
    class BlackListMemberNameFilter<T> : Filter<T> where T : MemberInfo
    {
        public override bool Contains(T mi)
        {
            // if (ToLuaExport.memberFilter.Contains(mi.Name))
            //     return true;
            // var type = mi.ReflectedType;
            // if (type != null)
            //     return ToLuaExport.memberFilter.Contains(type.Name + "." + mi.Name);
            return false;
        }
    }

    class OpMethodFilter : Filter<MethodInfo>
    {
        public override bool Contains(MethodInfo mi)
        {
            return mi.Name.StartsWith("Op_") || mi.Name.StartsWith("add_") || mi.Name.StartsWith("remove_");
        }
    }

    /// <summary>
    /// Get/Set 方法过滤
    /// </summary>
    class GetSetMethodFilter : Filter<MethodInfo>
    {
        public override bool Contains(MethodInfo type)
        {
            return type.Name.StartsWith("get_") || type.Name.StartsWith("set_");
        }
    }
    
    /// <summary>
    /// 废弃过滤
    /// </summary>
    /// <typeparam name="T"></typeparam>
    class ObsoleteFilter<T> : Filter<T> where T : MemberInfo
    {
        public override bool Contains(T mb)
        {
            object[] attrs = mb.GetCustomAttributes(true);

            for (int j = 0; j < attrs.Length; j++)
            {
                Type t = attrs[j].GetType();

                if (t == typeof(ObsoleteAttribute) ||
                    t == typeof(XLua.BlackListAttribute) ||
                    t == typeof(MonoPInvokeCallbackAttribute) ||
                    t.Name == "MonoNotSupportedAttribute" ||
                    t.Name == "MonoTODOAttribute")
                {
                    return true;
                }
            }
            return false;
        }
    }

    /// <summary>
    /// 泛型方法过滤
    /// </summary>
    class GenericMethodFilter : Filter<MethodInfo>
    {
        public override bool Contains(MethodInfo mi)
        {
            return mi.IsGenericMethod;
        }
    }

    /// <summary>
    /// 扩展方法过滤
    /// </summary>
    class ExtendMethodFilter : Filter<MethodInfo>
    {
        private readonly Type _type;

        public ExtendMethodFilter(Type type)
        {
            _type = type;
        }

        public override bool Contains(MethodInfo mi)
        {
            ParameterInfo[] infos = mi.GetParameters();
            if (infos.Length == 0) return false;

            var pi = infos[0];
            return pi.ParameterType == _type;
        }
    }

    class MethodData
    {
        public bool IsExtend;
        public MethodInfo Method;
    }

    class MethodDataSet
    {
        public List<MethodData> MethodList = new List<MethodData>();

        public void Add(MethodInfo mi, bool isExtend)
        {
            MethodData md = new MethodData { IsExtend = isExtend, Method = mi };
            MethodList.Add(md);
        }
    }

    abstract class CodeGenerator
    {
        readonly Filter<MethodInfo> methodExcludeFilter = new ObsoleteFilter<MethodInfo>()
            .Or(new OpMethodFilter())
            .Or(new BlackListMemberNameFilter<MethodInfo>())
            .Or(new GenericMethodFilter())
            .Or(new GetSetMethodFilter());

        protected BindType _bindType;

        public virtual void Gen(BindType bt, string path)
        {
            _bindType = bt;

            GenMethods();
            GenProperties();
        }

        protected void GenMethods()
        {
            var flags = BindingFlags.Public | BindingFlags.Static | BindingFlags.IgnoreCase | BindingFlags.Instance |
                        BindingFlags.DeclaredOnly;
            Dictionary<string, MethodDataSet> allMethods = new Dictionary<string, MethodDataSet>();
            Action<MethodInfo, bool> methodCollector = (mi, isExtend) =>
            {
                MethodDataSet set;
                if (allMethods.TryGetValue(mi.Name, out set))
                {
                    set.Add(mi, isExtend);
                }
                else
                {
                    set = new MethodDataSet();
                    set.Add(mi, isExtend);
                    allMethods.Add(mi.Name, set);
                }
            };

            // //extend
            // if (_bindType.BaseType != null)
            // {
            //     foreach (var type in _bindType.extendList)
            //     {
            //         MethodInfo[] methodInfos = type.GetMethods(flags);
            //         var extFilter = new GeneralFilter<MethodInfo>(methodInfos)
            //             .Exclude(methodExcludeFilter)
            //             .And(new ExtendMethodFilter(_bindType));
            //         extFilter.Each(mi => { methodCollector(mi, true); });
            //     }
            // }

            //base
            var methods = _bindType.GetMethods(flags);
            var filter = new GeneralFilter<MethodInfo>(methods);
            var methodFilter = filter.Exclude(methodExcludeFilter);
            methodFilter.Each(mi => { methodCollector(mi, false); });

            foreach (var pair in allMethods)
            {
                GenMethod(pair.Key, pair.Value);
            }
        }

        protected void GenProperties()
        {
            Type type = _bindType;
            if (type.IsSubclassOf(typeof(System.Enum)))
            {
                return;
            }
            //props
            var propList = type.GetProperties(BindingFlags.GetProperty | BindingFlags.SetProperty |
                                              BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase |
                                              BindingFlags.DeclaredOnly | BindingFlags.Static);
            var propFilter = new GeneralFilter<PropertyInfo>(propList)
                .Exclude(new BlackListMemberNameFilter<PropertyInfo>())
                .Exclude(new ObsoleteFilter<PropertyInfo>());
            propFilter.Each(GenProperty);

            //fields
            var fields = type.GetFields(BindingFlags.GetField | BindingFlags.SetField | BindingFlags.Instance |
                                        BindingFlags.Public | BindingFlags.Static);
            var fieldFilter = new GeneralFilter<FieldInfo>(fields)
                .Exclude(new BlackListMemberNameFilter<FieldInfo>())
                .Exclude(new ObsoleteFilter<FieldInfo>());
            fieldFilter.Each(GenField);

            //events
            var events = type.GetEvents(BindingFlags.DeclaredOnly | BindingFlags.Instance | BindingFlags.Public |
                                        BindingFlags.Static);
            var evtFilter = new GeneralFilter<EventInfo>(events)
                .Exclude(new BlackListMemberNameFilter<EventInfo>())
                .Exclude(new ObsoleteFilter<EventInfo>());
            evtFilter.Each(GenEvent);
        }

        protected abstract void GenProperty(PropertyInfo pi);

        protected abstract void GenEvent(EventInfo ei);

        protected abstract void GenField(FieldInfo fi);

        protected abstract void GenMethod(string name, MethodDataSet methodDataSet);
    }

    static class TypeExtension
    {
        public static string GetTypeStr(this Type type)
        {
            if (typeof(ICollection).IsAssignableFrom(type))
            {
                return "table";
            }
            if (type.IsGenericType)
            {
                var typeName = type.Name;
                int pos = typeName.IndexOf("<", StringComparison.Ordinal);
                if (pos > 0)
                    return typeName.Substring(0, pos);
            }
            return type.Name;
        }
    }

    class LuaAPIGenerator : CodeGenerator
    {
        private StringBuilder _baseSB;
        private StringBuilder _propBuilder;
        private StringBuilder _methodBuilder;

        public override void Gen(System.Type bt, string path)
        {
            if (bt.BaseType == typeof(MulticastDelegate))
            {
                return;
            }
            _baseSB = new StringBuilder();
            var isSingleton = false;
            if (bt.BaseType != null)
            {
                _baseSB.AppendFormat("---@class {0} : {1}\n", fixType(bt), fixType(bt.BaseType));
                if (!string.IsNullOrEmpty(bt.BaseType.FullName) && bt.BaseType.FullName.ToLower().Contains("singleton"))
                {
                    isSingleton = true;
                }
            }
            else
                _baseSB.AppendFormat("---@class {0}\n", fixType(bt));

            if (bt.GetConstructors().Length > 0)
            {
                _baseSB.AppendFormat("---@operator call():{0}\n", fixType(bt));
            }
            _propBuilder = new StringBuilder();
            _methodBuilder = new StringBuilder();
            base.Gen(bt,null);
            genEnum(bt);

            if (isSingleton)
            {
                _baseSB.Append("---@field Instance self\n");
            }
            _baseSB.Append(_propBuilder);
            _baseSB.Append("local m = {}\n");
            _baseSB.Append(_methodBuilder);

            var finalName = fixType(bt);
            string[] ns = finalName.Split('.');

            if (!bt.Name.Contains(">"))
            {
                _baseSB.Append("CS = {}\n");
                for (int i = 0; i < ns.Length - 1; i++)
                {
                    _baseSB.AppendFormat("CS.{0} = {{}}\n", string.Join(".", ns, 0, i + 1));
                }
                _baseSB.AppendFormat("CS.{0} = m\n", finalName);
            }

            if (finalName.StartsWith("FairyGUI."))
            {
                _baseSB.Append("\n");
                _baseSB.AppendFormat("---@class fgui.{0} : {1}\n", finalName.Substring("FairyGUI.".Length), finalName);
            }
            

            string fileName = string.Format("{0}/{1}Wrap.lua", path, fixType(bt).Replace(".", "_"));
            File.WriteAllBytes(fileName, Encoding.GetEncoding("UTF-8").GetBytes(_baseSB.ToString()));
        }

        private void genEnum(Type bt)
        {
            if (bt.IsSubclassOf(typeof(System.Enum)))
            {
                try
                {

                    var names = Enum.GetNames(bt);
                    var values = Enum.GetValues(bt);
                    for (int i = 0; i < names.Length; i++)
                    {
                        _propBuilder.AppendFormat("---@field {0} number @{1}\n", names[i], (int)values.GetValue(i));
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError("gen enum error" + bt.FullName + "exception:" + e);
                }
            }
        }

        private Dictionary<string, string> typeName = new Dictionary<string, string>()
        {
            {"System.Boolean", "bool"},
            {"System.UInt32", "uint"},
            {"System.Int32", "int"},
            {"System.UInt64", "uint"},
            {"System.Int64", "int"},
            {"System.Single", "float"},
            {"System.Double", "float"},
            {"System.String", "string"},
            {"System.Void", "void"},
        };

        private string fixType(Type bt)
        {
            if (bt.BaseType == typeof(MulticastDelegate))
            {
                var delegateType = "";
                var strParam = "";
                var strReturn = "";
                var invoke = bt.GetMethod("Invoke");
                foreach (var parameter in invoke.GetParameters())
                {
                    if (strParam.Length > 0)
                    {
                        strParam += ", ";
                    }

                    strParam += $"{parameter.Name}:{fixType(parameter.ParameterType)}";
                }

                if (invoke.ReturnType != typeof(void))
                {
                    strReturn = " : " + fixType(invoke.ReturnType);
                }
                return $"fun({strParam}){strReturn}";
            }
            var name = bt.FullName;
            if (string.IsNullOrEmpty(name))
            {
                UnityEngine.Debug.LogWarning("empty name:" + bt.Assembly.FullName + " " + bt.Namespace);
                return name;
            }

            //去除泛型结构
            var m = Regex.Match(name, @"`\d+\[");
            if (m.Success) {
                name = name.Substring(0, m.Index);
            }

            name = name.Replace("+", ".").Replace("&", "");
            
            if (typeName.TryGetValue(name, out var newName))
            {
                name = newName;
            }
            return name;
        }

        protected override void GenProperty(PropertyInfo pi)
        {
            _propBuilder.AppendFormat("---@field {0} {1}\n", pi.Name, fixType(pi.PropertyType));
        }

        protected override void GenEvent(EventInfo ei)
        {
            //Debug.Log(ei);
        }

        protected override void GenField(FieldInfo fi)
        {
            _propBuilder.AppendFormat("---@field {0} {1}\n", fi.Name, fixType(fi.FieldType));
        }

        protected override void GenMethod(string name, MethodDataSet methodDataSet)
        {
            //overload
            if (methodDataSet.MethodList.Count > 1)
            {
                for (var j = 1; j < methodDataSet.MethodList.Count; j++)
                {
                    var data = methodDataSet.MethodList[j];
                    var mi = data.Method;
                    var parameters = mi.GetParameters();
                    int startIdx = data.IsExtend ? 1 : 0;
                    string[] paramNames = new string[parameters.Length - startIdx];
                    for (var i = startIdx; i < parameters.Length; i++)
                    {
                        var pi = parameters[i];
                        paramNames[i - startIdx] = string.Format("{0}:{1}", pi.Name, fixType(pi.ParameterType));
                    }

                    if (mi.IsStatic)
                    {
                        _methodBuilder.AppendFormat("---@overload fun({0}):{1}\n", string.Join(", ", paramNames), fixType(mi.ReturnType));
                    }
                    else
                    {
                        _methodBuilder.AppendFormat("---@overload fun(self:self, {0}):{1}\n", string.Join(", ", paramNames), fixType(mi.ReturnType));
                    }
                }
            }
            //main
            {
                var data = methodDataSet.MethodList[0];
                var mi = data.Method;
                var parameters = mi.GetParameters();
                int startIdx = data.IsExtend ? 1 : 0;
                string[] paramNames = new string[parameters.Length - startIdx];
                for (var i = startIdx; i < parameters.Length; i++)
                {
                    var pi = parameters[i];
                    var new_name = fix_name(pi.Name);
                    if (pi.HasDefaultValue)
                    {
                        _methodBuilder.AppendFormat("---@param {0}? {1} (default:{2})\n", new_name, fixType(pi.ParameterType), pi.DefaultValue);
                    }
                    else
                    {
                        _methodBuilder.AppendFormat("---@param {0} {1}\n", new_name, fixType(pi.ParameterType));
                    }
                    paramNames[i - startIdx] = new_name;
                }
                var returnType = mi.ReturnType;
                if (typeof(void) != returnType)
                {
                    _methodBuilder.AppendFormat("---@return {0}\n", fixType(returnType));
                    _methodBuilder.AppendFormat("---@nodiscard\n");
                }
                string c = mi.IsStatic && !data.IsExtend ? "." : ":";
                _methodBuilder.AppendFormat("function m{0}{1}({2}) end\n", c, mi.Name, string.Join(", ", paramNames));
            }
        }

        private List<string> list_fix_name = new List<string>()
        {
            "end",
            "repeat"
        };
        private string fix_name(string piName)
        {
            if (list_fix_name.Contains(piName))
            {
                return "_" + piName;
            }

            return piName;
        }
    }
}