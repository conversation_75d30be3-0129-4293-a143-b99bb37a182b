using System.Collections.Generic;
using Sultan.ECSModule;
using Unity.Mathematics;
using UnityEngine;

namespace DefaultNamespace
{
    public class RenderTest : MonoBehaviour
    {
        public string npcMeshPath =
            "Character/Data/npc_octopus_01_001_low_animData/npc_octopus_01_001_low_a_0_mesh.asset";
        public string npcMatPath =
            "Character/Npc/npc_octopus_01_low/materials/npc_octopus_01_001_low.mat";

        public string fbxMeshPath = "Scenes/Models/Build/models/build_world_famuchang_lv01.fbx";
        public string fbxMatPath = "Scenes/Models/Build/materials/build_world_famuchang_lv01.mat";
        public string hudPath = "Scenes/Hud/prefabs/hud_lv.prefab";
        public string prefabPaht = "Scenes/Prefabs/Build/build_island03_bubingying_diy.prefab";

        public float3 CityPos;

        public void OnGUI()
        {
        }

        public void Start()
        {
            StartTest();

        }

        public void StartTest()
        {
            // ResManager.Instance.LoadAsset(fbxMeshPath, (o, op) =>
            // {
            //     var gameObject = o as GameObject;
            //     var m = o as Mesh;
            // });
            // ResLruCache c = new ResLruCache(10);
            //
            // var mesh = c.GetMesh(fbxMeshPath);
            // Debug.Log(mesh);
            if (CityPos.Equals(float3.zero))
            {
                CityPos = this.transform.localPosition;
            }
            uint uniqueId = GameWorld.Instance.GetOrCreateAsset<SkinRenderResource>(
                "Character/Data/npc_octopus_01_001_low_animData/npc_octopus_01_001_low_animData.asset");

            var list = new List<float3>();
            for(int i=0; i< 1; i++)
            {
                GameWorld.Instance.Convert(uniqueId, CityPos + new float3(8, 1, 20));
            }

            uint famoUniqueId = GameWorld.Instance.GetOrCreateAsset<MeshRenderResource>(
                fbxMeshPath, fbxMatPath);
            
            GameWorld.Instance.Convert(famoUniqueId, CityPos +new float3(16, 1, 20));
            // GameWorld.Instance.Convert(RenderComponent.GetUniqueId(fbxMeshPath, fbxMatPath), list , null);
            
            
            // uint groupUniqueId = GameWorld.Instance.GetOrCreateAsset<GroupRenderResource>(
            //     hudPath);
            // GameWorld.Instance.Convert(groupUniqueId, CityPos +new float3(8, 1, 20));

            TestArmy();
            TestHud();
        }

        private void TestArmy()
        {
            ArmyInfo info = new ArmyInfo();
            info.radius = 2;
            info.x = CityPos.x + 16;
            info.y = 0;
            info.z = CityPos.z + 8;
            info.ArmyId = 123123123123;
            int nTotal = 1;
            info.troops = new ArmyTroopInfo[nTotal];
            // for (int i = 0; i < nTotal; i++)
            {
                info.troops[0] = new ArmyTroopInfo()
                {
                    TroopId = 1,
                    TroopType = 0,
                    asset = new WeakAssetReference("Character/Data/soldier_swarrior_lv05_001_low_animData/soldier_swarrior_lv05_001_low_animData.asset"),
                    num = 1,
                    StandOffset = new float3(0,0,0.77f),
                    FightOffset = new float3(0,0,0.77f),
                    Scale =  1,
                    isGenerated = false
                };
            }
            EntityFactory.Instance.CreateArmy(info, quaternion.identity);
        }


        private void TestHud()
        {
            var hudAtlasPath = "Scenes/Hud/textures/hud.spriteatlas";
            var hudMatPath = "Scenes/Hud/materials/common_world_hud.mat";
            uint groupUniqueId = GameWorld.Instance.GetOrCreateAsset<HUDRenderResource>(
                hudAtlasPath, hudMatPath); 
            
            GameWorld.Instance.ConvertHud(groupUniqueId, "icon_lod_alliance_flag_01" ,CityPos +  new float3(12, 1, 10), 2, 0);
            GameWorld.Instance.ConvertHud(groupUniqueId, "icon_lod_castle_01" ,CityPos +  new float3(8, 1, 10), 2, 0);
            GameWorld.Instance.ConvertHud(groupUniqueId, "maincity_icon_f_001" ,CityPos +  new float3(4, 1, 10), 2, 0);
        }
        
    }
}