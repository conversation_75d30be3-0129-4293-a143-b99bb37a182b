using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using Sultan.Manager;
using Sultan.Shadow.Runtime;
using UnityEngine;
using Sultan.Game;
using Sultan.Lighting;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.SceneManagement;
using XLua;
using Object = UnityEngine.Object;
#if UNITY_EDITOR
using UnityEditor;
using Sultan.Editor;
using Unity.Entities;
using UnityEditor.SceneManagement;
#endif

namespace Sultan.Test
{
    [DisallowMultipleComponent]
    [ExecuteAlways]
    public class SceneConfigSwitcher : MonoBehaviour
    {
#if UNITY_EDITOR

        [Header("当前场景的 场景框架配置")] public SceneConfig currentSceneConfig;
        [Header("当前场景的 profile")] public SceneConfigProfile sceneConfigProfile;
        [Header("当前profile的 环境光配置（只读）")] public LightingAsset currentLightingAsset;
        [Header("当前profile的 后处理配置（只读）")] public VolumeProfile currentVolumeProfile;
        [Header("当前profile的 阴影配置")] public ShadowConfig currentShadowConfig;
        [Header("当前profile的 雾效配置")] public FogAsset currentFogAsset;


        public Scene scene;
        public string sceneName;
        public string sceneConfigPath;
        public string SCENE_LEVELS_DIR = SceneConfigExporter.SCENE_LEVELS_DIR;

        public string scenePrefabPath;
        public string btnPrefabTitle;

        public GameObject scenePrefab;
        public SceneConfigExporter.SceneExportWindow.ExportAction ExportAction;

        private ShadowConfig defaultShadowConfig;

        private GameObject volumeObject;

        //当编辑器下的配置产生变动时调用
        public void OnValidate()
        {
            //Debug.Log("测试是否启用编辑器下刷新");
            AssignShadowConfig();
        }


        void OnEnable()
        {
            Init();
            //监听编辑器更新并刷新雾配置到场景
            EditorApplication.update += RefreshFogAsset;
            //Debug.Log("将配置界面上的雾效应用到当前场景");
        }

        void OnDisable()
        {
            EditorApplication.update -= RefreshFogAsset;
        }

        public void Init()
        {
            scene = EditorSceneManager.GetActiveScene();
            sceneName = scene.name;
            var sceneDir = Path.Combine(SCENE_LEVELS_DIR, sceneName);
            // Debug.LogWarning(sceneDir);
            if (!Directory.Exists(sceneDir))
            {
                Directory.CreateDirectory(sceneDir);
            }
            else
            {
                Debug.LogWarning("该场景同名配置文件夹已创建，请勿重复创建！");
            }

            sceneConfigPath = sceneDir;
            // 导出场景
            if (ExportAction == null)
            {
                ExportAction = new SceneConfigExporter.SceneExportWindow.ExportAction();
            }
            
            
            if (sceneConfigPath != null && sceneName != null)
            {
                scenePrefabPath = Path.Combine(sceneConfigPath, sceneName + ".prefab");
                scenePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(scenePrefabPath);
                
                btnPrefabTitle = "导出Prefab";
                if (scenePrefab != null)
                {
                    Debug.Log("Prefab已生成！");
                    btnPrefabTitle = "更新Prefab";
                }
            }
            else
            {
                btnPrefabTitle = "导出Prefab";
                Debug.Log("Prefab未生成，请点击导出Prefab.");
            }

            AssignShadowConfig(); //更新阴影配置到场景
            
            //找到场景中的Volume对象并存到变量VolumeObject
            GameObject[] globalVolumeObjs = GameObject.FindObjectsOfType<GameObject>()
                .Where(go => go.name == "Global Volume")
                .ToArray();
            if (globalVolumeObjs.Length == 0)
            {
                Debug.LogError("未找到任何 Global Volume 对象！");
                volumeObject = null;
                return;
            }
            else
            {
                volumeObject = globalVolumeObjs[0];
            }


        }


        public static T FindSceneComponenet<T>() where T : Object //Scene scene
        {
            var gos = GameObject.FindObjectsOfType<T>();
            if (gos != null && gos.Length > 0)
            {
                return gos[0];
            }

            return null;
        }


        public static Camera FindCurrentCamera()
        {
            var cameras = GameObject.FindObjectsOfType<Camera>();
            Camera camera = null;
            foreach (var item in cameras)
            {
                if (item.gameObject.activeSelf && !item.orthographic)
                {
                    camera = item;
                    break;
                }
            }

            if (camera == null)
            {
                foreach (var item in cameras)
                {
                    if (item.gameObject.activeSelf)
                    {
                        camera = item;
                        break;
                    }
                }
            }

            if (camera == null)
            {
                foreach (var item in cameras)
                {
                    camera = item;
                    break;
                }
            }

            return camera;
        }

        public static bool HasRenderFeature<T>(SceneConfig sceneConfig)
        {
            if (sceneConfig.renderFeatures != null)
            {
                foreach (var rf in sceneConfig.renderFeatures)
                {
                    if (rf != null)
                    {
                        if (rf.GetType() == typeof(T))
                        {
                            return true;
                        }
                    }
                    else
                    {
                        Debug.LogFormat("renderFeatures is null!");
                    }
                }
            }


            return false;
        }

        public static void AddRenderFeature<T>(SceneConfig sceneConfig) where T : ScriptableObject
        {
            ScriptableObject component = ScriptableObject.CreateInstance<T>();
            component.name = typeof(T).Name;
            AssetDatabase.AddObjectToAsset(component, sceneConfig);
            sceneConfig.renderFeatures.Add(component as ScriptableRendererFeature);
        }

        //////////////////////////////////////

        public void UpdateCameraValidRect(Scene scene, SceneConfig sceneConfig)
        {
            #region [validRect]

            GameObject validRectNode = null;
            GameObject[] allObjs = scene.GetRootGameObjects();
            foreach (var item in allObjs)
            {
                if (item.name == "validRect")
                {
                    validRectNode = item;
                    break;
                }
            }

            if (validRectNode != null)
            {
                sceneConfig.validRect = new Rect(
                    validRectNode.transform.position.x - validRectNode.transform.localScale.x * 0.5f,
                    validRectNode.transform.position.z - validRectNode.transform.localScale.z * 0.5f,
                    validRectNode.transform.localScale.x, validRectNode.transform.localScale.z);
            }

            #endregion
        }

        public void CollectVolumeToSwitcher()
        {
            Volume volume = volumeObject.GetComponent<Volume>();
            if (volume != null)
            {
                // string volumePath = AssetDatabase.GetAssetPath(volume.sharedProfile);
                // VolumeProfile vProfile = AssetDatabase.LoadAssetAtPath<VolumeProfile>(volumePath);

                //让界面上volume配置栏更新为收集到的
                // this.currentVolumeProfile = vProfile;
                this.currentVolumeProfile = volume.sharedProfile;
                // EditorUtility.SetDirty(volumeObject);


                Debug.Log("后处理文件已从场景中收集到界面");
            }
            else
            {
                Debug.LogWarning("当前场景中没有Volume后处理对象，请先在项目中创建Volume!");
            }
        }


        public void CreateSceneConfigAndProfile()
        {
            #region [sceneConfig]

            var sceneConfigFilePath = Path.Combine(sceneConfigPath, sceneName + ".asset");
            var sceneConfigProfileAssetPath =
                Path.Combine(sceneConfigPath, sceneName + "_ConfigProfile.asset");
            var lightAssetPath = Path.Combine(sceneConfigPath, sceneName + "_LightingAsset.asset");
            var fogAssetPath = Path.Combine(SCENE_LEVELS_DIR, sceneName, sceneName + "_FogAsset.asset");

            if (this.currentSceneConfig == null)
            {
                //优先找一下看是不是已经有了(如果找到了那就直接用)
                currentSceneConfig = AssetDatabase.LoadAssetAtPath<SceneConfig>(sceneConfigFilePath);
                //如果没找到就创建
                if (currentSceneConfig == null)
                {
                    this.currentSceneConfig = ScriptableObject.CreateInstance<SceneConfig>();
                    this.currentSceneConfig.SceneName = sceneName;
                    //将初次创建的场景配置作为界面上的关联
                    AssetDatabase.CreateAsset(this.currentSceneConfig, sceneConfigFilePath);
                }

                Debug.Log("创建SceneConfig并关联到界面上完成。");
            }
            else
            {
                Debug.LogWarning("SceneConfig已创建且关联,请勿重复创建！");
            }

            #endregion

            #region [sceneConfigProfile]

            if (this.sceneConfigProfile == null)
            {
                //先去找，有就直接关联
                this.sceneConfigProfile =
                    AssetDatabase.LoadAssetAtPath<SceneConfigProfile>(sceneConfigProfileAssetPath);
                if (this.sceneConfigProfile == null)
                {
                    this.sceneConfigProfile = ScriptableObject.CreateInstance<SceneConfigProfile>();
                    this.sceneConfigProfile.sceneName = sceneName;

                    //将初次创建的场景profile作为界面上的关联，且作为config的默认profile
                    AssetDatabase.CreateAsset(this.sceneConfigProfile, sceneConfigProfileAssetPath);
                    // this.currentSceneConfig.sceneConfigProfile = this.sceneConfigProfile;
                }

                Debug.Log("创建SceneConfigProflie完成并关联到界面。");
            }


            else
            {
                Debug.LogWarning("SceneConfigProfile已创建或已关联,请勿重复创建！");
            }

            #endregion

            #region [lightAsset]

            if (this.currentLightingAsset == null) //lightAsset界面上为空
            {
                if (this.sceneConfigProfile != null && this.sceneConfigProfile.lightAsset != null)
                {
                    currentLightingAsset = this.sceneConfigProfile.lightAsset;
                }
                else
                {
                    currentLightingAsset =
                        AssetDatabase.LoadAssetAtPath<LightingAsset>(lightAssetPath);
                    if (this.currentLightingAsset == null)
                    {
                        this.currentLightingAsset = ScriptableObject.CreateInstance<LightingAsset>();
                        this.currentLightingAsset.Baked();

                        //初次创建的lightAsset作为界面上的关联
                        AssetDatabase.CreateAsset(this.currentLightingAsset, lightAssetPath);
                        Debug.Log("创建lightAsset完成。");
                    }
                }
            }
            else
            {
                Debug.LogWarning("lightAsset已创建且关联，请勿重复创建！");
            }

            #endregion

            #region [volume]

            if (this.currentVolumeProfile == null)
            {
                CollectVolumeToSwitcher();
            }

            #endregion

            #region [FogAsset]

            if (this.currentFogAsset == null)
            {
                if (this.sceneConfigProfile != null && this.sceneConfigProfile.fogAsset != null)
                {
                    currentFogAsset = this.sceneConfigProfile.fogAsset;
                }
                else
                {
                    currentFogAsset = AssetDatabase.LoadAssetAtPath<FogAsset>(fogAssetPath);
                    if (this.currentFogAsset == null)
                    {
                        this.currentFogAsset = ScriptableObject.CreateInstance<FogAsset>();
                        this.currentFogAsset.FogEnable = false;
                        this.currentFogAsset.RefreshTexture = false;

                        //初次创建雾效配置并关联给界面
                        AssetDatabase.CreateAsset(this.currentFogAsset, fogAssetPath);
                        Debug.Log("FogAsset已创建并关联完成");
                    }
                }

                RefreshFogAsset();
            }
            else
            {
                Debug.LogWarning("FogAsset已存在，请勿重复创建！");
            }

            #endregion

            #region [ShadowConfig]

            if (this.currentShadowConfig == defaultShadowConfig && this.sceneConfigProfile.shadowConfig != null)
            {
                this.currentShadowConfig = this.sceneConfigProfile.shadowConfig;
            }

            #endregion
        }

        public void RefreshFeatures()
        {
            Volume volume = volumeObject.GetComponent<Volume>();
            //3.更新renderFeature
            var camera = FindCurrentCamera();
            //3.1 如果设置了 Antialias，那么添加TAA
            var uacd = camera.GetUniversalAdditionalCameraData();
            if (this.currentSceneConfig.renderFeatures == null)
            {
                this.currentSceneConfig.renderFeatures = new List<ScriptableRendererFeature>();
            }

            if (uacd.antialiasing != AntialiasingMode.None)
            {
                // if (!HasRenderFeature<TAAFeature>(sceneConfig)) //没有则创建
                // {
                //     AddRenderFeature<TAAFeature>(sceneConfig);
                //     // sceneConfig.renderFeatures.Add(ScriptableObject.CreateInstance<TAAFeature>());
                // }
            }

            //3.2 如果volume设置了AO，那么添加HBAO
            if (volume != null && volume.profile != null)
            {
                var hasAO = volume.profile.Has<AmbientOcclusion>();
                if (hasAO != false)
                {
                    if (!HasRenderFeature<HorizonBasedAmbientOcclusion>(this.currentSceneConfig))
                    {
                        AddRenderFeature<HorizonBasedAmbientOcclusion>(this.currentSceneConfig);
                        // sceneConfig.renderFeatures.Add(ScriptableObject.CreateInstance<HorizonBasedAmbientOcclusion>());
                    }
                }
            }

            //3.3 统一添加阴影
            if (!HasRenderFeature<CSMS_RenderFeature>(this.currentSceneConfig))
                AddRenderFeature<CSMS_RenderFeature>(this.currentSceneConfig);

            this.currentSceneConfig.ValidateRendererFeatures();
        }

        public void SaveDatas()
        {
            //保存
            EditorUtility.SetDirty(this.currentSceneConfig.sceneConfigProfile);
            EditorUtility.SetDirty(this.currentSceneConfig);
            EditorUtility.SetDirty(this.sceneConfigProfile);
            EditorUtility.SetDirty(this.currentShadowConfig);
            EditorUtility.SetDirty(this.currentFogAsset);
            EditorUtility.SetDirty(this.currentLightingAsset);
            EditorUtility.SetDirty(this.currentVolumeProfile);
            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        public void RefreshConfigs()
        {
            CollectVolumeToSwitcher(); //先把更新后的收集到界面上
            currentLightingAsset.Baked(); //收集Environment里的参数给当前界面上关联的lightasset
            if (this.currentSceneConfig && this.sceneConfigProfile && this.currentLightingAsset &&
                this.currentVolumeProfile && this.currentFogAsset)
            {
                this.currentSceneConfig.sceneConfigProfile = this.sceneConfigProfile;

                this.sceneConfigProfile.lightAsset = this.currentLightingAsset;
                this.sceneConfigProfile.volumeProfile = this.currentVolumeProfile;
                this.sceneConfigProfile.shadowConfig = this.currentShadowConfig;
                this.sceneConfigProfile.fogAsset = this.currentFogAsset;

                Debug.Log("已将界面中的四个配置更新到Profile中，并将当前Profile作为Config的默认配置");
            }
            else
            {
                Debug.LogWarning("有某个配置没创建，请先点首次创建配置！");
            }
        }

        public void ReadConfigProfile()
        {
            var profileLightAsset = this.sceneConfigProfile.lightAsset;
            var profileVolume = this.sceneConfigProfile.volumeProfile;
            var profileShadowConfig = this.sceneConfigProfile.shadowConfig;
            var profileFogAsset = this.sceneConfigProfile.fogAsset;

            if (this.sceneConfigProfile && profileLightAsset && profileVolume && profileShadowConfig && profileFogAsset)
            {
                this.currentLightingAsset = profileLightAsset;
                this.currentLightingAsset.Apply();

                this.currentVolumeProfile = profileVolume;
                Volume volume = volumeObject.GetComponent<Volume>();
                if (volume != null)
                {
                    volume.sharedProfile = profileVolume;
                    EditorUtility.SetDirty(volumeObject);
                }
                else
                {
                    Debug.LogWarning("没有找到场景中的Volume组件");
                }

                this.currentShadowConfig = profileShadowConfig;
                this.currentFogAsset = profileFogAsset;
                Debug.Log("当前Profile已读取完成。");
            }
            else
            {
                Debug.LogWarning("当前Profile有某个配置没创建，请先确认一下Profile配置是否完整！");
            }
            EditorSceneManager.MarkSceneDirty(scene);
        }


        public void AssignShadowConfig()
        {
            Shader.SetGlobalFloat(enableKingdomDarkID, 0.0f); //编辑器下不查看九国压暗效果

            //shadowConfig 根据是否为空来决定是否使用默认阴影配置
            var defaultShadowConfigPath = AssetDatabase.GUIDToAssetPath(defaultShadowConfigPathGUID);
            defaultShadowConfig = AssetDatabase.LoadAssetAtPath<ShadowConfig>(defaultShadowConfigPath);

            if (currentShadowConfig == null)
            {
                currentShadowConfig = defaultShadowConfig;
            }

            //获取forwardRenderFeature列表
            var renderDataPath = AssetDatabase.GUIDToAssetPath(renderDataPathGUID);
            forwardRendererData = AssetDatabase.LoadAssetAtPath<ScriptableRendererData>(renderDataPath);
            features = forwardRendererData.rendererFeatures;

            if (currentSceneConfig != null && currentShadowConfig != null)
            {
                foreach (var feature in features)
                {
                    if (feature is CSMS_RenderFeature csmsRenderFeature)
                    {
                        //将阴影配置给到编辑器下ForwardRenderFeature的 CSMSRender Feature中
                        csmsRenderFeature.DefaultShadowConfig = currentShadowConfig;
                    }
                }
            }
        }


        private void RefreshFogAsset()
        {
            //Debug.Log("将配置界面上的雾效应用到当前场景");
            // var defaultFogConfigPath = AssetDatabase.GUIDToAssetPath(defaultFogConfigPathGUID);
            // var defaultFogConfig = AssetDatabase.LoadAssetAtPath<FogAsset>(defaultFogConfigPath);

            if (this.currentFogAsset == null)
            {
                //currentFogAsset = defaultFogConfig;
                Shader.DisableKeyword("SULTAN_FOG_ON");
            }
            else
            {
                //Shader.EnableKeyword("SULTAN_FOG_ON");
                this.currentFogAsset.DataDirty = true;
                this.currentFogAsset.CheckDataDirty();
            }
        }


        private string renderDataPathGUID = "4a8e21d5c33334b11b34a596161b9360"; //ForwardRenderer
        private string defaultShadowConfigPathGUID = "0cd1d651f8c834715beaa5c6c5fe353d"; //defaultShadowConfig
        // private string defaultFogConfigPathGUID = "8c19540f1e3ca0d489d8607d657379b4"; //defaultFogConfig 默认如果没有雾效就创建一个新的

        private ScriptableRendererData forwardRendererData;
        private List<ScriptableRendererFeature> features;
        private static readonly int enableKingdomDarkID = Shader.PropertyToID("_EnableKingdomDark");
#endif
    }
}