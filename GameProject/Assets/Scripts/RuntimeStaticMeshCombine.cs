using System.Collections.Generic;
using SRF;
using Terrains;
using Unity.Collections;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Rendering;

namespace Sultan.Core
{
    //简单实现的静态合批，只支持MeshRenderer, 静态对象有变化的时候删除组件 再重新加，mesh.CombineMeshes有限制，awake中调用可以绕过mesh的读写标记
    public class RuntimeStaticMeshCombine : MonoBehaviour
    {
        public bool RemoveCombineNode = false;
        public bool NodeDirty = true;
        private List<GameObject> finalRenderObjects;
        private List<Mesh> finalMeshs;
        public void Awake()
        {
            finalRenderObjects = new List<GameObject>();
            finalMeshs = new List<Mesh>();
        }

        public void Update()
        {
            if(NodeDirty)
                UpdateSubRender();
        }


        public void OnDestroy()
        {
            foreach (var mesh in finalMeshs)
            {
                DestroyImmediate(mesh);
                Resources.UnloadAsset(mesh);
            }

            foreach (var obj in finalRenderObjects)
            {
                DestroyImmediate(obj);
            }
            finalMeshs.Clear();
        }

        public void Clear()
        {
            foreach (var mesh in finalMeshs)
            {
                DestroyImmediate(mesh);
                Resources.UnloadAsset(mesh);
            }

            foreach (var obj in finalRenderObjects)
            {
                DestroyImmediate(obj);
            }
            finalMeshs.Clear();
        }

        public void UpdateSubRender()
        {
            Clear();
            NodeDirty = false;
            var meshRenders = GetComponentsInChildren<MeshRenderer>();
            Dictionary<Material, List<CombineInstance>> renderDataDict = new Dictionary<Material, List<CombineInstance>>();
            foreach (var render in meshRenders)
            {
                if (render.gameObject.activeInHierarchy && !render.gameObject.name.StartsWith("CombineMesh_"))
                {
                    MeshFilter meshFilter = render.gameObject.GetComponent<MeshFilter>();
                    if (render.sharedMaterial != null && meshFilter != null && meshFilter.sharedMesh != null)
                    {
                        render.gameObject.RemoveComponentIfExists<RenderCollectComponent>(); 
                        render.enabled = false;
                        AddCombineMesh(meshFilter, render.sharedMaterial, ref renderDataDict);
                        if(RemoveCombineNode)
                            DestroyImmediate(render.gameObject);
                    }
                }
            }
            
            int i = 0;
            foreach (var kv in renderDataDict)
            {
                var combineInstances = kv.Value;
                var combineMesh = new Mesh();
                // combineMesh.Optimize();
                // combineMesh.UploadMeshData(false);
                var array = combineInstances.ToArray();
                combineMesh.CombineMeshes(array, true, true, false);
                // var combineMesh = CombineMesh(array);
                finalMeshs.Add(combineMesh);
                var go = new GameObject($"CombineMesh_{i}");
                i++;
                go.transform.SetParent(transform);
                var meshFilter = go.AddComponent<MeshFilter>();
                meshFilter.sharedMesh = combineMesh;
                
                var meshRenderer = go.AddComponent<MeshRenderer>();
                meshRenderer.sharedMaterial = kv.Key;
                var com = go.AddComponent<RenderCollectComponent>();
                com.IsStatic = true;
                finalRenderObjects.Add(go);
                // combineMesh.UploadMeshData(true);
                //
                // foreach (var node in array)
                // {
                //     node.mesh.Optimize();
                //     node.mesh.UploadMeshData(true);
                // }
            }
        }
        
        public void AddCombineMesh(MeshFilter meshFilter, Material material, ref Dictionary<Material, List<CombineInstance>> renderDataDict)
        {
            if(!renderDataDict.TryGetValue(material, out var combineInstances))
            {
                combineInstances = new List<CombineInstance>();
                renderDataDict.Add(material, combineInstances);
            }
            
            combineInstances.Add(new CombineInstance()
            {
                mesh = meshFilter.sharedMesh,
                transform = meshFilter.transform.localToWorldMatrix
            });
        }

        #region hiden
        #if false
        internal struct VectexData
        {
            public half posX, posY, posZ, posW;
            public half uvX, uvY;
 
        }

        //不处理顶点色
        public Mesh CombineMesh(CombineInstance[] combineInstances)
        {
            List<Vector4> vertexList = new List<Vector4>();
            List<Vector2> uvList = new List<Vector2>();
            List<Color> colorList = new List<Color>();
            List<ushort> triangleList = new List<ushort>();
            List<Vector2Int> indexList = new List<Vector2Int>();
            List<Vector4> transList = new List<Vector4>();
            int triOffset = 0;
            
            List<Vector3> curVertexList = new List<Vector3>();
            List<Vector2> curUVList = new List<Vector2>();
            List<Color> curColorList = new List<Color>();
            List<int> curTriangle = new List<int>();
            
            float maxX = float.MinValue, maxY = float.MinValue, maxZ = float.MinValue;
            float minX = float.MaxValue, minY = float.MaxValue, minZ = float.MaxValue;
            
            VertexAttributeDescriptor[] layout = new[]
            {
                new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float16, 4),
                new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float16, 2),
            };

            for (int i = 0; i < combineInstances.Length; i++)
            {
                var combineData = combineInstances[i];
                Mesh curMesh = combineData.mesh;
                curVertexList.Clear();
                curUVList.Clear();
                curColorList.Clear();
                curTriangle.Clear();
                //vertex
                curMesh.GetVertices(curVertexList);

                //uv
                curMesh.GetUVs(0, curUVList);
                
                //color
                curMesh.GetColors(curColorList);
                
                //tri
                curMesh.GetTriangles(curTriangle, 0);
                
                //collect
                uvList.AddRange(curUVList);
                colorList.AddRange(curColorList);
                
                Matrix4x4 local2World = combineData.transform;
                for (int v = 0; v < curVertexList.Count; v++)
                {
                    Vector3 localPos = curVertexList[v];
                    Vector3 worldPos = local2World * new Vector4(localPos.x, localPos.y, localPos.z, 1);
                    maxX = Mathf.Max(maxX, worldPos.x);
                    maxY = Mathf.Max(maxY, worldPos.y);
                    maxZ = Mathf.Max(maxZ, worldPos.z);
                    
                    minX = Mathf.Min(minX, worldPos.x);
                    minY = Mathf.Min(minY, worldPos.y);
                    minZ = Mathf.Min(minZ, worldPos.z);
                    
                    vertexList.Add(new Vector4(worldPos.x, worldPos.y, worldPos.z, i));
                }
                for (int t = 0; t < curTriangle.Count; t++)
                {
                    triangleList.Add((ushort)(curTriangle[t] + triOffset));
                }
                triOffset += curVertexList.Count;
            }
            
            Mesh finalMesh = new Mesh();
            finalMesh.SetVertexBufferParams(vertexList.Count, layout);
            NativeArray<VectexData> finalVertexList = new NativeArray<VectexData>(vertexList.Count, Allocator.Temp);
            for (int i = 0; i < vertexList.Count; i++)
            {
                VectexData vertex = new VectexData();
                vertex.posX = new half(vertexList[i].x);
                vertex.posY = new half(vertexList[i].y);
                vertex.posZ = new half(vertexList[i].z);
                vertex.posW = new half(vertexList[i].w);
                
                vertex.uvX = new half(uvList[i].x);
                vertex.uvY = new half(uvList[i].y);
                finalVertexList[i] = vertex;
            }
            finalMesh.SetVertexBufferData(finalVertexList, 0, 0, finalVertexList.Length);
            finalVertexList.Dispose();
            
            finalMesh.SetIndexBufferParams(triangleList.Count, IndexFormat.UInt16);
            finalMesh.SetIndexBufferData(triangleList.ToArray(), 0, 0, triangleList.Count);
            finalMesh.subMeshCount = 1;
            
            Bounds finalMeshBound = new Bounds();
            finalMeshBound.SetMinMax(new Vector3(minX, minY, minZ), new Vector3(maxX, maxY, maxZ));
            
            var subMeshDesc = new SubMeshDescriptor(0, triangleList.Count, MeshTopology.Triangles);
            subMeshDesc.bounds = finalMeshBound;
            finalMesh.SetSubMesh(0, subMeshDesc);
            finalMesh.bounds = finalMeshBound;

            finalMesh.UploadMeshData(true);
            return finalMesh;
        }
        #endif
        #endregion        
    }
}