using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using Sultan.Manager;
using Sultan.Shadow.Runtime;
using Sultan.Terrain.Runtime;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class TestCSMScroll : MonoBehaviour
{
    public bool UseLegacyCSM = false;
    public bool EnableDrawCSMDebug = false;
    public bool EnableRenderDocCapture = false;
    public float ScrollDepthBias = 0.0f;
 

    public bool EnableForceDrawShadowmapPerFrame = false;
    public bool EnableForceAlwaysScroll = false;

    public bool LockShadowCameraAutoMove = false;

    public bool MoveLeft = false;

    public bool MoveRight = false;

    public int MoveStep = 10;
    // Start is called before the first frame update
    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        DrawCSMDebug();
    }
    
    public void OnClickSwitch()
    {
        // if (TerrainManager.Instance.UseLegacyCSM)
        // {
        //     TerrainManager.Instance.UseLegacyCSM = false;
        //     EnableRenderFeature(false);
        //     Debug.Log("USE_CSM_SCROLLING");
        // }
        // else
        // {
        //     TerrainManager.Instance.UseLegacyCSM = true;
        //     EnableRenderFeature(true);
        //     Debug.Log("USE_LegacyCSM");
        // }
    }

    private bool LastUseLegacyCSM = true;
    private void OnValidate()
    {
        // if (null != CSMC_ShadowCameraUpdater.m_instance)
        // {
        //     CSMC_ShadowCameraUpdater.m_instance.LockAutoMove = LockShadowCameraAutoMove;
        // }

        if (MoveLeft)
        {
            MoveLeft = false;
            if (m_csmsFeature)
            {
               Camera cam = m_csmsFeature.GetShadowCamera();
               cam.transform.position = (cam.transform.right * -MoveStep) + cam.transform.position;
            }
        }
        
        if (MoveRight)
        {
            MoveRight = false;
            if (m_csmsFeature)
            {
                Camera cam = m_csmsFeature.GetShadowCamera();
                cam.transform.position = (cam.transform.right * MoveStep) + cam.transform.position;
            }
        }

        if (LastUseLegacyCSM != UseLegacyCSM)
        {
            LastUseLegacyCSM = UseLegacyCSM;
            Debug.Log("USE Legacy CSM:" + (UseLegacyCSM ? 1 : 0));
            TerrainManager.Instance.UseLegacyCSM = UseLegacyCSM;
            EnableRenderFeature(UseLegacyCSM);
        }

        CSMS_RenderFeature feature = GetRenderFeature<CSMS_RenderFeature>() as CSMS_RenderFeature;
        if (feature)
        {
            feature.ForceAlwaysScroll = EnableForceAlwaysScroll;
            feature.ScrollShadowBias = ScrollDepthBias;
            feature.EnableRDCCapture = EnableRenderDocCapture;
            feature.ForceDrawShadowMaoPerFrame = EnableForceDrawShadowmapPerFrame;
        }
    }

    ScriptableRendererFeature GetRenderFeature<T>()
    {
        Camera mainCamera = CameraSystem.Instance.MainCamera;
        UniversalAdditionalCameraData urpData = mainCamera.GetComponent<UniversalAdditionalCameraData>();
        if (urpData)
        {
            var renderer = urpData.scriptableRenderer;
            var property =
                typeof(ScriptableRenderer).GetProperty("rendererFeatures",
                    BindingFlags.NonPublic | BindingFlags.Instance);

            List<ScriptableRendererFeature> features = property.GetValue(renderer) as List<ScriptableRendererFeature>;

            foreach (var feature in features)
            {
                if (feature is T)
                {
                    return feature;
                }
            }
        }

        return null;
    }

    private CSMS_RenderFeature m_csmsFeature;
    void EnableRenderFeature(bool useLegacyCSM)
    {
        Camera mainCamera = CameraSystem.Instance.MainCamera;
        UniversalAdditionalCameraData urpData = mainCamera.GetComponent<UniversalAdditionalCameraData>();
        if (urpData)
        {
            var renderer = urpData.scriptableRenderer;
            var property =
                typeof(ScriptableRenderer).GetProperty("rendererFeatures",
                    BindingFlags.NonPublic | BindingFlags.Instance);

            List<ScriptableRendererFeature> features = property.GetValue(renderer) as List<ScriptableRendererFeature>;

            foreach (var feature in features)
            {
                // if (feature.GetType() == typeof(SSM_RenderFeature))
                // {
                //     feature.SetActive(useLegacyCSM);
                // }

                if (feature.GetType() == typeof(CSMS_RenderFeature))
                {
                    feature.SetActive(!useLegacyCSM);
                    m_csmsFeature = feature as CSMS_RenderFeature;
                }
            }
        }
    }

    private List<TrimInfo> m_curFrameStaticList = new List<TrimInfo>();
    private Queue<GameObject> m_curFrameDisplayedCube = new Queue<GameObject>();
    private Queue<GameObject> m_curFrameDebugCube = new Queue<GameObject>();
    void DrawCSMDebug()
    {
        if (null == m_csmsFeature)
        {
            return;
        }

        //m_csmsFeature.ForceDrawShadowMaoPerFrame = EnableForceDrawShadowmapPerFrame;

        if (EnableDrawCSMDebug)
        {
            bool hasCameraMoved = true;//Mathf.Abs(Time.frameCount - TerrainManager.Instance.GetCameraMoveFrame) < 2;
            //Debug.Log(TerrainManager.Instance.GetCameraMoveFrame +"_"+Time.frameCount);
        
            if (hasCameraMoved)
            {
                while (m_curFrameDisplayedCube.Count > 0)
                {
                    GameObject cube = m_curFrameDisplayedCube.Dequeue();
                    cube.SetActive(false);
                    m_curFrameDebugCube.Enqueue(cube);
                }
        
                //TerrainManager.Instance.CSMScrollingGetStaticObjList(m_curFrameStaticList);

                for (int i = 0; i < m_curFrameStaticList.Count; i++)
                {
                    TrimInfo info = m_curFrameStaticList[i];

                    if (info.matrixRenderArr.size > 0)
                    {
                        for (int j = 0; j < info.matrixRenderArr.size; j++)
                        {
                            GameObject debugCube = null;
                            if (m_curFrameDebugCube.Count > 0)
                            {
                                debugCube = m_curFrameDebugCube.Dequeue();
                            }
                            else
                            {
                                debugCube = GameObject.CreatePrimitive(PrimitiveType.Cube);
                            }
            
                            m_curFrameDisplayedCube.Enqueue(debugCube);

                            Vector3 worldPos = info.matrixRenderArr[j].GetPosition();
                            debugCube.name = info.matPath;
                            debugCube.transform.position = worldPos;
                            debugCube.SetActive(true);
                        }
                    }
                }
            }
        }
    }
}
