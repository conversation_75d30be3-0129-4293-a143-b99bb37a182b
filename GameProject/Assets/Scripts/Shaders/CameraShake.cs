using System;
using System.Collections;
using Sultan.Core;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Sultan.Test
{
    // [ExecuteInEditMode]
    [DisallowMultipleComponent]
    public class CameraShake : MonoBehaviour
    {
        [Header("产生位移抖动的对象")] public Transform ShakeObject;
        [Header("是否循环")] public bool IsLoop = false; // 是否无限循环该抖动
        [Header("抖动持续时间")] public float Duration = 1f; // 抖动持续时间
        [Header("抖动幅度")] public float Magnitude = 0.1f; // 抖动幅度
        public AnimationCurve DecayCurve = AnimationCurve.Linear(0.0f, 1.0f, 1.0f, 0.0f); // 控制抖动衰减的动画曲线

        private int shakeCount = 2; // 抖动次数
        private float elapsedTime = 0f; // 记录已经过去的时间
        private bool isShaking = false; // 标记是否正在抖动
        // private Vector3 originalPos;

        private Camera camera;
        private Matrix4x4 originalProjectionMatrix;
        private Matrix4x4 jitterMatrix = Matrix4x4.identity;

        private void OnEnable()
        {
            if (ShakeObject == null)
            {
                ShakeObject = this.transform;
            }
        
            camera = ShakeObject.GetComponent<Camera>();
            // 记录原始投影矩阵
            if (camera != null)
            {
                originalProjectionMatrix = camera.projectionMatrix;
                jitterMatrix = camera.projectionMatrix;
                StartShake();
            }

        }

        private void OnDisable()
        {
            StopShake();
            if (!IsLoop)
            {
                shakeCount += 1;
            }
        }

        void Update()
        {
            if (isShaking && camera!=null )
            {
                ShakeCamera();
                elapsedTime += Time.deltaTime;
                if (elapsedTime >= Duration)
                {
                    if (!IsLoop)
                    {
                        StopShake();
                    }
                    else
                    {
                        elapsedTime = 0f;
                    }
                }
            }
        }

        public void StartShake()
        {
            if (!isShaking)
            {
                isShaking = true;
                elapsedTime = 0f;
                if (!IsLoop)
                {
                    shakeCount -= 1; // 每次开始抖动时减少次数
                    if (shakeCount <= 0) // 如果次数用完，停止抖动
                    {
                        StopShake();
                    }
                }
            }
        }

        void ShakeCamera()
        {
            float decayedMagnitude = Magnitude * DecayCurve.Evaluate(elapsedTime / Duration);
            Vector3 shakeOffset = Random.insideUnitSphere * decayedMagnitude;


            jitterMatrix.m03 = originalProjectionMatrix.m03 + shakeOffset.x;
            jitterMatrix.m13 = originalProjectionMatrix.m13 + shakeOffset.y;
            camera.projectionMatrix = jitterMatrix;
        }

        void StopShake()
        {
            isShaking = false;
            if (camera != null)
            {
                camera.projectionMatrix = originalProjectionMatrix; 
            }
        }

        public void SetShakeCamera(Camera camera)
        {
            Debug.Assert(camera!= null, "CameraShake SetShakeObj:transform is null");
            Debug.Log($"CameraShake SetShakeObj:{camera}");
            enabled = false;
            ShakeObject = camera.transform;
            enabled = true;
        }
    }
}