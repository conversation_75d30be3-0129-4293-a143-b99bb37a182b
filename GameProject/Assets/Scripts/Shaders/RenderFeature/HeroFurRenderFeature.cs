using System;
using System.Collections.Generic;
using Unity.Collections;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace Sultan.RenderFeature
{
    /// <summary>
    /// fur render feature
    /// </summary>
    public class HeroFurRenderFeature : ScriptableRendererFeature
    {
        /// <summary>
        /// Render Feature Pass
        /// </summary>
        public class FurRenderPass : ScriptableRenderPass
        {       
            const string m_ProfilerTag = "HeroFurRenderPass";
            private static readonly ProfilingSampler m_ProfilingSampler = new ProfilingSampler(m_ProfilerTag);

            private readonly ShaderTagId shaderTagId = new ShaderTagId("Fur");
            private int passCount;
            private CommandBuffer cmdBuffer;

            class CachedBufferData
            {
                public int LastUsedFrame;
                public GraphicsBuffer UVBuffer;
                public GraphicsBuffer IndexBuffer;

                public void ReleaseBuffer()
                {
                    UVBuffer.Release();
                    IndexBuffer.Release();
                }
            }

            private static int SHADER_ID_VERTEXBUFFER = Shader.PropertyToID("_InstancingSkinnedVertexBuffer");
            private static int SHADER_ID_UVBUFFER = Shader.PropertyToID("_InstancingUVBuffer");
            private static int SHADER_ID_FUROFFSET = Shader.PropertyToID("_InstancingFUR_OFFSET");
            private const string INSTANCING_UV32_PASS_NAME = "FurInstancing_UV32";
            private const string INSTANCING_UV16_PASS_NAME = "FurInstancing_UV16";
            private const string NORMAL_PASS_NAME = "FurPass";
            private const string LIGHT_MODEL_NAME = "Fur";
            private const string HERO_FIND_PATH = "Heroshow_01/Stage";
            private const string CMD_PROFILER = "FurFeatureCmd";

            private bool m_supportDrawProcedural = false;
            private int m_instancingPassIdx = -1;
            private HashSet<SkinnedMeshRenderer> m_collectedFurSkinnedMeshRenderer;
            private Dictionary<int, CachedBufferData> m_cachedBuffer;
            private Queue<int> m_willRemoveKeys;

            public FurRenderPass(int passCount)
            {
                this.passCount = passCount;
                m_collectedFurSkinnedMeshRenderer = new HashSet<SkinnedMeshRenderer>();
                m_cachedBuffer = new Dictionary<int, CachedBufferData>();
                m_willRemoveKeys = new Queue<int>();
                
                m_instancingPassIdx = -1;

                var graphicsName = SystemInfo.graphicsDeviceName.ToLower();
                bool isMaliChip = graphicsName.Contains("mali");
                
                m_supportDrawProcedural = 
                    SystemInfo.supportsInstancing && 
                    SystemInfo.supportsComputeShaders &&
                    SystemInfo.graphicsShaderLevel >= 45 &&
                    !isMaliChip;
            }

            public void ClearCached()
            {
                if (null != m_cachedBuffer)
                {
                    foreach (var kvp in m_cachedBuffer)
                    {
                        kvp.Value.ReleaseBuffer();
                    }
                    m_cachedBuffer.Clear();
                }

                if (null != m_collectedFurSkinnedMeshRenderer)
                {
                    m_collectedFurSkinnedMeshRenderer.Clear();
                }

                if (null != m_willRemoveKeys)
                {
                    m_willRemoveKeys.Clear();
                }
            }
            
            private void CollectSkinnedMeshRendererWithFurPass()
            {
                m_collectedFurSkinnedMeshRenderer.Clear();
                GameObject stageGo = GameObject.Find(HERO_FIND_PATH);
                if (stageGo)
                {
                    SkinnedMeshRenderer[] smrs = stageGo.GetComponentsInChildren<SkinnedMeshRenderer>();
                    foreach (var smr in smrs)
                    {
                        Material shareMaterial = smr.sharedMaterial;
                        bool hasFurPass = shareMaterial.FindPass(NORMAL_PASS_NAME) != -1;
                        bool hasFurPassEnabled = shareMaterial.GetShaderPassEnabled(LIGHT_MODEL_NAME);
                        if (shareMaterial && (hasFurPass && hasFurPassEnabled))
                        {
                            m_collectedFurSkinnedMeshRenderer.Add(smr);

                            if (m_instancingPassIdx == -1)
                            {
                                m_supportDrawProcedural &= shareMaterial.shader.isSupported;
                                
                                var uvBufferIndex = smr.sharedMesh.GetVertexAttributeStream(VertexAttribute.TexCoord0);
                                VertexAttributeDescriptor uvAttribute = default;

                                var attributes = smr.sharedMesh.GetVertexAttributes();

                                bool findUVAttr = false;
                                for (int i = 0; i < attributes.Length; ++i)
                                {
                                    var attribute = attributes[i];
                                    if (attribute.attribute == VertexAttribute.TexCoord0)
                                    {
                                        uvAttribute = attribute;
                                        findUVAttr = true;
                                        break;
                                    }
                                }

                                if (findUVAttr)
                                {
                                    m_instancingPassIdx = FindPassIndexWithUVStride(shareMaterial,
                                        uvAttribute.format == VertexAttributeFormat.Float32 ? 8 : 4);
                                }
                            }
                        }
                    }
                }
            }

            // This method is called before executing the render pass.
            // It can be used to configure render targets and their clear state. Also to create temporary render target textures.
            // When empty this render pass will render to the active camera render target.
            // You should never call CommandBuffer.SetRenderTarget. Instead call <c>ConfigureTarget</c> and <c>ConfigureClear</c>.
            // The render pipeline will ensure target setup and clearing happens in an performance manner.
            public override void OnCameraSetup(CommandBuffer cmd, ref RenderingData renderingData)
            {
                cmdBuffer = CommandBufferPool.Get(CMD_PROFILER);

                #if !UNITY_EDITOR
                //模拟器平台不开启毛发合批
                m_supportDrawProcedural &= !NativeRenderingPlugin.IsSimulatePlatform();
                #endif

                if (m_supportDrawProcedural)
                {
                    CollectSkinnedMeshRendererWithFurPass();
                }
            }

            private int FindPassIndexWithUVStride(Material mat, int stride)
            {
                if (stride > 4)
                {
                    return mat.FindPass(INSTANCING_UV32_PASS_NAME);
                }
                
                return mat.FindPass(INSTANCING_UV16_PASS_NAME);
            }

            private Queue<GraphicsBuffer> tmpBuffer = new Queue<GraphicsBuffer>();
            // Here you can implement the rendering logic.
            // Use <c>ScriptableRenderContext</c> to issue drawing commands or execute command buffers
            // https://docs.unity3d.com/ScriptReference/Rendering.ScriptableRenderContext.html
            // You don't have to call ScriptableRenderContext.submit, the render pipeline will call it at specific points in the pipeline.
            public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
            {
                if (passCount == 0) return;
                
                GameRenderQuality curQuality = GameRenderQuality.GetCurrentQuality();
                if (curQuality.FUR_Passcount_DownDivider == 0) return;

                bool useInstancing = m_supportDrawProcedural && curQuality.ENABLE_FUR_INSTANCING;
                
                #if UNITY_EDITOR
                if (!Application.isPlaying)
                {
                    useInstancing = false;
                }
                #endif
                
                int modifyedPassCount = (int)(passCount / (float)curQuality.FUR_Passcount_DownDivider);
                float inter = 1.0f / modifyedPassCount;
                
                //use instancing fur
                if (useInstancing)
                {
                    if (m_instancingPassIdx == -1 || null == m_collectedFurSkinnedMeshRenderer ||
                        m_collectedFurSkinnedMeshRenderer.Count == 0)
                    {
                        return;
                    }
                    
                    cmdBuffer.Clear();
                    foreach (var smr in m_collectedFurSkinnedMeshRenderer)
                    {
                        if (!smr.isVisible) continue;
                        
                        var shareMesh = smr.sharedMesh;
                        var shareMaterial = smr.sharedMaterial;
                        
                        shareMesh.vertexBufferTarget = GraphicsBuffer.Target.Vertex | GraphicsBuffer.Target.Raw;
                        smr.vertexBufferTarget = GraphicsBuffer.Target.Vertex | GraphicsBuffer.Target.Raw;
                        
                        var skinnedVertexBuffer = smr.GetVertexBuffer();
                        
                        GraphicsBuffer uvBuffer = null;
                        GraphicsBuffer indexBuffer = null;
                        CachedBufferData cached = null;

                        int smrId = smr.GetInstanceID();
                        bool hasCached = m_cachedBuffer.TryGetValue(smrId, out cached);
                        if (!hasCached)
                        {
                            var uvBufferIndex = shareMesh.GetVertexAttributeStream(VertexAttribute.TexCoord0);
                            
                            uvBuffer = smr.sharedMesh.GetVertexBuffer(uvBufferIndex);
                            indexBuffer = smr.sharedMesh.GetIndexBuffer();
                            
                            // List<Vector4> uvs = new List<Vector4>();
                            // smr.sharedMesh.GetUVs(0, uvs);
                            // half[] halfUVS = new half[uvs.Count * 2];
                            // for (int i = 0; i < uvs.Count; i++)
                            // {
                            //     int halfIdx = i * 2;
                            //     halfUVS[halfIdx] = (half)uvs[i].x;
                            //     halfUVS[halfIdx + 1] = (half)uvs[i].y;
                            // }
                            //
                            // uvBuffer = new GraphicsBuffer(GraphicsBuffer.Target.Vertex | GraphicsBuffer.Target.Raw, halfUVS.Length, 4);
                            // uvBuffer.SetData(halfUVS);
                            
                            cached = new CachedBufferData()
                            {
                                LastUsedFrame = Time.frameCount,
                                UVBuffer = uvBuffer,
                                IndexBuffer = indexBuffer
                            };
                            
                            m_cachedBuffer.Add(smrId, cached);
                        }
                        else
                        {
                            uvBuffer = cached.UVBuffer;
                            indexBuffer = cached.IndexBuffer;
                            cached.LastUsedFrame = Time.frameCount;
                        }

                        int indexCount = (int)smr.sharedMesh.GetIndexCount(0);
                        var matrix = smr.rootBone.localToWorldMatrix;
                        
                        // shareMaterial.SetBuffer(SHADER_ID_VERTEXBUFFER, skinnedVertexBuffer);
                        // shareMaterial.SetBuffer(SHADER_ID_UVBUFFER, uvBuffer);
                        // shareMaterial.SetVector(SHADER_ID_FUROFFSET, new Vector4(inter, skinnedVertexBuffer.stride, uvBuffer.stride, 0));

                        tmpBuffer.Enqueue(skinnedVertexBuffer);

                        //in C# instancingCount = modifyedPassCount - 1
                        //in Shader InstancingFurOffset = _InstancingFUR_OFFSET * (instancingId + 1)
                        //beacuse when instanceId == 0, the SV_Position will be 0(NaN)
                        int instancingCount = modifyedPassCount - 1;
                        shareMaterial.SetBuffer(SHADER_ID_VERTEXBUFFER, skinnedVertexBuffer);
                        shareMaterial.SetBuffer(SHADER_ID_UVBUFFER, uvBuffer);
                        shareMaterial.SetVector(SHADER_ID_FUROFFSET, new Vector4(inter, skinnedVertexBuffer.stride, uvBuffer.stride, 0));
                        // cmdBuffer.SetGlobalBuffer(SHADER_ID_VERTEXBUFFER, skinnedVertexBuffer);
                        // cmdBuffer.SetGlobalBuffer(SHADER_ID_UVBUFFER, uvBuffer);
                        // cmdBuffer.SetGlobalVector(SHADER_ID_FUROFFSET, new Vector4(inter, skinnedVertexBuffer.stride, uvBuffer.stride, 0));
                        cmdBuffer.DrawProcedural(indexBuffer, matrix, shareMaterial, m_instancingPassIdx, MeshTopology.Triangles, indexCount, instancingCount);
                    }
                    context.ExecuteCommandBuffer(cmdBuffer);

                    while (tmpBuffer.Count > 0)
                    {
                        tmpBuffer.Dequeue().Release();
                    }
                }
                else
                {
                    DrawingSettings drawingSetting = CreateDrawingSettings(shaderTagId, ref renderingData,
                        renderingData.cameraData.defaultOpaqueSortFlags);
                    FilteringSettings filteringSettings = new FilteringSettings(RenderQueueRange.all, -1);
                    
                    for (int i = 1; i < modifyedPassCount; i++)
                    {
                        cmdBuffer.SetGlobalFloat("_FUR_OFFSET", i * inter); //由内而外由 0-1
                        context.ExecuteCommandBuffer(cmdBuffer);
                        cmdBuffer.Clear();
                        context.DrawRenderers(renderingData.cullResults, ref drawingSetting, ref filteringSettings);
                    }
                    
                    context.ExecuteCommandBuffer(cmdBuffer);
                }
            }

           
            /// Cleanup any allocated resources that were created during the execution of this render pass.
            public override void OnCameraCleanup(CommandBuffer cmd)
            {
                cmdBuffer.Release();
                
                foreach (var cached in m_cachedBuffer)
                {
                    if (cached.Value.LastUsedFrame != Time.frameCount)
                    {
                        cached.Value.ReleaseBuffer();
                        m_willRemoveKeys.Enqueue(cached.Key);
                    }
                }

                while (m_willRemoveKeys.Count > 0)
                {
                    m_cachedBuffer.Remove(m_willRemoveKeys.Dequeue());
                }
            }
        }


        
        
        /// <summary>
        /// Render Feature
        /// </summary>

        [System.Serializable]
        public class Settings {
            [Range(0,20)]
            public int PassCount = 10;
            public RenderPassEvent RenderPassEvent = RenderPassEvent.AfterRenderingOpaques;
        }
        
        
        [SerializeField]
        private Settings settings = new Settings();
        
        private FurRenderPass m_ScriptablePass;

        
        
        public override void Create()
        {
            this.m_ScriptablePass = new FurRenderPass(settings.PassCount);
            m_ScriptablePass.renderPassEvent = settings.RenderPassEvent;
        }

        public override void SetActive(bool active)
        {
            m_ScriptablePass?.ClearCached();
            base.SetActive(active);
        }

        protected override void Dispose(bool disposing)
        {
            m_ScriptablePass?.ClearCached();
            base.Dispose(disposing);
        }

        // Here you can inject one or multiple render passes in the renderer.
        // This method is called when setting up the renderer once per-camera.
        public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
        {
            renderer.EnqueuePass(m_ScriptablePass);
        }

    }
}