using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Sultan.Game;

namespace Sultan.Test
{
    public class DistanceAndHeightFogPass : ScriptableRenderPass
    {
        private Shader shader;
        private Material passMaterial;
        private FogAsset fogAsset;
        private RenderTargetIdentifier srcCameraColor;
        private RenderTargetIdentifier srcColorAtt;
        private RenderTargetHandle tempRenderTexture;

        private CommandBuffer cmdBuffer;

        private float offsetByCameraHeight;
        private Texture2D fogGradientTexture;
        private Texture2D fogNoiseTexture;
        private Vector4 dhMinMax;
        private Vector4 dhOffsetNoise;
        
        // float4 _DHMinMax; //x: _DMin  y:_DMax  z:_HMin  w:_HMax
        // float4 _DHOffsetNoise; //x: _DistanceOffset  y:_HeightOffset  z:_NoiseTilling  w:_NoiseAlpha
        
        private static readonly int GradientTextureID = Shader.PropertyToID("_FogGradientTexture");
        private static readonly int NoiseTextureID = Shader.PropertyToID("_FogNoiseTexture");
        private static readonly int OffsetByCameraHeightID = Shader.PropertyToID("_OffsetByCameraHeight");
        private static readonly int DHMinMaxID = Shader.PropertyToID("_DHMinMax");
        private static readonly int DHOffsetNoiseID = Shader.PropertyToID("_DHOffsetNoise");

        const string m_ProfilerTag = "DistanceAndHeightFog Pass";
        private static readonly ProfilingSampler m_ProfilingSampler = new ProfilingSampler(m_ProfilerTag);

        public DistanceAndHeightFogPass(FogAsset fogAsset) : base()
        {
            this.fogAsset = fogAsset;
            tempRenderTexture.Init("_TempRenderTexture");

            //将FogAsset的值传进来
            offsetByCameraHeight = fogAsset.OffsetByCameraHeight;
            fogGradientTexture = fogAsset.GradientTexture;
            fogNoiseTexture = fogAsset.NoiseTexture;
            dhMinMax.x = fogAsset.DistanceMin;
            dhMinMax.y = fogAsset.DistanceMax;
            dhMinMax.z = fogAsset.HeightMin;
            dhMinMax.w = fogAsset.HeightMax;

            dhOffsetNoise.x = fogAsset.DistanceOffset;
            dhOffsetNoise.y = fogAsset.HeightOffset;
            dhOffsetNoise.z = fogAsset.NoiseTilling;
            dhOffsetNoise.w = fogAsset.NoiseAlpha;

            shader = Shader.Find("Hidden/DistanceAndHeightFog");
            if (shader != null)
                passMaterial = CoreUtils.CreateEngineMaterial(shader);
        }

        public override void OnCameraSetup(CommandBuffer cmd, ref RenderingData renderingData)
        {
            //获取当前相机渲染目标
            srcCameraColor = renderingData.cameraData.renderer.cameraColorTarget;

            //申请一个cmd
            cmdBuffer = CommandBufferPool.Get("DistanceAndHeightFogFeature");
            //通过目标相机获取到描述信息
            var cameraTextureDesc = renderingData.cameraData.cameraTargetDescriptor;
            cameraTextureDesc.depthBufferBits = 0;
            cameraTextureDesc.msaaSamples = 1;
            //根据描述信息创建临时RT
            cmdBuffer.GetTemporaryRT(tempRenderTexture.id, cameraTextureDesc, FilterMode.Bilinear);
            
            this.ConfigureInput(ScriptableRenderPassInput.Depth);
        }

        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            if (fogAsset == null)
            {
                return;
            }
            else
            {
                //当雾资源的开关是开启状态并且是 High Quality
                if (QualitySettings.GetQualityLevel() == 2)
                {
                    // fogAsset.FogEnable = false;
                    using (new ProfilingScope(cmdBuffer, m_ProfilingSampler))
                    {
                        //把传入的FogAsset上的值给到shader
                        passMaterial.SetFloat(OffsetByCameraHeightID, offsetByCameraHeight);
                        passMaterial.SetTexture(GradientTextureID, fogGradientTexture);
                        passMaterial.SetTexture(NoiseTextureID, fogNoiseTexture);
                        passMaterial.SetVector(DHMinMaxID, dhMinMax);
                        passMaterial.SetVector(DHOffsetNoiseID, dhOffsetNoise);


                        Blit(cmdBuffer, tempRenderTexture.Identifier(), srcCameraColor, passMaterial, 0);
                        // Blit(cmdBuffer, tempRenderTexture.Identifier(), srcCameraColor);
                    }
                    context.ExecuteCommandBuffer(cmdBuffer);
                }
            }
        }

        public override void OnCameraCleanup(CommandBuffer cmd)
        {
            cmdBuffer.ReleaseTemporaryRT(tempRenderTexture.id);
            cmdBuffer.Release();
        }
    }
}