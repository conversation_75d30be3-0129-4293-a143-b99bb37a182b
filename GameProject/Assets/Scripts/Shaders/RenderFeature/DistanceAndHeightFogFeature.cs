using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Sultan.Manager;
using Sultan.Game;

namespace Sultan.Test
{
    public class DistanceAndHeightFogFeature : ScriptableRendererFeature
    {
        
        public RenderPassEvent Event = RenderPassEvent.BeforeRenderingPostProcessing;


        [Tooltip("当前场景使用的美术配置")]
        public SceneConfigProfile CurrentSceneConfigProfile = null;

        DistanceAndHeightFogPass m_ScriptablePass;


        
        public override void Create()
        {
            
            var CurrentFogAsset = CurrentSceneConfigProfile.fogAsset;
            // _material.SetFloat(desaturateID,Settings.Desaturate);
            m_ScriptablePass = new DistanceAndHeightFogPass(CurrentFogAsset);
            // Configures where the render pass should be injected.
            m_ScriptablePass.renderPassEvent = Event;
        }

        // Here you can inject one or multiple render passes in the renderer.
        // This method is called when setting up the renderer once per-camera.
        public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
        {
            renderer.EnqueuePass(m_ScriptablePass);
        }
    }
}
