using UnityEngine;
using UnityEngine.Rendering;

//挂到雾的父级上面

namespace Sultan.Test
{
    public class CityFogRTCamera : MonoBehaviour
    {
        private Camera orthoCamera;
        private RenderTexture renderTexture;
        private int RenderTexSize = 50;
        private Material targetMaterial;

        private string renderTextureName = "FogAreaTexture_RT";
        private static readonly int fogAreaTexID = Shader.PropertyToID("_AreaMaskTex"); // Shader中的变量名


        void Start()
        {
            // 动态创建正交相机
            orthoCamera = CreateOrthographicCamera(new Vector3(0f,0f,0f), new Vector3(90f, 180f, 0f), 50f);

            orthoCamera.cullingMask = 1 << LayerMask.NameToLayer("FogRT");

            // 创建并分配RenderTexture
            CreateAndAssignRenderTexture(RenderTexSize, RenderTexSize);
        }

        void OnDisable()
        {
            DestroyCameraAndTexture();
        }

        private Camera CreateOrthographicCamera(Vector3 position, Vector3 rotation, float size)
        {
            GameObject cameraGO = new GameObject("CityFogRTCamera");
            cameraGO.transform.parent = transform;
            cameraGO.transform.localPosition = position;
            cameraGO.transform.localEulerAngles = rotation;

            Camera camera = cameraGO.AddComponent<Camera>();
            camera.orthographic = true;
            camera.orthographicSize = size;
            camera.clearFlags = CameraClearFlags.SolidColor;
            camera.backgroundColor = Color.black;
            camera.farClipPlane = 40.0f;
            return camera;
        }

        private void CreateAndAssignRenderTexture(int width, int height)
        {
            renderTexture = new RenderTexture(width, height, 16);
            renderTexture.name = renderTextureName;
            orthoCamera.targetTexture = renderTexture;

            // 将RenderTexture赋值给Shader变量
            targetMaterial = GetComponentInChildren<Renderer>().sharedMaterial;
            targetMaterial.SetTexture(fogAreaTexID, renderTexture);
        }

        private void DestroyCameraAndTexture()
        {
            if (orthoCamera != null)
            {
                UnityEngine.Object.Destroy(orthoCamera.gameObject);
                orthoCamera = null;
            }

            if (renderTexture != null)
            {
                UnityEngine.Object.Destroy(renderTexture);
                renderTexture = null;
            }
        }
    }
}