using System.Collections;
using System.Collections.Generic;
using Sirenix.Utilities;
using Unity.Transforms;
using UnityEngine;
using System;

namespace Sultan.Test
{
    public enum Area
    {
        Inner,
        Outer
    }
    // [ExecuteAlways]
    public class SetFogMaterialProps : MonoBehaviour
    {
        [Header("触发解锁功能")]public bool UnlockArea = false;
        [Header("解锁的速度")]public float UnlockSpeed = 0.5f;
        [Header("解锁区迭代值")]public float StartArea = 0.0f;
        public Action onDisappearCompleted;
        
        private float _timeInterpolator = 0.0f;
        private float _timePercent = 0.0f;

        private float startValue;
        private float iterValue = 0.09f;

        private Material _mat;
        private string _propName;


        private float _areaMask = 0.0f; // updata ++ interValue


        private static readonly int fogAreaID = Shader.PropertyToID("_AreaMask");

        Renderer[] allRenderer;
        private Renderer r;

        void Awake()
        {
            allRenderer = transform.GetComponentsInChildren<Renderer>();
        }

        private void OnDestroy()
        {
            allRenderer = null;
        }

        /// <summary>
        /// 设置是否内城
        /// </summary>
        /// <param name="isInner"></param>
        public void setIsInner(bool isInner)
        {
            if (isInner)
            {
                iterValue = 0.09f;
            }
            else
            {
                iterValue = 0.3f;
            }
        }

        /// <summary>
        /// 初始化设置区域数据
        /// </summary>
        /// <param name="startArea"></param>
        public void initSetAreaData(int startArea)
        {
            StartArea = startArea;
            var allRenderCount = allRenderer.Length; 
            if (allRenderCount != 0)
            {
                startValue = StartArea * iterValue;
                for (int i = 0; i < allRenderCount; i++)
                {
                    r = allRenderer[i];

                    if (r.sharedMaterial.HasProperty(fogAreaID))
                    {
                        _areaMask = Mathf.Max(0.0f, Mathf.Min(1.0f, startValue));
                        r.sharedMaterial.SetFloat(fogAreaID, _areaMask);
                    }
                }

            }
        }

        void Update()
        {
            if (UnlockArea && allRenderer.Length != 0)
            {
                
                startValue = StartArea * iterValue;
                _timeInterpolator += Time.deltaTime * UnlockSpeed;
                _timePercent = Mathf.Min(_timeInterpolator, 1.0f);
                for (int i = 0; i < allRenderer.Length; i++)
                {
                    r = allRenderer[i];

                    if (r.sharedMaterial.HasProperty(fogAreaID))
                    {
                        _areaMask = Mathf.Lerp(startValue, startValue + iterValue, _timePercent);
                        _areaMask = Mathf.Max(0.0f, Mathf.Min(1.0f, _areaMask));
                        r.sharedMaterial.SetFloat(fogAreaID, _areaMask);
                    }
                }
                
                if (_timePercent >= 1.0f)
                {
                    UnlockArea = false;
                    StartArea += 1.0f;
                    _timeInterpolator = 0.0f;
                    onDisappearCompleted?.Invoke();
                }
            }
        }
    }
}