using System;
using UnityEngine;
using System.Collections;


public class TimeOfDayController : MonoBehaviour
{
    public enum TimeOfDay { Day, Evening, Night, Morning }
    
    [System.Serializable]
    public class TimeSettings
    {
        public Quaternion rotation;
        public Color color = Color.white;
        public float intensity = 1f;
    }

    public Light mainLight;
    

    public TimeSettings daySettings = new TimeSettings();
    public TimeSettings eveningSettings = new TimeSettings();
    public TimeSettings nightSettings = new TimeSettings();
    public TimeSettings morningSettings = new TimeSettings();
    
    [Range(0f, 4f)] 
    public float currentTime = 0f; // 0=Day, 1=Evening, 2=Night, 3=Morning, 4=Day
    [Range(0f, 1f)] 
    public float cycleSpeed = 0.2f; // 每秒钟时间变化量
    public bool autoCycle = true; // 是否自动循环
    

    [Tooltip("白天停留")][Range(0f, 50f)] 
    public float dayDuration = 10f;
    public float dayTimeIterator = 0f; 
    [Tooltip("夜晚停留")][Range(0f, 50f)] 
    public float nightDuration = 10f;
    public float nightTimeIterator = 0f;

    //记录器
    private bool isDayEnd = false;
    private bool isNightEnd = false;
    
    public bool isPaused = true;
    public bool debug = false;

    private void OnValidate()
    {
        ApplyTimeSettings(currentTime);
    }

    private void Awake()
    {
        FindMainLight();
    }

    private void Start()
    {
        ApplyTimeSettings(currentTime);
    }

    private void Update()
    {
        //表示时间在走
        if (autoCycle)
        {
            // 检查是否需要停留
            CheckForPausePoints();
            // 如果没有被暂停
            if (!isPaused)
            {
                currentTime += cycleSpeed * Time.deltaTime;
                // 循环时间
                if (currentTime >= 4f)
                {
                    currentTime = 0f;
                    //清除时间轴上的标签
                    isDayEnd = false;
                    isNightEnd = false;
                }
            }

            ApplyTimeSettings(currentTime);
        }

    }
    

    public void FindMainLight()
    {
        // 尝试查找所有方向光
        Light[] allLights = FindObjectsOfType<Light>();

        foreach (Light light in allLights)
        {
            // 优先查找标记为"MainLight"的灯光
            if (light.CompareTag("MainLight") && light.type == LightType.Directional)
            {
                mainLight = light;
                Debug.Log($"找到主灯光: {light.name} (通过标签 'MainLight')");
                return;
            }
        }

        if (mainLight == null)
        {
            autoCycle = false;
        }
    }

    // 检查是否到达停留点
    private void CheckForPausePoints()
    {
        //如果白天晚上的停留时间都是0，就设置状态为不暂停
        if (dayDuration == 0 && nightDuration == 0)
        {
            isPaused = false;
        }
        else
        {
            // 到达白天或晚上开始点 (0或2或4)时 暂停

            //停在白天
            if (!isDayEnd && currentTime > 0f && dayDuration != 0)
            {
                isPaused = true;//pause
                dayTimeIterator += cycleSpeed * Time.deltaTime;
                //迭代白天的时间
                if (dayTimeIterator >= dayDuration)
                {
                    isPaused = false; //continue
                    dayTimeIterator = 0f;
                    isDayEnd = true; //给时间轴插个小标签，表示迭代过一次
                }
            }
            //停在夜晚
            else if (!isNightEnd && currentTime >2f && nightDuration != 0 )
            {
                isPaused = true; //pause
                
                //迭代晚上的时间
                nightTimeIterator += cycleSpeed * Time.deltaTime;
                if (nightTimeIterator >= nightDuration)
                {
                    isPaused = false; //continue
                    nightTimeIterator = 0f;
                    isNightEnd = true; //给时间轴插个小标签，表示迭代过一次
                }
            }
            
        }

        
    }
    

    // 应用时间设置（根据0-4的值）
    public void ApplyTimeSettings(float time)
    {
        if (mainLight == null) return;

        // 确定当前时间段和插值比例
        float t;
        TimeSettings startSettings, endSettings;

        if (time < 1f) // Day -> Evening
        {
            t = time;
            startSettings = daySettings;
            endSettings = eveningSettings;
        }
        else if (time < 2f) // Evening -> Night
        {
            t = time - 1f;
            startSettings = eveningSettings;
            endSettings = nightSettings;
        }
        else if (time < 3f) // Night -> Morning
        {
            t = time - 2f;
            startSettings = nightSettings;
            endSettings = morningSettings;
        }
        else // Morning -> Day
        {
            t = time - 3f;
            startSettings = morningSettings;
            endSettings = daySettings;
        }

        // 插值应用设置
        mainLight.transform.rotation = Quaternion.Slerp(startSettings.rotation, endSettings.rotation, t);
        mainLight.color = Color.Lerp(startSettings.color, endSettings.color, t);
        mainLight.intensity = Mathf.Lerp(startSettings.intensity, endSettings.intensity, t);
    }

    public void ApplySettings(TimeSettings settings)
    {
        if (mainLight == null)
        {
            FindMainLight();
            if (mainLight == null) return;
        }
        
        mainLight.transform.rotation = settings.rotation;
        mainLight.color = settings.color;
        mainLight.intensity = settings.intensity;
    }

    public void RecordCurrentSettings(TimeOfDay time)
    {
        if (mainLight == null)
        {
            FindMainLight();
            if (mainLight == null) return;
        }

        var settings = GetSettings(time);
        settings.rotation = mainLight.transform.rotation;
        settings.color = mainLight.color;
        settings.intensity = mainLight.intensity;
    }

    private TimeSettings GetSettings(TimeOfDay time)
    {
        switch (time)
        {
            case TimeOfDay.Day: return daySettings;
            case TimeOfDay.Evening: return eveningSettings;
            case TimeOfDay.Night: return nightSettings;
            case TimeOfDay.Morning: return morningSettings;
            default: return daySettings;
        }
    }

    // 快速跳转到特定时间点
    public void JumpToTime(TimeOfDay time)
    {
        switch (time)
        {
            case TimeOfDay.Day:
                currentTime = 0f;
                break;
            case TimeOfDay.Evening:
                currentTime = 1f;
                break;
            case TimeOfDay.Night:
                currentTime = 2f;
                break;
            case TimeOfDay.Morning:
                currentTime = 3f;
                break;
        }
        ApplyTimeSettings(currentTime);
        isPaused = false; // 取消停留状态
    }
    
}