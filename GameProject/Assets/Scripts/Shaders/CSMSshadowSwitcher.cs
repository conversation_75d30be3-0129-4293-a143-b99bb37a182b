using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using UnityEngine.Rendering.Universal;

namespace Sultan.Test
{
    [DisallowMultipleComponent]
    public class CSMSshadowSwitcher : MonoBehaviour
    {
        //public string tooltips = "此脚本为编辑器下使用，挂给 01test 这个对象";
        [Toolt<PERSON>("Switch CSMS On Off")] public bool CSMS_Enable = true;
        private  ScriptableRendererData srd;
        private string CSMSfeatureName = "CSMSRenderFeature";
        private string m_guid = "4a8e21d5c33334b11b34a596161b9360";

        private void OnValidate()
        {
#if UNITY_EDITOR
            // string assetPath = "Assets/Settings/ForwardRenderer.asset";
            string assetPath = AssetDatabase.GUIDToAssetPath(m_guid);
            srd = AssetDatabase.LoadAssetAtPath<ScriptableRendererData>(assetPath);
            // srd = (ScriptableRendererData)GetAssetByGUID("4a8e21d5c33334b11b34a596161b9360");
            if (srd != null)
            {
                List<ScriptableRendererFeature> features = srd.rendererFeatures;
                for (int i = 0; i < features.Count; i++)
                {
                    if (features[i].name == CSMSfeatureName)
                    {
                        features[i].SetActive(CSMS_Enable);
                        Debug.Log("feature change:" + features[i].name + ",active :" + features[i].isActive);
                    }
                }
           }
#endif
        }

        /*
        public ScriptableRenderer GetScriptableRenderer()
        {
            Camera[] mainCameras =
                FindObjectsOfType<Camera>().Where(camera => camera.CompareTag("MainCamera")).ToArray();
            if (Camera.main != null && mainCameras.Length < 2)
            {
                mainCamera = Camera.main;
                UniversalAdditionalCameraData urpData = mainCamera.GetComponent<UniversalAdditionalCameraData>();
                sr = urpData.scriptableRenderer;
            }
       
            else
            {
                Debug.Log("检查:当前场景没有主相机或有多个主相机");  
            }
            return sr;
        }
        

        public void EnableFeature(ScriptableRenderer sr, string featureName, bool featureEnable)
        {
            var property = typeof(ScriptableRenderer)
                .GetProperty("rendererFeatures", BindingFlags.NonPublic | BindingFlags.Instance);
            List<ScriptableRendererFeature> features = property.GetValue(sr) as List<ScriptableRendererFeature>;
            for (int i = 0; i < features.Count; i++)
            {
                if (features[i].name == featureName)
                {
                    features[i].SetActive(featureEnable);
                    Debug.Log("feature change:" + features[i].name + ",active :" + features[i].isActive);
                }
            }

        }
        */

    }

}