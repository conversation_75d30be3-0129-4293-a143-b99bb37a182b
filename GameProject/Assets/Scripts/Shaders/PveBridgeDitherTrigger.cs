using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Sultan.Test
{
    [ExecuteInEditMode]
    public class PveBridgeDitherTrigger : MonoBehaviour
    {
        private string _ditherKeyWord = "_DITHERENABLED_ON";
        private Renderer[] _renderers;
        private Material[] _materials;
        private MaterialPropertyBlock[] _blocks;
        private static int _ditherPropID = Shader.PropertyToID("_Dithered");
        private HashSet<GameObject> _playersInside = new HashSet<GameObject>();
        

        void OnEnable()
        {
            Init();
        }

        void Init()
        {
            // 获取该物体上的渲染组件
            _renderers = transform.GetComponentsInChildren<Renderer>();
            
            if (_renderers != null)
            {
                _materials = new Material[_renderers.Length];
                _blocks = new MaterialPropertyBlock[_renderers.Length];
                //获取每个子对象的材质球 并初始化dither值
                for (int i = 0; i < _renderers.Length; i++)
                {
                    _blocks[i] = new MaterialPropertyBlock(); 
                    
                    _materials[i] = _renderers[i].sharedMaterial;
                    _materials[i].EnableKeyword(_ditherKeyWord);
                    _blocks[i].SetFloat(_ditherPropID, 0.0f);
                    _renderers[i].SetPropertyBlock(_blocks[i]);
                }
            }
        }

        // 当进入触发器时
        void OnTriggerEnter(Collider bridge)
        {
            //Debug.Log("触发器相交！" + bridge.gameObject.name + " 触发了 " + gameObject.name);
            // 检查碰撞到的对象是否为角色
            var player = bridge.gameObject;
            if (player.CompareTag("Player"))
            {
                _playersInside.Add(player);
                UpdateDitehr(_playersInside.Count > 0);
            }
        }

        // 当离开触发器时
        void OnTriggerExit(Collider bridge)
        {
            var player = bridge.gameObject;
            if (player.CompareTag("Player") && _playersInside.Contains(player) )
            {
                _playersInside.Remove(player);
                UpdateDitehr(_playersInside.Count > 0);
            }
        }

        void UpdateDitehr(bool isEnable)
        {
            for (int i = 0; i < _renderers.Length; i++)
            {
                _blocks[i].SetFloat(_ditherPropID, isEnable? 0.75f : 0.0f);
                _renderers[i].SetPropertyBlock(_blocks[i]);
            }
        }

        private void OnDisable()
        {
            _blocks = null;
        }
    }
    
}
