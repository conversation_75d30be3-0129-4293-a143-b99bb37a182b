using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[ExecuteInEditMode]
[DisallowMultipleComponent]
public class ShaderLOD : MonoBehaviour
{
    public enum Quality
    {
        High,
        Medium,
        Low
    }

    public Quality theQuality = Quality.High;

    void Start()
    {
    }

    void Update()
    {
        switch (theQuality)
        {
            case Quality.High:
                Shader.globalMaximumLOD = 300;
                break;
            case Quality.Medium:
                Shader.globalMaximumLOD = 200;
                break;
            case Quality.Low:
                Shader.globalMaximumLOD = 100;
                break;
        }
    }
}