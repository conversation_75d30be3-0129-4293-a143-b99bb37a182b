using System.Collections;
using System.Collections.Generic;
using Sirenix.Utilities;
using Unity.Transforms;
using UnityEngine;
using System;

namespace Sultan.Test
{
    // [ExecuteAlways]
    public class SetCloudMaterialProps : MonoBehaviour
    {
        [Header("是否解锁")] public bool UnlockArea;
        [Header("解锁速度")] public float UnlockSpeed = 0.4f;
        public Action onDisappearCompleted;

        private float _timeInterpolator = 0.0f;
        private float _timePercent = 0.0f;
        private bool _hadNotified;

        private Material _mat;
        private string _propName;

        private float _cloudMode; // 0:Constant  2:Circle
        private Vector4 _scaleParaX; //_ScalePara.x  cloud scale  -2 --> 2
        private Vector4 _scaleCircleParaX; //_CirclePara.x  cloud scale  0 --> 1
        public Vector4 _cloudCenter;

        private static readonly int cloudModeID = Shader.PropertyToID("_ScaleMode");
        private static readonly int cloudScaleID = Shader.PropertyToID("_ScalePara");
        private static readonly int cloudCircleScaleID = Shader.PropertyToID("_CirclePara");
        private static readonly int cloudCenterID = Shader.PropertyToID("_RootCenter");

        Renderer[] allRenderer;
        private MaterialPropertyBlock[] allblocks;


        void Awake()
        {
            allRenderer = transform.GetComponentsInChildren<Renderer>();
            allblocks = new MaterialPropertyBlock[allRenderer.Length];
            _cloudCenter = (Vector4)transform.position;
        }

        private void OnDestroy()
        {
            allRenderer = null;
            allblocks = null;
        }

        void Update()
        {
            if (!UnlockArea || allRenderer.Length == 0)
            {
                return;
            }
            else
            {
                _timeInterpolator += Time.deltaTime * UnlockSpeed;
                _timePercent = Mathf.Min(_timeInterpolator, 1.0f);

    
                for(int i = 0; i < allRenderer.Length; i++)
                {
                    MaterialPropertyBlock block = allblocks[i];
                    if (null == block)
                    {
                        block = new MaterialPropertyBlock();
                        allblocks[i] = block;
                    }
                    block.SetVector(cloudCenterID, _cloudCenter);
                    
                    Renderer r = allRenderer[i];
                    
                    _cloudMode = r.sharedMaterial.GetFloat(cloudModeID);
                    
                    if (r.sharedMaterial.HasProperty(cloudScaleID) && Math.Abs(_cloudMode) < 0.01f)
                    {
                        _scaleParaX = r.sharedMaterial.GetVector(cloudScaleID);

                        _scaleParaX.x = Mathf.Lerp(-2f, 2f, _timePercent);

                        block.SetVector(cloudScaleID, _scaleParaX);
                    }
                    else if (r.sharedMaterial.HasProperty(cloudCircleScaleID) && Math.Abs(_cloudMode - 2.0f) < 0.01f)
                    {
                        _scaleCircleParaX = r.sharedMaterial.GetVector(cloudCircleScaleID);

                        _scaleCircleParaX.x = Mathf.Lerp(0f, 1.0f, _timePercent);

                        block.SetVector(cloudCircleScaleID, _scaleCircleParaX);
                    }

                    
                    r.SetPropertyBlock(block);
                }

                if (!_hadNotified && _timePercent >= 1.0f)
                {
                    _hadNotified = true;
                    onDisappearCompleted?.Invoke();
                }
                
            }
        }
    }
}

