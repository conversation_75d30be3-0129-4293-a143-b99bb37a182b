using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityTemplateProjects.Tools.CameraCapturer;

namespace Sultan.Test
{
    [ExecuteInEditMode]
    [RequireComponent(typeof(MeshRenderer))]
    [DisallowMultipleComponent]
    public class CloudWorldToCaptureMatrix : MonoBehaviour
    {
        public CameraCaptureData ScriptableObject;

        private Material mat;
        private static readonly int WorldToCaptureCameraMatrixID = Shader.PropertyToID("_WorldToCaptureCamera");

        // void Awake()
        // {
        //     mats = GetComponentInChildren<MeshRenderer>().sharedMaterials;
        // }

        void Awake()
        {
            mat = GetComponentInChildren<MeshRenderer>().sharedMaterial;
            if (null != ScriptableObject)
            {
                mat.SetMatrix(WorldToCaptureCameraMatrixID,ScriptableObject.WorldToCaptureCamera);
            }
        }
    }
}
