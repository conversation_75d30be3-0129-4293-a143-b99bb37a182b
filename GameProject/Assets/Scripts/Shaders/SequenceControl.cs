using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Sultan.Test
{
    [ExecuteInEditMode]
    [DisallowMultipleComponent]
    public class SequenceControl : MonoBehaviour
    {
        [Header("是否为循环")]
        public bool IsLoop;
        [Header("循环开启时播放持续时间")]
        public float Duration;
        [Range(0.0f,10.0f)][Header("播放特效的速度")]
        public float Speed;


        private Renderer _renderer;
        private Material _mat;
        private MaterialPropertyBlock _block;

        private static readonly int TimeDataID = Shader.PropertyToID("_TimeData");

        private float _timeInterpolator;
        private float _timeFactor;
        private float _time;


        // void Awake()
        // {
        //     Init();
        // }

        private void OnEnable()
        {
            Init();
        }

        private void OnDisable()
        {
            _timeFactor = 0.0f;
            _timeInterpolator = 0.0f;
            _block = null;
        }

        // Update is called once per frame
        void Update()
        {
            if (_renderer != null && _mat != null)
            {
                _timeFactor += Time.deltaTime * Speed ;
                if (_timeFactor >= 1.0f)
                {
                    if (IsLoop)
                    {
                        _timeFactor -= 1.0f;
                    }
                    else
                    {
                        _time = 1.0f;
                    }
                }
                else
                {
                    _time = _timeFactor;
                }
                _block.SetFloat(TimeDataID, _time);
                _renderer.SetPropertyBlock(_block);

                _timeInterpolator += Time.deltaTime;
                if(IsLoop && _timeInterpolator >= Duration || !IsLoop && _timeFactor > 1.0f)
                {
                    gameObject.SetActive(false);
                }
                
            }
        }

        void Init()
        {
            _timeInterpolator = 0.0f;
            _timeFactor = 0.0f;
            _renderer = GetComponentInChildren<MeshRenderer>();
            if (_renderer != null)
            {
                _mat = _renderer.sharedMaterial;
            }


            if (_block == null)
            {
                _block = new MaterialPropertyBlock();
            }
        }
    }
}