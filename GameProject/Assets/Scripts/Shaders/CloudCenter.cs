using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Sultan.Test
{
    [ExecuteAlways]
    public class CloudCenter : MonoBehaviour
    {
        private static readonly int cloudCenterID = Shader.PropertyToID("_CloudCenter");
        private Vector4 cloudCenter;

        void Update()
        {
            cloudCenter = transform.position;
            Shader.SetGlobalVector(cloudCenterID,cloudCenter);
        }
    }
}