using System.Collections;
using System.Collections.Generic;
using UnityEngine;

//运行时批量给子对象添加脚本

namespace Sultan.Test
{
    public class AddFogDissolveRTMatScript : MonoBehaviour
    {

        void Awake()
        {
            AddTestScriptToAllChildren();
        }

        void AddTestScriptToAllChildren()
        {
            foreach (Transform child in this.transform)
            {
                if (child.gameObject.GetComponent<FogDissolveRTMat>() == null)
                {
                    child.gameObject.AddComponent<FogDissolveRTMat>();
                }
            }
        }
    }
}

