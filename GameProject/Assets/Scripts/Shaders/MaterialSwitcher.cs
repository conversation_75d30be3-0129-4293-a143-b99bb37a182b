using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Sultan.Test
{
    [ExecuteAlways]
    public class MaterialSwitcher : MonoBehaviour
    {
        public bool SwitchMaterial = false; // 初始状态的布尔值
        public Material materialOff; // 当bool值为false时使用的材质球
        public Material materialOn; // 当bool值为true时使用的材质球
        public float BreatheSpeed = 1.0f;
        
        
        private Renderer renderer;
        private static readonly int AlphaChannelID =
            Shader.PropertyToID("_Intensity_Opacity_DistortionIntensity_AlphaChannel");
        private Vector4 alphaChannel;
        private MaterialPropertyBlock matBlock;

        private void OnValidate()
        {
            renderer = GetComponent<Renderer>();
            UpdateMaterial();
        }

        void Awake()
        {
            renderer = GetComponent<Renderer>(); // 获取物体的Renderer组件
            UpdateMaterial();
        }

        /// <summary>
        /// 切换材质
        /// </summary>
        public void switchMat(bool flag)
        {
            if (flag != SwitchMaterial)
            {
                SwitchMaterial = flag;
                UpdateMaterial();
            }
        }


        private void Update()
        {
            if (renderer != null && SwitchMaterial && materialOn != null)
            {
                if (matBlock == null)
                {
                    matBlock = new MaterialPropertyBlock();
                }
                else
                {
                    var sinTemp = Mathf.Sin(Time.time * BreatheSpeed * 3.5f ) * 0.25f + 0.75f;
                    alphaChannel.y = sinTemp;
                    matBlock.SetVector(AlphaChannelID, alphaChannel);

                    renderer.SetPropertyBlock(matBlock);
                }
                
            
            }
        }

        private void OnDestroy()
        {
            renderer = null;
            matBlock = null;
        }

        void UpdateMaterial()
        {
            if (renderer != null)
            {
                if (materialOff == null)
                {
                    materialOff = renderer.sharedMaterial;
                }

                if (SwitchMaterial)
                {
                    alphaChannel = materialOn.GetVector(AlphaChannelID);
                    renderer.material = materialOn;
                }
                else
                {
                    renderer.material = materialOff;
                }

            }
            else
            {
                Debug.LogWarning("No Renderer component found on the object.");
            }
        }


        // void OnSomeConditionChange()
        // {
        //     switchMaterial = !switchMaterial;
        //     UpdateMaterial();
        // }
    }
}