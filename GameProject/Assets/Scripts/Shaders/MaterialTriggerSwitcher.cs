using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Sultan.Test
{
    [ExecuteAlways]
    public class MaterialTriggerSwitcher : MonoBehaviour
    {
        public Material materialTrigger; // 

        private Material materialBase; // 
        private Renderer _renderer;
        private bool isTriggered = false;
        private Collider collider;

        private void OnValidate()
        {
            if (_renderer == null)
            {
                _renderer = GetComponent<Renderer>();
            }
            else
            {
                materialBase = _renderer.sharedMaterial;
            }
            collider = GetComponent<Collider>();
        }

        void Awake()
        {
            _renderer = GetComponent<Renderer>(); // 获取物体的Renderer组件
            if (_renderer != null)
            {
               materialBase = _renderer.sharedMaterial;
            }

            collider = GetComponent<Collider>();
            if (collider == null)
            {
                Debug.LogError("GameObject must have a Collider component to detect collisions.");
            }
            else
            {
                collider.isTrigger = true;
            }
        }
        
        private void FixedUpdate()
        {
            if (!Physics.autoSimulation)
            {
                Physics.Simulate(Time.fixedDeltaTime);
            }
        }

        private void OnTriggerEnter(Collider other)
        {
            if (other.CompareTag("Player"))
            {
                _renderer.sharedMaterial = materialTrigger;
            }
            
        }

        private void OnTriggerStay(Collider other)
        {
            if (other.CompareTag("Player"))
            {
                _renderer.sharedMaterial = materialTrigger;
            }
        }

        private void OnTriggerExit(Collider other)
        {
            if (other.CompareTag("Player"))
            {
                _renderer.sharedMaterial = materialBase;
            }
            
        }
#if UNITY_EDITOR
        // 编辑模式下可视化Trigger区域
        private void OnDrawGizmos()
        {
            if (collider == null) return;
            Gizmos.color = new Color(0f,1f,0f,0.15f);
            Gizmos.DrawCube(transform.position + collider.bounds.center, collider.bounds.size);
        }
#endif
        
    }
}