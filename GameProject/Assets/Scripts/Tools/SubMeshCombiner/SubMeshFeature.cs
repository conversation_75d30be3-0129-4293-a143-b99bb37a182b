using System.Collections.Generic;
using Sultan.Core;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.RendererUtils;
using UnityEngine.Rendering.Universal;

public class SubMeshFeature : ScriptableRendererFeature
{  
    public int InstancingCount = 1023;
    public float Interval = 0.1f;
    public float Rotation = 63;
    public float Scale = 1;
    public int DrawMeshIndex = 0;
    public int RepeatDraw = 1;

    public Mesh DrawMesh;
    public Material DrawMat;
    public Mesh DrawMesh1;
    public Material DrawMat1;
    
    class CustomRenderPass : ScriptableRenderPass
    {
        private Matrix4x4[] DrawMatrix;
        public Mesh DrawMesh;
        public Material DrawMat;
        public Mesh DrawMesh1;
        public Material DrawMat1;
        public int DrawMeshIndex = 0;
        public int Repeat;

        MaterialPropertyBlock mbp = new MaterialPropertyBlock();
        public CustomRenderPass(Mesh mesh, Material material, Mesh mesh1, Material material1, int drawIndex, int instanceingCount, float interval, float rotation, float scale, int repeat)
        {
            DrawMesh = mesh;
            DrawMat = material;
            DrawMesh1 = mesh1;
            DrawMat1 = material1;
            DrawMeshIndex = drawIndex;
            Repeat = repeat;
            
            int w = (int)Mathf.Ceil(Mathf.Sqrt(instanceingCount));
            Vector3 selfPos = Vector3.zero;
            List<Matrix4x4> tmpMatrixList = new List<Matrix4x4>();
            for (int i = 0; i < w; i++)
            {
                for (int j = 0; j < w; j++)
                {
                    Vector3 pos = new Vector3(i * interval, 0, j * interval);
                    pos += selfPos;

                    if (tmpMatrixList.Count >= 1023)
                    {
                        continue;
                    }
                    Matrix4x4 mvp = Matrix4x4.TRS(pos, Quaternion.Euler(new Vector3(0, rotation, 0)), Vector3.one * scale);
                    tmpMatrixList.Add(mvp);
                }
            }
            DrawMatrix = tmpMatrixList.ToArray();
            
            mbp.SetColor("_EmissionColor", Color.red);
        }
        // This method is called before executing the render pass.
        // It can be used to configure render targets and their clear state. Also to create temporary render target textures.
        // When empty this render pass will render to the active camera render target.
        // You should never call CommandBuffer.SetRenderTarget. Instead call <c>ConfigureTarget</c> and <c>ConfigureClear</c>.
        // The render pipeline will ensure target setup and clearing happens in a performant manner.
        public override void OnCameraSetup(CommandBuffer cmd, ref RenderingData renderingData)
        {
        }

        // Here you can implement the rendering logic.
        // Use <c>ScriptableRenderContext</c> to issue drawing commands or execute command buffers
        // https://docs.unity3d.com/ScriptReference/Rendering.ScriptableRenderContext.html
        // You don't have to call ScriptableRenderContext.submit, the render pipeline will call it at specific points in the pipeline.
        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            if (null != DrawMatrix && DrawMesh && DrawMat)
            {
                if (DrawMeshIndex > 0)
                {
                    CommandBuffer cmd = CommandBufferPool.Get("Fuck");
                   
                    //
                    for (int d = 0; d < Repeat; d++)
                    {
                        for (int i = 0; i <= DrawMeshIndex; i++)
                        {
                            cmd.DrawMeshInstanced(DrawMesh, i, DrawMat, 0, DrawMatrix, DrawMatrix.Length);
                        }
                    }
                    context.ExecuteCommandBuffer(cmd);
                    CommandBufferPool.Release(cmd);
                }
                else
                {
                    if (DrawMesh && DrawMesh1)
                    {
                        CommandBuffer cmd = CommandBufferPool.Get("Fuck");
                        for (int d = 0; d < Repeat; d++)
                        {
                            cmd.DrawMeshInstanced(DrawMesh, 0, DrawMat, 0, DrawMatrix, DrawMatrix.Length, mbp);
                            cmd.DrawMeshInstanced(DrawMesh1, 0, DrawMat1, 0, DrawMatrix, DrawMatrix.Length, mbp);
                        }
                        context.ExecuteCommandBuffer(cmd);
                        CommandBufferPool.Release(cmd);
                    }
                    
                }
            }
        }

        // Cleanup any allocated resources that were created during the execution of this render pass.
        public override void OnCameraCleanup(CommandBuffer cmd)
        {
        }
    }

    CustomRenderPass m_ScriptablePass;

    /// <inheritdoc/>
    public override void Create()
    {
        m_ScriptablePass = new CustomRenderPass(DrawMesh, DrawMat,  DrawMesh1, DrawMat1,DrawMeshIndex, InstancingCount, Interval, Rotation, Scale, RepeatDraw);

        // Configures where the render pass should be injected.
        m_ScriptablePass.renderPassEvent = RenderPassEvent.AfterRenderingOpaques;
    }

    // Here you can inject one or multiple render passes in the renderer.
    // This method is called when setting up the renderer once per-camera.
    public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
    {
        renderer.EnqueuePass(m_ScriptablePass);
    }
}


