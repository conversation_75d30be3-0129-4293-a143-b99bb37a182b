using System.Reflection;
using UnityEngine;
using UnityEngine.Rendering;
using System.Runtime.InteropServices;
using XLua;


/// <summary>
/// Author:Aoicocoon
/// Date:20230721
/// </summary>

[BlackList]
public class MetalFrameCapturer : MonoBehaviour
{
	public bool NeedCapture = false;
	public int CaptureNumFrame = 1;

	#if DISABLE_UWA_SDK
#if (UNITY_IOS || UNITY_TVOS || UNITY_WEBGL) && !UNITY_EDITOR
	[DllImport ("__Internal")]
#else
	[DllImport("MetalFrameCapturer")]
#endif
	private static extern void FrameCaptureOnBeforeRendering();

#if (UNITY_IOS || UNITY_TVOS || UNITY_WEBGL) && !UNITY_EDITOR
	[DllImport ("__Internal")]
#else
	[DllImport("MetalFrameCapturer")]
#endif
	private static extern void FrameCaptureOnAfterRendering();

#if UNITY_WEBGL && !UNITY_EDITOR
	[DllImport ("__Internal")]
	private static extern void RegisterPlugin();
#endif
	
	#endif

	int m_CaptureAtFrame = -1;
	private bool m_HasStartCapture = false;

	void WriteBeginLogMsg(ScriptableRenderContext context, Camera[] camera)
    {
#if DISABLE_UWA_SDK
		if (m_HasStartCapture)
		{
			if((Time.frameCount - m_CaptureAtFrame) >= CaptureNumFrame)
            {
	            FrameCaptureOnAfterRendering();
	            RenderPipelineManager.beginFrameRendering -= WriteBeginLogMsg;
				m_HasStartCapture = false;
			}
		}

		if (NeedCapture && !m_HasStartCapture)
        {
	        m_CaptureAtFrame = Time.frameCount;
			m_HasStartCapture = true;
			NeedCapture = false;
			FrameCaptureOnBeforeRendering();
        }
#endif
    }
    

	//call form outside
	//param:captureFrameCount  capture frame count
	public void StartCapture(int captureFrameCount)
    {
		NeedCapture = true;
		m_HasStartCapture = false;
		CaptureNumFrame = captureFrameCount;
		RenderPipelineManager.beginFrameRendering += WriteBeginLogMsg;
    }
	
#if UNITY_EDITOR_OSX && DISABLE_UWA_SDK
	[BlackList]
	[UnityEditor.Callbacks.PostProcessBuild]
	public static void RequireInfoPlist(UnityEditor.BuildTarget target, string buildPath)
	{
		if(target == UnityEditor.BuildTarget.iOS && UnityEditor.EditorUserBuildSettings.development == true)
		{
			if(UnityEditor.BuildPipeline.IsBuildTargetSupported(UnityEditor.BuildTargetGroup.iOS, UnityEditor.BuildTarget.iOS))
			{
				// Get plist file and read it.
				string plistPath = buildPath + "/Info.plist";
				Debug.Log("In the ChangeXCodePlist, path is: " + plistPath);
				
				// UnityEditor.iOS.Xcode.PlistDocument plist = new UnityEditor.iOS.Xcode.PlistDocument();
				// plist.ReadFromString(System.IO.File.ReadAllText(plistPath));
				//
				// Debug.Log("In the ChangeXCodePlist");
				//
				//

				// string plistPath = buildPath + "/Info.plist";
				// Debug.Log("In the ChangeXCodePlist, path is: " + plistPath);
				// UnityEditor.iOS.Xcode.PlistDocument plist = new UnityEditor.iOS.Xcode.PlistDocument();
				// plist.ReadFromString(System.IO.File.ReadAllText(plistPath));
				// Debug.Log("In the ChangeXCodePlist");
				//

				// // Get root
				// UnityEditor.iOS.Xcode.PlistElementDict rootDict = plist.root;
				// rootDict.SetBoolean("UIFileSharingEnabled", true);
				// rootDict.SetBoolean("MetalCaptureEnabled", true);
				//
				// System.IO.File.WriteAllText(plistPath, plist.WriteToString());

				
				///Users/<USER>/Documents/AOW/AOW_UNITY/Build/GameProject/Info.plist

				//Use reflection to create pList, because some developers do not install the IOS Build Target, can not find IOS namespace
				
				string method= "ReadFromString";
				string method1= "SetBoolean";
				string method2= "WriteToString";
				
				var xcodeAssembly = System.Reflection.Assembly.Load("UnityEditor.iOS.Extensions.Xcode");

				Debug.Log("1. Load Xcode Assembly");
				
				if (null != xcodeAssembly)
				{
					var myClassType = xcodeAssembly.GetType("UnityEditor.iOS.Xcode.PlistDocument");
					
					Debug.Log("2. Load PlistDocument");

					object rootInstance = myClassType == null ? null : System.Activator.CreateInstance(myClassType); //Check if exists, instantiate if so.
					var writeStringMethod = myClassType.GetMethod(method2);

					if (null != rootInstance)
					{
						Debug.Log("3. Get ReadFromString metrhod" + (myClassType.GetMethod(method) != null ? "true" : "false"));
						string pListData = System.IO.File.ReadAllText(plistPath);
						Debug.Log("Data:" + pListData);
						myClassType.GetMethod(method)?.Invoke(rootInstance, new object[]{pListData});
					}
				
					// Get root
					object elementInstance = myClassType.GetField("root", BindingFlags.Public | BindingFlags.Instance).GetValue(rootInstance);
					myClassType = xcodeAssembly.GetType("UnityEditor.iOS.Xcode.PlistElementDict");
					var setBooleanMethod = myClassType.GetMethod(method1);
					
					if (null != elementInstance)
					{
						Debug.Log("4. Load PlistElementDict");
						Debug.Log("5. Get SetBoolean metrhod" + (myClassType.GetMethod(method1) != null ? "true" : "false"));
						Debug.Log("6. Get WriteToString metrhod" + (writeStringMethod != null ? "true" : "false"));
						
						setBooleanMethod?.Invoke(elementInstance, new object[] {"UIFileSharingEnabled", true });
						setBooleanMethod?.Invoke(elementInstance, new object[] {"MetalCaptureEnabled", true });

						if (null != writeStringMethod)
						{
							string ret = writeStringMethod?.Invoke(rootInstance, null) as string;
							System.IO.File.WriteAllText(plistPath, ret);
							
							Debug.Log("7. Write Custom info.plist done:" + ret);
						}
					}
				}
			}
		}
	}
#endif
}
