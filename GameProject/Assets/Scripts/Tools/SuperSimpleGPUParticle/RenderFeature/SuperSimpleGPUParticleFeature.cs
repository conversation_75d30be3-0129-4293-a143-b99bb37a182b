using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class SuperSimpleGPUParticleFeature : ScriptableRendererFeature
{
    class UpdateParticlePass : ScriptableRenderPass
    {
        public CommandBuffer ExecCMD;
        // This method is called before executing the render pass.
        // It can be used to configure render targets and their clear state. Also to create temporary render target textures.
        // When empty this render pass will render to the active camera render target.
        // You should never call CommandBuffer.SetRenderTarget. Instead call <c>ConfigureTarget</c> and <c>ConfigureClear</c>.
        // The render pipeline will ensure target setup and clearing happens in a performant manner.
        public override void OnCameraSetup(CommandBuffer cmd, ref RenderingData renderingData)
        {
        }

        // Here you can implement the rendering logic.
        // Use <c>ScriptableRenderContext</c> to issue drawing commands or execute command buffers
        // https://docs.unity3d.com/ScriptReference/Rendering.ScriptableRenderContext.html
        // You don't have to call ScriptableRenderContext.submit, the render pipeline will call it at specific points in the pipeline.
        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            context.ExecuteCommandBuffer(ExecCMD);
        }

        // Cleanup any allocated resources that were created during the execution of this render pass.
        public override void OnCameraCleanup(CommandBuffer cmd)
        {
        }
    }
    
    class RenderParticlePass : UpdateParticlePass
    {
       
    }

    UpdateParticlePass m_UpdatePass;
    RenderParticlePass m_RenderPass;

    /// <inheritdoc/>
    public override void Create()
    {
       

       
    }

    public void Setup(CommandBuffer updateCMD, CommandBuffer renderCMD)
    {
        if (null == m_UpdatePass)
        {
            m_UpdatePass = new UpdateParticlePass();
            m_UpdatePass.renderPassEvent = RenderPassEvent.AfterRenderingShadows;
        }

        if (null == m_RenderPass)
        {
            m_RenderPass = new RenderParticlePass();
            m_RenderPass.renderPassEvent = RenderPassEvent.AfterRenderingOpaques;
        }
        
        m_UpdatePass.ExecCMD = updateCMD;
        m_RenderPass.ExecCMD = renderCMD;
    }

    // Here you can inject one or multiple render passes in the renderer.
    // This method is called when setting up the renderer once per-camera.
    public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
    {
        renderer.EnqueuePass(m_UpdatePass);
        renderer.EnqueuePass(m_RenderPass);
    }
}


