Shader "Unlit/UpdateParticleShader"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
    }
    
    SubShader
    {
        Pass
        {
            Tags
            {
                "RenderType"="Opaque"
                "Queue"="Opaque"
            }
            
            HLSLPROGRAM
            #pragma vertex Vert
            #pragma fragment Frag
            #pragma enable_d3d11_debug_symbols

            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            struct Varyings
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
            };

            struct Attributes
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            TEXTURE2D(_GPUParticle);
            SAMPLER(sampler_GPUParticle);

            CBUFFER_START(UnityPerMaterial)
            float _Dt;
            float4 _ParticleDirection;
            float2 _MinMaxSpeed;
            float _LifeTime;
            float _EmitterDistance;
            float _MoveRandomWeight;
            float _ParticleDiePos;
            float4 _GPUParticlePosDiff;
            CBUFFER_END
            
            Varyings Vert(Attributes v)
            {
                Varyings o;
                o.pos = TransformObjectToHClip(v.vertex);
                o.uv = v.uv;
                return o;
            }

            //https://gist.github.com/patriciogonzalezvivo/670c22f3966e662d2f83?permalink_comment_id=2351862
            float GetPerlinNoise(float2 uv)
            {
                return frac(sin(dot(uv, float2(12.9898, 78.233))) * 43758.5453);
            }

            float3 CalcParticleDirection(float2 uv)
            {
                float3 velocity = float3(
                    GetPerlinNoise(uv + float2(1,1)),
                    GetPerlinNoise(uv + float2(2,2)),
                    GetPerlinNoise(uv + float2(3,3))
                    );

                //convert perlinnoise [0-1] -> [-1,1]
                velocity = velocity * 2.0 - 1.0;
                
                velocity = lerp(_ParticleDirection, velocity, _MoveRandomWeight);
                
                velocity = normalize(velocity) * lerp(_MinMaxSpeed.x, _MinMaxSpeed.y, GetPerlinNoise(uv));

                return velocity;
            }

            float4 Frag(Varyings i) : SV_Target
            {
                float4 p = SAMPLE_TEXTURE2D(_GPUParticle, sampler_GPUParticle, i.uv);
                if (p.w > 0)
                {
                    p.xyz += CalcParticleDirection(i.uv) * _Dt;
                    p.xyz += _GPUParticlePosDiff;
                    p.w -= _Dt;
                    return p;
                }

                //particle die here
                float respawnX = GetPerlinNoise(i.uv + float2(1,1)) * 2 - 1;
                float respawnY = GetPerlinNoise(i.uv + float2(2,2));
                float respawnZ = GetPerlinNoise(i.uv + float2(3,3)) * 2 - 1;

                float rndLife = _LifeTime * GetPerlinNoise(i.uv + _Time.xx);
                
                p = float4(respawnX, respawnY, respawnZ, rndLife);
                p.xyz += _GPUParticlePosDiff;
                p *= _EmitterDistance;

                return p;
            }
                    
            ENDHLSL
        }
    }
}
