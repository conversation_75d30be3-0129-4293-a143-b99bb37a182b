Shader "Unlit/RenderParticleShader"
{
    Properties
    {
        _MainTex ("Texture", 2DArray) = "white" {}
        _RotateSpeed("Rotate Speed",Range(1,50)) = 2
    }
    SubShader
    {
        Tags { "IgnoreProjector" = "True" "RenderPipeline" = "UniversalPipeline" }
        LOD 100
        Cull Back
        
        Pass
        {
            Tags
            {
                "RenderType"="Opaque"
                "Queue"="AlphaTest"
            }
            
            HLSLPROGRAM
            #pragma vertex Vert
            #pragma fragment Frag
            //#pragma enable_d3d11_debug_symbols

            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            
            struct Attributes
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 positionOS : TEXCOORD1;
                uint random : TEXCOORD2;
                // float3 positionWS : TEXCOORD1;
            };


            CBUFFER_START(UnityPerMaterial)
            float4 _GPUParticleOutput_TexelSize;
            float4 _GPUParticleColor;
            float _GPUParticleSize;
            half _RotateSpeed;
            // float4 _MainTex_ST;
            CBUFFER_END
            int LOWQuality_LinearMode;

            // TEXTURE2D(_MainTex);
            TEXTURE2D_ARRAY(_MainTex);
            SAMPLER(sampler_MainTex);
            
            TEXTURE2D(_GPUParticleOutput);
            SAMPLER(sampler_GPUParticleOutput);


            float4 GetBillboardPositionCS(float3 positionOS, float3 center)
            {
                float3 cameraPosWS = GetCameraPositionWS();
                float3 cameraPosOS = TransformWorldToObject(cameraPosWS);
            
                float3 normalDir = cameraPosOS - center;
                normalDir = normalize(normalDir);
                float3 upDir = abs(normalDir.y) > 0.999 ? float3(0, 0, 1) : float3(0, 1, 0);
                float3 rightDir = normalize(cross(upDir, normalDir));
                upDir = normalize(cross(normalDir, rightDir));
                float3 centerOffs = positionOS - center;
                float3 localPos = center + rightDir * centerOffs.x + upDir * centerOffs.y + normalDir * centerOffs.z;
                float4 positionCS = TransformObjectToHClip(localPos);
                return positionCS;
            }

            Varyings Vert(Attributes v)
            {
                Varyings o;

                o.positionOS = v.vertex;
                
                float2 GPUParticleUV = v.uv.xy + _GPUParticleOutput_TexelSize.xy * 0.5;
                float4 positionCenter = SAMPLE_TEXTURE2D_LOD(_GPUParticleOutput, sampler_GPUParticleOutput, GPUParticleUV, 0);
                float4 vos = v.vertex * _GPUParticleSize; //缩放
                vos.xyz += positionCenter.xyz;

                vos = GetBillboardPositionCS(vos, positionCenter);
                
                o.positionCS = vos;

                float rotationSpeed = max(0.2,v.uv.x) * _RotateSpeed * _Time.y ; //uv.xy is a constant with random 0--1 pre particle
                float sinX = sin ( rotationSpeed );
			    float cosX = cos ( rotationSpeed );
			    float2x2 rotationMatrix = float2x2( cosX, -sinX, sinX, cosX);

                float2 rotUV = mul(v.vertex.xy, rotationMatrix);
                rotUV += 0.5;

                o.uv.xy = rotUV;
                
                uint random = floor(v.uv.x * 4); //
                o.random = random;
                
                return o;
            }

            float4 Frag(Varyings i) : SV_Target
            {
                half4 c;
                half4 mainTex = SAMPLE_TEXTURE2D_ARRAY(_MainTex, sampler_MainTex, i.uv.xy, i.random);
                c.rgb = saturate(mainTex.rgb * _GPUParticleColor.rgb);

                half dis = length(i.positionOS.xy-half2(0,0));
                half circleAlpha = step(dis,0.5);
                
                //save shader variant
                c.rgb = lerp(c.rgb * 4, c.rgb, LOWQuality_LinearMode);
                c.a = saturate(mainTex.a * circleAlpha);
                clip(c.a-0.5);
                

                return c;
            }
            ENDHLSL
        }
    }
}
