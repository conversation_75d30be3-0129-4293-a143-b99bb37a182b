//Author:Aoicocoon
//Date:20230905

using System;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.SceneManagement;

namespace UnityTemplateProjects.Tools.CameraCapturer
{
    public class CameraCapturer : MonoBehaviour
    {
#if UNITY_EDITOR
        public enum CaptureType
        {
            Color,
            Depth
        }

        [Serialize<PERSON><PERSON>, Header("1.选择抓取Color/Depth")]
        public CaptureType UsedCaptureType = CaptureType.Color;

        [SerializeField, Header("2.选择纹理尺寸")] public Vector2 RenderTextureSize = Vector2.zero;

        [Header("3.选择要捕捉的相机")] public Camera TargetCamera;

        [Header("4.点击捕捉")] public bool StartCapture;

        [SerializeField] public Shader BlitShader;

        public Texture2D Output;
        public string OutputPath;

        private Camera m_captureCamera;
        private RenderTexture m_renderTarget;
        private RenderTexture m_output;
        private bool m_renderReady = false;
        private int m_readyFrame = -1;
        private Material m_blitMat;

        private void OnValidate()
        {
            if (StartCapture)
            {
                StartCapture = false;

                bool prepare = PrepareEnv();

                if (!prepare)
                {
                    return;
                }

                CopyCamera();
                PrepareRenderTarget();
                Setup();
                Capture();
            }
        }

        bool PrepareEnv()
        {
            if (!TargetCamera)
            {
                EditorUtility.DisplayDialog("Error", "Please choose a Target Camera", "OK");
                return false;
            }

            return true;
        }

        private void OnEnable()
        {
            m_captureCamera = this.gameObject.GetComponent<Camera>();
            m_captureCamera.enabled = false;
            m_captureCamera.hideFlags = HideFlags.NotEditable;

            m_blitMat = new Material(BlitShader);

            RenderPipelineManager.endCameraRendering -= OnEndCameraRendering;
        }

        private void OnDisable()
        {
            RenderPipelineManager.endCameraRendering -= OnEndCameraRendering;
        }

        void OnEndCameraRendering(ScriptableRenderContext context, Camera cam)
        {
            if (cam == m_captureCamera)
            {
                SaveOutput();
                m_readyFrame = -1;
                m_renderReady = false;
                m_captureCamera.targetTexture = null;

                RenderPipelineManager.endCameraRendering -= OnEndCameraRendering;
            }
        }

        void PrepareRenderTarget()
        {
            Vector2 rtSize = RenderTextureSize == Vector2.zero
                ? new Vector2(Screen.width, Screen.height)
                : RenderTextureSize;

            {
                m_renderTarget = new RenderTexture((int)rtSize.x, (int)rtSize.y, 0, RenderTextureFormat.ARGB32);
                m_renderTarget.filterMode = FilterMode.Bilinear;
                m_renderTarget.wrapMode = TextureWrapMode.Clamp;
                m_renderTarget.autoGenerateMips = false;
                m_renderTarget.name = "CaptureTarget";
            }

            if (UsedCaptureType == CaptureType.Color)
            {
                m_output = new RenderTexture(m_renderTarget);
            }
            else
            {
                m_output = new RenderTexture((int)rtSize.x, (int)rtSize.y, 0, RenderTextureFormat.R8);
                m_output.filterMode = FilterMode.Bilinear;
                m_output.wrapMode = TextureWrapMode.Clamp;
                m_output.autoGenerateMips = false;
            }

            m_output.name = "CaptureOutput";
            m_output.Create();

            m_renderTarget.Create();
        }

        void Setup()
        {
            if (!m_renderTarget || !m_captureCamera)
            {
                return;
            }

            m_captureCamera.targetTexture = m_renderTarget;

            RenderPipelineManager.endCameraRendering += OnEndCameraRendering;
            m_renderReady = true;
        }

        void Capture()
        {
            if (!m_renderReady)
            {
                return;
            }

            if (m_readyFrame == -1)
            {
                m_readyFrame = Time.frameCount;
            }

            m_captureCamera.Render();
        }

        void SaveOutput()
        {
            if (!m_blitMat)
            {
                m_blitMat = new Material(BlitShader);
            }
            
            Graphics.Blit(UsedCaptureType == CaptureType.Depth ? null : m_renderTarget, m_output, m_blitMat,
                UsedCaptureType == CaptureType.Depth ? 1 : 0);

            RenderTexture.active = m_output;
            Texture2D renderedTexture = new Texture2D(m_renderTarget.width, m_renderTarget.height, UsedCaptureType == CaptureType.Depth ? TextureFormat.R8 : TextureFormat.ARGB32, false, true);
            renderedTexture.ReadPixels(new Rect(0, 0, m_renderTarget.width, m_renderTarget.height), 0, 0);
            renderedTexture.Apply();
            RenderTexture.active = null;

            Scene curScene = SceneManager.GetActiveScene();
            string outputName = null != curScene ? curScene.name : "NoName";
            DateTime nowTime = DateTime.Now;
            string timeStr = nowTime.ToString("yyyy_M_d_HH_mm_ss");
            outputName = "Scene_" + outputName + "_" + timeStr + "_" + UsedCaptureType.ToString();

            string savePath = "Assets/Scripts/Tools/CameraCapturer/Output/" + outputName + ".asset";
            AssetDatabase.CreateAsset(renderedTexture, savePath);

            AssetDatabase.Refresh();

            Texture2D savedAsset =
                AssetDatabase.LoadAssetAtPath<Texture2D>(savePath);

            Output = savedAsset;

            Matrix4x4 p = Matrix4x4.identity;
            if (TargetCamera.orthographic)
            {
                float orthAspect = renderedTexture.width / (float)renderedTexture.height;
                float h = TargetCamera.orthographicSize;
                float w = h * orthAspect;
                p = Matrix4x4.Ortho(-w, w, -h, h, TargetCamera.nearClipPlane, TargetCamera.farClipPlane);
            }
            else
            {
                p = Matrix4x4.Perspective(TargetCamera.fieldOfView, renderedTexture.width / (float)renderedTexture.height,
                    TargetCamera.nearClipPlane, TargetCamera.farClipPlane);
            }
        
            CameraCaptureData data = ScriptableObject.CreateInstance<CameraCaptureData>();
            data.WorldToCaptureCamera = GL.GetGPUProjectionMatrix(p, false) * TargetCamera.worldToCameraMatrix;

            savePath = "Assets/Scripts/Tools/CameraCapturer/Output/" + outputName + "_data.asset";

            AssetDatabase.CreateAsset(data, savePath);
            AssetDatabase.Refresh();

            OutputPath = savePath;

            if (savedAsset)
            {
                Debug.Log("SaveDone:" + savePath);
            }
            else
            {
                Debug.Log("Saved Failed");
            }
        }

        void CopyCamera()
        {
            Camera usedCamera = TargetCamera ?? Camera.main;

            if (!usedCamera)
            {
                return;
            }

            if (!m_captureCamera)
            {
                m_captureCamera = this.gameObject.GetComponent<Camera>();
                m_captureCamera.enabled = false;
            }

            if (!m_captureCamera)
            {
                return;
            }

            m_captureCamera.name = "CaptureCamera";
            m_captureCamera.CopyFrom(usedCamera);
            m_captureCamera.depthTextureMode =
                UsedCaptureType == CaptureType.Depth ? DepthTextureMode.Depth : DepthTextureMode.None;
        }
#endif
    }
}