Shader "Unlit/SDFTestShader"
{
    Properties
    {
        _MainTex ("SDFTexture", 2D) = "white" {}
        _FogTex("Fog", 2D) = "white" {}
        _Height ("Height", Range(0,1)) = 0
    }
    SubShader
    {
        Tags { "RenderType"="Transparent" "Queue"="Transparent"}
        Blend SrcAlpha OneMinusSrcAlpha
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float2 fogUV : TEXCOORD1;
                float4 vertex : SV_POSITION;
                float3 worldPos : TEXCOORD3;
            };

            sampler2D _MainTex;
            sampler2D _FogTex;
            float4 _FogTex_ST;
            float4 _MainTex_ST;
            float _Height;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.worldPos = mul(UNITY_MATRIX_M, float4(v.vertex.xyz, 1));
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.fogUV = TRANSFORM_TEX(v.uv, _FogTex);
                o.fogUV.x += _Time.x;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                float4 fogCol = tex2D(_FogTex, i.fogUV);
                float d = tex2D(_MainTex, i.uv).r;
                fogCol.a = saturate(d/_Height);
                return fogCol;
            }
            ENDCG
        }
    }
}
