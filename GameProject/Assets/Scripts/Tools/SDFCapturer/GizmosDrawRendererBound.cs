//file: GizmosDrawRendererBound.cs
//Author: Aoicocoon
//Date: 2023-09-26 11:20

using System;
using UnityEngine;

namespace UnityTemplateProjects.Tools.SDFCapturer
{
   
    public class GizmosDrawRendererBound : MonoBehaviour
    {
        private Renderer m_renderer;

        void Awake()
        {
           
        }

        private void OnDrawGizmos()
        {
            if (!m_renderer)
            {
                m_renderer = this.gameObject.GetComponent<Renderer>();
            }
            
            if (m_renderer)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(m_renderer.transform.position, m_renderer.bounds.size);
            }
        }
    }
}