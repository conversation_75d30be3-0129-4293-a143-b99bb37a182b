//file: StaticMeshCombiner.cs
//Author: Aoicocoon
//HLOD like Build
//Date: 2023-12-15 17:28

using System;
using System.Collections.Generic;
using System.Linq;
using Sultan.Core;
using Unity.Collections;
using Unity.Mathematics;
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;

namespace Tools.StaticMeshCombiner
{
    public class StaticMeshCombiner : MonoBehaviour
    {
        public bool DoCombine = false;
        public string[] MaterialWorkPath = {"Assets/ArtTmp/Scenes/Models/Tree/materials/", "Assets/ArtTmp/Scenes/Models/Grass/materials/"};
        public Transform CombineMeshRoot;
        public int TextureArraySize = 256;
        public Texture m_Output;
        public Mesh m_OutputMesh;

        #if UNITY_EDITOR
        private InternalStaticMeshCombiner m_combiner;
        #endif
        
#if UNITY_EDITOR
        private void OnValidate()
        {
            if (DoCombine)
            {
                DoCombine = false;

                if (null == m_combiner)
                {
                    m_combiner = new InternalStaticMeshCombiner(MaterialWorkPath, TextureArraySize);
                }
                
                m_Output = m_combiner.CombineTex();
                m_OutputMesh = m_combiner.CombineMesh(CombineMeshRoot, null);
            }
        }
#endif
    }

#if UNITY_EDITOR
    public class InternalStaticMeshCombiner
    {
        public static string[] S_MATERIAL_WORK_PATH = {"Assets/ArtTmp/Scenes/Models/Tree/materials/", "Assets/ArtTmp/Scenes/Models/Grass/materials/"};

        public static string S_COMBINE_MATERIAL_SAVE_PATH =
            "Assets/Scripts/Tools/StaticMeshCombiner/CombinedTexMaterial.asset";
        
        public static string S_COMBINE_ANIM_MATERIAL_SAVE_PATH =
            "Assets/Scripts/Tools/StaticMeshCombiner/CombinedTexAnimMaterial.asset";

        public static string S_COMBINE_TEXTURE_SAVE_PATH =
            "Assets/Scripts/Tools/StaticMeshCombiner/CombinedTex_{0}.asset";
        public static string S_COMBINE_CFG_SAVE_PATH = "Assets/Scripts/Tools/StaticMeshCombiner/CombinedTexCfg.asset";
        public static string S_COMBINE_MESH_SAVE_PATH = "Assets/Scripts/Tools/StaticMeshCombiner/{0}.asset";

        public static string[] S_PREFAB_VEGETATION_INPUT_PATH =
            { "Assets/ArtTmp/Scenes/Prefabs/Tree", "Assets/ArtTmp/Scenes/Prefabs/Grass" };
        public static string[] S_PREFAB_VEGETATION_OUTPUT_PATH =
            { "Assets/Res/Scenes/Prefabs/Tree/{0}.prefab", "Assets/Res/Scenes/Prefabs/Grass/{0}.prefab" };
        
        public static string S_PREFAB_INPUT_PATH = "Assets/ArtTmp/Scenes/Prefabs/Buildcombined/";
        public static string S_PREFAB_OUTPUT_PATH = "Assets/Res/Scenes/Prefabs/Build/{0}.prefab";

        public static string S_BIG_WORLD_TRIM_OUTPUT_PATH = "Assets/Res/Scenes/Terrain/chunkAssets/mesh/{0}.asset";
        public static string S_BIG_WORLD_TRIM_INPUTPUT_PATH = "Assets/Res/Scenes/Terrain/chunkAssets/chunk/world";
        
        public string[] MaterialWorkPath;
        public int TextureArraySize = 256;
        
        private List<Material> m_sortMat = new List<Material>();
        private List<Texture2D> m_sortTexture = new List<Texture2D>();
        private SortedDictionary<string, Texture2D> m_packedTextureDict = new SortedDictionary<string, Texture2D>();

        public InternalStaticMeshCombiner(string[] materialPath, int textureSize)
        {
            MaterialWorkPath = materialPath;
            TextureArraySize = textureSize;
        }

        struct BackUpTexInfo
        {
            public int Size;
            public TextureFormat Format;

            public BackUpTexInfo(Texture2D tex)
            {
                Size = tex.width;
                Format = tex.format;
            }
        }

        private Dictionary<Texture2D, BackUpTexInfo> m_BackupTexDict = new Dictionary<Texture2D, BackUpTexInfo>();
        public Texture CombineTex()
        {
            //1 先计算纹理数组顺序，把所有要合并的材质放在一起，或选中根目录
            // 1)根据目录材质球Hash排序
            // 2)按主纹理Hash排序
            // 3)生成全局纹理索引说明配置 + 纹理数组文件
            
            m_sortMat.Clear();
            var guids = AssetDatabase.FindAssets("t:Material", MaterialWorkPath);
            foreach (var gid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(gid);
                Material mat = AssetDatabase.LoadAssetAtPath<Material>(path);

                if (mat && mat.shader.name == "Sultan/Scene/BuildUnLit")
                {
                    m_sortMat.Add(mat);
                }
            }
            
            m_sortMat.Sort(SortMat);

            m_sortTexture.Clear();
            for (int i = 0; i < m_sortMat.Count; i++)
            {
                var mainTexture = m_sortMat[i].GetTexture("_BaseMap");
                if (mainTexture)
                {
                    m_sortTexture.Add(mainTexture as Texture2D);
                }
            }
            m_sortTexture.Sort(SortTexture);

            m_packedTextureDict.Clear();
            for (int i = 0; i < m_sortTexture.Count; i++)
            {
                var tex = m_sortTexture[i];
                string texName = tex.name;

                if (!m_packedTextureDict.ContainsKey(texName))
                {
                    m_packedTextureDict.Add(tex.name, tex);
                }
            }

            int idx = 0;
            TextureFormat format = TextureFormat.ARGB32;
            FilterMode filterMode = FilterMode.Bilinear;
            TextureWrapMode wrapMode = TextureWrapMode.Clamp;
            m_BackupTexDict.Clear();
            foreach (var t in m_packedTextureDict)
            {
                if (idx == 0)
                {
                    format = t.Value.format;
                    filterMode = t.Value.filterMode;
                    wrapMode = t.Value.wrapMode;
                }
                Debug.LogFormat("idx {0} t {1}", idx++, t.Key);
                
                m_BackupTexDict.Add(t.Value, new BackUpTexInfo(t.Value));
                
                string texPath = AssetDatabase.GetAssetPath(t.Value);
                ModifySrcTexture(texPath, TextureArraySize);
            }

            Texture2DArray texArray = new Texture2DArray(TextureArraySize, TextureArraySize, m_packedTextureDict.Count,
                format, true, false);
            texArray.filterMode = filterMode;
            texArray.wrapMode = wrapMode;

            idx = 0;
            foreach(var t in m_packedTextureDict)
            {
                for (int m = 0; m < t.Value.mipmapCount; m++)
                {
                    Graphics.CopyTexture(t.Value, 0, m, texArray, idx, m);
                }

                idx++;
            }
            texArray.Apply(false, true);
            

            long version = -1;
            var s = System.DateTime.Now.ToString("yyyyMMddHHmmss");
            long.TryParse(s, out version);
            
            string outputPath = string.Format(S_COMBINE_TEXTURE_SAVE_PATH,version);

            //remove old version
            string texSaveFolder = System.IO.Path.GetDirectoryName(S_COMBINE_TEXTURE_SAVE_PATH);
            
            guids = AssetDatabase.FindAssets("t:Texture2DArray", new string[]{texSaveFolder});
            foreach (var gid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(gid);
                AssetDatabase.DeleteAsset(path);
            }

            AssetDatabase.CreateAsset(texArray, outputPath);
            AssetDatabase.Refresh();

            var output = AssetDatabase.LoadAssetAtPath<Texture>(outputPath);


            foreach (var t in m_packedTextureDict)
            {
                BackUpTexInfo info = m_BackupTexDict[t.Value];
                string texPath = AssetDatabase.GetAssetPath(t.Value);
                RecoverSrcTexture(texPath, info.Size);
            }
            
            //生成配置
            StaticCombineTextureArrayConfig cfg = ScriptableObject.CreateInstance<StaticCombineTextureArrayConfig>();
            List<String> backupTexNameList = new List<string>();
            foreach (var t in m_packedTextureDict)
            {
                backupTexNameList.Add(t.Key);
            }

            cfg.TextureNames = backupTexNameList.ToArray();
            cfg.Version = version;
            outputPath = S_COMBINE_CFG_SAVE_PATH;
            StaticCombineTextureArrayConfig oldCfg =
                AssetDatabase.LoadAssetAtPath<StaticCombineTextureArrayConfig>(outputPath);
            if (oldCfg)
            {
                AssetDatabase.DeleteAsset(outputPath);
            }
            AssetDatabase.CreateAsset(cfg, outputPath);
            
            //创建材质
            Material combineMat = new Material(Shader.Find("Sultan/Scene/BuildUnLit"));
            combineMat.EnableKeyword("COMBINE_MESH_ON");
            combineMat.SetInt("COMBINE_MESH_ON", 1);
            combineMat.EnableKeyword("_ALPHATEST_ON");
            combineMat.SetTexture("_BaseMapArray", output);
            combineMat.EnableKeyword("_SHADOW_MODDEL_BILLBOARD");
            combineMat.SetInt("_SHADOW_MODDEL_BILLBOARD", 1);
            combineMat.EnableKeyword("_RECEIVE_SHADOWS");
            combineMat.SetInt("_RECEIVE_SHADOWS", 1);
            combineMat.SetVector("_AnchorPosition", new Vector4(0,0,-0.6f, -0.1f));
            
            outputPath = S_COMBINE_MATERIAL_SAVE_PATH;
            Material oldMat = AssetDatabase.LoadAssetAtPath<Material>(outputPath);
            if (oldMat)
            {
                AssetDatabase.DeleteAsset(outputPath);
            }
            AssetDatabase.CreateAsset(combineMat, outputPath);
            
            //带顶点动画材质
            Material combineAnimMat = new Material(Shader.Find("Sultan/Scene/BuildUnLit"));
            combineAnimMat.EnableKeyword("COMBINE_MESH_ON");
            combineAnimMat.SetInt("COMBINE_MESH_ON", 1);
            combineAnimMat.EnableKeyword("_ALPHATEST_ON");
            combineAnimMat.SetTexture("_BaseMapArray", output);
            combineAnimMat.EnableKeyword("VERTEXANI_ON");
            combineAnimMat.SetInt("VERTEXANI_ON", 1);
            combineAnimMat.EnableKeyword("_SHADOW_MODDEL_BILLBOARD");
            combineAnimMat.SetInt("_SHADOW_MODDEL_BILLBOARD", 1);
            combineAnimMat.EnableKeyword("_RECEIVE_SHADOWS");
            combineAnimMat.SetInt("_RECEIVE_SHADOWS", 1);
            combineAnimMat.SetVector("_AnchorPosition", new Vector4(0,0,-0.6f, -0.1f));
            
            outputPath = S_COMBINE_ANIM_MATERIAL_SAVE_PATH;
            oldMat = AssetDatabase.LoadAssetAtPath<Material>(outputPath);
            if (oldMat)
            {
                AssetDatabase.DeleteAsset(outputPath);
            }
            AssetDatabase.CreateAsset(combineAnimMat, outputPath);

            AssetDatabase.Refresh();


            return output;
        }

        int GetMainTextureInArrayIndexByMaterial(Material mat)
        {
            var mainTexture = mat.GetTexture("_BaseMap");
            if (!mainTexture)
            {
                Debug.Log("Missing BaseMap" + mat.name);
                return -1;
            }
            int idx = -1;
            var keys = m_packedTextureDict.Keys.ToArray();
            idx = System.Array.IndexOf(keys, mainTexture.name);
            return idx;
        }

        struct CustomVertex
        {
            public half posX, posY, posZ, posW;
            public half uvX, uvY;
        }

        struct ColorCustomVertex
        {
            public half posX, posY, posZ, posW;
            public half R, G, B,A;
            public half uvX, uvY;
        }

        bool ValidTex2DArrayFileReady()
        {
            //load cfg and tex
            string cfgPath = S_COMBINE_CFG_SAVE_PATH;
            StaticCombineTextureArrayConfig cfg = AssetDatabase.LoadAssetAtPath<StaticCombineTextureArrayConfig>(cfgPath);
            if (!cfg)
            {
                Debug.LogError("Create texArray file before");
                return false;
            }

            string texPath = string.Format(S_COMBINE_TEXTURE_SAVE_PATH,
                cfg.Version);
            Texture2DArray texArray =  AssetDatabase.LoadAssetAtPath<Texture2DArray>(texPath);
            if (!texArray)
            {
                Debug.LogError("Can not find tex2DArray file or version not match, recreate texArray file");
                return false;
            }
            
            m_packedTextureDict = m_packedTextureDict ?? new SortedDictionary<string, Texture2D>();
            m_packedTextureDict.Clear();
            foreach (var texName in cfg.TextureNames)
            {
                m_packedTextureDict.Add(texName, null);
            }

            return true;
        }

        public Mesh BuildAndCompressMesh(Mesh srcMesh, Material srcMaterial, string outputMeshName)
        {
            if (!srcMesh)
            {
                return null;
            }
            
            bool isValidTex2DArrayFileReady = ValidTex2DArrayFileReady();
            if (!isValidTex2DArrayFileReady)
            {
                return null;
            }
            
            //看看导入的mesh 是否包含color
            bool hasColorAttribute = false;
            for (int a = 0; a < srcMesh.vertexAttributeCount; a++)
            {
                var attribute = srcMesh.GetVertexAttribute(a);
                if (attribute.attribute == VertexAttribute.Color)
                {
                    hasColorAttribute = true;
                    break;
                }
            }
            
            int mainTextureIndex = GetMainTextureInArrayIndexByMaterial(srcMaterial);
            if (mainTextureIndex == -1)
            {
                Debug.LogError("Find Combined Tex index faild:" + srcMesh.name);
                return null;
            }
            
            List<Vector4> vertexList = new List<Vector4>();
            List<Vector2> uvList = new List<Vector2>();
            List<Color> colorList = new List<Color>();
            List<ushort> triangleList = new List<ushort>();
            
            //vertex
            List<Vector3> curVertexList = new List<Vector3>();
            srcMesh.GetVertices(curVertexList);

            //uv
            List<Vector2> curUVList = new List<Vector2>();
            srcMesh.GetUVs(0, curUVList);
                
            //color
            List<Color> curColorList = new List<Color>();
            srcMesh.GetColors(curColorList);
                
            //tri
            List<int> curTriangle = new List<int>();
            srcMesh.GetTriangles(curTriangle, 0);
                
            //collect
            uvList.AddRange(curUVList);
            colorList.AddRange(curColorList);
            
            for (int v = 0; v < curVertexList.Count; v++)
            {
                Vector3 localPos = curVertexList[v];
                vertexList.Add(new Vector4(localPos.x, localPos.y, localPos.z, mainTextureIndex));
            }

                
            for (int t = 0; t < curTriangle.Count; t++)
            {
                triangleList.Add((ushort)(curTriangle[t]));
            }

            VertexAttributeDescriptor[] layout = null;
            if (hasColorAttribute)
            {
                layout = new[]
                {
                    new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float16, 4),
                    new VertexAttributeDescriptor(VertexAttribute.Color, VertexAttributeFormat.Float16, 4),
                    new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float16, 2),
                   
                };
            }
            else
            {
                layout = new[]
                {
                    new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float16, 4),
                    new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float16, 2),
                };
            }

            Mesh finalMesh = new Mesh();
            
            finalMesh.SetVertexBufferParams(vertexList.Count, layout);
            
            if (hasColorAttribute)
            {
                NativeArray<ColorCustomVertex> finalVertexList = new NativeArray<ColorCustomVertex>(vertexList.Count, Allocator.Persistent);
                for (int i = 0; i < vertexList.Count; i++)
                {
                    ColorCustomVertex vertex = new ColorCustomVertex();
                    vertex.posX = (half)vertexList[i].x;
                    vertex.posY = (half)vertexList[i].y;
                    vertex.posZ = (half)vertexList[i].z;
                    vertex.posW = (half)vertexList[i].w;
                    
                    vertex.R = (half)colorList[i].r;
                    vertex.G = (half)colorList[i].g;
                    vertex.B = (half)colorList[i].b;
                    vertex.A = (half)colorList[i].a;

                    vertex.uvX = (half)uvList[i].x;
                    vertex.uvY = (half)uvList[i].y;

                   

                    finalVertexList[i] = vertex;
                }
                finalMesh.SetVertexBufferData(finalVertexList, 0, 0, finalVertexList.Length);
                finalVertexList.Dispose();
            }
            else
            {
                NativeArray<CustomVertex> finalVertexList = new NativeArray<CustomVertex>(vertexList.Count, Allocator.Persistent);
                for (int i = 0; i < vertexList.Count; i++)
                {
                    CustomVertex vertex = new CustomVertex();
                    vertex.posX = (half)vertexList[i].x;
                    vertex.posY = (half)vertexList[i].y;
                    vertex.posZ = (half)vertexList[i].z;
                    vertex.posW = (half)vertexList[i].w;

                    vertex.uvX = (half)uvList[i].x;
                    vertex.uvY = (half)uvList[i].y;

                    finalVertexList[i] = vertex;
                }
                finalMesh.SetVertexBufferData(finalVertexList, 0, 0, finalVertexList.Length);
                finalVertexList.Dispose();
            }
            
            finalMesh.SetIndexBufferParams(triangleList.Count, IndexFormat.UInt16);
            finalMesh.SetIndexBufferData(triangleList.ToArray(), 0, 0, triangleList.Count);
           
            finalMesh.subMeshCount = 1;
            var subMeshDesc = new SubMeshDescriptor(0, triangleList.Count, MeshTopology.Triangles);
            subMeshDesc.bounds = srcMesh.bounds;
            finalMesh.SetSubMesh(0, subMeshDesc);
            finalMesh.bounds = srcMesh.bounds;

            finalMesh.UploadMeshData(true);

            Mesh output = null;

            outputMeshName = string.IsNullOrEmpty(outputMeshName) ? "CombinedMesh" : outputMeshName;
            string path = string.Format(S_COMBINE_MESH_SAVE_PATH, outputMeshName);
            Mesh oldMesh = AssetDatabase.LoadAssetAtPath<Mesh>(path);
            if (oldMesh)
            {
                AssetDatabase.DeleteAsset(path);
            }
            AssetDatabase.CreateAsset(finalMesh, path);
            AssetDatabase.Refresh();

            output = AssetDatabase.LoadAssetAtPath<Mesh>(path);

            return output;
        }

        public Mesh CombineMesh(Transform CombineMeshRoot, string outputMeshName)
        {
            //start combine mesh
            if (!CombineMeshRoot)
            {
                return null;
            }
            
            bool isValidTex2DArrayFileReady = ValidTex2DArrayFileReady();
            if (!isValidTex2DArrayFileReady)
            {
                return null;
            }

            List<Mesh> CollectedMeshesFromRoot = new List<Mesh>();
            List<Material> CollectedMaterialFromRoot = new List<Material>();
            List<Matrix4x4> CollectedLocalToWorld = new List<Matrix4x4>();
            
            MeshFilter[] mfs = CombineMeshRoot.gameObject.GetComponentsInChildren<MeshFilter>();

            foreach (var mf in mfs)
            {
                if (!mf.sharedMesh) continue;
                
                GameObject curObj = mf.gameObject;
                MeshRenderer mr = curObj.GetComponent<MeshRenderer>();

                if (mr.sharedMaterial && mr.sharedMaterial.shader.name == "Sultan/Scene/BuildUnLit")
                {
                    CollectedMeshesFromRoot.Add(mf.sharedMesh);

                    CollectedMaterialFromRoot.Add(mr.sharedMaterial);

                    CollectedLocalToWorld.Add(mr.localToWorldMatrix);
                }
            }
            
            List<Vector4> vertexList = new List<Vector4>();
            List<Vector2> uvList = new List<Vector2>();
            List<Color> colorList = new List<Color>();
            List<ushort> triangleList = new List<ushort>();
            
            Debug.Log("Num Attribute:" + CollectedMeshesFromRoot[0].vertexAttributeCount);
            for (int a = 0; a < CollectedMeshesFromRoot[0].vertexAttributeCount; a++)
            {
                VertexAttributeDescriptor desc = CollectedMeshesFromRoot[0].GetVertexAttribute(a);
                Debug.LogFormat("index {0} name {1} format {2} dime {3}",a, desc.attribute, desc.format, desc.dimension);
            }

            int triOffset = 0;
            float maxX = float.MinValue, maxY = float.MinValue, maxZ = float.MinValue;
            float minX = float.MaxValue, minY = float.MaxValue, minZ = float.MaxValue;
            for (int i = 0; i < CollectedMeshesFromRoot.Count; i++)
            {
                Mesh curMesh = CollectedMeshesFromRoot[i];
                Material curMaterial = CollectedMaterialFromRoot[i];
                int mainTextureIndex = GetMainTextureInArrayIndexByMaterial(curMaterial);
                if (mainTextureIndex == -1)
                {
                    Debug.LogError("Find Combined Tex index faild:" + curMesh.name);
                    continue;
                }

                //vertex
                List<Vector3> curVertexList = new List<Vector3>();
                curMesh.GetVertices(curVertexList);

                //uv
                List<Vector2> curUVList = new List<Vector2>();
                curMesh.GetUVs(0, curUVList);
                
                //color
                List<Color> curColorList = new List<Color>();
                curMesh.GetColors(curColorList);
                
                //tri
                List<int> curTriangle = new List<int>();
                curMesh.GetTriangles(curTriangle, 0);
                
                //collect
                uvList.AddRange(curUVList);
                colorList.AddRange(curColorList);
                
                Matrix4x4 local2World = CollectedLocalToWorld[i];
                for (int v = 0; v < curVertexList.Count; v++)
                {
                    Vector3 localPos = curVertexList[v];
                    Vector3 worldPos = local2World * new Vector4(localPos.x, localPos.y, localPos.z, 1);

                    maxX = Mathf.Max(maxX, worldPos.x);
                    maxY = Mathf.Max(maxY, worldPos.y);
                    maxZ = Mathf.Max(maxZ, worldPos.z);
                    
                    minX = Mathf.Min(minX, worldPos.x);
                    minY = Mathf.Min(minY, worldPos.y);
                    minZ = Mathf.Min(minZ, worldPos.z);
                    vertexList.Add(new Vector4(worldPos.x, worldPos.y, worldPos.z, mainTextureIndex));
                }

                
                for (int t = 0; t < curTriangle.Count; t++)
                {
                    triangleList.Add((ushort)(curTriangle[t] + triOffset));
                }
                triOffset += curVertexList.Count;
            }

            var layout = new[]
            {
                new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float16, 4),
                new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float16, 2),
            };

            NativeArray<CustomVertex> finalVertexList = new NativeArray<CustomVertex>(vertexList.Count, Allocator.Persistent);
            for (int i = 0; i < vertexList.Count; i++)
            {
                CustomVertex vertex = new CustomVertex();
                vertex.posX = (half)vertexList[i].x;
                vertex.posY = (half)vertexList[i].y;
                vertex.posZ = (half)vertexList[i].z;
                vertex.posW = (half)vertexList[i].w;

                vertex.uvX = (half)uvList[i].x;
                vertex.uvY = (half)uvList[i].y;

                finalVertexList[i] = vertex;
            }
            Mesh finalMesh = new Mesh();
            
            finalMesh.SetVertexBufferParams(vertexList.Count, layout);
            finalMesh.SetVertexBufferData(finalVertexList, 0, 0, finalVertexList.Length);
            finalMesh.SetIndexBufferParams(triangleList.Count, IndexFormat.UInt16);
            finalMesh.SetIndexBufferData(triangleList.ToArray(), 0, 0, triangleList.Count);
           
            finalMesh.subMeshCount = 1;
            Bounds finalMeshBound = new Bounds();
            finalMeshBound.SetMinMax(new Vector3(minX, minY, minZ), new Vector3(maxX, maxY, maxZ));
            var subMeshDesc = new SubMeshDescriptor(0, triangleList.Count, MeshTopology.Triangles);
            subMeshDesc.bounds = finalMeshBound;
            finalMesh.SetSubMesh(0, subMeshDesc);
            finalMesh.bounds = finalMeshBound;

            finalMesh.UploadMeshData(true);


            finalVertexList.Dispose();

            Mesh output = null;

            outputMeshName = string.IsNullOrEmpty(outputMeshName) ? "CombinedMesh" : outputMeshName;
            string path = string.Format(S_COMBINE_MESH_SAVE_PATH, outputMeshName);
            Mesh oldMesh = AssetDatabase.LoadAssetAtPath<Mesh>(path);
            if (oldMesh)
            {
                AssetDatabase.DeleteAsset(path);
            }
            AssetDatabase.CreateAsset(finalMesh, path);
            AssetDatabase.Refresh();

            output = AssetDatabase.LoadAssetAtPath<Mesh>(path);

            return output;
        }

        void RecoverSrcTexture(string texPath, int Size)
        {

            string[] PlatformsStr = {"Android", "iPhone"};
           
            TextureImporter ti = (TextureImporter)AssetImporter.GetAtPath(texPath);
            if (ti.isReadable)
            {
                ti.compressionQuality = (int)TextureCompressionQuality.Normal;
                foreach (var Platform in PlatformsStr)
                {
                    TextureImporterPlatformSettings settings = new TextureImporterPlatformSettings();
                    settings.name = Platform;
                    settings.overridden = true;
                    settings.maxTextureSize = Size;
                    settings.format = TextureImporterFormat.ASTC_6x6;
                    settings.compressionQuality = (int)TextureCompressionQuality.Normal;
                    
                    ti.SetPlatformTextureSettings(settings);
                }
                ti.isReadable = false;
                ti.SaveAndReimport();
            }

        }

        void ModifySrcTexture(string texPath, int Size)
        {

            string[] PlatformsStr = {"Android", "iPhone"};
           
            TextureImporter ti = (TextureImporter)AssetImporter.GetAtPath(texPath);
            if (!ti.isReadable)
            {
                ti.compressionQuality = (int)TextureCompressionQuality.Normal;
                foreach (var Platform in PlatformsStr)
                {
                    TextureImporterPlatformSettings settings = new TextureImporterPlatformSettings();
                    settings.name = Platform;
                    settings.overridden = true;
                    settings.maxTextureSize = Size;
                    settings.format = TextureImporterFormat.ASTC_6x6;
                    settings.compressionQuality = (int)TextureCompressionQuality.Normal;
                    
                    ti.SetPlatformTextureSettings(settings);
                }
                ti.isReadable = true;
                ti.SaveAndReimport();
            }

        }

        int SortMat(Material A, Material B)
        {
            return A.GetHashCode() < B.GetHashCode() ? -1 : 1;
        }

        int SortShader(Shader A, Shader B)
        {
            return A.GetHashCode() < B.GetHashCode() ? -1 : 1;
        }

        int SortTexture(Texture2D A, Texture2D B)
        {
            return A.GetHashCode() < B.GetHashCode() ? -1 : 1;
        }
    }
#endif
}