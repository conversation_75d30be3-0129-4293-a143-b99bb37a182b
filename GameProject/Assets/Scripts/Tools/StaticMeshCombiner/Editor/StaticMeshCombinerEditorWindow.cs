//file: StaticMeshCombinerEditorWindow.cs
//Author: Aoicocoon
//Date: 2023-12-18 16:56

using System;
using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
// using Codice.Client.GameUI.Checkin.ConflictCheckers;
using Sultan.Terrain.PrefabTerrain;
using Sultan.Terrain.Runtime;
using UnityEngine.Rendering;

namespace Tools.StaticMeshCombiner
{
    public class StaticMeshCombinerEditorWindow : EditorWindow
    {
        [MenuItem("Tools/模型导出/内城建筑地表环境合并批量导出")]
        static void Main()
        {
            StaticMeshCombinerEditorWindow d =
                EditorWindow.GetWindow(typeof(StaticMeshCombinerEditorWindow)) as StaticMeshCombinerEditorWindow;
            d.position = new Rect(Screen.resolutions[0].width / 2.0f, Screen.resolutions[0].height / 2.0f, 500, 600);
        }


        private InternalStaticMeshCombiner m_combiner;
        private Texture2DArray m_combinedTex;
        private Material m_combineMat;

        public void Init(string[] path, int texSize)
        {
            m_combiner = new InternalStaticMeshCombiner(path, texSize);
        }

        void OnGUI()
        {
            GUILayout.Label("当前会合并纹理的材质球目录(尺寸256):");
            for (int i = 0; i < InternalStaticMeshCombiner.S_MATERIAL_WORK_PATH.Length; i++)
            {
                GUILayout.Label(InternalStaticMeshCombiner.S_MATERIAL_WORK_PATH[i]);
            }

            bool clickCreateTexBtn = GUILayout.Button("1.生成纹理数组和材质");
            /**
            GUILayout.Label("重构建所有植被网格（压缩+灌入纹理索引），和建筑基座内的植被合并不冲突:");
            for (int i = 0; i < InternalStaticMeshCombiner.S_PREFAB_VEGETATION_INPUT_PATH.Length; i++)
            {
                GUILayout.Label(InternalStaticMeshCombiner.S_PREFAB_VEGETATION_INPUT_PATH[i]);
            }

            GUILayout.Label("当前预制体导出目录:");
            for (int i = 0; i < InternalStaticMeshCombiner.S_PREFAB_VEGETATION_OUTPUT_PATH.Length; i++)
            {
                GUILayout.Label(InternalStaticMeshCombiner.S_PREFAB_VEGETATION_OUTPUT_PATH[i]);
            }

            bool clickCreateVegetationMeshBtn = GUILayout.Button("2.批量生成植被网格(暂时不用)");**/
            GUILayout.Label("通过读取ChunkResData替换装饰物网格");
            bool clickCreateWorldVegetationMeshBtn = GUILayout.Button("2.批量生成装饰物网格(大地图)");
            GUILayout.Label("当前会合并网格目录:");
            GUILayout.Label(InternalStaticMeshCombiner.S_PREFAB_INPUT_PATH);
            GUILayout.Label("当前预制体导出目录:");
            GUILayout.Label(InternalStaticMeshCombiner.S_PREFAB_OUTPUT_PATH);
            bool clickCreateBuildingMeshBtn = GUILayout.Button("3.批量生成建筑基座网格");

            if (clickCreateTexBtn)
            {
                if (null == m_combiner)
                {
                    m_combiner = new InternalStaticMeshCombiner(InternalStaticMeshCombiner.S_MATERIAL_WORK_PATH, 256);
                }

                m_combinedTex = m_combiner.CombineTex() as Texture2DArray;
            }

            if (clickCreateWorldVegetationMeshBtn)
            {
                //Collect all prefabs
                var guids = AssetDatabase.FindAssets("t:ChunkResData", new string[]
                {
                    InternalStaticMeshCombiner.S_BIG_WORLD_TRIM_INPUTPUT_PATH
                });
                //trim cfg 里网格有重复的，做个dict过滤下本次创建过的
                Dictionary<int, bool> tmpCreatedMeshDict = new Dictionary<int, bool>();
                foreach (var gid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(gid);
                    ChunkResData prefab = AssetDatabase.LoadAssetAtPath<ChunkResData>(path);
                    List<TrimAssetCfg> cfgs = new List<TrimAssetCfg>();
                    cfgs.AddRange(prefab.trimAssetArr);
                    cfgs.AddRange(prefab.decalAssetArr);
                    foreach (var trim in cfgs)
                    {
                        var meshPath = trim.meshResPath;
                        var matPath = trim.materialResPath;

                        var hash = HashCode.Combine(meshPath, matPath);
                        if(tmpCreatedMeshDict.ContainsKey(hash)) continue;
                        tmpCreatedMeshDict.Add(hash, true);

                        var mesh = AssetDatabase.LoadAssetAtPath<Mesh>("Assets/Res/" + meshPath);
                        var mat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Res/" + matPath);

                        if (mesh && mat)
                        {
                            if (null == m_combiner)
                            {
                                m_combiner =
                                    new InternalStaticMeshCombiner(InternalStaticMeshCombiner.S_MATERIAL_WORK_PATH,
                                        256);
                            }

                            var combinedMesh = m_combiner.BuildAndCompressMesh(mesh, mat, mesh.name + "_compressed");
                            if (combinedMesh)
                            {
                                string outputPath =
                                    string.Format(InternalStaticMeshCombiner.S_BIG_WORLD_TRIM_OUTPUT_PATH, mesh.name);
                                var oldMesh = AssetDatabase.LoadAssetAtPath<Mesh>(outputPath);
                                if (oldMesh)
                                {
                                    AssetDatabase.DeleteAsset(outputPath);
                                }

                                var srcPath = AssetDatabase.GetAssetPath(combinedMesh);
                                AssetDatabase.MoveAsset(srcPath, outputPath);
                            }
                        }
                    }

                    AssetDatabase.Refresh();
                }
            }

            if(false)//if (clickCreateVegetationMeshBtn)
            {
                m_combineMat =
                    AssetDatabase.LoadAssetAtPath<Material>(
                        InternalStaticMeshCombiner.S_COMBINE_MATERIAL_SAVE_PATH);

                for (int i = 0; i < InternalStaticMeshCombiner.S_PREFAB_VEGETATION_INPUT_PATH.Length; i++)
                {
                    var curWorkPath = InternalStaticMeshCombiner.S_PREFAB_VEGETATION_INPUT_PATH[i];
                    //Collect all prefabs
                    var guids = AssetDatabase.FindAssets("t:Prefab", new string[]
                    {
                        curWorkPath
                    });
                    foreach (var gid in guids)
                    {
                        string path = AssetDatabase.GUIDToAssetPath(gid);
                        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                        if (prefab)
                        {
                            GameObject copyed = GameObject.Instantiate(prefab);
                            if (copyed)
                            {
                                MeshFilter mf = copyed.GetComponentInChildren<MeshFilter>();
                                MeshRenderer mr = copyed.GetComponentInChildren<MeshRenderer>();
                                if (mf && mr)
                                {
                                    if (null == m_combiner)
                                    {
                                        m_combiner =
                                            new InternalStaticMeshCombiner(
                                                InternalStaticMeshCombiner.S_MATERIAL_WORK_PATH, 256);
                                    }

                                    var combinedMesh = m_combiner.BuildAndCompressMesh(mf.sharedMesh, mr.sharedMaterial,
                                        prefab.name + "_compressed");
                                    if (combinedMesh)
                                    {
                                        //特殊处理的部分,带有Color的植被为动画植被，需要开启VertexAnim的材质
                                        if (mf.sharedMesh.HasVertexAttribute(VertexAttribute.Color))
                                        {
                                            m_combineMat = AssetDatabase.LoadAssetAtPath<Material>(
                                                InternalStaticMeshCombiner.S_COMBINE_ANIM_MATERIAL_SAVE_PATH);

                                            //copy anim prop
                                            Vector4 srcNoise = mr.sharedMaterial.GetVector("_NoiseMapPara");
                                            Vector4 srcAnim = mr.sharedMaterial.GetVector("_VertexAniPara");
                                            m_combineMat.SetVector("_NoiseMapPara", srcNoise);
                                            m_combineMat.SetVector("_VertexAniPara", srcAnim);
                                            m_combineMat.SetTexture("_NoiseMap",
                                                mr.sharedMaterial.GetTexture("_NoiseMap"));
                                        }

                                        mf.sharedMesh = combinedMesh;
                                        mr.sharedMaterial = m_combineMat;

                                        //save copyed prefab
                                        bool saveDone = false;
                                        string savePath = string.Format(
                                            InternalStaticMeshCombiner.S_PREFAB_VEGETATION_OUTPUT_PATH[i], prefab.name);
                                        PrefabUtility.SaveAsPrefabAsset(copyed, savePath, out saveDone);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (clickCreateBuildingMeshBtn)
            {
                m_combineMat =
                    AssetDatabase.LoadAssetAtPath<Material>(
                        InternalStaticMeshCombiner.S_COMBINE_MATERIAL_SAVE_PATH);

                //Collect all prefabs
                var guids = AssetDatabase.FindAssets("t:Prefab", new string[]
                {
                    InternalStaticMeshCombiner.S_PREFAB_INPUT_PATH
                });

                foreach (var gid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(gid);
                    GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                    if (prefab)
                    {
                        GameObject copyed = GameObject.Instantiate(prefab);
                        if (copyed)
                        {
                            //find need combine root
                            Transform environ = copyed.transform.Find("environ");
                            if (environ)
                            {
                                Transform environParent = environ.parent;
                                if (null == m_combiner)
                                {
                                    m_combiner =
                                        new InternalStaticMeshCombiner(InternalStaticMeshCombiner.S_MATERIAL_WORK_PATH,
                                            256);
                                }

                                var combinedMesh = m_combiner.CombineMesh(environ, prefab.name + "_environ");

                                if (combinedMesh)
                                {
                                    //delete old node
                                    GameObject.DestroyImmediate(environ.gameObject);

                                    //add newNode
                                    GameObject newEnvironNode = new GameObject("environ");
                                    var mf = newEnvironNode.AddComponent<MeshFilter>();
                                    var mr = newEnvironNode.AddComponent<MeshRenderer>();
                                    mf.sharedMesh = combinedMesh;
                                    mr.sharedMaterial = m_combineMat;
                                    newEnvironNode.transform.parent = environParent;

                                    //save copyed prefab
                                    bool saveDone = false;
                                    string savePath = string.Format(InternalStaticMeshCombiner.S_PREFAB_OUTPUT_PATH,
                                        prefab.name);
                                    PrefabUtility.SaveAsPrefabAsset(copyed, savePath, out saveDone);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}