using Sultan.Test;
using UnityEditor;
using UnityEngine;

namespace Sultan.Editor
{
    [CustomEditor(typeof(FogMaskTexture))]
    public class FogMaskTexture_Editor : UnityEditor.Editor
    {
        public RectInt UnlockArea;
        
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            FogMaskTexture fogMask = target as FogMaskTexture;
            ;
            UnlockArea = EditorGUILayout.RectIntField("解锁区域",UnlockArea);
            if (GUILayout.Button("unlock"))
            {
                fogMask.UnlockArea(UnlockArea);             
            }
            
        }
    }
}