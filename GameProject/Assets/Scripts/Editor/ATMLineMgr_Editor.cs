using System;
using System.Collections.Generic;
using Sultan.Tools;
using UnityEditor;
using UnityEngine;
namespace Sultan.Editor
{
    [CustomEditor(typeof(ATMLineMgrEx))]
    public class ATMLineMgr_Editor :UnityEditor.Editor
    {
        private SerializedProperty _textureListProperty;
        public String _exportTextureArrayPath = "Assets/ArtTmp/Scenes/Terrain/Legion/terrainArea/legionArray.asset";
        public String _srcTextureFolder = "Assets/ArtTmp/Scenes/Terrain/Legion/area/";
        public String _srcTextureRule = "Area_";

        public void OnEnable()
        {
            _textureListProperty = serializedObject.FindProperty("GeneralTextureList");

        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            var com = (ATMLineMgrEx)target;

            _exportTextureArrayPath = EditorGUILayout.TextField("导出纹理数组路径", _exportTextureArrayPath);
            _srcTextureFolder =EditorGUILayout.TextField("纹理目录", _srcTextureFolder); 
            _srcTextureRule = EditorGUILayout.TextField("纹理匹配规则", _srcTextureRule);

            if (GUILayout.Button("Fix"))
                FixTextureColor();

            if (GUILayout.Button("准备贴图"))
            {
                var guids = AssetDatabase.FindAssets(_srcTextureRule + " t:Texture", new []{_srcTextureFolder});
                foreach (var guid in guids)
                {
                    var tex = AssetDatabase.LoadAssetAtPath<Texture2D>(AssetDatabase.GUIDToAssetPath(guid));
                    com.GeneralTextureList.Add(tex);
                }
            }
            
            if (GUILayout.Button("生成"))
            {
                ExportTextureArray(_exportTextureArrayPath);
            }

            
            if(GUILayout.Button("ForceRefresh"))
            {
                com.ForceRefresh();
            }
            RenderPreview(com.m_InfoTex, "联盟信息");
        }

        public void FixTextureColor()
        {
            var com = (ATMLineMgrEx)target;
            var guids = AssetDatabase.FindAssets(_srcTextureRule + " t:Texture", new []{_srcTextureFolder});
            try
            {
                AssetDatabase.StartAssetEditing();
                Color c = Color.black;
                c.a = 0;
                foreach (var guid in guids)
                {
                    var tex = AssetDatabase.LoadAssetAtPath<Texture2D>(AssetDatabase.GUIDToAssetPath(guid));
                    var colors = tex.GetPixels();
                    for (int i = 0; i < colors.Length; i++)
                    {
                        if(colors[i].Equals(Color.white))
                            colors[i] = c;
                    }
                    tex.SetPixels(colors);
                    tex.Apply();
                    AssetDatabase.SaveAssetIfDirty(tex);
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
                AssetDatabase.Refresh();
            }
             
        }

        public void ExportTextureArray(string output)
        {
            int idx = 0;
            TextureFormat format = TextureFormat.R8;
            FilterMode filterMode = FilterMode.Bilinear;
            TextureWrapMode wrapMode = TextureWrapMode.Clamp;
            var com = target as ATMLineMgrEx;
            var textureArraySize = 50;

            try
            {
                AssetDatabase.StartAssetEditing();
                foreach (var t in com.GeneralTextureList)
                {
                    if (idx == 0)
                    {
                        format = t.format;
                        filterMode = t.filterMode;
                        wrapMode = t.wrapMode;
                    }

                    idx++;
                    
                    string texPath = AssetDatabase.GetAssetPath(t);
                    // CombineHelper.ModifySrcTexture(texPath, textureArraySize, TextureImporterFormat.R8);
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
                AssetDatabase.Refresh();
            }
            
            Texture2DArray texArray = new Texture2DArray(textureArraySize, textureArraySize, com.GeneralTextureList.Count,
                format, true, true);
            texArray.filterMode = filterMode;
            texArray.wrapMode = wrapMode;
            idx = 0;
            foreach(var t in  com.GeneralTextureList)
            {
                for (int m = 0; m < t.mipmapCount; m++)
                {
                    Graphics.CopyTexture(t, 0, m, texArray, idx, m);
                }

                idx++;
            }
            texArray.Apply(false, true);
            try
            {
                AssetDatabase.StartAssetEditing();
                foreach (var t in com.GeneralTextureList)
                {
                    string texPath = AssetDatabase.GetAssetPath(t);
                    // CombineHelper.RecoverSrcTexture(texPath, textureArraySize);
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
                AssetDatabase.Refresh();
            }
            AssetDatabase.CreateAsset(texArray, output);
            
        }
        
        void RenderPreview(Texture tex, string label)
        {
            if(tex == null)
                return;

            

            EditorGUILayout.Space();
            EditorGUILayout.LabelField(label + string.Format(": {0}x{1}",tex.width,tex.height));
            EditorGUI.DrawPreviewTexture(GUILayoutUtility.GetAspectRect((float)tex.width / tex.height), tex);
        }
    }
}