using UnityEditor;
using UnityEngine;
using UnityEditor.SceneManagement;
using Sultan.Editor;
using System.IO;
using Sultan.Manager;
using Sultan.Shadow.Runtime;
using System.Collections.Generic;
using System.Drawing;
using XLua;
using Sultan.Game;
using Sultan.Lighting;
using UnityEngine.Rendering;
using UnityEngine.SceneManagement;
using UnityEngine.Rendering.Universal;
using FontStyle = UnityEngine.FontStyle;

namespace Sultan.Test
{
    [CustomEditor(typeof(SceneConfigSwitcher))]
    public class SceneConfigSwitcherEditor : UnityEditor.Editor
    {
        private SceneConfigSwitcher switcher;

        private GUIStyle labelStyle = new GUIStyle(EditorStyles.toolbarButton);
        private GUIStyle preSliderStyle = new GUIStyle("PreSlider");
        // private GUIStyle preSliderStyle = new GUIStyle("WindowBottomResize");

        private string sceneName;
        // private ShadowConfig shadowConfig;

        /// ////////////////////////////////////
    

        public override void OnInspectorGUI()
        {
            //属性参数
            // 1. 先同步最新数据
            serializedObject.Update();
            switcher = (SceneConfigSwitcher)target;
            
            // var _exportAction = new SceneConfigExporter.SceneExportWindow.ExportAction();

            
            //Button size
            int width = 110;
            int height = 35;
            
            
            EditorGUILayout.BeginVertical();
            
            //定义一种标题风格
            labelStyle.wordWrap = true;
            labelStyle.fontStyle = FontStyle.Bold;
            EditorGUILayout.LabelField("当前场景:" + switcher.sceneName, labelStyle);
            




            // 2. 获取要操作的属性
            SerializedProperty SceneConfig = serializedObject.FindProperty("currentSceneConfig");
            SerializedProperty SceneConfigProfile = serializedObject.FindProperty("sceneConfigProfile");
            SerializedProperty LightingAsset = serializedObject.FindProperty("currentLightingAsset");
            SerializedProperty VolumeProfile = serializedObject.FindProperty("currentVolumeProfile");
            SerializedProperty ShadowConfig = serializedObject.FindProperty("currentShadowConfig");
            SerializedProperty FogAsset = serializedObject.FindProperty("currentFogAsset");

            // 3. 绘制编辑器控件并修改属性
            EditorGUILayout.PropertyField(SceneConfig);
            EditorGUI.indentLevel++; //缩进
            EditorGUILayout.PropertyField(SceneConfigProfile);

            EditorGUI.indentLevel++;
            EditorGUI.BeginDisabledGroup(switcher.sceneConfigProfile != null);
            EditorGUILayout.PropertyField(LightingAsset);
            EditorGUILayout.PropertyField(VolumeProfile);
            EditorGUI.EndDisabledGroup();

            EditorGUILayout.PropertyField(ShadowConfig);
            EditorGUILayout.PropertyField(FogAsset);

            EditorGUI.indentLevel--;
            EditorGUI.indentLevel--;

            // base.DrawDefaultInspector();

            EditorGUILayout.Space(20);
            
            EditorGUILayout.LabelField(string.Empty, preSliderStyle);
            ////////////////////////////////////////////////////////////////////

            
            
            #region [生成场景运行时配置]
            EditorGUILayout.LabelField("生成场景运行时配置", labelStyle);
            
            //新增场景同名文件夹

            

            EditorGUILayout.BeginHorizontal();
            

            bool isCreate = false;
            if (GUILayout.Button("查找并创建配置",GUILayout.Height(height), GUILayout.MaxWidth(width)))
            {
                //在project的sceneConfig同目录下找对应的配置（Volume是通过找场景内组件得到源）
                switcher.Init();
                switcher.CreateSceneConfigAndProfile();
                switcher.RefreshFeatures();
                switcher.SaveDatas();
            }
            isCreate = switcher.currentSceneConfig == null;


            GUILayout.FlexibleSpace();
            
            if (GUILayout.Button("读取当前Profile", GUILayout.Height(height), GUILayout.MaxWidth(width)))
            {
                switcher.Init();
                switcher.ReadConfigProfile();
            }

            GUILayout.FlexibleSpace();
            
            
            

            //从点击 创建配置 到 点击 更新配置 之间 美术可以自己进行编辑和修改switcher上的配置
            
            EditorGUI.BeginDisabledGroup(isCreate);
            if (GUILayout.Button("更新配置",GUILayout.Height(height), GUILayout.MaxWidth(width)))
            {
                switcher.UpdateCameraValidRect(switcher.scene, switcher.currentSceneConfig);
                switcher.RefreshConfigs();
                switcher.RefreshFeatures();
                switcher.SaveDatas();
            }
            EditorGUI.EndDisabledGroup();

           

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.LabelField("路径:" + switcher.sceneConfigPath);


            #endregion
            
            
            

            EditorGUILayout.Space(10);
            
            EditorGUILayout.LabelField(string.Empty, preSliderStyle);
            //////////////////////////////////////////////////////////////////////////////
            
            EditorGUILayout.LabelField("生成场景运行时调用Prefab", labelStyle);

            EditorGUILayout.BeginVertical();
            switcher.ExportAction.DealWithCamera = EditorGUILayout.Toggle("是否处理相机（默认为true）", switcher.ExportAction.DealWithCamera);

            // // 导出场景
            // var scenePrefabPath = Path.Combine(switcher.SCENE_LEVELS_DIR, switcher.sceneName, switcher.sceneName + ".prefab");
            //
            //
            // var scenePrefabPath = switcher.sceneConfigPath;
            // var btnPrefabTitle = switcher.btnPrefabTitle;
            // if (File.Exists(scenePrefabPath))
            // {
            //     btnPrefabTitle = "更新Prefab";
            // }

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button(switcher.btnPrefabTitle, GUILayout.Height(height),GUILayout.MaxWidth(width+60)))
            {
                SceneConfigExporter.SceneExportWindow.ExportCurrentScene(switcher.ExportAction);
            }

            GUILayout.FlexibleSpace();
            EditorGUI.BeginDisabledGroup(switcher.btnPrefabTitle == "导出Prefab");
            if (GUILayout.Button("选中文件", GUILayout.Height(height),GUILayout.MaxWidth(width+60)))
            {
                var prefabScene = AssetDatabase.LoadAssetAtPath<GameObject>(switcher.scenePrefabPath);
                if (prefabScene != null)
                {
                    //在Project面板标记高亮显示
                    UnityEditor.EditorGUIUtility.PingObject(prefabScene);
                    //在Project面板自动选中，并在Inspector面板显示详情
                    UnityEditor.Selection.activeObject = prefabScene;
                }
            }
            EditorGUI.EndDisabledGroup();

            EditorGUILayout.EndHorizontal();

            if(switcher.btnPrefabTitle == "更新Prefab")
                EditorGUILayout.LabelField("路径:" + switcher.scenePrefabPath);

            EditorGUILayout.Space(12);
            EditorGUILayout.LabelField("tips: 如果要修改运行时config和对应profile请找策划修改配表", labelStyle);
            EditorGUILayout.EndVertical();
            /////////////////////////////////////////////////////////////

            EditorGUILayout.EndVertical();
            // 4. 将修改后的数据写回实际对象
            serializedObject.ApplyModifiedProperties();
        }
    }
}