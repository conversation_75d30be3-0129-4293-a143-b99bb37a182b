using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

public class AddSuffixLOD : MonoBehaviour
{

    [MenuItem("GameObject/AddSuffix/LOD0",false,1)]
    static void AddSuffixLOD0()
    {
        AddSuffix("_lod0");
    }
    
    [MenuItem("GameObject/AddSuffix/LOD1",false,1)]
    static void AddSuffixLOD1()
    {
        AddSuffix("_lod1");
    }
    
    [MenuItem("GameObject/AddSuffix/LOD2",false,1)]
    static void AddSuffixLOD2()
    {
        AddSuffix("_lod2");
    }
    
    static void AddSuffix(string suffix)
    {
        foreach (GameObject obj in Selection.gameObjects)
        {
            string originalName = obj.name;
            if (!originalName.EndsWith(suffix))
                obj.name = originalName + suffix;
        }
    }

}