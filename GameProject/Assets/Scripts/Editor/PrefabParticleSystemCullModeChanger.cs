using UnityEngine;
using UnityEditor;

public class PrefabParticleSystemCullModeChanger
{
    private static ParticleSystemCullingMode cullMode;
    
    [MenuItem("Tools/TA Tools/批量修改粒子系统的Cull Mode/Automatic")]
    static void ChangeCullModeToAutomatic()
    {
        cullMode = ParticleSystemCullingMode.Automatic;
        ChangeCullMode(cullMode);
    }
    
    
    [MenuItem("Tools/TA Tools/批量修改粒子系统的Cull Mode/Pause")]
    static void ChangeCullModeToPause()
    {
        cullMode = ParticleSystemCullingMode.Pause;
        ChangeCullMode(cullMode);
    }
    
    static void ChangeCullMode(ParticleSystemCullingMode cullMode)
    {
#if UNITY_EDITOR
        // 选择要处理的prefab资源  
        Object[] selection = Selection.GetFiltered(typeof(GameObject), SelectionMode.DeepAssets);

        foreach (Object obj in selection)
        {
            if (obj is GameObject prefabGO)
            {
                // 实例化Prefab  
                GameObject prefabInstance = PrefabUtility.InstantiatePrefab(prefabGO) as GameObject;

                // 遍历prefab实例的所有ParticleSystem组件  
                ParticleSystem[] particleSystems = prefabInstance.GetComponentsInChildren<ParticleSystem>(true);
                foreach (ParticleSystem ps in particleSystems)
                {
                    // 设置Cull Mode为Pause  
                    var mainModule = ps.main;
                    mainModule.cullingMode = cullMode;
                }

                // 应用更改并保存Prefab  
                string prefabPath = AssetDatabase.GetAssetPath(prefabGO);
                PrefabUtility.SaveAsPrefabAsset(prefabInstance, prefabPath);

                // 销毁临时的prefab实例  
                UnityEngine.Object.DestroyImmediate(prefabInstance);

                // 刷新编辑器以显示更改  
                AssetDatabase.Refresh();
            }
        }
       
#endif
    }
}