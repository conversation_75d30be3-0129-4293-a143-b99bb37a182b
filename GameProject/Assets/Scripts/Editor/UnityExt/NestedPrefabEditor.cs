using System;
using Sultan.Core;
using Sultan.Test;
using UnityEditor;
using UnityEngine;

namespace Sultan.Editor.UnityExt
{
    [CustomEditor(typeof(NestedPrefab))]
    public class NestedPrefabEditor : UnityEditor.Editor
    {
        GameObject _nestedPrefab;
        private SerializedProperty _runtimeAssetPath;
        private SerializedProperty _EditorAssetPath;
        private SerializedProperty _EditorAssetGUID;
        public void OnEnable()
        {
            NestedPrefab prefab = (NestedPrefab)target;
            _nestedPrefab = prefab.GetNestedPrefab();
            _runtimeAssetPath = serializedObject.FindProperty("RuntimeAssetPath");
            _EditorAssetPath = serializedObject.FindProperty("EditorAssetPath");
            CreateEditorNested();
            // _EditorAssetGUID =serializedObject.FindProperty("EditorAssetGUID");
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            // EditorGUILayout.PropertyField(_runtimeAssetPath);
            // EditorGUILayout.PropertyField(_EditorAssetPath);
            // EditorGUILayout.PropertyField(_EditorAssetGUID);
            if (Application.isPlaying)
                return;
            NestedPrefab prefab = (NestedPrefab)target; 
            EditorGUI.BeginChangeCheck();
            // EditorGUILayout.PropertyField(m_AssetPath);
            _nestedPrefab = (GameObject)EditorGUILayout.ObjectField("NestedObject", _nestedPrefab, typeof(GameObject), true);
            if (EditorGUI.EndChangeCheck())
            {
                var assetPath = AssetDatabase.GetAssetPath(_nestedPrefab);
                var guid = AssetDatabase.GUIDFromAssetPath(assetPath);
                RefreshAssetPath(prefab, guid.ToString());
                // prefab.CreateEditorNested();
                CreateEditorNested();
                EditorUtility.SetDirty(target);
            }
            
            GUILayout.BeginHorizontal();
            if(GUILayout.Button("刷新"))
                CreateEditorNested();
            
            var tips = prefab.ShowNestedObject ? "隐藏子节点" : "显示子节点";
            if(GUILayout.Button(tips))
                prefab.ShowNestedObject = !prefab.ShowNestedObject;
            
            GUILayout.EndHorizontal();
            
        }
        
        public void CreateEditorNested()
        {
#if UNITY_EDITOR
            NestedPrefab prefab = (NestedPrefab)target; 
            if (prefab.NestedObject != null)
            {
                DestroyImmediate(prefab.NestedObject);
            }
            
            // if (_nestedObject == null)
            {
                var _nestedObject = new GameObject("NestedObject");
                _nestedObject.SetParent(prefab.transform, false);
                prefab.NestedObject = _nestedObject;
                prefab.ShowNestedObject = prefab.ShowNestedObject;
                // _nestedObject.hideFlags = HideFlags.DontSaveInEditor ;
            }

            if (prefab.GetNestedPrefab())
            {
                prefab.SetNestedPrefab(null);
            }

            RefreshAssetPath(prefab, prefab.EditorAssetGUID);
            if (String.IsNullOrEmpty(prefab.EditorAssetGUID))
            {
                return;
            }
            
            var prefabOjb = AssetDatabase.LoadAssetAtPath<GameObject>(prefab.EditorAssetPath);
            var tmp = Instantiate(prefabOjb, prefab.NestedObject.transform, false);
            prefab.SetNestedPrefab(prefabOjb);
            _nestedPrefab = prefabOjb;
            PrefabUtility.ConnectGameObjectToPrefab(tmp, prefab.GetNestedPrefab());
#endif
        }
        
        public static void RefreshAssetPath(NestedPrefab prefab, string assetGuid)
        {
            prefab.EditorAssetGUID = assetGuid;
            prefab.EditorAssetPath = AssetDatabase.GUIDToAssetPath(prefab.EditorAssetGUID);
            prefab.RuntimeAssetPath = prefab.EditorAssetPath.Replace("Assets/ArtTmp/", "").Replace("Assets/Res/", "");
        }

        
        [MenuItem("Tools/TA Tools/更新所有嵌套Prefab路径")]
        public static void FixAllNestedPrefab()
        {
            var guids = AssetDatabase.FindAssets("t:Prefab",new string[]
            {
                "Assets/Res",
                "Assets/ArtTmp",
            });

            foreach (var guid in guids)
            {
                var prefabPath = AssetDatabase.GUIDToAssetPath(guid);
                var prefabRoot = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                var nestedPrefabs = prefabRoot.GetComponentsInChildren<NestedPrefab>(true);
                bool change = false;
                foreach (var nestedPrefab in nestedPrefabs)
                {
                    var nestedTargetPath = AssetDatabase.GUIDToAssetPath(nestedPrefab.EditorAssetGUID);
                    if (nestedPrefab.EditorAssetPath != nestedTargetPath)
                    {
                        RefreshAssetPath(nestedPrefab, nestedPrefab.EditorAssetGUID);
                        change = true;
                        EditorUtility.SetDirty(prefabRoot);
                        Debug.Log($"prefab {prefabPath} nestedPrefab {nestedPrefab.name} assetPath changeTo {nestedTargetPath}");
                    }
                }

                if (change)
                {
                    PrefabUtility.SavePrefabAsset(prefabRoot);
                }
            }

            Resources.UnloadUnusedAssets();
        }
    }
}