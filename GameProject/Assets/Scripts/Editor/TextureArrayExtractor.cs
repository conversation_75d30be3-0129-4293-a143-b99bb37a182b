using System;
using UnityEngine;
using UnityEditor;
using System.Linq;
using System.IO;


#if UNITY_EDITOR
public enum TextureArrayMode
{
    ThirteenTile,
    SevenTile,
}

/// <summary>
/// 用于从输入的Texture2dArray对象中抽取对应索引的图存成新的Texture2dArray,输出的路径默认是Source Texture2dArray
/// </summary>
public class TextureArrayExtractor : EditorWindow
{

    public Texture2DArray sourceTextureArray;
    public string outputName = "";
    public string outputPath = "Assets/";
    // public string indicesInput = "32,38,33,2,10,34,4,12,36,3,18,20,11"; // 默认的
    public TextureArrayMode TextureArrayMode;
    
    private string[] displayedOptions = {"13 Tile","7 Tile"};
    private string indicesInput; //反转后拼接地砖的
    
    [MenuItem("Assets/TA Tools/提取Texture Array为新Array", true)]
    private static bool ValidateExtractFromSelected()
    {
        return Selection.activeObject is Texture2DArray;
    }

    [MenuItem("Assets/TA Tools/提取Texture Array为新Array")]
    public static void ShowWindow()
    {
        var window = GetWindow<TextureArrayExtractor>("Texture Array Extractor");
        window.minSize  = new Vector2(510, 210);
        window.maxSize  = window.minSize; 
        window.sourceTextureArray = Selection.activeObject as Texture2DArray;
        if (window.sourceTextureArray != null)
        {
            window.outputName = window.sourceTextureArray.name;
        }
        

    }

    private void OnGUI()
    {
        GUILayout.Label("Texture Array Extractor", EditorStyles.boldLabel); 

        sourceTextureArray = (Texture2DArray)EditorGUILayout.ObjectField("Source Texture2DArray", sourceTextureArray, typeof(Texture2DArray), false);
        
        if (sourceTextureArray != null)
        {
            var assetPath = AssetDatabase.GetAssetPath(sourceTextureArray);
            outputPath = Path.GetDirectoryName(assetPath)?.Replace("\\", "/") + "/";
            EditorGUILayout.LabelField("OutPut Path", outputPath);
            outputName = EditorGUILayout.TextField("Output Name", outputName);
        }

        EditorGUILayout.Space(12);

        TextureArrayMode = (TextureArrayMode)EditorGUILayout.Popup("Selected Tile Mode:",(int)TextureArrayMode,displayedOptions);
        switch (TextureArrayMode)
        {
            case TextureArrayMode.SevenTile:
                indicesInput = "5,0,15,8,4,9,10";
                break;
            case TextureArrayMode.ThirteenTile:
                indicesInput = "31,25,30,61,53,29,59,51,27,60,45,43,52";
                break;
        }

        // indicesInput = EditorGUILayout.TextField("Selected Indices [,]", indicesInput);
        EditorGUILayout.Space(20);
        if (sourceTextureArray != null && GUILayout.Button("Extract and Save"))
        {
            ExecuteExtraction();
        }
    }
    

    private void ExecuteExtraction()
    {
        if (sourceTextureArray == null || string.IsNullOrEmpty(outputName) || string.IsNullOrEmpty(outputPath) || string.IsNullOrEmpty(indicesInput))
        {
            Debug.LogError("Please fill all the fields.");
            return;
        }

        // 确保输出路径以斜杠结尾
        if (!outputPath.EndsWith("/")) outputPath += "/";

        // 解析索引输入
        var selectedIndices = indicesInput.Split(new char[] { ',' }, System.StringSplitOptions.RemoveEmptyEntries)
                                          .Select(str => int.Parse(str.Trim()))
                                          .ToArray();

        // 验证索引是否有效
        foreach (var index in selectedIndices)
        {
            if (index < 0 || index >= sourceTextureArray.depth) 
            {
                Debug.LogError($"Index {index} is out of bounds.");
                return;
            }
        }

        // 检查目标文件是否存在，存在则删除
        string assetPath = outputPath + outputName + ".asset";
        
        // 检查现有资源
        Texture2DArray existingArray = AssetDatabase.LoadAssetAtPath<Texture2DArray>(assetPath);
        if (existingArray != null && sourceTextureArray.depth > selectedIndices.Length)
        {
            // 如果深度相同，直接覆盖内容
            if (existingArray.depth == selectedIndices.Length)
            {
                for (int i = 0; i < selectedIndices.Length; i++)
                {
                    Graphics.CopyTexture(sourceTextureArray, selectedIndices[i], existingArray, i);
                }
                EditorUtility.SetDirty(existingArray);
                AssetDatabase.SaveAssets();
                Debug.Log("已更新现有Texture2DArray: " + assetPath);
                AssetDatabase.Refresh();
                return;
            }
            else
            {
                // 深度不同，删除旧资源
                if (!AssetDatabase.DeleteAsset(assetPath))
                {
                    Debug.LogError("删除旧资源失败: " + assetPath);
                    return;
                }
                AssetDatabase.Refresh();
            }
        }
        
        

        // 直接复制层到新数组
        bool mipChain = sourceTextureArray.mipmapCount  > 1;
        Texture2DArray newArray = new Texture2DArray(
            sourceTextureArray.width,  
            sourceTextureArray.height,  
            selectedIndices.Length, 
            sourceTextureArray.format,  
            mipChain
        );

        if (sourceTextureArray.depth > selectedIndices.Length)
        {
            for (int i = 0; i < selectedIndices.Length; i++)
            {
                Graphics.CopyTexture(sourceTextureArray, selectedIndices[i], newArray, i);
            }
        }
        else
        {
            Debug.LogError("提取的图集索引数量不能大于源图集索引数量!");
        }

        
        AssetDatabase.CreateAsset(newArray, assetPath);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        Debug.Log("新的图集已经提取成功！");
    }
}
#endif