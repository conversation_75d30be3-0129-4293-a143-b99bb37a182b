using UnityEngine;
using UnityEngine.Rendering;

/// <summary>
/// Author:Aoicocoon
/// Adreno, Mali FrameBufferFetch Shader切换
/// UnityBug, GLSL Shader下会丢失 Object2World矩阵，需要自己传入 _FBFLocal2World
/// 已测试Mali设备 华为Mate40, 三星S9
/// </summary>

public class FBFSelector : MonoBehaviour
{
    public Shader MaliShader;

    public Shader AdrenoShader;

    public Material SelfMaterial;

    private bool m_enableMaliFBS = false;
    private Transform m_transform;

    private static int FBF_LOCAL2WORLD_MATRIX = Shader.PropertyToID("_FBFLocal2World");

    // Start is called before the first frame update
    void Start()
    {
        SelfMaterial = this.GetComponent<MeshRenderer>()?.sharedMaterial;
        m_enableMaliFBS = false;
        m_transform = this.transform;
        
        Debug.Log("FBFSelector Start!");
        
        if (!SelfMaterial) return;

        if (NativeRenderingPlugin.IsFrameBufferFetchSupported())
        {
            if (SystemInfo.graphicsDeviceType == GraphicsDeviceType.OpenGLES2 ||
                SystemInfo.graphicsDeviceType == GraphicsDeviceType.OpenGLES3)
            {
                Debug.Log("FBFSelector IsSupport FBF!");
            
                if (NativeRenderingPlugin.IsAdrenoDevice())
                {
                    SelfMaterial.shader = AdrenoShader;
                    Debug.Log("FBFSelector is Adreno");
                }

                if (NativeRenderingPlugin.IsMaliDevice() && MaliShader.isSupported)
                {
                    SelfMaterial.shader = MaliShader;
                    m_enableMaliFBS = true;
                    Debug.Log("FBFSelector is Mali");
                    Debug.Log(MaliShader.name);
                }
            }
        }
        else
        {
            Debug.Log("FBFSelector Not Support FBF!");
        }
    }

    private Matrix4x4 lastMat;
    void Update()
    {
        if (m_enableMaliFBS && m_transform && m_transform.localToWorldMatrix != lastMat)
        {
            lastMat = m_transform.localToWorldMatrix;
            UpdateMatrix();
        }
    }

    private void OnTransformParentChanged()
    {
        UpdateMatrix();
    }

    void UpdateMatrix()
    {
        if (m_enableMaliFBS)
        {
            if (SelfMaterial && m_transform)
            {
                SelfMaterial.SetMatrix(FBF_LOCAL2WORLD_MATRIX, m_transform.localToWorldMatrix);
            }
        }
    }

    // private void OnGUI()
    // {
    //     if (GUI.Button(new Rect(50, 50, 100, 100), "Click"))
    //     {
    //         DoSwitch();
    //     }
    // }
}
