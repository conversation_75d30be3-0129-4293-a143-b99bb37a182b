using System;
using System.Collections.Generic;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Jobs;
using UnityEngine;
using UnityEngine.Rendering;

public class SimpleBRGExample : MonoBehaviour
{
    public Mesh mesh;
    public Material material;
    public Mesh mesh1;
    public Material material1;

    [Header("Debug Options")]
    public bool useBRG = true;
    public bool createTraditionalObjects = true;

    private BatchRendererGroup m_BRG;

    // Some helper constants to make calculations more convenient.
    private const int kSizeOfMatrix = sizeof(float) * 4 * 4;
    private const int kSizeOfPackedMatrix = sizeof(float) * 4 * 3;
    private const int kSizeOfFloat4 = sizeof(float) * 4;
    private const int kBytesPerInstance = (kSizeOfPackedMatrix * 2) + kSizeOfFloat4;
    private const int kExtraBytes = kSizeOfMatrix * 2;
    private const int kNumInstances = 10;


    class CustomRenderData
    {
        public Mesh mesh;
        public Material material;
        public Matrix4x4 localToWorldMatrix;
        public int renderOrder;
        public BatchID BatchId;
        public BatchMeshID meshId;
        public BatchMaterialID materialId;
        public GraphicsBuffer instanceData; // Each render data has its own buffer
    }
    
    List<CustomRenderData> _listRenderData = new List<CustomRenderData>();

    // The PackedMatrix is a convenience type that converts matrices into
    // the format that Unity-provided SRP shaders expect.
    struct PackedMatrix
    {
        public float c0x;
        public float c0y;
        public float c0z;
        public float c1x;
        public float c1y;
        public float c1z;
        public float c2x;
        public float c2y;
        public float c2z;
        public float c3x;
        public float c3y;
        public float c3z;

        public PackedMatrix(Matrix4x4 m)
        {
            c0x = m.m00;
            c0y = m.m10;
            c0z = m.m20;
            c1x = m.m01;
            c1y = m.m11;
            c1z = m.m21;
            c2x = m.m02;
            c2y = m.m12;
            c2z = m.m22;
            c3x = m.m03;
            c3y = m.m13;
            c3z = m.m23;
        }
    }

    private void Start()
    {
        Debug.Log("=== SimpleBRGExample Start ===");
        if (material != null)
        {
            material.EnableKeyword("DOTS_INSTANCING_ON");
            Debug.Log($"Enabled DOTS_INSTANCING_ON for material: {material.name}");
        }
        if (material1 != null)
        {
            material1.EnableKeyword("DOTS_INSTANCING_ON");
            Debug.Log($"Enabled DOTS_INSTANCING_ON for material1: {material1.name}");
        }

        //构造数据 - 确保对象在相机前方
        var matrices = new Matrix4x4[kNumInstances];
        float startX = -0.5f * (kNumInstances - 1) / 2;
        float offset = 0.5f;
        for (int i = 0; i < kNumInstances; i++)
        {
            matrices[i] = Matrix4x4.TRS(new Vector3(startX + i * offset, 0, 0), Quaternion.Euler(new Vector3(90f, 0, 0)), Vector3.one);
        }

        Debug.Log("Created transformation matrices:");
        var mat = Resources.Load<Material>("img");
        if (mat == null)
        {
            Debug.LogError("mat is null");
        }

        var mat1 = Resources.Load<Material>("img1");
        if (mat1 == null)
        {
            Debug.LogError("mat1 is null");
        }
        for (int i = 0; i < kNumInstances; i++)
        {
            var data = new CustomRenderData();
            data.mesh = CreateQuadMesh();
            data.material = i % 2 == 0 ? mat : mat1;
            data.localToWorldMatrix = matrices[i];
            data.renderOrder = i;
            _listRenderData.Add(data);

            Debug.Log($"Created render data {i}: position {matrices[i].GetColumn(3)}, material: {data.material.name}");
        }
        if (useBRG)
        {
            m_BRG = new BatchRendererGroup(this.OnPerformCulling, IntPtr.Zero);

            Debug.Log($"Created {_listRenderData.Count} render data objects");

            foreach (var renderData in _listRenderData)
            {
                renderData.meshId = m_BRG.RegisterMesh(renderData.mesh);
                renderData.materialId = m_BRG.RegisterMaterial(renderData.material);

                Debug.Log($"Registered mesh: {renderData.meshId}, material: {renderData.materialId} ({renderData.material.name})");
                Debug.Log($"Mesh vertices: {renderData.mesh.vertexCount}, triangles: {renderData.mesh.triangles.Length/3}");
            }

            PopulateInstanceDataBuffer1();
        }
    }


    private void CheckCameraSetup()
    {
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            Debug.LogError("No main camera found!");
            return;
        }

        Debug.Log($"Main camera position: {mainCamera.transform.position}");
        Debug.Log($"Main camera rotation: {mainCamera.transform.rotation.eulerAngles}");
        Debug.Log($"Main camera forward: {mainCamera.transform.forward}");
        Debug.Log($"Main camera near plane: {mainCamera.nearClipPlane}, far plane: {mainCamera.farClipPlane}");

        // 检查对象是否在相机视野内
        foreach (var renderData in _listRenderData)
        {
            Vector3 objectPos = renderData.localToWorldMatrix.GetColumn(3);
            Vector3 viewportPoint = mainCamera.WorldToViewportPoint(objectPos);
            Debug.Log($"Object at {objectPos} -> Viewport: {viewportPoint} (visible: {viewportPoint.z > 0 && viewportPoint.x >= 0 && viewportPoint.x <= 1 && viewportPoint.y >= 0 && viewportPoint.y <= 1})");
        }
    }

    private Mesh CreateQuadMesh()
    {
        // 总是创建新的 mesh，不使用缓存
        // if (true)
        // {
        //     return this.mesh1;
        // }
        // 1. 创建顶点数据（4个顶点）
        Vector3[] vertices = new Vector3[4]
        {
            new Vector3(-0.5f, -0.5f, 0), // 左下角
            new Vector3(0.5f, -0.5f, 0),  // 右下角
            new Vector3(-0.5f, 0.5f, 0),  // 左上角
            new Vector3(0.5f, 0.5f, 0)    // 右上角
        };

        // 2. 定义三角形索引（2个三角形组成一个面）
        int[] triangles = new int[6]
        {
            0, 1, 2, // 第一个三角形
            1, 3, 2  // 第二个三角形
        };

        // 3. UV坐标映射（0-1范围对应纹理）
        Vector2[] uv = new Vector2[4]
        {
            new Vector2(0, 0), // 左下角对应纹理左下
            new Vector2(1, 0), // 右下角对应纹理右下
            new Vector2(0, 1), // 左上角对应纹理左上
            new Vector2(1, 1)  // 右上角对应纹理右上
        };

        // 4. 创建Mesh并赋值
        Mesh mesh = new Mesh();
        mesh.vertices = vertices;
        mesh.triangles = triangles;
        mesh.uv = uv;

        return mesh;
    }

    private void PopulateInstanceDataBuffer1()
    {
        //每个单独创建一个batch
        for (int i = 0; i < _listRenderData.Count; i++)
        {
            PopulateInstanceDataBuffer(i, _listRenderData[i]);
        }
    }
    
    private void PopulateInstanceDataBuffer(int startIndex, CustomRenderData renderData)
    {
        // Create a separate GraphicsBuffer for this render data object
        renderData.instanceData = new GraphicsBuffer(GraphicsBuffer.Target.Raw,
            BufferCountForInstances(kBytesPerInstance, 1, kExtraBytes), // Only 1 instance per buffer
            sizeof(int));

        // Place a zero matrix at the start of the instance data buffer, so loads from address 0 return zero.
        var zero = new Matrix4x4[1] { Matrix4x4.zero };

        // Create transform matrix for this single instance.
        var matrices = new Matrix4x4[1]
        {
            renderData.localToWorldMatrix,
        };

        // Convert the transform matrices into the packed format that shaders expects.
        var objectToWorld = new PackedMatrix[1]
        {
            new PackedMatrix(matrices[0]),
        };

        // Also create packed inverse matrices.
        var worldToObject = new PackedMatrix[1]
        {
            new PackedMatrix(matrices[0].inverse),
        };

        // Make all instances have unique colors based on startIndex.
        var colors = new Vector4[1]
        {
            startIndex == 0 ? new Vector4(1, 0, 0, 1) : // Red
            startIndex == 1 ? new Vector4(0, 1, 0, 1) : // Green
                              new Vector4(0, 0, 1, 1),  // Blue
        };

        // In this example, the instance data is placed into the buffer like this:
        // Offset | Description
        //      0 | 64 bytes of zeroes, so loads from address 0 return zeroes
        //     64 | 32 uninitialized bytes to make working with SetData easier, otherwise unnecessary
        //     96 | unity_ObjectToWorld, one packed float3x4 matrix
        //    144 | unity_WorldToObject, one packed float3x4 matrix
        //    192 | _BaseColor, one float4

        // Calculates start addresses for the different instanced properties. unity_ObjectToWorld starts at
        // address 96 instead of 64 which means 32 bits are left uninitialized. This is because the
        // computeBufferStartIndex parameter requires the start offset to be divisible by the size of the source
        // array element type. In this case, it's the size of PackedMatrix, which is 48.
        uint byteAddressObjectToWorld = kSizeOfPackedMatrix * 2;
        uint byteAddressWorldToObject = byteAddressObjectToWorld + kSizeOfPackedMatrix * 1;
        uint byteAddressColor = byteAddressWorldToObject + kSizeOfPackedMatrix * 1;

        // Upload the instance data to the GraphicsBuffer so the shader can load them.
        renderData.instanceData.SetData(zero, 0, 0, 1);
        renderData.instanceData.SetData(objectToWorld, 0, (int)(byteAddressObjectToWorld / kSizeOfPackedMatrix), objectToWorld.Length);
        renderData.instanceData.SetData(worldToObject, 0, (int)(byteAddressWorldToObject / kSizeOfPackedMatrix), worldToObject.Length);
        renderData.instanceData.SetData(colors, 0, (int)(byteAddressColor / kSizeOfFloat4), colors.Length);

        // Set up metadata values to point to the instance data. Set the most significant bit 0x80000000 in each
        // which instructs the shader that the data is an array with one value per instance, indexed by the instance index.
        var metadata = new NativeArray<MetadataValue>(3, Allocator.Temp);
        metadata[0] = new MetadataValue { NameID = Shader.PropertyToID("unity_ObjectToWorld"), Value = 0x80000000 | byteAddressObjectToWorld, };
        metadata[1] = new MetadataValue { NameID = Shader.PropertyToID("unity_WorldToObject"), Value = 0x80000000 | byteAddressWorldToObject, };
        metadata[2] = new MetadataValue { NameID = Shader.PropertyToID("_BaseColor"), Value = 0x80000000 | byteAddressColor, };

        // Finally, create a batch for the instance, and make the batch use the GraphicsBuffer with the
        // instance data, as well as the metadata values that specify where the properties are.
        renderData.BatchId = m_BRG.AddBatch(metadata, renderData.instanceData.bufferHandle);
    }



    // Raw buffers are allocated in ints. This is a utility method that calculates
    // the required number of ints for the data.
    int BufferCountForInstances(int bytesPerInstance, int numInstances, int extraBytes = 0)
    {
        // Round byte counts to int multiples
        bytesPerInstance = (bytesPerInstance + sizeof(int) - 1) / sizeof(int) * sizeof(int);
        extraBytes = (extraBytes + sizeof(int) - 1) / sizeof(int) * sizeof(int);
        int totalBytes = bytesPerInstance * numInstances + extraBytes;
        return totalBytes / sizeof(int);
    }


    private void OnDisable()
    {
        // Dispose all individual GraphicsBuffers
        foreach (var renderData in _listRenderData)
        {
            renderData.instanceData?.Dispose();
        }

        if (m_BRG != null)
        {
            m_BRG.Dispose();
        }
    }

    public unsafe JobHandle OnPerformCulling(
        BatchRendererGroup rendererGroup,
        BatchCullingContext cullingContext,
        BatchCullingOutput cullingOutput,
        IntPtr userContext)
    {
        // Debug.Log($"OnPerformCulling called with {_listRenderData.Count} render data objects");

        // UnsafeUtility.Malloc() requires an alignment, so use the largest integer type's alignment
        // which is a reasonable default.
        int alignment = UnsafeUtility.AlignOf<long>();

        // Acquire a pointer to the BatchCullingOutputDrawCommands struct so you can easily
        // modify it directly.
        var drawCommands = (BatchCullingOutputDrawCommands*)cullingOutput.drawCommands.GetUnsafePtr();

        // Calculate the number of draw commands needed - one for each render data object
        int numDrawCommands = _listRenderData.Count;
        int numVisibleInstances = _listRenderData.Count; // Each batch has 1 instance

        // Debug.Log($"Creating {numDrawCommands} draw commands, {numVisibleInstances} visible instances");

        // Allocate memory for the output arrays. We need:
        // - one draw command for each render data object (each has its own batch)
        // - a single draw range that covers all draw commands
        // - one visible instance index for each render data object
        // You must always allocate the arrays using Allocator.TempJob.
        drawCommands->drawCommands = (BatchDrawCommand*)UnsafeUtility.Malloc(
            UnsafeUtility.SizeOf<BatchDrawCommand>() * numDrawCommands, alignment, Allocator.TempJob);
        drawCommands->drawRanges = (BatchDrawRange*)UnsafeUtility.Malloc(
            UnsafeUtility.SizeOf<BatchDrawRange>(), alignment, Allocator.TempJob);
        drawCommands->visibleInstances = (int*)UnsafeUtility.Malloc(
            numVisibleInstances * sizeof(int), alignment, Allocator.TempJob);
        drawCommands->drawCommandPickingInstanceIDs = null;

        drawCommands->drawCommandCount = numDrawCommands;
        drawCommands->drawRangeCount = 1;
        drawCommands->visibleInstanceCount = numVisibleInstances;

        // This example doesn't use depth sorting, so it leaves instanceSortingPositions as null.
        drawCommands->instanceSortingPositions = null;
        drawCommands->instanceSortingPositionFloatCount = 0;

        // Configure each draw command for each render data object
        for (int i = 0; i < _listRenderData.Count; i++)
        {
            var renderData = _listRenderData[i];

            // Each draw command draws 1 instance (since each batch contains only 1 instance)
            // The visible offset points to the corresponding index in the visibleInstances array
            drawCommands->drawCommands[i].visibleOffset = (uint)i;
            drawCommands->drawCommands[i].visibleCount = 1;
            drawCommands->drawCommands[i].batchID = renderData.BatchId;
            drawCommands->drawCommands[i].materialID = renderData.materialId;
            drawCommands->drawCommands[i].meshID = renderData.meshId;
            drawCommands->drawCommands[i].submeshIndex = 0;
            drawCommands->drawCommands[i].splitVisibilityMask = 0xff;
            drawCommands->drawCommands[i].flags = 0;
            drawCommands->drawCommands[i].sortingPosition = renderData.renderOrder + 10000;
        }

        // Configure the single draw range to cover all draw commands
        drawCommands->drawRanges[0].drawCommandsBegin = 0;
        drawCommands->drawRanges[0].drawCommandsCount = (uint)numDrawCommands;

        // This example doesn't care about shadows or motion vectors, so it leaves everything
        // at the default zero values, except the renderingLayerMask which it sets to all ones
        // so Unity renders the instances regardless of mask settings.
        drawCommands->drawRanges[0].filterSettings = new BatchFilterSettings { renderingLayerMask = 0xffffffff, };

        // Write the visible instance indices. Since each batch has only 1 instance,
        // each visible instance index is 0 (the first and only instance in each batch).
        for (int i = 0; i < numVisibleInstances; ++i)
            drawCommands->visibleInstances[i] = 0;

        // This simple example doesn't use jobs, so it returns an empty JobHandle.
        // Performance-sensitive applications are encouraged to use Burst jobs to implement
        // culling and draw command output. In this case, this function returns a
        // handle here that completes when the Burst jobs finish.
        return new JobHandle();
    }
}