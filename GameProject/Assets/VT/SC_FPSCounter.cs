using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SC_FPSCounter : MonoBehaviour
{
    /* Assign this script to any object in the Scene to display frames per second */

    public float updateInterval = 0.5f; //How often should the number update

    float accum = 0.0f;
    int frames = 0;
    float timeleft;
    float fps;
    float minFps;
    float maxFps;

    private float minFloatLeft = 5;

    GUIStyle textStyle = new GUIStyle();

    // Use this for initialization
    void Start()
    {
        timeleft = updateInterval;

        textStyle.fontStyle = FontStyle.Bold;
        textStyle.normal.textColor = Color.red;
        textStyle.fontSize = 24;

        minFps = float.MaxValue;
    }

    // Update is called once per frame
    void Update()
    {
        timeleft -= Time.deltaTime;
        accum += Time.timeScale / Time.deltaTime;
        ++frames;

        // Interval ended - update GUI text and start new interval
        if (timeleft <= 0.0)
        {
            // display two fractional digits (f2 format)
            fps = (accum / frames);
            timeleft = updateInterval;
            accum = 0.0f;
            frames = 0;

            maxFps = Mathf.Max(fps, maxFps);
            minFps = Mathf.Min(fps, minFps);
        }

        minFloatLeft -= Time.deltaTime;
        if (minFloatLeft <= 0)
        {
            minFps = fps;
            minFloatLeft = 5;
        }
    }

    void OnGUI()
    {
        //Display the fps and round to 2 decimals
        GUI.Label(new Rect(5, 5, 100, 25), fps.ToString("F2") + "FPS", textStyle);
        GUI.Label(new Rect(5, 25, 100, 25), maxFps.ToString("F2") + "MaxFPS", textStyle);
        GUI.Label(new Rect(5, 45, 100, 25), minFps.ToString("F2") + "MinFPS", textStyle);
    }
}