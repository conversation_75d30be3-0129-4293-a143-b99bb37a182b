{"appConfigName": "koh", "appInfo": {"appId": "*********", "appKey": "c350813b0b965a8c477739d488ea811c", "language": "ar", "channel": "googleplay", "screenOrientation": "portrait"}, "manifestPlaceHolder": {"onemtSdkScreenOrientation": "portrait", "onemtSdkConfigChanges": "locale|layoutDirection", "onemtSdkWebViewScreenOrientation": "portrait"}, "modules": ["accountBase", "accountEmail", "accountGoogle", "accountFacebook", "pushBase", "pushFirebase", "reportBase", "reportCtk", "reportFirebase", "reportFacebook", "reportAdjust", "social", "networkAnalytics", "accountMobile"], "dependencies": {"onemtSdk": ["com.onemt.sdk.business:share-whatsapp:3.19.0", "com.onemt.sdk.business:terms:3.19.0", "com.onemt.sdk.business:report-ctk:3.19.0", "com.onemt.sdk.business:entry:3.19.0", "com.onemt.sdk.business:share-more:3.19.0", "com.onemt.sdk.business:report-firebase:3.19.0", "com.onemt.sdk.business:report-facebook:3.19.0", "com.onemt.sdk.business:share-facebook:3.19.0", "com.onemt.sdk.business:account-email:3.19.0", "com.onemt.sdk.business:push-firebase:3.19.0", "com.onemt.sdk.business:launch-picture-portrait:3.19.0", "com.onemt.sdk.business:avatar:3.19.0", "com.onemt.sdk.business:network-analytics:3.19.0", "com.onemt.sdk.business:resources-common:3.19.0", "com.onemt.sdk.business:account-google:3.19.0", "com.onemt.sdk.business:voice-msg:3.19.0", "com.onemt.sdk.business:account-mobile:3.19.0", "com.onemt.sdk.business:social:3.19.0", "com.onemt.sdk.business:account-facebook:3.19.0", "com.onemt.sdk.business:report-adjust:3.19.0", "com.onemt.sdk.business:share-instagram:3.19.0", "com.onemt.sdk.business:voice-rt-agora:3.19.0", "com.onemt.sdk.business:launch-picture-portrait:3.19.0"], "api": ["androidx.multidex:multidex:2.0.1"], "annotationProcessor": ["com.alibaba:arouter-compiler:1.2.2"]}, "ucLoginType": ["google", "facebook", "email", "mobile"], "facebook": {"enablePhotoSharing": false, "facebookAppId": "****************", "facebookLoginProtocolScheme": "fb****************"}, "google": {"googlePlayClientId": "*************-ipkameb4ofuf3s1seff0228jhstg9m3r.apps.googleusercontent.com", "googleGamesServiceAppId": "*************"}, "firebase": {"pushSenderId": "************"}, "agora": {"appId": "6c66e4c74d1b4da2b3db76de5d79dda4"}, "adjust": {"enableImei": false, "appSecretInfo4": *********, "appSecretInfo3": *********, "appSecretInfo2": **********, "appSecretInfo1": **********, "appSecretId": 1, "appToken": "pw2e9vb0wsu8"}, "adjustReport": {"config": {"appSecretId": 1, "appSecretInfo1": **********, "appSecretInfo2": **********, "appSecretInfo3": *********, "appSecretInfo4": *********, "appToken": "pw2e9vb0wsu8"}, "eventStatus": {"Activation": false, "Alliance": false, "CancelPay": true, "ConsumerGold": false, "Day2Active": false, "Day3Active": false, "Day7Active": false, "EnterGameStore": false, "EnterGiftBagStore": false, "FinishGuide": true, "GetGift": false, "JoinGroup": false, "LevelUp": true, "MayPay": true, "Online": true, "Pay": true, "Pvp": false, "Register": true, "Share": false, "StartCheckOut": false}, "eventToken": {"Activation": "", "Alliance": "", "CancelPay": "4q2tnl", "ConsumerGold": "", "Day2Active": "", "Day3Active": "", "Day7Active": "", "EnterGameStore": "", "EnterGiftBagStore": "", "FinishGuide": "n9c82q", "GetGift": "", "JoinGroup": "", "LevelUp": "it4j2f", "LevelUp10": "s5op74", "LevelUp11": "lk7a6g", "LevelUp13": "rvls88", "LevelUp15": "gf5xgm", "LevelUp16": "8dcpd9", "LevelUp19": "6h57cw", "LevelUp20": "m2ru3y", "LevelUp25": "y66r0s", "LevelUp3": "197wjn", "LevelUp30": "qigvam", "LevelUp5": "pj5saw", "LevelUp6": "wh6xo1", "MayPay": "pvqubr", "Online": "4r9f5e", "Pay": "b4lnvd", "Pvp": "", "Register": "n02i2g", "Share": "", "StartCheckOut": ""}}, "facebookReport": {"eventStatus": {"Activation": true, "Alliance": true, "CancelPay": true, "ConsumerGold": true, "Day2Active": true, "Day3Active": true, "Day7Active": true, "EnterGameStore": true, "EnterGiftBagStore": true, "FinishGuide": true, "GetGift": true, "JoinGroup": true, "LevelUp": true, "MayPay": true, "Online": true, "Pay": true, "Pvp": true, "Register": true, "Share": true, "StartCheckOut": true}}, "firebaseReport": {"eventStatus": {"Activation": true, "Alliance": true, "CancelPay": true, "ConsumerGold": true, "Day2Active": true, "Day3Active": true, "Day7Active": true, "EnterGameStore": true, "EnterGiftBagStore": true, "FinishGuide": true, "GetGift": true, "JoinGroup": true, "LevelUp": true, "MayPay": true, "Online": true, "Pay": true, "Pvp": true, "Register": true, "Share": true, "StartCheckOut": true}}, "urlHosts": [{"name": "sdkCommon", "url": "https://sdkcommon.menaapp.net/", "debugUrl": "https://sdkcommondebug.menaapp.net/", "betaUrl": "https://sdkcommonbeta.menaapp.net/"}, {"name": "userCenter", "url": "https://apiuc.menaapp.net/", "debugUrl": "https://apiucdebug.menaapp.net/", "betaUrl": "https://apiucbeta.menaapp.net/"}, {"name": "adReport", "url": "https://adapi.onemt.co/", "debugUrl": "https://addebug.onemt.co/", "betaUrl": "https://adbeta.onemt.co/"}, {"name": "push", "url": "https://api.push.menaapp.net/", "debugUrl": "https://apipushdebug.menaapp.net/", "betaUrl": "https://apipushbeta.menaapp.net/"}, {"name": "communityH5", "url": "https://apicosdk.onemt.co", "debugUrl": "https://apicosdkdebug.onemt.co", "betaUrl": "https://apicosdkbeta.onemt.co"}, {"name": "faqH5", "url": "https://apicssdk.onemt.co", "debugUrl": "https://apicssdkdebug.onemt.co", "betaUrl": "https://apicssdkbeta.onemt.co"}, {"name": "gameSupport", "url": "https://eventnotice.onemt.co/", "debugUrl": "https://eventtest.onemt.co/", "betaUrl": "https://eventtest.onemt.co/"}, {"name": "community", "url": "https://apicosdk.onemt.co/", "debugUrl": "https://apicosdkdebug.onemt.co/", "betaUrl": "https://apicosdkbeta.onemt.co/"}, {"name": "faq", "url": "https://apicssdk.onemt.co/v2/", "debugUrl": "https://apicssdkdebug.onemt.co/", "betaUrl": "https://apicssdkbeta.onemt.co/"}, {"name": "avatar", "url": "https://avatarapi.menaapp.net/", "debugUrl": "https://avatarapibeta.menaapp.co/", "betaUrl": "https://avatarapibeta.menaapp.co/"}, {"name": "voice", "url": "https://fileupload.onemt.co/", "debugUrl": "https://fileuploadbeta.onemt.co/", "betaUrl": "https://fileuploadbeta.onemt.co/"}, {"name": "pay", "url": "https://pay.onemt.co/", "debugUrl": "https://rechargetest.onemt.co/", "betaUrl": "https://rechargetest.onemt.co/"}, {"name": "pay_web", "url": "https://pay.onemt.co/pay/payment/recharge", "debugUrl": "https://rechargetest.onemt.co/pay/payment/recharge", "betaUrl": "https://rechargetest.onemt.co/pay/payment/recharge"}, {"name": "kafkaReport", "url": "https://gameapi.onemt.co/", "debugUrl": "https://gameapidebug.onemt.co/", "betaUrl": "https://gameapidebug.onemt.co/"}, {"name": "dataReport", "url": "https://datareport.onemt.co/", "debugUrl": "https://datareporttest.onemt.co/", "betaUrl": "https://datareporttest.onemt.co/"}, {"name": "ctkReport", "url": "https://dgapi.onemt.co/", "debugUrl": "https://dgtest.onemt.co/", "betaUrl": "https://dgtest.onemt.co/"}, {"name": "billing", "url": "https://iap.menaapp.net/", "debugUrl": "http://rechargedebug.onemt.co/", "betaUrl": "http://iapdev.menaapp.net/"}, {"name": "logReport", "url": "https://sdklog.menaapp.net/", "debugUrl": "https://sdklogdebug.menaapp.net/", "betaUrl": "https://sdklogbeta.menaapp.net/"}]}