/*
* Copyright (c) <2021> Side Effects Software Inc.
*
* Permission is hereby granted, free of charge, to any person obtaining a copy
* of this software and associated documentation files (the "Software"), to deal
* in the Software without restriction, including without limitation the rights
* to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
* copies of the Software, and to permit persons to whom the Software is
* furnished to do so, subject to the following conditions:
*
* The above copyright notice and this permission notice shall be included in all
* copies or substantial portions of the Software.
*
* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
* IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
* FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
* AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
* LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
* OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
* SOFTWARE.
*
* Produced by:
*      Side Effects Software Inc
*      123 Front Street West, Suite 1401
*      Toronto, Ontario
*      Canada   M5J 2M2
*      416-504-9876
*/


/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// WARNING! This file is automatically GENERATED.
// DO NOT modify manually or commit to the repository!
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


#if (UNITY_EDITOR_WIN || UNITY_EDITOR_OSX || UNITY_STANDALONE_LINUX)
#define HOUDINIENGINEUNITY_ENABLED
#endif

using UnityEngine;
using System;
using System.Runtime.InteropServices;

namespace HoudiniEngineUnity
{
    using HAPI_Bool = System.Boolean;
    using HAPI_UInt8 = System.Byte;
    using HAPI_Int8 = System.SByte;
    using HAPI_Int16 = System.Int16;
    using HAPI_Int64 = System.Int64;
    using HAPI_ProcessId = System.Int32;
    using HAPI_SessionId = System.Int64;
    using HAPI_StringHandle = System.Int32;
    using HAPI_AssetLibraryId = System.Int32;
    using HAPI_NodeId = System.Int32;
    using HAPI_ParmId = System.Int32;
    using HAPI_PartId = System.Int32;
    using HAPI_PDG_WorkitemId = System.Int32;
    using HAPI_PDG_GraphContextId = System.Int32;
    using HAPI_HIPFileId = System.Int32;
    using HAPI_ErrorCodeBits = System.Int32;
    using HAPI_NodeTypeBits = System.Int32;
    using HAPI_NodeFlagsBits = System.Int32;

    public static class HEU_HAPIFunctions
    {
#if HOUDINIENGINEUNITY_ENABLED
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CreateInProcessSession(
                out HAPI_Session session);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_StartThriftSocketServer(
                ref HAPI_ThriftServerOptions options,
                int port,
                out HAPI_ProcessId process_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CreateThriftSocketSession(
                out HAPI_Session session,
                byte[] host_name,
                int port);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_StartThriftNamedPipeServer(
                ref HAPI_ThriftServerOptions options,
                byte[] pipe_name,
                out HAPI_ProcessId process_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CreateThriftNamedPipeSession(
                out HAPI_Session session,
                byte[] pipe_name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_BindCustomImplementation(
                HAPI_SessionType session_type,
                byte[] dll_path);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CreateCustomSession(
                HAPI_SessionType session_type,
                byte[] session_info,
                out HAPI_Session session);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_IsSessionValid(
                ref HAPI_Session session);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CloseSession(
                ref HAPI_Session session);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_IsInitialized(
                ref HAPI_Session session);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_Initialize(
                ref HAPI_Session session,
                ref HAPI_CookOptions cook_options,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool use_cooking_thread,
                int cooking_thread_stack_size,
                byte[] houdini_environment_files,
                byte[] otl_search_path,
                byte[] dso_search_path,
                byte[] image_dso_search_path,
                byte[] audio_dso_search_path);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_Cleanup(
                ref HAPI_Session session);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_Shutdown(
                ref HAPI_Session session);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetEnvInt(
                HAPI_EnvIntType int_type,
                out int value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetSessionEnvInt(
                ref HAPI_Session session,
                HAPI_SessionEnvIntType int_type,
                out int value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetServerEnvInt(
                ref HAPI_Session session,
                byte[] variable_name,
                out int value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetServerEnvString(
                ref HAPI_Session session,
                byte[] variable_name,
                out HAPI_StringHandle value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetServerEnvVarCount(
                ref HAPI_Session session,
                out int env_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetServerEnvVarList(
                ref HAPI_Session session,
                [Out] HAPI_StringHandle[] values_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetServerEnvInt(
                ref HAPI_Session session,
                byte[] variable_name,
                int value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetServerEnvString(
                ref HAPI_Session session,
                byte[] variable_name,
                byte[] value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetStatus(
                ref HAPI_Session session,
                HAPI_StatusType status_type,
                out int status);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetStatusStringBufLength(
                ref HAPI_Session session,
                HAPI_StatusType status_type,
                HAPI_StatusVerbosity verbosity,
                out int buffer_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetStatusString(
                ref HAPI_Session session,
                HAPI_StatusType status_type,
                byte[] string_value,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ComposeNodeCookResult(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_StatusVerbosity verbosity,
                out int buffer_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetComposedNodeCookResult(
                ref HAPI_Session session,
                byte[] string_value,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CheckForSpecificErrors(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_ErrorCodeBits errors_to_look_for,
                out HAPI_ErrorCodeBits errors_found);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ClearConnectionError();
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetConnectionErrorLength(
                out int buffer_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetConnectionError(
                byte[] string_value,
                int length,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool clear);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetCookingTotalCount(
                ref HAPI_Session session,
                out int count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetCookingCurrentCount(
                ref HAPI_Session session,
                out int count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_Interrupt(
                ref HAPI_Session session);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ConvertTransform(
                ref HAPI_Session session,
                ref HAPI_TransformEuler transform_in,
                HAPI_RSTOrder rst_order,
                HAPI_XYZOrder rot_order,
                out HAPI_TransformEuler transform_out);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ConvertMatrixToQuat(
                ref HAPI_Session session,
                float[] matrix,
                HAPI_RSTOrder rst_order,
                out HAPI_Transform transform_out);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ConvertMatrixToEuler(
                ref HAPI_Session session,
                float[] matrix,
                HAPI_RSTOrder rst_order,
                HAPI_XYZOrder rot_order,
                out HAPI_TransformEuler transform_out);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ConvertTransformQuatToMatrix(
                ref HAPI_Session session,
                ref HAPI_Transform transform,
                [Out] float[] matrix);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ConvertTransformEulerToMatrix(
                ref HAPI_Session session,
                ref HAPI_TransformEuler transform,
                [Out] float[] matrix);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_PythonThreadInterpreterLock(
                ref HAPI_Session session,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool locked);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetStringBufLength(
                ref HAPI_Session session,
                HAPI_StringHandle string_handle,
                out int buffer_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetString(
                ref HAPI_Session session,
                HAPI_StringHandle string_handle,
                byte[] string_value,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetCustomString(
                ref HAPI_Session session,
                byte[] string_value,
                out HAPI_StringHandle handle_value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_RemoveCustomString(
                ref HAPI_Session session,
                HAPI_StringHandle string_handle);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetStringBatchSize(
                ref HAPI_Session session,
                int[] string_handle_array,
                int string_handle_count,
                out int string_buffer_size);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetStringBatch(
                ref HAPI_Session session,
                [Out] byte[] char_buffer,
                int char_array_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetTime(
                ref HAPI_Session session,
                out float time);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetTime(
                ref HAPI_Session session,
                float time);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetUseHoudiniTime(
                ref HAPI_Session session,
                [MarshalAs(UnmanagedType.U1)] out HAPI_Bool enabled);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetUseHoudiniTime(
                ref HAPI_Session session,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool enabled);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetTimelineOptions(
                ref HAPI_Session session,
                out HAPI_TimelineOptions timeline_options);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetTimelineOptions(
                ref HAPI_Session session,
                ref HAPI_TimelineOptions timeline_options);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetCompositorOptions(
                ref HAPI_Session session,
                out HAPI_CompositorOptions compositor_options);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetCompositorOptions(
                ref HAPI_Session session,
                ref HAPI_CompositorOptions compositor_options);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_LoadAssetLibraryFromFile(
                ref HAPI_Session session,
                byte[] file_path,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool allow_overwrite,
                out HAPI_AssetLibraryId library_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_LoadAssetLibraryFromMemory(
                ref HAPI_Session session,
                byte[] library_buffer,
                int library_buffer_length,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool allow_overwrite,
                out HAPI_AssetLibraryId library_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAvailableAssetCount(
                ref HAPI_Session session,
                HAPI_AssetLibraryId library_id,
                out int asset_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAvailableAssets(
                ref HAPI_Session session,
                HAPI_AssetLibraryId library_id,
                [Out] HAPI_StringHandle[] asset_names_array,
                int asset_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAssetInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                out HAPI_AssetInfo asset_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAssetDefinitionParmCounts(
                ref HAPI_Session session,
                HAPI_AssetLibraryId library_id,
                byte[] asset_name,
                out int parm_count,
                out int int_value_count,
                out int float_value_count,
                out int string_value_count,
                out int choice_value_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAssetDefinitionParmInfos(
                ref HAPI_Session session,
                HAPI_AssetLibraryId library_id,
                byte[] asset_name,
                [Out] HAPI_ParmInfo[] parm_infos_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAssetDefinitionParmValues(
                ref HAPI_Session session,
                HAPI_AssetLibraryId library_id,
                byte[] asset_name,
                [Out] int[] int_values_array,
                int int_start,
                int int_length,
                [Out] float[] float_values_array,
                int float_start,
                int float_length,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool string_evaluate,
                [Out] HAPI_StringHandle[] string_values_array,
                int string_start,
                int string_length,
                [Out] HAPI_ParmChoiceInfo[] choice_values_array,
                int choice_start,
                int choice_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_LoadHIPFile(
                ref HAPI_Session session,
                byte[] file_name,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool cook_on_load);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_MergeHIPFile(
                ref HAPI_Session session,
                byte[] file_name,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool cook_on_load,
                out HAPI_HIPFileId file_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SaveHIPFile(
                ref HAPI_Session session,
                byte[] file_path,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool lock_nodes);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetHIPFileNodeCount(
                ref HAPI_Session session,
                HAPI_HIPFileId id,
                out int count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetHIPFileNodeIds(
                ref HAPI_Session session,
                HAPI_HIPFileId id,
                out HAPI_NodeId node_ids,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_IsNodeValid(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int unique_node_id,
                [MarshalAs(UnmanagedType.U1)] out HAPI_Bool answer);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetNodeInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                out HAPI_NodeInfo node_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetNodePath(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_NodeId relative_to_node_id,
                out HAPI_StringHandle path);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetManagerNodeId(
                ref HAPI_Session session,
                HAPI_NodeType node_type,
                out HAPI_NodeId node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ComposeChildNodeList(
                ref HAPI_Session session,
                HAPI_NodeId parent_node_id,
                HAPI_NodeTypeBits node_type_filter,
                HAPI_NodeFlagsBits node_flags_filter,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool recursive,
                out int count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetComposedChildNodeList(
                ref HAPI_Session session,
                HAPI_NodeId parent_node_id,
                [Out] HAPI_NodeId[] child_node_ids_array,
                int count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CreateNode(
                ref HAPI_Session session,
                HAPI_NodeId parent_node_id,
                byte[] operator_name,
                byte[] node_label,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool cook_on_creation,
                out HAPI_NodeId new_node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CreateInputNode(
                ref HAPI_Session session,
                out HAPI_NodeId node_id,
                byte[] name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CreateInputCurveNode(
                ref HAPI_Session session,
                out HAPI_NodeId node_id,
                byte[] name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CreateHeightfieldInputNode(
                ref HAPI_Session session,
                HAPI_NodeId parent_node_id,
                byte[] name,
                int xsize,
                int ysize,
                float voxelsize,
                out HAPI_NodeId heightfield_node_id,
                out HAPI_NodeId height_node_id,
                out HAPI_NodeId mask_node_id,
                out HAPI_NodeId merge_node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CreateHeightFieldInput(
                ref HAPI_Session session,
                HAPI_NodeId parent_node_id,
                byte[] name,
                int xsize,
                int ysize,
                float voxelsize,
                HAPI_HeightFieldSampling sampling,
                out HAPI_NodeId heightfield_node_id,
                out HAPI_NodeId height_node_id,
                out HAPI_NodeId mask_node_id,
                out HAPI_NodeId merge_node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CreateHeightfieldInputVolumeNode(
                ref HAPI_Session session,
                HAPI_NodeId parent_node_id,
                out HAPI_NodeId new_node_id,
                byte[] name,
                int xsize,
                int ysize,
                float voxelsize);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CookNode(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                ref HAPI_CookOptions cook_options);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_DeleteNode(
                ref HAPI_Session session,
                HAPI_NodeId node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_RenameNode(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] new_name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ConnectNodeInput(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int input_index,
                HAPI_NodeId node_id_to_connect,
                int output_index);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_DisconnectNodeInput(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int input_index);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_QueryNodeInput(
                ref HAPI_Session session,
                HAPI_NodeId node_to_query,
                int input_index,
                out HAPI_NodeId connected_node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetNodeInputName(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int input_idx,
                out HAPI_StringHandle name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_DisconnectNodeOutputsAt(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int output_index);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_QueryNodeOutputConnectedCount(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int output_idx,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool into_subnets,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool through_dots,
                out int connected_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_QueryNodeOutputConnectedNodes(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int output_idx,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool into_subnets,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool through_dots,
                [Out] HAPI_NodeId[] connected_node_ids_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetNodeOutputName(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int output_idx,
                out HAPI_StringHandle name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetNodeFromPath(
                ref HAPI_Session session,
                HAPI_NodeId parent_node_id,
                byte[] path,
                out HAPI_NodeId node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetOutputNodeId(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int output,
                out HAPI_NodeId output_node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParameters(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                [Out] HAPI_ParmInfo[] parm_infos_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_ParmId parm_id,
                out HAPI_ParmInfo parm_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmIdFromName(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                out HAPI_ParmId parm_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmInfoFromName(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                out HAPI_ParmInfo parm_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmTagName(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_ParmId parm_id,
                int tag_index,
                out HAPI_StringHandle tag_name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmTagValue(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_ParmId parm_id,
                byte[] tag_name,
                out HAPI_StringHandle tag_value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ParmHasTag(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_ParmId parm_id,
                byte[] tag_name,
                [MarshalAs(UnmanagedType.U1)] out HAPI_Bool has_tag);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ParmHasExpression(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                int index,
                [MarshalAs(UnmanagedType.U1)] out HAPI_Bool has_expression);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmWithTag(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] tag_name,
                out HAPI_ParmId parm_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmExpression(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                int index,
                out HAPI_StringHandle value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_RevertParmToDefault(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                int index);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_RevertParmToDefaults(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetParmExpression(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] value,
                HAPI_ParmId parm_id,
                int index);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_RemoveParmExpression(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_ParmId parm_id,
                int index);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmIntValue(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                int index,
                out int value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmIntValues(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                [Out] int[] values_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmFloatValue(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                int index,
                out float value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmFloatValues(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                [Out] float[] values_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmStringValue(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                int index,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool evaluate,
                out HAPI_StringHandle value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmStringValues(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool evaluate,
                [Out] HAPI_StringHandle[] values_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmNodeValue(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                out HAPI_NodeId value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmFile(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                byte[] destination_directory,
                byte[] destination_file_name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetParmChoiceLists(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                [Out] HAPI_ParmChoiceInfo[] parm_choices_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetParmIntValue(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                int index,
                int value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetParmIntValues(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int[] values_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetParmFloatValue(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                int index,
                float value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetParmFloatValues(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                float[] values_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetParmStringValue(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] value,
                HAPI_ParmId parm_id,
                int index);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetParmNodeValue(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] parm_name,
                HAPI_NodeId value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_InsertMultiparmInstance(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_ParmId parm_id,
                int instance_position);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_RemoveMultiparmInstance(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_ParmId parm_id,
                int instance_position);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetHandleInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                [Out] HAPI_HandleInfo[] handle_infos_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetHandleBindingInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int handle_index,
                [Out] HAPI_HandleBindingInfo[] handle_binding_infos_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetPresetBufLength(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PresetType preset_type,
                byte[] preset_name,
                out int buffer_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetPreset(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                [Out] byte[] buffer,
                int buffer_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetPreset(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PresetType preset_type,
                byte[] preset_name,
                byte[] buffer,
                int buffer_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetObjectInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                out HAPI_ObjectInfo object_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetObjectTransform(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_NodeId relative_to_node_id,
                HAPI_RSTOrder rst_order,
                out HAPI_Transform transform);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ComposeObjectList(
                ref HAPI_Session session,
                HAPI_NodeId parent_node_id,
                byte[] categories,
                out int object_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetComposedObjectList(
                ref HAPI_Session session,
                HAPI_NodeId parent_node_id,
                [Out] HAPI_ObjectInfo[] object_infos_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetComposedObjectTransforms(
                ref HAPI_Session session,
                HAPI_NodeId parent_node_id,
                HAPI_RSTOrder rst_order,
                [Out] HAPI_Transform[] transform_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetInstancedObjectIds(
                ref HAPI_Session session,
                HAPI_NodeId object_node_id,
                [Out] HAPI_NodeId[] instanced_node_id_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetInstanceTransforms(
                ref HAPI_Session session,
                HAPI_NodeId object_node_id,
                HAPI_RSTOrder rst_order,
                [Out] HAPI_Transform[] transforms_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetInstanceTransformsOnPart(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                HAPI_RSTOrder rst_order,
                [Out] HAPI_Transform[] transforms_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetObjectTransform(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                ref HAPI_TransformEuler trans);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetDisplayGeoInfo(
                ref HAPI_Session session,
                HAPI_NodeId object_node_id,
                out HAPI_GeoInfo geo_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetOutputGeoCount(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                out int count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetOutputGeoInfos(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                [Out] HAPI_GeoInfo[] geo_infos_array,
                int count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetGeoInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                out HAPI_GeoInfo geo_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetPartInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                out HAPI_PartInfo part_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetEdgeCountOfEdgeGroup(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] group_name,
                out int edge_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetFaceCounts(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                [Out] int[] face_counts_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetVertexList(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                [Out] int[] vertex_list_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                HAPI_AttributeOwner owner,
                out HAPI_AttributeInfo attr_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeNames(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                HAPI_AttributeOwner owner,
                [Out] HAPI_StringHandle[] attribute_names_array,
                int count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeIntData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                int stride,
                [Out] int[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeIntArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                [Out] int[] data_fixed_array,
                int data_fixed_length,
                [Out] int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeUInt8Data(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                int stride,
                [Out] HAPI_UInt8[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeUInt8ArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                [Out] HAPI_UInt8[] data_fixed_array,
                int data_fixed_length,
                [Out] int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeInt8Data(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                int stride,
                [Out] HAPI_Int8[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeInt8ArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                [Out] HAPI_Int8[] data_fixed_array,
                int data_fixed_length,
                [Out] int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeInt16Data(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                int stride,
                [Out] HAPI_Int16[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeInt16ArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                [Out] HAPI_Int16[] data_fixed_array,
                int data_fixed_length,
                [Out] int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeInt64Data(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                int stride,
                [Out] HAPI_Int64[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeInt64ArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                [Out] HAPI_Int64[] data_fixed_array,
                int data_fixed_length,
                [Out] int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeFloatData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                int stride,
                [Out] float[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeFloatArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                [Out] float[] data_fixed_array,
                int data_fixed_length,
                [Out] int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeFloat64Data(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                int stride,
                [Out] double[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeFloat64ArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                [Out] double[] data_fixed_array,
                int data_fixed_length,
                [Out] int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeStringData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                [Out] HAPI_StringHandle[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetAttributeStringArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                [Out] HAPI_StringHandle[] data_fixed_array,
                int data_fixed_length,
                [Out] int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetGroupNames(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_GroupType group_type,
                [Out] HAPI_StringHandle[] group_names_array,
                int group_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetGroupMembership(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                HAPI_GroupType group_type,
                byte[] group_name,
                [MarshalAs(UnmanagedType.U1)] out HAPI_Bool membership_array_all_equal,
                [Out] int[] membership_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetGroupCountOnPackedInstancePart(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                out int pointGroupCount,
                out int primitiveGroupCount);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetGroupNamesOnPackedInstancePart(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                HAPI_GroupType group_type,
                [Out] HAPI_StringHandle[] group_names_array,
                int group_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetGroupMembershipOnPackedInstancePart(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                HAPI_GroupType group_type,
                byte[] group_name,
                [MarshalAs(UnmanagedType.U1)] out HAPI_Bool membership_array_all_equal,
                [Out] int[] membership_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetInstancedPartIds(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                [Out] HAPI_PartId[] instanced_parts_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetInstancerPartTransforms(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                HAPI_RSTOrder rst_order,
                [Out] HAPI_Transform[] transforms_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetPartInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                ref HAPI_PartInfo part_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetFaceCounts(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                int[] face_counts_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetVertexList(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                int[] vertex_list_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_AddAttribute(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_DeleteAttribute(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeIntData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                int[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeUInt8Data(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                HAPI_UInt8[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeInt8Data(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                HAPI_Int8[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeInt16Data(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                HAPI_Int16[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeInt64Data(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                HAPI_Int64[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeFloatData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                float[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeFloat64Data(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                double[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeStringData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                string[] data_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeIntArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                int[] data_fixed_array,
                int data_fixed_length,
                int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeUInt8ArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                HAPI_UInt8[] data_fixed_array,
                int data_fixed_length,
                int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeInt8ArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                HAPI_Int8[] data_fixed_array,
                int data_fixed_length,
                int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeInt16ArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                HAPI_Int16[] data_fixed_array,
                int data_fixed_length,
                int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeInt64ArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                HAPI_Int64[] data_fixed_array,
                int data_fixed_length,
                int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeFloatArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                float[] data_fixed_array,
                int data_fixed_length,
                int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeFloat64ArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                double[] data_fixed_array,
                int data_fixed_length,
                int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAttributeStringArrayData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                ref HAPI_AttributeInfo attr_info,
                string[] data_fixed_array,
                int data_fixed_length,
                int[] sizes_fixed_array,
                int start,
                int sizes_fixed_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_AddGroup(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                HAPI_GroupType group_type,
                byte[] group_name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_DeleteGroup(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                HAPI_GroupType group_type,
                byte[] group_name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetGroupMembership(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                HAPI_GroupType group_type,
                byte[] group_name,
                int[] membership_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CommitGeo(
                ref HAPI_Session session,
                HAPI_NodeId node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_RevertGeo(
                ref HAPI_Session session,
                HAPI_NodeId node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetMaterialNodeIdsOnFaces(
                ref HAPI_Session session,
                HAPI_NodeId geometry_node_id,
                HAPI_PartId part_id,
                [MarshalAs(UnmanagedType.U1)] out HAPI_Bool are_all_the_same,
                [Out] HAPI_NodeId[] material_ids_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetMaterialInfo(
                ref HAPI_Session session,
                HAPI_NodeId material_node_id,
                out HAPI_MaterialInfo material_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_RenderCOPToImage(
                ref HAPI_Session session,
                HAPI_NodeId cop_node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_RenderTextureToImage(
                ref HAPI_Session session,
                HAPI_NodeId material_node_id,
                HAPI_ParmId parm_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetImageInfo(
                ref HAPI_Session session,
                HAPI_NodeId material_node_id,
                out HAPI_ImageInfo image_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetImageInfo(
                ref HAPI_Session session,
                HAPI_NodeId material_node_id,
                ref HAPI_ImageInfo image_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetImagePlaneCount(
                ref HAPI_Session session,
                HAPI_NodeId material_node_id,
                out int image_plane_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetImagePlanes(
                ref HAPI_Session session,
                HAPI_NodeId material_node_id,
                [Out] HAPI_StringHandle[] image_planes_array,
                int image_plane_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ExtractImageToFile(
                ref HAPI_Session session,
                HAPI_NodeId material_node_id,
                byte[] image_file_format_name,
                byte[] image_planes,
                byte[] destination_folder_path,
                byte[] destination_file_name,
                out int destination_file_path);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetImageFilePath(
                ref HAPI_Session session,
                HAPI_NodeId material_node_id,
                byte[] image_file_format_name,
                byte[] image_planes,
                byte[] destination_folder_path,
                byte[] destination_file_name,
                HAPI_ParmId texture_parm_id,
                out int destination_file_path);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ExtractImageToMemory(
                ref HAPI_Session session,
                HAPI_NodeId material_node_id,
                byte[] image_file_format_name,
                byte[] image_planes,
                out int buffer_size);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetImageMemoryBuffer(
                ref HAPI_Session session,
                HAPI_NodeId material_node_id,
                [Out] byte[] buffer,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetSupportedImageFileFormatCount(
                ref HAPI_Session session,
                out int file_format_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetSupportedImageFileFormats(
                ref HAPI_Session session,
                [Out] HAPI_ImageFileFormat[] formats_array,
                int file_format_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetAnimCurve(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_ParmId parm_id,
                int parm_index,
                HAPI_Keyframe[] curve_keyframes_array,
                int keyframe_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetTransformAnimCurve(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_TransformComponent trans_comp,
                HAPI_Keyframe[] curve_keyframes_array,
                int keyframe_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_ResetSimulation(
                ref HAPI_Session session,
                HAPI_NodeId node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetVolumeInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                out HAPI_VolumeInfo volume_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetFirstVolumeTile(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                out HAPI_VolumeTileInfo tile);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetNextVolumeTile(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                out HAPI_VolumeTileInfo tile);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetVolumeVoxelFloatData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                int x_index,
                int y_index,
                int z_index,
                [Out] float[] values_array,
                int value_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetVolumeTileFloatData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                float fill_value,
                ref HAPI_VolumeTileInfo tile,
                [Out] float[] values_array,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetVolumeVoxelIntData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                int x_index,
                int y_index,
                int z_index,
                [Out] int[] values_array,
                int value_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetVolumeTileIntData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                int fill_value,
                ref HAPI_VolumeTileInfo tile,
                [Out] int[] values_array,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetHeightFieldData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                [Out] float[] values_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetVolumeInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                ref HAPI_VolumeInfo volume_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetVolumeTileFloatData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                ref HAPI_VolumeTileInfo tile,
                float[] values_array,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetVolumeTileIntData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                ref HAPI_VolumeTileInfo tile,
                int[] values_array,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetVolumeVoxelFloatData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                int x_index,
                int y_index,
                int z_index,
                float[] values_array,
                int value_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetVolumeVoxelIntData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                int x_index,
                int y_index,
                int z_index,
                int[] values_array,
                int value_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetVolumeBounds(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                out float x_min,
                out float y_min,
                out float z_min,
                out float x_max,
                out float y_max,
                out float z_max,
                out float x_center,
                out float y_center,
                out float z_center);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetHeightFieldData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                byte[] name,
                float[] values_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetVolumeVisualInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                out HAPI_VolumeVisualInfo visual_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetCurveInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                out HAPI_CurveInfo info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetCurveCounts(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                [Out] int[] counts_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetCurveOrders(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                [Out] int[] orders_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetCurveKnots(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                [Out] float[] knots_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetCurveInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                ref HAPI_CurveInfo info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetCurveCounts(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                int[] counts_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetCurveOrders(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                int[] orders_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetCurveKnots(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                float[] knots_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetInputCurveInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                out HAPI_InputCurveInfo info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetInputCurveInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                ref HAPI_InputCurveInfo info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetInputCurvePositions(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                float[] positions_array,
                int start,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetInputCurvePositionsRotationsScales(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PartId part_id,
                float[] positions_array,
                int positions_start,
                int positions_length,
                float[] rotations_array,
                int rotations_start,
                int rotations_length,
                float[] scales_array,
                int scales_start,
                int scales_length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetBoxInfo(
                ref HAPI_Session session,
                HAPI_NodeId geo_node_id,
                HAPI_PartId part_id,
                out HAPI_BoxInfo box_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetSphereInfo(
                ref HAPI_Session session,
                HAPI_NodeId geo_node_id,
                HAPI_PartId part_id,
                out HAPI_SphereInfo sphere_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetActiveCacheCount(
                ref HAPI_Session session,
                out int active_cache_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetActiveCacheNames(
                ref HAPI_Session session,
                [Out] HAPI_StringHandle[] cache_names_array,
                int active_cache_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetCacheProperty(
                ref HAPI_Session session,
                byte[] cache_name,
                HAPI_CacheProperty cache_property,
                out int property_value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetCacheProperty(
                ref HAPI_Session session,
                byte[] cache_name,
                HAPI_CacheProperty cache_property,
                int property_value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SaveGeoToFile(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] file_name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_LoadGeoFromFile(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] file_name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SaveNodeToFile(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] file_name);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_LoadNodeFromFile(
                ref HAPI_Session session,
                byte[] file_name,
                HAPI_NodeId parent_node_id,
                byte[] node_label,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool cook_on_load,
                out HAPI_NodeId new_node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetGeoSize(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] format,
                out int size);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SaveGeoToMemory(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                [Out] byte[] buffer,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_LoadGeoFromMemory(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                byte[] format,
                byte[] buffer,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetNodeDisplay(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                int onOff);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetTotalCookCount(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_NodeTypeBits node_type_filter,
                HAPI_NodeFlagsBits node_flags_filter,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool recursive,
                out int count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetSessionSync(
                ref HAPI_Session session,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool enable);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetViewport(
                ref HAPI_Session session,
                out HAPI_Viewport viewport);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetViewport(
                ref HAPI_Session session,
                ref HAPI_Viewport viewport);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetSessionSyncInfo(
                ref HAPI_Session session,
                out HAPI_SessionSyncInfo session_sync_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetSessionSyncInfo(
                ref HAPI_Session session,
                ref HAPI_SessionSyncInfo session_sync_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetPDGGraphContexts(
                ref HAPI_Session session,
                out int num_contexts,
                [Out] HAPI_StringHandle[] context_names_array,
                [Out] HAPI_PDG_GraphContextId[] context_id_array,
                int count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetPDGGraphContextId(
                ref HAPI_Session session,
                HAPI_NodeId top_node_id,
                out HAPI_PDG_GraphContextId context_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CookPDG(
                ref HAPI_Session session,
                HAPI_NodeId cook_node_id,
                int generate_only,
                int blocking);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CookPDGAllOutputs(
                ref HAPI_Session session,
                HAPI_NodeId cook_node_id,
                int generate_only,
                int blocking);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetPDGEvents(
                ref HAPI_Session session,
                HAPI_PDG_GraphContextId graph_context_id,
                [Out] HAPI_PDG_EventInfo[] event_array,
                int length,
                out int event_count,
                out int remaining_events);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetPDGState(
                ref HAPI_Session session,
                HAPI_PDG_GraphContextId graph_context_id,
                out int pdg_state);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CreateWorkitem(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                out HAPI_PDG_WorkitemId workitem_id,
                byte[] name,
                int index);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetWorkitemInfo(
                ref HAPI_Session session,
                HAPI_PDG_GraphContextId graph_context_id,
                HAPI_PDG_WorkitemId workitem_id,
                out HAPI_PDG_WorkitemInfo workitem_info);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetWorkitemIntData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PDG_WorkitemId workitem_id,
                byte[] data_name,
                int[] values_array,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetWorkitemFloatData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PDG_WorkitemId workitem_id,
                byte[] data_name,
                float[] values_array,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_SetWorkitemStringData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PDG_WorkitemId workitem_id,
                byte[] data_name,
                int data_index,
                byte[] value);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CommitWorkitems(
                ref HAPI_Session session,
                HAPI_NodeId node_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetNumWorkitems(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                out int num);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetWorkitems(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                [Out] int[] workitem_ids_array,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetWorkitemDataLength(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PDG_WorkitemId workitem_id,
                byte[] data_name,
                out int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetWorkitemIntData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PDG_WorkitemId workitem_id,
                byte[] data_name,
                [Out] int[] data_array,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetWorkitemFloatData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PDG_WorkitemId workitem_id,
                byte[] data_name,
                [Out] float[] data_array,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetWorkitemStringData(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PDG_WorkitemId workitem_id,
                byte[] data_name,
                [Out] HAPI_StringHandle[] data_array,
                int length);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_GetWorkitemResultInfo(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                HAPI_PDG_WorkitemId workitem_id,
                [Out] HAPI_PDG_WorkitemResultInfo[] resultinfo_array,
                int resultinfo_count);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_DirtyPDGNode(
                ref HAPI_Session session,
                HAPI_NodeId node_id,
                [MarshalAs(UnmanagedType.U1)] HAPI_Bool clean_results);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_PausePDGCook(
                ref HAPI_Session session,
                HAPI_PDG_GraphContextId graph_context_id);
        [DllImport(HEU_HoudiniVersion.HAPI_LIBRARY, CallingConvention = CallingConvention.Cdecl)]
        public static extern HAPI_Result
        HAPI_CancelPDGCook(
                ref HAPI_Session session,
                HAPI_PDG_GraphContextId graph_context_id);
#endif

    }

}
