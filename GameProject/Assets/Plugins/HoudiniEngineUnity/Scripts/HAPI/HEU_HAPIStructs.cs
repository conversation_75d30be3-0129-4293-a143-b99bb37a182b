/*
* Copyright (c) <2021> Side Effects Software Inc.
*
* Permission is hereby granted, free of charge, to any person obtaining a copy
* of this software and associated documentation files (the "Software"), to deal
* in the Software without restriction, including without limitation the rights
* to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
* copies of the Software, and to permit persons to whom the Software is
* furnished to do so, subject to the following conditions:
*
* The above copyright notice and this permission notice shall be included in all
* copies or substantial portions of the Software.
*
* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
* IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
* FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
* AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
* LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
* OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
* SOFTWARE.
*
* Produced by:
*      Side Effects Software Inc
*      123 Front Street West, Suite 1401
*      Toronto, Ontario
*      Canada   M5J 2M2
*      416-504-9876
*/


/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// WARNING! This file is automatically GENERATED.
// DO NOT modify manually or commit to the repository!
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

using System;
using System.Runtime.InteropServices;

namespace HoudiniEngineUnity
{
    using HAPI_Bool = System.Boolean;
    using HAPI_UInt8 = System.Byte;
    using HAPI_Int8 = System.SByte;
    using HAPI_Int16 = System.Int16;
    using HAPI_Int64 = System.Int64;
    using HAPI_ProcessId = System.Int32;
    using HAPI_SessionId = System.Int64;
    using HAPI_StringHandle = System.Int32;
    using HAPI_AssetLibraryId = System.Int32;
    using HAPI_NodeId = System.Int32;
    using HAPI_ParmId = System.Int32;
    using HAPI_PartId = System.Int32;
    using HAPI_PDG_WorkitemId = System.Int32;
    using HAPI_PDG_GraphContextId = System.Int32;
    using HAPI_HIPFileId = System.Int32;
    using HAPI_ErrorCodeBits = System.Int32;
    using HAPI_NodeTypeBits = System.Int32;
    using HAPI_NodeFlagsBits = System.Int32;

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_Transform          //A Transform with Quaternion rotation
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_POSITION_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] position;              

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_QUATERNION_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] rotationQuaternion;              

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_SCALE_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] scale;              

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_SHEAR_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] shear;              

        public HAPI_RSTOrder rstOrder;              

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_TransformEuler          //A Transform with Euler rotation
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_POSITION_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] position;              

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_EULER_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] rotationEuler;              

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_SCALE_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] scale;              

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_SHEAR_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] shear;              

        public HAPI_XYZOrder rotationOrder;              

        public HAPI_RSTOrder rstOrder;              

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_Session          //Identifies a session
    {
        public HAPI_SessionType type;              //The type of session determines the which implementation will beused to communicate with the Houdini Engine library.

        public HAPI_SessionId id;              //Some session types support multiple simultaneous sessions. This meansthat each session needs to have a unique identifier.

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_ThriftServerOptions          //Options to configure a Thrift server being started from HARC.
    {
        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool autoClose;              //Close the server automatically when all clients disconnect from it.

        [MarshalAs(UnmanagedType.R4)]
        public float timeoutMs;              //Timeout in milliseconds for waiting on the server tosignal that it ready to serve. If the server failsto signal within this time interval the start server call failsand the server process is terminated.

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_TimelineOptions          //Data for global timeline used with HAPI_SetTimelineOptions
    {
        [MarshalAs(UnmanagedType.R4)]
        public float fps;              

        [MarshalAs(UnmanagedType.R4)]
        public float startTime;              

        [MarshalAs(UnmanagedType.R4)]
        public float endTime;              

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_AssetInfo          //Meta-data about an HDA returned by HAPI_GetAssetInfo
    {
        public HAPI_NodeId nodeId;              //Use the node id to get the asset parameters.See HAPI_Nodes_Basics.

        public HAPI_NodeId objectNodeId;              //The objectNodeId differs from the regular nodeId in that forgeometry based assets SOPs it will be the node id of the dummyobject OBJ node instead of the asset node. For object based assetsthe objectNodeId will equal the nodeId. The reason the distinctionexists is because transforms are always stored on the object nodebut the asset parameters may not be on the asset node if the assetis a geometry asset so we need both.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasEverCooked;              //It possible to instantiate an asset without cooking it.See HAPI_Assets_Cooking.

        public HAPI_StringHandle nameSH;              //Instance name the label + a number.

        public HAPI_StringHandle labelSH;              //This is what any end user should be shown.

        public HAPI_StringHandle filePathSH;              //Path to the .otl library file.

        public HAPI_StringHandle versionSH;              //User-defined asset version.

        public HAPI_StringHandle fullOpNameSH;              //Full asset name and namespace.

        public HAPI_StringHandle helpTextSH;              //Asset help marked-up text.

        public HAPI_StringHandle helpURLSH;              //Asset help URL.

        [MarshalAs(UnmanagedType.I4)]
        public int objectCount;              //See HAPI_Objects.

        [MarshalAs(UnmanagedType.I4)]
        public int handleCount;              //See HAPI_Handles.

        [MarshalAs(UnmanagedType.I4)]
        public int transformInputCount;              //Transform inputs exposed by the asset. For OBJ assets this is thenumber of transform inputs on the OBJ node. For SOP assets this isthe singular transform input on the dummy wrapper OBJ node.See HAPI_AssetInputs.

        [MarshalAs(UnmanagedType.I4)]
        public int geoInputCount;              //Geometry inputs exposed by the asset. For SOP assets this isthe number of geometry inputs on the SOP node itself. OBJ assetswill always have zero geometry inputs.See HAPI_AssetInputs.

        [MarshalAs(UnmanagedType.I4)]
        public int geoOutputCount;              //Geometry outputs exposed by the asset. For SOP assets this isthe number of geometry outputs on the SOP node itself. OBJ assetswill always have zero geometry outputs.See HAPI_AssetInputs.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool haveObjectsChanged;              //For incremental updates. Indicates whether any of the assetsobjects have changed. Refreshed only during an asset cook.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool haveMaterialsChanged;              //For incremental updates. Indicates whether any of the assetmaterials have changed. Refreshed only during an asset cook.

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_CookOptions          //Options which affect how nodes are cooked.
    {
        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool splitGeosByGroup;              //Normally geos are split into parts in two different ways. First itis split by group and within each group it is split by primitive type.For example if you have a geo with group1 covering half of the meshand volume1 and group2 covering the other half of the mesh all ofcurve1 and volume2 you will end up with 5 parts. First two partswill be for the half-mesh of group1 and volume1 and the last threewill cover group2.This toggle lets you disable the splitting by group and just havethe geo be split by primitive type alone. By default this is trueand therefore geos will be split by group and primitive type. Ifset to false geos will only be split by primitive type.

        public HAPI_StringHandle splitGroupSH;              //Normally geos are split into parts in two different ways. First itis split by group and within each group it is split by primitive type.For example if you have a geo with group1 covering half of the meshand volume1 and group2 covering the other half of the mesh all ofcurve1 and volume2 you will end up with 5 parts. First two partswill be for the half-mesh of group1 and volume1 and the last threewill cover group2.This toggle lets you disable the splitting by group and just havethe geo be split by primitive type alone. By default this is trueand therefore geos will be split by group and primitive type. Ifset to false geos will only be split by primitive type.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool splitGeosByAttribute;              //This toggle lets you enable the splitting by unique valuesof a specified attribute. By default this is false andthe geo be split as described above.as described above. If this is set to true and splitGeosByGroupset to false mesh geos will be split on attribute valuesThe attribute name to split on must be created with HAPI_SetCustomStringand then the splitAttrSH handle set on the struct.

        public HAPI_StringHandle splitAttrSH;              //This toggle lets you enable the splitting by unique valuesof a specified attribute. By default this is false andthe geo be split as described above.as described above. If this is set to true and splitGeosByGroupset to false mesh geos will be split on attribute valuesThe attribute name to split on must be created with HAPI_SetCustomStringand then the splitAttrSH handle set on the struct.

        [MarshalAs(UnmanagedType.I4)]
        public int maxVerticesPerPrimitive;              //For meshes only this is enforced by convexing the mesh. Use -1to avoid convexing at all and get some performance boost.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool refineCurveToLinear;              //For curves only.If this is set to true then all curves will be refined to a linearcurve and you can no longer access the original CVs. You can controlthe refinement detail via HAPI_CookOptions::curveRefineLOD.If it false the curve type NURBS Bezier etc will be left as is.

        [MarshalAs(UnmanagedType.R4)]
        public float curveRefineLOD;              //Controls the number of divisions per unit distance when refininga curve to linear. The default in Houdini is 8.0.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool clearErrorsAndWarnings;              //If this option is turned on then we will recursively clear theerrors and warnings and messages of all nodes before performingthe cook.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool cookTemplatedGeos;              //Decide whether to recursively cook all templated geos or not.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool splitPointsByVertexAttributes;              //Decide whether to split points by vertex attributes. This takesall vertex attributes and tries to copy them to their respectivepoints. If two vertices have any difference in their attribute valuesthe corresponding point is split into two points. This is repeateduntil all the vertex attributes have been copied to the points.With this option enabled you can reduce the total number of verticeson a game engine side as sharing of attributes like UVs is optimized.To make full use of this feature you have to think of Houdini pointsas game engine vertices sharable. With this option OFF or beforethis feature existed you had to map Houdini vertices to game enginevertices to make sure all attribute values are accounted for.

        public HAPI_PackedPrimInstancingMode packedPrimInstancingMode;              //Choose how you want the cook to handle packed primitives.The default is: HAPI_PACKEDPRIM_INSTANCING_MODE_DISABLED

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool handleBoxPartTypes;              //Choose which special part types should be handled. Unhandled specialpart types will just be refined to HAPI_PARTTYPE_MESH.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool handleSpherePartTypes;              //Choose which special part types should be handled. Unhandled specialpart types will just be refined to HAPI_PARTTYPE_MESH.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool checkPartChanges;              //If enabled sets the HAPI_PartInfo::hasChanged member during thecook. If disabled the member will always be true. Checking forpart changes can be expensive so there is a potential performancegain when disabled.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool cacheMeshTopology;              //This toggle lets you enable the caching of the mesh topology.By default this is false. If this is set to true cooking a meshgeometry will update only the topology if the number of points changed.Use this to get better performance on deforming meshes.

        [MarshalAs(UnmanagedType.I4)]
        public int extraFlags;              //For internal use only. :

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_NodeInfo          //Meta-data for a Houdini Node
    {
        public HAPI_NodeId id;              

        public HAPI_NodeId parentId;              

        public HAPI_StringHandle nameSH;              

        public HAPI_NodeType type;              

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isValid;              //Always true unless the asset definition has changed due to loadinga duplicate asset definition and from another OTL asset libraryfile OR deleting the OTL asset library file used by this node asset.

        [MarshalAs(UnmanagedType.I4)]
        public int totalCookCount;              //Total number of cooks of this node.

        [MarshalAs(UnmanagedType.I4)]
        public int uniqueHoudiniNodeId;              //Use this unique id to grab the OP_Node pointer for this node.If you linking against the C++ HDK include the OP_Node.h headerand call OP_Node::lookupNode.

        public HAPI_StringHandle internalNodePathSH;              //This is the internal node path in the Houdini scene graph. This pathis meant to be abstracted away for most client purposes but foradvanced uses it can come in handy.

        [MarshalAs(UnmanagedType.I4)]
        public int parmCount;              //Total number of parameters this asset has exposed. Includes hiddenparameters.See HAPI_Parameters.

        [MarshalAs(UnmanagedType.I4)]
        public int parmIntValueCount;              //Number of values. A single parameter may have more than one value sothis number is more than or equal to HAPI_NodeInfo::parmCount.@{@}

        [MarshalAs(UnmanagedType.I4)]
        public int parmFloatValueCount;              //Number of values. A single parameter may have more than one value sothis number is more than or equal to HAPI_NodeInfo::parmCount.@{@}

        [MarshalAs(UnmanagedType.I4)]
        public int parmStringValueCount;              //Number of values. A single parameter may have more than one value sothis number is more than or equal to HAPI_NodeInfo::parmCount.@{@}

        [MarshalAs(UnmanagedType.I4)]
        public int parmChoiceCount;              //The total number of choices among all the combo box parameters.See HAPI_Parameters_ChoiceLists.

        [MarshalAs(UnmanagedType.I4)]
        public int childNodeCount;              //The number of child nodes. This is 0 for all nodes that are notnode networks.

        [MarshalAs(UnmanagedType.I4)]
        public int inputCount;              //The number of inputs this specific node has.

        [MarshalAs(UnmanagedType.I4)]
        public int outputCount;              //The number of outputs this specific node has.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool createdPostAssetLoad;              //Nodes created via scripts or via HAPI_CreateNode will be havethis set to true. Only such nodes can be deleted usingHAPI_DeleteNode.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isTimeDependent;              //Indicates if this node will change over time

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_ParmInfo          //Contains parameter information like name label type and size.
    {
        public HAPI_ParmId id;              //The parent id points to the id of the parent parmof this parm. The parent parm is something like a folder.

        public HAPI_ParmId parentId;              //Parameter id of the parent of this parameter.

        [MarshalAs(UnmanagedType.I4)]
        public int childIndex;              //Child index within its immediate parent parameter.

        public HAPI_ParmType type;              //The HAPI type of the parm

        public HAPI_PrmScriptType scriptType;              //The Houdini script-type of the parm

        public HAPI_StringHandle typeInfoSH;              //Some parameter types require additional type information.- File path parameters will indicate what file extensions theyexpect in a space-separated list of wild-cards. This is setin the Operator Type Properties using the File Patternparameter property.For example for filtering by PNG and JPG only: "*.png *.jpg"

        public HAPI_Permissions permissions;              //For the majority of parameter types permission will not be applicable.For file path parameters these permissions will indicate how theasset plans to use the file: whether it will only read it only writeto it or both. This is set in the Operator Type Properties usingthe Browse Mode parameter property.

        [MarshalAs(UnmanagedType.I4)]
        public int tagCount;              //Number of tags on this paramter.

        [MarshalAs(UnmanagedType.I4)]
        public int size;              //Tuple size. For scalar parameters this value is 1 but for vectorparameters this value can be greater. For example a 3 vector wouldhave a size of 3. For folders and folder lists this value is thenumber of children they own.

        public HAPI_ChoiceListType choiceListType;              //Any HAPI_ParmType can be a choice list. If this is set toHAPI_CHOICELISTTYPE_NONE than this parameter is NOT a choice list.Otherwise the parameter is a choice list of the indicated type.See HAPI_Parameters_ChoiceLists.

        [MarshalAs(UnmanagedType.I4)]
        public int choiceCount;              //Any HAPI_ParmType can be a choice list. If the parameter is achoice list this tells you how many choices it currently has.Note that some menu parameters can have a dynamic number of choicesso it is important that this count is re-checked after every cook.See HAPI_Parameters_ChoiceLists.

        public HAPI_StringHandle nameSH;              //Note that folders are not real parameters in Houdini so they do nothave names. The folder names given here are generated from the nameof the folderlist or switcher parameter which is a parameter. Thefolderlist parameter simply defines how many of the "next" parametersbelong to the first folder how many of the parameters after thatbelong to the next folder and so on. This means that folder namescan change just by reordering the folders around so don rely onthem too much. The only guarantee here is that the folder names willbe unique among all other parameter names.

        public HAPI_StringHandle labelSH;              //The label string for the parameter

        public HAPI_StringHandle templateNameSH;              //If this parameter is a multiparm instance than theHAPI_ParmInfo::templateNameSH will be the hash-templated parm namecontaining # for the parts of the name that use the instance number.Compared to the HAPI_ParmInfo::nameSH the HAPI_ParmInfo::nameSHwill be the HAPI_ParmInfo::templateNameSH but with the #replaced by the instance number. For regular parms theHAPI_ParmInfo::templateNameSH is identical to theHAPI_ParmInfo::nameSH.

        public HAPI_StringHandle helpSH;              //The help string for this parameter

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasMin;              //Whether min/max exists for the parameter values.@{@}

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasMax;              //Whether min/max exists for the parameter values.@{@}

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasUIMin;              //Whether min/max exists for the parameter values.@{@}

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasUIMax;              //Whether min/max exists for the parameter values.@{@}

        [MarshalAs(UnmanagedType.R4)]
        public float min;              //Parameter value range shared between int and float parameters.@{@}

        [MarshalAs(UnmanagedType.R4)]
        public float max;              //Parameter value range shared between int and float parameters.@{@}

        [MarshalAs(UnmanagedType.R4)]
        public float UIMin;              //Parameter value range shared between int and float parameters.@{@}

        [MarshalAs(UnmanagedType.R4)]
        public float UIMax;              //Parameter value range shared between int and float parameters.@{@}

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool invisible;              //Whether this parm should be hidden from the user entirely. This ismostly used to expose parameters as asset meta-data but not allow theuser to directly modify them.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool disabled;              //Whether this parm should appear enabled or disabled.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool spare;              //If true it means this parameter doesn actually exist on the nodein Houdini but was added by Houdini Engine as a spare parameter.This is just for your information. The behaviour of this parameteris not any different than a non-spare parameter.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool joinNext;              //Whether this parm should be on the same line as

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool labelNone;              //Whether the label should be displayed.

        [MarshalAs(UnmanagedType.I4)]
        public int intValuesIndex;              //The index to use to look into the values array in order to retrievethe actual values of this parameter.@{@}

        [MarshalAs(UnmanagedType.I4)]
        public int floatValuesIndex;              //The index to use to look into the values array in order to retrievethe actual values of this parameter.@{@}

        [MarshalAs(UnmanagedType.I4)]
        public int stringValuesIndex;              //The index to use to look into the values array in order to retrievethe actual values of this parameter.@{@}

        [MarshalAs(UnmanagedType.I4)]
        public int choiceIndex;              //The index to use to look into the values array in order to retrievethe actual values of this parameter.@{@}

        public HAPI_NodeType inputNodeType;              //If this is a HAPI_PARMTYPE_NODE this tells you what node typesthis parameter accepts.

        public HAPI_NodeFlags inputNodeFlag;              //The node input parameter could have another subtype filter specifiedlike "Object: Geometry Only". In this case this value will specifythat extra filter. If the filter demands a node that HAPI does notsupport both this and HAPI_ParmInfo::inputNodeType will be set toNONE as such a node is not settable through HAPI.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isChildOfMultiParm;              //See HAPI_Parameters_MultiParms.@{

        [MarshalAs(UnmanagedType.I4)]
        public int instanceNum;              //The index of the instance in the multiparm.

        [MarshalAs(UnmanagedType.I4)]
        public int instanceLength;              //The number of parms in a multiparm instance.

        [MarshalAs(UnmanagedType.I4)]
        public int instanceCount;              //The number of instances in a multiparm.

        [MarshalAs(UnmanagedType.I4)]
        public int instanceStartOffset;              //First instance HAPI_ParmInfo::instanceNum. Either 0 or 1.

        public HAPI_RampType rampType;              //@}

        public HAPI_StringHandle visibilityConditionSH;              //Provides the raw condition string which is used to evaluate thethe visibility of a parm

        public HAPI_StringHandle disabledConditionSH;              //Provides the raw condition string which is used to evalute whethera parm is enabled or disabled

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool useMenuItemTokenAsValue;              //Whether or not the "Use Menu Item Token As Value" checkbox was checked in a integer menu item.

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_ParmChoiceInfo          //Meta-data for a combo-box / choice parm
    {
        public HAPI_ParmId parentParmId;              

        public HAPI_StringHandle labelSH;              

        public HAPI_StringHandle valueSH;              //This evaluates to the value of the token associated with the labelapplies to string menus only.

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_HandleInfo          //Contains handle information such as the type of handletranslate rotate scale softxform ...etc and the number ofparameters the current handle is bound to.
    {
        public HAPI_StringHandle nameSH;              

        public HAPI_StringHandle typeNameSH;              

        [MarshalAs(UnmanagedType.I4)]
        public int bindingsCount;              

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_HandleBindingInfo          //Contains binding information that maps the handle parameter tothe asset parameter. The index is only used for int and float vectorand colour parms.
    {
        public HAPI_StringHandle handleParmNameSH;              

        public HAPI_StringHandle assetParmNameSH;              

        public HAPI_ParmId assetParmId;              

        [MarshalAs(UnmanagedType.I4)]
        public int assetParmIndex;              

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_ObjectInfo          //Meta-data for an OBJ Node
    {
        public HAPI_StringHandle nameSH;              

        public HAPI_StringHandle objectInstancePathSH;              //@deprecated This member is no longer used

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasTransformChanged;              //For incremental updates. Indicates whether the object transformhas changed. Refreshed only during an asset cook.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool haveGeosChanged;              //For incremental updates. Indicates whether any of the objectgeometry nodes have changed. Refreshed only during an asset cook.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isVisible;              //Whether the object is hidden and should not be shown. Some objectsshould be hidden but still brought into the host environment forexample those used only for instancing.See HAPI_Instancing.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isInstancer;              //See HAPI_Instancing.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isInstanced;              //Determine if this object is being instanced. Normally this impliesthat while this object may not be visible it should still bebrought into the host application because it is needed by an instancer.See HAPI_Instancing.

        [MarshalAs(UnmanagedType.I4)]
        public int geoCount;              //@deprecated No longer used. See HAPI_Geos

        public HAPI_NodeId nodeId;              //Use the node id to get the node parameters.Using the HDK you can also get the raw node C++ pointer for thisobject internal node.See HAPI_Nodes_Basics.

        public HAPI_NodeId objectToInstanceId;              //If the object is an instancer this variable gives the object id ofthe object that should be instanced.See HAPI_Instancing.

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_GeoInfo          //Meta-data for a SOP Node
    {
        public HAPI_GeoType type;              

        public HAPI_StringHandle nameSH;              

        public HAPI_NodeId nodeId;              //Use the node id to get the node parameters.Using the HDK you can also get the raw node C++ pointer for thisobject internal node.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isEditable;              //Whether the SOP node has been exposed by dragging it into theeditable nodes section of the asset definition.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isTemplated;              //Has the templated flag turned on which means "expose as read-only".

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isDisplayGeo;              //Final Result Display SOP.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasGeoChanged;              //For incremental updates.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasMaterialChanged;              //@deprecated This variable is deprecated and should no longer be used.Materials are now separate from parts. They are maintained at theasset level so you only need to check if the material itself haschanged via HAPI_MaterialInfo::hasChanged instead of the materialon the part.

        [MarshalAs(UnmanagedType.I4)]
        public int pointGroupCount;              //Groups.@{@}

        [MarshalAs(UnmanagedType.I4)]
        public int primitiveGroupCount;              //Groups.@{@}

        [MarshalAs(UnmanagedType.I4)]
        public int edgeGroupCount;              //Groups.@{@}

        [MarshalAs(UnmanagedType.I4)]
        public int partCount;              //Total number of parts this geometry contains.See HAPI_Parts.

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_PartInfo          //Meta-data describing a Geo Part
    {
        public HAPI_PartId id;              //Id to identify this part relative to it GeoString handle for the name of the part

        public HAPI_StringHandle nameSH;              //Id to identify this part relative to it GeoString handle for the name of the part

        public HAPI_PartType type;              //Id to identify this part relative to it GeoString handle for the name of the part

        [MarshalAs(UnmanagedType.I4)]
        public int faceCount;              //Number of points. Note that this is NOT the numberof "positions" as "points" may imply. If yourgeometry has 3 points then set this to 3 and not 3*3.

        [MarshalAs(UnmanagedType.I4)]
        public int vertexCount;              //Number of points. Note that this is NOT the numberof "positions" as "points" may imply. If yourgeometry has 3 points then set this to 3 and not 3*3.

        [MarshalAs(UnmanagedType.I4)]
        public int pointCount;              //Number of points. Note that this is NOT the numberof "positions" as "points" may imply. If yourgeometry has 3 points then set this to 3 and not 3*3.

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = (int)HAPI_AttributeOwner.HAPI_ATTROWNER_MAX, ArraySubType = UnmanagedType.I4)]
        public int[] attributeCounts;              

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isInstanced;              //If this is true don display this part. Load its data but theninstance it where the corresponding instancer part tells you toinstance it.

        [MarshalAs(UnmanagedType.I4)]
        public int instancedPartCount;              //The number of parts that this instancer part is instancing.For example if we instancing a curve and a box they would comeacross as two parts hence this count would be two.Call HAPI_GetInstancedPartIds to get the list of HAPI_PartId.

        [MarshalAs(UnmanagedType.I4)]
        public int instanceCount;              //The number of instances that this instancer part is instancing.Using the same example as with HAPI_PartInfo::instancedPartCountif I instancing the merge of a curve and a box 5 times this countwould be 5. To be clear all instanced parts are instanced the samenumber of times and with the same transform for each instance.Call HAPI_GetInstancerPartTransforms to get the transform ofeach instance.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasChanged;              //If this is false the underlying attribute data appear to match that ofthe previous cook. In this case you may be able to re-used marshaleddata from the previous cook.

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_AttributeInfo          //Meta-data describing an attributeSee HAPI_Attributes.
    {
        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool exists;              

        public HAPI_AttributeOwner owner;              

        public HAPI_StorageType storage;              

        public HAPI_AttributeOwner originalOwner;              //When converting from the Houdini native GA geometry format to theGT geometry format HAPI uses some attributes might change owners.For example in Houdini GA curves can have points shared byvertices but the GT format only supports curve verticesno points. This means that if you had point attributes on a curvein Houdini when it comes out of HAPI those point attributes will nowbe vertex attributes. In this case the HAPI_AttributeInfo::ownerwill be set to HAPI_ATTROWNER_VERTEX but theHAPI_AttributeInfo::originalOwner will be HAPI_ATTROWNER_POINT.

        [MarshalAs(UnmanagedType.I4)]
        public int count;              //Number of attributes. This count will match the number of valuesgiven the owner. For example if the owner is HAPI_ATTROWNER_VERTEXthis count will be the same as the HAPI_PartInfo::vertexCount.To be clear this is not the number of values in the attribute ratherit is the number of attributes. If your geometry has three 3D pointsthen this count will be 3 not 3*3 while theHAPI_AttributeInfo::tupleSize will be 3.

        [MarshalAs(UnmanagedType.I4)]
        public int tupleSize;              //Number of values per attribute.Note that this is NOT the memory size of the attribute. It is thenumber of values per attributes. Multiplying this by thesize of the HAPI_AttributeInfo::storage will give you the memorysize per attribute.

        public HAPI_Int64 totalArrayElements;              //Total number of elements for an array attribute.An array attribute can be thought of as a 2 dimensional array wherethe 2nd dimension can vary in size for each element in the 1stdimension. Therefore this returns the total number of values inthe entire array.This should be used to determine the total storagesize needed by multiplying with HAPI_AttributeInfo::storage.Note that this will be 0 for a non-array attribute.

        public HAPI_AttributeTypeInfo typeInfo;              //Attribute type infoThis is used to help identify the type of data stored in an attribute.Using the type is recommended over using just an attribute name to identifyits purpose.

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_MaterialInfo          
    {
        public HAPI_NodeId nodeId;              //This is the HAPI node id for the SHOP node this material is attachedto. Use it to get access to the parameters which contain thetexture paths.IMPORTANT: When the HAPI_MaterialInfo::hasChanged is true thisnodeId could have changed. Do not assume HAPI_MaterialInfo::nodeIdwill never change for a specific material.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool exists;              

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasChanged;              

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_ImageFileFormat          //Describes an image format used with HAPI_GetSupportedImageFileFormats
    {
        public HAPI_StringHandle nameSH;              

        public HAPI_StringHandle descriptionSH;              

        public HAPI_StringHandle defaultExtensionSH;              

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_ImageInfo          //Data for an image used with HAPI_GetImageInfo and HAPI_SetImageInfo
    {
        public HAPI_StringHandle imageFileFormatNameSH;              //Unlike the other members of this struct changing imageFileFormatNameSHand giving this struct back to HAPI_SetImageInfo nothing will happen.Use this member variable to derive which image file format will be usedby the HAPI_ExtractImageToFile and HAPI_ExtractImageToMemoryfunctions if called with image_file_format_name set to NULL. This wayyou can decide whether to ask for a file format conversion slower ornot faster.Read-only

        [MarshalAs(UnmanagedType.I4)]
        public int xRes;              

        [MarshalAs(UnmanagedType.I4)]
        public int yRes;              

        public HAPI_ImageDataFormat dataFormat;              

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool interleaved;              //ex: true = RGBRGBRGB false = RRRGGGBBB

        public HAPI_ImagePacking packing;              

        [MarshalAs(UnmanagedType.R8)]
        public double gamma;              //Adjust the gamma of the image. For anything less thanHAPI_IMAGE_DATA_INT16 you probably want to leave this as 2.2.

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_Keyframe          //Data for a single Key Frame
    {
        [MarshalAs(UnmanagedType.R4)]
        public float time;              

        [MarshalAs(UnmanagedType.R4)]
        public float value;              

        [MarshalAs(UnmanagedType.R4)]
        public float inTangent;              

        [MarshalAs(UnmanagedType.R4)]
        public float outTangent;              

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_VolumeInfo          //This represents a volume primitive but does not contain the actual voxelvalues which can be retrieved on a per-tile basis.See HAPI_Volumes.
    {
        public HAPI_StringHandle nameSH;              

        public HAPI_VolumeType type;              

        [MarshalAs(UnmanagedType.I4)]
        public int xLength;              //Each voxel is identified with an index. The indices will rangebetween:[  minX minY minZ   minX+xLength minY+yLength minZ+zLength  @{@}

        [MarshalAs(UnmanagedType.I4)]
        public int yLength;              //Each voxel is identified with an index. The indices will rangebetween:[  minX minY minZ   minX+xLength minY+yLength minZ+zLength  @{@}

        [MarshalAs(UnmanagedType.I4)]
        public int zLength;              //Each voxel is identified with an index. The indices will rangebetween:[  minX minY minZ   minX+xLength minY+yLength minZ+zLength  @{@}

        [MarshalAs(UnmanagedType.I4)]
        public int minX;              //Each voxel is identified with an index. The indices will rangebetween:[  minX minY minZ   minX+xLength minY+yLength minZ+zLength  @{@}

        [MarshalAs(UnmanagedType.I4)]
        public int minY;              //Each voxel is identified with an index. The indices will rangebetween:[  minX minY minZ   minX+xLength minY+yLength minZ+zLength  @{@}

        [MarshalAs(UnmanagedType.I4)]
        public int minZ;              //Each voxel is identified with an index. The indices will rangebetween:[  minX minY minZ   minX+xLength minY+yLength minZ+zLength  @{@}

        [MarshalAs(UnmanagedType.I4)]
        public int tupleSize;              //Number of values per voxel.The tuple size field is 1 for scalars and 3 for vector data.

        public HAPI_StorageType storage;              //Can be either HAPI_STORAGETYPE_INT or HAPI_STORAGETYPE_FLOAT.

        [MarshalAs(UnmanagedType.I4)]
        public int tileSize;              //The dimensions of each tile.This can be 8 or 16 denoting an 8x8x8 or 16x16x16 tiles.

        [MarshalAs(UnmanagedType.Struct)]
        public HAPI_Transform transform;              //The transform of the volume with respect to the lengths.The volume may be positioned anywhere in space.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasTaper;              //Denotes special situations where the volume tiles are not perfectcubes but are tapered instead.

        [MarshalAs(UnmanagedType.R4)]
        public float xTaper;              //If there is taper involved denotes the amount of taper involved.@{@}

        [MarshalAs(UnmanagedType.R4)]
        public float yTaper;              //If there is taper involved denotes the amount of taper involved.@{@}

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_VolumeTileInfo          //A HAPI_VolumeTileInfo represents an cube subarray of the volume.The size of each dimension is HAPI_VolumeInfo::tileSizebbox [minX minY minZ minX+tileSize minY+tileSize minZ+tileSize
    {
        [MarshalAs(UnmanagedType.I4)]
        public int minX;              

        [MarshalAs(UnmanagedType.I4)]
        public int minY;              

        [MarshalAs(UnmanagedType.I4)]
        public int minZ;              

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isValid;              

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_VolumeVisualInfo          //Describes the visual settings of a volume.
    {
        public HAPI_VolumeVisualType type;              

        [MarshalAs(UnmanagedType.R4)]
        public float iso;              

        [MarshalAs(UnmanagedType.R4)]
        public float density;              

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_CurveInfo          //Represents the meta-data associated with a curve mesh a numberof curves of the same type.
    {
        public HAPI_CurveType curveType;              

        [MarshalAs(UnmanagedType.I4)]
        public int curveCount;              //The number of curves contained in this curve mesh.

        [MarshalAs(UnmanagedType.I4)]
        public int vertexCount;              //The number of control vertices CVs for all curves.

        [MarshalAs(UnmanagedType.I4)]
        public int knotCount;              //The number of knots for all curves.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isPeriodic;              //Whether the curves in this curve mesh are periodic closed by appending a new point

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isRational;              //Whether the curves in this curve mesh are rational.

        [MarshalAs(UnmanagedType.I4)]
        public int order;              //Order of 1 is invalid. 0 means there is a varying order.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool hasKnots;              //Whether the curve has knots.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool isClosed;              //Similar to isPeriodic but creates a polygon instead of a separate point

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_InputCurveInfo          
    {
        public HAPI_CurveType curveType;              //The desired curve type of the curveNote that this is NOT necessarily equal to the value in HAPI_CurveInfoin the case of curve refinement

        [MarshalAs(UnmanagedType.I4)]
        public int order;              //The desired order for your input curveThis is your desired order which may differ from HAPI_CurveInfoas it will do range checks and adjust the actual order accordingly

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool closed;              //Whether or not the curve is closedMay differ from HAPI_CurveInfo::isPeriodic depending on the curveTypee.g. A NURBs curve is never technically closed according to HAPI_CurveInfo

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool reverse;              //Whether or not to reverse the curve input

        public HAPI_InputCurveMethod inputMethod;              

        public HAPI_InputCurveParameterization breakpointParameterization;              

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_BoxInfo          //Data for a Box Part
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_POSITION_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] center;              

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_SCALE_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] size;              

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_EULER_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] rotation;              

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_SphereInfo          //Data for a Sphere Part
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_POSITION_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] center;              

        [MarshalAs(UnmanagedType.R4)]
        public float radius;              

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_PDG_EventInfo          //Data associated with a PDG Event
    {
        public HAPI_NodeId nodeId;              //id of related node

        public HAPI_PDG_WorkitemId workitemId;              //id of related workitem

        public HAPI_PDG_WorkitemId dependencyId;              //id of related workitem dependency

        [MarshalAs(UnmanagedType.I4)]
        public int currentState;              //HAPI_PDG_WorkitemState value of current state for state change

        [MarshalAs(UnmanagedType.I4)]
        public int lastState;              //HAPI_PDG_WorkitemState value of last state for state change

        [MarshalAs(UnmanagedType.I4)]
        public int eventType;              //HAPI_PDG_EventType event type

        public HAPI_StringHandle msgSH;              //String handle of the event message > 0 if there is a message

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_PDG_WorkitemInfo          //Info for a PDG Workitem
    {
        [MarshalAs(UnmanagedType.I4)]
        public int index;              //index of the workitem

        [MarshalAs(UnmanagedType.I4)]
        public int numResults;              //number of results reported

        public HAPI_StringHandle nameSH;              //name of the workitem

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_PDG_WorkitemResultInfo          //Data for a PDG file result
    {
        [MarshalAs(UnmanagedType.I4)]
        public int resultSH;              //result string data

        [MarshalAs(UnmanagedType.I4)]
        public int resultTagSH;              //result tag

        public HAPI_Int64 resultHash;              //hash value of result

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_Viewport          //Contains the information for synchronizing viewport between Houdiniand other applications. When SessionSync is enabled Houdini willupdate this struct with its viewport state. It will also updateits own viewport if this struct has changed.The data stored is in Houdini right-handed Y-up coordinate system.
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_POSITION_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] position;              //The world position of the viewport camera pivot.

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = HEU_HAPIConstants.HAPI_QUATERNION_VECTOR_SIZE, ArraySubType = UnmanagedType.R4)]
        public float[] rotationQuaternion;              //The direction of the viewport camera stored as a quaternion.

        [MarshalAs(UnmanagedType.R4)]
        public float offset;              //The offset from the pivot to the viewport cameraactual world position.

    };

    [StructLayout(LayoutKind.Sequential)]
    [Serializable]
    public partial struct HAPI_SessionSyncInfo          //Contains the information for synchronizing general SessionSyncstate between Houdini and other applications. When SessionSyncis enabled Houdini will update this struct with its state.It will also update its internal state if this struct haschanged.
    {
        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool cookUsingHoudiniTime;              //Specifies whether Houdini current time is used for Houdini Enginecooks. This is automatically enabled in SessionSync whereHoudini viewport forces cooks to use Houdini current time.This is disabled in non-SessionSync mode but can be toggled tooverride default behaviour.

        [MarshalAs(UnmanagedType.U1)]
        public HAPI_Bool syncViewport;              //Specifies whether viewport synchronization is enabled. If enabledin SessionSync Houdini will update its own viewport usingHAPI_Viewport.

    };

    [StructLayout(LayoutKind.Sequential)]
    public partial struct HAPI_CompositorOptions          //Configuration options for Houdini compositing context
    {
        [MarshalAs(UnmanagedType.I4)]
        public int maximumResolutionX;              //Specifies the maximum allowed width of an image in the compositor

        [MarshalAs(UnmanagedType.I4)]
        public int maximumResolutionY;              //Specifies the maximum allowed height of an image in the compositor

    };


}
