/*
* Copyright (c) <2020> Side Effects Software Inc.
* All rights reserved.
*
* Redistribution and use in source and binary forms, with or without
* modification, are permitted provided that the following conditions are met:
*
* 1. Redistributions of source code must retain the above copyright notice,
*    this list of conditions and the following disclaimer.
*
* 2. The name of Side Effects Software may not be used to endorse or
*    promote products derived from this software without specific prior
*    written permission.
*
* THIS SOFTWARE IS PROVIDED BY SIDE EFFECTS SOFTWARE "AS IS" AND ANY EXPRESS
* OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.  IN
* NO EVENT SHALL SIDE EFFECTS SOFTWARE BE LIABLE FOR ANY DIRECT, INDIRECT,
* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
* LIMITED TO, PROCUREMENT OF SU<PERSON><PERSON>TU<PERSON> GOODS OR SERVICES; LOSS OF USE, DATA,
* OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
* LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
* NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
* EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

using UnityEngine;
using System.Collections.Generic;

// Expose internal classes/functions
#if UNITY_EDITOR
using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("HoudiniEngineUnityEditor")]
[assembly: InternalsVisibleTo("HoudiniEngineUnityEditorTests")]
[assembly: InternalsVisibleTo("HoudiniEngineUnityPlayModeTests")]
#endif

namespace HoudiniEngineUnity
{
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // Typedefs (copy these from HEU_Common.cs)
    using HAPI_NodeId = System.Int32;
    using HAPI_AssetLibraryId = System.Int32;
    using HAPI_StringHandle = System.Int32;
    using HAPI_ErrorCodeBits = System.Int32;
    using HAPI_NodeTypeBits = System.Int32;
    using HAPI_NodeFlagsBits = System.Int32;
    using HAPI_ParmId = System.Int32;
    using HAPI_PartId = System.Int32;
    

    // Where the material originated from
    public enum HEU_MaterialSourceWrapper
    {
        DEFAULT,
        HOUDINI,
        UNITY,
        SUBSTANCE,
    };

    /// <summary>
    /// Holds all parameter data for an asset.
    /// </summary>
    public interface IHEU_MaterialData
    {
	/// <summary>
	/// The material
	/// </summary>
	Material Material {get; }

	/// <summary>
	/// Where the material was from
	/// </summary>
	HEU_MaterialSourceWrapper MaterialSource { get; }

	/// <summary>
	/// The ID generated by this plugin for managing on the Unity side.
	/// All HEU_MaterialData will have a unique ID, either same as _materialHoudiniID for Houdini materials.
	/// or hash of material path

	/// The ID used to uniquely identify a material.
	/// For Houdini materials, this is the ID returned by the material info.
	/// For existing Unity materials (via unity_material attribute), this is 
	/// the hash of the material path on project (eg. Assets/Materials/materialname.mat)
	/// </summary>
	int MaterialKey { get; set; }


	/// <summary>
	/// For this object's _material, we update the shader attributes and 
	/// fetch the textures from Houdini.
	/// </summary>
	/// <param name="materialInfo">This material's info from Houdini</param>
	/// <param name="assetCacheFolderPath">Path to asset's cache folder</param>
	bool UpdateMaterialFromHoudini(HAPI_MaterialInfo materialInfo, string assetCacheFolderPath);


	/// <summary>
	/// Returns true if this material was pre-existing in Unity and not generated from Houdini at cook time.
	/// </summary>
	bool IsExistingMaterial();
    }


}   // HoudiniEngineUnity