{"appConfigName": "BKD", "appInfo": {"appId": "*********", "appKey": "K34ndWZ9LyN5bX0sdXtjeXYhKzF+fyF+YHl/JHo2eig=", "language": "en", "channel": "googleplay", "publisher": "2", "screenOrientation": "landscape", "policy": "https://www.obliviland.com/policy", "terms": "https://www.obliviland.com/terms"}, "manifestPlaceHolder": {"onemtSdkScreenOrientation": "sensorLandscape", "onemtSdkConfigChanges": "locale|layoutDirection", "onemtSdkWebViewScreenOrientation": "sensorLandscape", "ADMOB_APPID": "ca-app-pub-****************~**********"}, "modules": ["accountBase", "accountEmail", "accountGoogle", "accountFacebook", "pushBase", "pushFirebase", "reportBase", "reportCtk", "reportFirebase", "reportFacebook", "reportAdjust", "social", "networkAnalytics", "accountMobile", "advertPlatformAdmob", "advertMediationFacebook", "advertMediationUnity", "advertMediationIronsource", "advertMediationApplovin", "pushLocal"], "dependencies": {"onemtSdk": ["com.onemt.sdk.business:crashlytics-firebase:5.19.2", "com.onemt.sdk.business:terms-common:5.19.2", "com.onemt.sdk.business:social:5.19.2", "com.onemt.sdk.business:advert-unity:5.19.2", "com.onemt.sdk.business:account-mobile:5.19.2", "com.onemt.sdk.business:account-email:5.19.2", "com.onemt.sdk.business:billing-google:5.19.2", "com.onemt.sdk.business:account-google:5.19.2", "com.onemt.sdk.business:advert-admob:5.19.2", "com.onemt.sdk.business:advert-facebook:5.19.2", "com.onemt.sdk.business:share-whatsapp:5.19.2", "com.onemt.sdk.component:support-light:5.19.2", "com.onemt.sdk.business:review-google:5.19.2", "com.onemt.sdk.business:report-firebase:5.19.2", "com.onemt.sdk.business:report-ctk:5.19.2", "com.onemt.sdk.business:advert-ironsource:5.19.2", "com.onemt.sdk.business:push-firebase:5.19.2", "com.onemt.sdk.business:report-adjust:5.19.2", "com.onemt.sdk.business:push-local:5.19.2", "com.onemt.sdk.business:account-facebook:5.19.2", "com.onemt.sdk.business:report-facebook:5.19.2", "com.onemt.sdk.business:advert-applovin:5.19.2", "com.onemt.sdk.business:resources-bkd-bkd:5.19.2", "com.onemt.sdk.business:share-facebook:5.19.2", "com.onemt.sdk.business:entry:5.19.2", "com.onemt.sdk.business:network-analytics:5.19.2", "com.onemt.sdk.component:community-light:5.19.2"], "api": ["androidx.multidex:multidex:2.0.1"], "annotationProcessor": ["com.alibaba:arouter-compiler:1.2.2"]}, "ucLoginType": ["google", "facebook", "email", "mobile"], "h5Config": [{"module": "community", "bgColor": "#ffffff"}, {"module": "support", "bgColor": "#ffffff"}], "ucConfig": {"personalInfo": true}, "facebook": {"facebookToken": "********************************", "enablePhotoSharing": false, "facebookAppId": "****************", "facebookLoginProtocolScheme": "fb****************"}, "google": {"checkGoogleAvailable": false, "googlePlayClientId": "************-ia6olj690qa2i40d13mom7vsj131gsnk.apps.googleusercontent.com", "googleGamesServiceAppId": "************"}, "firebase": {"pushSenderId": "5973495162"}, "adjust": {"enableImei": false, "appToken": "zehn66x9bv9c"}, "advert": {"appLovinSdkKey": "l6UJ48_6rE0vuAZLuQxfg1ZIFJSA48AzJwe5IA6HQ2Sx_IPAJLRbftIsJPAiWnapA0465vresahAfPfzxGzfNs"}, "Terms": {"Cmp": true}, "adjustReport": {"eventStatus": {"Activation": true, "Day2Active": true, "Day3Active": true, "Day7Active": true, "FinishGuide": true, "Online": true, "Pay": true, "Register": true}, "eventToken": {"Activation": "b90kp1", "Day2Active": "801thn", "Day3Active": "ptqx9x", "Day7Active": "2b97vu", "FinishGuide": "t4dbte", "Online": "rw2ak8", "Pay": "vx64ye", "Register": "bezgmq"}}, "facebookReport": {"eventStatus": {"Activation": true, "Day2Active": true, "Day3Active": true, "Day7Active": true, "FinishGuide": true, "Online": true, "Pay": true, "Register": true}}, "firebaseReport": {"eventStatus": {"Activation": true, "Day2Active": true, "Day3Active": true, "Day7Active": true, "FinishGuide": true, "Online": true, "Pay": true, "Register": true}}, "urlHosts": [{"name": "sdkCommon", "url": "https://sdkcommon.menaapp.net/", "debugUrl": "https://sdkcommondebug.menaapp.net/", "betaUrl": "https://sdkcommonbeta.menaapp.net/"}, {"name": "userCenter", "url": "https://apiuc.menaapp.net/", "debugUrl": "https://apiucdebug.menaapp.net/", "betaUrl": "https://apiucbeta.menaapp.net/"}, {"name": "adReport", "url": "https://adapi.onemt.co/", "debugUrl": "https://addebug.onemt.co/", "betaUrl": "https://adbeta.onemt.co/"}, {"name": "push", "url": "https://apipush.menaapp.net/", "debugUrl": "https://apipushdebug.menaapp.net/", "betaUrl": "https://apipushbeta.menaapp.net/"}, {"name": "communityH5", "url": "https://apicosdk.onemt.co", "debugUrl": "https://apicosdkdebug.onemt.co", "betaUrl": "https://apicosdkbeta.onemt.co"}, {"name": "faqH5", "url": "https://apicssdk.onemt.co", "debugUrl": "https://apicssdkdebug.onemt.co", "betaUrl": "https://apicssdkbeta.onemt.co"}, {"name": "gameSupport", "url": "https://eventnotice.onemt.co/", "debugUrl": "https://eventtest.onemt.co/", "betaUrl": "https://eventtest.onemt.co/"}, {"name": "community", "url": "https://apicosdk.onemt.co/", "debugUrl": "https://apicosdkdebug.onemt.co/", "betaUrl": "https://apicosdkbeta.onemt.co/"}, {"name": "faq", "url": "https://apicssdk.onemt.co/v2/", "debugUrl": "https://apicssdkdebug.onemt.co/", "betaUrl": "https://apicssdkbeta.onemt.co/"}, {"name": "avatar", "url": "https://avatarapi.menaapp.net/", "debugUrl": "https://avatarapibeta.menaapp.co/", "betaUrl": "https://avatarapibeta.menaapp.co/"}, {"name": "voice", "url": "https://fileupload.onemt.co/", "debugUrl": "https://fileuploadbeta.onemt.co/", "betaUrl": "https://fileuploadbeta.onemt.co/"}, {"name": "pay", "url": "https://pay.onemt.co/", "debugUrl": "https://rechargetest.onemt.co/", "betaUrl": "https://rechargetest.onemt.co/"}, {"name": "pay_web", "url": "https://pay.onemt.co/pay/payment/recharge", "debugUrl": "https://rechargetest.onemt.co/pay/payment/recharge", "betaUrl": "https://rechargetest.onemt.co/pay/payment/recharge"}, {"name": "kafkaReport", "url": "https://gameapi.onemt.co/", "debugUrl": "https://gameapidebug.onemt.co/", "betaUrl": "https://gameapidebug.onemt.co/"}, {"name": "dataReport", "url": "https://datareport.onemt.co/", "debugUrl": "https://datareporttest.onemt.co/", "betaUrl": "https://datareporttest.onemt.co/"}, {"name": "ctkReport", "url": "https://dgapi.onemt.co/", "debugUrl": "https://dgtest.onemt.co/", "betaUrl": "https://dgtest.onemt.co/"}, {"name": "billing", "url": "https://iap.menaapp.net/", "debugUrl": "https://rechargedebug.onemt.co/", "betaUrl": "https://iapdev.menaapp.net/"}, {"name": "logReport", "url": "https://sdklog.menaapp.net/", "debugUrl": "https://sdklogdebug.menaapp.net/", "betaUrl": "https://sdklogbeta.menaapp.net/"}, {"name": "webApi", "url": "https://wj.menaapp.net/", "debugUrl": "https://wjbeta.menaapp.net/", "betaUrl": "https://wjbeta.menaapp.net/"}, {"name": "notice", "url": "https://gamesupport.onemt.co/", "debugUrl": "https://gamesupportdev.onemt.co/", "betaUrl": "https://gamesupportdev.onemt.co/"}, {"name": "event", "url": "https://apiac.menaapp.net/", "debugUrl": "https://apiacdev.menaapp.net/", "betaUrl": "https://apiacdev.menaapp.net/"}, {"name": "notification", "url": "nf.menaapp.net:8831", "debugUrl": "nftest.menaapp.net:8831", "betaUrl": "nftest.menaapp.net:8831"}, {"name": "notificationWeb", "url": "https://notification.menaapp.net/", "debugUrl": "https://nftest.menaapp.net/", "betaUrl": "https://nftest.menaapp.net/"}]}