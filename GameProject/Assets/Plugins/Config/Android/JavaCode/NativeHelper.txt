package com.unity3d.player;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Resources;

public class NativeHelper {
    public static Context s_Context;

    private static boolean isDevelop = false;
    private static boolean isSkipUpdate = false;
    private static boolean isRelease = false;

    public static void Init(Context context)
    {
        s_Context = context;
        InitDevelopCfg();
    }

    public static boolean IsDevelop(){ return isDevelop; }

    public static boolean IsSkipUpdate(){
        return isSkipUpdate;
    }

    public static boolean IsRelease() { return isRelease; }

    public static String GetChannel(){
        if(s_Context != null)
        {
            Resources resource =  s_Context.getResources();
            if(resource != null)
            {
                int id = resource.getIdentifier("appChannel","string", s_Context.getPackageName());
                return resource.getString(id);
            }
        }
        return "None";
    }

    public static void InitDevelopCfg() {
        Resources resource =  s_Context.getResources();
        String packageName = s_Context.getPackageName();
        int id = resource.getIdentifier("isDevelop","bool", packageName);
        isDevelop = resource.getBoolean(id);

        id = resource.getIdentifier("isSkipUpdate","bool", packageName);
        isSkipUpdate = resource.getBoolean(id);

        id = resource.getIdentifier("isRelease","bool", packageName);
        isRelease = resource.getBoolean(id);
    }

    public static int GetVersionCode(){
        PackageManager packageManager = UnityPlayer.currentActivity.getPackageManager();
        UnityPlayer.currentActivity.getPackageName();
        try
        {
            PackageInfo info =packageManager.getPackageInfo(UnityPlayer.currentActivity.getPackageName(), 0);
            return info.versionCode;
        }catch (PackageManager.NameNotFoundException e){

        }
        return 0;
    }

}
