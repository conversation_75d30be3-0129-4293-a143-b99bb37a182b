package com.unity3d.player;

import static com.unity3d.player.MessageDefine.GAME_MODE_CHANGE;

import android.content.Context;
import android.app.GameManager;
import android.os.Build;
import android.os.PowerManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

public class GameModeManager {

    public static Context s_Context;

    private static View s_SplashLayout;

    private static PowerManager.OnThermalStatusChangedListener listener;

    private static boolean thermalInited = false;

    public static int GetGameMode(){
        int gameMode = GameManager.GAME_MODE_UNSUPPORTED;
        if ( s_Context != null ) {
            if ( Build.VERSION.SDK_INT >= Build.VERSION_CODES.S ) {
                // Get GameManager from SystemService
                GameManager gameManager = s_Context.getSystemService(GameManager.class);

                // Returns the selected GameMode
                gameMode = gameManager.getGameMode();
            }
        }
        return gameMode == GameManager.GAME_MODE_UNSUPPORTED ? GameManager.GAME_MODE_STANDARD : gameMode;
    }

    public static void NotifyGameMode(){
        int mode = GameModeManager.GetGameMode();
        CShapBridge.SendNativeMessageCommand(GAME_MODE_CHANGE, ""+mode);
    }

    public static int GetThermalStatus()
    {
        int state = PowerManager.THERMAL_STATUS_NONE;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            PowerManager pm = (PowerManager) s_Context.getSystemService(Context.POWER_SERVICE);
            if (pm != null) {
                state = pm.getCurrentThermalStatus();
            }
        }
        return state;
    }

    public static void InitThermalListener(){
        if (s_Context != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            PowerManager pm = (PowerManager) s_Context.getSystemService(Context.POWER_SERVICE);
            if (pm != null) {
                listener = new PowerManager.OnThermalStatusChangedListener() {
                    @Override
                    public void onThermalStatusChanged(int status) {
                        CShapBridge.SendNativeMessageCommand(MessageDefine.THERMAL_STATUS_CHANGED, ""+status);
                    }
                };
                pm.addThermalStatusListener(listener);
                thermalInited = true;
            }
        }
    }

    public static void UnInitThermalListener(){
        if(s_Context == null || listener == null || !thermalInited)
            return;

        thermalInited = false;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            PowerManager pm = (PowerManager) s_Context.getSystemService(Context.POWER_SERVICE);
            if (pm != null) {
                pm.removeThermalStatusListener(listener);
            }
        }
    }

public static void ShowSplash(){
        UnityPlayerActivity activity = (UnityPlayerActivity)s_Context;
        Log.d("AAA","ShowSplash");
        activity.runOnUiThread(new Runnable(){
            @Override
            public void run() {
                androidx.appcompat.widget.ContentFrameLayout root = activity.findViewById(android.R.id.content);
                View view = LayoutInflater.from(activity).inflate(R.layout.start_logo, null, false);
                root.addView(view);

                s_SplashLayout = view;
                ImageView imageView = s_SplashLayout.findViewById(R.id.sultan_logo_image);
                int resourceId = activity.getResources().getIdentifier("logo", "drawable", activity.getPackageName());
                imageView.setImageDrawable(activity.getDrawable(resourceId));

            }
        });

    }

    public static void CloseSplash(){
        Log.d("AAA","CloseSplash");
        if(s_SplashLayout != null)
        {
            UnityPlayerActivity activity = (UnityPlayerActivity)s_Context;
            activity.runOnUiThread(new Runnable(){
                @Override
                public void run() {
                    androidx.appcompat.widget.ContentFrameLayout root = activity.findViewById(android.R.id.content);
                    root.removeView(s_SplashLayout);
                    s_SplashLayout = null;
                }
            });

        }
    }


    public static void Init(Context context)
    {
        s_Context = context;
    }
}
