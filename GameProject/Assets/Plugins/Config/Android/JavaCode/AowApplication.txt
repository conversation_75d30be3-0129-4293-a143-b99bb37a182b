package com.unity3d.player;

import android.app.Activity;
import android.app.Application;
import android.content.ComponentName;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.multidex.MultiDex;

import com.onemt.sdk.component.util.LogUtil;
import com.onemt.sdk.core.OneMTCore;
import com.onemt.sdk.entry.OneMTApplication;

import java.io.File;
import java.util.List;

public class AowApplication extends OneMTApplication implements Application.ActivityLifecycleCallbacks {

    @Override
    public void onCreate() {
        registerActivityLifecycleCallbacks(this);
        super.onCreate();
    }

//    @Override
//    public File getExternalCacheDir() {
//        if (super.getExternalCacheDir() == null) {
//            return super.getCacheDir();
//        }
//        return super.getExternalCacheDir();
//    }
//
//    @Override
//    public File getExternalFilesDir(@Nullable String type) {
//        if (super.getExternalFilesDir(type) == null) {
//            return super.getCacheDir();
//        }
//        return super.getExternalFilesDir(type);
//    }


    @Override
    public File getExternalCacheDir() {
        if (super.getExternalCacheDir() == null) {
            try {
                throw new NullPointerException();
            }catch (Exception e) {
                e.printStackTrace();
            }
            return super.getCacheDir();
        }
        return super.getExternalCacheDir();
    }

    @Override
    public File getExternalFilesDir(@Nullable String type) {
        if (super.getExternalFilesDir(type) == null) {
            try {
                throw new NullPointerException();
            }catch (Exception e) {
                e.printStackTrace();
            }

            return super.getFilesDir();
        }
        return super.getExternalFilesDir(type);
    }

    @Override
    public File[] getExternalMediaDirs() {
        try {
            throw new NullPointerException();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return super.getExternalMediaDirs();
    }

    @Override
    public File[] getExternalCacheDirs() {
        try {
            throw new NullPointerException();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return super.getExternalCacheDirs();
    }

    @Override
    public File[] getExternalFilesDirs(String type) {
        try {
            throw new NullPointerException();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return super.getExternalFilesDirs(type);
    }




    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        if (OneMTCore.getApplication() == null || OneMTCore.getGameActivity() == null && activity instanceof UnityPlayerActivity) {
            PackageManager packageManager = activity.getPackageManager();
            Intent intent = packageManager.getLaunchIntentForPackage(activity.getPackageName());
            if (intent != null) {
                ComponentName componentName = intent.getComponent();
                if (componentName == null || TextUtils.equals(componentName.getClassName(), activity.getComponentName().getClassName())) {
                    // 被系统杀死， 重启
                    if (activity instanceof FragmentActivity) {
                        destroyAllUIFragment((FragmentActivity) activity);
                    }
                }
            }
        }
    }

    /**
     * 销毁所有IM相关的fragment
     * @param activity
     */
    public static void destroyAllUIFragment(FragmentActivity activity) {
        try {
            FragmentManager fragmentManager = activity.getSupportFragmentManager();
            List<Fragment> fragments = fragmentManager.getFragments();
            if (fragments != null && !fragments.isEmpty()) {
                int size = fragments.size();
                for (int i = size - 1; i >= 0; i--) {
                    Fragment fragment = fragments.get(i);
                    fragmentManager.popBackStackImmediate();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Override
    public void onActivityStarted(@NonNull Activity activity) {

    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {

    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {

    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {

    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {

    }
}