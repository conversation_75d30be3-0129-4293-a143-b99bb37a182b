package com.unity3d.player;

import org.json.JSONException;
import org.json.JSONObject;

public class CShap<PERSON>ridge {
    public static String gameObjectName = "GameServices";

    public static void callLuaFunctionWithString(int luaFunctionId, String value){
        JSONObject data = new JSONObject();
        try {
            data.put("luaFunctionId", luaFunctionId);
            data.put("value", value);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        UnityPlayer.UnitySendMessage(gameObjectName,"callLuaFunctionWithString", data.toString());
    }

    public static void callLuaGlobalFunctionWithString(String luaFunctionName, String value)
    {
        JSONObject data = new JSONObject();
        try {
            data.put("luaFunctionName", luaFunctionName);
            data.put("value", value);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        UnityPlayer.UnitySendMessage(gameObjectName,"callLuaGlobalFunctionWithString", data.toString());
    }

    public static void SendNativeMessageCommand(int messageCode, String data)
    {
        JSONObject jsonData = new JSONObject();
        try {
            jsonData.put("code", messageCode);
            jsonData.put("data", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        UnityPlayer.UnitySendMessage(gameObjectName,"SendNativeMessageCommand", jsonData.toString());
    }
}
